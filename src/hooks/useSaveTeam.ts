import { useMutation, useQueryClient } from '@tanstack/react-query';
import { QUERY_KEY_TEAM, saveMemberTarget } from 'api/teamApi';
import { TeamTarget } from 'types/team';

export function useSaveTeamTarget() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: TeamTarget }) =>
      saveMemberTarget(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_TEAM.TARGET],
        refetchType: 'active',
      });
    },
  });
}
