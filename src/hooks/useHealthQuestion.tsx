import {
  useMutation,
  useQueries,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';

import {
  answerHealthQuestion,
  closeHealthQuestion,
  generateHealthQuestion,
  generateHealthQuestions,
  getHealthQuestion,
  getQuestionToolTipContent,
  lookupQuestionOptionsList,
} from 'api/healthQuestionApi';
import {
  HealthQuestion2,
  LookupQuestionOptionsListParams,
  QuestionOptionsList,
  QuestionOptionsListParams,
  QuestionToolTipParams,
} from 'types/healthQuestion';
import { useDebounceMutation } from './useDebounceMutation';
import { useLocaleHeader } from './useLocaleHeader';
import { useAlert } from './useAlert';

const getHealthQuestionKey = (id: string) => [`healthQuestion/${id}`];

const getQuestionOptionsListKey = ({
  branch,
  tag,
  questionName,
  locale = 'English',
}: QuestionOptionsListParams) => [branch, tag, questionName, locale];

const getLookupQuestionOptionsListKey = ({
  branch,
  tag,
  questionName,
  searchKeyword,
  locale = 'English',
}: LookupQuestionOptionsListParams) => [
  branch,
  tag,
  questionName,
  locale,
  searchKeyword,
];

const GENERATE_HEALTH_QUESTION_BODY_EXAMPLE = {
  case: {
    selectedProposalId: {
      $oid: '646dddcaef99444378b8df07',
    },
    policyId: null,
    status: 'IN_APP',
    isFNACompleted: true,
    party: {
      insureds: [],
      proposers: [],
      beneficiary: [],
      payers: null,
    },
    fna: {},
    quotations: [],
    application: {},
  },
};

export const useGenerateHealthQuestion = () => {
  const localeHeader = useLocaleHeader();
  return useMutation({
    mutationFn: () => {
      const requestBody = GENERATE_HEALTH_QUESTION_BODY_EXAMPLE;
      return generateHealthQuestion(requestBody, localeHeader);
    },
  });
};

export const useGetHealthQuestionManually = (id: string) => {
  const queryClient = useQueryClient();
  const localeHeader = useLocaleHeader();
  const { alertBackendError } = useAlert();
  return useMutation({
    mutationFn: async () => {
      const data = await getHealthQuestion(id, localeHeader);
      queryClient.setQueryData(getHealthQuestionKey(id), data);
      queryClient.invalidateQueries(getHealthQuestionKey(id));
      return data;
    },
    onError: alertBackendError,
  });
};

export const useGetHealthQuestionByIdManually = () => {
  const queryClient = useQueryClient();
  const localeHeader = useLocaleHeader();
  const { alertBackendError } = useAlert();
  return useMutation({
    mutationFn: async (id: string) => {
      const data = await getHealthQuestion(id, localeHeader);
      queryClient.setQueryData(getHealthQuestionKey(id), data);
      queryClient.invalidateQueries(getHealthQuestionKey(id));
      return data;
    },
    onError: alertBackendError,
  });
};

export const useGetHealthQuestion = (id: string) => {
  const localeHeader = useLocaleHeader();
  const { alertBackendError } = useAlert();
  return useQuery({
    queryKey: getHealthQuestionKey(id),
    queryFn: async () => {
      const data = await getHealthQuestion(id, localeHeader);
      return data;
    },
    onError: () => id && alertBackendError(),
  });
};

export const useGetHealthQuestions = (ids: string[]) => {
  const localeHeader = useLocaleHeader();
  return useQueries({
    queries: ids.map(id => ({
      queryKey: getHealthQuestionKey(id),
      queryFn: async () => {
        const data = await getHealthQuestion(id, localeHeader);
        return data;
      },
    })),
  });
};

type RequestData = {
  answers: { [question: string]: (string | undefined)[] | undefined };
};

export const useAnswerHealthQuestion = ({
  onSuccess,
}: {
  onSuccess: (data: HealthQuestion2) => void;
}) => {
  const queryClient = useQueryClient();
  const localeHeader = useLocaleHeader();
  const { debouncedMutate, ...rest } = useDebounceMutation(
    {
      mutationFn: async ({
        id,
        requestData,
      }: {
        id: string;
        requestData: RequestData;
      }) => await answerHealthQuestion(id, requestData, localeHeader),
      onSuccess: (data: HealthQuestion2) => {
        onSuccess(data);
        queryClient.setQueryData(getHealthQuestionKey(data.enquiryId), data);
        queryClient.invalidateQueries(getHealthQuestionKey(data.enquiryId));
      },
    },
    500,
  );

  return { ...rest, mutate: debouncedMutate };
};

export const useLookupQuestionOptionsList = (
  questionOptionsListParams: LookupQuestionOptionsListParams,
  { enabled = false }: { enabled: boolean },
) => {
  const localeHeader = useLocaleHeader();
  return useQuery({
    initialData: [],
    queryKey: getLookupQuestionOptionsListKey(questionOptionsListParams),
    queryFn: async () => {
      const data = await lookupQuestionOptionsList(
        questionOptionsListParams,
        localeHeader,
      );
      return data as string[];
    },
    enabled,
  });
};

export const useGetQuestionToolTipContent = (
  params: QuestionToolTipParams,
  { enabled = false }: { enabled: boolean },
) => {
  const localeHeader = useLocaleHeader();
  return useQuery({
    queryKey: [
      params.branch,
      params.tag,
      params.optionListName,
      params.optionTag,
    ],
    queryFn: async () => {
      const data = await getQuestionToolTipContent(params, localeHeader);
      return data;
    },
    enabled,
  });
};

export const useGenerateHealthQuestions = () => {
  const localeHeader = useLocaleHeader();
  return useMutation({
    mutationFn: (owbModel: object) => {
      return generateHealthQuestions(owbModel, localeHeader);
    },
  });
};

export const useCloseHealthQuestion = () => {
  return useMutation({
    mutationFn: (owbModel: object) => {
      return closeHealthQuestion(owbModel);
    },
  });
};
