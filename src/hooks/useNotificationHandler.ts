import { useCallback } from 'react';
import * as Notifications from 'expo-notifications';

import { cancelScheduledNotificationAsync } from 'utils/notification';
import useScheduledNotifications from 'features/task/hooks/useScheduledNotifications';
import { useRootStackNavigation } from './useRootStack';
import useBoundStore from './useBoundStore';
import { country } from 'utils/context';

export const useNotificationHandler = () => {
  const { updateAllScheduledNotifications } = useScheduledNotifications();
  const { navigate, reset } = useRootStackNavigation();

  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );
  const setActiveCase = useBoundStore(state => state.caseActions.setActiveCase);
  const activeAgentCode = useBoundStore(state => state.auth.agentCode);

  const handleResponseReceived = useCallback(
    async (identifier: string) => {
      await cancelScheduledNotificationAsync(identifier);
      await updateAllScheduledNotifications();
    },
    [updateAllScheduledNotifications],
  );

  const handler = useCallback(
    async (response: Notifications.NotificationResponse) => {
      switch (country) {
        case 'ph': {
          const type = response.notification.request.content.data.type;
          switch (type) {
            case 'RemoteSellingConfirm': {
              const caseId = response.notification.request.content.data.caseId;
              const agentId =
                response.notification.request.content.data.agentId;
              if (caseId && agentId === activeAgentCode) {
                clearActiveCase();
                reset({
                  index: 0,
                  routes: [{ name: 'Main' }],
                });
                await sleep(100);
                setActiveCase(caseId);
                navigate('EApp');
              }
              break;
            }
            default:
              navigate('NotificationScreen');
          }

          handleResponseReceived(response.notification.request.identifier);
          break;
        }
        case 'id': {
          const url = response.notification.request.content.data.url;
          if (url.includes('payment--')) return;
          navigate('NotificationScreen');
          handleResponseReceived(response.notification.request.identifier);
          break;
        }
        default: {
          navigate('NotificationScreen');
          handleResponseReceived(response.notification.request.identifier);
        }
      }
    },
    [
      clearActiveCase,
      handleResponseReceived,
      navigate,
      reset,
      setActiveCase,
      activeAgentCode,
    ],
  );

  return handler;
};

const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));
