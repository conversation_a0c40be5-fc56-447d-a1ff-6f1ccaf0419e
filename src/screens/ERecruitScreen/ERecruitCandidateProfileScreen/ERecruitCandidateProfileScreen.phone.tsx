import React from 'react';
import { country } from 'utils/context';
import { ERAppStatusProps } from './ERecruitCandidateProfileScreen';
import NotFoundScreen from 'screens/NotFoundScreen';
import PHCandidateProfileScreen from 'features/eRecruit/ph/phone/CandidateProfileScreen';
import FIBCandidateProfileLayout from 'features/eRecruit/ib/phone/FIBCandidateProfileLayout';

export default function ERecruitCandidateProfileScreenPhone(
  props: ERAppStatusProps,
) {
  switch (country) {
    case 'ph':
      return <PHCandidateProfileScreen />;
    case 'my':
    case 'ib':
      return <FIBCandidateProfileLayout {...props} />;
    default:
      return <NotFoundScreen />;
  }
}
