import React from 'react';
import { country } from 'utils/context';
import MainScreenPH from 'features/eRecruit/ph/tablet/MainScreenPH';
import MainScreenMY from 'features/eRecruit/my/tablet/MainScreenMY';
import MainScreenIB from 'features/eRecruit/ib/tablet/MainScreenIB';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function ERecruitScreenTablet() {
  switch (country) {
    case 'ph':
      return <MainScreenPH />;
    case 'my':
      return <MainScreenMY />;
    case 'ib':
    case 'id':
      return <MainScreenIB />;
    default:
      return <NotFoundScreen />;
  }
}
