import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitReviewAgentsApplicationScreenTablet from './ERecruitReviewAgentsApplicationScreen.tablet';
import ERecruitReviewAgentsApplicationScreenPhone from './ERecruitReviewAgentsApplicationScreen.phone';
import { ERecruitReviewAgentsApplicationParamList } from 'types';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

export type ERReviewAgentsApplicationProps = NativeStackScreenProps<
  ERecruitReviewAgentsApplicationParamList,
  'ERecruitReviewAgentsApplication'
>;
export default function ERecruitReviewAgentsApplicationScreen(
  props: ERReviewAgentsApplicationProps,
) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  // TODO: get route params for the clicked eRecruit submit app item
  return isTabletMode ? (
    <ERecruitReviewAgentsApplicationScreenTablet {...props} />
  ) : (
    <ERecruitReviewAgentsApplicationScreenPhone {...props} />
  );
}
