import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ERecruitReviewAgentsSubmissionScreenTablet from './ERecruitReviewAgentsSubmissionScreen.tablet';
import ERecruitReviewAgentsSubmissionScreenPhone from './ERecruitReviewAgentsSubmissionScreen.phone';

export default function ERecruitReviewAgentsSubmissionScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <ERecruitReviewAgentsSubmissionScreenTablet />
  ) : (
    <ERecruitReviewAgentsSubmissionScreenPhone />
  );
}
