import AgentProfileScreenTablet from './AgentProfileScreen.tablet';
import AgentProfileScreenPhone from './AgentProfileScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function AgentProfileScreen() {
  // const isTablet = useBoundStore(store => store.isTablet);
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <AgentProfileScreenTablet />
  ) : (
    <AgentProfileScreenPhone />
  );
}
