import React from 'react';
import { country } from 'utils/context';
import ProfileDetails from './ProfileDetails';
import ProfileDetailsScreenPH from 'features/lead/ph/LeadProfile/phone/ProfileDetailsScreen';
import NotFoundScreen from 'screens/NotFoundScreen';
import IdnProfileDetails from './NewProfileDetails';

export default function LeadProfileDetailsScreenPhone() {
  switch (country) {
    case 'ph':
      return <ProfileDetailsScreenPH />;
    case 'my':
    case 'ib':
      return <ProfileDetails />;
    case 'id':
      return <IdnProfileDetails />;
    default:
      return <NotFoundScreen />;
  }
}
