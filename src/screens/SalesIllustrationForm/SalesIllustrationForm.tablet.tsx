import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Header } from '@react-navigation/elements';
import {
  NavigationProp,
  RouteProp,
  StackActions,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { SIPdfWarnings } from 'api/proposalApi';
import DialogTablet from 'components/Dialog.tablet';
import InputEventListener from 'components/Input/InputEventListener';
import {
  Box,
  Button,
  Icon,
  Row,
  Toast,
  Typography,
  XView,
  addToast,
} from 'cube-ui-components';
import { isAfter, parseISO } from 'date-fns';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import FundsAllocation from 'features/proposal/components/FundsAllocation';
import {
  PieChartContextProvider,
  usePieChartContext,
} from 'features/proposal/components/FundsAllocation/common/PieChartContext';
import InsuredDetailsModal from 'features/proposal/components/InsuredDetailsModal';
import PdfFilePopupTablet from 'features/proposal/components/PdfFilePopupTablet';
import PreviewPdfImage from 'features/proposal/components/PreviewPdfImage';
import PuzzleContainer from 'features/proposal/components/PuzzleContainer';
import { PuzzleEdge } from 'features/proposal/components/PuzzleContainer/PuzzleContainer';
import QuotationErrorMessage from 'features/proposal/components/QuotationErrorMessage';
import Rider from 'features/proposal/components/Rider';
import SalesIllustrationDetailsModal from 'features/proposal/components/SalesIllustrationDetailsModal';
import SustainabilityCheckModal from 'features/proposal/components/SalesIllutrationModals/SustainabilityCheckModal';
import SaveQuotationPanel from 'features/proposal/components/SaveQuotationPanel';
import ProposalAgreementModal from 'features/proposal/components/proposalAgreementModal';
import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';
import useQuotationForm from 'features/proposal/hooks/useQuotationForm';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import useRiderDefinitions from 'features/proposal/hooks/useRiderDefinitions';
import useSaveNewQuotation from 'features/proposal/hooks/useSaveNewQuotation';
import {
  FundValidationFeedback,
  SiNextButtonAction,
  SiPdf,
  TopUpFilter,
} from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import { useGetFnaByLeadId } from 'hooks/useGetLeadFna';
import useKeyboardHeight from 'hooks/useKeyboardHeight';
import useSkipScreen from 'hooks/useSkipScreen';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Keyboard,
  KeyboardEvent,
  Pressable,
  TextInput,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import {
  Gesture,
  GestureDetector,
  ScrollView,
} from 'react-native-gesture-handler';
import Animated, {
  FadeIn,
  FadeOut,
  runOnJS,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import RootSiblings from 'react-native-root-siblings';
import { RootStackParamList } from 'types';
import { PartyRole } from 'types/party';
import { ProductId } from 'types/products';
import {
  BasicConfig,
  Plan,
  PlanConfig,
  RiskPrefValidationResult,
} from 'types/quotation';
import { countryModuleSiConfig } from 'utils/config/module';
import GATracking from 'utils/helper/gaTracking';
import getPdfPasswordFromDob from 'utils/helper/getPdfPasswordFromDob';
import StickyHeader from '../../features/proposal/components/StickyHeader/StickyHeader.tablet';
import { useViewSaleIllustrationPdf } from './useViewSaleIllustrationPdf';
import RiskConfirmation from 'features/proposal/components/riskConfirmation/RiskConfirmation';
import { useGetQuotationManually } from 'hooks/useGetQuotation';
import useToggle from 'hooks/useToggle';
import { renderLabelByLanguage } from 'utils/helper/translation';

const Footer = styled.View(({ theme: { space, colors } }) => ({
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  backgroundColor: colors.background,
  paddingHorizontal: space[4],
  height: space[21],
}));

const Container = styled.View(({ theme: { colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[50],
  flex: 1,
}));

const AnimatedKeyboardAwareScrollView =
  Animated.createAnimatedComponent(ScrollView);

const SalesIllustrationForm = () => {
  const params =
    useRoute<RouteProp<RootStackParamList, 'SalesIllustrationForm'>>().params;
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();
  const { colors, space, animation } = useTheme();
  const { t } = useTranslation(['proposal', 'common']);
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();
  const keyboardHeight = useKeyboardHeight();
  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseData } = useGetCase(caseId);
  const leadId = caseData?.parties?.find(
    p => p.roles.includes(PartyRole.PROPOSER) && !!p.leadId,
  )?.leadId;

  const formSource = useMemo(() => {
    if (params?.from) return params.from;
    return 'quick_quote_form';
  }, [params?.from]);

  const [toastArchor, setToastAnchor] = useState<
    RootSiblings | null | undefined
  >();
  const [previewDocsVisible, setPreviewDocsVisible] = useState<boolean>(false);
  const [isSyncTopUp, setSyncTopUp] = useState<boolean>(false);
  const [showSaveQuotationPanel, setShowSaveQuotationPanel] = useState<
    'back' | 'leave'
  >();
  const [visibleAgreement, setVisibleAgreement] = useState<boolean>(false);
  const [showCoverageDetail, setShowCoverageDetail] = useState<boolean>(false);
  const [showSustainabilityModal, setSustainabilityModal] =
    useState<boolean>(false);
  const [
    showSalesIllustrationDetailsModal,
    setShowSalesIllustrationDetailsModal,
  ] = useState<boolean>(false);
  const [isHeaderCollapsed, setHeaderCollapse] = useState(false);
  const [isKbdShowing, setIsKbdShowing] = useState(false);
  const [prevKbdShowing, setPrevKbdShowing] = useState(false);
  const [riskConfirmationVisible, showRiskConfirmation, hideRiskConfirmation] =
    useToggle();
  const [riskType, setRiskType] = useState<RiskPrefValidationResult>();
  const [feedback, setFeedback] = useState<FundValidationFeedback>();
  const scrollRef = useRef<ScrollView>(null);
  const { owner, mainInsured, activeCase, allParties, isEntityFlow } =
    useQuotationInsureds();
  const { data: fnaData } = useGetFnaByLeadId(leadId);
  const hasFnaData = !!fnaData;
  const selectedProductCode = useFnaStore(state => state.selectedProductCode);
  const isMultipleInsureds = useFnaStore(
    state => state.isMultipleInsuredsProduct,
  );
  const { mutateAsync: getQuotation } = useGetQuotationManually();

  const { data: agentProfile } = useGetAgentProfile();

  const quotationPid = (params?.pid ||
    selectedProductCode ||
    rawQuotation?.pid ||
    '') as ProductId;
  const {
    form: {
      control,
      setValue,
      getValues,
      clearErrors,
      formState: { isDirty },
    },
    defaultQuotationName,
    basePlanInfo,
    basePlanDefinition,
    fundsDefinitions,
    isQuickSi,
    rpqResult,
    onStartInput,
    onEndInput,
    isLockFormAction,
    ignoreFormDirty,
    isLockSubmitButton,
    isCalculatingQuotation,
    isInitializingQuotation,
    isValidCaseStatus,
    isResumeQuotation,
    isUpsertingQuotation,
    isUpsertQuotationError,
    errors: { getCaseError, criticalErrors, initError, duplicateError },
    upsertQuotation,
    saveSelectedQuotation,
    triggerQuotationCalculation,
    toggleAgeChangeDialog,
    setReminderModal,
    reminderModal,
    nextButtonAction,
    setNextButtonAction,
    nextButtonLabel,
    nextButtonSubLabel,
    siConfig,
  } = useQuotationForm({
    pid: quotationPid,
    isSyncTopUp,
    hasFnaData,
    lockMainInsuredToProposer:
      isMultipleInsureds || params?.lockMainInsuredToProposer,
    beforeQuotationCalculation: () => {
      // hide the prevvious toast
      if (toastArchor) {
        Toast.hide(toastArchor);
      }
    },
  });

  const { preventNewVersion } = useSaveNewQuotation(
    isResumeQuotation,
    isQuickSi,
  );

  useEffect(() => {
    toggleAgeChangeDialog();
  }, [toggleAgeChangeDialog]);

  useFocusEffect(
    useCallback(() => {
      // trigger ga event when form is opened/exited
      GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
        action_type: GATracking.ACTION_TYPES.SI_OPEN_FORM,
        form_source: formSource,
      });

      return () => {
        GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
          action_type: GATracking.ACTION_TYPES.SI_EXIT,
          form_source: formSource,
        });
      };
    }, [formSource]),
  );

  const pdfPopupItems = useMemo(() => {
    const items = [
      {
        title: t('proposal:attachment.proposal'),
        fileType: SiPdf.SI_PROPOSAL,
        disabled: isLockFormAction,
      },
      {
        title: t('proposal:attachment.productBrochure'),
        fileType: SiPdf.PRODUCT_BROCHURE,
        disabled: false,
      },
    ];

    const hasProductTipsBrochure = rawQuotation?.plans?.find?.(
      i => i?.tipsBrochureLink,
    );

    if (hasProductTipsBrochure) {
      items.push({
        title: t('proposal:attachment.productTipsBrochure'),
        fileType: SiPdf.TIPS_PRODUCT_BROCHURE,
        disabled: false,
      });
    }

    const hasProductHighlight = rawQuotation?.plans?.find?.(
      i => i?.productHighlight,
    );

    if (hasProductHighlight) {
      items.push({
        title: t('proposal:attachment.productHighlights'),
        fileType: SiPdf.PRODUCT_HIGHLIGHTS,
        disabled: false,
      });
    }

    const hasRIPLAY = rawQuotation?.plans?.find?.(i => i?.riplayLink);
    if (hasRIPLAY) {
      items.push({
        title: 'RIPLAY',
        fileType: SiPdf.RIPLAY,
        disabled: false,
      });
    }

    return items;
  }, [isLockFormAction, rawQuotation?.plans, t]);

  const isKbdTogging = prevKbdShowing || isKbdShowing;
  /**
   * handling expand/collapse of sticky header
   */
  const headerExpandedHeight = useSharedValue(0);
  const headerCollapsedHeight = useSharedValue(0);
  const headerHeight = useSharedValue(headerExpandedHeight.value);
  const transY = useSharedValue(headerExpandedHeight.value);
  const movedY = useSharedValue(0);
  const scrollY = useSharedValue(0);
  const scrollWrapperStyle = useAnimatedStyle(() => ({
    // don't open beyond the open limit
    transform: [
      {
        translateY: transY.value,
      },
    ],
    zIndex: -1,
  }));
  const updateKeyboardStatus = (isOpened: boolean) => {
    setIsKbdShowing(isOpened);
    setPrevKbdShowing(!isOpened);
  };
  // scroll handler for scrollview
  const scrollHandler = useAnimatedScrollHandler(({ contentOffset }) => {
    scrollY.value = Math.round(contentOffset.y);
  });

  const scrollGesture = Gesture.Pan()
    .onUpdate(e => {
      if (isInitializingQuotation) return; // disable gesture when initializing form

      if (
        scrollY.value === 0 ||
        headerHeight.value === headerExpandedHeight.value
      ) {
        transY.value = Math.min(
          Math.max(
            headerHeight.value + e.translationY - movedY.value,
            headerCollapsedHeight.value,
          ),
          headerExpandedHeight.value,
        );
      } else {
        movedY.value = e.translationY;
      }
    })
    .onEnd(e => {
      if (isInitializingQuotation) return; // disable gesture when initializing form

      // Expand header if large velocity
      if ((e.velocityY > 500 || e.translationY > 100) && scrollY.value < 1) {
        transY.value = withTiming(
          headerExpandedHeight.value,
          {
            duration: animation.duration,
          },
          () => runOnJS(setHeaderCollapse)(false),
        );
        headerHeight.value = headerExpandedHeight.value;
        // else collapse header
      } else if (e.velocityY < -500 || e.translationY < -100) {
        transY.value = withTiming(
          headerCollapsedHeight.value,
          {
            duration: animation.duration,
          },
          () => runOnJS(setHeaderCollapse)(true),
        );
        headerHeight.value = headerCollapsedHeight.value;
        // Do nothing if not enough velocity
      } else {
        transY.value = withTiming(headerHeight.value, {
          duration: animation.duration,
        });
      }
    })
    .onFinalize(() => {
      // stopped touching screen
      movedY.value = 0;
    })
    .simultaneousWithExternalGesture(scrollRef);

  const {
    pdfVisible,
    pdfGenerator,
    title,
    hidePdf,
    showPdf,
    mailConfig,
    sharable,
    generatePdfError,
    isGeneratingPdf,
    viewSiPdf,
    viewProductBrochure,
    viewTipsProductBrochure,
    viewProductHighlight,
    viewRIPLAY,
    handleOnEmailSent,
    isRequiredToReadAllPdf,
    sendPdfButtonLabels,
    isPdfSIloaded,
  } = useViewSaleIllustrationPdf();
  const { getPieChartBase64Image } = usePieChartContext();
  const topupRiderPlanDefinitions = useRiderDefinitions({
    topUpFilter: TopUpFilter.TOPUP_ONLY,
  });

  const selectableRiderDefintions = useRiderDefinitions({
    selectableOnly: true,
    topUpFilter: TopUpFilter.NON_TOPUP_ONLY,
  });

  const isStartRPQ = siConfig.rpq.enabled && !rpqResult;
  const maxFund = siConfig.maxFund;

  const puzzleContents = [
    Array.isArray(topupRiderPlanDefinitions) &&
    topupRiderPlanDefinitions.length > 0
      ? {
          key: 'PuzzleContent-Rider-Topup-Only',
          component: (
            <Rider
              sectionFilter={TopUpFilter.TOPUP_ONLY}
              setValue={setValue}
              getValues={getValues}
              control={control}
              onRiderPlanUpdated={triggerQuotationCalculation}
              isAllRidersMandatory={false}
            />
          ),
        }
      : null,
    Array.isArray(selectableRiderDefintions) &&
    selectableRiderDefintions.length > 0
      ? {
          key: 'PuzzleContent-Rider-Non-Topup-Only',
          component: (
            <Rider
              sectionFilter={TopUpFilter.NON_TOPUP_ONLY}
              setValue={setValue}
              getValues={getValues}
              control={control}
              onRiderPlanUpdated={triggerQuotationCalculation}
              isAllRidersMandatory={false}
            />
          ),
        }
      : null,
    Array.isArray(fundsDefinitions) && fundsDefinitions.length > 0
      ? {
          key: 'PuzzleContent-FundsAllocation',
          component: (
            <FundsAllocation
              control={control}
              leadId={leadId}
              setValue={setValue}
              maxFund={maxFund}
              isStartRPQ={isStartRPQ}
              clearErrors={clearErrors}
              onSyncTopUp={setSyncTopUp}
              triggerValidation={triggerQuotationCalculation}
            />
          ),
        }
      : null,
  ].filter(Boolean) as Array<{ key: string; component: React.JSX.Element }>;

  const riderPlans = useRiderPlanInfo(undefined, {
    forDisplayOnly: true,
  }) as Plan[];

  const coveredInsureds = allParties.filter(
    person =>
      riderPlans?.some(rider => rider.insuredId === person.insuredId) ||
      person.isMainInsured,
  );

  const coveredInsuredsAmount = coveredInsureds.length;

  const baseAndBasicPlanConfig = useMemo(
    () =>
      ({
        ...basePlanDefinition?.planConfig,
        ...basePlanDefinition?.basicConfig,
      } as PlanConfig & BasicConfig),
    [basePlanDefinition],
  );

  const isExpired = useMemo(() => {
    if (!rawQuotation?.expiryDate) {
      return false;
    }

    const expiredDate = parseISO(rawQuotation?.expiryDate);
    if (!expiredDate) {
      return false;
    }

    return isAfter(Date.now(), expiredDate);
  }, [rawQuotation?.expiryDate]);

  const onPressBack = () => setShowSaveQuotationPanel('back');

  const { backToBefore } = useSkipScreen();

  const onBackFullSi = useCallback(() => {
    GATracking.logButtonPress({
      screenName,
      screenClass: 'Sales flow',
      actionType: 'non_cta_button',
      buttonName: 'Back to previous screen',
    });
    params?.goBack && navigation.canGoBack()
      ? navigation.goBack()
      : navigation.navigate('Main');
  }, [screenName, params?.goBack, navigation]);

  const onBack = useCallback(
    (shouleSkipProductionSelection = false) => {
      shouleSkipProductionSelection
        ? backToBefore('ProductSelection')
        : navigation.goBack();

      GATracking.logButtonPress({
        screenName: 'Sales Illustration',
        screenClass: 'Sales flow',
        actionType: 'non_cta_button',
        buttonName: shouleSkipProductionSelection
          ? 'Back to root screen'
          : 'Back to product selection',
      });
    },

    [navigation, backToBefore],
  );

  const onSaveQuotation = useCallback(
    async (quotationName: string, isNewVersion: boolean) => {
      const pieChartBase64Image = await getPieChartBase64Image?.();

      const shouleSkipProductionSelection = showSaveQuotationPanel === 'leave';

      await upsertQuotation(
        quotationName.trim(),
        isNewVersion,
        undefined,
        pieChartBase64Image,
      );
      setShowSaveQuotationPanel(undefined);

      GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
        action_type: GATracking.ACTION_TYPES.SI_SAVE,
        form_source: formSource,
      });

      setTimeout(() => {
        addToast(
          [
            {
              message: isNewVersion
                ? t('proposal:savedNewVersion')
                : t('proposal:saved'),
              IconLeft: <Icon.Tick />,
            },
          ],
          false,
          true,
        );

        onBack(shouleSkipProductionSelection);
      }, animation.duration);
    },
    [
      getPieChartBase64Image,
      showSaveQuotationPanel,
      upsertQuotation,
      formSource,
      animation.duration,
      t,
      onBack,
    ],
  );

  const handleInputEvent = useCallback(
    (name: string) => {
      onEndInput();
      console.log('input event: ', name);
      if (basePlanInfo?.calcBy) {
        switch (name) {
          case 'basePlan.sumAssured':
            setValue('basePlan.calcBy', 'sumAssured');
            break;
          case 'basePlan.premium':
            setValue('basePlan.calcBy', 'premium');
            break;
          default:
            break;
        }
      }

      triggerQuotationCalculation();
    },
    [basePlanInfo?.calcBy, onEndInput, setValue, triggerQuotationCalculation],
  );

  const pdfPassword = useMemo(() => {
    if (!countryModuleSiConfig.pdf.isPasswordProtected) return undefined;

    const dob = isEntityFlow ? mainInsured?.dob : owner?.dob;
    return getPdfPasswordFromDob(dob?.toString());
  }, [isEntityFlow, mainInsured?.dob, owner?.dob]);

  // const prepareSiPdf = useCallback(
  //   async (
  //     language: Language,
  //     password?: string,
  //   ): Promise<PdfViewerProps | undefined> => {
  //     const pieChartBase64Image = await getPieChartBase64Image?.();

  //     const result = await generateSiPdf(
  //       language,
  //       pieChartBase64Image,
  //       password,
  //     );

  //     setSiPdf(result);

  //     return result;
  //   },
  //   [getPieChartBase64Image, generateSiPdf],
  // );

  const nextButtonDisabled = useMemo(() => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL)
      return isLockFormAction || isInitializingQuotation || isGeneratingPdf;

    return isLockFormAction || isLockSubmitButton || isInitializingQuotation;
  }, [
    isGeneratingPdf,
    isInitializingQuotation,
    isLockFormAction,
    isLockSubmitButton,
    nextButtonAction,
  ]);

  const onFinishQuickQuote = useCallback(async () => {
    await upsertQuotation('__FinishQuickSi__', true);

    GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
      action_type: GATracking.ACTION_TYPES.SI_COMPLETE,
      form_source: formSource,
    });

    siConfig.flow.screenName !== 'EApp'
      ? GATracking.logCustomEvent('fn_assessment', {
          action_type: 'fna_open_form',
          form_source: 'sales_illustration',
        })
      : GATracking.logCustomEvent('application', {
          action_type: 'eapp_open_form',
          form_source: 'sales_illustration_form ',
        });

    navigation.dispatch(
      StackActions.replace(siConfig.flow.screenName ?? 'Fna'),
    );
  }, [upsertQuotation, formSource, navigation, siConfig.flow.screenName]);

  const onFinishFullQuote = useCallback(async () => {
    const pieChartBase64Image = await getPieChartBase64Image?.();
    await saveSelectedQuotation({
      quotationName: defaultQuotationName,
      fundsPieChartImage: pieChartBase64Image,
      feedback,
    });

    GATracking.logCustomEvent(GATracking.EVENTS.SALES_ILLUSTRATION, {
      action_type: GATracking.ACTION_TYPES.SI_START_APPLICATION,
      form_source: formSource,
    });

    navigation.dispatch(
      StackActions.replace(siConfig.flow.screenName ?? 'EApp'),
    );
  }, [
    getPieChartBase64Image,
    saveSelectedQuotation,
    defaultQuotationName,
    formSource,
    navigation,
    feedback,
    siConfig.flow.screenName,
  ]);

  const handlePreviewSiPdf = useCallback(
    async (params?: { feedback?: InvestmentPreferenceReason }) => {
      // handle the risk pref confirmation in Takaful region
      // So the view PDF action in the confirm button of modal
      if (!params?.feedback && caseId && rpqResult?.riskPref) {
        const qid = await upsertQuotation(defaultQuotationName, false);
        if (qid) {
          const q = await getQuotation({
            caseId,
            quotationId: qid,
          });
          if (q && q.riskPrefValidationResult) {
            setRiskType(q.riskPrefValidationResult);
            showRiskConfirmation();
            return;
          }
        }
      }

      if (isLockSubmitButton || !isPdfSIloaded) {
        const pieChartBase64Image = await getPieChartBase64Image?.();
        const payload = {
          pdfPassword,
          fundsPieChartImage: pieChartBase64Image,
        };
        if (params?.feedback) {
          payload.feedback = params.feedback;
        }

        viewSiPdf(payload);
        if (
          !countryModuleSiConfig.email.isMailToEnabled[
            isQuickSi ? 'quickQuote' : 'fullQuote'
          ]
        ) {
          setNextButtonAction(SiNextButtonAction.PROCEED_NEXT_STEP);
        }
      } else {
        showPdf();
      }
    },
    [
      isQuickSi,
      isLockSubmitButton,
      getPieChartBase64Image,
      viewSiPdf,
      pdfPassword,
      setNextButtonAction,
      showPdf,
      isPdfSIloaded,
    ],
  );

  const onPressNextButton = useCallback(async () => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL) {
      await handlePreviewSiPdf();
      return;
    }

    if (isQuickSi) {
      await onFinishQuickQuote();
      return;
    }

    if (siConfig.flow.enableFullQuoteAgreement) {
      setVisibleAgreement(true);
      return;
    }

    await onFinishFullQuote();
  }, [
    isQuickSi,
    nextButtonAction,
    handlePreviewSiPdf,
    setVisibleAgreement,
    onFinishQuickQuote,
    onFinishFullQuote,
    siConfig.flow,
  ]);

  const onPdfItemPress = (pdfType: SiPdf) => {
    setPreviewDocsVisible(false);
    pdfType === SiPdf.SI_PROPOSAL && handlePreviewSiPdf();
    pdfType === SiPdf.TIPS_PRODUCT_BROCHURE && viewTipsProductBrochure();
    pdfType === SiPdf.PRODUCT_BROCHURE && viewProductBrochure();
    pdfType === SiPdf.PRODUCT_HIGHLIGHTS && viewProductHighlight();
    pdfType === SiPdf.RIPLAY && viewRIPLAY();
  };

  const onAgreementModalClose = useCallback(
    async (isConfirm: boolean) => {
      setVisibleAgreement(false);
      if (isConfirm) {
        onFinishFullQuote();
      }
    },
    [onFinishFullQuote],
  );

  const navigateSimulation = () => {
    navigation.navigate('SimulationTable', { pid: quotationPid });
  };

  const saveProposalNameError = useCallback(
    (
      defaultValue: string,
      currentValue: string,
      shouCheckSameName: boolean,
    ) => {
      const resumeNewVersionDuplicate =
        isResumeQuotation &&
        ((shouCheckSameName && currentValue.trim() === defaultValue) ||
          isUpsertQuotationError);
      const duplicateErrorOnBackCreate =
        duplicateError && currentValue.trim() === defaultValue;
      return resumeNewVersionDuplicate || duplicateErrorOnBackCreate
        ? t('proposal:error.invalidProposalName')
        : undefined;
    },
    [isResumeQuotation, isUpsertQuotationError, duplicateError, t],
  );

  useEffect(() => {
    if (getCaseError) {
      console.warn('get errors from case fetching: ', getCaseError);
      navigation.goBack();
    }
  }, [getCaseError, navigation]);

  useEffect(() => {
    if (!isValidCaseStatus) {
      navigation.goBack();
      return;
    }
  }, [isValidCaseStatus, navigation]);

  useEffect(() => {
    if (isSyncTopUp) {
      triggerQuotationCalculation();
    }
  }, [isSyncTopUp]);

  useEffect(() => {
    if (Array.isArray(generatePdfError)) {
      setSustainabilityModal(true);
    }
  }, [generatePdfError]);

  useEffect(() => {
    if (criticalErrors) {
      const errorMessages = criticalErrors.map(e => ({
        message: t(`proposal:criticalError.${e.errorCode}`),
      }));

      const ta = Toast.show(errorMessages, {
        duration: undefined,
        type: 'errorBottom',
        showDismiss: true,
      });
      setToastAnchor(ta);
    }
  }, [criticalErrors, t]);

  // manually handle scroll into focussed input when keyboard opened
  useEffect(() => {
    const keyboardDidShowSubscription = Keyboard.addListener(
      'keyboardDidShow',
      (e: KeyboardEvent) => {
        const keyboardHeight = e.endCoordinates.height;

        if (!TextInput.State.currentlyFocusedInput()) return;

        TextInput.State.currentlyFocusedInput().measure(
          (x, y, width, componentHeight, pageX, pageY) => {
            if (pageY < windowHeight - keyboardHeight) return; // skip if input isn't overlapped by keyboard

            const offset =
              pageY -
              windowHeight +
              keyboardHeight +
              componentHeight +
              space[10];
            updateKeyboardStatus(true);
            scrollRef.current?.scrollTo({
              y: scrollY.value + offset,
            });
          },
        );
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        updateKeyboardStatus(false);
        scrollRef.current?.scrollTo({
          y: scrollY.value,
        });
      },
    );

    return () => {
      keyboardDidShowSubscription.remove();
      keyboardDidHideListener.remove();
    };
  }, [windowHeight, scrollY.value, space]);

  const previewImageVariant = useMemo(() => {
    if (isInitializingQuotation) {
      return 'disabled';
    }

    // ignore the form dirty stage, becuase the quotation is ready in initial call
    if (!ignoreFormDirty && !isDirty && !(params?.from === 'saved_proposals')) {
      return 'disabled';
    }

    if (isLockFormAction) {
      return 'error';
    }

    return 'success';
  }, [
    isInitializingQuotation,
    ignoreFormDirty,
    isDirty,
    params?.from,
    isLockFormAction,
  ]);

  return (
    <Container>
      <Box zIndex={1}>
        <Header
          headerTitle={({ children }) => (
            <Typography.LargeLabel
              fontWeight="bold"
              color={colors.palette.fwdDarkGreen[100]}>
              {children}
            </Typography.LargeLabel>
          )}
          headerTitleAlign="left"
          title={t('proposal:header.tablet.title')}
          headerRight={() => (
            <Row>
              <Pressable
                style={{
                  flexDirection: 'row',
                  gap: space[1],
                  alignItems: 'center',
                  marginRight: space[8],
                }}
                onPress={() => setShowSalesIllustrationDetailsModal(true)}>
                <Icon.ProductTick
                  fill={colors.palette.fwdDarkGreen[100]}
                  height={space[6]}
                  width={space[6]}
                />
                <Typography.Body
                  fontWeight="bold"
                  color={colors.palette.fwdDarkGreen[100]}>
                  {t('proposal:header.tablet.productYouAreGoingToApply')}
                </Typography.Body>
              </Pressable>

              <Pressable
                style={{
                  flexDirection: 'row',
                  gap: space[1],
                  alignItems: 'center',
                  marginRight: space[8],
                }}
                onPress={() => setShowCoverageDetail(true)}>
                <Icon.Team
                  fill={colors.palette.fwdDarkGreen[100]}
                  height={space[6]}
                  width={space[6]}
                />
                <Typography.Body
                  fontWeight="bold"
                  color={colors.palette.fwdDarkGreen[100]}>
                  {`${t('proposal:insuredDetails.title')} ${
                    coveredInsuredsAmount ? `(${coveredInsuredsAmount})` : ''
                  }`}
                </Typography.Body>
              </Pressable>

              <Pressable
                style={{
                  flexDirection: 'row',
                  gap: space[1],
                  alignItems: 'center',
                }}
                onPress={() =>
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'Main' }],
                  })
                }>
                <Icon.Home
                  fill={colors.palette.fwdDarkGreen[100]}
                  height={space[6]}
                  width={space[6]}
                />
                <Typography.Body
                  fontWeight="bold"
                  color={colors.palette.fwdDarkGreen[100]}>
                  {t('proposal:header.tablet.home')}
                </Typography.Body>
              </Pressable>

              <InsuredDetailsModal
                insureds={allParties}
                caseParties={activeCase?.parties ?? []}
                isVisible={showCoverageDetail}
                onClose={() => setShowCoverageDetail(false)}
              />

              <SalesIllustrationDetailsModal
                isVisible={showSalesIllustrationDetailsModal}
                onClose={() => setShowSalesIllustrationDetailsModal(false)}
              />
            </Row>
          )}
          headerLeft={() => (
            <Pressable onPress={onPressBack} disabled={isInitializingQuotation}>
              <Icon.ArrowLeft
                size={space[6]}
                fill={colors.palette.fwdDarkGreen[100]}
              />
            </Pressable>
          )}
          headerLeftContainerStyle={{
            paddingLeft: space[3],
            justifyContent: 'center',
          }}
          headerRightContainerStyle={{
            paddingRight: space[3],
            justifyContent: 'center',
          }}
          headerStyle={{ backgroundColor: colors.background }}
        />
      </Box>

      {isExpired && (
        <QuotationErrorMessage
          message={t('proposal:criticalError.QuotationExpired')}
        />
      )}

      {initError && (
        <QuotationErrorMessage message={t('proposal:error.initError')} />
      )}

      <InputEventListener
        onFocus={onStartInput}
        onBlur={handleInputEvent}
        onDropdownPicked={handleInputEvent}
        setFormValue={setValue}>
        <GestureDetector gesture={scrollGesture}>
          <Box flex={1}>
            <StickyHeader
              currentY={transY}
              pid={quotationPid}
              navigateSimulation={navigateSimulation}
              isLockFormAction={isLockFormAction}
              planName={basePlanInfo?.productName?.en ?? '--'}
              productLine={basePlanInfo?.productLineName?.en ?? '--'}
              logo={basePlanInfo?.thumbnail}
              currency={basePlanInfo?.currency}
              sumAssured={basePlanInfo?.sumAssured}
              modalPremium={basePlanInfo?.modalPremium}
              paymentMode={basePlanInfo?.paymentMode}
              paymentTerm={basePlanInfo?.premiumTerm}
              paymentType={basePlanInfo?.paymentType}
              policyBenefitPeriod={basePlanInfo?.policyTerm}
              owner={basePlanInfo.owner}
              insured={basePlanInfo.insured}
              isInitializing={isInitializingQuotation}
              animationDisabled={false}
              control={control}
              basePlanConfig={baseAndBasicPlanConfig}
              annualPrem={basePlanInfo?.annualPrem}
              totalAnnualPrem={basePlanInfo?.totalAnnualPrem}
              saverAnnualPrem={basePlanInfo?.saverAnnualPrem}
              totalInitialPrem={basePlanInfo?.totalInitialPrem}
              totalPrem={basePlanInfo?.totalPrem}
              premiumTerm={basePlanInfo?.premiumTerm}
              maxPolicyTerm={basePlanInfo?.maxPolicyTerm}
              maxPremiumTerm={basePlanInfo?.maxPremiumTerm}
              deathBenefitOption={basePlanInfo?.deathBenefitOption}
              isCollapsed={isHeaderCollapsed}
              isKeepBottomFlatten={!puzzleContents.length}
              onExpandedLayout={e => {
                if (!isKbdTogging || !isHeaderCollapsed) {
                  headerExpandedHeight.value = e.nativeEvent?.layout?.height;
                  headerHeight.value = e.nativeEvent?.layout?.height;
                  transY.value = e.nativeEvent?.layout?.height;
                }
                // reset kbd status for toggle checking to work correctly
                if (prevKbdShowing && !isKbdShowing) {
                  setPrevKbdShowing(false);
                }
              }}
              onCollapsedLayout={e => {
                headerCollapsedHeight.value = e.nativeEvent.layout.height;
              }}
              isMultipleInsureds={basePlanInfo.isMultipleInsureds}
              triggerCalculation={triggerQuotationCalculation}
              setValue={setValue}
            />

            <Animated.View style={scrollWrapperStyle}>
              <AnimatedKeyboardAwareScrollView
                ref={scrollRef}
                bounces={false}
                overScrollMode="never"
                onScroll={scrollHandler}
                scrollEventThrottle={16}
                scrollEnabled={isHeaderCollapsed}
                style={{
                  marginHorizontal: space[4],
                  zIndex: -1,
                  paddingTop: space[3],
                }}>
                {puzzleContents.map((puzzleContent, index) => {
                  const isFirstPuzzle = index === 0;
                  const isLastPuzzle = index === puzzleContents.length - 1;

                  const topEdge =
                    isHeaderCollapsed && isFirstPuzzle
                      ? PuzzleEdge.Flat
                      : PuzzleEdge.Pocket;

                  const bottomEdge = isLastPuzzle
                    ? PuzzleEdge.Flat
                    : PuzzleEdge.Knob;

                  return (
                    <View
                      key={puzzleContent.key}
                      style={[
                        !isFirstPuzzle && { marginTop: space[3] },
                        {
                          zIndex: puzzleContents.length - index,
                        },
                      ]}>
                      <PuzzleContainer
                        topEdge={topEdge}
                        bottomEdge={bottomEdge}
                        crossFillColor={colors.palette.fwdGrey[100]}
                        screenColor={colors.palette.fwdGrey[50]}>
                        {React.cloneElement(puzzleContent.component, {
                          step: index + 2, // step index will start from 2
                        })}
                      </PuzzleContainer>
                    </View>
                  );
                })}

                <View
                  key={keyboardHeight + 'make-keyboard-change'}
                  style={{
                    height: windowHeight - 500 + keyboardHeight,
                    width: '100%',
                  }}
                />
              </AnimatedKeyboardAwareScrollView>
            </Animated.View>
          </Box>
        </GestureDetector>
      </InputEventListener>

      <Footer>
        <XView style={{ height: '100%', alignItems: 'center' }}>
          <View style={{ flex: 1, height: '100%' }}>
            <XView
              style={{
                alignItems: 'flex-end',
                position: 'absolute',
                bottom: 0,
                zIndex: 9,
                elevation: 9,
              }}>
              <TouchableOpacity
                onPress={() => handlePreviewSiPdf()}
                disabled={isLockFormAction}>
                <Animated.View entering={FadeIn} exiting={FadeOut}>
                  <PreviewPdfImage variant={previewImageVariant} />
                </Animated.View>
              </TouchableOpacity>
              <XView
                style={{
                  alignItems: 'center',
                  height: space[21],
                  marginLeft: space[4],
                }}>
                <TouchableOpacity
                  style={{
                    flexDirection: 'row',
                  }}
                  disabled={!countryModuleSiConfig.pdf.isPreviewable}
                  onPress={() =>
                    countryModuleSiConfig.pdf.isPreviewable &&
                    setPreviewDocsVisible(true)
                  }>
                  <Typography.H6
                    fontWeight="bold"
                    color={colors.palette.fwdDarkGreen[100]}
                    style={{ marginRight: space[2] }}>
                    {t('proposal:previewDocument')}
                  </Typography.H6>
                  <View style={{ paddingTop: space[1] }}>
                    <Icon.ChevronUp
                      fill={
                        isLockFormAction
                          ? colors.palette.fwdGreyDarker
                          : colors.palette.fwdDarkGreen[100]
                      }
                    />
                  </View>
                </TouchableOpacity>
              </XView>
            </XView>
          </View>
          <Button
            disabled={isLockFormAction || isInitializingQuotation}
            loading={isUpsertingQuotation}
            text={t('common:save')}
            variant="secondary"
            onPress={() => setShowSaveQuotationPanel('leave')}
            contentStyle={{
              width: space[29],
              height: space[13],
            }}
          />
          <Button
            disabled={nextButtonDisabled}
            loading={isCalculatingQuotation}
            text={nextButtonLabel}
            style={{ marginLeft: space[3] }}
            contentStyle={{ height: space[13] }}
            subtext={nextButtonSubLabel}
            onPress={onPressNextButton}
          />
        </XView>

        {previewDocsVisible && (
          <PdfFilePopupTablet
            onClose={() => setPreviewDocsVisible(false)}
            onPress={onPdfItemPress}
            pdfPopupItems={pdfPopupItems}
          />
        )}
      </Footer>

      <ProposalAgreementModal
        handleClose={isConfirm => onAgreementModalClose(isConfirm)}
        visible={visibleAgreement}
      />

      {pdfVisible && (
        <PdfViewer
          visible={pdfVisible}
          onClose={hidePdf}
          downloadable
          pdfGenerator={pdfGenerator.current}
          title={title}
          sharable={sharable}
          shareType="email"
          mailConfig={mailConfig}
          actionOption={
            sharable && isRequiredToReadAllPdf
              ? {
                  activeMode: 'end-of-file',
                  actionMode: 'send-email',
                  onEmailSent: async emailBody => {
                    await handleOnEmailSent(emailBody);

                    GATracking.logCustomEvent(
                      GATracking.EVENTS.SALES_ILLUSTRATION,
                      {
                        action_type: GATracking.ACTION_TYPES.SI_SEND_PDF,
                        form_source: formSource,
                      },
                    );

                    setNextButtonAction(SiNextButtonAction.PROCEED_NEXT_STEP);
                  },
                  ...sendPdfButtonLabels,
                }
              : undefined
          }
          extra={{
            '1': renderLabelByLanguage(basePlanInfo?.productName) ?? '',
            '2': basePlanInfo?.owner?.person?.name?.fullName ?? '',
            '3': rawQuotation?.proposalNum ?? '',
          }}
        />
      )}

      <SaveQuotationPanel
        visible={!!showSaveQuotationPanel}
        onClose={() => setShowSaveQuotationPanel(undefined)}
        onBack={isQuickSi ? () => onBack(false) : onBackFullSi}
        onSave={onSaveQuotation}
        preventNewVersion={preventNewVersion}
        defaultQuotationName={defaultQuotationName}
        error={saveProposalNameError}
        //
        saveDisabled={isLockFormAction || isInitializingQuotation}
        saveQuotationPanelControl={showSaveQuotationPanel}
      />

      <SustainabilityCheckModal
        isVisible={showSustainabilityModal}
        warnings={(generatePdfError as SIPdfWarnings[]) ?? []}
        onCancel={() => setSustainabilityModal(false)}
        setValue={setValue}
        triggerCalculation={triggerQuotationCalculation}
      />

      <DialogTablet
        visible={reminderModal.visible}
        style={{ width: '40%', padding: space[12] }}>
        <Typography.H6
          fontWeight="bold"
          style={{
            marginBottom: space[3],
          }}>
          {reminderModal.title}
        </Typography.H6>
        <Typography.Body>{reminderModal.description}</Typography.Body>
        <Button
          style={{ marginTop: space[6], width: space[50], alignSelf: 'center' }}
          text={t('common:ok')}
          onPress={() => setReminderModal({ ...reminderModal, visible: false })}
        />
      </DialogTablet>
      <RiskConfirmation
        visible={riskConfirmationVisible}
        riskType={riskType}
        onDismiss={() => {
          hideRiskConfirmation();
        }}
        onConfirm={async fb => {
          console.debug('feedback: ', fb);
          hideRiskConfirmation();
          setFeedback(fb);
          await handlePreviewSiPdf({ feedback: fb });
        }}
      />
    </Container>
  );
};

export default () => (
  <PieChartContextProvider>
    <SalesIllustrationForm />
  </PieChartContextProvider>
);
