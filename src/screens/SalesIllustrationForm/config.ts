import { SiConfig } from 'types/config';
import { countryModuleSiConfig } from 'utils/config/module';

export const getConfig = (
  isEntityFlow?: boolean,
  isQuickQoute?: boolean,
  hasFnaData?: boolean,
): SiConfig => {
  let flowConfig = undefined;
  let rpqConfig = undefined;
  let maxFundConfig = undefined;
  const fundAllocationMultipleOfNumber =
    countryModuleSiConfig.fund.fundAllocationMultipleOfNumber;
  const topUpAllocationMultipleOfNumber =
    countryModuleSiConfig.fund.topUpAllocationMultipleOfNumber;

  if (isEntityFlow) {
    rpqConfig = countryModuleSiConfig.rpq.entity;
    flowConfig = countryModuleSiConfig.flow.entity;
    maxFundConfig = countryModuleSiConfig.maxFund;
  } else {
    rpqConfig = countryModuleSiConfig.rpq.individual;
    flowConfig = countryModuleSiConfig.flow.individual;
    maxFundConfig = countryModuleSiConfig.maxFund;
  }

  if (isQuickQoute) {
    return {
      rpq: { enabled: rpqConfig.enabled },
      flow: {
        screenName: flowConfig.quickQuoteScreenName,
        label: hasFnaData
          ? flowConfig.quickQuoteLabelWithFNA
          : flowConfig.quickQuoteLabel,
      },
      maxFund: maxFundConfig,
      fundAllocationMultipleOfNumber,
      topUpAllocationMultipleOfNumber,
      isProductReselectionEnabled:
        countryModuleSiConfig.isProductReselectionEnabled,
      isSendEmailRequiredToProceed:
        countryModuleSiConfig.email.sendEmailToProceed.quickQuote,
    };
  } else {
    return {
      rpq: { enabled: rpqConfig.enabled },
      flow: {
        screenName: flowConfig.fullQuoteScreenName,
        label: flowConfig.fullQuoteLabel,
        enableFullQuoteAgreement: flowConfig.enableFullQuoteAgreement,
      },
      maxFund: maxFundConfig,
      fundAllocationMultipleOfNumber,
      topUpAllocationMultipleOfNumber,
      isProductReselectionEnabled:
        countryModuleSiConfig.isProductReselectionEnabled,
      isSendEmailRequiredToProceed:
        countryModuleSiConfig.email.sendEmailToProceed.fullQuote,
    };
  }
};
