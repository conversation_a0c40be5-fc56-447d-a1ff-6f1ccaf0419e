import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import CustomerProfileDetailsScreenPhone from './CustomerProfileDetailsScreen.phone';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function CustomerProfileDetailsScreen({
  route,
}: {
  route: RouteProp<RootStackParamList, 'CustomerProfileDetails'>;
}) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <NotFoundScreen />
  ) : (
    <CustomerProfileDetailsScreenPhone route={route} />
  );
}
