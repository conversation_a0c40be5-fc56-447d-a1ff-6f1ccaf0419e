import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import NewBusinessListScreenPH, {
  NewBusinessProps,
} from 'features/policy/ph/phone/NewBusinessListScreen';
import NewBusinessListScreenIB from 'features/policy/ib/phone/NewBusinessListScreen';

export default function NewBusinessListScreenPhone(props: NewBusinessProps) {
  switch (country) {
    case 'ph':
      return <NewBusinessListScreenPH {...props} />;
    case 'my':
    case 'ib':
      return <NewBusinessListScreenIB {...props} />;
    default:
      return <NotFoundScreen />;
  }
}
