import React from 'react';
import { country } from 'utils/context';
import NotFoundScreen from 'screens/NotFoundScreen';
import PolicyPhoneMainScreen from 'features/policy/components/phone';

export default function PoliciesScreenPhone() {
  switch (country) {
    case 'ph':
    case 'ib':
    case 'my':
    case 'id':
      return <PolicyPhoneMainScreen />;
    default:
      return <NotFoundScreen />;
  }
}
