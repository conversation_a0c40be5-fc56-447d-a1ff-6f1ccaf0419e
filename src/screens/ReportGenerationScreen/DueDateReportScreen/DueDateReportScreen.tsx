import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DueDateReportScreenTablet from './DueDateReportScreen.tablet';
import DueDateReportScreenPhone from './DueDateReportScreen.phone';

export default function DueDateReportScreen(props: any) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <DueDateReportScreenTablet {...props} />
  ) : (
    <DueDateReportScreenPhone />
  );
}
