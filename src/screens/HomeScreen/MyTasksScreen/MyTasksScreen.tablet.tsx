import React, { Fragment, useMemo, useState } from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, Typography, XView } from 'cube-ui-components';
import BirthdaySection from 'features/home/<USER>/MyTasks/BirthdaySection';
import ContactLeadsSection from 'features/home/<USER>/MyTasks/ContactLeadsSection';
import PaymentRemindersSection from 'features/home/<USER>/MyTasks/PaymentRemindersSection';
import PolicyIssuesSection from 'features/home/<USER>/MyTasks/PolicyIssuesSection';
import ReorderPanel from 'features/home/<USER>/utils/ReorderPanel/tablet';
import useBoundStore from 'hooks/useBoundStore';
import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import useScheduledNotifications from 'features/task/hooks/useScheduledNotifications';
import { BuildCountry, RootStackParamListMap } from 'types';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import { TodayTasksSectionsKeys } from 'types/home';
import TaskEmptyRecordCard from 'features/home/<USER>/MyTasks/TaskEmptyRecordCard';
import { useHasPermission } from 'hooks/useCheckClientScope';

export default function MyTasksScreenTablet() {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['common', 'home', 'lead']);

  const hasPermission = useHasPermission();
  const canViewLead = hasPermission('lead');
  const canViewPolicy = hasPermission('policy');

  const [reorderPanelVisible, setReorderPanelVisible] = useState(false);
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();

  // const sectionsOrder = useBoundStore(
  //   store => store.home.myTasks.sectionsOrder,
  // );

  //  Do not directly use the store value
  const NOT_PROCEEDED_sectionsOrder = useBoundStore(
    store => store.home.myTasks.sectionsOrder,
  );

  const setNewSectionsOrder = useBoundStore(
    store => store.homeActions.myTasks.setNewSectionsOrder,
  );

  const { updateAllScheduledNotifications } = useScheduledNotifications();

  useFocusEffect(() => {
    updateAllScheduledNotifications();
  });

  const emptyCaseComponentMap: Record<
    BuildCountry,
    Record<TodayTasksSectionsKeys, React.ReactNode | undefined> | undefined
  > = {
    id: undefined,
    ph: undefined,
    my: undefined,
    ib: {
      BirthdaySection: (
        <TaskEmptyRecordCard
          label={t('lead:myTask.emptyBirthdayTask')}
          containerStyle={{ marginVertical: space[5] }}
        />
      ),
      ContactLeadsSection: (
        <TaskEmptyRecordCard
          label={t('lead:myTask.emptyLeadsTasks')}
          containerStyle={{ marginVertical: space[5] }}
        />
      ),
      PaymentRemindersSection: (
        <TaskEmptyRecordCard
          label={t('home:task.payment.emptyCase')}
          containerStyle={{ marginVertical: space[5] }}
        />
      ),
      PolicyIssuesSection: (
        <TaskEmptyRecordCard
          label={t('home:task.policy.emptyCase')}
          containerStyle={{ marginVertical: space[5] }}
        />
      ),
      AgencyConfidentialReportSection: undefined,
    },
  };

  const sectionOrderForDisplay = useMemo(() => {
    return NOT_PROCEEDED_sectionsOrder?.filter(item => {
      const isTaskItemShown =
        (countryModuleSellerConfig?.tasks &&
          item?.key in countryModuleSellerConfig.tasks &&
          countryModuleSellerConfig?.tasks?.[item.key]) ??
        false;

      if (item.key === 'ContactLeadsSection') {
        return hasPermission('lead');
      }

      if (
        item.key === 'PolicyIssuesSection' ||
        item.key === 'PaymentRemindersSection'
      ) {
        return hasPermission('policy');
      }

      return isTaskItemShown;
    });
  }, [NOT_PROCEEDED_sectionsOrder, hasPermission]);

  return (
    <>
      <TitleContainer style={{ marginBottom: space[6] }}>
        <Title fontWeight="bold">{t('todayTasks.title')}</Title>
        <IconContainer
          onPress={() => {
            setReorderPanelVisible(true);
          }}>
          <Icon.Reorder width={24} height={24} fill={colors.secondary} />
        </IconContainer>
      </TitleContainer>
      <AllTasksContainer>
        {sectionOrderForDisplay.map(section => {
          switch (section.key) {
            // case 'AgencyConfidentialReportSection':
            //   return <AgencyConfidentialReportSection key={section.key} />;
            case 'BirthdaySection':
              return (
                <Animated.View key={section.key} layout={LinearTransition}>
                  <BirthdaySection
                    isSkeletonShown
                    EmptyRecordComponent={
                      emptyCaseComponentMap?.[country]?.BirthdaySection
                    }
                  />
                </Animated.View>
              );
            case 'ContactLeadsSection':
              return (
                <Animated.View key={section.key} layout={LinearTransition}>
                  <ContactLeadsSection
                    isSkeletonShown
                    EmptyRecordComponent={
                      emptyCaseComponentMap?.[country]?.ContactLeadsSection
                    }
                  />
                </Animated.View>
              );
            case 'PaymentRemindersSection':
              return (
                <Animated.View key={section.key} layout={LinearTransition}>
                  <PaymentRemindersSection
                    isSkeletonShown
                    EmptyRecordComponent={
                      emptyCaseComponentMap?.[country]?.PaymentRemindersSection
                    }
                    onViewAllPress={() =>
                      navigate('Main', {
                        screen: 'Policies',
                        params: {
                          screen: 'POS',
                        },
                      })
                    }
                  />
                </Animated.View>
              );
            case 'PolicyIssuesSection':
              return (
                <Animated.View key={section.key} layout={LinearTransition}>
                  <PolicyIssuesSection
                    isSkeletonShown
                    EmptyRecordComponent={
                      emptyCaseComponentMap?.[country]?.PolicyIssuesSection
                    }
                    onViewAllPress={() => {
                      if (country == 'ph') {
                        navigate('Main', {
                          screen: 'Policies',
                          params: {
                            screen: 'NewBusiness',
                            params: { screen: 'pending' },
                          },
                        });
                        return;
                      }
                      navigate('Main', {
                        screen: 'Policies',
                        params: {
                          screen: 'NewBusiness',
                        },
                      });
                    }}
                  />
                </Animated.View>
              );
            default:
              <Animated.View key={section.key} layout={LinearTransition}>
                <Fragment />
              </Animated.View>;
          }
        })}
      </AllTasksContainer>

      <ReorderPanel
        type="todayTasks"
        visible={reorderPanelVisible}
        onClose={() => setReorderPanelVisible(false)}
        itemsOrder={sectionOrderForDisplay}
        onSaveReorder={setNewSectionsOrder}
      />
    </>
  );
}

const TitleContainer = styled(XView)(() => ({
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const Title = styled(Typography.H6)(() => ({
  display: 'flex',
}));

const IconContainer = styled.TouchableOpacity(() => ({
  paddingTop: 2,
  paddingLeft: 2,
}));

const AllTasksContainer = styled.View(({ theme }) => ({
  gap: theme.space[4],
}));
