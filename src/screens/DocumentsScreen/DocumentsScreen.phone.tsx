import { TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import { Icon } from 'cube-ui-components';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import DocumentScreenContent from 'features/Document/phone/DocumentsScreenContent';
import { SearchDocumentsModal } from 'features/Document/phone/SearchDocumentsModal';

export default function DocumentsScreenPhone() {
  const { colors } = useTheme();
  const { t } = useTranslation('document');
  const [searchModalVisible, setSearchModalVisible] = useState(false);

  return (
    <View
      style={{
        backgroundColor: colors.background,
        flex: 1,
      }}>
      <ScreenHeader
        route={'DocumentsScreen'}
        customTitle={t('documents')}
        rightChildren={
          <HeaderRightButton setSearchModalVisible={setSearchModalVisible} />
        }
      />
      <SearchDocumentsModal
        searchModalVisible={searchModalVisible}
        setSearchModalVisible={setSearchModalVisible}
      />
      <DocumentScreenContent />
    </View>
  );
}

interface Props {
  setSearchModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const HeaderRightButton: React.FC<Props> = ({ setSearchModalVisible }) => {
  const { colors, space } = useTheme();
  const HIT_SLOP_SPACE_ONE = HIT_SLOP_SPACE(1);

  return (
    <TouchableOpacity
      hitSlop={HIT_SLOP_SPACE_ONE}
      onPress={() => {
        // console.log('Search? ');
        setSearchModalVisible(true);
      }}>
      <Icon.Search size={space[6]} fill={colors.secondary} />
    </TouchableOpacity>
  );
};
