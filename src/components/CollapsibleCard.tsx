import { useTheme } from '@emotion/react';
import { SvgIconProps, Box, Row, Typography, Icon } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useState } from 'react';
import { TouchableOpacity } from 'react-native';

const CollapsibleCard = ({
  title,
  children,
  TitleIcon,
  titleIconSize,
  isDefaultCollapse = false,
  ...props
}: {
  title: string;
  children?: React.ReactNode;
  TitleIcon: (props: SvgIconProps) => JSX.Element;
  titleIconSize?: number;
  isDefaultCollapse?: boolean;
  [key: string]: any;
}) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <CollapsibleCardTablet
      title={title}
      TitleIcon={TitleIcon}
      titleIconSize={titleIconSize}
      isDefaultCollapse={isDefaultCollapse}
      {...props}>
      {children}
    </CollapsibleCardTablet>
  ) : (
    <CollapsibleCardMobile
      title={title}
      TitleIcon={TitleIcon}
      titleIconSize={titleIconSize}
      isDefaultCollapse={isDefaultCollapse}
      {...props}>
      {children}
    </CollapsibleCardMobile>
  );
};

export default CollapsibleCard;

const CollapsibleCardMobile = ({
  title,
  children,
  TitleIcon,
  titleIconSize,
  isDefaultCollapse,
  ...props
}: {
  title: string;
  children?: React.ReactNode;
  TitleIcon: (props: SvgIconProps) => JSX.Element;
  titleIconSize?: number;
  isDefaultCollapse: boolean;
  [key: string]: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const [isCollapse, setIsCollapse] = useState(isDefaultCollapse);

  return (
    <Box
      padding={space[4]}
      borderRadius={borderRadius.large}
      backgroundColor={colors.background}
      {...props}>
      <Row justifyContent="space-between" alignItems="center">
        <Row justifyContent="center" alignItems="center" gap={8}>
          <TitleIcon size={titleIconSize && titleIconSize} />
          <Typography.H7 fontWeight="bold">{title}</Typography.H7>
        </Row>
        <TouchableOpacity
          onPress={() => {
            setIsCollapse(!isCollapse);
          }}>
          {isCollapse ? (
            <Icon.ChevronDown width={24} height={24} />
          ) : (
            <Icon.ChevronUp width={24} height={24} />
          )}
        </TouchableOpacity>
      </Row>
      {!isCollapse && (
        <>
          <Box boxSize={16} />
          {children}
        </>
      )}
    </Box>
  );
};

const CollapsibleCardTablet = ({
  title,
  children,
  TitleIcon,
  titleIconSize,
  isDefaultCollapse,
  ...props
}: {
  title: string;
  children?: React.ReactNode;
  TitleIcon: (props: SvgIconProps) => JSX.Element;
  titleIconSize?: number;
  isDefaultCollapse: boolean;
  [key: string]: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const [isCollapse, setIsCollapse] = useState(isDefaultCollapse);

  return (
    <Box
      padding={space[6]}
      borderRadius={borderRadius.large}
      backgroundColor={colors.background}
      {...props}>
      <Row justifyContent="space-between" alignItems="center">
        <Row justifyContent="center" alignItems="center" gap={8}>
          <TitleIcon size={titleIconSize && titleIconSize} />
          <Typography.H6 fontWeight="bold">{title}</Typography.H6>
        </Row>
        <TouchableOpacity
          onPress={() => {
            setIsCollapse(!isCollapse);
          }}>
          {isCollapse ? (
            <Icon.ChevronDown width={24} height={24} />
          ) : (
            <Icon.ChevronUp width={24} height={24} />
          )}
        </TouchableOpacity>
      </Row>
      {!isCollapse && (
        <>
          <Box boxSize={16} />
          {children}
        </>
      )}
    </Box>
  );
};
