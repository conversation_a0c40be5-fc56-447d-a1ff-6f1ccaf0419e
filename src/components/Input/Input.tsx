import { useBottomSheetInternal } from '@gorhom/bottom-sheet';
import useLatest from 'hooks/useLatest';
import { TFunc<PERSON>ey } from 'i18next';
import React, { useMemo, useState } from 'react';
import {
  Control,
  FieldValues,
  Path,
  get,
  useController,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useInputEventListenerContext } from './InputEventListener';
import { Platform } from 'react-native';

interface FormInput<V> {
  value?: V;
  onChange?: (
    value: any, // eslint-disable-line  @typescript-eslint/no-explicit-any
    secondArg?: any, // eslint-disable-line  @typescript-eslint/no-explicit-any
    preventDefaultAction?: () => void,
  ) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export type InputProps<P, V, FV extends FieldValues> = P & {
  as: React.ComponentType<P>;
  control: Control<FV>;
  name: Path<FV>;
  shouldUnregister?: boolean;
  // either boolean or function with form value as param that returns boolean
  // function type will help determine whether field should be highlighted when having default value
  shouldHighlightOnUntouched?: boolean | ((value: V) => boolean);
  // whether to highlight on first render, support blank form UX
  initialHighlight?: boolean;
  shouldIgnoreEventListener?: boolean; // avoid triggering onFocus/onBlur if Input is nested inside InputEventListener
  preventFocus?: boolean; // prevent focus on input to prevent crashing on some cases, eg: input is Picker
};

function Input<V, FV extends FieldValues, P extends FormInput<V>>({
  as: Component,
  control,
  shouldUnregister = false,
  shouldHighlightOnUntouched,
  shouldIgnoreEventListener = false,
  initialHighlight = true,
  onChange: onChangeProp,
  onFocus: onFocusExternal,
  onBlur: onBlurExternal,
  ...props
}: P extends FormInput<V> ? InputProps<P, V, FV> : InputProps<P, V, FV>) {
  const listener = useInputEventListenerContext();

  const {
    field: { value, onBlur, onChange },
    fieldState: { error, isTouched },
    formState: { disabled: disabledForm },
  } = useController({
    name: props.name,
    control,
    shouldUnregister,
  });
  const bottomSheetContext = useBottomSheetInternal(true);
  const { t } = useTranslation();
  const [isFocusing, setIsFocusing] = useState(false);

  // handle highlight flag
  const [blockingHighlight, setBlockingHighlight] = useState(!initialHighlight);
  const [internalHighlight, setInternalHighlight] = useState(false);
  const disabled =
    ('disabled' in props && Boolean(props?.disabled)) || disabledForm;

  const highlight = useMemo<boolean>(() => {
    if (disabled) {
      return false;
    }
    if (blockingHighlight) {
      return false;
    }
    if (internalHighlight) return true;
    if (shouldHighlightOnUntouched === undefined) return false;
    return typeof shouldHighlightOnUntouched === 'function'
      ? shouldHighlightOnUntouched(value)
      : shouldHighlightOnUntouched;
  }, [
    disabled,
    internalHighlight,
    blockingHighlight,
    shouldHighlightOnUntouched,
    value,
  ]);

  const onRef = <T,>(e: T | null) => {
    const field = get(control._fields, props.name);

    if (field && e) {
      const elm = e as unknown as HTMLFormElement;
      field._f.ref = {
        focus: () => {
          if (props.preventFocus) {
            return;
          }
          elm?.focus?.();
        },
        select: () => elm.select?.(),
        setCustomValidity: (message: string) =>
          elm.setCustomValidity?.(message),
        reportValidity: () => elm.reportValidity?.(),
        elm,
        toggleHighlight: () => setInternalHighlight(true),
        unblockHighlight: () => setBlockingHighlight(false),
      };
    }
  };

  return (
    <Component
      ref={onRef}
      value={value}
      onFocus={() => {
        if (bottomSheetContext?.shouldHandleKeyboardEvents) {
          bottomSheetContext.shouldHandleKeyboardEvents.value = true;
        }
        if (listener?.onFocus && !shouldIgnoreEventListener) {
          listener.onFocus(props.name);
        }
        onFocusExternal?.();
        setIsFocusing(true);
      }}
      onBlur={() => {
        if (bottomSheetContext?.shouldHandleKeyboardEvents) {
          bottomSheetContext.shouldHandleKeyboardEvents.value = false;
        }
        if (listener?.onBlur && !shouldIgnoreEventListener) {
          listener.onBlur(props.name);
        }
        onBlurExternal?.();
        setIsFocusing(false);
        onBlur();
      }}
      onChange={(e, secondArg) => {
        let preventDefault = false;
        onChangeProp?.(e, secondArg, () => (preventDefault = true));
        if (preventDefault) {
          return;
        }

        onChange(e);
        setInternalHighlight(false);
      }}
      error={!isFocusing && t(error?.message as TFuncKey, { ...error?.params })}
      highlight={!isTouched && highlight}
      {...props}
      disabled={disabled}
    />
  );
}

Input.defaultHighlightCheck = <T,>(value: T) => !value;
Input.defaultHighlightCheckEApp = <T,>(value: T) =>
  !value && Platform.OS === 'ios'; // TODO: remove later
Input.defaultHighlightCheckForBoolean = <T,>(value: T) =>
  typeof value !== 'boolean';

export default Input;
