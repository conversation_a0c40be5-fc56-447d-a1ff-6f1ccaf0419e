import React from 'react';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ViewStyle } from 'react-native';

export default function ListItemDate({
  date,
  width,
  iconSize,
  showIcon = true,
  containerStyle,
  isShorterLineHeight,
}: {
  date: string;
  width?: string;
  iconSize?: number;
  showIcon?: boolean;
  containerStyle?: ViewStyle;
  isShorterLineHeight?: boolean;
}) {
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <Row
      width={width}
      alignItems="center"
      justifyContent="space-between"
      px={space[4]}
      style={{ ...containerStyle }}>
      {isShorterLineHeight ? (
        <Typography.Label>{dateFormatUtil(date)}</Typography.Label>
      ) : (
        <Typography.Body>{dateFormatUtil(date)}</Typography.Body>
      )}
      {showIcon && (
        <Icon.ChevronRight fill={colors.primary} size={iconSize ?? sizes[4]} />
      )}
    </Row>
  );
}
