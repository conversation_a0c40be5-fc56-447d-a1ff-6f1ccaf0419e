export type Birthday = {
  todayList: BirthdayListItem[];
  monthList: BirthdayListItem[];
  nextMonthList: BirthdayListItem[];
  next7daysList: BirthdayListItem[];
  otherList: BirthdayListItem[];
  lastList?: BirthdayListItem[];
};

export type BirthdayListItem = {
  customerId: string | null;
  dateOfBirth: Date;
  phoneNumber: string;
  emailAddr: string | null;
  recType: 'C' | 'I' | 'A';
  agentId: string | null;
  namePrefix?: string;
  firstName?: string;
  nameSuffix?: string;
  chiFstNm?: string;
  chiLstNm?: string;
  DateOfBirthStr?: string;
  agentDateLeft?: null | undefined | number | string;
  displayName: DisplayName;
};
export type BirthdayTableItemProps = {
  customerId: string | null;
  dateOfBirth: Date;
  phoneNumber: string;
  emailAddr: string | null;
  recType: 'C' | 'I' | 'A';
  agentId: string | null;
  namePrefix?: string;
  firstName?: string;
  nameSuffix?: string;
  chiFstNm?: string;
  chiLstNm?: string;
  DateOfBirthStr?: string;
  agentDateLeft?: null | undefined | number | string;
  displayName: DisplayName;
  index: number | null;
  lastIndex: number | null;
};

export type DisplayName = {
  ph?: string | null;
  en: string;
};

export type BirthdayListTabsParamList = {
  Today: undefined;
  SevenDays: undefined;
};
