import { Summary } from './quotation';
export type LeadTrackingDetails = {
  agentId: string;
  year: string;
  month: string;
  updatedAt: string;
  conversions: ConversionDetails[];
  targets?: TargetDetails[];
  summary: SummaryTargetDetails;
};

export type SummaryTargetDetails = {
  target: {
    contact: number;
    appointment: number;
    illustrate: number;
    submit: number;
  };
};

export type RequestDataType = {
  year: string | undefined;
  month: string | undefined;
  targets: RequestTarget[] | undefined;
};

export type RequestTarget = {
  week: number;
  contact: number;
  appointment: number;
  illustrate: number;
  submit: number;
  startDate?: string;
  endDate?: string;
};

export type LeadTrackingRequestDetails = Omit<
  LeadTrackingDetails,
  'agentId' | 'updatedAt' | 'conversions' | 'summary'
>;

export type ConversionDetails = {
  leads: {
    value: number;
    ratio: number;
  };
  contact: {
    value: number;
    ratio: number;
  };
  appointment: {
    value: number;
    ratio: number;
  };
  illustrate: {
    value: number;
    ratio: number;
  };
  submit: {
    value: number;
    ratio: number;
  };
};

export type TargetDetails = {
  week: number;
  contact: number;
  appointment: number;
  illustrate: number;
  submit: number;
  startDate: string;
  endDate: string;
};
