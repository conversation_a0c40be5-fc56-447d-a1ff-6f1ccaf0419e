import { cubeClient } from './cubeClient';
import { AgentProfile } from 'types';
import * as FileSystem from 'expo-file-system';
import { baseUrl, country } from 'utils/context';
import { getBoundStoreState } from 'hooks/useBoundStore';

const AGENT_PROFILE_API = '/proc/agent';

export async function getAgentProfileById(agentId: string) {
  return await cubeClient.get<AgentProfile>(
    `${AGENT_PROFILE_API}/${agentId}?photo=1`,
    {
      headers:
        country === 'my' || country === 'ib'
          ? {
              'x-agent-id': agentId,
            }
          : {},
    },
  );
}

export async function uploadAgentPhoto({
  imgPath,
  accessToken,
  agentId,
}: {
  imgPath: string;
  agentId: string | null;
  accessToken: string | null;
}) {
  return await FileSystem.uploadAsync(
    `${baseUrl}/api-gateway${AGENT_PROFILE_API}/photo/${agentId}`,
    imgPath,
    {
      fieldName: 'photo',
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: 'Bearer ' + accessToken,
        'Content-Type': 'multipart/form-data',
      },
      sessionType: FileSystem.FileSystemSessionType.FOREGROUND,
    },
  );
}

export async function deleteAgentProfile(agentId: string) {
  return await cubeClient.delete<AgentProfile>(
    `${AGENT_PROFILE_API}/photo/${agentId}`,
  );
}
