import useBoundStore from 'hooks/useBoundStore';
import { LeadApiFilter } from './leadApi';
import { cubeClient } from './cubeClient';
import { GetCustomersResponse, LACustomer } from 'types';
import { country } from 'utils/context';
import { NewCustomerProfile } from 'types/customerProfile';
import { customerProfileEndpoints } from './customerProfileApi';

const ENDPOINT = '/exp/api/customer';
const LOOK_UP_ENDPOINT = '/exp/api/customer/lookup-exact-match';

export const ITEMS_PER_PAGE = 500;

export async function getCustomers(
  filters?: LeadApiFilter,
  options?: {
    page?: number;
    headers?: Record<string, string>;
    signal?: any;
  },
) {
  const agentId = useBoundStore.getState().auth.agentCode;
  const curPage = options?.page ?? 1;

  const pageParams = options?.page
    ? {
        offset: (curPage - 1) * ITEMS_PER_PAGE,
        limit: ITEMS_PER_PAGE,
      }
    : {};

  const headers = options?.headers || {};

  return await cubeClient.get<GetCustomersResponse>(ENDPOINT, {
    params: {
      ...pageParams,
      ...filters,
    },
    signal: options?.signal,
    headers: {
      ...headers,
      tenant: country,
      'x-agent-id': agentId,
    },
  });
}

export type LACustomerLookupParams = {
  firstName: string;
  middleInitial: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
};
export const getLACustomerLookup = async (
  requestData: LACustomerLookupParams,
) => {
  const agentId = useBoundStore.getState().auth.agentCode;
  return await cubeClient.post<LACustomerLookupParams, LACustomer>(
    LOOK_UP_ENDPOINT,
    requestData,
    {
      headers: {
        'Content-Type': 'application/json',
        'x-agent-id': agentId,
        'is-restful': true,
      },
    },
  );
};

// a new api is applicable to FIB project because of extra policy info are added in customer profile
export async function getNewCustomers() {
  return await cubeClient.get<NewCustomerProfile[]>(
    customerProfileEndpoints.getCustomerProfile,
  );
}
