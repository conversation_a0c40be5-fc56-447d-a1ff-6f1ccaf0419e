import apiClient from './apiClient';
import { cubeClient } from './cubeClient';
import {
  ContentStackAllNewsResponse,
  ContentStackNewsDetailResponse,
  NewsItem,
} from 'types/news';
import { CONTENT_TYPES } from 'hooks/useContentStack';
import { contentStackEnvironment as currentEnv } from 'utils/context';
import {
  contentStackDeliveryToken,
  contentStackKey,
  country,
} from 'utils/context';
import { CHANNELS_OUTCOMES } from 'types/channel';

export const TOP_FIVE_NEWS_ENDPOINT = '/proc/news/top5';
export const ALL_NEWS_ENDPOINT = '/proc/news';

const CHANNELS_BY_NEWS: { [key: string]: string } = {
  TA: 'agency',
  BCA: 'banca',
  // channel from IB is GBSN_TA or GBSN_BCA, which differ from the channel in Takaful (TA, or BCA)
  // ContentStack uses the channel from Takaful to filter the news
  // FIB FE for now is mapping FIB channels to Takaful channels
  GBSN_TA: 'agency',
  GBSN_BCA: 'banca',
} satisfies Partial<Record<CHANNELS_OUTCOMES, string>>;

const NEWS_TYPE_MY = `\"regulation\",\"campaign\",\"contest\",\"notice\",\"news\",\"info\"`;

export async function getTopFiveNews() {
  return await cubeClient.get<Array<NewsItem>>(TOP_FIVE_NEWS_ENDPOINT);
}

export async function getAllNews() {
  return await cubeClient.get<Array<NewsItem>>(ALL_NEWS_ENDPOINT);
}

export async function getContentStackAllNews({ channel }: { channel: string }) {
  const query = constructQuery({ channel });
  return await fetchNewsFromContentStack({ query });
}

export async function getContentStackTopFiveNews({
  channel,
}: {
  channel: string;
}) {
  const query = constructQuery({ channel });
  const limit = 5;
  return await fetchNewsFromContentStack({ query, limit });
}

export async function getAlwaysDisplayedNews({
  channel,
  isAllNews,
}: {
  channel: string;
  isAllNews: boolean;
}) {
  const NUMBER_OF_NEWS = 5;
  const additionalQuery = ',"show_news_on_top": true';
  const query = constructQuery({ channel, additionalQuery });
  return await fetchNewsFromContentStack({
    query,
    limit: isAllNews ? undefined : NUMBER_OF_NEWS,
  });
}

export async function getContentStackNewsDetail({ uid }: { uid: string }) {
  const CONTENT_STACK_NEWS_DETAILS_ENDPOINT = `https://cdn.contentstack.io/v3/content_types/${CONTENT_TYPES.NEWS}/entries/${uid}?environment=${currentEnv}&include_fallback=true`;

  const res = await apiClient.get<ContentStackNewsDetailResponse>(
    CONTENT_STACK_NEWS_DETAILS_ENDPOINT,
    {
      headers: getHeaders(),
    },
  );

  if (res?.data?.entry) return res?.data?.entry;

  return null;
}

// --------------- helper functions ---------------

async function fetchNewsFromContentStack({
  query,
  limit,
  sort = country === 'my' || country === 'ib' ? 'publish_date' : 'created_at',
}: {
  query: string;
  limit?: number;
  sort?: 'created_at' | 'publish_date';
}) {
  //  setting env, sort, limit, and preconstructed query

  const endpoint = `https://cdn.contentstack.io/v3/content_types/${
    CONTENT_TYPES.NEWS
  }/entries?environment=${currentEnv}${query}&desc=${sort}&include_fallback=true${
    limit ? `&limit=${limit}` : ''
  }`;

  const res = await apiClient.get<ContentStackAllNewsResponse>(endpoint, {
    headers: getHeaders(),
  });

  return res?.data?.entries || [];
}

function constructQuery({
  channel,
  additionalQuery = '',
}: {
  channel: string;
  additionalQuery?: string;
}): string {
  const now = new Date().toISOString();
  let lowerCaseChannel = channel?.toLocaleLowerCase();

  // ! similar to MongoDB's query language
  let query = `&query={"channels":{"$in":["${lowerCaseChannel}"]},"expiry_date":{"$gte":"${now}"} ${additionalQuery}}`;

  if (country == 'my') {
    lowerCaseChannel = CHANNELS_BY_NEWS[channel] || CHANNELS_BY_NEWS.TA;
    query = `&query={"tags":{"$eq":[]},"channels":{"$in":["${lowerCaseChannel}"]},"type_of_news_post":{"$in":[${NEWS_TYPE_MY}]},"expiry_date":{"$gte":"${now}"},"publish_date":{"$lte":"${now}"}${additionalQuery}}`;
  }

  if (country == 'ib') {
    lowerCaseChannel = CHANNELS_BY_NEWS[channel] || CHANNELS_BY_NEWS.TA;
    query = `&query={"tags":{"$in":["fib"]},"channels":{"$in":["${lowerCaseChannel}"]},"type_of_news_post":{"$in":[${NEWS_TYPE_MY}]},"expiry_date":{"$gte":"${now}"},"publish_date":{"$lte":"${now}"}${additionalQuery}}`;
  }

  return query;
}

function getHeaders() {
  const headers: {
    api_key: string;
    access_token: string;
    branch?: string;
  } = {
    api_key: contentStackKey,
    access_token: contentStackDeliveryToken,
  };

  if (country === 'my' || country === 'ib') {
    headers.branch = 'capricorn';
  }

  return headers;
}
