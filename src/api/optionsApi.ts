export type OptionKey =
  | 'clientType'
  | 'title'
  | 'extensionName'
  | 'gender'
  | 'proposalRelationship'
  | 'industry'
  | 'paymentType'
  | 'planOptions'
  | 'premiumPaymentMode'
  | 'premiumPaymentTerm'
  | 'multiplier'
  | 'riderType'
  | 'contributionFund';

type Options = {
  [key in OptionKey]?: string[] | number[] | { [key: string]: string }[];
};

const options: Options = {
  clientType: ['Individual', 'Entity'],
  title: [
    'Atty',
    'Dr',
    'Engr',
    'Fr',
    'Gen',
    'Hon',
    'Mr',
    'Mrs',
    'Ms',
    'Msgr',
    'Others',
    'Rev',
    'Sr',
  ],
  extensionName: ['Jr', 'Sr', 'I', 'II', 'III', 'IV', 'V', 'VI', 'IX', 'X'],
  gender: ['Male', 'Female'],
  proposalRelationship: [
    'Self',
    'Father',
    'Mother',
    'Spouse',
    'Sister',
    'Brother',
    'Son',
    'Daughter',
    'Grandchild',
    'Grandparent',
    'Uncle',
    'Aunt',
    'Cousin',
    'Niece',
    'N<PERSON>hew',
    'Fiancee',
    'Fiance',
    'Guardian',
    'Godparent',
    'Mother in law',
    'Father in law',
    'Other',
    'Employee',
    'Business partner	Business partner',
    'Employee - Keyman	Employee - Keyman',
    'Creditor	Creditor',
  ],
  industry: [
    'Nature of business/Industry description',
    'Agriculture, hunting, and industry',
    'Construction',
    'Education',
    'Electricity, Gas, and Water Supply',
    'Extra-Territorial Organizations and Bodies',
    'Financial Intermediation',
    'Fishing',
    'Health and Social Work',
    'Hotels and Restaurants',
    'Manufacturing',
    'Mining and Quarrying',
    'Other Community, Social and Personal Services and Activities',
    'Others',
    'Private Households with employed Persons',
    'Public Administration and Defense; Compulsory Social Security',
    'Real Estate, Renting and Business Activities',
    'Transport, Storage and Communications',
    'Wholesale and Retail Trade; Repair of Motor Vehicles, Motorcycles & Personal & Household Goods',
    'Electronic Money Issuer',
    'Cash Intensive businessess',
    'Foreign Exchange Dealers',
    'Money Changers',
    'Remittance Agents',
    'Casinos',
    'Arms Dealer',
    'Jewelry Dealers/precious Stones',
    'Non-Profit Organization',
    'Offshore Entities',
  ],
  paymentType: ['Single', 'Regular'],
  planOptions: [
    'Short Term Cover - 5 Years',
    'Short Term Cover -  10 years',
    'Income Protector - up to age 55',
    'Income Protector - up to age 60',
    'Income Protector - up to age 65',
    'Estate Protector',
  ],
  premiumPaymentMode: [
    'Annual',
    'Semi-annual',
    'Quarterly',
    'Monthly',
    'Single',
  ],
  premiumPaymentTerm: [1, 5, 6, 10, 20],
  multiplier: [],
  riderType: [],
  contributionFund: [
    { 'FWD Peso Fixed Income Fund': 'Conservative' },
    { 'FWD Peso Bond Fund': 'Conservative' },
    { 'FWD Peso Balanced Fund': 'Balanced' },
    { 'FWD Peso Stable Fund': 'Balanced' },
    { 'FWD Peso Equity Fund': 'Aggressive' },
    { 'FWD Peso Growth Fund': 'Aggressive' },
    { 'FWD Peso High Divd. Eq. Fund': 'Aggressive' },
    { 'FWD Peso Equity Index Fund': 'Aggressive' },
    { 'FWD Diversity Fund': 'Aggressive' },
    { 'FWD Global Good Peso ESG Fund': 'Aggressive' },
    { 'FWD Peso Nitro Global Payout Fund': 'Aggressive' },
    { 'FWD Peso Velocity Global Payout Fund': 'Balanced' },
  ],
};

export async function getOptions(key: OptionKey) {
  return options[key];
}
