import { CONTENT_TYPES } from 'hooks/useContentStack';
import { contentStackEnvironment as currentEnv } from 'utils/context';
import { AgentRewardResponse } from 'types/agentRewards';
import {
  contentStackDeliveryToken,
  contentStackKey,
  country,
} from 'utils/context';
import apiClient from './apiClient';

export async function getAgentReward() {
  const AGENT_REWARD_ENDPOINT = `https://cdn.contentstack.io/v3/content_types/${CONTENT_TYPES.AGENT_REWARDS}/entries?environment=${currentEnv}`;
  const res = await apiClient.get<AgentRewardResponse>(AGENT_REWARD_ENDPOINT, {
    headers: {
      api_key: contentStackKey,
      access_token: contentStackDeliveryToken,
      ...(['ib', 'my'].includes(country) ? { branch: 'capricorn' } : {}),
    },
  });

  if (Array.isArray(res?.data?.entries)) return res.data.entries;

  return [];
}
