import { build, country } from 'utils/context';
import { smartClient } from './smartClient';

export type SendSmsBody = {
  text: string;
  title: string;
  to: string;
};

export type SendSmsResponse = {
  message: string;
  status: string;
};

const endpoints = {
  sendSms:
    build === 'uat' && country === 'ib'
      ? '/capricorn-integration/bulksms/send'
      : '/smart-integration/bulksms/send',
};

export const sendSms = async (body: SendSmsBody) => {
  const data = await smartClient.post<SendSmsBody, SendSmsResponse>(
    endpoints.sendSms,
    body,
  );
  return data;
};
