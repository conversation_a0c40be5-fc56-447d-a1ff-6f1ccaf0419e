import { cubeClient } from './cubeClient';
import {
  RecruitmentHomeStat,
  ApplicationListDataResponds,
  CandidateProfileParams,
  CandidateProfileResponds,
  GetRemoteLinkResponse,
  NewCandidateRequestBody,
  NewCandidateResponseBody,
  RemoveCandidateParams,
  GetERecruitConfigResponse,
  DocumentByFileIdParams,
  DocumentList,
  UploadSignature,
  ApplicationSubmissionModel,
  ApplicationListQueryParams,
  AgentInfoResponse,
  ApplicationFormRequest,
  ApplicationFormResponds,
  SaveApplicationFormResponse,
  DocumentListPrarams,
  ApplicationStatusParams,
  ERecruitApplicationStatusQueryParams,
  ReviewAgentApplicationListResponds,
} from 'types/eRecruit';
import { documentDirectory, downloadAsync } from 'expo-file-system';
import useBoundStore from 'hooks/useBoundStore';
import {
  ApplicationCountResponse,
  ApplicationStatusResponse,
  MaintenanceTableResponse,
  ProgressMeterResponse,
  PurlResponse,
  RecruitApprovalRequestBody,
  RecruitsListRequestBody,
  RecruitsListResponse,
  TableType,
} from 'features/eRecruit/ph/types';

export type ApplicationStatusQueryParams = {
  statusType?: string;
};

export type ApplicationListResponds = {
  cubeStatus?: string;
  name: string;
  email: string;
  mobilePhone: string;
  registrationId?: number;
  candidatePosition?: string;
  status?: string;
  submissionDate?: string;
  approvedDate?: string;
  signatureDate?: string;
  registrationStagingId?: number;
  stage?: string;
  lstUpdDate?: string;
};

type ERecruitOTPRes = {
  status: 'PENDING' | 'VERIFIED';
  lastSentAt: string; // ISO date string
  expiredAt: string; // ISO date string
  tryCount: number;
  allowedResendAt: string; // ISO date string
  resendCount: number;
  maxTry: number;
  maxResend: number;
};

export type ConsentListParams = {
  registrationStagingId: string;
  // id: string;
  // type: string;
  // url: string;
};

export type DocumentResource = {
  registrationStagingId: string | null;
  id:
    | 'AgentAgreement'
    | 'LeaderAgreement'
    | 'AAJICodeOfEthics'
    | 'FWDCodeOfEthicsOfSales'
    | 'FWDCodeOfEthics'
    | 'PersonalDataProtection'
    | 'ConfirmationLetter';
  type: 'dynamic' | 'static';
  url: string;
};
export const SEND_OTP_ENDPOINT =
  '/proc/recruitment/application/send-verification';
export const VERIFY_OTP_ENDPOINT = '/proc/recruitment/application/verify';

export const APPLICATION_FORM__ENDPOINT = '/proc/recruitment/application';

export const RECRUIT_ENDPOINT = '/proc/recruitment';

export const RECRUIT_LEADER_APPROVAL_ENDPOINT =
  '/proc/recruitment/leader-approval/list';
export const RECRUIT_AGENT_APPLICATION_ENDPOINT =
  'proc/recruitment/leader-approval/detail';

export type SendOTPParam = { registrationStagingId: string | number };
export type VerifyOTPParam = {
  registrationStagingId: string | number;
  token: string | number;
};

export const sendOTP = async ({ registrationStagingId }: SendOTPParam) => {
  const response = await cubeClient.post<unknown, ERecruitOTPRes>(
    SEND_OTP_ENDPOINT,
    {
      registrationStagingId: registrationStagingId,
    },
  );
  return response;
};

export const verifyOTP = async ({
  registrationStagingId,
  token,
}: VerifyOTPParam) => {
  const response = await cubeClient.post<unknown, ERecruitOTPRes>(
    VERIFY_OTP_ENDPOINT,
    {
      registrationStagingId: registrationStagingId,
      token: token,
    },
  );
  return response;
};

export async function getReviewAgentsApplicationDetails(applicationId: number) {
  return await cubeClient.get<ApplicationFormResponds>(
    `${RECRUIT_AGENT_APPLICATION_ENDPOINT}/${applicationId}`,
    {},
  );
}

export async function getApplicationApprovedAndRejectedList(
  queryParams: ERecruitApplicationStatusQueryParams,
  status: 'REJECTED' | 'PENDING_LEADER_APPROVAL' | 'APPROVED',
) {
  return await cubeClient.get<ReviewAgentApplicationListResponds[]>(
    `${RECRUIT_LEADER_APPROVAL_ENDPOINT}`,
    {
      params: { ...queryParams, status },
    },
  );
}

export async function getSubmissionList(queryParams: ApplicationStatusParams) {
  return await cubeClient.get(`${RECRUIT_ENDPOINT}/registration/submission`, {
    params: { ...queryParams },
  });
}

export async function postApproveRejectComment(data: {
  applicationId: number;
  action: 'APPROVE' | 'REJECT';
  comment: string;
  interviewDate: string;
}) {
  return await cubeClient.post(
    `${RECRUIT_ENDPOINT}/leader-approval/approve`,
    data,
  );
}

export async function getRecruitmentStat() {
  return await cubeClient.get<RecruitmentHomeStat>(`${RECRUIT_ENDPOINT}/home`);
}

export async function getRecruitmentConfig() {
  return await cubeClient.get<GetERecruitConfigResponse>(
    `${RECRUIT_ENDPOINT}/config`,
  );
}

export async function updateRecruitmentTarget(data: {
  candidate: number;
  submitted: number;
  approve: number;
  typeCode: 'W' | 'M';
}) {
  return await cubeClient.post(`${RECRUIT_ENDPOINT}/target`, data);
}

export async function getApplicationList(
  queryParams: ApplicationListQueryParams,
) {
  const processedQueryParams = {
    ...queryParams,
    cubeStatusList: queryParams.cubeStatusList?.join(','),
  } satisfies Omit<ApplicationListQueryParams, 'cubeStatusList'> & {
    cubeStatusList?: string | undefined;
  };

  return await cubeClient.get<ApplicationListDataResponds>(
    `${RECRUIT_ENDPOINT}/application`,
    {
      params: { ...processedQueryParams },
    },
  );
}

export async function getCandidateProfile(queryParams: CandidateProfileParams) {
  return await cubeClient.get<CandidateProfileResponds>(
    `${RECRUIT_ENDPOINT}/application/candidate`,
    {
      params: { ...queryParams },
    },
  );
}

export async function removeCandidate(queryParams: RemoveCandidateParams) {
  return await cubeClient.post(
    `${RECRUIT_ENDPOINT}/application/candidate/remove`,
    {},
    {
      params: {
        ...queryParams,
      },
    },
  );
}

export type LangsForRemoteRecruitmentLink = 'en';

export async function getRemoteRecruitmentLink({
  lang = 'en',
}: {
  lang: LangsForRemoteRecruitmentLink;
}) {
  return cubeClient.get<GetRemoteLinkResponse>(
    `${RECRUIT_ENDPOINT}/agent-info/share-token`,
    {
      headers: {
        lang,
      },
    },
  );
}

export async function addNewCandidate(data: NewCandidateRequestBody) {
  return await cubeClient.post<
    NewCandidateRequestBody,
    NewCandidateResponseBody
  >(`${RECRUIT_ENDPOINT}/application`, data);
}

export async function getApplicationForm(applicationId: string) {
  console.log('get url:', `${APPLICATION_FORM__ENDPOINT}/${applicationId}`);
  return await cubeClient.get<ApplicationFormResponds>(
    `${APPLICATION_FORM__ENDPOINT}/${applicationId ?? ''}`,
  );
}

export async function saveApplicationForm(data: ApplicationFormRequest) {
  console.log('posting api /applications');
  // * when saving application, null id results in creating a new application,
  // * and existing id input results in updating the saved application data
  return await cubeClient.post<
    ApplicationFormRequest,
    SaveApplicationFormResponse
  >(`${APPLICATION_FORM__ENDPOINT}`, data);
}

export async function getAgentInfo(agentCode: string | null) {
  return await cubeClient.get<AgentInfoResponse>(
    `${RECRUIT_ENDPOINT}/agent-info/info?agentId=${agentCode}`,
  );
}

export async function getDocumentByFileId(queryParams: DocumentByFileIdParams) {
  return await cubeClient.get(`${RECRUIT_ENDPOINT}/document/file`, {
    params: { ...queryParams },
  });
}

export async function getConsentListById(queryParams: ConsentListParams) {
  return await cubeClient.get<DocumentResource[]>(
    `${RECRUIT_ENDPOINT}/document/form-list`,
    {
      params: { ...queryParams },
    },
  );
}

export async function postERDocument(formData: FormData) {
  console.log('posting api /document/upload', formData);
  return await cubeClient.postDirectPayLoad<
    unknown,
    {
      fileId: number;
      fileKey: string;
      fileSize: number;
      fileType: string;
    }
  >(`${RECRUIT_ENDPOINT}/document/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function getDocumentList(queryParams: DocumentListPrarams) {
  console.log('get api /document/list', queryParams);
  return await cubeClient.get<DocumentList>(
    `${RECRUIT_ENDPOINT}/document/list`,
    {
      params: { ...queryParams },
    },
  );
}

export async function getRecruitmentDocumentImage(uri: string) {
  const DOC_IMG_ENDPOINT = uri;

  const { registrationStagingId, fileId } = getParamsFromUrl(uri);

  const fileUri =
    documentDirectory + `e${registrationStagingId}-${fileId}` + '.jpg';

  console.log('uri', uri);
  const res = await downloadAsync(DOC_IMG_ENDPOINT, fileUri, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization:
        'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
    },
  });
  console.log(res);
  if (!res) return;

  return res.uri;
}

export async function getRecruitmentDocumentPdf(uri: string) {
  const PDF_ENDPOINT = uri;
  console.log('uri', uri);

  const res = await downloadAsync(
    PDF_ENDPOINT,
    documentDirectory + 'eRecruit_document' + '.pdf',
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization:
          'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
      },
    },
  );
  console.log('response from posting pdf file....>>>>', res);
  if (!res) {
    return console.log('failed to upload pdf file');
  }
  return res.uri;
}

export async function postApplicationSubmit(signatureData: UploadSignature) {
  console.log('posting api /application/submit');

  return await cubeClient.post(
    `${RECRUIT_ENDPOINT}/application/submit`,
    signatureData,
  );
}

export async function deleteDocument(
  fileId: string,
  registrationStagingId: string,
) {
  return await cubeClient.delete<{ result?: string }>(
    `${RECRUIT_ENDPOINT}/document/file?registrationStagingId=${registrationStagingId}&fileId=${fileId}`,
  );
}

export async function submitApplication(
  submissionData: ApplicationSubmissionModel,
) {
  return await cubeClient.post<
    ApplicationSubmissionModel,
    {
      registrationId: number;
    }
  >(`${APPLICATION_FORM__ENDPOINT}/submit`, submissionData);
}

/**
 * PH recruitment (SRT) APIs
 */
export const PURL_END_POINT = '/proc/recruitment/purl';
export const APPLICATION_STATUS_END_POINT =
  '/proc/recruitment/applicationStatus';
export const RECRUITS_LIST_END_POINT = '/proc/recruitment/recruitsList';
export const MAINTENANCE_TABLE_END_POINT =
  '/proc/recruitment/maintenance/:table';
export const RECRUIT_APPROVAL_END_POINT = '/proc/recruitment/approveRecruit';
export const PROGRESS_METER_END_POINT = '/proc/recruitment/progressMeter/:id';
export const APPLICATION_COUNT_END_POINT = '/proc/recruitment/applicationCount';

export async function getPurl() {
  return await cubeClient.get<PurlResponse>(PURL_END_POINT);
}

export async function getApplicationStatus() {
  return await cubeClient.get<ApplicationStatusResponse>(
    APPLICATION_STATUS_END_POINT,
  );
}

export async function postRecruitsList(data: RecruitsListRequestBody) {
  return await cubeClient.post<RecruitsListRequestBody, RecruitsListResponse>(
    RECRUITS_LIST_END_POINT,
    data,
  );
}

export async function getMaintenanceTableByTableType(tableType: TableType) {
  return await cubeClient.get<MaintenanceTableResponse>(
    MAINTENANCE_TABLE_END_POINT.replace(':table', tableType),
  );
}

export async function postRecruitApproval(data: RecruitApprovalRequestBody) {
  return await cubeClient.post(RECRUIT_APPROVAL_END_POINT, data);
}

export async function getProgressMeterByRecruitId(recruitId: string) {
  return await cubeClient.get<ProgressMeterResponse>(
    PROGRESS_METER_END_POINT.replace(':id', recruitId),
  );
}

export async function getApplicationCount() {
  return await cubeClient.get<ApplicationCountResponse>(
    APPLICATION_COUNT_END_POINT,
  );
}

const getParamsFromUrl = (url: string) => {
  // 'https://go420-dev.mys.apse1.git.np-api.fwd.com/api-gateway/proc/recruitment/document/file?registrationStagingId=2383&fileId=14470';
  const urlObj = new URL(url);
  const params = new URLSearchParams(urlObj.search);

  const registrationStagingId = params.get('registrationStagingId');
  const fileId = params.get('fileId');

  return { registrationStagingId, fileId };
};
