import { cubeClient } from './cubeClient';
import { NotificationMessage } from 'types/home';
import i18n from 'utils/translation';

export const NOTIFICATION_API = '/proc/notification';
export const GET_NOTIFICATION_API = NOTIFICATION_API + '/message';
export const DELETE_NOTIFICATION_API = NOTIFICATION_API + '/message';
export const READ_NOTIFICATION_API = NOTIFICATION_API + '/message';

export async function registerPushToken(agentId: string, token: string) {
  const language = i18n.language;

  return await cubeClient.post(
    `${NOTIFICATION_API}/token/${agentId}/tokenInfo`,
    {
      token,
      language,
    },
  );
}

export async function unregisterPushToken(agentId: string, token: string) {
  return await cubeClient.delete(
    `${NOTIFICATION_API}/token/${agentId}/tokenInfo`,
    {
      data: {
        requestData: {
          token,
        },
      },
    },
  );
}

export async function getListNotification() {
  return await cubeClient.get<NotificationMessage[]>(GET_NOTIFICATION_API);
}

export async function deleteNotificationMessage(id: string) {
  return await cubeClient.delete<string>(`${DELETE_NOTIFICATION_API}/${id}`);
}

export async function readNotificationMessage(id: string) {
  return await cubeClient.patch<{ isRead: boolean }, null>(
    `${READ_NOTIFICATION_API}/${id}`,
    {
      isRead: true,
    },
  );
}
