const siConfig = {
  coverageDetails: {
    nationality: {
      visible: true,
      optionListField: 'COUNTRY',
    },
    residencyType: {
      visible: false,
    },
    phoneCode: {
      disabled: true,
    },
    occupation: {
      disabled: false,
    },
    residence: {
      visible: true,
    },
  },
  rider: {
    shouldDisplayFilter: false,
  },
  email: {
    sendEmailToProceed: {
      quickQuote: false,
      fullQuote: false,
    },
    isMailToEnabled: {
      quickQuote: false,
      fullQuote: false,
    },
  },
  rpq: {
    entity: {
      enabled: false,
    },
    individual: {
      enabled: false,
    },
  },
  flow: {
    entity: {
      quickQuoteScreenName: null,
      quickQuoteLabel: null,
      quickQuoteLabelWithFNA: null,
      enableFullQuoteAgreement: true,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
    individual: {
      quickQuoteScreenName: 'Fna',
      quickQuoteLabel: 'proposal:startFNA',
      quickQuoteLabelWithFNA: 'proposal:reviewFNA',
      enableFullQuoteAgreement: true,
      fullQuoteScreenName: 'EApp',
      fullQuoteLabel: 'proposal:startApplication',
    },
  },
  simulation: {
    hasWithdrawalAndTopup: false,
  },
  fund: {
    filterType: 'fundType',
    hasTopup: true,
    fillUnselectedFund: false,
    fundAllocationMultipleOfNumber: 5,
    topUpAllocationMultipleOfNumber: 5,
    fundAllocationMin: 5,
    topUpAllocationMin: 5,
  },
  pdf: {
    isPasswordProtected: false,
    isPreviewable: true,
    isRequiredToReadAll: {
      quickQuote: true,
      fullQuote: true,
    },
    supportedLanguages: ['en'],
    isAttachmentChangeable: true,
  },
  maxFund: null,
  summaryModalsEnabled: true, // todo: remove this when all regions have summary modals
  isProductReselectionEnabled: false,
  siForm: {
    currency: {
      visible: true,
    },
    loadingSubtitle: {
      visible: true,
    },
  },
};

export default siConfig;
