// For mapping option list

export interface Option {
  label: string;
  value: string;
}

export default function getLabelFromValue(
  options?: Option[],
  value?: string | number,
) {
  if (!options || !Array.isArray(options) || !value) return;

  const option = options.find(
    option =>
      option.value == value ||
      (option.value && option.value !== value && option.label == value),
  );
  return option ? option.label : null;
}
