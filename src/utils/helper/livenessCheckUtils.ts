import { Sk<PERSON><PERSON>, <PERSON><PERSON> } from '@shopify/react-native-skia';
export const rotatePointAxis = (
  point: { x: number; y: number },
  size: { width: number; height: number },
) => {
  'worklet';
  const { height } = size;
  return { x: height - point.y, y: point.x };
};


export const getSkiaPathForContourShoulder = (
  start: { x: number; y: number },
  direction: 'left' | 'right',
  isPortrait: boolean,
  isFlipped: boolean,
): SkPath => {
  'worklet';
  const path = Skia.Path.Make();
  path.moveTo(start.x, start.y);

  const factor = direction === 'left' ? -1 : 1;

  if (isPortrait) {
    if (isFlipped) {
      path.cubicTo(
        start.x - 40,
        start.y + 80 * -1 * factor,
        start.x - 80,
        start.y + 160 * -1 * factor,
        start.x - 100,
        start.y + 230 * -1 * factor,
      );
      path.cubicTo(
        start.x - 130,
        start.y + 330 * -1 * factor,
        start.x - 330,
        start.y + 300 * -1 * factor,
        start.x - 390,
        start.y + 285 * -1 * factor,
      );
    } else {
      path.cubicTo(
        start.x + 40,
        start.y - 80 * factor,
        start.x + 80,
        start.y - 160 * factor,
        start.x + 100,
        start.y - 230 * factor,
      );
      path.cubicTo(
        start.x + 130,
        start.y - 330 * factor,
        start.x + 330,
        start.y - 300 * factor,
        start.x + 390,
        start.y - 285 * factor,
      );
    }
    return path;
  }

  path.cubicTo(
    start.x + 80 * factor,
    start.y + 40,
    start.x + 160 * factor,
    start.y + 80,
    start.x + 230 * factor,
    start.y + 100,
  );
  path.cubicTo(
    start.x + 330 * factor,
    start.y + 130,
    start.x + 300 * factor,
    start.y + 330,
    start.x + 285 * factor,
    start.y + 390,
  );

  return path;
};

export const getFileInfoFromUri = (
  uri: string,
): { uri: string; name: string; type: string } => {
  const fileUri = uri.startsWith('file://') ? uri : `file://${uri}`;
  const fileName = uri.split('/').pop() ?? '';
  const fileType = fileName.split('.').pop() ?? '';

  return {
    uri: fileUri,
    name: fileName,
    type: `image/${fileType}`,
  };
};
