import {
  INTL_PHONE_PREFIX,
  PHONE_CALLING_CODE,
  PHONE_COUNTRY_CODE,
  SHOULD_PREPEND_LEADING_ZERO_IN_FORMAT,
} from 'constants/phone';
import {
  formatIncompletePhoneNumber,
  isValidPhoneNumber,
} from 'libphonenumber-js';
import { prependZero } from './stringUtil';

export function formatPhoneNumberForEmail(phone: string) {
  // PH only
  return phone.replace(/(\d{3})(\d{3,4})(\d{4})/, '(+$1) $2-$3');
}

export function formatPhoneNumber(
  phone = '',
  { withIntPhoneCode }: { withIntPhoneCode?: boolean } = {
    withIntPhoneCode: true,
  },
) {
  const standardizedPhone = phone
    .trim()
    .replace(/^0/, '')
    .replace(`/^\\+${PHONE_CALLING_CODE}/`, '');

  if (isValidPhoneNumber(standardizedPhone, PHONE_COUNTRY_CODE)) {
    const formattedPhoneNumber = formatIncompletePhoneNumber(
      SHOULD_PREPEND_LEADING_ZERO_IN_FORMAT
        ? prependZero(standardizedPhone)
        : standardizedPhone,
      PHONE_COUNTRY_CODE,
    ).replace(/^0/, '');

    if (withIntPhoneCode) {
      return `${INTL_PHONE_PREFIX} ${formattedPhoneNumber.replaceAll(
        '-',
        ' ',
      )}`;
    }

    return formattedPhoneNumber;
  }
  return phone;
}
