import AsyncStorage from '@react-native-async-storage/async-storage';

const DEVICE_ID = 'device-id';
const APPLICATION_COUNT = 'application-count';

export const getDeviceId = () => {
  return AsyncStorage.getItem(DEVICE_ID);
};

export const saveDeviceId = (id: string) => {
  return AsyncStorage.setItem(DEVICE_ID, id);
};

export const increaseApplicationCount = async () => {
  let count = await AsyncStorage.getItem(APPLICATION_COUNT);
  if (!count) {
    count = '0';
  }
  const increasedValue = String(Number(count) + 1);
  await AsyncStorage.setItem(APPLICATION_COUNT, increasedValue);
  return increasedValue;
};
