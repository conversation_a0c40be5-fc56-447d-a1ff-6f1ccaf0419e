import { MainTabParamList } from 'types/navigation';
import React, { Fragment, useCallback, useState } from 'react';
import { View, useWindowDimensions } from 'react-native';
import { useTheme } from '@emotion/react';
import useBoundStore from 'hooks/useBoundStore';
import { useFocusEffect } from '@react-navigation/native';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { createDrawerNavigator } from '@react-navigation/drawer';
import RootDrawerTabBar, {
  drawerTabs,
} from 'navigation/components/RootDrawerTabBar';
import MYRootDrawerTabBar, {
  drawerTabsMY,
} from 'navigation/MainNavigator/my/MYRootDrawerTabBar';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Box, Column } from 'cube-ui-components';
import { Path, Svg } from 'react-native-svg';
import MaskedView from '@react-native-masked-view/masked-view';
import PHRootDrawerTabBar, { drawerTabsPH } from './ph/PHRootDrawerTabBar';

const RootDrawer = createDrawerNavigator<MainTabParamList>();

const CORNER_RADIUS = 32;

// * For the Bottom Tab
export default function TabletMainDrawerNavigator() {
  const { colors, space, sizes } = useTheme();

  const { top } = useSafeAreaInsets();
  const { height } = useWindowDimensions();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );
  const resetFnaStoreState = useFnaStore(state => state.resetFnaStoreState);
  useFocusEffect(
    useCallback(() => {
      resetFnaStoreState();
      clearActiveCase();
    }, []),
  );
  const dimensions = useWindowDimensions();

  // Force the drawer to re-render
  const [drawerHeight, setDrawerHeight] = useState(height - top);
  const drawerTabsData =
    country === 'my'
      ? drawerTabsMY
      : country === 'ph'
      ? drawerTabsPH
      : drawerTabs;

  return (
    <RootDrawer.Navigator
      initialRouteName="Home"
      drawerContent={props => (
        <Column
          flex={1}
          onLayout={e => {
            if (e.nativeEvent.layout.height - top != drawerHeight) {
              setDrawerHeight(e.nativeEvent.layout.height - top);
            }
          }}>
          {country === 'my' ? (
            <MYRootDrawerTabBar {...props} />
          ) : country === 'ph' ? (
            <PHRootDrawerTabBar {...props} />
          ) : (
            <RootDrawerTabBar {...props} />
          )}
          <Column
            // eslint-disable-next-line react-native/no-color-literals
            style={{
              backgroundColor: 'transparent',
              position: 'absolute',
              top: top,
              width: CORNER_RADIUS + sizes[2],
              right: -CORNER_RADIUS,
            }}>
            <DrawerCorner height={drawerHeight} cornerRadius={CORNER_RADIUS} />
          </Column>
        </Column>
      )}
      screenOptions={{
        headerShown: false,
        drawerType: dimensions.width >= 500 ? 'permanent' : 'front',
        drawerStyle: {
          backgroundColor: colors.primary,
          width: space[26],
          borderRightWidth: 0,
        },
      }}>
      {drawerTabsData.map(({ name, component }) => {
        if (!component) return null;
        if (!countryModuleSellerConfig[name]) {
          return <Fragment key={name} />;
        }
        return (
          <RootDrawer.Screen key={name} name={name} component={component} />
        );
      })}
    </RootDrawer.Navigator>
  );
}

const DrawerCorner = ({
  cornerRadius = CORNER_RADIUS,
  height = 0,
}: {
  cornerRadius?: number;
  height?: number;
}) => {
  const { colors, sizes } = useTheme();

  return (
    <MaskedView
      maskElement={
        <Svg>
          <Path
            d={`
        M 0 0 H ${cornerRadius + sizes[2]}
        A ${cornerRadius} ${cornerRadius} 0 0 0 ${sizes[2]} ${cornerRadius}
        V ${height - cornerRadius} A ${cornerRadius} ${cornerRadius} 0 0 0 ${
              cornerRadius + sizes[2]
            } ${height} H 0 
        `}
            fill={colors.primary}></Path>
        </Svg>
      }>
      <View
        style={{
          height: height,
          width: cornerRadius + sizes[2],
          backgroundColor: colors.primary,
        }}>
        <Box
          flex={1}
          style={{
            overflow: 'hidden',
          }}>
          <Box
            flex={1}
            backgroundColor={colors.primary}
            w={cornerRadius * 2}
            left={sizes[2]}
            style={{
              shadowColor: colors.palette.black,
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: 0.2,
              shadowRadius: 3.84,
              elevation: 2,
              borderRadius: cornerRadius,
            }}
          />
        </Box>
      </View>
    </MaskedView>
  );
};
