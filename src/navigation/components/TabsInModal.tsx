import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  CompositeNavigationProp,
  NavigationProp,
  useNavigation,
} from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack/lib/typescript/src/types';
import { Box, Row, Typography } from 'cube-ui-components';
import { useHasPermission } from 'hooks/useCheckClientScope';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import React, { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, PressableProps, View } from 'react-native';
import { MainTabParamList, RootStackParamList } from 'types';
import { CHANNELS } from 'types/channel';
import { countryModuleSellerConfig } from 'utils/config/module';
import { country } from 'utils/context';
import RootBottomTabBarContext from './RootBottomTabBarContext';

type TabsInModalProps = {
  closeModal: () => void;
};

type TagProps = {
  backgroundColor?: string;
};

export default function TabsInModal({ closeModal }: TabsInModalProps) {
  const navigation =
    useNavigation<
      CompositeNavigationProp<
        NavigationProp<MainTabParamList>,
        NativeStackNavigationProp<RootStackParamList>
      >
    >();
  const { t } = useTranslation('navigation');
  const { colors, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const cubeChannel = useGetCubeChannel();
  const hasPermission = useHasPermission();
  const { data: agentProfile } = useGetAgentProfile();

  const IS_LEADER = agentProfile?.designation?.toLowerCase() !== 'fwp';
  const IS_AGENCY_CHANNEL = cubeChannel === CHANNELS.AGENCY;

  const { toggleShouldHighlightOthers, shouldHighlightOthers } = useContext(
    RootBottomTabBarContext,
  );

  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  const filteredOtherMenuTabs =
    IS_LEADER && IS_AGENCY_CHANNEL
      ? otherMenuTabsMobile
      : otherMenuTabsMobile.filter(m => {
          return m.name !== 'TeamManagement' && m.name !== 'ERecruit';
        });

  return (
    <TabMenuContainer>
      {filteredOtherMenuTabs.map(({ name, icon, isNew }) => {
        // Hide TeamManagement tab for ib country temporarily because the screen is not ready
        if (name === 'TeamManagement' && !isTabletMode && country === 'ib') {
          return null;
        }
        if (!countryModuleSellerConfig[name]) {
          return null;
        }

        return (
          <PressableWithFeedback
            key={name}
            onPress={() => {
              closeModal();
              toggleShouldHighlightOthers();
              if (name === 'TrainerAiBot') {
                navigation.push('EcoachHome');
              } else {
                navigation.navigate(name);
              }
            }}>
            <Row style={{ gap: sizes[3] }}>
              {icon}
              <Typography.H6 color={colors.background} fontWeight={'bold'}>
                {t(`tabScreen.${name}`)}
              </Typography.H6>

              {isNew && (
                <Tag>
                  <Typography.H8
                    color={colors.palette.fwdDarkGreen[100]}
                    fontWeight="bold">
                    {t('tag.new')}
                  </Typography.H8>
                </Tag>
              )}
            </Row>
          </PressableWithFeedback>
        );
      })}
    </TabMenuContainer>
  );
}

function PressableWithFeedback(
  props: PressableProps & { children?: React.ReactNode },
) {
  const { colors, sizes } = useTheme();

  const [isPressed, setIsPressed] = useState(false);
  const children = props?.children;

  return (
    <Pressable
      {...props}
      style={{
        padding: sizes[5],
        paddingLeft: sizes[9],
        backgroundColor: isPressed
          ? colors.palette.fwdAlternativeOrange[100]
          : colors.palette.whiteTransparent,
      }}
      onPressIn={() => setIsPressed(true)}
      onPressOut={() => setIsPressed(false)}>
      {children}
    </Pressable>
  );
}

const TabMenuContainer = styled(View)(({ theme: { sizes } }) => ({
  marginTop: sizes[14],
}));

const Tag = styled(Box)<TagProps>(({ theme }) => ({
  marginLeft: theme.space[1],
  borderRadius: theme.borderRadius['x-small'],
  paddingHorizontal: theme.space[2],
  backgroundColor: theme.colors.palette.fwdYellow[100],
  justifyContent: 'center',
}));
