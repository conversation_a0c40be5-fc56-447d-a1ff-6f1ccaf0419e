import { useTheme } from '@emotion/react';
import { Box, Icon, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import DrawerTabStyled from '../MainNavigator/my/DrawerTabStyled';
import { View } from 'react-native';

export default function MoreMenuTabButton({onPress, focused, selectMenuName, isShowPop, onLayout}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('navigation');

  return (
    <View onLayout={onLayout}>
      <DrawerTabStyled.TouchableButton
        onPress={onPress}
        focused={focused}
        lineNumber={0}>
        <Box alignItems="center">
          {focused
            ?
            selectMenuName === ''
              ?
              <Icon.Close
                fill={colors.background}
                height={space[7]}
                width={space[7]}
              />
              :
              isShowPop
                ?
                <Icon.Close
                  fill={colors.background}
                  height={space[7]}
                  width={space[7]}
                />
                :
                <Icon.Menu
                  fill={colors.background}
                  height={space[7]}
                  width={space[7]}
                />
            : <Icon.Menu
              fill={colors.background}
              height={space[7]}
              width={space[7]}
            />}
          <Typography.SmallLabel
            color={colors.background}
            style={{
              alignSelf: 'center',
              textAlign: 'center',
              maxWidth: '94%',
            }}>
            {t('tabScreen.More')}
          </Typography.SmallLabel>
        </Box>
      </DrawerTabStyled.TouchableButton>
    </View>
  );
}

