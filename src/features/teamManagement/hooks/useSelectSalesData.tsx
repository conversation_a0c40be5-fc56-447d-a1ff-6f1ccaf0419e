import { useMemo } from 'react';
import { useGetTeamSalesBalancePerformance } from 'hooks/useGetTeam';

export default function useSelectSalesData(agentCode: string) {
  const { data: salesData, isLoading: isSalesLoading } =
    useGetTeamSalesBalancePerformance();
  const selectedAgentData = useMemo(() => {
    const leaderData = salesData?.members ?? [];
    const memberData = salesData?.members ?? [].map(({ members }) => members);
    const allAgentData = [...leaderData, ...memberData];
    const agentData =
      allAgentData.find(data => data?.agentCode === agentCode) ??
      allAgentData[0];
    return agentData;
  }, [JSON.stringify(salesData), agentCode]);

  return { data: selectedAgentData, isLoading: isSalesLoading };
}
