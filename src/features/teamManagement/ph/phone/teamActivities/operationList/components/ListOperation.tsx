import { memo } from 'react';
import OperationRow from './OperationRow';
import { OperationListItem } from '../OperationList';

export interface ListOperationsProps {
  data: OperationListItem;
  index: number;
  onPress?: (data: OperationListItem) => void;
}

export const ListOperations = memo(function ListOperations({
  data,
  index,
  onPress,
}: ListOperationsProps) {
  return <OperationRow data={data} index={index} onPress={onPress} />;
});
