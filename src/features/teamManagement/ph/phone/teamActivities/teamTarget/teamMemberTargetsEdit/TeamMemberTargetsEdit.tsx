import styled from '@emotion/native';
import React, { useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import HeaderCloseButton from 'navigation/components/HeaderCloseButton';
import { TeamPerformanceActivitiesParamList } from 'types';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import useBoundStore from 'hooks/useBoundStore';
import { useSaveTeamTarget } from 'hooks/useSaveTeam';
import { AgentTarget } from 'types/team';
import { Box, Button, Row } from 'cube-ui-components';
import TeamMemberTargetItem from './TeamMemberTargetItem';
import { FlashList } from '@shopify/flash-list';
import { useForm } from 'react-hook-form';
import { AgentTargetModal } from 'features/teamManagement/components/AgentTargetModal';
import { SaveChangesModal } from 'features/teamManagement/components/SaveChangesModal';
import { MemberTargetForm } from 'features/teamManagement/validation/teamTarget/memberTarget';

export default function TeamMemberTargetsEditScreen() {
  const { goBack } =
    useNavigation<NavigationProp<TeamPerformanceActivitiesParamList>>();
  const { params } =
    useRoute<
      RouteProp<TeamPerformanceActivitiesParamList, 'TeamMemberTargetsEdit'>
    >();
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const { mutateAsync: saveTeamTarget } = useSaveTeamTarget();
  const [isShowSaveConfirmPopup, setIsShowSaveConfirmPopup] =
    useState<boolean>(false);
  const [selectedAgentViewInfo, setSelectedAgentViewInfo] =
    useState<AgentTarget>();
  const [isShowDialogAgentLastYearInfo, setIsShowDialogAgentLastYearInfo] =
    useState<boolean>(false);
  const onShowAgentLastYearInfo = (agentTarget: AgentTarget) => {
    setSelectedAgentViewInfo(agentTarget);
    setIsShowDialogAgentLastYearInfo(true);
  };

  const {
    auth: { agentCode },
  } = useBoundStore();

  const {
    control,
    handleSubmit,
    reset: onResetForm,
    formState: { isValid, isDirty },
  } = useForm<MemberTargetForm>({
    mode: 'onChange',
    defaultValues: {
      data: params.data,
    },
  });

  const onSubmitEditTarget = async (data: AgentTarget[]) => {
    setIsShowSaveConfirmPopup(false);
    setAppLoading();

    try {
      if (agentCode) {
        await saveTeamTarget({
          id: agentCode,
          data: {
            agentCode: agentCode,
            yyyy: params.year,
            downlineAgents: data,
          },
        });
      }
    } catch (e) {
      //TODO: handle request fails
    } finally {
      setAppIdle();
    }

    goBack();
  };

  const onGoBack = () => {
    if (isDirty) {
      setIsShowSaveConfirmPopup(true);
    } else {
      goBack();
    }
  };

  const renderItem = ({
    item,
    index,
  }: {
    item: AgentTarget;
    index: number;
  }) => {
    return (
      <TeamMemberTargetItem
        index={index}
        control={control}
        onViewLastYearDetails={() => onShowAgentLastYearInfo(item)}
      />
    );
  };

  const onSelectSaveData = async () => {
    if (isValid) {
      await handleSubmit(
        submittedValues => onSubmitEditTarget(submittedValues.data),
        errors => {
          console.log('Insured form errors: ', errors);
        },
      )();
    }
  };

  const Footer = () => (
    <FooterContainer>
      <ButtonAction
        text="Reset"
        variant="secondary"
        onPress={() => onResetForm()}
        disabled={!isDirty}
      />
      <HorizontalSpacing />
      <ButtonAction
        text="Save"
        onPress={onSelectSaveData}
        disabled={!isDirty}
      />
    </FooterContainer>
  );

  return (
    <PageContainer>
      <ScreenHeader
        route={'TeamMemberTargetsEdit'}
        leftChildren={<HeaderCloseButton handlePress={onGoBack} />}
      />
      <FlashList
        data={params.data}
        renderItem={renderItem}
        ItemSeparatorComponent={() => <ItemSeparator />}
        estimatedItemSize={118}
      />
      {selectedAgentViewInfo && (
        <AgentTargetModal
          dialogVisible={isShowDialogAgentLastYearInfo}
          data={selectedAgentViewInfo}
          onClose={() => setIsShowDialogAgentLastYearInfo(false)}
        />
      )}
      <SaveChangesModal
        dialogVisible={isShowSaveConfirmPopup}
        onClose={goBack}
        onConfirm={onSelectSaveData}
      />

      <Footer />
    </PageContainer>
  );
}

const PageContainer = styled(SafeAreaView)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const ItemSeparator = styled(View)(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginHorizontal: theme.space[4],
}));

const ButtonAction = styled(Button)(() => ({
  flex: 1,
}));

const HorizontalSpacing = styled(Box)(({ theme }) => ({
  width: theme.space[4],
}));

const FooterContainer = styled(Row)(({ theme }) => ({
  padding: theme.space[4],
  borderTopWidth: 1,
  borderTopColor: theme.colors.palette.fwdGrey[50],
}));
