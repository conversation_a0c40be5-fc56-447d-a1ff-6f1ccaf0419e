import { TouchableOpacity, View, ViewProps } from 'react-native';
import React, { useContext, useMemo } from 'react';
import styled from '@emotion/native';
import { Box, Center, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ScrollView } from 'react-native';
import { formatCurrency } from 'utils';
import { useTranslation } from 'react-i18next';
import { SCREEN_WIDTH } from '@gorhom/bottom-sheet';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ViewShot from 'react-native-view-shot';
import { TeamPerformanceParamList } from 'types';
import { useGetTeamContributionByAgentId } from 'hooks/useGetTeam';
import { FlashList } from '@shopify/flash-list';
import TeamManagementContext from 'features/teamManagement/context/TeamManagementContext';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import DoughnutChart from 'components/Chart/DoughnutChart';

export default function ContributionTab() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('teamManagement');
  const navigation = useNavigation<NavigationProp<TeamPerformanceParamList>>();

  const {
    params: { viewShotRef, setTopTabIndex },
  } = useRoute();
  setTopTabIndex(navigation.getState().index);

  const { selectedAgentId } = useContext(TeamManagementContext);
  const { data: agentTeamBranch } = useGetTeamContributionByAgentId(
    selectedAgentId || '',
  );

  const onSelectAgent = (item: {
    agentId: string;
    title?: string;
    // color?: string;
    // percentage?: number;
    // opacity?: number;
    // value?: number;
  }) => {
    navigation.navigate('AgentPerformance', {
      data: {
        agentId: item.agentId,
        agentName: item.title,
      },
    });
  };

  const totalAPE = agentTeamBranch?.downlineAgents.reduce(
    (accumulator, object) => {
      return accumulator + object.ape;
    },
    0,
  );

  const chartData = useMemo(
    () =>
      agentTeamBranch?.downlineAgents.map((e, index) => ({
        agentId: e.agentId,
        color: colors.primary,
        percentage: totalAPE ? (e.ape / totalAPE) * 100 : 0,
        opacity: index < 4 ? 1 - 0.25 * index : 0.12,
        title: e.displayName?.en,
        value: e.ape,
      })) ?? [],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [agentTeamBranch?.downlineAgents],
  );

  return (
    <ScrollView style={{ padding: space[4], backgroundColor: colors.surface }}>
      <ViewShotContainer
        ref={viewShotRef}
        options={{
          fileName: 'team-performance',
          format: 'jpg',
          quality: 1.0,
        }}>
        <Container>
          <Title
            fontWeight="bold"
            children={t('teamView.teammateContributionToTotalAPE')}
          />
          <SubTitle
            children={`${t('teamView.manager')}: ${
              agentTeamBranch?.displayName?.en
            }`}
          />
          <SubTitle
            children={`${t('asOf')} ${
              agentTeamBranch?.asOfDate
                ? dateFormatUtil(agentTeamBranch?.asOfDate)
                : '--'
            }`}
          />

          <Center my={space[4]}>
            {chartData?.length > 0 && (
              <DoughnutChart
                data={chartData}
                title={formatCurrency(totalAPE ?? 0)}
                subtitle={t('teamView.teamTotalAPE')}
                radius={SCREEN_WIDTH / 3}
              />
            )}
          </Center>

          <Column mt={space[3]} gap={space[4]}>
            <Row justifyContent="space-between">
              <Typography.H7 fontWeight="bold" children={t('agentName')} />
              <Typography.H7
                fontWeight="bold"
                children={t('teamView.salesAPEPHP')}
                style={{ marginRight: space[6] }}
              />
            </Row>

            {/* <TableContent> */}
            <FlashList
              data={chartData}
              renderItem={({ item, index }) => (
                <RowInfo
                  onPress={() => onSelectAgent(item)}
                  isShowUnderLine={index < chartData?.length - 1}>
                  <Row alignItems="center">
                    <BoxColor color={item.color} opacity={item.opacity} />
                    <InfoTitle>{item.title}</InfoTitle>
                  </Row>
                  <Row alignItems="center">
                    <InfoTitle children={formatCurrency(item.value)} />
                    <Icon.ChevronRight
                      size={space[5]}
                      fill={colors.palette.fwdDarkGreen[100]}
                    />
                  </Row>
                </RowInfo>
              )}
            />
          </Column>
        </Container>
      </ViewShotContainer>
    </ScrollView>
  );
}

const Container = styled(View)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
  padding: theme.space[4],
  borderRadius: theme.space[4],
}));

const RowInfo = styled(TouchableOpacity)<
  ViewProps & { isShowUnderLine: boolean }
>(({ theme, isShowUnderLine }) => ({
  flexDirection: 'row',
  paddingVertical: theme.space[4],
  justifyContent: 'space-between',
  alignItems: 'center',
  borderBottomWidth: isShowUnderLine ? 1 : 0,
  borderBottomColor: theme.colors.palette.fwdGrey[100],
}));

const BoxColor = styled(Box)<ViewProps & { color: string; opacity: number }>(
  ({ theme, color, opacity }) => ({
    width: theme.space[5],
    height: theme.space[5],
    borderRadius: 2,
    backgroundColor: color,
    opacity: opacity,
  }),
);

const ViewShotContainer = styled(ViewShot)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.surface,
}));

const InfoTitle = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  marginHorizontal: theme.space[2],
}));

const Title = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  maxWidth: '90%',
}));

const SubTitle = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
  marginTop: theme.space[1],
}));
