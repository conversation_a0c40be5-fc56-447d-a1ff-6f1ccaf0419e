import { FlatList, Text, TouchableOpacity, View, Image } from 'react-native';
import React from 'react';
import { Modal } from 'react-native';
import {
  Column,
  H6,
  Icon,
  LargeLabel,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { TeamBranch, TeamDownlineAgent } from 'types/team';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import AvatarPlaceholderSVG from 'features/teamManagement/assets/AvatarPlaceholderSVG';
import LevelBadgeSVG from 'features/teamManagement/assets/LevelBadgeSVG';

const RenderItem = ({ data }: { data: TeamDownlineAgent }) => {
  const { colors, space, sizes } = useTheme();
  return (
    <Row paddingY={space[3]} alignItems="center">
      <Column marginRight={space[3]}>
        {data?.imageUrl ? (
          <ImageContainer>
            <AgentImage source={{ uri: data.imageUrl }} />
          </ImageContainer>
        ) : (
          <AvatarPlaceholderSVG size={sizes[10]} />
        )}
        <Column
          position="absolute"
          bottom={0}
          right={-space[1]}
          justifyContent="center"
          alignItems="center">
          <LevelBadgeSVG />
          <BadgeText>{data?.rank}</BadgeText>
        </Column>
      </Column>

      <Column gap={space[1]} flex={1}>
        <LargeLabel color={colors.secondary} fontWeight="bold">
          {data?.displayName?.en}
        </LargeLabel>
        <SmallLabel color={colors.palette.fwdGreyDarkest}>
          {data?.designationCode}
        </SmallLabel>
      </Column>
    </Row>
  );
};

export default function TeamMembersModal({
  isVisible,
  teamsData,
  onClose,
}: {
  isVisible: boolean;
  teamsData: TeamBranch;
  onClose: () => void;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('teamManagement');
  return (
    <Modal
      visible={isVisible}
      animationType="fade"
      transparent={true}
      style={{ alignItems: 'center', justifyContent: 'center' }}>
      <Background>
        <Column
          paddingLeft={space[12]}
          paddingRight={space[6]}
          paddingTop={space[12]}
          backgroundColor={colors.background}
          borderRadius={borderRadius.large}
          width={480}
          height="100%">
          <Row justifyContent="flex-end">
            <TouchableOpacity onPress={onClose}>
              <Icon.Close size={sizes[6]} fill={colors.secondary} />
            </TouchableOpacity>
          </Row>

          <H6
            color={colors.secondary}
            fontWeight="bold"
            children={`${t('teamTarget.teamMembers')} (${
              teamsData?.branchAgentCount ?? teamsData?.downlineAgents?.length
            })`}
            style={{ paddingBottom: space[3] }}
          />

          <FlatList
            bounces={false}
            data={teamsData?.downlineAgents}
            renderItem={({ item, index }) => (
              <Column paddingRight={space[5]}>
                <RenderItem data={item} key={index} />
                {index !== teamsData?.downlineAgents?.length - 1 && (
                  <Separator />
                )}
              </Column>
            )}
            ListFooterComponent={<Column height={space[12]} />}
          />
        </Column>
      </Background>
    </Modal>
  );
}

const Separator = styled(View)(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

const ImageContainer = styled(View)(({ theme }) => ({
  width: theme.space[10],
  height: theme.space[10],
  borderRadius: theme.space[5],
  overflow: 'hidden',
}));

const AgentImage = styled(Image)(() => ({
  width: '100%',
  height: '100%',
}));

const BadgeText = styled(Text)(({ theme }) => ({
  fontFamily: 'FWDCircularTT-Medium',
  fontSize: 7,
  color: theme.colors.palette.fwdOrange[100],
  position: 'absolute',
}));

const Background = styled(View)(({ theme }) => ({
  justifyContent: 'center',
  alignItems: 'center',
  paddingVertical: theme.space[8],
  flex: 1,
  backgroundColor: 'rgba(0,0,0,0.5)',
}));
