export type RankingData = {
  agentId: string;
  ranking?: number; // index
  agentName?: string;
  title?: string;
  value?: number;
};

export type MemberAchievementData = {
  id: string;
  name: string;
  completedPercent: number;
  targetAmount: number;
  nextTier: string;
  fyc: number;
  fyp: number;
};

export type LeadTiers =
  | 'leads'
  | 'contacted'
  | 'meetingCompleted'
  | 'proposalCreated'
  | 'applicationCreated';

export enum TeamCompareByOptionKey {
  APE = 'APE',
  FYP = 'FYP',
  CASE_COUNT = 'CASE_COUNT',
  AGENT_COUNT = 'AGENT_COUNT',
}

export type TeamOrderByOption = {
  key: TeamCompareByOptionKey;
  label: string;
  filterLabel: string;
};

export type MemberTarget = {
  agentCode: string;
  avatarUrl?: string;
  displayName: { en?: string; th?: string };
  designation?: string;
  lastYearApe?: number;
  lastYearFyp?: number;
  lastYearTargetCase?: number;
  targetAPE: number;
  targetCase: number;
};

export type TeamTarget = {
  agentCode: string;
  currency: string;
  lastYearApe: number;
  lastYearFyp: number;
  lastYearCase: number;
  month: number;
  year: number;
  fypAsOfYear: number;
  targetFyp: number;
  targetAPE: number;
  targetCase: number;
  downlineAgents: MemberTarget[];
};

export const agentRoleCategories = [
  'tm',
  'fwd',
  'fwbm',
  'afwd',
  'fwm',
  'afwm',
  'fwo',
  'afwo',
  'fwp',
  'fwa',
  'afwbm',
] as const;

export enum AgentLevel {
  TM = 'tm',
  FWD = 'fwd',
  FWBM = 'fwbm',
  AFWD = 'afwd',
  FWM = 'fwm',
  AFWM = 'afwm',
  FWO = 'fwo',
  AFWO = 'afwo',
  FWP = 'fwp',
  FWA = 'fwa',
  AFWBM = 'afwbm',
}

export type TabName = (typeof agentRoleCategories)[number];

export type AgentTargetInput = {
  agentId: string;
  agentCode?: string;
  targetAPE?: string | null;
  targetCase?: string | null;
  lastYearAPE?: number;
  avatarUrl?: string;
  displayName?: {
    en?: string;
    th?: string;
  };
};

export enum PersistencyPeriod {
  ROLLING_13 = 'rolling13',
  ROLLING_25 = 'rolling25',
  ROLLING_12 = 'rolling12',
  ROLLING_24 = 'rolling24',
  FIXED_13 = 'fixed13',
  FIXED_25 = 'fixed25',
  FIXED_12 = 'fixed12',
  FIXED_24 = 'fixed24',
}
