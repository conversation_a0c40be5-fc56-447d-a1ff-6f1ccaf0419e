import styled from '@emotion/native';
import { Column } from 'cube-ui-components';

export const Table = styled.View(({ theme }) => ({
  flexDirection: 'row',
  overflow: 'hidden',
}));

export const TableHeaderContainer = styled.View(({ theme: { colors } }) => ({
  flexDirection: 'row',
  backgroundColor: colors.primary,
  width: '100%',
  overflow: 'hidden',
}));

export const LeftColumn = styled(Column)(({ theme: { getElevation } }) => ({
  ...getElevation(4),
  position: 'relative',
  zIndex: 1,
}));

export const RightColumn = styled(Column)(({ theme: { colors, space } }) => ({
  flex: 1,
}));

export const ButtonContainer = styled.TouchableOpacity(
  ({ theme: { colors, space, sizes, borderRadius, getElevation } }) => ({
    borderRadius: borderRadius['x-large'],
    backgroundColor: colors.background,
    ...getElevation(4),
    zIndex: 10,
    paddingHorizontal: space[4],
    paddingVertical: space[1],
    position: 'absolute',
    bottom: space[8],
    left: '50%',
    transform: [{ translateX: '-50%' }],
  }),
);
