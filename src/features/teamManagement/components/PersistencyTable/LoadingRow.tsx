import { StyleProp, ViewStyle } from 'react-native';
import React from 'react';
import styled from '@emotion/native';
import { HeaderConfig } from './TableConfig';
import LoadingItem from './LoadingItem';

export default function LoadingRow({
  headerConfig,
  customStyle,
}: {
  headerConfig?: HeaderConfig[];
  customStyle?: StyleProp<ViewStyle>;
}) {
  return (
    <RowContainer style={customStyle}>
      {headerConfig?.map(({ field, ratio }, i) => (
        <LoadingItem key={`skeletonRow-${field}-${i}`} ratio={ratio} />
      ))}
    </RowContainer>
  );
}

const RowContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  height: theme.sizes[18],
  width: '100%',
}));
