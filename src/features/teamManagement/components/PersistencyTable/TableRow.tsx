import { StyleProp, ViewStyle } from 'react-native';
import React from 'react';
import styled from '@emotion/native';
import TableItem from './TableItem';
import { HeaderConfig } from './TableConfig';
import { Policy } from 'types/team';

export default function TableRow({
  headerConfig,
  data,
  customStyle,
}: {
  headerConfig?: HeaderConfig[];
  data: Policy;
  customStyle?: StyleProp<ViewStyle>;
}) {
  return (
    <RowContainer style={customStyle}>
      {headerConfig?.map(({ field, ratio, isTruncated }, i) => (
        <TableItem
          key={field + i}
          text={data[field]}
          ratio={ratio}
          isTruncated={isTruncated}
        />
      ))}
    </RowContainer>
  );
}

const RowContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  height: theme.sizes[18],
  width: '100%',
}));
