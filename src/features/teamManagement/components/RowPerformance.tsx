import { View, Image, TouchableOpacity, Text, ViewProps } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import { Typography, Icon, Row, Center, Box } from 'cube-ui-components';
import {
  rankingBadgeFirstPlace,
  rankingBadgeSecondPlace,
  rankingBadgeThirdPlace,
} from 'features/teamManagement/assets/images';
import styled from '@emotion/native';
import { RankingData } from '../types';
import numberToKFormat from 'utils/helper/numberToKFormat';
import Skeleton from 'components/Skeleton';

export type RowPerformanceProps = {
  data: RankingData;
  onSelect?: (data: RankingData) => void;
  isLoading?: boolean;
  isLastRow: boolean;
};

export const RowPerformance = ({
  data,
  onSelect,
  isLoading,
  isLastRow,
}: RowPerformanceProps) => {
  const { space, colors } = useTheme();

  const isInTopThreeRanking = data.ranking > 0 && data.ranking < 4;
  const imageSource =
    data.ranking === 1
      ? rankingBadgeFirstPlace
      : data.ranking === 2
      ? rankingBadgeSecondPlace
      : rankingBadgeThirdPlace;

  return (
    <TouchableOpacity
      onPress={() => onSelect?.(data)}
      key={`${data.agentId}_main_touchopacity`}>
      <Container key={`${data.agentId}_container`} isLastRow={isLastRow}>
        <IconContainer key={`${data.agentId}_rowcenter`}>
          {isInTopThreeRanking ? (
            <ImageContainer>
              <RankingLogo source={imageSource} />
            </ImageContainer>
          ) : (
            <RankingTitleContainer>
              <RankingNumber fontWeight="bold">{data.ranking}</RankingNumber>
            </RankingTitleContainer>
          )}
        </IconContainer>
        <Box w={space[2]} />
        {isLoading ? (
          <>
            <Skeleton containerStyle={{ flex: 1 }} height={24} radius={2} />
            <View style={{ width: 40 }} />
            <Skeleton width={65} height={24} radius={2} />
          </>
        ) : (
          <RowTitle>
            <Typography.H7>{data.agentName || data.title}</Typography.H7>
            <RowValue>
              <ValueNumber>
                <Text>{numberToKFormat(data.value)}</Text>
              </ValueNumber>
              <Icon.ChevronRight
                size={space[5]}
                fill={colors.palette.fwdGreyDark}
              />
            </RowValue>
          </RowTitle>
        )}
      </Container>
    </TouchableOpacity>
  );
};

const RowTitle = styled(Row)(({ theme }) => ({
  flex: 1,
  justifyContent: 'space-between',
}));

const RankingNumber = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[50],
}));

const ValueNumber = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.primary,
  marginRight: theme.space[1],
}));

const Container = styled(Center)<ViewProps & { isLastRow: boolean }>(
  ({ theme, isLastRow }) => ({
    padding: theme.space[3],
    justifyContent: 'space-between',
    borderBottomWidth: isLastRow ? 0 : 1,
    borderColor: theme.colors.palette.fwdGrey[100],
    flexDirection: 'row',
  }),
);

const IconContainer = styled(View)(({ theme }) => ({
  alignItems: 'center',
  flexDirection: 'row',
}));

const ImageContainer = styled(View)(({ theme }) => ({
  width: 32,
  height: 32,
}));

const RankingTitleContainer = styled(Center)(({ theme }) => ({
  width: 32,
  height: 32,
}));

const RankingLogo = styled(Image)(() => ({
  width: '100%',
  height: '100%',
}));

const RowValue = styled.View(() => ({
  flexDirection: 'row',
}));
