import React, { useContext } from 'react';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import numberToKFormat from 'utils/helper/numberToKFormat';
import { FlashList } from '@shopify/flash-list';
import styled from '@emotion/native';
import SmileyFaceSVG from 'features/teamManagement/assets/SmileyFaceSVG';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { TeamHierarchyPerformance } from 'types/team';
import { TeamManagementContext } from '../ib/context/TeamManagementContext';
import {
  sortMembersInTable,
  TeamComparisonChartViewYAxisInfo,
} from '../Utils/utils';
import { Stat } from './TeamTable/services';
import { TouchableOpacity } from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { build } from 'utils/context';

const FIRST_COLUMN_FLEX = 1;
type TableContentItem = TeamHierarchyPerformance;

type TeamComparisonData = TeamHierarchyPerformance;

const checkEmptyNumber = (
  num: number | undefined,
  callback: (num: number) => string,
) => {
  if (!num && num !== 0) {
    return '--';
  }
  return callback(num);
};
// const findLargestAndSmallest = (
//   config: TableConfig,
//   data: TeamComparisonData,
// ) => {
//   return config.reduce(
//     (acc, item, index) => ({
//       largest:
//         item.data > acc.largest.value
//           ? { value: item.data, index }
//           : acc.largest,
//       smallest:
//         item.data < acc.smallest.value
//           ? { value: item.data, index }
//           : acc.smallest,
//     }),
//     {
//       APE: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       Case: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       BSC: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       CFF: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       Persistency: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       Complaint: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//       CPD: {
//         largest: { value: 0, index: 0 },
//         smallest: { value: 0, index: 0 },
//       },
//     } satisfies
//     | Record<
//       Extract<MetricKeys, 'BSC' | 'CFF' | 'Complaint' | 'CPD'>,
//       {
//         largest: { value: number; index: number };
//         smallest: { value: number; index: number };
//       }
//     >
//     | Record<
//       Extract<MetricKeys, 'APE' | 'Case' | 'Persistency'>,
//       {
//         firstParam: {
//           largest: { value: number; index: number };
//           smallest: { value: number; index: number };
//         };
//         secondParam: {
//           largest: { value: number; index: number };
//           smallest: { value: number; index: number };
//         };
//       }
//     >,
//   );
// };

export default function TeamComparisonDetailTable({
  data,
  currentTab,
}: {
  data: TeamComparisonData;
  currentTab: keyof typeof TeamComparisonChartViewYAxisInfo;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  if (!data) return <></>;
  const { agentName, designation } = data;
  // const topAndLastIndexPerMetrcMap = config.reduce((acc, cur) => {
  //   const key = cur.type;
  //   // switch (key) {
  //   //   case 'APE':
  //   //   case 'Case'
  //   //   case 'BSC':
  //   //   case 'CFF':
  //   //   case 'Persistency':
  //   //   case 'Complaint':
  //   //   case 'CPD':
  //   //   default:
  //   //     break;
  //   // }

  //   acc[cur.type] = {
  //     topIndex: 0,
  //     lastIndex: 0,
  //   };
  //   return acc;
  // }, {} as Record<MetricKeys, { topIndex: number; lastIndex: number }>);
  const header = designation
    ? `Team performance of ${agentName} (${designation})`
    : `Team performance of ${agentName}`;
  return (
    <Box gap={space[6]}>
      <Typography.H6 fontWeight="bold">{header}</Typography.H6>
      <TeamPerformanceTable data={data} currentTab={currentTab} />
    </Box>
  );
}
const config = [
  {
    Icon: Icon.Investment,
    type: 'APE',
    label: 'APE',
    subTitle: ['Issued', 'Submitted'],
    flex: 1.5,
  },
  {
    Icon: Icon.DocumentCertified,
    type: 'Case',
    label: 'Case',
    subTitle: ['Issued', 'Submitted'],
    flex: 1.5,
  },
  // {
  //   Icon: Icon.Cheque,
  //   type: 'BSC',
  //   label: 'BSC',
  //   subTitle: ['Score'],
  //   flex: 0.8,
  // },
  // {
  //   Icon: Icon.CorporateGovernance,
  //   type: 'CFF',
  //   label: 'CFF',
  //   subTitle: ['Ratio'],
  //   flex: 0.8,
  // },
  {
    Icon: Icon.Calendar4,
    type: 'Persistency',
    label: 'Persistency',
    subTitle: ['1st year', '2nd year'],
    flex: 1.5,
  },
  // {
  //   Icon: Icon.Support,
  //   type: 'Complaint',
  //   label: 'Complaint',
  //   subTitle: ['number'],
  //   flex: 0.8,
  // },
  // {
  //   Icon: Icon.Team,
  //   type: 'CPD',
  //   label: 'CPD',
  //   subTitle: ['Hours'],
  //   flex: 0.8,
  // },
] as const;
type TableConfig = typeof config;
type MetricKeys = (typeof config)[number]['type'];

function Header() {
  const { colors, space, sizes, borderRadius, getElevation } = useTheme();
  return (
    <Row minH={space[19]} alignItems="center">
      <Box flex={FIRST_COLUMN_FLEX} />
      {config.map(({ type, label, subTitle, Icon, flex }) => (
        <Box flex={flex} key={type} gap={space[2]}>
          <Row alignItems="center" justifyContent="center" gap={space[1]}>
            <Typography.SmallLabel>{label}</Typography.SmallLabel>
            <Icon size={space[4]} fill={colors.palette.fwdDarkGreen[100]} />
          </Row>
          <Row>
            {subTitle.map(sub => (
              <Typography.SmallBody
                key={type + sub}
                style={{ flex: 1, textAlign: 'center' }}
                color={colors.palette.fwdGreyDarkest}>
                {sub}
              </Typography.SmallBody>
            ))}
          </Row>
        </Box>
      ))}
      <Column width={space[6]} />
    </Row>
  );
}

function TeamPerformanceTable({
  data,
  currentTab,
}: {
  data: TeamComparisonData;
  currentTab: keyof typeof TeamComparisonChartViewYAxisInfo;
}) {
  const { colors } = useTheme();
  const { timePeriod } = useContext(TeamManagementContext);
  const timePeriodKey = timePeriod.toLowerCase() as 'mtd' | 'ytd';

  const totalMetric = data.teamPerformance && {
    apeSubmission: data.teamPerformance[timePeriodKey].submittedApe,
    apeCompletion: data.teamPerformance[timePeriodKey].issuedApe,
    caseSubmission: data.teamPerformance[timePeriodKey].submittedCase,
    caseCompletion: data.teamPerformance[timePeriodKey].issuedCase,
    firstYearPersistency: data.teamPerformance.persistency.firstYear,
    secondYearPersistency: data.teamPerformance.persistency.secondYear,
  };

  const members = sortMembersInTable({
    members: data.members,
    timePeriod: timePeriod,
    performanceIndex: currentTab,
    designation: data.designation,
  });

  const allApeSubmitted = members?.map(
    data => data?.individualPerformance?.[timePeriodKey]?.submittedApe ?? 0,
  );
  const allApeIssued = members?.map(
    data => data?.individualPerformance?.[timePeriodKey]?.issuedApe ?? 0,
  );
  const allIssuedCase = members?.map(
    data => data?.individualPerformance?.[timePeriodKey]?.issuedCase ?? 0,
  );
  const allSubmittedCase = members?.map(
    data => data?.individualPerformance?.[timePeriodKey]?.submittedCase ?? 0,
  );
  const allFirstYearPersistency = members?.map(
    data => data?.individualPerformance?.persistency?.firstYear ?? 0,
  );
  const allsecondYearPersistency = members?.map(
    data => data?.individualPerformance?.persistency?.secondYear ?? 0,
  );

  const stat = {
    highestApeSubmmited: allApeSubmitted?.length
      ? Math.max(...allApeSubmitted)
      : 0,
    lowestApeSubmmited: allApeSubmitted?.length
      ? Math.min(...allApeSubmitted)
      : 0,
    highestApeIssued: allApeIssued?.length ? Math.max(...allApeIssued) : 0,
    lowestApeIssued: allApeIssued?.length ? Math.min(...allApeIssued) : 0,
    highestCaseSubmitted: allSubmittedCase?.length
      ? Math.max(...allSubmittedCase)
      : 0,
    lowestCaseSubmitted: allSubmittedCase?.length
      ? Math.min(...allSubmittedCase)
      : 0,
    highestCaseIssued: allIssuedCase?.length ? Math.max(...allIssuedCase) : 0,
    lowestCaseIssued: allIssuedCase?.length ? Math.min(...allIssuedCase) : 0,
    highest1stYearPersistency: allFirstYearPersistency?.length
      ? Math.max(...allFirstYearPersistency)
      : 0,
    lowest1stYearPersistency: allFirstYearPersistency?.length
      ? Math.min(...allFirstYearPersistency)
      : 0,
    highest2ndYearPersistency: allsecondYearPersistency?.length
      ? Math.max(...allsecondYearPersistency)
      : 0,
    lowest2ndYearPersistency: allsecondYearPersistency?.length
      ? Math.min(...allsecondYearPersistency)
      : 0,
  };

  return (
    <FlashList
      estimatedItemSize={80}
      keyExtractor={d => String(d.agentCode)}
      data={members}
      ListHeaderComponent={() => (
        <>
          <Header />
          <TableTotalRow totalMetric={totalMetric} />
          <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
        </>
      )}
      renderItem={({ item, index }) => (
        <TableContentRow item={item} index={index} stat={stat} />
      )}
      ItemSeparatorComponent={() => (
        <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
      )}
    />
  );
}
function TableTotalRow({
  totalMetric,
}: {
  totalMetric: {
    apeCompletion: number;
    apeSubmission: number;
    caseSubmission: number;
    caseCompletion: number;
    firstYearPersistency: number;
    secondYearPersistency: number;
  };
}) {
  const { colors, space, sizes } = useTheme();
  return (
    <Row minH={space[18]} backgroundColor={colors.palette.fwdGrey[20]}>
      <Row
        flex={FIRST_COLUMN_FLEX}
        alignItems="center"
        gap={space[3]}
        pl={space[2]}>
        <Typography.Body
          style={{
            maxWidth: sizes[24],
          }}>
          {'Team total'}
        </Typography.Body>
      </Row>
      {config.map((c, index) => {
        switch (c.type) {
          case 'APE':
            return (
              <Row flex={c.flex} alignItems="center">
                <TableItemText>
                  {checkEmptyNumber(
                    totalMetric?.apeCompletion,
                    numberToKFormat,
                  )}
                </TableItemText>
                <TableItemText>
                  {checkEmptyNumber(
                    totalMetric?.apeSubmission,
                    numberToKFormat,
                  )}
                </TableItemText>
                <ColumnSeparator />
              </Row>
            );
          case 'Case':
            return (
              <Row flex={c.flex} alignItems="center">
                <TableItemText>
                  {checkEmptyNumber(
                    totalMetric?.caseCompletion,
                    numberToKFormat,
                  )}
                </TableItemText>
                <TableItemText>
                  {checkEmptyNumber(
                    totalMetric?.caseSubmission,
                    numberToKFormat,
                  )}
                </TableItemText>
                <ColumnSeparator />
              </Row>
            );
          case 'Persistency':
            return (
              <Row flex={c.flex} alignItems="center">
                <TableItemText>
                  {!totalMetric?.firstYearPersistency &&
                  totalMetric?.firstYearPersistency !== 0
                    ? '--'
                    : totalMetric?.firstYearPersistency + '%'}
                </TableItemText>
                <TableItemText>
                  {!totalMetric?.secondYearPersistency &&
                  totalMetric?.secondYearPersistency !== 0
                    ? '--'
                    : totalMetric?.secondYearPersistency + '%'}
                </TableItemText>
              </Row>
            );
          default:
            return (
              <Row flex={c?.flex ?? 1} alignItems="center">
                <TableItemText>--</TableItemText>
                {config.length - 1 === index ? null : <ColumnSeparator />}
              </Row>
            );
        }
      })}
      {/* {TODO:remove environment logic} */}
      {(build === 'dev' || build === 'sit') && <Column width={space[6]} />}
    </Row>
  );
}

function TableContentRow({
  item,
  index,
  stat,
}: {
  item: TableContentItem;
  index: number;
  stat: Stat;
}) {
  const { colors, space, sizes, borderRadius, getElevation } = useTheme();
  const ranking = index + 1;
  const { timePeriod } = useContext(TeamManagementContext);
  const timePeriodKey = timePeriod.toLowerCase() as 'mtd' | 'ytd';
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const isTop = (currentValue?: number, maxValue?: number) => {
    if (!currentValue) {
      return false;
    }
    return currentValue === maxValue;
  };

  const isLast = (currentValue?: number, minValue?: number) => {
    if (!currentValue) {
      return false;
    }
    return currentValue === minValue;
  };

  const data = item;
  const agentName =
    data?.designation !== 'AGT'
      ? `${data?.agentName} (${data?.designation})`
      : data?.agentName;
  return (
    <TouchableOpacity
      disabled={build !== 'dev' && build !== 'sit'}
      onPress={() => {
        if (build === 'dev' || build === 'sit') {
          navigation.navigate('TeamIndividualProfile', {
            agentName: `${data.agentName}`,
            agentCode: `${data.agentCode}`,
          });
        }
      }}>
      <Row minH={space[20]} alignItems="center">
        <Row
          flex={FIRST_COLUMN_FLEX}
          alignItems="center"
          gap={space[3]}
          pl={space[2]}>
          <Typography.Body>{`${ranking}`}</Typography.Body>
          <Typography.Body
            style={{
              maxWidth: sizes[24],
            }}>
            {agentName}
          </Typography.Body>
        </Row>
        {config.map(c => {
          switch (c.type) {
            case 'APE':
              return (
                <Row flex={c.flex} alignItems="center">
                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.[timePeriodKey]?.issuedApe,
                      stat?.highestApeIssued,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.[timePeriodKey]?.issuedApe,
                      stat?.lowestApeIssued,
                    )}>
                    {checkEmptyNumber(
                      data?.individualPerformance?.[timePeriodKey]?.issuedApe,
                      numberToThousandsFormat,
                    )}
                  </TableItemTextWithIcon>

                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedApe,
                      stat?.highestApeSubmmited,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedApe,
                      stat?.lowestApeSubmmited,
                    )}>
                    {checkEmptyNumber(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedApe,
                      numberToThousandsFormat,
                    )}
                  </TableItemTextWithIcon>
                  <ColumnSeparator />
                </Row>
              );
            case 'Case':
              return (
                <Row flex={c.flex} alignItems="center">
                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.[timePeriodKey]?.issuedCase,
                      stat?.highestCaseIssued,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.[timePeriodKey]?.issuedCase,
                      stat?.lowestCaseIssued,
                    )}>
                    {checkEmptyNumber(
                      data?.individualPerformance?.[timePeriodKey]?.issuedCase,
                      numberToThousandsFormat,
                    )}
                  </TableItemTextWithIcon>
                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedCase,
                      stat?.highestCaseSubmitted,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedCase,
                      stat?.lowestCaseSubmitted,
                    )}>
                    {checkEmptyNumber(
                      data?.individualPerformance?.[timePeriodKey]
                        ?.submittedCase,
                      numberToThousandsFormat,
                    )}
                  </TableItemTextWithIcon>

                  <ColumnSeparator />
                </Row>
              );
            // // case 'BSC':
            // //   return (
            // //     <Row flex={c.flex} justifyContent="center" alignItems="center">
            // //       <TableItemTextWithIcon isLast>
            // //         {data?.bsc ?? '--'}%
            // //       </TableItemTextWithIcon>
            // //       <ColumnSeparator />
            // //     </Row>
            // //   );
            // // case 'CFF':
            // //   return (
            // //     <Row flex={c.flex} alignItems="center">
            // //       <TableItemTextWithIcon isTop>
            // //         {numberToKFormat(data?.cffCompletion) + '%'}
            // //       </TableItemTextWithIcon>
            // //       <ColumnSeparator />
            // //     </Row>
            // //   );
            case 'Persistency':
              return (
                <Row flex={c.flex} alignItems="center">
                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.persistency?.firstYear,
                      stat?.highest1stYearPersistency,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.persistency?.firstYear,
                      stat?.lowest1stYearPersistency,
                    )}>
                    {!data?.individualPerformance?.persistency?.firstYear &&
                    data?.individualPerformance?.persistency?.firstYear !== 0
                      ? '--'
                      : numberToKFormat(
                          data?.individualPerformance?.persistency?.firstYear,
                        ) + '%'}
                  </TableItemTextWithIcon>
                  <TableItemTextWithIcon
                    isTop={isTop(
                      data?.individualPerformance?.persistency?.secondYear,
                      stat?.highest2ndYearPersistency,
                    )}
                    isLast={isLast(
                      data?.individualPerformance?.persistency?.secondYear,
                      stat?.lowest2ndYearPersistency,
                    )}>
                    {!data?.individualPerformance?.persistency?.secondYear &&
                    data?.individualPerformance?.persistency?.secondYear !== 0
                      ? '--'
                      : numberToKFormat(
                          data?.individualPerformance?.persistency?.secondYear,
                        ) + '%'}
                  </TableItemTextWithIcon>
                </Row>
              );
            // case 'Complaint':
            //   return (
            //     <Row flex={c.flex} alignItems="center">
            //       <TableItemText>
            //         {numberToKFormat(data?.complaint)}
            //       </TableItemText>
            //       <ColumnSeparator />
            //     </Row>
            //   );
            // case 'CPD':
            //   return (
            //     <Row flex={c.flex} alignItems="center">
            //       <TableItemText>
            //         {numberToKFormat(data?.cpd) + ' hrs'}
            //       </TableItemText>
            //     </Row>
            //   );
            default:
              return null;
          }
        })}
        {/* {TODO:remove environment logic} */}
        {(build === 'dev' || build === 'sit') && (
          <Icon.ChevronRight fill={colors.primary} size={sizes[6]} />
        )}
      </Row>
    </TouchableOpacity>
  );
}

const TableItemText = styled(Typography.Body)(() => ({
  textAlign: 'center',
  textAlignVertical: 'center',
  flex: 1,
}));

function TableItemTextWithIcon({
  children,
  isLast,
  isTop,
}: {
  children?: React.ReactNode;
  isLast?: boolean;
  isTop?: boolean;
}) {
  const { colors, space, sizes, borderRadius, getElevation } = useTheme();

  return (
    <Box
      flex={1}
      px={space[5]}
      py={space[4]}
      alignItems="center"
      justifyContent="center">
      <Box
        minH={54}
        minW={44}
        alignItems="center"
        justifyContent="center"
        backgroundColor={
          isTop
            ? colors.palette.alertGreenLight
            : isLast
            ? colors.palette.alertRedLight
            : colors.palette.whiteTransparent
        }
        borderRadius={borderRadius['x-small']}>
        <TableItemText
          style={{
            flex: undefined,
            position: 'relative',
          }}>
          {children}
        </TableItemText>
        {(isTop || isLast) && (
          <Box position="absolute" top={6}>
            {isTop ? (
              <Icon.ThumbUp
                size={space[3]}
                fill={colors.palette.fwdDarkGreen[100]}
              />
            ) : isLast ? (
              <SmileyFaceSVG
                size={space[3]}
                fill={colors.palette.fwdDarkGreen[100]}
              />
            ) : (
              <></>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
}

const ColumnSeparator = styled(Box)(({ theme: { colors, space } }) => ({
  width: 1,
  height: space[6],
  backgroundColor: colors.palette.fwdGrey[100],
}));
