import React from 'react';
import styled from '@emotion/native';
import {
  Column,
  ExtraSmallLabel,
  LargeBody,
  Row,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { TableConfig, ColumnType } from './type';
import { useTheme } from '@emotion/react';

export default function TableHeader({ config }: { config: TableConfig }) {
  const { space, colors, sizes } = useTheme();

  return (
    <TableHeaderContainer>
      {config?.length > 0 && (
        <SecondHeader>
          {config?.map((con, index) => {
            const { name, Icon, ratio, column, type, isShowHeaderSeperator } =
              con;
            return (
              <React.Fragment key={name + type + index}>
                <SecondRowItem
                  width={ratio}
                  borderColor={
                    isShowHeaderSeperator ? '#D9D9D9' : 'transparent'
                  }>
                  {type === ColumnType.text ? (
                    <SingleItemColumn>
                      <Column marginLeft={space[3]}>
                        <LargeBody>{name}</LargeBody>
                      </Column>
                    </SingleItemColumn>
                  ) : (
                    <DoubleHeaderColumn>
                      <Row
                        gap={space[1]}
                        justifyContent="center"
                        alignItems="center">
                        <SmallLabel color={colors.secondary}>{name}</SmallLabel>
                        <Column
                          width={20}
                          height={20}
                          justifyContent="center"
                          alignItems="center">
                          {Icon && (
                            <Icon
                              width={21}
                              height={sizes[5]}
                              fill={colors.secondary}
                            />
                          )}
                        </Column>
                      </Row>

                      {type === ColumnType.multipleColumn && (
                        <Row alignItems="center">
                          {column?.map(columnName => (
                            <SubColumnItem key={columnName}>
                              <ExtraSmallLabel
                                color={colors.palette.fwdGreyDarker}>
                                {!!columnName && columnName}
                              </ExtraSmallLabel>
                            </SubColumnItem>
                          ))}
                        </Row>
                      )}
                    </DoubleHeaderColumn>
                  )}
                </SecondRowItem>
              </React.Fragment>
            );
          })}
          <PaddingColumn />
        </SecondHeader>
      )}
    </TableHeaderContainer>
  );
}

const TableHeaderContainer = styled(Column)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({}),
);

const SecondHeader = styled(Row)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({
    flex: 1,
    paddingVertical: space[4],
    height: 78,
  }),
);

const SecondRowItem = styled(Column)(
  ({ theme: { sizes, borderRadius, colors } }) => ({
    height: '100%',
    justifyContent: 'center',
    paddingHorizontal: 6,
    borderRightWidth: 1,
  }),
);

const SingleItemColumn = styled(Column)(
  ({ theme: { sizes, borderRadius, colors } }) => ({
    justifyContent: 'flex-end',
    height: '100%',
  }),
);

const DoubleHeaderColumn = styled(Column)(
  ({ theme: { sizes, borderRadius, colors, space } }) => ({
    gap: space[2],
    height: '100%',
    justifyContent: 'center',
  }),
);

const SubColumnItem = styled(Column)(
  ({ theme: { sizes, borderRadius, colors } }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  }),
);

const PaddingColumn = styled(Column)(
  ({ theme: { sizes, borderRadius, colors } }) => ({
    borderRightWidth: 1,
    borderColor: 'transparent',
    width: '4%',
  }),
);
