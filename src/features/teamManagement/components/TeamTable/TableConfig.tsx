import { Icon, SvgIconProps } from 'cube-ui-components';

import { ColumnType } from './type';
import { t } from 'i18next';
import { PersistencyPeriod } from 'features/teamManagement/types';
import { getPersistencyRollingDateRange } from 'features/teamManagement/Utils/utils';

//note: Date column already occupied 4% of the width

export type TableConfig = {
  name: string;
  Icon: null | ((props: SvgIconProps) => JSX.Element);
  ratio: string;
  column: (string | null)[];
  type: ColumnType;
  isShowSeperator?: boolean;
  isShowHeaderSeperator?: boolean;
};

export const getSalesTableConfig = (isTeamLeader: boolean): TableConfig[] => [
  {
    name: isTeamLeader ? t('teamManagement:team') : t('teamManagement:agent'),
    Icon: null,
    ratio: '24%',
    column: ['Team'],
    type: ColumnType.text,
  },
  {
    name: isTeamLeader
      ? t('teamManagement:teamComparison.teamAPE')
      : t('teamManagement:teamComparison.agentAPE'),
    Icon: Icon.Investment,
    ratio: '24%',
    column: ['Issued', 'Submitted'],
    isShowSeperator: true,
    type: ColumnType.multipleColumn,
  },
  {
    name: isTeamLeader
      ? t('teamManagement:teamComparison.teamCase')
      : t('teamManagement:teamComparison.agentCase'),
    Icon: Icon.DocumentCertified,
    ratio: '24%',

    column: ['Issued', 'Submitted'],
    isShowSeperator: true,
    type: ColumnType.multipleColumn,
  },
  {
    name: isTeamLeader
      ? t('teamManagement:teamComparison.persistency')
      : t('teamManagement:teamComparison.agentPersistency'),
    Icon: Icon.Calendar4,
    ratio: '24%',
    column: ['1st year', '2nd year'],
    type: ColumnType.multipleColumn,
  },
  {
    name: '',
    Icon: null,
    ratio: '4%',
    column: [null],
    type: ColumnType.text,
  },
];

export const mapaTableConfig: TableConfig[] = [
  {
    name: 'Team',
    Icon: null,
    ratio: '20%',
    column: [null],
    type: ColumnType.text,
  },
  {
    name: 'Manpower',
    Icon: Icon.Team,
    ratio: '19%',
    column: ['Total agent', 'Active agent'],
    isShowSeperator: true,
    type: ColumnType.multipleColumn,
  },

  {
    name: 'Active ratio',
    Icon: Icon.BalanceScale,
    ratio: '19%',
    column: ['Submission', 'Issuance'],
    isShowSeperator: true,
    type: ColumnType.multipleColumn,
  },
  {
    name: 'Productivity',
    Icon: Icon.ClipPath,
    ratio: '19%',
    column: ['Submission', 'Issuance'],
    isShowSeperator: true,
    type: ColumnType.multipleColumn,
  },
  {
    name: 'Average case size (RM)',
    Icon: Icon.ChartBar,
    ratio: '19%',
    column: ['Submission', 'Issuance'],
    type: ColumnType.multipleColumn,
  },
];

export const leadConversionConfig = [
  {
    name: 'Team',
    Icon: Icon.Account,
    ratio: '32%',
    column: ['Team'],
    type: ColumnType.text,
  },
  {
    name: 'Lead generated',
    Icon: Icon.Account,
    ratio: '32%',
    column: ['Lead generated'],
    isShowSeperator: true,
    type: ColumnType.textWithIcon,
  },
  {
    name: 'Lead conversion rate',
    Icon: Icon.InvestorInformation,
    ratio: '32%',
    column: ['Lead conversion rate'],
    type: ColumnType.textWithIcon,
  },
];

export const getPersistencyConfig = (
  selectedMonth: string,
  selectedYear: string,
) => {
  return [
    {
      name: 'Team breakdown',
      Icon: null,
      ratio: '20%',
      column: [null],
      type: ColumnType.text,
    },
    {
      name: '13 month',
      Icon: null,
      ratio: '16%',
      column: [
        getPersistencyRollingDateRange(
          { year: selectedYear, month: selectedMonth },
          PersistencyPeriod.ROLLING_13,
        ),
      ],
      type: ColumnType.multipleColumn,
    },
    {
      name: '25 month',
      Icon: null,
      ratio: '16%',
      column: [
        getPersistencyRollingDateRange(
          { year: selectedYear, month: selectedMonth },
          PersistencyPeriod.ROLLING_25,
        ),
      ],
      isShowSeperator: true,
      isShowHeaderSeperator: true,
      type: ColumnType.multipleColumn,
    },
    {
      name: '12 month',
      Icon: null,
      ratio: '16%',
      column: [
        getPersistencyRollingDateRange(
          { year: selectedYear, month: selectedMonth },
          PersistencyPeriod.ROLLING_12,
        ),
      ],
      type: ColumnType.multipleColumn,
    },
    {
      name: '24 month',
      Icon: null,
      ratio: '16%',
      column: [
        getPersistencyRollingDateRange(
          { year: selectedYear, month: selectedMonth },
          PersistencyPeriod.ROLLING_24,
        ),
      ],
      type: ColumnType.multipleColumn,
    },
    {
      name: '',
      Icon: null,
      ratio: '16%',
      column: [null],
      type: ColumnType.text,
    },
  ];
};
