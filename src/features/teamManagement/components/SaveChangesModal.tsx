import { memo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import DialogPhone from 'components/Dialog.phone';
import { Button, Icon, Typography } from 'cube-ui-components';
import styled from '@emotion/native';

type SaveChangesModalProps = {
  dialogVisible: boolean;
  onClose: () => void;
  onConfirm: () => void;
};

export const SaveChangesModal = memo(function ExitModal({
  dialogVisible,
  onClose,
  onConfirm,
}: SaveChangesModalProps) {
  const { colors } = useTheme();
  const { t } = useTranslation('teamManagement');
  return (
    <DialogPhone visible={dialogVisible}>
      <ButtonClose onPress={onClose}>
        <Icon.Close fill={colors.palette.black} />
      </ButtonClose>
      <Title fontWeight="bold">{t('teamTarget.saveChanges')}</Title>
      <RowInfo>
        <SubTitle>{`${t('teamTarget.saveChangesMessage')}:`}</SubTitle>
      </RowInfo>
      <ButtonConfirm text={t('save')} onPress={onConfirm} />
    </DialogPhone>
  );
});

const ButtonConfirm = styled(Button)(({ theme }) => ({
  marginTop: theme.space[8],
}));

const ButtonClose = styled(TouchableOpacity)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  alignSelf: 'flex-end',
}));

const Title = styled(Typography.ExtraLargeBody)(({ theme }) => ({
  justifyContent: 'center',
  alignItems: 'center',
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const SubTitle = styled(Typography.LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const RowInfo = styled(View)(({ theme }) => ({
  marginTop: theme.space[4],
}));
