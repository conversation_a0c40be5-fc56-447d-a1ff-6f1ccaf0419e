import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import React from 'react';
import styled from '@emotion/native';
import { Body, Icon, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';

export default function NarrowDropdown({
  selectedValue,
  optionList,
  onSelect,
  dropDownStyle,
}: {
  selectedValue: string;
  optionList: Array<{ label: string; value: string }>;
  onSelect: (value: string) => void;
  dropDownStyle?: StyleProp<ViewStyle>;
}) {
  const { space } = useTheme();
  return (
    <Autocomplete
      renderInput={() => (
        <Button
          selectedValue={
            optionList.find(item => item?.value === selectedValue)?.label || ''
          }
        />
      )}
      value={selectedValue}
      data={optionList as any}
      getItemLabel={(item: any) => item?.label}
      getItemValue={(item: any) => item?.value}
      onChange={value => onSelect(value)}
      modalStyle={{ marginTop: space[1], width: 146 }}
    />
  );
}

const Button = ({ selectedValue }: { selectedValue: string }) => {
  const { space, colors, sizes } = useTheme();
  return (
    <TouchableOpacity>
      <Outline>
        <Row>
          <Body fontWeight="medium">{selectedValue}</Body>
        </Row>
        <Row marginLeft={space[1]}>
          <Icon.ChevronDown size={sizes[4]} fill={colors.secondary} />
        </Row>
      </Outline>
    </TouchableOpacity>
  );
};

const Outline = styled(Row)(({ theme: { colors, space, borderRadius } }) => ({
  paddingVertical: space[2],
  paddingLeft: space[3],
  paddingRight: space[2],
  borderRadius: borderRadius['small'],
  borderColor: colors.palette.fwdGrey[100],
  borderWidth: 1,
  alignItems: 'center',
  backgroundColor: colors.background,
}));
