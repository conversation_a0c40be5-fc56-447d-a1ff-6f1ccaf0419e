import { useTheme } from '@emotion/react';
import { Row, Typography } from 'cube-ui-components';
import React, { useContext } from 'react';
import { useTranslation } from 'react-i18next';

import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
} from 'react-native';
import { TeamManagementContext } from '../ib/context/TeamManagementContext';

export enum TimePeriod {
  MTD = 'MTD',
  YTD = 'YTD',
}

const TimePeriodTabs = () => {
  const { t } = useTranslation('teamManagement');
  const { space, colors } = useTheme();
  const { timePeriod: currentTab, updateTimePeriod: updateTab } = useContext(
    TeamManagementContext,
  );
  return (
    <Row alignItems="center" gap={space[2]}>
      <Typography.Label color={colors.palette.fwdGreyDarkest}>
        {t('timePeriod')}
      </Typography.Label>
      <TimeTabButton
        containerStyle={{
          flex: 0,
        }}
        isActive={currentTab === TimePeriod.MTD}
        label={t('mtd')}
        onPress={() => updateTab(TimePeriod.MTD)}
      />
      <TimeTabButton
        containerStyle={{
          flex: 0,
        }}
        isActive={currentTab === TimePeriod.YTD}
        label={t('ytd')}
        onPress={() => updateTab(TimePeriod.YTD)}
      />
    </Row>
  );
};

export default TimePeriodTabs;

export function TimeTabButton(
  props: {
    containerStyle?: ViewStyle;
    isActive: boolean;
    label: string;
    Icon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    isActiveMedium?: boolean;
    onPress?: () => void;
  } & TouchableOpacityProps,
) {
  const { colors, space, borderRadius } = useTheme();
  const { isActive, label, containerStyle, isActiveMedium, ...rest } = props;

  return (
    <TouchableOpacity
      style={[
        {
          flex: 1,
          borderWidth: isActive ? 2 : 1,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: borderRadius['x-large'],
          paddingHorizontal: space[3],
          borderColor: isActive ? colors.primary : colors.palette.fwdGrey[100],
          backgroundColor: isActive
            ? colors.primaryVariant3
            : colors.background,
          height: space[8],
        },
        containerStyle,
      ]}
      {...rest}>
      <Row alignItems="center" gap={space[1]}>
        {props?.Icon}
        <Typography.Label
          fontWeight={
            isActive ? (isActiveMedium ? 'normal' : 'medium') : 'normal'
          }
          color={colors.secondary}>
          {label}
        </Typography.Label>
        {props.rightIcon}
      </Row>
    </TouchableOpacity>
  );
}
