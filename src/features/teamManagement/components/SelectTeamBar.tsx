import { TouchableOpacity, View } from 'react-native';
import React, { useState, useContext, useEffect } from 'react';
import { Column, LargeBody, Row, SmallLabel } from 'cube-ui-components';
import styled from '@emotion/native';

import { useTheme } from '@emotion/react';
import OrgChartSVG from '../assets/OrgChartSVG';
import SelectTeamModal from './SelectTeamModal';
import { TeamManagementContext } from '../ib/context/TeamManagementContext';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { formatTeamHierarchy } from '../Utils/utils';
import AvatarPlaceholderSVG from '../assets/AvatarPlaceholderSVG';
export default function SelectTeamBar() {
  const { data } = useGetTeamHierarchy();

  const [teamDetail, setTeamDetail] = useState([]);
  const {
    updateSelectedAgentId,
    selectedAgentId,
    updateIsSearching,
    designation,
  } = useContext(TeamManagementContext);

  useEffect(() => {
    if (data) setTeamDetail(formatTeamHierarchy(data));
  }, [data]);

  const { sizes, space, colors } = useTheme();

  const selectedAgentInfo = teamDetail.find(({ id }) => id === selectedAgentId);
  const [isShowModal, setIsShowModal] = useState(false);
  const onSelect = (id: string) => {
    setTeamDetail(
      teamDetail.map(agent =>
        agent.id === id
          ? { ...agent, isSelected: true }
          : { ...agent, isSelected: false },
      ),
    );
    setIsShowModal(false);
    updateSelectedAgentId(id);
  };
  const selectedTeam = teamDetail.find(({ isSelected }) => isSelected);

  return (
    <View style={{ paddingRight: 32 }}>
      <PanelContainer>
        <Column>
          <Row gap={space[2]} alignItems="center">
            <TouchableOpacity
              onPress={() => setIsShowModal(true)}
              disabled={teamDetail.length <= 1}>
              <NameContainer>
                <SubTitle position="absolute">
                  <SmallLabel>Selected team</SmallLabel>
                </SubTitle>

                <AvatarPlaceholderSVG size={space[9]} />
                <Column flex={1}>
                  <LargeBody>{selectedAgentInfo?.name}</LargeBody>
                </Column>
                <OrgChartSVG />
              </NameContainer>
            </TouchableOpacity>

            {/* <TouchableOpacity onPress={() => updateIsSearching(true)}>
            <SearchIcon>
              <Icon.Search fill={colors.primary} size={sizes[5]} />
            </SearchIcon>
          </TouchableOpacity> */}
          </Row>
        </Column>
        {isShowModal && (
          <SelectTeamModal
            data={teamDetail}
            onDismiss={() => setIsShowModal(false)}
            onSelect={id => onSelect(id)}
          />
        )}
      </PanelContainer>
    </View>
  );
}

const NameContainer = styled(Row)(
  ({ theme: { space, sizes, colors, borderRadius } }) => ({
    alignItems: 'center',
    height: space[14],
    paddingVertical: 10,
    paddingHorizontal: space[4],
    gap: space[3],
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[100],
    width: 339,
    position: 'relative',
  }),
);

const SubTitle = styled(Column)(
  ({ theme: { space, sizes, colors, borderRadius } }) => ({
    left: space[4],
    top: -7,
    position: 'absolute',
    backgroundColor: colors.palette.white,
    paddingHorizontal: space[1],
  }),
);

const SearchIcon = styled(Column)(
  ({ theme: { sizes, colors, borderRadius } }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: borderRadius['xx-large'],
    width: sizes[12],
    height: sizes[12],
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[100],
  }),
);

const PanelContainer = styled(Column)(
  ({ theme: { sizes, colors, borderRadius } }) => ({
    paddingTop: 10,
    paddingBottom: 10,
  }),
);
