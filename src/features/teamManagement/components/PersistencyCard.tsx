import React from 'react';
import { Column, Row, SmallLabel, Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import { ProgressGauge } from 'components/Chart';
import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';

export default function PersistencyCard({
  percentage,
  benchmark,
  month,
  period,
  textColor,
  lineColor,
  isLoading,
}: {
  percentage?: number | null;
  benchmark: number;
  month: string;
  period: string;
  textColor: string;
  lineColor: string;
  isLoading: boolean;
}) {
  const { space, colors, sizes } = useTheme();
  return (
    <CardContainer>
      <Column width={space[20]} height={space[12]} overflow="hidden">
        <ProgressGauge
          type="semi"
          percent={percentage || 0}
          strokeWidth={sizes[2]}
          color={isLoading ? colors.palette.fwdGrey[100] : lineColor}
          padding={0}
          center={
            isLoading ? (
              <Row justifyContent="center" alignItems="center" top={-space[2]}>
                <Skeleton width={sizes[10]} height={sizes[4]} radius={0} />
                <SmallLabel fontWeight="bold" color={textColor}>
                  %
                </SmallLabel>
              </Row>
            ) : (
              <Row justifyContent="center" alignItems="center" top={-space[2]}>
                <Typography.H6 fontWeight="bold" color={textColor}>
                  {!percentage && percentage !== 0 ? '--' : percentage}
                </Typography.H6>
                <SmallLabel color={textColor}>%</SmallLabel>
              </Row>
            )
          }
          benchmark={benchmark}
          style={{
            height: space[20],
            width: space[20],
          }}
        />
      </Column>
      <Column>
        <Typography.Body fontWeight="medium">{month} month</Typography.Body>
        <Typography.SmallLabel color={colors.palette.fwdGreyDarkest}>
          {period}
        </Typography.SmallLabel>
      </Column>
    </CardContainer>
  );
}

const CardContainer = styled(Row)(
  ({ theme: { colors, space, borderRadius } }) => ({
    flex: 1,
    paddingVertical: space[4],
    gap: space[6],
    borderRadius: borderRadius.medium,
    borderColor: colors.palette.fwdGrey[50],
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  }),
);
