import { isSameDay } from 'date-fns';
import { OcrResult } from 'types/ocr';
import { Gender } from 'types/person';

export enum OcrValidationResult {
  Match = 'match',
  NameMismatch = 'name-mismatch',
  DobOrGenderMismatch = 'dob-or-gender-mismatch',
}

export type MismatchFields = {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date | null;
  gender?: Gender | null;
};

export const validateOcr = (
  ocrData: OcrResult['extract'],
  partInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: Date | null;
    gender: Gender;
  },
): [OcrValidationResult, MismatchFields] => {
  const mismatchFields: MismatchFields = {};
  let result: OcrValidationResult = OcrValidationResult.Match;

  if (ocrData.firstName?.toLowerCase() !== partInfo.firstName?.toLowerCase()) {
    mismatchFields.firstName = ocrData.firstName || '';
    result = OcrValidationResult.NameMismatch;
  }
  if (ocrData.lastName?.toLowerCase() !== partInfo.lastName?.toLowerCase()) {
    mismatchFields.lastName = ocrData.lastName || '';
    result = OcrValidationResult.NameMismatch;
  }
  if (
    !ocrData.dateOfBirth ||
    !isSameDay(
      ocrData.dateOfBirth || new Date(),
      partInfo.dateOfBirth || new Date(),
    )
  ) {
    mismatchFields.dateOfBirth = ocrData.dateOfBirth;
    result = OcrValidationResult.DobOrGenderMismatch;
  }
  if (
    ocrData.gender !== (partInfo.gender === Gender.MALE ? 'MALE' : 'FEMALE')
  ) {
    if (ocrData.gender === 'MALE') {
      mismatchFields.gender = Gender.MALE;
    } else if (ocrData.gender === 'FEMALE') {
      mismatchFields.gender = Gender.FEMALE;
    } else {
      mismatchFields.gender = null;
    }
    result = OcrValidationResult.DobOrGenderMismatch;
  }
  return [result, mismatchFields];
};
