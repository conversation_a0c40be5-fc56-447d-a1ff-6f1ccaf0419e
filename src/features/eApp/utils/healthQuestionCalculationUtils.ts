export const cmToFtAndInch = (cm: number) => {
  const ft = Math.trunc(cm / 30.48);
  const inch = Math.round(((cm / 30.48) % 1) * 12);
  return { ft, inch };
};
export const ftAndInchToCm = (ft: number, inch: number) => {
  const cm = Math.round((ft * 12 + inch) * 2.54 * 100) / 100;
  let numericFt = ft;
  let numericInch = inch;
  if (inch >= 12) {
    numericFt = numericFt + Math.round(inch / 12);
    numericInch = numericInch % 12;
  }
  return { cm, newFt: numericFt, newInch: numericInch };
};

export const kgToLbs = (kg: number) => {
  const lbs = Math.round(kg * 2.2 * 10) / 10;
  return { lbs };
};
export const lbsToKg = (lbs: number) => {
  const kg = Math.round((lbs / 2.2) * 10) / 10;
  return { kg };
};
