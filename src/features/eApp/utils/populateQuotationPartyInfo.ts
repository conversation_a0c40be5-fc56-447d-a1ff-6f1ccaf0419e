import { Quotation } from 'types/quotation';
import { EAppState } from './store/eAppStore';
import { getFullName } from 'features/eApp/utils/getFullName';
import { format } from 'date-fns';
import { PartyType } from 'types/party';

export const populateQuotationPartyInfo = (
  agentId: string,
  quotation: Quotation,
  policyOwnerPersonalInfo: EAppState['policyOwnerPersonalInfo'],
  insuredPersonalInfo: EAppState['insuredPersonalInfo'],
  isPIEqualPO: boolean,
) => {
  const proposerPersonalDetails =
    policyOwnerPersonalInfo.personalDetails.customerType === PartyType.ENTITY
      ? policyOwnerPersonalInfo.authorizedRepresentativeDetails
      : policyOwnerPersonalInfo.personalDetails;
  quotation.proposers = [
    {
      id: policyOwnerPersonalInfo.id,
      age: policyOwnerPersonalInfo.personalDetails.age,
      agentId: agentId || '',
      cid: '',
      clientType:
        policyOwnerPersonalInfo.personalDetails.customerType ===
        PartyType.INDIVIDUAL
          ? 'Individual'
          : 'Entity',
      createdDT: new Date(),
      dob: format(
        policyOwnerPersonalInfo.personalDetails.dateOfBirth || new Date(),
        'yyyy-MM-dd',
      ),
      extensionName: proposerPersonalDetails.extensionName || '',
      fullName: getFullName(proposerPersonalDetails),
      ...(policyOwnerPersonalInfo.personalDetails.customerType ===
      PartyType.INDIVIDUAL
        ? { gender: policyOwnerPersonalInfo.personalDetails.gender }
        : undefined),
      givenname: {
        en: proposerPersonalDetails.firstName,
      },
      surname: proposerPersonalDetails.lastName,
      firstName: proposerPersonalDetails.firstName,
      lastName: proposerPersonalDetails.lastName,
      leadSourceCode:
        policyOwnerPersonalInfo.personalDetails.customerType ===
        PartyType.ENTITY
          ? policyOwnerPersonalInfo.entityDetails.leadSource
          : policyOwnerPersonalInfo.personalDetails.leadSource,
      middleName: proposerPersonalDetails.middleName,
      relationship:
        policyOwnerPersonalInfo.personalDetails.customerType ===
        PartyType.ENTITY
          ? 'OTHER'
          : 'SELF',
      residentCountry: '',
      title: proposerPersonalDetails.title,
      ...(policyOwnerPersonalInfo.personalDetails.customerType ===
      PartyType.ENTITY
        ? {
            companyName: policyOwnerPersonalInfo.entityDetails.entityName,
            natureOfBusiness:
              policyOwnerPersonalInfo.entityDetails.businessNature,
          }
        : undefined),
      updatedDT: new Date(),
    },
  ];
  if (isPIEqualPO) {
    quotation.insureds = quotation.proposers;
  } else {
    quotation.insureds = [
      {
        id: policyOwnerPersonalInfo.id,
        age: policyOwnerPersonalInfo.personalDetails.age,
        agentId: agentId || '',
        cid: '',
        clientType:
          insuredPersonalInfo.personalDetails.customerType ===
          PartyType.INDIVIDUAL
            ? 'Individual'
            : 'Entity',
        createdDT: new Date(),
        dob: format(
          insuredPersonalInfo.personalDetails.dateOfBirth || new Date(),
          'yyyy-MM-dd',
        ),
        extensionName: insuredPersonalInfo.personalDetails.extensionName || '',
        fullName: getFullName(insuredPersonalInfo.personalDetails),
        gender: insuredPersonalInfo.personalDetails.gender,
        givenname: {
          en: insuredPersonalInfo.personalDetails.firstName,
        },
        surname: insuredPersonalInfo.personalDetails.lastName,
        firstName: insuredPersonalInfo.personalDetails.firstName,
        lastName: insuredPersonalInfo.personalDetails.lastName,
        leadSourceCode: '',
        middleName: insuredPersonalInfo.personalDetails.middleName,
        relationship: insuredPersonalInfo.personalDetails.relationship,
        residentCountry: '',
        title: insuredPersonalInfo.personalDetails.title,
        updatedDT: new Date(),
      },
    ];
  }
};
