export default {
  app: 'Application',
  home: 'Home',
  next: 'Next',
  ok: 'OK',
  exitEApp: 'Are you sure to quit?',
  exitEAppDesc: 'Do you want to save before exit the application?',
  dontSave: "Don't save",
  save: 'Save',
  policyOwner: 'Certificate owner',
  insured: 'Person covered',
  mainInsured: 'Main Person covered',
  payor: 'Payor',
  beneficiary: 'Nomination',
  optional: 'optional',
  failedToStart: 'Failed to start application',
  failedToSaveData: 'Failed to save data',
  goToTheField: 'Go to the field',
  'info.title': 'Your details',
  'consents.title': 'Consents',
  'renewalPaymentSetup.title': 'Direct credit',
  'directCredit.menu': 'Direct credit details',
  title: 'Upload documents',
  'documentUpload.frontID': 'Front of ID (NRIC/Passport/Birth Certificate)',
  'documentUpload.backID': 'Back of ID (NRIC/Passport/Birth Certificate)',
  'documentUpload.consentLetter': 'Consent form',
  'documentUpload.selfieNRIC': 'Selfie with NRIC',
  'documentUpload.permanentResident': 'Permanent resident',
  'documentUpload.largeAmountQuestionnaire': 'Large amount questionnaire',
  'documentUpload.foreignerQuestionnaire': 'Foreigner questionnaire',
  'documentUpload.validPass':
    'Valid VISA/Employment Pass/Resident Pass/MM2H VISA',
  'documentUpload.residentialQuestionnaire': 'Residential Questionnaire',
  'documentUpload.covid19Questionnaire': 'Covid-19 Questionnaire',
  'documentUpload.fatcaCrsDeclarationForm': 'FATCA/CRS declaration form',
  'documentUpload.consentFormFromParentLegalGuardian':
    'Consent Form from Parent/Legal Guardian',
  'documentUpload.authorisationLetter':
    'Authorisation letter for the appointed Authorised Signatory',
  'documentUpload.certifiedTrueForm24':
    'Certified true copy of Form 24/Section 51/Section 78',
  'documentUpload.certifiedTrueForm58':
    'Certified true copy of Form 49/Section 14/Section 58',
  'documentUpload.certifiedTrueForm9':
    'Certified true copy of Form 9/Section 17/SSM',
  'documentUpload.frontMyKad': 'Front of MyKad for the Authorised Signatory',
  'documentUpload.backMyKad': 'Back of MyKad for the Authorised Signatory',
  'documentUpload.passport': 'Passport for the Authorised Signatory',
  'reviewSummary.title': 'Review & signatures',
  'payment.title': 'Payment',
  'directCredit.form.bankName': 'Bank name',
  'directCredit.form.accountNumber': 'Bank account number',
  'directCredit.form.accountHolderName': 'Account holder name',
  'directCredit.form.mobileNumber': 'Mobile number',
  'directCredit.form.identityType': 'Identity type',
  'directCredit.form.identityNumber': 'Identification number',
  'directCredit.form.email': 'Email address',
  'directCredit.form.debitAmount': 'Direct debit amount (RM)',
  'directCredit.form.maxNumberDebiting': 'Max. number of debiting',
  'directCredit.form.paymentPurpose': 'Purpose of payment',
  'directCredit.form.paymentFrequencyNo': 'Payment reference no.',
  'directCredit.form.paymentFrequency': 'Frequency of debiting',
  'directCredit.form.emailWarning':
    'An email will be sent to your customer if eMandate registration process.',
  'directCredit.form.termCondition1':
    'By clicking this box, I fully understand to the ',
  'directCredit.form.termCondition2': 'Terms & Conditions ',
  'directCredit.form.termCondition3':
    ' of Direct Debit services and I acknowledge that upon successful completion of this online application, RM {{amount}} shall be debited from my selected account for the purpose of this service and the amount shall be refunded to me by FWD Takaful.',
  'directCredit.viewTerm': 'Terms and Conditions of Direct Credit Instruction',
  // errors
  'validation.requiredInput': 'Required field',
  'validation.invalidFormat': 'Invalid format',
  'validation.invalidDate': 'Invalid date',
  'validation.error.required': 'Required field',
  'validation.error.duplicate': 'The reason can not be same',
  'validation.error.invalidFormat': 'Invalid format',
  'validation.error.minLength5': 'Minimum length is 5',
  'validation.error.maxLength20': 'Maximum length is 20',
  'validation.error.NRICNotMatch':
    'NRIC number doesn’t match with Date of birth or Gender',
  'validation.error.format': 'Error in format',
  'validation.error.invalidAnnualIncomeAmount':
    'Value should be greater than 200,000',
  'termAndCondition.title.en': 'Terms and Conditions Direct Credit Instruction',
  'termAndCondition.title.my': 'Terma dan Syarat Arahan Pengkreditan Terus',
  close: 'Close',
  'btn.english': 'English',
  'btn.takaful': 'Bahasa',
  gender: 'Gender',
  open: 'Open',
  male: 'Male',
  female: 'Female',
  email: 'Email',
  smokingHabit: 'Smoking habit',
  smoker: 'Smoker',
  nonSmoker: 'Non-Smoker',
  address: 'Address',
  titleDeleteDialog: 'Are you sure to remove?',
  first: 'First',
  second: 'Second',
  third: 'Third',
  fourth: 'Fourth',
  fifth: 'Fifth',
  nthChildInformation: '{{position}} child’s information',
  childInformation: '{{childName}}’s information',
  taxTitle: 'Tax - Purpose of takaful coverage',
  business: 'Business',
  private: 'Private',
  continue: 'Continue',
  dateFormat: 'DD/MM/YYYY',
  contentDeleteDialog:
    'Filled information under the {{position}} child will be removed.',
  'certificate.title': 'Certificate owner info',
  'certificate.subtitle1': 'Importance of truthful disclosure: ',
  'certificate.subtitle2':
    'It’s important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy.',
  'certificate.participantTitle': 'Personal details',
  'certificate.menu.certificateOwner': 'Certificate owner',
  'certificate.menu.personCovered': 'Person covered',
  'certificate.menu.andreaGarcia': 'Andrea Garcia',
  'certificate.title.correspondenceAddress': 'Correspondence address',
  'certificate.title.residentialAddress': 'Residential address',
  'certificate.title.businessAddress': 'Business address',
  'certificate.button.addChild': 'Add child / dependents',
  'certificate.form.title': 'Title',
  'certificate.form.maritalStatus': 'Marital status',
  'certificate.form.dateOfBirth': 'Date of birth',
  'certificate.form.age': 'Age',
  'certificate.form.primaryIdType': 'ID Type',
  'certificate.form.additionalIDType': 'Additional ID Type (optional)',
  'certificate.form.additionalIdType': 'Additional ID Type',
  'certificate.form.additionalIDType.required': 'Additional ID Type',
  'certificate.form.source': 'Lead Source',
  'certificate.form.fullName': 'Full name',
  'certificate.form.ethnicity': 'Ethnicity',
  'certificate.form.race': 'Race',
  'certificate.form.religion': 'Religion',
  'certificate.form.identificationNumber': 'Identification number',
  'certificate.form.additionalIdentification':
    'Additional identification number (optional)',
  'certificate.form.additionalIdentification.required':
    'Additional identification number',
  'certificate.form.additional.identification':
    'Additional identification number',
  'certificate.nationalityTitle': 'Nationality details',
  'certificate.form.nationality': 'Nationality',
  'certificate.form.countryOfBirth': 'Place of birth - Country',
  'certificate.form.stateOfBirth': 'Place of birth - State',
  'certificate.form.cityOfBirth': 'Place of birth - City',
  'certificate.form.cityName': 'City name',
  'certificate.occupationTitle': 'Occupation details',
  'certificate.form.occupation': 'Occupation',
  'certificate.form.nameOfBusiness': 'Name of business/Employer',
  'certificate.form.exactDuties': 'Exact duties',
  'certificate.form.annualIncomeAmount': 'Annual income amount (RM)',
  'certificate.form.natureOfWork': 'Nature of work/Business',
  'certificate.form.annualIncome': 'Annual income',
  'certificate.form.annualIncomeOptional': 'Annual income (optional)',
  'certificate.form.reminder':
    'Ensure income declared matches amount in Bank Suitability Assessment Report (SAR)',
  'certificate.contactTitle': 'Contact details',
  'certificate.form.countryCode': 'Code',
  'certificate.form.mobileNumber': 'Mobile number',
  'certificate.form.homePhone': 'Home phone number (optional)',
  'certificate.form.mobilePhone': 'Mobile phone no.',
  'certificate.form.homeNumber': 'Home phone no.',
  'certificate.form.officeNumber': 'Office phone no.',
  'certificate.form.faxNumber': 'Fax number (optional)',
  'certificate.form.officePhone': 'Office phone number (optional)',
  'certificate.addressInfoTitle': 'Address Information',
  'certificate.form.addressLine1': 'Address line 1',
  'certificate.form.SubAddressLine1':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'certificate.form.addressLine2': 'Address line 2 (optional)',
  'certificate.form.SubAddressLine2': 'Street no./Street Name',
  'certificate.form.addressLine3': 'Address line 3 (optional)',
  'certificate.form.postCode': 'Postcode',
  'certificate.form.city': 'City',
  'certificate.form.state': 'State',
  'certificate.form.country': 'Country',
  'certificate.form.correspondenceAddress': 'Correspondence Address',
  'certificate.form.residentialAddress': 'Residential address',
  'certificate.form.businessAddress': 'Business address',
  'certificate.form.nameOfChild': 'Name of child / dependent',
  'certificate.form.relationship': 'Relationship',
  'certificate.form.relationshipWithCertificate':
    'Relationship with certificate owner',
  'certificate.form.yearToSupport': 'Year to support',
  'certificate.form.occupationChild': 'Occupation',
  'certificate.form.preferredCertificate': 'Preferred copy of your certificate',
  'certificate.form.preferredContact': 'Preferred contact mode',
  'certificate.form.preferredDocument': 'Preferred document language',
  'certificate.form.paymentMethod': 'Payment method',
  'certificate.form.transactionAmount': 'Transaction amount (RM)',
  'certificate.occupationClass': 'Occupation class',
  'certificate.className': 'Class 1',
  'certificate.occupationDescription': 'Occupation description',
  'certificate.occupationDescContent':
    'Profession in specific business or services (e.g. accountant, company secretary, lawyer, property agent or developer.)',

  'certificate.correspondenceAddress.certificate':
    'Same as Certificate Owner Address',
  'certificate.correspondenceAddress.new': 'New Address',

  'certificate.residentialAddress.correspondence':
    'Same as Correspondence Address',
  'certificate.residentialAddress.new': 'New Address',

  'certificate.businessAddress.new': 'New Address',
  'certificate.campaignCode': 'Campaign code',
  'certificate.affiliateCode': 'Affiliate Code',
  'certificate.affiliateCampaignCode': 'Affiliate Campaign Code',

  'other.nominationDetails.title': 'Do you want to make a nomination?',
  'other.nominationDetails.chooseHibahOrWasi':
    'Please choose either Hibah or Wasi.',
  'other.nominationDetails.hibahDetails': 'Hibah’s details',
  'other.nominationDetails.nominationNotApplicable':
    'Nomination is not applicable.',
  'other.nominationDetails.nominationDuplicated':
    'The nomination is duplicated.',
  'other.nominationDetails.minimumAllocation': 'Minimum allocation is 10%',
  'other.nominationDetails.wasiDetails': 'Wasi’s details',
  'other.nominationDetails.maximumHibah': '*Maximum to add 5 hibah',
  'other.nominationDetails.maximumWasi': '*Maximum to add 2 wasi',
  'other.nominationDetails.addHibah': 'Add hibah',
  'other.nominationDetails.addWasi': 'Add wasi',
  'other.nominationDetails.hibah': 'Hibah',
  'other.nominationDetails.guardianee': '- Guardianee',
  'other.nominationDetails.wasi': 'Wasi',
  'other.nominationDetails.confitionalHibah': 'Confitional hibah',
  'other.nominationDetails.allocation': 'Allocation (%)',
  'other.nominationDetails.payablePercentageArrangement':
    'Payable percentage arrangement',
  'other.totalPercentage': '{{totalPercentage}}/100%',
  'other.nominationDetails.total': 'Total:',
  'other.nominationDetails.payorDetails.title': 'Payor’s details',
  'other.nominationDetails.payorDetails.question':
    'Is the certificate owner the same as payor?',
  'other.nominationDetails.details': 'Nomination details',
  'other.nominationDetails.switchConfirmationDialog.title':
    'Are you sure to choose {{type}}?',
  'other.nominationDetails.switchConfirmationDialog.des':
    'You can only select either Hibah or Wasi for the nomination details. If you choose Hibah, the information you’ve filled in Wasi will be removed.',
  'other.nominationDetails.circularProgressChart.totalProportion':
    'Total proportion',
  yes: 'Yes',
  no: 'No',
  cancel: 'Cancel',
  remove: 'Remove',

  'declaration.fatca': 'FATCA / CRS',
  'declaration.fatca.title':
    'Foreign Account Tax Compliance Act (FATCA) & Common Reporting Standard (CRS)',
  'declaration.fatca.message':
    'In relation to Foreign Account Tax Compliance Act (FATCA) and/or Common Reporting Standard (CRS)’s requirement, FWD Takaful require you to answer question as follow:',
  'declaration.fatca.hint':
    '*According to the record, your nationality is not Malaysian.',
  'declaration.fatca.question.1':
    'Do you have and/or act for yourself and/or others who have tax residency from other countries outside Malaysia?',
  'declaration.fatca.question.2':
    'Are you a United States citizen / United States Green Card holder / United States resident for US federal tax purposes?',
  'declaration.fatca.taxResidency':
    'Tax residency from other countries ({{index}})',
  'declaration.fatca.taxResidency.country':
    'Country/Jurisdiction of tax residence',
  'declaration.fatca.taxResidency.tin.question':
    'Do you have tax identification number (TIN)?',
  'declaration.fatca.taxResidency.tin.number':
    'Tax identification number (TIN)',
  'declaration.fatca.taxResidency.tin.add': 'Add tax residency',
  'declaration.fatca.taxResidency.noTin.title':
    'If no TIN available, please provide reason',
  'declaration.fatca.taxResidency.noTin.additionalInfomation':
    'Additional information for reason',
  'declaration.fatca.taxResidency.noTin.1':
    'The country/jurisdiction where the Account Holder is resident does not issue TiNs to its residents',
  'declaration.fatca.taxResidency.noTin.2':
    'The Account Holder is unable to obtain a TIN or equivalent number (Please explain why you are unable to obtain a TIN in the above table if you have selected this reason)',
  'declaration.fatca.taxResidency.noTin.3':
    'No TIN is required (Note: only select this reason if the domestic law of the relevant jurisdiction does not require the collection of the TIN issued by such jurisdiction)',

  'declaration.declarationDetail.title': 'Declaration',
  english: 'English',
  bahasa: 'Bahasa',
  'declaration.declarationDetail.herebyDeclare':
    'I, as the prospective Takaful Certificate Owner, hereby declare:',
  'declaration.declarationDetail.complianceStatement':
    'The Compliance Statement relates to tax residents outside Malaysia',
  'declaration.declarationDetail.a': 'a. ',
  'declaration.declarationDetail.b': 'b. ',
  'declaration.declarationDetail.c': 'c. ',
  'declaration.declarationDetail.d': 'd. ',
  'declaration.declarationDetail.1':
    'All the information that I give to FWD Takaful as stated in this declaration form is correct and complete. I shall be fully responsible for all the consequences that may occur if there is any wrong information. I agree to disclose, inform or provide FWD Takaful with my personal information or data within 30 calendar days since the proposal / changes that relate to my personal information and status, or I am being taxed in more than one country at a certain time as disclosure to any authorities. The data update will be my responsibility and it will not be FWD Takaful’s responsibility.',
  'declaration.declarationDetail.2':
    'I understand that it is mandatory for FWD Takaful and/or affiliation to comply with any regulation, guideline, instruction and requirement that have been stipulated in the local regulations applicable in Malaysia or foreign law including the Foreign Account Tax Compliance Act of the United States of America (FATCA), or any public agreement, judicative, taxation, government and/or other authority such as the Inland Revenue Board of Malaysia and Internal Revenue Service (IRS) that are pertinent in several jurisdictions including Malaysia that have been announced together with changes from time to time (Law Obligation).',
  'declaration.declarationDetail.3':
    'I agree to provide necessary assistance to FWD Takaful to comply with all regulations as stipulated in the mandated law on my Takaful Certificates. If the provision of data is more than 30 days since the transaction, I shall bear all the risks including financial loss that is incurred due to transaction rejection and/or freezing of the transaction.',
  'declaration.declarationDetail.4':
    'I shall give authority to FWD Takaful to report tax information and/or data that I own to related authorities.',
  'declaration.roc': 'Replacement of Certificate (ROC)',
  'declaration.roc.title': 'Replacement of Certificate (ROC)',
  'declaration.roc.question.number.1': '1. ',
  'declaration.roc.question.number.2': '2. ',
  'declaration.roc.question.number.2a': '2a. ',
  'declaration.roc.question.number.3': '3. ',
  'declaration.roc.question.1':
    'Do you intend to replace any of your existing family Takaful plans with us or with any other Takaful Operator/Insurance Company with the proposal of this New Family Takaful Plan?',
  'declaration.roc.question.2':
    'Is there any party that has influenced you to surrender or terminate any of your existing Takaful Certificate?',
  'declaration.roc.question.2a':
    'Were you fully satisfied with the explanation given to you?*',
  'declaration.roc.question.3':
    'Is this application a coverage extension to an existing family Takaful product? If Yes, please note that such extension is entirely at the discretion of the Certificate Owner.*',
  'declaration.roc.replacementReason': 'Replacement Reason',
  'declaration.roc.takafulOperator': 'Takaful Operator',
  'declaration.roc.planName': 'Plan Name',
  'declaration.roc.sumCovered': 'Sum Covered (RM)',
  'declaration.roc.comment': 'Comment',

  'declaration.note': 'The above information is correct.',

  'health.fullUnderwriting':
    'You are not eligible for SIO underwriting, please proceed to answer the full underwriting question as below:',

  'bar.appDetail': 'Your details',
  'bar.policyOwner': 'Certificate owner',
  'bar.company': 'Company info',
  'bar.authorizedSignatory': 'Authorized signatory info',
  'bar.insured': 'Person covered',
  'bar.otherStakeholder': 'Other(s)',
  'bar.declaration': 'Declaration',
  'bar.healthQuestion': 'Health questions',
  'bar.consents': 'Consents',
  'bar.directCredit': 'Direct credit',
  'bar.uploadDocument': 'Upload documents',
  'bar.reviewSignature': 'Review & signatures',
  'bar.payment': 'Payment',
  pdp: 'PDP',
  'personCovered.employeeTitle': 'Person covered’s Information',
  'personCovered.spouseTitle': 'Spouse’s Information',
  'personCovered.childTitle': 'Child’s Information',
  'personCovered.subtitle1': 'Importance of truthful disclosure: ',
  'personCovered.subtitle2':
    'It’s important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy.',
  'personCovered.className': 'Finance',
  'personCovered.menu.pc': 'Person covered',
  'personCovered.menu.spouse': 'Spouse',
  'personCovered.menu.child': 'Child',
  back: 'Back',
  'documentUpload.menu.certificateOwner': 'Certificate owner',
  'documentUpload.menu.spouse': 'Spouse',
  'documentUpload.menu.personCovered': 'Person covered',
  'documentUpload.menu.child': 'Child',
  'documentUpload.menu.employee': 'Employee',
  'documentUpload.menu.employeeSpouse': 'Employee spouse',
  'documentUpload.menu.employeeChild': 'Employee child',
  'documentUpload.menu.payor': 'Third Party Payor',
  'documentUpload.menu.company': 'Company',
  'documentUpload.uploadLater': 'Upload later',
  'documentUpload.f2fQuestion': 'Are you meeting your customer face to face?',
  'documentUpload.f2fQuestion.note':
    'Note: Consent form and selfie with NRIC are required if you are selling remotely.',
  'documentUpload.documentsOf': 'Documents of {{label}}',
  'documentUpload.acceptedFormats':
    'Accepted formats: jpg, jpeg, png, or pdf (up to 5MB each)',
  'documentUpload.upload': 'Upload',
  'documentUpload.takePhotoWithCamera': 'Take Photo with Camera',
  'documentUpload.selectFromAlbum': 'Select from Album',
  'documentUpload.attachFile': 'Attach file',
  'documentUpload.deleteTitle': 'Are you sure to delete?',
  'documentUpload.deleteSubtitle': 'Are you sure to delete the uploaded file?',
  'documentUpload.deleted': 'Deleted the file',
  'documentUpload.failedToDelete': 'Failed to delete the file',
  'documentUpload.failedToUpload': 'Upload document failed',
  viewPDPANotice: 'View PDPA Notice',
  'idTypeCd.nricNew': 'New NRIC',
  'idTypeCd.passport': 'Passport',
  'idTypeCd.nricOld': 'Old NRIC',
  'idTypeCd.birthCertificate': 'Birth Certificate',
  'maritalStatusCode.divorced': 'Divorced',
  'maritalStatusCode.married': 'Married',
  'maritalStatusCode.single': 'Single',
  'maritalStatusCode.widowed': 'Widowed',
  'leadSourceCode.referredByCompany': 'Referred by company',
  'leadSourceCode.selfSource': 'Self source',
  'labelSourceCode.recommendedByAnotherCompany':
    'Recommended by another company',
  'labelSourceCode.referredByBusinessBanking':
    'Referred by Retail Business Banking',
  'ethnicityCd.chinese': 'Chinese',
  'ethnicityCd.indian': 'Indian',
  'ethnicityCd.malay': 'Malay',
  'ethnicityCd.others': 'Others',
  'religionCd.buddhist': 'Buddhist',
  'religionCd.christianity': 'Christianity',
  'religionCd.hinduism': 'Hinduism',
  'religionCd.islam': 'Islam',
  'religionCd.others': 'Others',
  'summary.title': 'Application summary',
  'summary.cert': 'Certification owner information',
  'summary.cert.title': 'Certification owner',
  'summary.cert.personal.title': 'Personal details',
  'summary.cert.contact.title': 'Contact details',
  'summary.cert.address.title': 'Address information',
  'summary.cert.nationality.title': 'Nationality details',
  'summary.cert.occupation.title': 'Occupation details',
  'summary.document': 'Documents uploaded',
  'summary.cert.health': 'Health and lifestyle information',
  'review.applicationSummary': 'Application summary',
  'review.idCard': 'ID card',
  'review.health.PO': 'Certification owner’s health conditions',
  'review.health.PI': 'Person covered’s health conditions',
  view: 'View',
  'review.basePlanSummary': 'Base plan',
  'review.topUp': 'Top-up',
  'review.applicationDetails': 'Application details',
  'review.sumCovered': 'Sum Covered',
  'review.plan': 'Plan',
  'review.rider.optionalPackageBenefit': 'Optional package benefit',
  'review.rider.optionalBenefit': 'Optional benefit',
  'review.rider.medReimbursementPurchase':
    'Med. Reimbursement, Purchase of Orthopedic & Alt. Medical Treatment',
  'review.rider.annualIncome': 'Annual income',
  'review.rider.monthlyIncomeBenefit': 'Monthly Income Benefit',
  'review.rider.coverageSelection': 'Coverage selection',
  'review.rider.charitable.title': 'Charitable organisation',
  'review.rider.charitable.code': 'Charitable organisation',
  'review.rider.charitable.donation': 'Allocation',
  'review.rider.charitable.maxTotalAmount':
    'Total allocation cannot exceed 100%',
  'review.deductible': 'Deductible',
  'review.contribution': 'Contribution',
  'review.coverageDetails.title': 'Coverage details',
  'review.paymentType': 'Payment type',
  'review.certificateTerm': 'Certificate term',
  'review.contributionMode': 'Contribution mode',
  'review.singleContribution': 'Single contribution',
  'review.contributionTerm': 'Contribution term',
  'review.sinceYear': 'Since certificate year',
  'review.certificateYearFrom': 'Certificate year from',
  'review.certificateYearTo': 'Certificate year to',
  'review.applicationNumber': 'Application number',
  'review.certificateOwner': 'Certificate Owner',
  'review.personCovered': 'Person Covered',
  'review.email': 'Email',
  'review.payment.singlePremium': 'Premium Tunggal',
  'review.dateOfApplication': 'Date of application',
  'review.certificateCommencementDate': 'Certificate commencement date',
  'review.fundAllocation': 'Fund allocation',
  'review.fundName': 'Fund name',
  'review.allocation': 'Allocation',
  'review.coveragePerson': 'Coverage person',
  'review.coverage.title': 'Coverage {{index}}: ',
  'review.personCovered.name': 'Person covered: {{name}}',
  'review.personCovered.spouse': 'Person covered (Spouse)',
  'review.personCovered.child': 'Person covered (Child {{index}})',
  'review.payor': 'Payor',
  'review.payor.info': 'Payor information',
  'review.salutationTitle': 'Salutation/Title',
  'review.gender': 'Gender',
  'review.additionalProtection': 'Additional protection',
  'review.riderName': 'Rider name',
  'review.modalContribution': 'Modal contribution',
  'review.basicAnnualContribution': 'Basic annual contribution',
  'review.initialContribution': 'Initial contribution',
  'review.regularCashPayment': 'Regular cash payment',
  'review.familyCoverLimit': 'Family Cover Limit (FCL)',
  'review.Initial contribution': 'Initial contribution',
  'review.Regular cash payment': 'Regular cash payment',
  'review.Family Cover Limit (FCL)': 'Family Cover Limit (FCL)',
  'review.formatAge': '{{age}} y/o',
  'review.personalInfo': 'Personal information',
  'review.certificationOwnerInformation': 'Certification owner information',
  'review.certificateOwnerInformation': 'Certificate owner information',
  'review.personCoveredInformation': 'Person covered/ payor information',
  'review.healthQuestions': 'Health questions',
  'review.beneficiary': 'Nomination',
  'review.nomination.title': 'Nomination ({{type}})',
  'review.nomination.information': '{{type}} {{index}} information',
  'review.nomination.guardianee': '{{type}} {{index}} - {{relationship}}',
  'review.nomination.personalDetails': 'Personal details',
  'review.declarationAndCertificate': 'Declaration & Certificate documents',
  'review.declaration': 'Consents and declarations',
  'review.declaration.title':
    'Consents and declarations you have read and agreed to:',
  'review.declaration.fatca': 'FATCA/CRS',
  'review.declaration.replacementOfCertificate': 'Replacement of Certificate',
  'review.declaration.personalDataProtection': 'Personal Data Protection (PDP)',
  'review.documentsUploaded': 'Documents uploaded',
  'review.cert': 'Certificate documents',
  'review.cert.benefitIllustration': 'Benefit illustration',
  'review.cert.benefitIllustration.my': 'Benefit illustration',
  'review.cert.benefitIllustration.en': 'Benefit illustration',
  'review.cert.customerFactFinding': 'Customer Fact Finding',
  'review.cert.applicationForm': 'Application form',
  'review.cert.reviewYour.title': 'Please review your {{title}}',
  'review.documentOf': 'Document of {{name}}',
  'review.authorizedSignatoryDetails': 'Authorized signatory details',
  'personalDetails.customerType': 'Customer type',
  'personalDetails.primaryIdType': 'Primary ID type',
  'nationalityDetails.nationality': 'Nationality',
  'nationalityDetails.placeOfBirth': 'Place of birth',
  'signature.certificateOwner':
    'Certificate owner: {{name}} | Signature date: {{date}}',
  'signature.authorizedSignatory':
    'Authorized signatory: {{name}} | Signature date: {{date}}',
  'signature.personCovered':
    'Person covered: {{name}} | Signature date: {{date}}',
  'signature.servicingAgent':
    'Servicing agent: {{name}} | Signature date: {{date}}',
  'signature.approver': 'Approver: {{name}} | Signature date: {{date}}',
  'signature.title.certificateOwner':
    "{{name}}'s (Certificate owner) signature",
  'signature.title.authorizedSignatory':
    "{{name}}'s (Authorized signatory) signature",
  'signature.title.personCovered': "{{name}}'s (Person covered) signature",
  'signature.title.servicingAgent': "{{name}}'s (Servicing agent) signature",
  'signature.title.approver': "{{name}}'s (Approver) signature",
  'signature.understandAndConfirm':
    'I understand and confirm that I have studied, read and understand the life insurance. And life insurance agents Have a look at my life insurance products. Also verify the accuracy of the details provided by the life insurance agent and I have has signed the document for the Company to precede with the next step.',
  'signature.agreeTerm': 'I, {{name}}, agree to the terms and conditions:',
  'signature.agreeTermDetail':
    'I/We declare and agree on behalf of myself/our self and any person or persons, firm or corporation, who may have or claim any interest in this application form, that:',
  'signature.declaration.title.agent': 'Declaration of Agent/ Intermediary',
  'signature.declaration.title.policyOwner':
    'Declaration Made by Certificate Owner',
  'signature.declaration.title.insured': 'Declaration Made by Person Covered',
  'signature.viewDetails': 'View full version of terms and conditions',
  'signature.importantNoticeSignature':
    'Your signature will be appeared in the document you have reviewed',
  'signature.clear': 'Clear',
  'signature.placeOfSigning': 'Place of signature',
  'signature.policyOwner': 'Certificate owner:',
  'signature.signatureDate': 'Signature date:',
  'signature.dateOfSigning': 'Date of signing:',
  'signature.okGotIt': 'OK, got it',
  'signature.agreeAndContinue': 'Agree and continue',
  'signature.remoteSelling.hint':
    'Note: No signature & place of signature required for Non-face to face option',
  'signature.failedToSave': 'Failed to save signature',
  'review.cert.relationship': 'Relationship',
  'review.comment': 'Comment (optional)',
  'decision.failed': 'Failed to get decision',
  viewPlan: 'View your plan',
  incomeFirst: 'FWD Income First',
  'payment.biro.paymentDetails': 'Payment details',
  'payment.biro.fullNamePayor': 'Full name payor',
  'payment.biro.initialContributionAmount': 'Initial contribution amount (RM)',
  'payment.initialPaymentDetails': 'Initial payment details',
  'payment.initialPaymentTotal': 'Initial payment total',
  'payment.contribution': 'Contribution/{{mode}}',
  'payment.adv.contribution': 'Advanced contribution',
  'payment.sale': 'Sales & Service Tax (SST)',
  'payment.stamp': 'Stamp Duty',
  'payment.policy': 'Certificate details',
  'payment.covered': 'Sum covered',
  'payment.type': 'Payment type',
  'payment.cert': 'Certificate term',
  'payment.contribution.mode': 'Contribution mode',
  'payment.contribution.term': 'Contribution term',
  'payment.cash': 'Regular cash payment',
  'payment.cash.opt1': 'Accumulated in Participant’s Saving Account (PSA)',
  'payment.cash.opt2': 'Credit to your chosen bank account',
  'payment.coverage': 'Coverage details',
  'payment.coverage.rider': 'FWD Payor rider',
  'payment.coverage.ill': 'FWD Critical Illness Rider',
  'payment.add.rider': 'FWD Add rider',
  'payment.wavier.rider': 'FWD Critical Illness Wavier Rider',
  'payment.method.select': 'Select your payment method',
  'payment.method.no': 'Application no.',
  'payment.method.cert.no': 'Certificate no.',
  'payment.method.credit': 'Credit/Debit card',
  'payment.method.credit.desc': 'You can pay with VISA or MasterCard',
  'payment.method.online': 'Online Banking (FPX)',
  'payment.method.online.desc': `To ensure you don't miss your renewal contribution, please register eMandate after paying through Online Banking (FPX)`,
  'payment.method.cheque': `Cheque`,
  'payment.method.cheque.desc': `Payment method as cheque is applicable for initial payment total amount equal or more than RM 10,000`,
  'payment.method.biro': 'Biro Angkasa',
  'payment.method.biro.desc': `Without initial contribution payment`,
  'payment.method.direct': 'Direct transfer',
  'payment.method.direct.desc': `Payment method as direct transfer is applicable for initial payment total amount more than RM 50,000`,
  'payment.tc': 'Terms and condition of credit/debit card instruction',
  gotIt: 'OK, got it',
  'payment.link.sent': 'Payment link is sent',
  'payment.cheque.fullNamePayor': 'Full name payor',
  'payment.cheque.title': 'Cheque payment',
  'payment.cheque.note':
    'All cheque need to be credit to this bank account listed below:',
  'payment.cheque.note.bank': 'Bank: {{bankName}}',
  'payment.cheque.note.accountNumber': 'Account number: {{accountNumber}}',
  'payment.cheque.note.multipleApplication': 'Cheque for multiple application',
  'payment.cheque.NricNumber': 'NRIC number',
  'payment.cheque.passportNumber': 'Passport number',
  'payment.cheque.identificationNumber': 'Identification number',
  'payment.cheque.quotationNumber': 'Quotation number',
  'payment.cheque.initialContributionAmount':
    'Initial contribution amount (RM)',
  'payment.cheque.chequeAmount': 'Cheque amount',
  'payment.cheque.chequeNumber': 'Cheque number',
  'payment.cheque.chequeDate': 'Cheque date',
  'payment.cheque.chequeIssuerBank': 'Cheque issuer bank',
  'payment.cheque.uploadImage': 'Upload cheque image',
  'payment.cheque.uploadImage.font': 'Physical cheque (Front)',
  'payment.cheque.uploadImage.back': 'Physical cheque (Back)',
  'payment.cheque.uploadImage.slip': 'Cheque bank in slip',
  'payment.cheque.error.short':
    'Cheque amount are not sufficient for initial contribution amount. Please ensure insufficient balance are paid for this application.',
  'payment.cheque.error.over':
    'Cheque amount are more than Initial Contribution Amount.',
  'payment.cheque.submitted': 'Payment request submitted.',
  'payment.cheque.done': 'Payment done! ',
  'payment.cheque.cancel.title':
    'Are you sure to select other payment methods?',
  'payment.cheque.cancel.description':
    'Cheque details will not be saved. Please select other payment methods. (Credit/Debit card or Biro Angkasa without payment)',
  'payment.cheque.submit.title':
    'Are you sure you want to submit application without eMandate? ',
  'payment.cheque.submit.description':
    'You may also register eMandate in myPortal.',
  'payment.direct.title': 'Direct transfer',
  'payment.direct.recipient_reference': 'Recipient reference number',
  'payment.direct.transfer_date': 'Transfer date',
  'payment.direct.direct_transfer_type': 'Direct transfer type',
  'payment.direct.quotationNumber': 'Quotation number',
  'payment.direct.initialContributionAmount':
    'Initial contribution amount (RM)',
  'payment.direct.transaction_amount': 'Transaction amount (RM)',
  'payment.direct.uploadImage': 'Upload direct transfer image',
  'payment.direct.uploadImage.slip': 'Direct transfer bank in slip',
  'payment.direct.submitted': 'Payment request submitted.',
  'payment.direct.done': 'Payment done! ',
  'payment.direct.cancel.title':
    'Are you sure to select other payment methods?',
  'payment.direct.cancel.description':
    'The data has not been saved. Are you certain you want to choose a different payment method?',
  'payment.failedToGenerate': 'Failed to generate payment link',
  'payment.failedToSend': 'Failed to send payment link',
  'payment.expried.title': 'Payment link generation fail!',
  'payment.expried.des':
    'Sorry, the product is no longer available. Therefore we are not able to proceed for payment.',
  'payment.submitted': 'Payment request submitted',
  'payment.submitted.desc': 'No payment yet, please check again later.',
  'payment.check': 'Check payment status',
  'payment.checking': 'Checking payment status',
  'payment.failed': 'Payment fail',
  'payment.failed.title': 'Your transaction details are given below:',
  'payment.failed.orderId': 'Order ID',
  'payment.failed.paymentId': 'Payment ID',
  'payment.failed.status': 'Transaction status',
  'payment.failed.amount': 'Transaction amount',
  'payment.status.fail': 'Fail',
  'payment.success': 'Payment successful',
  'payment.congratulation': 'Congratulations',
  'payment.status.success': 'Success',
  'payment.status.eMandateRegistration': 'eMandate registration',
  'payment.success.noted':
    'Once this application submitted, it will send to your agency leader for approval.',
  'payment.submit': 'Submit application',
  'payment.back': 'Back to payment',
  'payment.proceedToPayment': 'Proceed to payment',
  'payment.proceed': 'Proceed',
  'payment.eMandateConfirmation.title': 'Important:',
  'payment.eMandateConfirmation.content':
    'Successful eMandate registration is required to process your application after the initial contribution payment is made through Online Banking (FPX).\n\nDo you wish to continue?',
  'payment.eMandateConfirmation.continue': 'Yes, continue',
  'payment.advanceContribution.title': 'Advance contribution',
  'payment.advanceContribution.selectAdvanceContribution':
    'Please select your advance contribution period',
  'payment.advanceContribution.months': 'months',
  'payment.advanceContribution.quarters': 'quarters',
  'payment.advanceContribution.semiAnnual': 'semi-annual',
  'paymentMode.L': 'Premium Tunggal',
  'payment.submethod.inapp': 'Pay within app',
  'payment.submethod.vialink': 'Send payment link',
  'application.submit.loading': 'Waiting for deposition to (Bank) #(Ref no)...',
  'application.submit.note':
    "Your application is submitted. You may refer the underwriting decision at customer's Submitted applications page.",
  'application.submit.hint':
    'You can find the summary of your submitted application in your mailbox.',
  'application.submit.back': 'Back To Home',
  'application.submit.certificateNumber': 'Certificate number',
  'application.submit.dateSubmitted': 'Date submitted',
  'application.submit.again': 'Submit again',
  'application.submit.failed':
    "We’re really sorry to be holding you up today!\nThere seems to be a temporary glitch on our end. Don't worry, try again in a few minutes and everything should be working smoothly.",
  'uwme.failed':
    'We’re really sorry to be holding you up today!\nThere seems to be a temporary glitch on our end. Give it another try in a moment, and things should be back to normal',
  'uwme.standard.title': 'Good news, you’re almost there!',
  'uwme.standard.desc': `Your takaful application has been approved. Thanks for taking the time to go through these important details.\n\nNow let’s continue the journey to get you covered!`,
  'uwme.refer.title': 'Thank you!',
  'uwme.refer.desc': `Thanks for going through these important details.\n\nWe need a little more time to consider your application. We aim to get back to your financial advisor within 2 working days.\nThank you for your patience.`,
  'uwme.decision.others.stayTuned': 'Stay tuned!\nWe’ll come back to you.',
  'uwme.decision.others.title': 'Thanks for your application!',
  'uwme.decision.standard.approved': 'Your application is approved!',
  'uwme.decision.standard.title': 'Good news!',
  'uwme.decision.rejectGIO.title': 'Thanks for applying',
  'uwme.decision.rejectGIO.apology':
    'Thank you for your interest in our Takaful product.',
  'uwme.decision.rejectGIO.reason':
    'After careful consideration, we regret to inform you that we are unable to approve your application because you do not meet the requirements of this plan.\n\nWhile this may be disappointing news, we hope you can consider our other products, or reapply in the future when your circumstances change.\n\nThank you for your continued support.',
  'uwme.decision.quitApplication': 'OK, quit application',
  'uwme.decision.rejectGIO.cantContinue':
    "We couldn't provide Takaful coverage on this application. This application cannot be processed.",
  'renewal.payment.renewalPayment': 'Renewal payment',
  'renewal.payment.renewalContribution': 'Renewal Contribution',
  'renewal.payment.monthly': 'Monthly',
  'renewal.payment.newApplication': 'New Application',
  'renewal.payment.failed.header':
    'Your eMandate registration is not successful ',
  'renewal.payment.failed.subHeader':
    'Your bank account details has not been registered for the direct debit instruction',
  'renewal.payment.failed.title': 'Transaction details:',
  'renewal.payment.failed.transactionDate': 'Transaction date',
  'renewal.payment.failed.applicationType': 'Application type',
  'renewal.payment.failed.transactionAmount': 'Transaction amount',
  'renewal.payment.failed.transactionRef': 'Payment ref number',
  'renewal.payment.failed.debitAmount': 'Direct Debit amount',
  'renewal.payment.failed.transactionID': 'FPX transaction ID',
  'renewal.payment.failed.bankName': 'Bank name',
  'renewal.payment.failed.status': 'Transaction status',
  'renewal.payment.failed.registration': 'eMandate registration',
  'renewal.payment.failed.submit': 'Submit application',
  'eMandate.inProgress': 'Your eMandate registration is in progress...',
  'eMandate.checkStatus': 'Check registration status',
  'eMandate.registration': 'eMandate re-registration',
  'eMandate.checkingStatus': 'Checking registration status',
  'eMandate.failed.desc': 'Your eMandate registration is not successful ',
  'eMandate.failed.title':
    'Your bank account details has not been registered for the direct debit instruction',
  'eMandate.failed.detail': 'Transaction details:',
  'eMandate.failed.date': 'Transaction date:',
  'eMandate.failed.type': 'Application type',
  'eMandate.failed.number': 'Transaction amount',
  'eMandate.failed.refNumber': 'Payment ref number',
  'eMandate.failed.debitAmount': 'Direct Debit amount',
  'eMandate.failed.fpxID': 'FPX transaction ID',
  'eMandate.failed.bankName': 'Bank name',
  'eMandate.failed.status': 'Transaction status',
  'eMandate.success.desc': 'Thank you for your eMandate registration',
  'eMandate.success.title':
    'Your bank account details has been registered for the direct debit instruction',
  'eMandate.offline': 'Offline',
  'application.electronicCopy': 'Electronic copy',
  'application.email': 'Email',
  'application.mobile': 'Mobile',
  'application.english': 'English',
  'application.bahasaMalaysia': 'Bahasa',
  'directCredit.form.debitAmountError':
    'Please enter value higher or equal with the contribution amount of RM {{lowerBound}}',
  healthQuestionCO: 'Health questions - certificate owner',
  noHealthQuestionCO: 'No health questions for certificate owner',
  healthQuestionPC: 'Health questions - person covered',
  noHealthQuestionPC: 'No health questions for person covered',
  noHealthQuestion: 'No health questions for {{name}}',
  healthQuestionFailed: 'Failed to get health questions. ',
  healthQuestionRetry: 'Retry',
  'healthQuestion.failedToGenerate': 'Failed to generate health question',
  'healthQuestion.failedToFinish': 'Failed to finish health question',
  'healthQuestion.height': 'Height',
  'healthQuestion.cm': 'cm',
  'healthQuestion.weight': 'Weight',
  'healthQuestion.kg': 'kg',
  'healthQuestion.index.a': 'a. {{text}}',
  'healthQuestion.index.b': 'b. {{text}}',
  'healthQuestion.incomplete.title': 'Incomplete field in health questions',
  'healthQuestion.incomplete.description':
    'There’s a missing field in your health questions, please go back to complete the form.',
  'healthQuestion.incomplete.action': 'Go back',
  shortYearOld: 'y.o.',
  'personal.data.protection.notice': 'Personal Data Protection Notice',
  'scroll.to.bottom': 'Please scroll to the bottom',
  'submission.waitForApproval':
    'Application will be sent to your agency leader for approval.',
  'submission.failed': 'Submission failed',
  'validation.error.maxLength12': 'Maximum length is 12',
  'companyInfo.company': 'Company',
  'companyInfo.companyInfo': 'Company info',
  'companyInfo.companyInfo.disclosure': 'Importance of truthful disclosure: ',
  'companyInfo.companyInfo.disclosure.content':
    "It's important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy.",
  'companyInfo.companyDetails': 'Company details',
  'companyInfo.companyName': 'Company name',
  'companyInfo.registrationDate': 'Date of registration',
  'companyInfo.regNumber': 'Registration number (Latest)',
  'companyInfo.regNumberOld': 'Registration number (Old) (optional)',
  'companyInfo.natureOfBusiness': 'Nature of business',
  'companyInfo.contactDetails': 'Contact details',
  'companyInfo.businessPhoneNumber': 'Business phone number',
  'companyInfo.email': 'Email',
  'companyInfo.preferredContact': 'Preferred contact mode',
  'companyInfo.preferredDocument': 'Preferred document language',
  'companyInfo.businessRegistration': 'Business registration',
  'companyInfo.addressLine1': 'Address line 1',
  'companyInfo.addressLine1.hint':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'companyInfo.addressLine2': 'Address line 2 (optional)',
  'companyInfo.addressLine2.hint': 'Street no./Street Name',
  'companyInfo.addressLine3': 'Address line 3 (optional)',
  'companyInfo.postcode': 'Postcode',
  'companyInfo.city': 'City',
  'companyInfo.state': 'State',
  'companyInfo.country': 'Country',
  'companyInfo.taxDetails': 'Tax details',
  'companyInfo.taxPurpose': 'Tax - Purpose of takaful coverage',
  'companyInfo.taxPurpose.business': 'Business',
  'companyInfo.taxPurpose.private': 'Private',
  'companyInfo.taxRegistered': 'Sales and service tax act 2018 (SST)',
  'companyInfo.taxRegistered.registered': 'Registered',
  'companyInfo.taxRegistered.nonRegistered': 'Non-Registered',
  'companyInfo.taxNumber': 'Sales and service tax registration number',
  'companyInfo.taxIdentificationNumber':
    'Malaysia Tax Identification Number (TIN)',
  'authorizedSignatoryInfo.authorizedSignatoryInfo':
    'Authorized signatory info',
  'authorizedSignatoryInfo.authorizedSignatory': 'Authorized signatory',
  'authorizedSignatoryInfo.disclosure': 'Importance of truthful disclosure: ',
  'authorizedSignatoryInfo.disclosure.content':
    "It's important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy.",
  'authorizedSignatoryInfo.personalDetails': 'Personal details',
  'authorizedSignatoryInfo.firstName': 'First name',
  'authorizedSignatoryInfo.lastName': 'Last name',
  'authorizedSignatoryInfo.fullName': 'Full name',
  'authorizedSignatoryInfo.gender': 'Gender',
  'authorizedSignatoryInfo.designation': 'Designation',
  'authorizedSignatoryInfo.title': 'Title',
  'authorizedSignatoryInfo.idType': 'ID type',
  'authorizedSignatoryInfo.idNumber': 'Identification number',
  nricHint: 'YYMMDD-PB-###G',
  'authorizedSignatoryInfo.dob': 'Date of birth',
  'authorizedSignatoryInfo.age': 'Age',
  'authorizedSignatoryInfo.nationality': 'Nationality',
  'authorizedSignatoryInfo.additionalIdType': 'Additional ID type (optional)',
  'authorizedSignatoryInfo.additionalIdNumber':
    'Additional identification number (optional)',
  'authorizedSignatoryInfo.additionalIdNumber.required':
    'Additional identification number',
  'validation.error.authorizedSignatoryNotApplicable':
    'The authorised signatory cannot be the same person as the person covered due to potential conflicts of interest',
  'validation.error.insuredNotApplicable':
    'The person covered cannot be the same person as the authorised signatory due to potential conflicts of interest',
  'validation.error.date': 'Invalid date',
  'sourceOfFund.title': 'Source of fund/ wealth details',
  'sourceOfFund.fundDetails': 'Fund details',
  'sourceOfFund.fundDetails.placeholder': 'Source of fund for contribution',
  'sourceOfFund.hint': '{{count}}/100 characters',
  'sourceOfFund.wealthDetails': 'Wealth details',
  'sourceOfFund.wealthDetails.placeholder': 'Source of wealth',
  'sourceOfFund.countryOfSourceOfWealth': 'Country of source of wealth',

  'banca.certificate.form.typeOfCustomer': 'Type of customer',
  'banca.certificate.form.staffId': 'Staff ID No.',
  'banca.other.payor.selectPayor': 'Select who is the payor',
  'banca.other.payor.note': 'Notes: ',
  'banca.other.payor.note.message':
    'The payor must be either the certificate owner or person covered.',
  'banca.other.payor.isCertificationOwner': 'The payor is certificate owner',
  'banca.other.payor.isPersionCovered': 'The payor is person covered',
  'banca.signature.declaration.title.agent': 'Declaration of Intermediary',
  'banca.signature.declaration.title.agent.bahasa': 'Pengakuan oleh Perantara',
  'banca.signature.title.certificateOwner':
    "{{name}}'s (Certificate owner) signature",
  'banca.signature.title.authorizedSignatory':
    "{{name}}'s (Authorized signatory) signature",
  'banca.signature.title.personCovered':
    "{{name}}'s (Person covered) signature",
  'banca.signature.title.servicingAgent':
    "{{name}}'s (Relationship Manager) signature",
  'banca.signature.title.approver': "{{name}}'s (Approver) signature",
  singlePremium: 'Single',
  'other.nominationDetails.remove.title': 'Are you sure to remove?',
  'other.nominationDetails.remove.description':
    'Filled information under the existing policy will be removed.',
  'payment.paymentMethodSelectionWarning':
    'The payment has been completed and no other payment method can be selected',
  'payment.resendPaymentLink.or': 'OR',
  'payment.resendPaymentLink.title': 'Want to resend payment link?',
  'payment.resendPaymentLink.scanToPay': 'Scan to pay',
  'payment.resendEMandate.title': 'Want to resend eMandate registration link?',
  'payment.resendEMandate.scanToPay': 'Scan to register eMandate',
  'payment.resendPaymentLink.whatsapp': 'Whatsapp',
  'payment.resendPaymentLink.whatsapp.error': 'Failed to share via whatsapp',
  'payment.resendPaymentLink.email': 'Email',
  'payment.resendPaymentLink.email.error':
    'Failed to share payment link via email',
  'payment.resendPaymentLink.copyLink': 'Copy link',
  'payment.resendPaymentLink.copyLink.success': 'Link is copied',
  'payment.resendPaymentLink.copyLink.error': 'Failed to copy payment link',
  'payment.resendPaymentLinkEmandate.title':
    'Want to resend eMandate registration link?',
  'payment.resendPaymentLinkEmandate.scanToRegister.tablet':
    'Scan to register eMandate',
  'ocr.verified': 'Verified',
  'advanceContribution.changeTitle': 'Update payment link',
  'advanceContribution.changeDescription':
    'The advance contribution is updated, please generate a new payment link and proceed with payment.',
};
