import { InferType, array, date, number, object, string } from 'yup';
import { requiredMessage } from './eAppErrorMessages';

export const chequePaymentSchema = object({
  fullNamePayor: string().required(requiredMessage),
  applicationNumber: string().required(requiredMessage),
  idNumber: string().required(requiredMessage),
  quotationNumber: string().required(requiredMessage),
  initialContributionAmount: string().required(requiredMessage),
  chequeAmount: string().required(requiredMessage),
  isForMultipleApplication: string(),
  chequeNumber: string().required(requiredMessage),
  chequeDate: date()
    .nullable()
    .test({
      name: 'required-date',
      test: value => value instanceof Date,
      message: requiredMessage,
    }),
  chequeIssuerBank: string().required(requiredMessage),
  documents: array().of(
    object({
      type: string().required(),
      files: array().of(
        object({
          uri: string().required(),
          size: number().defined(),
          base64: string().defined(),
          documentName: string().required(),
          status: string(),
        }),
      ),
    }),
  ),
});

export type ChequePaymentForm = InferType<typeof chequePaymentSchema>;

export const chequePaymentDefaultValue: ChequePaymentForm = {
  fullNamePayor: '',
  applicationNumber: '',
  idNumber: '',
  quotationNumber: '',
  initialContributionAmount: '',
  chequeAmount: '',
  chequeNumber: '',
  isForMultipleApplication: '',
  chequeDate: null,
  chequeIssuerBank: '',
  documents: [],
};
