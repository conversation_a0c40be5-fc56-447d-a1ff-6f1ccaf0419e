import { InferType, date, object, ref, string } from 'yup';
import { invalidFormatMessage, requiredMessage } from './eAppErrorMessages';
import {
  MY_COUNTRY,
  MY_ELECTRONIC_COPY,
  NEW_ADDRESS_OPTION,
} from 'constants/optionList';
import { mysAddressSchema } from './eAppCommonSchema';
import { build } from 'utils/context';
import { IB_TAX_IDENTIFICATION_VALIDATION, TAX_NUMBER_VALIDATION } from 'constants/regex';

export const companyAddressInformationSchema = object({
  ...mysAddressSchema(['correspondence']),
});

export const companyInfoValidationSchema = object({
  companyName: string().required(requiredMessage),
  regNumber: string()
    .required(requiredMessage)
    .length(12, invalidFormatMessage)
    .test({
      name: 'valid-reg-number',
      message: invalidFormatMessage,
      test: (value, ctx) => {
        if (!/^[0-9]+$/.test(value)) return false;
        const regDate = ctx.resolve(ref('registrationDate')) as Date | null;
        if (regDate) {
          return value.startsWith(String(regDate.getFullYear()));
        }
        return true;
      },
    }),
  registrationDate: date()
    .nullable()
    .test({
      name: 'required-date',
      test: value => value instanceof Date,
      message: requiredMessage,
    }),
  regNumberOld: string(),
  natureOfBusiness: string().required(requiredMessage),
  businessCountryCode: string().required(requiredMessage),
  businessPhoneNumber: string().required(requiredMessage),
  email: string().required(requiredMessage),
  preferredCertificateCopy: string().required(requiredMessage),
  preferredContact: string().required(requiredMessage),
  preferredDocument: string().required(requiredMessage),
  ...mysAddressSchema(['correspondence']),
  taxPurpose: string().required(requiredMessage),
  taxRegistered: string().required(requiredMessage),
  taxNumber: string().when('taxRegistered', {
    is: 'YES',
    then: schema =>
      schema
        .required(requiredMessage)
        .matches(TAX_NUMBER_VALIDATION, {
          message: invalidFormatMessage,
          excludeEmptyString: true,
        }),
    otherwise: schema => schema.optional(),
  }),
  taxIdentificationNumber: ['prd', 'stg'].includes(build)
    ? string()
    : string()
        .required(requiredMessage)
        .matches(IB_TAX_IDENTIFICATION_VALIDATION, {
          excludeEmptyString: true,
          message: invalidFormatMessage,
        }),
});

export type CompanyInfoForm = InferType<typeof companyInfoValidationSchema>;

export const initialCompanyInfo: CompanyInfoForm = {
  companyName: '',
  regNumber: '',
  registrationDate: null,
  regNumberOld: '',
  natureOfBusiness: '',
  businessCountryCode: '',
  businessPhoneNumber: '',
  email: '',
  preferredCertificateCopy: MY_ELECTRONIC_COPY,
  preferredContact: '',
  preferredDocument: '',
  correspondenceAddress: NEW_ADDRESS_OPTION,
  correspondenceAddressLine1: '',
  correspondenceAddressLine2: '',
  correspondenceAddressLine3: '',
  correspondencePostCode: '',
  correspondenceCity: '',
  correspondenceState: '',
  correspondenceCountry: MY_COUNTRY,
  taxPurpose: '',
  taxNumber: '',
  taxRegistered: '',
  taxIdentificationNumber: '',
};
