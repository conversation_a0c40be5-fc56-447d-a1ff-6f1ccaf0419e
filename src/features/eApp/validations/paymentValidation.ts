import * as yup from 'yup';
import { requiredMessage } from './eAppErrorMessages';
import { PaymentStatus } from 'features/eApp/types/paymentTypes';

export const paymentSchema = yup.object({
  paymentMethod: yup
    .string()
    .oneOf(['dragonPayOnlineBanking', 'dragonPayCards', 'offline', ''])
    .required(requiredMessage),
  paymentStatus: yup
    .string()
    .oneOf([
      PaymentStatus.RECEIVE_SUCCEED,
      PaymentStatus.RECEIVE_PENDING,
      PaymentStatus.RECEIVE_UNKNOWN,
      PaymentStatus.RECEIVE_FAILED,
      PaymentStatus.EMPTY,
      PaymentStatus.PENDING,
    ])
    .required(requiredMessage),
  merchantId: yup.string(),
  refNo: yup.string(),
});

export type PaymentForm = yup.InferType<typeof paymentSchema>;

export const paymentDefaultValue: PaymentForm = {
  paymentMethod: '',
  paymentStatus: PaymentStatus.PENDING,
  merchantId: '',
  refNo: '',
};

export const paymentOfflineSchema = yup.object({
  paymentOfflineMethod: yup
    .string()
    .oneOf([
      'otcBillsPayment',
      'gcash',
      'onlineBanking',
      'creditCardOption',
      'fwdPosMobile',
      'paymaya',
      '',
    ])
    .required(requiredMessage),
  paymentOfflineDetailMethod: yup
    .string()
    .oneOf([
      'otcBillsPayment.securityBank',
      'otcBillsPayment.metroBank',
      'otcBillsPayment.bdo',
      'otcBillsPayment.bpi',
      'otcBillsPayment.lbc',
      'otcBillsPayment.unionBank',
      'otcBillsPayment.cebuanaLhuillierBranches',
      'onlineBanking.securityBank',
      'onlineBanking.bdo',
      'onlineBanking.bpi',
      'onlineBanking.metroBankDirect',
      'onlineBanking.bancNetOnline',
      'onlineBanking.landBank',
      '',
    ])
    .when('paymentOfflineMethod', {
      is: 'otcBillsPayment',
      then: schema =>
        schema
          .notOneOf([
            'onlineBanking.securityBank',
            'onlineBanking.bdo',
            'onlineBanking.bpi',
            'onlineBanking.metroBankDirect',
            'onlineBanking.bancNetOnline',
            'onlineBanking.landBank',
          ])
          .required(requiredMessage),
    })
    .when('paymentOfflineMethod', {
      is: 'onlineBanking',
      then: schema =>
        schema
          .notOneOf([
            'otcBillsPayment.securityBank',
            'otcBillsPayment.metroBank',
            'otcBillsPayment.bdo',
            'otcBillsPayment.bpi',
            'otcBillsPayment.lbc',
            'otcBillsPayment.unionBank',
            'otcBillsPayment.cebuanaLhuillierBranches',
          ])
          .required(requiredMessage),
    }),
});

export type PaymentOfflineForm = yup.InferType<typeof paymentOfflineSchema>;

export const paymentOfflineDefaultValue: PaymentOfflineForm = {
  paymentOfflineMethod: '',
  paymentOfflineDetailMethod: '',
};
