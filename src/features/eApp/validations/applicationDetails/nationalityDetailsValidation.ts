import * as yup from 'yup';
import { invalidFormatMessage, requiredMessage } from '../eAppErrorMessages';
import { PH_COUNTRY } from 'constants/optionList';

const placeOfBirthRegex = /^\s*[a-zA-Z][a-zA-Z\s]*$/;

export const nationalityDetailsSchema = yup.object({
  nationality: yup.string().required(requiredMessage),
  countryOfBirth: yup.string().required(requiredMessage),
  placeOfBirth: yup
    .string()
    .required(requiredMessage)
    .when('countryOfBirth', {
      is: PH_COUNTRY, 
      then: schema => schema,
      otherwise: schema =>
        schema.matches(placeOfBirthRegex, {
          excludeEmptyString: true,
          message: invalidFormatMessage,
        }),
    }),
});

export type NationalityDetailsForm = yup.InferType<
  typeof nationalityDetailsSchema
>;

export const nationalityDetailsDefaultValue: NationalityDetailsForm = {
  nationality: '',
  countryOfBirth: '',
  placeOfBirth: '',
};
