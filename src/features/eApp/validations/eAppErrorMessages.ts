import { TFun<PERSON><PERSON><PERSON> } from 'i18next';

export const requiredMessage: TFunc<PERSON>ey<['eApp']> =
  'eApp:validation.error.required';
export const emptySpace: TFunc<PERSON>ey<['eApp']> =
  'eApp:validation.error.emptySpace';
export const invalidFormatMessage: TFunc<PERSON>ey<['eApp']> =
  'eApp:validation.error.invalidFormat';
export const identificationNumberNotMatchMessage: TFuncKey<['eApp']> =
  'eApp:validation.error.NRICNotMatch';
export const maxLength60Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength60';
export const maxLength14Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength14';
export const maxLength12Message: TFunc<PERSON>ey<['eApp']> =
  'eApp:validation.error.maxLength12';
export const maxLength13Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength13';
export const maxLength10Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength10';
export const maxLength16Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength16';
export const maxLength20Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength20';
export const maxLength30Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength30';
export const maxLength50Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength50';
export const maxLength120Message: TFuncKey<['eApp']> =
  'eApp:validation.error.maxLength120';
export const minLength5Message: TFuncKey<['eApp']> =
  'eApp:validation.error.minLength5';
export const incorrectGenderMessage: TFuncKey<['eApp']> =
  'eApp:validation.error.invalidGender';
export const invalidDateMessage: TFuncKey<['eApp']> =
  'eApp:validation.error.date';
export const invalidDateMessageExpired: TFuncKey<['eApp']> =
  'eApp:validation.error.dateExpired';
export const invalidCreditCardFormat: TFuncKey<['eApp']> =
  'eApp:validation.error.invalidCardFormat';
export const specialCharacterNotAllowed: TFuncKey<['eApp']> =
  'eApp:validation.error.specialCharacterNotAllowed';
export const errorInFormat: TFuncKey<['eApp']> = 'eApp:validation.error.format';
export const nominationNotApplicable: TFuncKey<['eApp']> =
  'eApp:other.nominationDetails.nominationNotApplicable';
export const nominationDuplicated: TFuncKey<['eApp']> =
  'eApp:other.nominationDetails.nominationDuplicated';
export const minimumAllocation: TFuncKey<['eApp']> =
  'eApp:other.nominationDetails.minimumAllocation';
export const invalidAnnualIncomeAmount: TFuncKey<['eApp']> =
  'eApp:validation.error.invalidAnnualIncomeAmount';
export const authorizedSignatoryNotApplicable: TFuncKey<['eApp']> =
  'eApp:validation.error.authorizedSignatoryNotApplicable';
export const insuredNotApplicable: TFuncKey<['eApp']> =
  'eApp:validation.error.insuredNotApplicable';
