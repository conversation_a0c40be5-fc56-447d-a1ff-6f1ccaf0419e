import { useNavigation } from '@react-navigation/native';
import { useEffect, useRef } from 'react';
import * as ScreenOrientation from 'expo-screen-orientation';

const useStartLandscape = () => {
  const replaceRef = useRef(false);
  const { addListener } = useNavigation();
  useEffect(() => {
    const beforeRemoveSub = addListener('beforeRemove', async () => {
      if (!replaceRef.current) {
        await ScreenOrientation.unlockAsync();
        await ScreenOrientation.lockAsync(
          ScreenOrientation.OrientationLock.PORTRAIT_UP,
        );
      }
    });
    return () => {
      beforeRemoveSub();
    };
  }, [addListener]);

  return replaceRef;
};

export default useStartLandscape;
