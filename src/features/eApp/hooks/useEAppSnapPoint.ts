import { useTheme } from '@emotion/react';
import { useMemo } from 'react';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

export const useEAppSnapPoints = (withContentHeight?: boolean) => {
  const { sizes, space } = useTheme();
  const { height } = useSafeAreaFrame();
  const { top } = useSafeAreaInsets();
  const snapPoints = useMemo(
    () =>
      withContentHeight
        ? ['CONTENT_HEIGHT', height - top - sizes[23], height - space[22]]
        : [height - top - sizes[23], height - space[22]],
    [withContentHeight, height, top, sizes, space],
  );
  return snapPoints;
};
