import { useTranslation } from 'react-i18next';
import { RocQuestionProps } from '../components/tablet/reviewSummary/declaration/RocReview';
import { RocForm } from '../validations/applicationDetails/declarationValidation';

export const useReviewRocMapper = (info: RocForm) => {
  const { t } = useTranslation(['eApp']);
  const question2: RocQuestionProps = {
    index: 2,
    question: t('eApp:declaration.roc.question.2'),
    answer: info.terminateInfluence === 'yes' ? t('eApp:yes') : t('eApp:no'),
  };

  if (info.terminateInfluence === 'yes') {
    question2.subQuestion = t('eApp:declaration.roc.question.2a');
    question2.subAnswer =
      info.terminateInfluence === 'yes'
        ? info.satisfiedWithExplanation === 'yes'
          ? t('eApp:yes')
          : t('eApp:no')
        : undefined;
    question2.info = [
      {
        fieldName: t('eApp:review.comment'),
        fieldValue: info.comment,
      },
    ];
  }

  return [
    {
      index: 1,
      question: t('eApp:declaration.roc.question.1'),
      answer: info.hasReplacePlan === 'yes' ? t('eApp:yes') : t('eApp:no'),
      info: info.hasReplacePlan === 'yes' && [
        {
          fieldName: t('eApp:declaration.roc.replacementReason'),
          fieldValue: info.replacementReason,
          secondFieldName: t('eApp:declaration.roc.takafulOperator'),
          secondFieldValue: info.takafulOperator,
        },
        {
          fieldName: t('eApp:declaration.roc.planName'),
          fieldValue: info.planName,
          secondFieldName: t('eApp:declaration.roc.sumCovered'),
          secondFieldValue: info.sumCovered,
        },
      ],
    },
    question2,
    {
      index: 3,
      question: t('eApp:declaration.roc.question.3'),
      answer: info.isCoverageExtension === 'yes' ? t('eApp:yes') : t('eApp:no'),
    },
  ] as RocQuestionProps[];
};
