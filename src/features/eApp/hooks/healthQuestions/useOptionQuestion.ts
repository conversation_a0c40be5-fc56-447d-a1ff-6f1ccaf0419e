import {
  OptionBackedHealthQuestion,
  OptionGroupHealthQuestion,
  OptionListHealthQuestion,
  QuestionOptionsList,
  QuestionType,
} from 'types/healthQuestion';
import { useController } from 'react-hook-form';
import useQuestionOptionList from './useQuestionOptionList';

type Props = {
  question:
    | OptionGroupHealthQuestion
    | OptionListHealthQuestion
    | OptionBackedHealthQuestion;
};

type UseOptionQuestion = {
  answers: string[];
  optionsList: QuestionOptionsList | undefined;
  isOptionSelected: (option: string) => boolean;
  onSelect: (option: string) => void;
  onDeselect: (option: string) => void;
  onSelectMultiOptions: (options: string[]) => void;
};

const useOptionQuestion = ({ question }: Props): UseOptionQuestion => {
  const { definition } = question;
  const { isMultiValued } = definition;
  const { field } = useController({ name: question.path });
  const answers: string[] = field.value || [];
  const { optionsList } = useQuestionOptionList({ question });

  const isOptionSelected = (option: string) => answers.indexOf(option) !== -1;

  const onSelect = (option: string) => {
    let newAnswers: string[] = [];
    if (isMultiValued) {
      const isExclusiveAnswer =
        question.definition?.optionTags?.[option]?.[0] === 'EXCLUSIVE';
      const exclusiveAnswers = answers.filter(
        a => question.definition?.optionTags?.[a]?.[0] === 'EXCLUSIVE',
      );
      if (isExclusiveAnswer) {
        newAnswers = [option];
      } else if (exclusiveAnswers.length > 0) {
        newAnswers = [
          ...answers.filter(a => !exclusiveAnswers.includes(a)),
          option,
        ];
      } else {
        newAnswers = [...answers, option];
      }
    } else {
      newAnswers = [option];
    }
    if ((question?.definition?.type as QuestionType) === 'OPTION_GROUP') {
      newAnswers = newAnswers.filter(answer =>
        (question?.definition?.options ?? []).includes(answer),
      );
    }
    field.onChange(newAnswers);
  };

  const onDeselect = (option: string) => {
    let newAnswers = answers.filter(ans => ans !== option);
    if ((question?.definition?.type as QuestionType) === 'OPTION_GROUP') {
      newAnswers = newAnswers.filter(answer =>
        (question?.definition?.options ?? []).includes(answer),
      );
    }
    field.onChange(newAnswers);
  };

  const onSelectMultiOptions = (options: string[]) => {
    field.onChange(options);
  };

  return {
    answers,
    optionsList,
    isOptionSelected,
    onSelect,
    onDeselect,
    onSelectMultiOptions,
  };
};

export default useOptionQuestion;
