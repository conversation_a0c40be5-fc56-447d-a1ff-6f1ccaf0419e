import { HealthQuestion } from 'types/healthQuestion';
import { RPQResult } from 'types/quotation';
import { EAppState } from 'features/eApp/utils/store/eAppStore';

export interface InfoGroup {
  title?: string;
  items: Array<{
    label: string;
    text: string;
  }>;
}

export interface InfoQuestion {
  title: string;
  items: Array<{
    label: string;
    text: string;
  }>;
  indexVisible?: boolean;
  healthQuestion?: HealthQuestion;
}

export type InfoItemType = InfoGroup | InfoQuestion | undefined;

export type InfoSectionData = {
  title: string;
  type: string;
  data: Array<InfoItemType>;
};

export interface DataPrivacyReviewParams {
  acceptDataPrivacy: boolean;
  acceptInform: boolean;
}

export interface RiskProfileReviewParams {
  data: {
    rpqResult: RPQResult | undefined;
  };
}

export interface HealthQuestionsReviewParams {
  title: string;
  enquiryId: string;
  partyId?: string;
}

export interface PersonalInformationReviewParams {
  title: string;
  sections: {
    title: string;
    icon: JSX.Element;
    perRow?: boolean;
    data: {
      label: string;
      value: string;
    }[];
  }[];
}

export interface IReviewInfo {
  text?: string;
}

export interface IReviewCardInfo {
  method: string;
  holderName: string;
  company: string;
  number: string;
  expiryDate: string;
  issuedBySecurityBank: string;
}

export interface IEntityLeadExtra {
  alts_blts_ref_num: string;
  bank_customer_id: string;
  referrer_code: string;
  service_branch: string;
}

export interface IReviewAppInfo {
  productName: string;
  sumAssured: number;
  totalPremium: number;
  perPeriod: string;
  currency: string;
  paymentPeriod: string;
  paymentType: string;
  paymentMode: string;
  applicationDetails: {
    applicationNumber: string;
    policyOwner: string;
    email: string;
    dateOfApplication: string;
  };
  referralDetails: {
    leadStatus: string;
    campaignCode: string;
    affiliateCode: string;
  };
  entityReferralDetails: IEntityLeadExtra;
}

export interface PolicyReplacementReviewParams {
  consentsAnswers: EAppState['consentsAnswers'];
  existingPolicyData: EAppState['existingPolicyData'];
}

export type PersonalInfoReviewParams = {
  title?: string;
} & (
  | {
      data: EAppState['policyOwnerPersonalInfo'];
      type: 'policyOwner';
    }
  | {
      data: EAppState['insuredPersonalInfo'];
      type: 'insured';
    }
  | {
      data: EAppState['beneficialOwnerPersonalInfo'];
      type: 'beneficialOwner';
    }
  | {
      data: EAppState['payorPersonalInfo'];
      type: 'payor';
    }
  | {
      data: EAppState['beneficiariesPersonalInfo'][0];
      type: 'beneficiary';
      percentage: number;
    }
);

export type ReviewDocumentImageSectionType = {
  type: 'front' | 'back' | 'bank' | 'certOnBO' | 'irswForm';
  images: ReviewDocumentImageType[];
  documentType: string;
};

export type ReviewDocumentImageType = {
  name: string;
  uri: string;
  base64: string;
};

export interface ImageListParams {
  data: ReviewDocumentImageType[];
  index: number;
}

export interface FatcaReviewRouteParams {
  partyId?: string;
  title?: string;
}
