import React, { useCallback, useEffect, useState } from 'react';
import { NumberHealthQuestion } from 'types/healthQuestion';
import { Box, TextField, Column, Row, LargeBody } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import QuestionBox from './QuestionBox';
import useNumberQuestion from 'features/eApp/hooks/healthQuestions/useNumberQuestion';
import {
  kgToLbs,
  lbsToKg,
} from 'features/eApp/utils/healthQuestionCalculationUtils';
import QuestionTitle from '../QuestionTitle';
import { country } from 'utils/context';
import useQuestionError from 'features/eApp/hooks/healthQuestions/useQuestionError';

type Props = {
  question: NumberHealthQuestion;
};

const WeightQuestion = ({ question }: Props) => {
  const { space, colors } = useTheme();
  const { definition } = question;
  const { text: questionTitle, type, helpText } = definition;
  const { text, onChange, onBlur } = useNumberQuestion({
    question,
  });
  const error = useQuestionError({ question });
  const [kg, setKg] = useState('');
  const [lbs, setLbs] = useState('');
  useEffect(() => {
    setKg(text ?? '0');
    setLbs(String(kgToLbs(Number(text ?? '0')).lbs));
  }, [text]);

  const onKgChanges = useCallback((kg: string) => {
    const numericKg = Number(kg);
    if (isNaN(numericKg)) return;
    const { lbs } = kgToLbs(numericKg);
    setLbs(String(lbs));
    setKg(String(numericKg));
  }, []);

  const onLbsChanges = useCallback((lbs: string) => {
    const numericLbs = Number(lbs);
    if (isNaN(numericLbs)) return;
    const { kg } = lbsToKg(numericLbs);
    setKg(String(kg));
    setLbs(String(numericLbs));
  }, []);

  return (
    <Column>
      <QuestionBox>
        <QuestionTitle title={questionTitle} helpText={helpText} />
        <Box h={space[5]} />
        <Row flex={1}>
          <TextField
            value={kg}
            onChange={onKgChanges}
            placeholder="Weight"
            onBlur={() => {
              onBlur();
              onChange(kg);
            }}
            right={
              <LargeBody color={colors.palette.fwdDarkGreen[50]}>kg</LargeBody>
            }
            keyboardType={type === 'INTEGER' ? 'number-pad' : 'decimal-pad'}
            style={{ flex: 1 }}
            error={error}
          />
          {country === 'ph' ? (
            <TextField
              style={{ flex: 1, marginLeft: space[4] }}
              value={lbs}
              placeholder="Weight"
              onChange={onLbsChanges}
              onBlur={() => {
                onBlur();
                onChange(kg);
              }}
              right={
                <LargeBody color={colors.palette.fwdDarkGreen[50]}>
                  lbs
                </LargeBody>
              }
              keyboardType={type === 'INTEGER' ? 'number-pad' : 'decimal-pad'}
            />
          ) : (
            <></>
          )}
        </Row>
      </QuestionBox>
    </Column>
  );
};

export default WeightQuestion;
