import React, { memo } from 'react';
import {
  DateHealthQuestion,
  HealthQuestion,
  NumberHealthQuestion,
  OptionBackedHealthQuestion,
  OptionGroupHealthQuestion,
  OptionListHealthQuestion,
  QuestionType,
  StringHealthQuestion,
} from 'types/healthQuestion';
import StringQuestion from './StringQuestion';
import NumberQuestion from './NumberQuestion';
import DateQuestion from './DateQuestion';
import OptionGroupQuestion from './OptionGroupQuestion';
import OptionListQuestion from './OptionListQuestion';
import OptionBackedQuestion from './OptionBackedQuestion';
import HeightQuestion from './HeightQuestion';
import WeightQuestion from './WeightQuestion';
import { htmlToRawText } from 'features/eApp/utils/stringUtils';

type Props = {
  question: HealthQuestion;
};

const Question = memo(({ question }: Props) => {
  const type = question.definition.type as QuestionType;
  const name = question.definition.name;
  // TODO: temp fix for omitting html tags, need to use MarkdownText later
  question.definition.text = htmlToRawText(question.definition.text);
  question.definition.helpText = htmlToRawText(question.definition.helpText);

  switch (type) {
    case 'STRING':
      return <StringQuestion question={question as StringHealthQuestion} />;
    case 'INTEGER':
    case 'NUMBER':
      if (name === 'HEIGHT') {
        return <HeightQuestion question={question as NumberHealthQuestion} />;
      } else if (name.includes('WEIGHT')) {
        return <WeightQuestion question={question as NumberHealthQuestion} />;
      }
      return <NumberQuestion question={question as NumberHealthQuestion} />;
    case 'PAST_DATE':
    case 'DATE':
    case 'FUTURE_DATE':
      return <DateQuestion question={question as DateHealthQuestion} />;
    case 'OPTION_GROUP':
      return (
        <OptionGroupQuestion question={question as OptionGroupHealthQuestion} />
      );
    case 'OPTION_LIST':
      return (
        <OptionListQuestion question={question as OptionListHealthQuestion} />
      );
    case 'OPTION_BACKED':
      return (
        <OptionBackedQuestion
          question={question as OptionBackedHealthQuestion}
        />
      );
    default:
      return null;
  }
});

export default Question;
