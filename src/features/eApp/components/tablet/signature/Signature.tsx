import styled from '@emotion/native';
import {
  PropsWithChildren,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Button,
  Icon,
  Typography,
  TextField,
  Picker,
  TextFieldRef,
  LargeLabel,
  Box,
  LargeBody,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import useToggle from 'hooks/useToggle';
import {
  SafeAreaView,
  SafeAreaViewProps,
  useSafeAreaFrame,
} from 'react-native-safe-area-context';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ViewStyle,
  Modal,
  StyleSheet,
} from 'react-native';
import DrawPad, { isDot, isLine } from './components/DrawPad';
import SignatureTermAndConditionModal from './components/SignatureTermAndConditionModal';
import Dots from './components/Dots';
import { useTranslation } from 'react-i18next';
import { formatSignatureDate } from 'utils/helper/formatUtil';
import TermText from './components/TermText';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import {
  useGetAgentProfile,
  useGetAgentProfileManually,
} from 'hooks/useGetAgentProfile';
import { AgentProfile } from 'types';
import SignatureSVG from 'features/eApp/assets/SignatureSVG';
import {
  BANCA_SIGNATURE_AGENT_TERMS_AND_CONDITIONS,
  PRODUCT_CODE_TO_TERMS,
  SIGNATURE_AGENT_TERMS_AND_CONDITIONS,
} from 'features/eApp/constants/signatureTermsAndConditions';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useUpdateSignature } from 'features/eApp/hooks/useUpdateSignature';
import { useGetOptionList } from 'hooks/useGetOptionList';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { calculateAge } from 'utils/helper/calculateAge';
import { useLanguageOptions } from 'hooks/useLanguageOptions';
import PromptProvider from 'components/prompt/PromptContext';
import { useAlert } from 'hooks/useAlert';
import { PortalProvider } from '@gorhom/portal';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useCheckEntity } from 'features/eApp/hooks/useCheckEntity';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CHANNELS } from 'types/channel';
import { useCheckBancaRookie } from 'hooks/useCheckRookieAgent';

const Container = styled(SafeAreaView)<SafeAreaViewProps & { height: number }>(
  ({ theme, height }) => ({
    flex: 1,
    paddingTop: 5,
    marginTop: theme.space[17],
    height: height - theme.space[17],
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
  }),
);

const HeaderContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  marginBottom: theme.space[4],
}));

const SignatureIcon = styled.View(({ theme }) => ({
  marginRight: theme.space[6],
  marginTop: theme.space[4],
}));

const Header = styled.View(({ theme }) => ({
  height: 46,
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  marginLeft: -theme.space[1],
  marginTop: theme.space[1],
  justifyContent: 'space-between',
}));

const HeaderTitle = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onBackground,
}));

const UnderstandAndConfirm = styled(Typography.LargeBody)(({ theme }) => ({
  flexShrink: 1,
  marginTop: theme.space[2],
}));

const NoticeContainer = styled.TouchableOpacity(({ theme }) => ({
  marginTop: theme.space[2],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
}));

const DetailText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.primary,
}));

const Footer = styled.View(({ theme }) => ({
  marginTop: theme.space[2],
  marginBottom: theme.space[6],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
  gap: theme.space[4],
}));

const Backdrop = styled(Animated.View)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'center',
  alignItems: 'center',
}));
const RemoteAgreeText = styled(Typography.H7)(({ theme }) => ({
  color: '#333333',
  marginBottom: theme.space[3],
}));

const DotContainer = styled.View(({ theme }) => ({
  justifyContent: 'center',
  marginTop: theme.space[4],
  marginBottom: theme.space[6],
}));

const Content = styled.View(({ theme }) => {
  const { width } = useSafeAreaFrame();
  return {
    backgroundColor: theme.colors.background,
    flex: 1,
    paddingHorizontal: theme.space[10],
    paddingTop: theme.space[4],
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    width,
  };
});

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  flex: 1,
}));

const PlaceAndDateContainer = styled.View(({ theme }) => ({
  height: 66,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  bottom: theme.space[6],
  left: 0,
  right: 0,
}));

const NameDate = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
}));

interface Props {
  visible?: boolean;
  onCancel: () => void;
  onNext: () => void;
}

export const _Signature = memo(function Signature({ onCancel, onNext }: Props) {
  const { t } = useTranslation(['common', 'eApp']);
  const { colors, space, borderRadius } = useTheme();
  const [signatureTermVisible, showSignatureTerm, hideSignatureTerm] =
    useToggle();
  const { height: screenHeight } = useSafeAreaFrame();
  const [language, setLanguage] = useState<'en' | 'my'>('en');
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const isEntity = useCheckEntity();

  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;

  const { alertError } = useAlert();
  const {
    my_policyOwnerPersonalInfo,
    my_authorizedSignatoryInfo,
    my_employeePersonalInfo,
    my_spousePersonalInfo,
    my_hasSpouseInsured,
    my_childPersonalInfo,
    my_hasChildInsured,
  } = useEAppStore(
    state => ({
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_authorizedSignatoryInfo: state.my_authorizedSignatoryInfo,
      my_employeePersonalInfo: state.my_employeePersonalInfo,
      my_spousePersonalInfo: state.my_spousePersonalInfo,
      my_childPersonalInfo: state.my_childrenPersonalInfo,
      my_hasSpouseInsured: state.my_hasSpouseInsured,
      my_hasChildInsured: state.my_hasChildInsured,
    }),
    shallow,
  );

  const { data: agentProfile } = useGetAgentProfile();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const quotation = useSelectedQuotation();
  const isRookieAgent = useCheckBancaRookie();

  const isF2FPO = useEAppStore(state => state.my_policyOwnerPersonalInfo.isF2F);
  const isF2FAS = useEAppStore(state => state.my_authorizedSignatoryInfo.isF2F);
  const isF2FEmployee = useEAppStore(
    state => state.my_employeePersonalInfo.isF2F,
  );
  const isF2FPISpouse = useEAppStore(
    state => state.my_spousePersonalInfo.isF2F,
  );
  const isF2FPIChildren = useEAppStore(
    state =>
      state.my_childrenPersonalInfo
        .filter(child => calculateAge(child.dob || new Date()) >= 17)
        .map(child => child.isF2F),
    shallow,
  );
  const f2fList = useMemo(() => {
    const f2fList: boolean[] = [Boolean(isEntity ? isF2FAS : isF2FPO)];
    if (my_hasSpouseInsured) {
      f2fList.push(Boolean(isF2FPISpouse));
    }
    if (isEntity) {
      f2fList.push(Boolean(isF2FEmployee));
    }
    if (my_hasChildInsured) {
      f2fList.push(...(isF2FPIChildren.map(Boolean) as boolean[]));
    }
    f2fList.push(true); // for agent
    if (isRookieAgent) {
      f2fList.push(true); // for approver
    }
    return f2fList;
  }, [
    isEntity,
    isF2FAS,
    isF2FEmployee,
    isF2FPIChildren,
    isF2FPISpouse,
    isF2FPO,
    my_hasChildInsured,
    my_hasSpouseInsured,
  ]);
  const nameList = useMemo(() => {
    const nameList = [
      isEntity
        ? my_authorizedSignatoryInfo.fullName
        : my_policyOwnerPersonalInfo.fullName,
    ];
    if (isEntity) {
      nameList.push(my_employeePersonalInfo.fullName);
    }
    if (my_hasSpouseInsured) {
      nameList.push(my_spousePersonalInfo.fullName ?? '');
    }
    if (my_hasChildInsured) {
      nameList.push(
        ...my_childPersonalInfo
          .filter(child => calculateAge(child.dob || new Date()) >= 17)
          .map(child => child.fullName ?? ''),
      );
    }
    nameList.push(agentProfile?.person.fullName ?? '');
    if (isRookieAgent) {
      nameList.push(caseObj?.agent?.supervisorName ?? ''); // for approver
    }
    return nameList;
  }, [
    agentProfile?.person.fullName,
    isEntity,
    my_authorizedSignatoryInfo.fullName,
    my_childPersonalInfo,
    my_employeePersonalInfo.fullName,
    my_hasChildInsured,
    my_hasSpouseInsured,
    my_policyOwnerPersonalInfo.fullName,
    my_spousePersonalInfo.fullName,
  ]);
  const agencyIndex = isRookieAgent ? f2fList.length - 2 : f2fList.length - 1;
  const lastIndex = f2fList.length - 1;
  const isRemoteSelling = !f2fList[activeIndex];

  const [strokes, setStrokes] = useState<string[]>([]);
  const [placeOfSigning, setPlaceOfSigning] = useState<string>();
  const placeOfSigningFieldRef = useRef<TextFieldRef>(null);

  const drawPadRef = useRef<
    { capture: () => Promise<string | undefined> } | undefined
  >();

  const disabled = useMemo(() => {
    if (isRemoteSelling) return false;

    if (strokes.length < 1) {
      return true;
    }

    if (!placeOfSigning) {
      return true;
    }
    return false;
  }, [isRemoteSelling, placeOfSigning, strokes.length]);

  const signedTime = useMemo(() => {
    if (disabled) {
      return undefined;
    } else {
      return new Date().getTime();
    }
  }, [disabled]);

  const date = useMemo(
    () => formatSignatureDate(signedTime ? new Date(signedTime) : new Date()),
    [signedTime],
  );

  const { updateSignature, isLoading: isUpdatingSignature } =
    useUpdateSignature();
  const onContinue = useCallback(async () => {
    try {
      const imageBase64 = (await drawPadRef.current?.capture())?.replace(
        /(\r\n|\n|\r)/gm,
        '',
      );
      const isInsuredSigning = activeIndex !== 0 && activeIndex < agencyIndex;
      const isApproverSigning = activeIndex !== 0 && activeIndex > agencyIndex;
      if (signedTime && imageBase64) {
        const insuredIndex = activeIndex;
        await updateSignature({
          signedTime,
          imageBase64,
          party:
            activeIndex === 0
              ? 'proposers'
              : activeIndex >= agencyIndex
              ? 'agent'
              : 'insureds',
          custSeq: isInsuredSigning
            ? String(insuredIndex)
            : isApproverSigning
            ? '2'
            : '1',
          placeOfSignature: placeOfSigning,
        });
      }

      setStrokes([]);
      placeOfSigningFieldRef?.current?.blur();

      if (activeIndex === lastIndex) {
        onNext();
      } else {
        setActiveIndex(activeIndex + 1);
      }
    } catch {
      alertError(t('eApp:signature.failedToSave'));
    }
  }, [
    activeIndex,
    alertError,
    agencyIndex,
    lastIndex,
    onNext,
    placeOfSigning,
    signedTime,
    t,
    updateSignature,
  ]);

  const onConfirm = useCallback(() => {
    // showSignatureTerm(false);
    hideSignatureTerm();
  }, [hideSignatureTerm]);

  const onClearPress = useCallback(() => {
    setStrokes([]);
  }, []);

  const onDismiss = useCallback(() => {
    setActiveIndex(0);
    setStrokes([]);
    setPlaceOfSigning('');
    onCancel();
  }, [onCancel]);

  const displayedName = nameList[activeIndex];

  const signatureType =
    activeIndex === 0
      ? isEntity
        ? 'authorizedSignatory'
        : 'certificateOwner'
      : activeIndex === agencyIndex
      ? 'servicingAgent'
      : activeIndex > agencyIndex
      ? 'approver'
      : 'personCovered';

  const declarationTitle =
    activeIndex === 0
      ? t(
          isEntity
            ? 'eApp:signature.declaration.title.agent'
            : 'eApp:signature.declaration.title.policyOwner',
        )
      : activeIndex >= agencyIndex
      ? isBanca
        ? t(
            language === 'en'
              ? 'eApp:banca.signature.declaration.title.agent'
              : 'eApp:banca.signature.declaration.title.agent.bahasa',
          )
        : t('eApp:signature.declaration.title.agent')
      : t('eApp:signature.declaration.title.insured');

  const declarationContent =
    activeIndex >= agencyIndex
      ? isBanca
        ? BANCA_SIGNATURE_AGENT_TERMS_AND_CONDITIONS[language]
        : SIGNATURE_AGENT_TERMS_AND_CONDITIONS[language] || ''
      : quotation?.plans?.[0].pid
      ? PRODUCT_CODE_TO_TERMS?.[quotation?.plans?.[0].pid]?.[language] || ''
      : '';
  const shortenDeclarationContent = useMemo(
    () => [declarationContent].flat().slice(0, 1).join('\n'),
    [declarationContent],
  );

  const languageOptions = useLanguageOptions();

  useEffect(() => {
    if (caseObj?.application?.signature) {
      if (
        f2fList[0] &&
        (!caseObj?.application?.signature.proposers ||
        caseObj?.application?.signature.proposers.length === 0)
      ) {
        setActiveIndex(0);
      } else if (
        f2fList[0] &&
        nameList.length >= (isRookieAgent ? 4 : 3) &&
        (!caseObj?.application?.signature.insureds ||
          caseObj?.application?.signature.insureds.length <
            nameList.length - (isRookieAgent ? 3 : 2))
      ) {
        setActiveIndex(
          (caseObj?.application?.signature?.insureds?.length || 0) + 1,
        );
      } else if (
        !caseObj?.application?.signature.agent ||
        caseObj?.application?.signature.agent.length === 0
      ) {
        setActiveIndex(agencyIndex);
      } else if (
        !caseObj?.application?.signature.agent ||
        caseObj?.application?.signature.agent.length === 1
      ) {
        setActiveIndex(lastIndex);
      }
    }
  }, [caseObj?.application?.signature, lastIndex, nameList, nameList.length]);

  return (
    <Backdrop
      renderToHardwareTextureAndroid
      entering={FadeIn}
      exiting={FadeOut}>
      <KeyboardAvoidingViewContainer
        behavior={Platform.select({
          android: undefined,
          ios: 'position',
        })}>
        <Container edges={['top']} height={screenHeight}>
          <Content>
            {!isRemoteSelling && (
              <DotContainer>
                <Dots total={f2fList.length} activeIndex={activeIndex} />
              </DotContainer>
            )}

            <HeaderContainer>
              <SignatureIcon>
                <SignatureSVG />
              </SignatureIcon>

              <Header>
                <HeaderTitle numberOfLines={1} fontWeight="bold">
                  {t(
                    isBanca
                      ? `eApp:banca.signature.title.${signatureType}`
                      : `eApp:signature.title.${signatureType}`,
                    {
                      name: displayedName,
                    },
                  )}
                </HeaderTitle>

                {isRemoteSelling && (
                  <Picker
                    value={language}
                    onChange={value => setLanguage(value as 'en' | 'my')}
                    type="chip"
                    items={languageOptions}
                    style={{ justifyContent: 'flex-end' }}
                  />
                )}
              </Header>
            </HeaderContainer>

            {isRemoteSelling ? (
              <>
                <Box
                  borderRadius={borderRadius.small}
                  py={14}
                  px={space[4]}
                  mb={space[4]}
                  bgColor={colors.primaryVariant3}>
                  <LargeBody color={colors.primary}>
                    {t('eApp:signature.remoteSelling.hint')}
                  </LargeBody>
                </Box>
                <ScrollView>
                  <RemoteAgreeText fontWeight="bold">
                    {t('eApp:signature.agreeTerm', {
                      name: displayedName,
                    })}
                  </RemoteAgreeText>
                  <TermText text={declarationContent} />
                </ScrollView>
              </>
            ) : (
              <>
                {isEntity ? (
                  <UnderstandAndConfirm>
                    {t('eApp:signature.understandAndConfirm')}
                  </UnderstandAndConfirm>
                ) : (
                  <>
                    <LargeLabel fontWeight="bold">
                      {t('eApp:signature.agreeTerm', {
                        name: displayedName,
                      })}
                    </LargeLabel>
                    <UnderstandAndConfirm numberOfLines={2}>
                      {shortenDeclarationContent}
                    </UnderstandAndConfirm>
                  </>
                )}

                <NoticeContainer onPress={showSignatureTerm}>
                  <DetailText fontWeight="bold">
                    {t('eApp:signature.viewDetails')}
                  </DetailText>

                  <Icon.ChevronRight size={20} fill={colors.primary} />
                </NoticeContainer>

                <DrawPad
                  ref={drawPadRef}
                  strokes={strokes}
                  onChange={setStrokes}
                  onClearPress={onClearPress}
                  signedTime={signedTime}
                />

                <PlaceAndDateContainer pointerEvents="box-none">
                  <TextField
                    ref={placeOfSigningFieldRef}
                    label={t('eApp:signature.placeOfSigning')}
                    style={{ width: 340, height: 56 }}
                    inputStyle={
                      { color: colors.palette.fwdDarkGreen } as ViewStyle
                    }
                    value={placeOfSigning}
                    onChange={setPlaceOfSigning}
                    disableFullscreenUI
                    highlight={!placeOfSigning}
                  />

                  <NameDate>
                    {t(`eApp:signature.${signatureType}`, {
                      name: displayedName,
                      date,
                    })}
                  </NameDate>
                </PlaceAndDateContainer>
              </>
            )}

            <Footer>
              <Button
                text={t('eApp:cancel')}
                variant="secondary"
                onPress={onDismiss}
                style={{
                  width: 100,
                }}
                disabled={isUpdatingSignature}
              />

              <Button
                disabled={disabled}
                text={
                  isRemoteSelling
                    ? t('eApp:signature.agreeAndContinue')
                    : t('eApp:next')
                }
                style={{
                  width: isRemoteSelling ? 171 : 162,
                }}
                variant="primary"
                onPress={onContinue}
                loading={isUpdatingSignature}
              />
            </Footer>
          </Content>
        </Container>
      </KeyboardAvoidingViewContainer>

      <SignatureTermAndConditionModal
        title={declarationTitle}
        content={declarationContent}
        visible={signatureTermVisible}
        onConfirm={onConfirm}
        language={language}
        onChangeLanguage={setLanguage}
      />
    </Backdrop>
  );
});

export default function Signature(props: Props) {
  return (
    <Modal
      visible={props.visible}
      transparent
      statusBarTranslucent
      animationType="slide">
      <PortalProvider>
        <PromptProvider>
          <_Signature {...props} />
        </PromptProvider>
      </PortalProvider>
    </Modal>
  );
}
