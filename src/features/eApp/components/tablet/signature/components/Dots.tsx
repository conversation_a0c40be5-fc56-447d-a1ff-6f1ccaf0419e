import styled from '@emotion/native';
import { memo, useMemo } from 'react';

const Container = styled.View(() => ({
  alignItems: 'center',
  flex: 1,
  flexDirection: 'row',
  justifyContent: 'center',
}));

interface Props {
  total: number;
  activeIndex: number;
}

const Dot = memo(
  ({ index, activeIndex }: { index: number; activeIndex: number }) => (
    <DotContainer>
      {index === activeIndex ? <ActiveDot1 /> : <InactiveDot />}
    </DotContainer>
  ),
);

const DotContainer = styled.View(() => ({
  width: 30,
  height: 10,
}));

const InactiveDot = styled.View(({ theme }) => ({
  width: 10,
  height: 10,
  borderRadius: 6,
  borderWidth: 2,
  borderColor: theme.colors.palette.fwdGreyDark,
}));

const ActiveDot1 = styled.View(({ theme }) => ({
  width: 10,
  height: 10,
  borderRadius: 6,
  borderWidth: 2,
  borderColor: theme.colors.palette.fwdOrange[100],
  backgroundColor: theme.colors.palette.fwdOrange[100],
}));

export const Dots = memo(function Dots({ total, activeIndex }: Props) {
  const steps = useMemo(() => new Array(total).fill(''), []);

  return (
    <Container>
      {steps.map((_, index) => (
        <Dot key={index} activeIndex={activeIndex} index={index} />
      ))}
    </Container>
  );
});

export default Dots;
