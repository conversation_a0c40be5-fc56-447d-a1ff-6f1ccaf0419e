import styled from '@emotion/native';
import { Column, CurrencyTextField, Row, TextField } from 'cube-ui-components';
import { ChildDependentDetailFormSchemaType } from 'features/eApp/validations/childDetailSchema';
import React, { useEffect, useMemo } from 'react';
import { calculateAge } from 'utils/helper/calculateAge';
import ChildContainer from './ChildContainer';
import { useTranslation } from 'react-i18next';
import { NumberSequence } from 'types';
import Input from 'components/Input';
import { AddIDType, IdType, IncomeRange } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import {
  Control,
  UseFormResetField,
  useController,
  useWatch,
} from 'react-hook-form';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import { Gender, MaritalStatus } from 'types/person';
import ReadOn<PERSON><PERSON>ield from 'features/eApp/components/tablet/common/ReadOnlyField';
import IdNumberField from 'components/IdNumberField';
import { INCOME_GREATER_THAN_200K } from 'constants/optionList';
import Autocomplete from 'components/Autocomplete';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { RiderCode } from 'types/quotation';
import { useGetPartyFromActiveCase } from 'hooks/useParty';

interface Props {
  item: ChildDependentDetailFormSchemaType;
  control: Control<{ data: ChildDependentDetailFormSchemaType[] }>;
  index: number;
  isLatest?: boolean;
  resetField: UseFormResetField<{ data: ChildDependentDetailFormSchemaType[] }>;
  forEntity?: boolean;
}

export default function ChildForm({
  item,
  control,
  index,
  isLatest,
  resetField,
  forEntity,
}: Props) {
  const { t } = useTranslation(['eApp', 'common']);
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const personalAge = item.dob
    ? `${calculateAge(new Date(item.dob))} ${t('eApp:shortYearOld')}`
    : null;

  const fullName = useWatch({
    name: `data.${index}.fullName`,
    control: control,
  });

  const gender = useWatch({
    name: `data.${index}.gender`,
    control: control,
  });

  const relationship = useWatch({
    name: `data.${index}.relationship`,
    control: control,
  });

  const yearToSupport = useWatch({
    name: `data.${index}.yearToSupport`,
    control: control,
  });

  const occupation = useWatch({
    name: `data.${index}.occupation`,
    control: control,
  });

  const { occupationDescription } = useOccupationClass(occupation);

  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: `data.${index}.occupationDescription`, control });

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
  }, [occupationDescription, onChangeOccupationDescription]);

  const annualIncome = useWatch({
    name: `data.${index}.annualIncome`,
    control: control,
  });

  const primaryIdType = useWatch({
    name: `data.${index}.primaryIdType`,
    control: control,
  });

  const additionalIDType = useWatch({
    name: `data.${index}.additionalIDType`,
    control: control,
  });

  const additionalIdentification = useWatch({
    name: `data.${index}.additionalIdentification`,
    control: control,
  });

  const {
    field: { onChange: onChangeMaritalStatus },
  } = useController({ name: `data.${index}.maritalStatus`, control });

  useEffect(() => {
    onChangeMaritalStatus(MaritalStatus.SINGLE);
  }, [onChangeMaritalStatus]);

  const annualIncomeValue = useWatch({
    name: `data.${index}.annualIncome`,
    control: control,
  });

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: `data.${index}.annualIncomeAmount`, control });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  const quotation = useSelectedQuotation();

  const hasAddRider = useMemo(() => {
    const addRider = quotation?.plans?.find(
      plan => plan?.pid === RiderCode.ADIA,
    );
    return addRider !== null && addRider !== undefined;
  }, [quotation]);

  const id = useWatch({ control, name: `data.${index}.id` });
  const party = useGetPartyFromActiveCase(id);

  return (
    <ChildContainer
      key={item?.id?.toString()}
      title={
        fullName
          ? t('eApp:childInformation', { childName: fullName })
          : t('eApp:nthChildInformation', {
              position: t(
                `common:position.${(index + 1) as NumberSequence<1, 5>}`,
              ),
            })
      }
      lineBottom={!isLatest}>
      <Content>
        <RowBox>
          <ReadOnlyField
            value={fullName}
            label={t('eApp:certificate.form.nameOfChild')}
          />
          <ReadOnlyField
            value={
              optionList?.RELATIONSHIP.options.find(
                e => relationship == e.value,
              )?.label
            }
            label={t('eApp:certificate.form.relationship')}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<IdType, string>}
            name={`data.${index}.primaryIdType`}
            label={t('eApp:certificate.form.primaryIdType')}
            style={{ flex: 1, marginTop: 7 }}
            data={optionList?.ID_TYPE.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            control={control}
            as={IdNumberField}
            idType={primaryIdType}
            name={`data.${index}.identificationNumber`}
            label={t('eApp:certificate.form.identificationNumber')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        <RowBox>
          <ReadOnlyField
            value={
              item.dob ? `${dateFormatUtil(item.dob)} (${personalAge})` : ''
            }
            label={t('eApp:certificate.form.dateOfBirth')}
          />
          <ReadOnlyField
            value={gender === Gender.MALE ? t('eApp:male') : t('eApp:female')}
            label={t('eApp:gender')}
          />
        </RowBox>

        <RowBox>
          <ReadOnlyField
            value={
              optionList?.OCCUPATION.options.find(e => occupation == e.value)
                ?.label
            }
            label={t('eApp:certificate.form.occupationChild')}
            withRowHasInput={forEntity}
          />
          {forEntity ? (
            <Input
              control={control}
              as={CurrencyTextField}
              name={`data.${index}.yearToSupport`}
              label={t('eApp:certificate.form.yearToSupport')}
              style={{ flex: 1, marginTop: 7 }}
              shouldHighlightOnUntouched={value => !value}
            />
          ) : (
            <ReadOnlyField
              value={yearToSupport}
              label={t('eApp:certificate.form.yearToSupport')}
            />
          )}
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<AddIDType, string>}
            name={`data.${index}.additionalIDType`}
            label={
              additionalIDType
                ? t('eApp:certificate.form.additionalIDType.required')
                : t('eApp:certificate.form.additionalIDType')
            }
            data={optionList?.ADD_ID_TYPE.options ?? []}
            // disabled
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1, marginTop: 7 }}
            onChange={(value: string | null) => {
              // For the case empty value
              if (value === additionalIDType || value === '') {
                resetField?.(`data.${index}.additionalIdentification`);
                resetField?.(`data.${index}.additionalIDType`);
              }
            }}
          />
          <Input
            control={control}
            as={TextField}
            name={`data.${index}.additionalIdentification`}
            label={
              additionalIDType
                ? t('eApp:certificate.form.additionalIdentification.required')
                : t('eApp:certificate.form.additionalIdentification')
            }
            style={{ flex: 1, marginTop: 7 }}
            value={additionalIDType ? additionalIdentification : ''}
            disabled={!additionalIDType}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<IncomeRange, string>}
            name={`data.${index}.annualIncome`}
            style={{ flex: 1, marginTop: 7 }}
            label={t('eApp:certificate.form.annualIncomeOptional')}
            data={optionList?.INCOME_RANGE.options ?? []}
            disabled={Boolean(hasAddRider && party?.isMainInsured)}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`data.${index}.annualIncomeAmount`}
            label={t('eApp:certificate.form.annualIncomeAmount')}
            style={{ flex: 1, marginTop: 7 }}
            // disabled
            disabled={annualIncome !== INCOME_GREATER_THAN_200K}
          />
        </RowBox>
      </Content>
    </ChildContainer>
  );
}

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));
