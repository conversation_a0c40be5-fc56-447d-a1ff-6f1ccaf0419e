import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Checkbox,
  Column,
  H6,
  Icon,
  Row,
} from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useController,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Country, OptionList, Postcode } from 'types/optionList';
import {
  MY_OPTION_LIST,
  MY_COUNTRY,
  NEW_ADDRESS_OPTION,
  MAIN_INSURED_ADDRESS_OPTION,
  OWNER_ADDRESS_OPTION,
} from 'constants/optionList';
import { TFuncKey } from 'i18next';
import styled from '@emotion/native';
import { View } from 'react-native';
import { useEffect } from 'react';
import { MYAddressType } from 'features/eApp/validations/eAppCommonSchema';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import AutocompletePopup from 'components/AutocompletePopup';
import { useChildAddressInformationOptions } from 'features/eApp/hooks/useChildAddressInformationOptions';
import AddressLineField from 'components/AddressLineField';

interface Props {
  index: number;
  addressTypes: MYAddressType[];
  ownerAddress?: AddressInfo;
  mainInsuredAddress?: AddressInfo;
  disabled?: boolean;
  control: Control<{ data: AddressInfo[] }>;
  setValue: UseFormSetValue<{ data: AddressInfo[] }>;
  getValues: UseFormGetValues<{ data: AddressInfo[] }>;
  trigger: UseFormTrigger<{ data: AddressInfo[] }>;
  shouldHighlight?: boolean;
  noAddressOption?: boolean;
  forEntity?: boolean;
}

export default function AddressInformationForm({
  index,
  ownerAddress,
  mainInsuredAddress,
  addressTypes,
  disabled,
  control,
  setValue,
  getValues,
  trigger,
  forEntity,
  shouldHighlight,
  noAddressOption,
}: Props) {
  return (
    <>
      {addressTypes.map((type, idx, arr) => (
        <View key={type}>
          <AddressSection
            index={index}
            type={type}
            ownerAddress={ownerAddress}
            mainInsuredAddress={mainInsuredAddress}
            disabled={disabled}
            control={control}
            setValue={setValue}
            getValues={getValues}
            trigger={trigger}
            noAddressOption={noAddressOption}
            shouldHighlight={shouldHighlight}
            forEntity={forEntity}
          />
          {idx !== arr.length - 1 && <Line />}
        </View>
      ))}
    </>
  );
}

const AddressSection = ({
  index,
  ownerAddress,
  mainInsuredAddress,
  type,
  noAddressOption,
  disabled,
  control,
  setValue,
  getValues,
  trigger,
  forEntity,
  shouldHighlight,
}: Omit<Props, 'addressTypes' | 'isOwner'> & {
  type: MYAddressType;
  noAddressOption?: boolean;
}) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['customerFactFind', 'eApp']);

  const { data: rawOptionList, isFetching: isLoadingOptionList } =
    useGetOptionList();
  const optionList = rawOptionList as OptionList<string, 'my'> | undefined;

  const { postCodeList, stateList, cityList, isMY, onCountryChange, onPostCodeChange } =
    useChildAddressInformationOptions(
      index,
      type,
      optionList,
      control,
      setValue,
      getValues,
      trigger,
    );

  let addressOptions = forEntity
    ? MY_OPTION_LIST.ENTITY_CORRESPONDENCE_ADDRESSES
    : MY_OPTION_LIST.CORRESPONDENCE_ADDRESSES;
  if (type === 'residential') {
    addressOptions = forEntity
      ? MY_OPTION_LIST.ENTITY_RESIDENTIAL_ADDRESSES
      : MY_OPTION_LIST.RESIDENTIAL_ADDRESSES;
  } else if (type === 'business') {
    addressOptions = MY_OPTION_LIST.CHILD_BUSINESS_ADDRESSES;
  }

  const {
    field: { value: selectedAddressOption, onChange: setAddressOption },
  } = useController({
    name: `data.${index}.${type}Address`,
    control: control,
  });

  const addressLine1 = mainInsuredAddress?.[`${type}AddressLine1`];
  const addressLine2 = mainInsuredAddress?.[`${type}AddressLine2`];
  const addressLine3 = mainInsuredAddress?.[`${type}AddressLine3`];
  const postCode = mainInsuredAddress?.[`${type}PostCode`];
  const city = mainInsuredAddress?.[`${type}City`];
  const state = mainInsuredAddress?.[`${type}State`];
  const country = mainInsuredAddress?.[`${type}Country`];

  useEffect(() => {
    if (selectedAddressOption === MAIN_INSURED_ADDRESS_OPTION) {
      setValue(`data.${index}.${type}AddressLine1`, addressLine1 || '');
      setValue(`data.${index}.${type}AddressLine2`, addressLine2 || '');
      setValue(`data.${index}.${type}AddressLine3`, addressLine3 || '');
      setValue(`data.${index}.${type}PostCode`, postCode || '');
      setValue(`data.${index}.${type}City`, city || '');
      setValue(`data.${index}.${type}State`, state || '');
      setValue(`data.${index}.${type}Country`, country || '');
    }
  }, [
    addressLine1,
    addressLine2,
    addressLine3,
    city,
    country,
    index,
    postCode,
    selectedAddressOption,
    setValue,
    state,
    type,
  ]);

  const isNewAddress = selectedAddressOption === NEW_ADDRESS_OPTION;

  useEffect(() => {
    if (selectedAddressOption === OWNER_ADDRESS_OPTION && ownerAddress) {
      setValue(
        `data.${index}.${type}AddressLine1`,
        ownerAddress[`${type}AddressLine1`],
      );
      setValue(
        `data.${index}.${type}AddressLine2`,
        ownerAddress[`${type}AddressLine2`],
      );
      setValue(
        `data.${index}.${type}AddressLine3`,
        ownerAddress[`${type}AddressLine3`],
      );
      setValue(
        `data.${index}.${type}PostCode`,
        ownerAddress[`${type}PostCode`],
      );
      setValue(`data.${index}.${type}City`, ownerAddress[`${type}City`]);
      setValue(`data.${index}.${type}State`, ownerAddress[`${type}State`]);
      setValue(`data.${index}.${type}Country`, ownerAddress[`${type}Country`]);
    }
  }, [index, ownerAddress, selectedAddressOption, setValue, type]);

  return (
    <Column backgroundColor={colors.background} px={space[6]}>
      <Row>
        <Icon.Location fill={colors.palette.black} />
        <H6 fontWeight="bold" style={{ marginLeft: space[1] }}>
          {t(`customerFactFind:personalDetails.title.${type}Address`)}
        </H6>
      </Row>
      {(!noAddressOption && addressOptions.length > 1) ||
      type === 'business' ? (
        <Checkbox
          label={t(
            `eApp:certificate.${addressOptions[0].label}` as TFuncKey<['eApp']>,
          )}
          checked={selectedAddressOption === addressOptions[0].value}
          disabled={type === 'business'}
          onChange={checked => {
            if (checked) {
              setAddressOption(addressOptions[0].value);
            } else if (addressOptions.length > 1) {
              setAddressOption(addressOptions[1].value);
              if (addressOptions[1].value === NEW_ADDRESS_OPTION) {
                setValue(`data.${index}.${type}AddressLine1`, '');
                setValue(`data.${index}.${type}AddressLine2`, '');
                setValue(`data.${index}.${type}AddressLine3`, '');
                setValue(`data.${index}.${type}PostCode`, '');
                setValue(`data.${index}.${type}City`, '');
                setValue(`data.${index}.${type}State`, '');
                setValue(`data.${index}.${type}Country`, MY_COUNTRY);
              }
            } else {
              setAddressOption('');
              setValue(`data.${index}.${type}AddressLine1`, '');
              setValue(`data.${index}.${type}AddressLine2`, '');
              setValue(`data.${index}.${type}AddressLine3`, '');
              setValue(`data.${index}.${type}PostCode`, '');
              setValue(`data.${index}.${type}City`, '');
              setValue(`data.${index}.${type}State`, '');
              setValue(`data.${index}.${type}Country`, MY_COUNTRY);
            }
          }}
          style={{ marginTop: 12 }}
        />
      ) : (
        <Box h={7} />
      )}
      {isNewAddress && (
        <>
          <Input
            control={control}
            as={AddressLineField}
            name={`data.${index}.${type}AddressLine1`}
            label={t('customerFactFind:personalDetails.form.addressLine1')}
            hint={t('customerFactFind:personalDetails.form.SubAddressLine1')}
            style={{ marginTop: 20 }}
            disabled={disabled}
            shouldHighlightOnUntouched={value =>
              Boolean(shouldHighlight && !value)
            }
          />
          <Input
            control={control}
            as={AddressLineField}
            name={`data.${index}.${type}AddressLine2`}
            label={t('customerFactFind:personalDetails.form.addressLine2')}
            hint={t('customerFactFind:personalDetails.form.SubAddressLine2')}
            style={{ marginTop: 27 }}
            disabled={disabled}
          />
          <Input
            control={control}
            as={AddressLineField}
            name={`data.${index}.${type}AddressLine3`}
            label={t('customerFactFind:personalDetails.form.addressLine3')}
            style={{ marginTop: 27 }}
            disabled={disabled}
          />
          {/* 1 */}
          <Row mt={27} gap={space[6]}>
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<Postcode<string, 'my'>, string>}
                name={`data.${index}.${type}PostCode`}
                label={t('customerFactFind:personalDetails.form.postCode')}
                data={postCodeList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={{ flex: 1 }}
                onChange={onPostCodeChange}
                searchable
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value)
                }
              />
            ) : (
              <Input
                control={control}
                as={AddressLineField}
                name={`data.${index}.${type}PostCode`}
                label={t('customerFactFind:personalDetails.form.postCode')}
                style={{ flex: 1 }}
                disabled={disabled}
                maxLength={10}
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value)
                }
              />
            )}
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<{ value: string; label: string }, string>}
                name={`data.${index}.${type}City`}
                label={t('customerFactFind:personalDetails.form.city')}
                data={cityList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={{ flex: 1 }}
                searchable
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value)
                }
              />
            ) : (
              <Input
                control={control}
                as={AddressLineField}
                name={`data.${index}.${type}City`}
                label={t('customerFactFind:personalDetails.form.city')}
                style={{ flex: 1 }}
                disabled={disabled}
                maxLength={60}
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value)
                }
              />
            )}
          </Row>
          {/* 2 */}
          <Row mt={27} gap={space[6]}>
            <Input
              control={control}
              as={AutocompletePopup<{ value: string; label: string }, string>}
              name={`data.${index}.${type}State`}
              label={t('customerFactFind:personalDetails.form.state')}
              data={stateList}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={{ flex: 1 }}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
            />
            <Input
              control={control}
              as={AutocompletePopup<Country, string>}
              name={`data.${index}.${type}Country`}
              label={t('customerFactFind:personalDetails.form.country')}
              data={optionList?.COUNTRY.options ?? []}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={{ flex: 1 }}
              onChange={onCountryChange}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
            />
          </Row>
        </>
      )}
    </Column>
  );
};

const Line = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  height: 1,
  marginVertical: space[5],
  marginHorizontal: space[6],
}));
