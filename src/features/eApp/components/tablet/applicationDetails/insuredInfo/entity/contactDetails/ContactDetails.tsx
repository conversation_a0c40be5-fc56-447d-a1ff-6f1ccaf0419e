import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { <PERSON>umn, Picker, Row, TextField } from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CountryCode, PrefContactMode } from 'types/optionList';
import ApplicationDetailsSectionContainer from '../../../common/ApplicationDetailsSectionContainer';
import PhoneSVG from 'features/eApp/assets/PhoneSVG';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { InsuredFormSchemaType } from 'features/eApp/validations/insuredSchema';
import PhoneField from 'components/PhoneField';
import { phoneNumberStyle } from 'features/customerFactFind/styles/phoneNumberStyle';
import { getCountryCodeDisplayedLabel, getCountryCodeValue, getOptionListLabel } from 'constants/optionList';
import AutocompletePopup from 'components/AutocompletePopup';

interface Props {
  control: Control<InsuredFormSchemaType>;
  setValue: UseFormSetValue<InsuredFormSchemaType>;
  getValues: UseFormGetValues<InsuredFormSchemaType>;
}

const ContactDetails = (props: Props) => {
  const { control } = props;
  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const [isDone, setDone] = useState<boolean>(false);
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const mobileCountryCode = useWatch({
    name: 'mobileCountryCode',
    control: control,
  });
  const mobileNumber = useWatch({
    name: 'mobileNumber',
    control: control,
  });

  const email = useWatch({
    name: 'email',
    control: control,
  });

  useEffect(() => {
    if (mobileCountryCode && mobileNumber && email) {
      setDone(true);
    } else {
      setDone(false);
    }
  }, [mobileCountryCode, mobileNumber, email]);

  return (
    <ApplicationDetailsSectionContainer
      title={t('eApp:certificate.contactTitle')}
      icon={<PhoneSVG />}
      isDone={isDone}>
      <Content>
        <RowBox>
          <Row flex={1} mt={7}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="mobileCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="mobileNumber"
              label={t('eApp:certificate.form.mobileNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              inputStyle={phoneNumberStyle.input}
              shouldHighlightOnUntouched={value => !value}
            />
          </Row>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('eApp:email')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        <RowBox>
          <Row flex={1} mt={7}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="homeCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="homeNumber"
              label={t('eApp:certificate.form.homeNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              inputStyle={phoneNumberStyle.input}
            />
          </Row>
          <Row flex={1} mt={7}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="officeCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="officeNumber"
              label={t('eApp:certificate.form.officeNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              inputStyle={phoneNumberStyle.input}
            />
          </Row>
        </RowBox>
      </Content>
    </ApplicationDetailsSectionContainer>
  );
};

export default ContactDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));
