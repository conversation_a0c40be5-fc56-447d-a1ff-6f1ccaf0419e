import styled from '@emotion/native';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import { authorizedSignatoryInfoValidationSchema } from 'features/eApp/validations/authorizedSignatoryInfoValidation';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useController, useForm, useWatch } from 'react-hook-form';
import { View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useYupResolver } from 'utils/validation/useYupResolver';
import EAppFooter from '../../common/EAppFooter';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import {
  Box,
  Column,
  H7,
  LargeBody,
  Picker,
  PictogramIcon,
  Row,
  SmallLabel,
  TextField,
} from 'cube-ui-components';
import ApplicationDetailsSectionContainer from '../common/ApplicationDetailsSectionContainer';
import Input from 'components/Input';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { AddIDType, IdType, Nationality } from 'types/optionList';
import { Entity, Gender } from 'types/person';
import { convertNewNRICtoDOBAndGender } from 'features/eApp/utils/idNumberUtils';
import { calculateAge } from 'utils/helper/calculateAge';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import IdNumberField from 'components/IdNumberField';
import { NEW_NRIC } from 'constants/optionList';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useSaveParty } from 'features/eApp/hooks/useParty';
import { useEAppAlert } from 'features/eApp/hooks/useEAppAlert';
import { PartyRole } from 'types/party';
import { toAuthorizedSignatoryInfo } from 'features/eApp/utils/caseUtils.my';
import { subYears } from 'date-fns';
import NameField from 'components/NameField';
import Autocomplete from 'components/Autocomplete';
import DatePickerCalendar from 'components/DatePickerCalendar';
import AutocompletePopup from 'components/AutocompletePopup';
import TabletSections from 'features/eAppV2/common/components/TabletSections';
import { useUpdatePrimaryIdType } from 'features/customerFactFind/hooks/useUpdatePrimaryIdType';

const { minDate, maxDate } = getDateOfBirthDropdownProps();
const defaultDate = subYears(new Date(), 17);

export default function AuthorizedSignatoryInfo() {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const [activePath, setActivePath] = useState('');
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const { my_authorizedSignatoryInfo, updateMY_AuthorizedSignatoryInfo } =
    useEAppStore(state => ({
      my_authorizedSignatoryInfo: state.my_authorizedSignatoryInfo,
      updateMY_AuthorizedSignatoryInfo: state.updateMY_AuthorizedSignatoryInfo,
    }));

  const { caseObj } = useGetActiveCase();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();

  const next = useEAppProgressBarStore(state => state.next);

  const { alertError } = useEAppAlert();
  const onNext = async () => {
    if (!caseObj) return;
    const owner = caseObj.parties?.find(p =>
      p.roles.includes(PartyRole.PROPOSER),
    );
    if (!owner) return;
    const data = getValues();
    const savingData = toAuthorizedSignatoryInfo(data);
    try {
      const id = await saveParty({
        ...owner,
        entity: {
          ...owner.entity,
          representative: savingData,
        } as Entity,
      });
      updateMY_AuthorizedSignatoryInfo({ id, ...data });
      next(true);
    } catch {
      alertError(onNext);
    }
  };

  const authorizedSignatoryInfoValidationResolver = useYupResolver(
    authorizedSignatoryInfoValidationSchema,
  );
  const {
    formState: { isValid },
    control,
    watch,
    setValue,
    getValues,
    resetField,
  } = useForm({
    mode: 'onBlur',
    defaultValues: my_authorizedSignatoryInfo,
    resolver: authorizedSignatoryInfoValidationResolver,
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control,
      watch,
      schema: authorizedSignatoryInfoValidationSchema,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
    });

  const gender = watch('gender');
  const titles = useMemo(() => {
    if (gender === Gender.MALE) {
      return optionList?.CUBE_TITLE.options.filter(
        o => o.gender.male === 'True',
      );
    }
    if (gender === Gender.FEMALE) {
      return optionList?.CUBE_TITLE.options.filter(
        o => o.gender.female === 'True',
      );
    }
    return [];
  }, [gender, optionList?.CUBE_TITLE.options]);

  const idType = watch('idType');
  const idNumber = watch('idNumber');
  const isNewNRIC = idType === NEW_NRIC;
  useEffect(() => {
    if (isNewNRIC) {
      const { dateOfBirth } = convertNewNRICtoDOBAndGender(idNumber);
      if (dateOfBirth) {
        setValue('dob', dateOfBirth, {
          shouldValidate: true,
          shouldTouch: true,
        });
      }
    }
  }, [isNewNRIC, idNumber, setValue]);

  const dob = watch('dob');
  const age = useMemo(
    () => (dob ? String(calculateAge(new Date(dob))) : ''),
    [dob],
  );

  const {
    field: { onChange: onChangePrimaryIdType },
  } = useController({
    name: 'idType',
    control,
  });

  const nationality = useWatch({
    name: 'nationality',
    control,
  });

  useUpdatePrimaryIdType(onChangePrimaryIdType, nationality);

  const additionalIdType = watch('additionalIdType');
  const additionalIdNumber = watch('additionalIdNumber');

  const fullName = watch('fullName');
  const sections = useMemo(() => {
    return [
      {
        name: 'authorizedSignatoryInfo',
        title: t('eApp:authorizedSignatoryInfo.authorizedSignatory'),
        subtitle: fullName,
        content: (
          <ScrollViewContainer ref={scrollRef}>
            <Column marginBottom={space[4]} paddingX={space[1]} gap={space[2]}>
              <H7 fontWeight="bold" style={{ flex: 1 }}>
                {t('eApp:authorizedSignatoryInfo.authorizedSignatoryInfo')}
              </H7>
              <LargeBody fontWeight="bold" style={{ flex: 1 }}>
                {t('eApp:authorizedSignatoryInfo.disclosure')}
                <LargeBody style={{ flex: 1 }}>
                  {t('eApp:authorizedSignatoryInfo.disclosure.content')}
                </LargeBody>
              </LargeBody>
            </Column>

            <ApplicationDetailsSectionContainer
              title={t('eApp:authorizedSignatoryInfo.personalDetails')}
              icon={<PictogramIcon.Lanyard size={40} />}
              isDone={isValid}>
              <Content>
                <RowBox>
                  <Input
                    control={control}
                    as={NameField}
                    name="fullName"
                    label={t('eApp:authorizedSignatoryInfo.fullName')}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                </RowBox>
                <RowBox>
                  <Input
                    control={control}
                    as={Picker}
                    name="gender"
                    label={t('eApp:authorizedSignatoryInfo.gender')}
                    items={optionList?.GENDER.options.map(i => ({
                      label: i.label,
                      value: i.value,
                    }))}
                    onChange={() => {
                      setValue('title', '');
                    }}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="designation"
                    label={t('eApp:authorizedSignatoryInfo.designation')}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                </RowBox>
                {gender && (
                  <Box mt={space[5]} gap={space[2]}>
                    <SmallLabel style={{ marginLeft: space[4] }}>
                      {t('eApp:authorizedSignatoryInfo.title')}
                    </SmallLabel>
                    <Input
                      control={control}
                      as={Picker}
                      type="chip"
                      name="title"
                      items={titles}
                      style={{ flex: 1 }}
                      containerStyle={{ flexWrap: 'wrap' }}
                    />
                  </Box>
                )}
                <RowBox>
                  <Input
                    control={control}
                    as={Autocomplete<IdType, string>}
                    name="idType"
                    label={t('eApp:authorizedSignatoryInfo.idType')}
                    data={optionList?.ID_TYPE.options || []}
                    getItemLabel={i => i.label}
                    getItemValue={i => i.value}
                    disabled
                    style={{ flex: 1, marginTop: 7 }}
                    onBlur={() => {
                      if (idNumber) {
                        setValue('idNumber', idNumber, {
                          shouldTouch: true,
                          shouldValidate: true,
                        });
                      }
                    }}
                  />
                  <Input
                    control={control}
                    as={IdNumberField}
                    name="idNumber"
                    label={t('eApp:authorizedSignatoryInfo.idNumber')}
                    hint={isNewNRIC ? t('eApp:nricHint') : ''}
                    idType={idType}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                </RowBox>
                <RowBox>
                  <Row flex={1} gap={space[3]}>
                    <Input
                      control={control}
                      as={DatePickerCalendar}
                      name="dob"
                      label={t('eApp:authorizedSignatoryInfo.dob')}
                      hint={t('eApp:dateFormat')}
                      style={{ flex: 1, marginTop: 7 }}
                      minDate={minDate}
                      maxDate={maxDate}
                      defaultDate={defaultDate}
                      disabled={isNewNRIC}
                      onBlur={() => {
                        if (isNewNRIC) {
                          setValue('idNumber', idNumber, {
                            shouldValidate: true,
                            shouldTouch: true,
                          });
                        }
                      }}
                    />
                    <TextField
                      label={t('eApp:authorizedSignatoryInfo.age')}
                      value={age}
                      disabled
                      style={{ width: 72, marginTop: 7 }}
                    />
                  </Row>
                  <Input
                    control={control}
                    as={AutocompletePopup<Nationality, string>}
                    name="nationality"
                    label={t('eApp:authorizedSignatoryInfo.nationality')}
                    data={optionList?.NATIONALITY.options || []}
                    getItemLabel={i => i.label}
                    getItemValue={i => i.value}
                    disabled={isLoadingOptionList}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                </RowBox>
                <RowBox>
                  <Input
                    control={control}
                    as={Autocomplete<AddIDType, string>}
                    name="additionalIdType"
                    label={t('eApp:authorizedSignatoryInfo.additionalIdType')}
                    data={optionList?.ADD_ID_TYPE.options || []}
                    getItemLabel={i => i.label}
                    getItemValue={i => i.value}
                    onChange={(value: string | null) => {
                      if (value === additionalIdType || value === '') {
                        setValue('additionalIdNumber', '', {
                          shouldTouch: false,
                          shouldValidate: false,
                        });
                        setValue('additionalIdType', '', {
                          shouldTouch: false,
                          shouldValidate: false,
                        });
                      }
                    }}
                    disabled={isLoadingOptionList}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="additionalIdNumber"
                    disabled={!additionalIdType}
                    label={
                      additionalIdType
                        ? t(
                            'eApp:authorizedSignatoryInfo.additionalIdNumber.required',
                          )
                        : t('eApp:authorizedSignatoryInfo.additionalIdNumber')
                    }
                    value={additionalIdType ? additionalIdNumber : ''}
                    style={{ flex: 1, marginTop: 7 }}
                  />
                </RowBox>
              </Content>
            </ApplicationDetailsSectionContainer>
            <BoxSpace />
          </ScrollViewContainer>
        ),
      },
    ];
  }, [
    additionalIdNumber,
    additionalIdType,
    age,
    control,
    fullName,
    gender,
    idNumber,
    idType,
    isLoadingOptionList,
    isNewNRIC,
    isValid,
    optionList?.ADD_ID_TYPE.options,
    optionList?.GENDER.options,
    optionList?.ID_TYPE.options,
    optionList?.NATIONALITY.options,
    setValue,
    space,
    t,
    titles,
  ]);

  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooter
        progressLock="appDetail--authorizedSignatory"
        onPress={onNext}
        disabled={!isValid}
        isLoading={isSavingParty}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnNextIncompleteField}
      />
    </>
  );
}
const BoxSpace = styled(View)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));
