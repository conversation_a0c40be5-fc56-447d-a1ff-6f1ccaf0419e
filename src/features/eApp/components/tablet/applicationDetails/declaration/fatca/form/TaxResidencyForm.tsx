import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import {
  Box,
  Column,
  Icon,
  Label,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  FatcaForm,
  NO_TIN_REASON,
} from 'features/eApp/validations/applicationDetails/declarationValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { Country } from 'types/optionList';

interface TaxResidencyProps {
  index: number;
  control: Control<FatcaForm>;
  onDelete?: () => void;
}

const TaxResidencyForm = (props: TaxResidencyProps) => {
  const { index, control, onDelete } = props;
  const { t } = useTranslation(['eApp']);
  const { space, sizes, colors } = useTheme();
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const hasTin = useWatch({
    name: `taxResidencies.${index}.hasTin`,
    control: control,
  });

  const {
    field: { value: noTinReason, onChange: onChangeNoTinReason },
  } = useController({
    name: `taxResidencies.${index}.noTinReason`,
    control: control,
  });

  const {
    field: { onChange: onChangeTinNumber },
  } = useController({
    name: `taxResidencies.${index}.tinNumber`,
    control: control,
  });

  const renderTin = () => {
    return (
      <Column gap={space[2]}>
        <Box flex={1} height={sizes[5]} />
        <Row gap={space[6]}>
          <Box flex={1}>
            <Input
              control={control}
              as={TextField}
              name={`taxResidencies.${index}.tinNumber`}
              label={t('eApp:declaration.fatca.taxResidency.tin.number')}
              shouldUnregister
            />
          </Box>
          <Box flex={1} />
        </Row>
      </Column>
    );
  };

  const renderNoTinReason = () => {
    return (
      <Column gap={space[2]}>
        <Box flex={1} height={sizes[4]} />
        <Label fontWeight="bold">
          {t('eApp:declaration.fatca.taxResidency.noTin.title')}
        </Label>

        <Input
          control={control}
          as={RadioButtonGroup}
          name={`taxResidencies.${index}.noTinReason`}
          shouldUnregister>
          <RadioButtonTopAlign
            value={NO_TIN_REASON.NOT_ISSUE}
            label={t('eApp:declaration.fatca.taxResidency.noTin.1')}
          />
          <RadioButtonTopAlign
            value={NO_TIN_REASON.UNABLE_TO_OBTAIN}
            label={t('eApp:declaration.fatca.taxResidency.noTin.2')}
          />
          {noTinReason === NO_TIN_REASON.UNABLE_TO_OBTAIN && (
            <Box ml={space[8]}>
              <Input
                control={control}
                as={TextField}
                name={`taxResidencies.${index}.additionalReason`}
                label={t(
                  'eApp:declaration.fatca.taxResidency.noTin.additionalInfomation',
                )}
                shouldUnregister
              />
            </Box>
          )}

          <RadioButtonTopAlign
            value={NO_TIN_REASON.NO_TIN_REQUIRED}
            label={t('eApp:declaration.fatca.taxResidency.noTin.3')}
          />
        </Input>
      </Column>
    );
  };

  return (
    <Content>
      <Box flex={1} height={sizes[4]} />
      <Row gap={space[2]}>
        <Box flex={1}>
          <Label fontWeight="bold">
            {t('eApp:declaration.fatca.taxResidency', { index: index + 1 })}
          </Label>
        </Box>
        {onDelete && (
          <TouchableOpacity onPress={onDelete}>
            <Icon.Delete fill={colors.palette.black} />
          </TouchableOpacity>
        )}
      </Row>
      <Row gap={space[6]} mt={space[5]}>
        <Box flex={1}>
          <Input
            control={control}
            as={AutocompletePopup<Country, string>}
            name={`taxResidencies.${index}.country`}
            label={t('eApp:declaration.fatca.taxResidency.country')}
            data={optionList?.COUNTRY?.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            shouldUnregister
            searchable
          />
        </Box>
        <Column flex={1}>
          <LargeLabel fontWeight="medium">
            {t('eApp:declaration.fatca.taxResidency.tin.question')}
          </LargeLabel>
          <Row mt={space[3]}>
            <Input
              control={control}
              as={RadioButtonGroup}
              name={`taxResidencies.${index}.hasTin`}
              shouldUnregister
              onChange={option => {
                if (option === 'yes') {
                  onChangeNoTinReason(undefined);
                } else {
                  onChangeTinNumber('');
                }
              }}>
              <RadioButton value="yes" label={t('eApp:yes')} />
              <RadioButtonEnd value="no" label={t('eApp:no')} />
            </Input>
          </Row>
        </Column>
      </Row>
      {hasTin === 'yes' && renderTin()}
      {hasTin === 'no' && renderNoTinReason()}
    </Content>
  );
};

export default TaxResidencyForm;

const Content = styled(Box)(() => ({}));

export const RadioButtonTopAlign = styled(RadioButton)(
  ({ theme: { space } }) => {
    return {
      alignItems: 'flex-start',
      paddingRight: space[6],
    };
  },
);

export const RadioButtonEnd = styled(RadioButton)(({ theme: { space } }) => {
  return {
    marginLeft: space[8],
  };
});
