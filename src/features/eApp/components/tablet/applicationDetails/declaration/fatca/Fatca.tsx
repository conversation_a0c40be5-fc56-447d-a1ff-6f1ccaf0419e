import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Button,
  Icon,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import SectionContainer from 'features/customerFactFind/components/SectionContainer';
import {
  FatcaForm,
  initialTaxResidencyFormData,
} from 'features/eApp/validations/applicationDetails/declarationValidation';
import React, { useCallback, useEffect } from 'react';
import { Control, useFieldArray, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import TaxResidencyForm from './form/TaxResidencyForm';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { MY_COUNTRY } from 'constants/optionList';

interface FatcaProps {
  control: Control<FatcaForm>;
}
const MAX_TAX_RESIDENCY = 5;

const Fatca = (props: FatcaProps) => {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const { space, colors, sizes } = useTheme();

  const { fields, append, remove } = useFieldArray({
    name: 'taxResidencies',
    control,
  });

  const hasOutsideTaxResidency = useWatch({
    name: 'hasOutsideTaxResidency',
    control: control,
  });

  const hasUsTaxResidency = useWatch({
    name: 'hasUsTaxResidency',
    control: control,
  });

  const renderTaxResidencies = useCallback(() => {
    return (
      <>
        {fields.map((_, index) => (
          <View key={index}>
            <TaxResidencyForm
              control={control}
              index={index}
              onDelete={index > 0 ? () => remove(index) : undefined}
            />
            {index < fields.length - 1 && (
              <Box flex={1} mt={space[6]}>
                <Divider />
              </Box>
            )}
          </View>
        ))}

        {fields.length > 0 && fields.length < MAX_TAX_RESIDENCY && (
          <>
            <Box flex={1} height={sizes[5]} />
            <Button
              text={t('eApp:declaration.fatca.taxResidency.tin.add')}
              icon={Icon.Plus}
              variant="secondary"
              size='small'
              mini={true}
              onPress={() => {
                append(initialTaxResidencyFormData, {shouldFocus: false});
              }}
            />
          </>
        )}
      </>
    );
  }, [fields, sizes, t, control, space, remove, append]);

  useEffect(() => {
    if (
      hasOutsideTaxResidency !== 'yes' &&
      hasUsTaxResidency !== 'yes' &&
      fields.length > 0
    ) {
      remove();
    } else if (
      (hasOutsideTaxResidency === 'yes' || hasUsTaxResidency === 'yes') &&
      fields.length === 0
    ) {
      append(initialTaxResidencyFormData, {shouldFocus: false});
    }
  }, [
    append,
    fields.length,
    hasOutsideTaxResidency,
    hasUsTaxResidency,
    remove,
  ]);

  const my_policyOwnerPersonalInfo = useEAppStore(
    state => state.my_policyOwnerPersonalInfo,
  );

  return (
    <SectionContainer title={t('eApp:declaration.fatca.title')}>
      <Content>
        <LargeBody fontWeight="medium">
          {t('eApp:declaration.fatca.message')}
        </LargeBody>
        <Box flex={1} height={sizes[6]} />
        {my_policyOwnerPersonalInfo.nationality !== MY_COUNTRY && (
          <HintContainer flex={1}>
            <LargeBody color={colors.primary}>
              {t('eApp:declaration.fatca.hint')}
            </LargeBody>
          </HintContainer>
        )}
        <Box flex={1} height={sizes[3]} />
        <LargeBody>{t('eApp:declaration.fatca.question.1')}</LargeBody>
        <Row mt={space[4]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'hasOutsideTaxResidency'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        <Box flex={1} height={sizes[4]} />
        <Divider />

        <Box flex={1} height={sizes[4]} />
        <LargeBody>{t('eApp:declaration.fatca.question.2')}</LargeBody>
        <Row mt={space[4]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'hasUsTaxResidency'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        {renderTaxResidencies()}
      </Content>
    </SectionContainer>
  );
};

export default Fatca;

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
  marginVertical: space[6],
}));

const HintContainer = styled(Box)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    paddingHorizontal: space[4],
    paddingVertical: space[4],
    borderRadius: borderRadius['small'],
  }),
);

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export const RadioButtonTopAlign = styled(RadioButton)(() => {
  return {
    alignItems: 'flex-start',
  };
});

export const RadioButtonEnd = styled(RadioButton)(({ theme: { space } }) => {
  return {
    marginLeft: space[8],
  };
});
