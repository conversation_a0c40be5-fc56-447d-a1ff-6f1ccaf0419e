import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useController,
  useWatch,
} from 'react-hook-form';
import {
  Box,
  Column,
  ExtraLargeBody,
  Icon,
  Picker,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { CubeTitle, IdType, OptionList, Relationship } from 'types/optionList';
import { NominationFormSchemaType } from 'features/eApp/validations/nominationDetailsSchema';
import { calculateAge } from 'utils/helper/calculateAge';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { TouchableOpacity, ViewStyle } from 'react-native';
import { capitalizeFirstLetterOfEachWord } from 'utils';
import { convertNewNRICtoDOBAndGender } from 'features/eApp/utils/idNumberUtils';
import { useGetTitleList } from 'hooks/useGetOptionList';
import { Gender } from 'types/person';
import NominationAddressInformationForm from './NominationAddressInformationForm';
import IdNumberField from 'components/IdNumberField';
import { NEW_NRIC } from 'constants/optionList';
import NameField from 'components/NameField';
import Autocomplete from 'components/Autocomplete';
import DatePickerCalendar from 'components/DatePickerCalendar';
const { defaultDate, maxDate, minDate } = getDateOfBirthDropdownProps();

interface Props {
  control: Control<NominationFormSchemaType>;
  index: number;
  onDelete?: () => void;
  isLatestItem: boolean;
  optionList: OptionList | undefined;
  isLoadingOptionList: boolean;
  selectedHibah: boolean;
  setValue: UseFormSetValue<NominationFormSchemaType>;
  getValues: UseFormGetValues<NominationFormSchemaType>;
  trigger: UseFormTrigger<NominationFormSchemaType>;
}

export type NominationFormRef = { expand?: () => void };

const NominationForm = forwardRef<NominationFormRef, Props>(
  (props: Props, ref: React.ForwardedRef<NominationFormRef>) => {
    const {
      control,
      index,
      onDelete,
      isLatestItem,
      optionList,
      isLoadingOptionList,
      selectedHibah,
      setValue,
      getValues,
      trigger,
    } = props;
    const { t } = useTranslation(['eApp']);
    const { space, colors } = useTheme();
    const [isCollapsed, setIsCollapsed] = useState(true);
    const [personalAge, setPersonalAge] = useState<string>('');

    const { maleTitles: MALES } = useGetTitleList(
      optionList?.CUBE_TITLE.options ?? [],
    );

    const personalDob = useWatch({
      name: `information.${index}.dob`,
      control: control,
    });

    const {
      field: { onChange: onChangeDob },
    } = useController({
      name: `information.${index}.dob`,
      control,
    });

    const personalPrimaryIdType = useWatch({
      name: `information.${index}.primaryIdType`,
      control: control,
    });

    const personalIdentificationNumber = useWatch({
      name: `information.${index}.identificationNumber`,
      control: control,
    });

    const {
      field: { onChange: onChangeFullName },
    } = useController({
      name: `information.${index}.fullName`,
      control,
    });

    const personalTitle = useWatch({
      name: `information.${index}.title`,
      control: control,
    });

    const {
      field: { onChange: onChangeGender },
    } = useController({
      name: `information.${index}.gender`,
      control,
    });

    const isNricNew = personalPrimaryIdType === NEW_NRIC;

    useEffect(() => {
      if (isNricNew) {
        const { dateOfBirth } = convertNewNRICtoDOBAndGender(
          personalIdentificationNumber,
        );
        if (dateOfBirth) {
          onChangeDob(dateOfBirth);
        }
      }
    }, [isNricNew, personalIdentificationNumber]);

    useEffect(() => {
      if (personalDob) {
        setPersonalAge(`${calculateAge(new Date(personalDob))}`);
      } else {
        setPersonalAge('');
      }
    }, [personalDob]);

    useEffect(() => {
      if (personalTitle) {
        onChangeGender(
          MALES.findIndex(e => e?.value === personalTitle) !== -1
            ? Gender.MALE
            : Gender.FEMALE,
        );
      }
    }, [personalTitle]);

    useEffect(() => {
      setIsCollapsed(isLatestItem ? false : true);
    }, [isLatestItem]);

    useEffect(() => {
      if (personalDob) {
        setPersonalAge(`${calculateAge(new Date(personalDob))}`);
      }
    }, [personalDob]);

    const expandForm = () => {
      setIsCollapsed(false);
    };

    useImperativeHandle(ref, () => ({
      expand: expandForm,
    }));

    const relationships = useMemo(() => {
      console.log('run 6');
      return (
        (optionList?.RELATIONSHIP.options ?? []) as Relationship<string, 'my'>[]
      ).filter(r => r.role.beneficiary === 'True' && r.isShow !== 'False');
    }, [optionList?.RELATIONSHIP.options]);

    const [disabled, setDisabled] = useState(true);
    useEffect(() => {
      console.log('run 7');
      setTimeout(() => setDisabled(false), 200);
    }, []);

    return (
      <Box
        bgColor={colors.background}
        p={space[6]}
        borderRadius={space[4]}
        maxHeight={isCollapsed ? 88 : undefined}
        overflow="hidden"
        mt={space[4]}>
        <Row alignItems="center" justifyContent="space-between">
          <Row alignItems="center">
            <PictogramIcon.Lanyard size={space[10]} />
            <Box w={space[2]} />
            <ExtraLargeBody fontWeight="bold">
              {selectedHibah
                ? t('eApp:other.nominationDetails.hibah')
                : t('eApp:other.nominationDetails.wasi')}{' '}
              {index + 1}
            </ExtraLargeBody>
          </Row>
          <Row>
            {onDelete && (
              <TouchableOpacity onPress={onDelete}>
                <Box pl={space[8]}>
                  <Icon.Delete size={space[6]} fill={colors.secondary} />
                </Box>
              </TouchableOpacity>
            )}

            <TouchableOpacity onPress={() => setIsCollapsed(!isCollapsed)}>
              <Box pl={space[8]}>
                {isCollapsed ? (
                  <Icon.ChevronDown size={space[6]} />
                ) : (
                  <Icon.ChevronUp size={space[6]} />
                )}
              </Box>
            </TouchableOpacity>
          </Row>
        </Row>

        <Content>
          {/* 1 */}
          <RowBox pt={space[1]}>
            <Input
              control={control}
              as={Autocomplete<CubeTitle, string>}
              name={`information.${index}.title`}
              label={t('eApp:certificate.form.title')}
              data={optionList?.CUBE_TITLE.options ?? []}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              disabled={isLoadingOptionList || disabled}
              shouldUnregister={false}
              style={{ flex: 1, marginTop: 7 }}
            />
            <Input
              control={control}
              as={NameField}
              name={`information.${index}.fullName`}
              label={t('eApp:certificate.form.fullName')}
              shouldUnregister={false}
              style={{ flex: 1, marginTop: 7 }}
            />
          </RowBox>
          {/* 2 */}
          <RowBox>
            <Input
              control={control}
              as={Autocomplete<IdType, string>}
              name={`information.${index}.primaryIdType`}
              label={t('eApp:certificate.form.primaryIdType')}
              data={optionList?.ID_TYPE.options ?? []}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldUnregister={false}
              style={{ flex: 1, marginTop: 7 }}
            />
            <Input
              control={control}
              as={IdNumberField}
              idType={personalPrimaryIdType}
              name={`information.${index}.identificationNumber`}
              label={t('eApp:certificate.form.identificationNumber')}
              shouldUnregister={false}
              style={{ flex: 1, marginTop: 7 }}
            />
          </RowBox>

          {/* 3 */}
          <RowBox>
            <Input
              control={control}
              as={Picker}
              name={`information.${index}.gender`}
              label={t('eApp:gender')}
              disabled
              items={optionList?.GENDER.options.map(o => ({
                value: o.value,
                text: o.label,
              }))}
              style={{ flex: 1, marginTop: 7 }}
              shouldUnregister={false}
            />
            <Row flex={1} mt={7} gap={space[3]}>
              <Input
                control={control}
                as={DatePickerCalendar}
                name={`information.${index}.dob`}
                label={t('eApp:certificate.form.dateOfBirth')}
                hint={t('eApp:dateFormat')}
                defaultDate={defaultDate}
                minDate={minDate}
                maxDate={maxDate}
                formatDate={val => (val ? dateFormatUtil(val) : '')}
                disabled={isNricNew || disabled}
                shouldUnregister={false}
                style={{ flex: 4 }}
              />
              <TextField
                label={t('eApp:certificate.form.age')}
                value={personalAge}
                disabled
                inputStyle={
                  { color: colors.palette.fwdGreyDarker } as ViewStyle
                }
                style={{ flex: 1.5 }}
              />
            </Row>
          </RowBox>

          {/* 4 */}
          <RowBox>
            <Input
              control={control}
              as={Autocomplete<Relationship<string, 'my'>, string>}
              label={t('eApp:certificate.form.relationshipWithCertificate')}
              name={`information.${index}.relationship`}
              data={relationships}
              disabled={isLoadingOptionList || disabled}
              getItemValue={item => item.value}
              getItemLabel={item =>
                item.label[0].toUpperCase() + item.label.substring(1)
              }
              shouldUnregister={false}
              style={{ flex: 1, marginTop: 7 }}
            />
            <Column flex={1} />
          </RowBox>
        </Content>

        <Content>
          <NominationAddressInformationForm
            control={control}
            setValue={setValue}
            getValues={getValues}
            trigger={trigger}
            index={index}
            disabled={disabled}
          />
        </Content>
      </Box>
    );
  },
);
export default NominationForm;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  marginTop: space[5],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  flex: 1,
  marginTop: space[5],
  gap: space[6],
}));
