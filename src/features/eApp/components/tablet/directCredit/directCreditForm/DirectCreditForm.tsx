import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, Column, Row, TextField, Typography } from 'cube-ui-components';
import {
  BankDirectForm,
  bankDirectSchema,
} from 'features/eApp/validations/directCreditValidation';
import {
  ForwardedRef,
  RefObject,
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { DirectCreditTermConditionModal } from '../modals/DirectCreditTermConditionModal';
import useToggle from 'hooks/useToggle';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { useYupResolver } from 'utils/validation/useYupResolver';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { toDirectCredit } from 'features/eApp/utils/caseUtils.my';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { DCBankList } from 'types/optionList';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useCheckEntity } from 'features/eApp/hooks/useCheckEntity';
import AutocompletePopup from 'components/AutocompletePopup';

export type DirectCreditDetailsRef = { focusIncompleteField?: () => void };

const DirectCreditDetails = memo(
  forwardRef(function DirectCreditDetails(
    {
      updateValid,
      updateHandleSubmit,
      updateTotalIncompleteFields,
      scrollRef,
    }: {
      updateValid: (value: boolean) => void;
      updateHandleSubmit: (cb: () => Promise<void>) => void;
      updateTotalIncompleteFields: (total: number) => void;
      scrollRef: RefObject<KeyboardAwareScrollView>;
    },
    ref: ForwardedRef<DirectCreditDetailsRef>,
  ) {
    const { space, colors, sizes } = useTheme();
    const { t } = useTranslation(['eApp']);
    const { data: optionList, isFetching: isLoadingOptionList } =
      useGetOptionList();
    const isEntity = useCheckEntity();
    //For non-bank-direct cases
    const [pickedIndex, setPickedIndex] = useState<number>(0);
    const [termConditionVisible, showTermCondition, hideTermCondition] =
      useToggle(false);
    const my_policyOwnerPersonalInfo = useEAppStore(
      state => state.my_policyOwnerPersonalInfo,
    );
    const my_companyInfo = useEAppStore(state => state.my_companyInfo);
    const my_directCredit = useEAppStore(state => state.my_directCredit);

    const [bankDirectDetail, updateBankDirectDetail] = useState<BankDirectForm>(
      {
        ...my_directCredit,
        paymentMethod: 'D',
        accountHolderName: isEntity
          ? my_companyInfo.companyName
          : my_policyOwnerPersonalInfo.fullName,
      },
    );

    const resolverBankDirect = useYupResolver(bankDirectSchema);
    const {
      setValue: setBankDirectValue,
      control: bankDirectFormControl,
      handleSubmit: handleBankDirectFormSubmit,
      formState: { isValid: isBankDirectValid },
      watch: watchBankDirect,
    } = useForm<BankDirectForm>({
      mode: 'onBlur',
      defaultValues: bankDirectDetail,
      resolver: resolverBankDirect,
    });

    useEffect(() => {
      if (isEntity) {
        setBankDirectValue('accountHolderName', my_companyInfo.companyName, {
          shouldValidate: true,
          shouldTouch: true,
        });
      }
    }, [isEntity, my_companyInfo.companyName, setBankDirectValue]);

    const agentId = useBoundStore(state => state.auth.agentCode);
    const caseId = useBoundStore(state => state.case.caseId);
    const { data: caseObj } = useGetCase(caseId);
    const { mutateAsync: saveApplication } = useCreateApplication();
    const handleSubmit = useCallback(async () => {
      if (pickedIndex === 0) {
        if (!caseId || !caseObj || !agentId) return;
        await handleBankDirectFormSubmit(async data => {
          await saveApplication({
            caseId,
            data: {
              ...caseObj?.application,
              directCredit: toDirectCredit(data, agentId),
            },
          });
          updateBankDirectDetail(data);
        })();
      }
    }, [
      pickedIndex,
      caseId,
      caseObj,
      agentId,
      handleBankDirectFormSubmit,
      saveApplication,
    ]);

    useEffect(() => {
      updateHandleSubmit(handleSubmit);
    }, [handleSubmit, updateHandleSubmit]);

    useEffect(() => {
      const isValid = pickedIndex === 0 && isBankDirectValid;
      updateValid(isValid);
    }, [pickedIndex, isBankDirectValid, updateValid]);

    const control = bankDirectFormControl;

    const paymentTypes = useMemo(() => {
      return [
        {
          value: 'bankDirect',
          label: 'Bank direct',
          icon: <BankDirectSVG />,
        },
      ];
    }, [t]);

    const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
      useIncompleteFields({
        control: bankDirectFormControl,
        watch: watchBankDirect,
        schema: bankDirectSchema,
        scrollRef,
        scrollTo: option =>
          scrollRef?.current?.scrollToPosition(
            0,
            option.y || 0,
            option.animated,
          ),
      });

    useEffect(() => {
      updateTotalIncompleteFields(totalIncompleteRequiredFields);
    }, [totalIncompleteRequiredFields, updateTotalIncompleteFields]);

    useImperativeHandle(ref, () => ({
      focusIncompleteField: focusOnNextIncompleteField,
    }));

    return (
      <Content>
        <Row alignItems="center">
          <CreditCard />
          <Box w={space[2]} />
          <Typography.H6 fontWeight="bold" color="#333333">
            {t('eApp:directCredit.menu')}
          </Typography.H6>
        </Row>
        <Box mt={sizes[6]} mb={sizes[3]}>
          <Row alignItems="center">
            <Typography.LargeLabel
              style={{ textAlign: 'center', marginRight: space[6] }}
              fontWeight="bold">
              {`${t('eApp:certificate.form.paymentMethod')}: `}
            </Typography.LargeLabel>
            <Row>
              {paymentTypes.map((type, index) => (
                <PaymentTypeItemContainer
                  key={type.value}
                  style={{
                    marginLeft: index > 0 ? space[6] : 0,
                  }}>
                  {type.icon}
                  <Box
                    h={sizes[10]}
                    alignItems="center"
                    justifyContent="center">
                    <Typography.H7
                      style={{ textAlign: 'center' }}
                      fontWeight="bold">
                      {type.label}
                    </Typography.H7>
                  </Box>
                </PaymentTypeItemContainer>
              ))}
            </Row>
          </Row>
        </Box>
        <RowBox>
          <Input
            control={control}
            as={AutocompletePopup<DCBankList, string>}
            name="bankName"
            label={t('eApp:directCredit.form.bankName')}
            data={optionList?.DC_BANKLIST.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
            searchable
          />
          <Input
            control={control}
            as={TextField}
            name="accountNumber"
            label={t('eApp:directCredit.form.accountNumber')}
            style={{ flex: 1 }}
            keyboardType="number-pad"
            returnKeyType="done"
          />
        </RowBox>
        <Box h={space[6]} />
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="accountHolderName"
            label={t('eApp:directCredit.form.accountHolderName')}
            style={{ flex: 1 }}
            disabled
          />
          <Box flex={1} />
        </RowBox>
        <Box h={space[6]} />
        <TouchableOpacity hitSlop={8} onPress={showTermCondition}>
          <Row alignItems="center">
            <Typography.H7
              color={colors.primary}
              style={{ textDecorationLine: 'underline' }}>
              {t('eApp:directCredit.viewTerm')}
            </Typography.H7>
          </Row>
        </TouchableOpacity>
        <DirectCreditTermConditionModal
          visible={termConditionVisible}
          onDismiss={hideTermCondition}
        />
      </Content>
    );
  }),
);
export default DirectCreditDetails;

const Content = styled(View)(({ theme: { space, colors, borderRadius } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  borderRadius: borderRadius.large,
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[5],
  flex: 1,
  marginTop: space[2],
}));

const PaymentTypeItemContainer = styled.View(({ theme: { sizes } }) => ({
  alignItems: 'center',
  flexDirection: 'row',
  marginLeft: sizes[6],
}));

const CreditCard = memo(() => {
  const { sizes } = useTheme();
  return (
    <Svg width={sizes[10]} height={sizes[10]} fill="none">
      <Path
        fill="#E87722"
        fillRule="evenodd"
        d="M32.557 10.714c.343 0 .686.143.971.4.258.257.4.629.4.972v15.828c0 .343-.142.714-.4.972-.257.257-.628.4-.971.4H7.443c-.343 0-.686-.143-.972-.4a1.411 1.411 0 0 1-.4-.972V12.086c0-.343.143-.686.4-.972.257-.257.629-.4.972-.4h25.114Z"
        clipRule="evenodd"
      />
      <Path
        fill="#fff"
        fillRule="evenodd"
        d="M13.442 26.628h-3.428a.451.451 0 0 1-.457-.457c0-.257.2-.457.457-.457h3.428c.258 0 .457.2.457.457a.47.47 0 0 1-.457.457ZM18.957 26.628h-3.429a.451.451 0 0 1-.457-.457c0-.257.2-.457.457-.457h3.429c.257 0 .457.2.457.457a.47.47 0 0 1-.457.457ZM24.47 26.628h-3.428a.451.451 0 0 1-.457-.457c0-.257.2-.457.457-.457h3.429c.257 0 .457.2.457.457a.47.47 0 0 1-.457.457ZM29.985 26.628h-3.4a.451.451 0 0 1-.457-.457c0-.257.2-.457.457-.457h3.4c.257 0 .457.2.457.457.029.229-.2.457-.457.457ZM18.957 23.029h-8.943a.451.451 0 0 1-.457-.458v-2c0-.257.2-.457.457-.457h8.943c.257 0 .457.2.457.457v2c0 .257-.2.457-.457.457Z"
        clipRule="evenodd"
      />
      <Path fill="#183028" d="M33.928 13.428H6.071v3.943h27.857V13.43Z" />
      <Path
        fill="#fff"
        fillRule="evenodd"
        d="M27.557 21.543a2.17 2.17 0 1 1-4.343 0 2.17 2.17 0 1 1 4.343 0Z"
        clipRule="evenodd"
      />
      <Path
        fill="#F3BB90"
        fillRule="evenodd"
        d="M30.472 21.543a2.17 2.17 0 1 1-4.343 0 2.17 2.17 0 1 1 4.343 0Z"
        clipRule="evenodd"
      />
    </Svg>
  );
});

const BankDirectSVG = memo(() => {
  const { sizes } = useTheme();
  return (
    <Svg width={sizes[9]} height={sizes[9]} fill="none">
      <Path
        fill="#F3BB90"
        fillRule="evenodd"
        d="M28.707 27.872H6.043a.687.687 0 0 1-.686-.687v-.003c0-.381.309-.688.686-.688h22.664c.38 0 .686.31.686.688v.003a.687.687 0 0 1-.686.687Z"
        clipRule="evenodd"
      />
      <Path
        fill="#E87722"
        fillRule="evenodd"
        d="M29.564 29.25H5.186a.689.689 0 0 1 0-1.378h24.378c.38 0 .686.31.686.688 0 .381-.306.69-.686.69ZM29.621 16.195H5.13a.629.629 0 0 1-.629-.63v-.115c0-.349.28-.63.629-.63H29.62c.348 0 .629.281.629.63v.115c0 .348-.28.63-.629.63Z"
        clipRule="evenodd"
      />
      <Path fill="#E87722" d="M8.921 16.195H7.032v10.3h1.889v-10.3Z" />
      <Path
        fill="#183028"
        fillRule="evenodd"
        d="M9.083 17.578H6.87a.27.27 0 0 1-.268-.27v-.846c0-.147.119-.27.268-.27h2.212a.27.27 0 0 1 .268.27v.847c0 .15-.119.269-.268.269Z"
        clipRule="evenodd"
      />
      <Path fill="#E87722" d="M15.187 16.195h-1.89v10.3h1.89v-10.3Z" />
      <Path
        fill="#183028"
        fillRule="evenodd"
        d="M15.348 17.578h-2.211a.27.27 0 0 1-.269-.27v-.846c0-.147.12-.27.269-.27h2.211a.27.27 0 0 1 .269.27v.847a.27.27 0 0 1-.269.269Z"
        clipRule="evenodd"
      />
      <Path fill="#E87722" d="M21.452 16.195h-1.889v10.3h1.89v-10.3Z" />
      <Path
        fill="#183028"
        fillRule="evenodd"
        d="M21.613 17.578h-2.212a.27.27 0 0 1-.268-.27v-.846c0-.147.12-.27.268-.27h2.212a.27.27 0 0 1 .268.27v.847c0 .15-.119.269-.268.269Z"
        clipRule="evenodd"
      />
      <Path fill="#E87722" d="M27.72 16.195H25.83v10.3h1.889v-10.3Z" />
      <Path
        fill="#183028"
        fillRule="evenodd"
        d="M27.879 17.578h-2.212a.27.27 0 0 1-.269-.27v-.846c0-.147.12-.27.269-.27h2.212a.27.27 0 0 1 .268.27v.847c0 .15-.12.269-.268.269Z"
        clipRule="evenodd"
      />
      <Path
        fill="#E87722"
        fillRule="evenodd"
        d="M16.733 6.952 5.357 14.82h24.036L18.017 6.952a1.123 1.123 0 0 0-1.284 0Z"
        clipRule="evenodd"
      />
    </Svg>
  );
});
