import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import { InactivateEMandateRequest } from 'api/ipay88Api';
import { fwdLoadingAnimation } from 'assets/images';
import { Box, Button, Center, LargeBody, Row } from 'cube-ui-components';
import SubmitClaim from 'features/eApp/assets/SubmitClaim';
import { useEAppSubmitApplication } from 'features/eApp/hooks/useEAppSubmitApplication';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useSubmitInactivateEMandate } from 'hooks/useIpay88Gateway';
import NavHeader from 'navigation/components/NavHeader';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Image } from 'react-native';
import { RootStackParamList } from 'types';
import { shallow } from 'zustand/shallow';

const SubmissionFailed = () => {
  const { space, sizes, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { alertError } = useAlert();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { mutateAsync: submitInactivateEMandate } =
    useSubmitInactivateEMandate();

  const submitEMandate = async (paymentTransactionId: string) => {
    const requestData: InactivateEMandateRequest = {
      paymentTransactionId,
    };
    try {
      await submitInactivateEMandate(requestData);
    } finally {
      navigation.dispatch(StackActions.replace('Submission'));
    }
  };

  const { my_renewalPaymentStatusResponse } = useEAppStore(
    state => ({
      my_renewalPaymentStatusResponse: state.my_renewalPaymentStatusResponse,
      my_renewalPaymentLinkResponse: state.my_renewalPaymentLinkResponse,
      my_renewalPaymentForm: state.my_renewalPaymentForm,
    }),
    shallow,
  );

  const { mutateAsync: submitApplication, isLoading: isSubmittingApplication } =
    useEAppSubmitApplication();

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  useEffect(() => {
    if (isSubmittingApplication) {
      setAppLoading();
    } else {
      setAppIdle();
    }
  }, [isSubmittingApplication, setAppIdle, setAppLoading]);

  return (
    <>
      <NavHeader title={t('eApp:app')} />
      <Center flex={1}>
        <Box maxWidth={572}>
          <Row
            alignContent="center"
            alignItems="center"
            justifyContent="center"
            mb={space[4]}>
            <SubmitClaim />
          </Row>
          <Message>{t('eApp:application.submit.failed')}</Message>
          <Row
            justifyContent="space-between"
            mt={space[12]}
            px={space[7]}
            gap={space[4]}>
            <ActionButton
              variant="secondary"
              text={t('back')}
              onPress={navigation.goBack}
              size="medium"
            />
            <ActionButton
              text={t('eApp:application.submit.again')}
              onPress={() => {
                submitApplication({
                  onSuccess: async () => {
                    if (my_renewalPaymentStatusResponse?.paymentTransactionId) {
                      await submitEMandate(
                        my_renewalPaymentStatusResponse?.paymentTransactionId,
                      );
                    } else {
                      navigation.navigate('Submission');
                    }
                  },
                  onFailure: () => {
                    alertError(t('eApp:submission.failed'));
                  },
                });
              }}
              size="medium"
            />
          </Row>
        </Box>
      </Center>
    </>
  );
};

export default SubmissionFailed;

const Message = styled(LargeBody)(({ theme: { colors } }) => ({
  alignSelf: 'center',
  textAlign: 'center',
  color: colors.placeholder,
}));

const ActionButton = styled(Button)({
  flex: 1,
});
