import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';
const PaymentSuccessIcon = (props: SvgIconProps) => (
  <Svg width={80} height={80} fill="none" {...props}>
    <G clip-path="url(#clip0_21197_11639)">
      <Path
        d="M57.6302 5.24138L52.4033 0L47.162 5.24138L41.9351 0L36.7082 5.24138L31.4669 0L26.24 5.24138L21.0131 0L15.7718 5.24138L10.5449 0L5.31804 5.24138L0.0766602 0V80L5.31804 74.7586L10.5449 80L15.7718 74.7586L21.0131 80L26.24 74.7586L31.4669 80L36.7082 74.7586L41.9351 80L47.162 74.7586L52.4033 80L57.6302 74.7586L62.8571 80V0L57.6302 5.24138Z"
        fill="#E87722"
      />
      <Path
        d="M21.2019 39.2422H6.11658V42.2621H21.2019V39.2422Z"
        fill="#F3BB91"
      />
      <Path
        d="M21.2019 45.2812H6.11658V48.3012H21.2019V45.2812Z"
        fill="#F3BB91"
      />
      <Path
        d="M42.0367 9.05859H27.2418V12.0786H42.0367V9.05859Z"
        fill="#183028"
      />
      <Path
        d="M56.8171 15.0977H27.2418V18.1176H56.8171V15.0977Z"
        fill="white"
      />
      <Path d="M56.8171 21.125H27.2418V24.145H56.8171V21.125Z" fill="white" />
      <Path d="M56.8171 27.1641H27.2418V30.184H56.8171V27.1641Z" fill="white" />
      <Path
        d="M56.8171 33.2031H27.2418V36.2231H56.8171V33.2031Z"
        fill="white"
      />
      <Path
        d="M56.8171 39.2422H27.2418V42.2621H56.8171V39.2422Z"
        fill="white"
      />
      <Path
        d="M56.8171 45.2852H27.2418V48.3051H56.8171V45.2852Z"
        fill="white"
      />
      <Path
        d="M46.5667 54.3281H16.3816V57.3481H46.5667V54.3281Z"
        fill="#F3BB91"
      />
      <Path
        d="M39.0168 60.3672H23.9315V63.3871H39.0168V60.3672Z"
        fill="#F3BB91"
      />
      <Path
        d="M6.05855 69.4297H3.06763V70.9397H6.05855V69.4297Z"
        fill="white"
      />
      <Path
        d="M12.0404 69.4297H9.04944V70.9397H12.0404V69.4297Z"
        fill="white"
      />
      <Path
        d="M18.0223 69.4297H15.0314V70.9397H18.0223V69.4297Z"
        fill="white"
      />
      <Path
        d="M24.0041 69.4297H21.0132V70.9397H24.0041V69.4297Z"
        fill="white"
      />
      <Path
        d="M29.9714 69.4297H26.9805V70.9397H29.9714V69.4297Z"
        fill="white"
      />
      <Path
        d="M35.9533 69.4297H32.9624V70.9397H35.9533V69.4297Z"
        fill="white"
      />
      <Path
        d="M41.9351 69.4297H38.9442V70.9397H41.9351V69.4297Z"
        fill="white"
      />
      <Path d="M47.917 69.4297H44.926V70.9397H47.917V69.4297Z" fill="white" />
      <Path d="M53.8989 69.4297H50.908V70.9397H53.8989V69.4297Z" fill="white" />
      <Path
        d="M59.8807 69.4297H56.8898V70.9397H59.8807V69.4297Z"
        fill="white"
      />
      <Path
        d="M12.6357 33.3787V30.4168C11.6774 30.2861 10.8353 30.0248 10.1094 29.6328C9.38342 29.2407 8.78814 28.7906 8.30901 28.2534C7.82988 27.7308 7.45239 27.15 7.19104 26.5402C6.9297 25.9159 6.75547 25.3206 6.71191 24.7253L10.1529 23.8977C10.1965 24.3478 10.2981 24.7689 10.4723 25.1609C10.6466 25.5529 10.8934 25.9159 11.2128 26.2353C11.5322 26.5547 11.9097 26.8015 12.3743 26.9903C12.839 27.179 13.3907 27.2662 14.044 27.2662C14.9442 27.2662 15.6557 27.0774 16.1493 26.6854C16.6429 26.2934 16.9043 25.7997 16.9043 25.1899C16.9043 24.7108 16.7301 24.3043 16.3961 23.9558C16.0477 23.6073 15.525 23.375 14.8281 23.2153L12.2727 22.6636C10.7772 22.3297 9.58669 21.7054 8.71554 20.8052C7.8444 19.905 7.40883 18.787 7.40883 17.4513C7.40883 16.7108 7.5395 16.0284 7.81536 15.4041C8.0767 14.7798 8.4542 14.2135 8.91881 13.7344C9.38342 13.2553 9.94966 12.8487 10.5885 12.5293C11.2273 12.2099 11.9097 11.9921 12.6502 11.876V8.91406H15.4814V11.9631C16.28 12.1083 16.9624 12.3551 17.5431 12.689C18.1239 13.023 18.6175 13.4005 19.0096 13.8215C19.4016 14.2426 19.721 14.7072 19.9533 15.2008C20.1856 15.6945 20.3453 16.1736 20.4324 16.6237L17.0204 17.5965C16.9769 17.3496 16.9043 17.0883 16.7881 16.7979C16.672 16.5075 16.4832 16.2171 16.2364 15.9558C15.9896 15.6945 15.6702 15.4622 15.2927 15.2879C14.9152 15.1137 14.4361 15.0266 13.8698 15.0266C12.9841 15.0266 12.3018 15.2444 11.8371 15.6799C11.3725 16.1155 11.1257 16.6237 11.1257 17.1899C11.1257 17.611 11.2854 17.9885 11.5903 18.3224C11.9097 18.6563 12.3889 18.9032 13.0277 19.0484L15.554 19.6291C17.3108 20.0211 18.603 20.689 19.4161 21.6473C20.2292 22.6055 20.6357 23.7235 20.6357 24.9721C20.6357 25.6255 20.5195 26.2353 20.3018 26.8306C20.084 27.4259 19.75 27.9631 19.2999 28.4567C18.8498 28.9504 18.3126 29.3569 17.6738 29.7054C17.035 30.0538 16.2945 30.2861 15.4669 30.4168V33.4077H12.6357V33.3787Z"
        fill="#183028"
      />
      <Path
        d="M61.7789 64.0919C71.3268 64.0919 79.067 56.3517 79.067 46.8038C79.067 37.2558 71.3268 29.5156 61.7789 29.5156C52.2309 29.5156 44.4907 37.2558 44.4907 46.8038C44.4907 56.3517 52.2309 64.0919 61.7789 64.0919Z"
        fill="#F3BB91"
      />
      <Path
        d="M61.9097 64.6602C71.8508 64.6602 79.9097 56.6013 79.9097 46.6602C79.9097 36.719 71.8508 28.6602 61.9097 28.6602C51.9685 28.6602 43.9097 36.719 43.9097 46.6602C43.9097 56.6013 51.9685 64.6602 61.9097 64.6602Z"
        fill="#F69E60"
      />
      <Path
        d="M70.7426 42.3464C71.0622 41.9977 71.2656 41.5328 71.2656 41.0244C71.2656 39.9203 70.3649 39.0195 69.2608 39.0195C68.7523 39.0195 68.3019 39.2229 67.9387 39.5425H67.9242L67.837 39.6006H67.8225L59.2801 48.1575L55.7208 44.6127H55.7063L55.6627 44.5546H55.6482C55.285 44.235 54.8201 44.0171 54.2826 44.0171C53.1784 44.0171 52.2777 44.9178 52.2777 46.022C52.2777 46.5595 52.4956 47.0244 52.8298 47.3876H52.8152L59.2656 53.8525L70.6554 42.4481L70.7426 42.3609V42.3464Z"
        fill="#183028"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_21197_11639">
        <Rect width="80" height="80" fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default PaymentSuccessIcon;
