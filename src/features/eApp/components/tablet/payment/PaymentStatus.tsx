import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Body, Box, Button, H6, H7, Row } from 'cube-ui-components';
import PaymentFailIcon from 'features/eApp/components/tablet/payment/icons/PaymentFailIcon';
import EAppFooter from '../common/EAppFooter';
import {
  IPay88PaymentStatus,
  PaymentGatewayStatusResponse,
} from 'api/ipay88Api';
import PaymentSuccessIcon from './icons/PaymentSuccessIcon';
import { PaymentMethod } from './PaymentMethods';
import { useEffect, useRef, useState } from 'react';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { shallow } from 'zustand/shallow';
import { useEAppSubmitApplication } from 'features/eApp/hooks/useEAppSubmitApplication';
import { useAlert } from 'hooks/useAlert';
import { useGetDefaultDebitAmount } from 'features/eApp/hooks/useDebitAmount';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { getPaymentGatewayCode } from './PaymentSubmission';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { CHANNELS } from 'types/channel';
import { PaymentMode } from 'types/proposal';

const mockSuccessPayment = (
  paymentMethod: PaymentMethod | null | undefined,
  channel: string,
  paymentTransactionId: string,
) =>
  ({
    paymentTransactionId: paymentTransactionId,
    paymentStatus: IPay88PaymentStatus.RECEIVE_SUCCEED,
    paymentGatewayCode: getPaymentGatewayCode(paymentMethod, channel),
    paymentGatewayReturnDetails: {
      payloadMap: {
        amount: '1.00',
        authCode: '128631',
        bindCardErrDesc: null,
        ccName: 'Payment By Pass',
        ccNo: '123412xxxxxx1234',
        checkMessage: null,
        checkStatus: null,
        currency: 'MYR',
        digest: null,
        errDesc: null,
        merchantCode: null,
        message: null,
        paymentId: '2',
        refNo: '2023120406292309a2e9',
        bankName: null,
        country: null,
        status: '1',
        tokenId: '09a2e9966a8640668f85',
        transId: 'T20222023120406292309a2e9',
        txnId: null,
        type: null,
        billingAmount: '2759.49',
        channel: paymentMethod,
      },
    },
  } as PaymentGatewayStatusResponse);

const mockFailurePayment = (
  paymentMethod: PaymentMethod | null | undefined,
  channel: string,
  paymentTransactionId: string,
) =>
  ({
    paymentTransactionId: paymentTransactionId,
    paymentStatus: IPay88PaymentStatus.RECEIVE_FAILED,
    paymentGatewayCode: getPaymentGatewayCode(paymentMethod, channel),
    paymentGatewayReturnDetails: {
      payloadMap: {
        amount: '1.00',
        authCode: '128631',
        bindCardErrDesc: null,
        ccName: 'Payment By Pass',
        ccNo: '123412xxxxxx1234',
        checkMessage: null,
        checkStatus: null,
        currency: 'MYR',
        digest: null,
        errDesc: null,
        merchantCode: null,
        message: null,
        paymentId: '2',
        refNo: '2023120406292309a2e9',
        bankName: null,
        country: null,
        status: '1',
        tokenId: '09a2e9966a8640668f85',
        transId: 'T20222023120406292309a2e9',
        txnId: null,
        type: null,
        billingAmount: '2759.49',
        channel: 'CC',
      },
    },
  } as PaymentGatewayStatusResponse);

const MAX_DIRECT_DEBIT_AMOUNT = 30000;

interface PaymentStatusProps {
  info: PaymentGatewayStatusResponse;
  paymentMethod?: PaymentMethod | null;
  onBackPress: () => Promise<void>;
  onStartEMandate: () => void;
}

const PaymentStatus = (props: PaymentStatusProps) => {
  const { colors, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { alertError } = useAlert();
  const [isGoingBack, setIsGoingBack] = useState(false);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const my_mockPaymentResult = useEAppStore(
    state => state.my_mockPaymentResult,
  );
  const { my_paymentLinkResponse } = useEAppStore(
    state => ({
      my_paymentLinkResponse: state.my_paymentLinkResponse,
    }),
    shallow,
  );
  const channel = useGetCubeChannel();

  // TODO, info comes from props, which api succeed
  const isFpxPayment = props.paymentMethod === PaymentMethod.FPX;
  let info = props.info;
  if (my_mockPaymentResult === 'success') {
    info = mockSuccessPayment(
      props.paymentMethod,
      channel,
      info.paymentTransactionId,
    );
  } else if (my_mockPaymentResult === 'failed') {
    info = mockFailurePayment(
      props.paymentMethod,
      channel,
      info.paymentTransactionId,
    );
  }
  const isSuccess = info?.paymentStatus === IPay88PaymentStatus.RECEIVE_SUCCEED;

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { mutateAsync: submitApplication, isLoading: isSubmittingApplication } =
    useEAppSubmitApplication();
  useEffect(() => {
    if (isSavingApplication || isSubmittingApplication) {
      setAppLoading();
    } else {
      setAppIdle();
    }
    return () => {
      setAppIdle();
    };
  }, [isSavingApplication, isSubmittingApplication, setAppIdle, setAppLoading]);

  const onGoBack = () => {
    setIsGoingBack(true);
    props.onBackPress().finally(() => setIsGoingBack(false));
  };

  const hasDoneInitialSaving = useRef(false);
  useEffect(() => {
    if (!caseId || !caseObj || hasDoneInitialSaving.current) return;
    saveApplication({
      caseId,
      data: {
        ...caseObj.application,
        paymentMethod: props.paymentMethod || '',
        initialPayment: {
          ...my_paymentLinkResponse,
          paymentMethod: props.paymentMethod || '',
          eMandate: isFpxPayment,
          paymentResponse: info,
          paymentDate: isSuccess
            ? (
                caseObj.application?.initialPayment as {
                  paymentDate?: string | null;
                }
              )?.paymentDate || new Date().toISOString()
            : null,
        },
      },
    }).then(() => (hasDoneInitialSaving.current = true));
  }, [
    caseId,
    caseObj,
    info,
    isFpxPayment,
    isSuccess,
    my_paymentLinkResponse,
    props.paymentMethod,
    saveApplication,
  ]);

  const quotation = useSelectedQuotation();
  const defaultDebitAmount = useGetDefaultDebitAmount(quotation);
  const isBanca = channel === CHANNELS.BANCA;

  const hasEMandate =
    isFpxPayment &&
    isSuccess &&
    Number(defaultDebitAmount) <= MAX_DIRECT_DEBIT_AMOUNT &&
    !(isBanca && quotation?.basicInfo?.paymentMode === PaymentMode.SINGLE);

  const onSubmit = async () => {
    // save initial payment info
    if (!caseId || !caseObj) return;
    try {
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          paymentMethod: props.paymentMethod || '',
          initialPayment: {
            ...my_paymentLinkResponse,
            paymentMethod: props.paymentMethod || '',
            eMandate: hasEMandate,
            paymentResponse: info,
            paymentDate: isSuccess
              ? (
                  caseObj.application?.initialPayment as {
                    paymentDate?: string | null;
                  }
                )?.paymentDate || new Date().toISOString()
              : null,
          },
        },
      });
    } catch {
      alertError(t('eApp:failedToSaveData'));
      return;
    }
    await submitApplication({
      onSuccess: () => navigation.navigate('Submission'),
      onFailure: () => navigation.navigate('SubmissionFailed'),
    });
  };

  return (
    <>
      <Box
        flex={1}
        alignContent="center"
        justifyContent="center"
        alignItems="center"
        backgroundColor={colors.surface}>
        {isSuccess ? <PaymentSuccessIcon /> : <PaymentFailIcon />}
        <Box height={sizes[4]} />
        <H6 fontWeight="bold">
          {isSuccess ? t('eApp:payment.success') : t('eApp:payment.failed')}
        </H6>
        <Box height={sizes[6]} />
        <Box
          backgroundColor={colors.background}
          borderRadius={borderRadius['large']}
          padding={sizes[6]}
          width={560}>
          <H7 fontWeight="bold">{t('eApp:payment.failed.title')}</H7>
          <Box marginTop={sizes[4]}>
            <RowItem
              title={t('eApp:payment.failed.orderId')}
              content={info?.paymentTransactionId}
            />
            <RowItem
              title={t('eApp:payment.failed.paymentId')}
              content={info?.paymentGatewayReturnDetails?.payloadMap?.paymentId}
            />
            <RowItem
              title={t('eApp:payment.failed.amount')}
              content={`${info?.paymentGatewayReturnDetails?.payloadMap?.amount}`}
            />
            <RowItem
              title={t('eApp:payment.failed.status')}
              content={
                isSuccess
                  ? t('eApp:payment.status.success')
                  : t('eApp:payment.status.fail')
              }
              isError={!isSuccess}
              isSuccess={isSuccess}
            />
          </Box>
        </Box>
        {/* {isSuccess ? (
            <Box
              mt={space[6]}
              backgroundColor={colors.primaryVariant3}
              borderRadius={borderRadius['small']}
              padding={space[4]}
              width={560}
              gap={space[1]}
              flexDirection="row">
              <Icon.InfoCircle />
              <LargeBody color={colors.placeholder}>
                {t('eApp:payment.success.noted')}
              </LargeBody>
            </Box>
          ) : null} */}
        <Box height={sizes[6]} />
        {hasEMandate ? (
          <Button
            text={t('eApp:payment.status.eMandateRegistration')}
            onPress={props.onStartEMandate}
          />
        ) : null}
      </Box>
      {hasEMandate ? null : (
        <EAppFooter
          progressLock="payment--"
          isLoading={isGoingBack}
          disabled={false}
          onPress={isSuccess ? onSubmit : onGoBack}
          label={isSuccess ? t('eApp:payment.submit') : t('eApp:payment.back')}
          variant={isSuccess ? undefined : 'secondary'}
        />
      )}
    </>
  );
};

export default PaymentStatus;

const RowItem = ({
  title,
  content,
  isError,
  isSuccess,
}: {
  title: string;
  content?: string | null;
  isError?: boolean;
  isSuccess?: boolean;
}) => {
  const { colors, sizes } = useTheme();
  return (
    <Row justifyContent="flex-start" marginBottom={sizes[2]}>
      <Box flex={2}>
        <Body color={colors.palette.fwdGreyDarkest}>{title}</Body>
      </Box>
      <Box flex={3}>
        <Body
          color={
            isError
              ? colors.error
              : isSuccess
              ? colors.palette.alertGreen
              : colors.secondary
          }
          fontWeight={isError || isSuccess ? 'bold' : 'normal'}>
          {content ?? ''}
        </Body>
      </Box>
    </Row>
  );
};
