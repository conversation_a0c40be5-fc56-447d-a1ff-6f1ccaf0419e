import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Body, Box, Button, H6, H7, Row } from 'cube-ui-components';
import PaymentFailIcon from 'features/eApp/components/tablet/payment/icons/PaymentFailIcon';
import { IPay88PaymentStatus, InactivateEMandateRequest } from 'api/ipay88Api';
import EAppFooter from '../common/EAppFooter';
import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import { RootStackParamList } from 'types';
import PaymentSuccessIcon from './icons/PaymentSuccessIcon';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useCreateApplication } from 'hooks/useCreateApplication';
import {
  useEMandateBankList,
  useSubmitInactivateEMandate,
} from 'hooks/useIpay88Gateway';
import { ScrollView } from 'react-native';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { useEffect, useRef } from 'react';
import { useEAppSubmitApplication } from 'features/eApp/hooks/useEAppSubmitApplication';
import { useAlert } from 'hooks/useAlert';
import { EMANDATE_PAYMENT_CODE } from './RenewalPayment';

interface RenewalPaymentStatusProps {
  isSubmittable?: boolean;
  onRedo: () => void;
}

const RenewalPaymentStatus = (props: RenewalPaymentStatusProps) => {
  const { colors, sizes, borderRadius, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { alertError } = useAlert();
  const { onRedo, isSubmittable } = props;

  const {
    my_renewalPaymentStatusResponse,
    my_renewalPaymentLinkResponse,
    my_renewalPaymentForm,
  } = useEAppStore(
    state => ({
      my_renewalPaymentStatusResponse: state.my_renewalPaymentStatusResponse,
      my_renewalPaymentLinkResponse: state.my_renewalPaymentLinkResponse,
      my_renewalPaymentForm: state.my_renewalPaymentForm,
    }),
    shallow,
  );
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { mutateAsync: submitApplication, isLoading: isSubmittingApplication } =
    useEAppSubmitApplication();
  useEffect(() => {
    if (isSavingApplication || isSubmittingApplication) {
      setAppLoading();
    } else {
      setAppIdle();
    }
    return () => {
      setAppIdle();
    };
  }, [isSavingApplication, isSubmittingApplication, setAppIdle, setAppLoading]);

  const { mutateAsync: submitInactivateEMandate } =
    useSubmitInactivateEMandate();

  const isSuccess =
    my_renewalPaymentStatusResponse?.paymentStatus ===
    IPay88PaymentStatus.RECEIVE_SUCCEED;

  const submitEMandate = async (paymentTransactionId: string) => {
    const requestData: InactivateEMandateRequest = {
      paymentTransactionId,
    };
    try {
      await submitInactivateEMandate(requestData);
    } finally {
      navigation.dispatch(StackActions.replace('Submission'));
    }
  };

  const hasDoneInitialSaving = useRef(false);
  useEffect(() => {
    if (!caseId || !caseObj || hasDoneInitialSaving.current) return;
    saveApplication({
      caseId,
      data: {
        ...caseObj.application,
        renewalPayment: {
          ...my_renewalPaymentLinkResponse,
          formData: {
            renewalPayment: my_renewalPaymentForm,
            consent: true,
          },
          eMandatePaymentStatusResponse: my_renewalPaymentStatusResponse,
          paymentDate: isSuccess
            ? (
                caseObj.application?.renewalPayment as {
                  paymentDate?: string | null;
                }
              )?.paymentDate || new Date().toISOString()
            : null,
        },
      },
    }).then(() => (hasDoneInitialSaving.current = true));
  }, [
    caseId,
    caseObj,
    isSuccess,
    my_renewalPaymentForm,
    my_renewalPaymentLinkResponse,
    my_renewalPaymentStatusResponse,
    saveApplication,
  ]);

  const onSubmit = async () => {
    if (!caseId || !caseObj) return;
    try {
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          renewalPayment: {
            ...my_renewalPaymentLinkResponse,
            formData: {
              renewalPayment: my_renewalPaymentForm,
              consent: true,
            },
            eMandatePaymentStatusResponse: my_renewalPaymentStatusResponse,
            paymentDate: isSuccess
              ? (
                  caseObj.application?.renewalPayment as {
                    paymentDate?: string | null;
                  }
                )?.paymentDate || new Date().toISOString()
              : null,
          },
        },
      });
    } catch {
      alertError(t('eApp:failedToSaveData'));
      return;
    }
    await submitApplication({
      onSuccess: async () => {
        if (my_renewalPaymentStatusResponse?.paymentTransactionId) {
          await submitEMandate(
            my_renewalPaymentStatusResponse?.paymentTransactionId,
          );
        } else {
          navigation.navigate('Submission');
        }
      },
      onFailure: () => navigation.navigate('SubmissionFailed'),
    });
  };

  const { data: eMandateBankList } = useEMandateBankList(EMANDATE_PAYMENT_CODE);

  return (
    <>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Box
          flex={1}
          py={space[15]}
          alignContent="center"
          justifyContent="center"
          alignItems="center"
          backgroundColor={colors.surface}>
          {isSuccess ? <PaymentSuccessIcon /> : <PaymentFailIcon />}
          <Box height={sizes[4]} />
          <H6 fontWeight="bold">
            {isSuccess
              ? t('eApp:eMandate.success.title')
              : t('eApp:eMandate.failed.title')}
          </H6>
          <Box h={sizes[3]} />
          <H7>
            {isSuccess
              ? t('eApp:eMandate.success.desc')
              : t('eApp:eMandate.failed.desc')}
          </H7>
          <Box height={sizes[6]} />
          <Box
            backgroundColor={colors.background}
            borderRadius={borderRadius['large']}
            padding={sizes[6]}
            width={560}>
            <H7 fontWeight="bold">{t('eApp:eMandate.failed.detail')}</H7>
            <Box marginTop={sizes[4]}>
              <RowItem
                title={t('eApp:eMandate.failed.date')}
                content={
                  my_renewalPaymentStatusResponse?.paymentGatewayReturnDetails
                    .payloadMap.fpx_fpxTxnTime ?? ''
                }
              />
              <RowItem
                title={t('eApp:eMandate.failed.type')}
                content={t('eApp:renewal.payment.newApplication')}
              />
              <RowItem
                title={t('eApp:eMandate.failed.number')}
                content={
                  my_renewalPaymentStatusResponse?.paymentGatewayReturnDetails
                    .payloadMap.fpx_txnAmountFormat ?? ''
                }
              />
              <RowItem
                title={t('eApp:eMandate.failed.refNumber')}
                content={
                  my_renewalPaymentStatusResponse?.paymentGatewayReturnDetails
                    ?.payloadMap?.fpx_sellerOrderNo
                }
              />
              <RowItem
                title={t('eApp:eMandate.failed.debitAmount')}
                content={`${my_renewalPaymentStatusResponse?.paymentGatewayReturnDetails.payloadMap.fpx_txnDDAmtFormat}`}
              />
              <RowItem
                title={t('eApp:eMandate.failed.fpxID')}
                content={
                  my_renewalPaymentStatusResponse?.paymentGatewayReturnDetails
                    ?.payloadMap?.fpx_fpxTxnId
                }
              />
              <RowItem
                title={t('eApp:eMandate.failed.bankName')}
                content={
                  eMandateBankList?.bankListDetail.find(
                    b =>
                      b.bankCode ===
                      my_renewalPaymentStatusResponse
                        ?.paymentGatewayReturnDetails.payloadMap
                        .fpx_buyerBankBranch,
                  )?.displayName ?? ''
                }
              />
              <RowItem
                title={t('eApp:payment.failed.status')}
                content={
                  isSuccess
                    ? t('eApp:payment.status.success')
                    : t('eApp:payment.status.fail')
                }
                isError={!isSuccess}
                isSuccess={isSuccess}
              />
            </Box>
          </Box>
          <Box height={sizes[6]} />

          {!isSuccess && (
            <Button
              text={t('eApp:payment.status.eMandateRegistration')}
              onPress={onRedo}
              variant={isSubmittable ? 'secondary' : 'primary'}
            />
          )}
        </Box>
      </ScrollView>
      {(isSubmittable || isSuccess) && (
        <EAppFooter
          progressLock="payment--"
          disabled={false}
          onPress={onSubmit}
          label={t('eApp:payment.submit')}
        />
      )}
    </>
  );
};

export default RenewalPaymentStatus;

const RowItem = ({
  title,
  content,
  isError,
  isSuccess,
}: {
  title: string;
  content?: string;
  isError?: boolean;
  isSuccess?: boolean;
}) => {
  const { colors, sizes } = useTheme();
  return (
    <Row justifyContent="flex-start" marginBottom={sizes[2]}>
      <Box flex={2}>
        <Body color={colors.palette.fwdGreyDarkest}>{title}</Body>
      </Box>
      <Box flex={3}>
        <Body
          color={
            isError
              ? colors.error
              : isSuccess
              ? colors.palette.alertGreen
              : colors.secondary
          }
          fontWeight={isError || isSuccess ? 'bold' : 'normal'}>
          {content}
        </Body>
      </Box>
    </Row>
  );
};
