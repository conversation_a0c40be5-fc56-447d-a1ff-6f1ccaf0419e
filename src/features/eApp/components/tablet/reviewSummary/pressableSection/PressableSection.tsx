import { useTheme } from '@emotion/react';
import { Box, H7, Icon, Row } from 'cube-ui-components';
import React from 'react';
import { TouchableOpacity } from 'react-native';

export default function PressableSection({
  onPress,
  icon,
  sectionName,
  actionLabel,
  emphasizeActionLabel = true,
}: {
  onPress?: () => void;
  icon?: JSX.Element;
  sectionName?: string;
  actionLabel?: string;
  emphasizeActionLabel?: boolean;
}) {
  const { sizes, space } = useTheme();
  return (
    <TouchableOpacity onPress={onPress}>
      <Row my={space[4]} justifyContent="space-between" alignItems="center">
        <Row alignItems="center">
          {icon}
          <Box width={sizes[2]} />
          <H7 fontWeight="bold">{sectionName}</H7>
        </Row>
        <Row alignItems="center" gap={space[3]}>
          <H7 fontWeight={emphasizeActionLabel ? 'bold' : 'normal'}>
            {actionLabel}
          </H7>
          <Icon.ChevronRight />
        </Row>
      </Row>
    </TouchableOpacity>
  );
}
