import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Icon, LargeLabel } from 'cube-ui-components';
import SectionContainer from 'features/customerFactFind/components/SectionContainer';
import { NOMINATION_TYPE } from 'features/eApp/constants/nominationType';
import { useCheckEntity } from 'features/eApp/hooks/useCheckEntity';
import { getPartyTitle } from 'features/eApp/utils/getPartyTitle';
import { RelationshipValue } from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { CHANNELS } from 'types/channel';
import { Party, PartyRole, PartyType } from 'types/party';
import { getRelationshipGroup } from 'utils/helper/optionListUtils';
import PressableSection from '../pressableSection/PressableSection';
import { useGetReviewSectionsData } from '../../../../hooks/useGetReviewSectionsData';

export default function PersonalInformationSummary() {
  const { t } = useTranslation(['eApp']);
  const { sizes, space, colors } = useTheme();
  const { data: optionList } = useGetOptionList();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;
  const quotation = useSelectedQuotation();
  const { onGetSectionData } = useGetReviewSectionsData();
  const shouldShowSourceOfFundWealth =
    quotation?.plans[0].showSourceOfFundWealth;
  const beneficiaries = useMemo(
    () =>
      caseObj?.parties?.filter(party =>
        party.roles.includes(PartyRole.BENEFICIARY),
      ) || [],
    [caseObj?.parties],
  );
  const otherParties = useMemo(() => {
    const owner = caseObj?.parties?.find(party =>
      party.roles.includes(PartyRole.PROPOSER),
    );
    const employee =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          getRelationshipGroup(p.relationship, optionList) ===
            RelationshipValue.EMPLOYEE,
      ) || [];
    const spouse =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          getRelationshipGroup(p.relationship, optionList) ===
            RelationshipValue.SPOUSE,
      ) || [];
    const children =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER) &&
          getRelationshipGroup(p.relationship, optionList) !==
            RelationshipValue.SPOUSE &&
          getRelationshipGroup(p.relationship, optionList) !==
            RelationshipValue.EMPLOYEE,
      ) || [];
    const payors =
      caseObj?.parties?.filter(
        party =>
          party.roles.includes(PartyRole.PAYER) && Boolean(caseObj.havePayer),
      ) || [];
    return [owner, ...employee, ...spouse, ...children, ...payors].filter(
      (p): p is Party => Boolean(p),
    );
  }, [caseObj?.havePayer, caseObj?.parties, optionList]);

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const onPress = (party: Party) => {
    const data = onGetSectionData(
      party,
      t,
      optionList,
      isBanca && shouldShowSourceOfFundWealth,
      isBanca,
    );
    if (data) {
      navigate('PersonalInformationReview', data);
    }
  };
  const isEntity = useCheckEntity();

  return (
    <SectionContainer
      title={
        isEntity
          ? t('eApp:review.personCoveredInformation')
          : t('eApp:review.personalInfo')
      }>
      <Box mx={space[6]}>
        {otherParties.map((party, idx, arr) => {
          const sectionName = getPartyTitle(party, t, optionList);
          const isCompany =
            party.roles.includes(PartyRole.PROPOSER) &&
            party.clientType === PartyType.ENTITY;
          const isLast = idx === arr.length - 1;
          return (
            <React.Fragment key={party.id}>
              {!isCompany && (
                <React.Fragment>
                  <PressableSection
                    onPress={() => onPress(party)}
                    sectionName={sectionName}
                    icon={<Icon.Account size={sizes[6]} />}
                    emphasizeActionLabel={false}
                    actionLabel={
                      party.person?.name?.fullName ?? party.entity?.name
                    }
                  />
                  {(!isLast || beneficiaries.length > 0) && (
                    <Box h={1} bgColor={colors.palette.fwdGrey[100]} />
                  )}
                </React.Fragment>
              )}
            </React.Fragment>
          );
        })}
        {beneficiaries.length > 0 && (
          <>
            <LargeLabel
              color={colors.secondaryVariant}
              fontWeight="bold"
              style={{ marginTop: space[4] }}>
              {t('eApp:beneficiary')}
            </LargeLabel>
            {beneficiaries.map((party, idx, arr) => {
              const sectionName = `${
                party.beneficiarySetting?.beneficiaryType ===
                NOMINATION_TYPE.HIBAH
                  ? t('eApp:other.nominationDetails.hibah')
                  : t('eApp:other.nominationDetails.wasi')
              } ${idx + 1}`;
              const isLast = idx === arr.length - 1;
              return (
                <React.Fragment key={party.id}>
                  <PressableSection
                    onPress={() => onPress(party)}
                    sectionName={sectionName}
                    icon={<Icon.Account size={sizes[6]} />}
                    emphasizeActionLabel={false}
                    actionLabel={
                      party.person?.name?.fullName ?? party.entity?.name
                    }
                  />
                  {!isLast && (
                    <Box h={1} bgColor={colors.palette.fwdGrey[100]} />
                  )}
                </React.Fragment>
              );
            })}
          </>
        )}
      </Box>
    </SectionContainer>
  );
}
