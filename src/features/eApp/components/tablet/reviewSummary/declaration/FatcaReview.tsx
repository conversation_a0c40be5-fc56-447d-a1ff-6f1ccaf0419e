import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, H6, H7, LargeBody, Row } from 'cube-ui-components';
import { useReviewFatcaMapper } from 'features/eApp/hooks/useReviewFatcaMapper';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RowInfoField, {
  RowInfoFieldProps,
} from '../rowInfoField/RowInfoField';
import { shallow } from 'zustand/shallow';
import { MY_COUNTRY } from 'constants/optionList';

interface FatcaQuestionProps {
  index?: number;
  question: string;
  answer?: string;
  subQuestion?: string;
  subAnswer?: string;
  info?: RowInfoFieldProps[];
  additional?: AdditionalInfoProps[];
}

interface AdditionalInfoProps {
  title: string;
  info?: RowInfoFieldProps[];
}

const AdditionalInfo = ({
  additional,
}: {
  additional?: AdditionalInfoProps;
}) => {
  const { sizes, colors, borderRadius, space } = useTheme();

  const renderContent = () => {
    return additional?.info?.map((item, index) => {
      return (
        <RowInfoField
          key={index}
          {...item}
          isLastItem={index === (additional?.info?.length ?? 0) - 1}
        />
      );
    });
  };

  return (
    <Column
      flex={1}
      gap={sizes[2]}
      backgroundColor={colors.palette.fwdGrey[20]}
      padding={sizes[4]}
      borderRadius={borderRadius.medium}>
      <H7 fontWeight="bold">{additional?.title}</H7>
      <Box height={sizes[4]} />
      {renderContent()}
    </Column>
  );
};

const Info = ({ info }: { info?: RowInfoFieldProps[] }) => {
  const { sizes, colors, borderRadius, space } = useTheme();

  const renderContent = () => {
    return info?.map((item, index) => {
      return (
        <RowInfoField
          key={index}
          {...item}
          isLastItem={index === info.length - 1}
        />
      );
    });
  };

  return (
    <Column
      flex={1}
      gap={sizes[2]}
      backgroundColor={colors.palette.fwdGrey[20]}
      padding={sizes[4]}
      borderRadius={borderRadius.medium}>
      {renderContent()}
    </Column>
  );
};

const Question = ({
  index,
  question,
  answer,
  subQuestion,
  subAnswer,
  additional,
  info,
}: FatcaQuestionProps) => {
  const { sizes, colors } = useTheme();

  return (
    <Column>
      <Row>
        {index && (
          <Box width={sizes[5]}>
            <LargeBody color={colors.placeholder}>{`${index}.`}</LargeBody>
          </Box>
        )}
        <Column>
          <LargeBody color={colors.placeholder}>{question}</LargeBody>
          {answer && (
            <Column>
              <Box height={sizes[2]} />
              <LargeBody>{answer}</LargeBody>
            </Column>
          )}
          {subQuestion && (
            <Column>
              <Box height={sizes[4]} />
              <LargeBody color={colors.placeholder}>{subQuestion}</LargeBody>
              <Box height={sizes[2]} />
              <LargeBody>{subAnswer}</LargeBody>
            </Column>
          )}
        </Column>
      </Row>
      {additional?.map(item => {
        return (
          <Column>
            <Box height={sizes[4]} />
            <AdditionalInfo additional={item} />
          </Column>
        );
      })}
      {info && (
        <Column>
          <Box height={sizes[4]} />
          <Info info={info} />
        </Column>
      )}
    </Column>
  );
};

const Separator = () => {
  const { sizes, colors } = useTheme();

  return (
    <Column>
      <Box h={sizes[4]} />
      <Box h={1} backgroundColor={colors.palette.fwdGrey[100]} />
      <Box h={sizes[4]} />
    </Column>
  );
};

export default function FatcaReview() {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation(['eApp']);

  const { info, my_policyOwnerPersonalInfo } = useEAppStore(
    state => ({
      info: state.my_declaration.fatca,
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
    }),
    shallow,
  );

  const displayedData = useReviewFatcaMapper(info);

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.fatca')}
        route={'FatcaReview'}
        isLeftCrossBackShown
      />
      <ScrollViewContainer
        contentContainerStyle={{ flexGrow: 1, paddingBottom: sizes[6] }}>
        <Content>
          <H6 fontWeight="medium">{t('eApp:declaration.fatca.title')}</H6>
          <Box flex={1} height={sizes[4]} />
          <LargeBody fontWeight="medium">
            {t('eApp:declaration.fatca.message')}
          </LargeBody>
          <Box flex={1} height={sizes[6]} />
          {my_policyOwnerPersonalInfo.nationality !== MY_COUNTRY && (
            <>
              <HintContainer flex={1}>
                <LargeBody color={colors.primary}>
                  {t('eApp:declaration.fatca.hint')}
                </LargeBody>
              </HintContainer>
              <Box flex={1} height={sizes[4]} />
            </>
          )}
        </Content>
        {displayedData?.map((item, index) => {
          return (
            <Column key={index}>
              <Question
                index={item.index}
                question={item.question}
                answer={item.answer}
                subQuestion={item.subQuestion}
                subAnswer={item.subAnswer}
                info={item.info}
                additional={item.additional}
              />
              {index !== displayedData.length - 1 && <Separator />}
            </Column>
          );
        })}
      </ScrollViewContainer>
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
}));

const HintContainer = styled(Box)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    paddingHorizontal: space[4],
    paddingVertical: space[4],
    borderRadius: borderRadius['small'],
  }),
);

export type { FatcaQuestionProps };
