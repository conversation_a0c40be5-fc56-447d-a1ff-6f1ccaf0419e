import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, PictogramIcon } from 'cube-ui-components';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useGenerateCFFPdf } from 'hooks/useGenerateCFFPdf';
import { useGetOwb } from 'features/eApp/hooks/healthQuestions/useGetOwb';
import { useGenerateAppFormPdf } from 'hooks/useGenerateAppFormPdf';
import PdfViewer, {
  PdfGenerator
} from 'features/pdfViewer/components/PdfViewer';
import { formatSignatureDate } from 'utils/helper/formatUtil';
import { Language } from 'types/quotation';
import { useViewSaleIllustrationPdf } from 'features/eApp/hooks/useViewSaleIllustrationPdf';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import SectionContainer from 'features/customerFactFind/components/SectionContainer';
import PressableSection from '../pressableSection/PressableSection';
import { useCheckEntity } from 'features/eApp/hooks/useCheckEntity';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { LanguagesKeys } from 'utils/translation';

export enum PreferredLanguage {
  bahasaMalaysia = 'my',
  english = 'en',
}

const PdfSummary = () => {
  const { t } = useTranslation(['eApp']);
  const { sizes, space } = useTheme();
  const [pdfVisible, setPdfVisible] = useState(false);

  const isEntity = useCheckEntity();

  const [pdfTitle, setPdfTitle] = useState<
    string | { title: string; option: string; value: Language }[]
  >('');
  const pdfGeneratorRef = useRef<PdfGenerator>(async () => ({
    base64: '',
    fileName: '',
  }));

  const { my_policyOwnerPersonalInfo } = useEAppStore(
    state => ({
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
    }),
    shallow,
  );

  const { getOwb } = useGetOwb();
  const { mutateAsync: generateCFFPdf } = useGenerateCFFPdf();
  const { mutateAsync: generateAppFormPdf } = useGenerateAppFormPdf();

  const { generateSiPdf } = useViewSaleIllustrationPdf();
  const quotation = useSelectedQuotation();

  const getSIPdf: PdfGenerator = async (lang?: LanguagesKeys) => {
    const pieChartBase64Image = quotation?.fundsPieChartImage || '';

    const result = await generateSiPdf(lang, pieChartBase64Image);
    return {
      base64: result?.base64 || '',
      fileName: `Benefit illustration - ${formatSignatureDate(new Date())}`,
    };
  };
  const getCFFPdf = async () => {
    const owbModel = await getOwb();
    const base64 = await generateCFFPdf(owbModel);
    return {
      base64,
      fileName: `CFF document - ${formatSignatureDate(new Date())}`,
    };
  };
  const getApplicationPdf = async () => {
    const owbModel = await getOwb();
    const base64 = await generateAppFormPdf(owbModel);
    return {
      base64,
      fileName: `Application form - ${formatSignatureDate(new Date())}`,
    };
  };

  return (
    <SectionContainer title={t('eApp:review.cert')}>
      <Box mx={space[6]}>
        <PressableSection
          onPress={() => {
            pdfGeneratorRef.current = getSIPdf;
            setPdfTitle([
              {
                title: t('eApp:review.cert.reviewYour.title', {
                  title: t('eApp:review.cert.benefitIllustration.my'),
                }),
                option: t('eApp:bahasa'),
                value: 'my',
              },
              {
                title: t('eApp:review.cert.reviewYour.title', {
                  title: t('eApp:review.cert.benefitIllustration.en'),
                }),
                option: t('eApp:english'),
                value: 'en',
              },
            ]);
            setPdfVisible(true);
          }}
          icon={<PictogramIcon.MedicalForm2 size={sizes[7]} />}
          sectionName={t('eApp:review.cert.benefitIllustration')}
          actionLabel={t('eApp:view')}
        />
        {isEntity ? undefined : (
          <>
            <Divider />
            <PressableSection
              onPress={() => {
                pdfGeneratorRef.current = getCFFPdf;
                const title = t('eApp:review.cert.reviewYour.title', {
                  title: t('eApp:review.cert.customerFactFinding'),
                });
                setPdfTitle(title);
                setPdfVisible(true);
              }}
              icon={<PictogramIcon.MedicalForm2 size={sizes[7]} />}
              sectionName={t('eApp:review.cert.customerFactFinding')}
              actionLabel={t('eApp:view')}
            />
          </>
        )}

        <Divider />
        <PressableSection
          onPress={() => {
            pdfGeneratorRef.current = getApplicationPdf;
            const title = t('eApp:review.cert.reviewYour.title', {
              title: t('eApp:review.cert.applicationForm'),
            });
            setPdfTitle(title);
            setPdfVisible(true);
          }}
          icon={<PictogramIcon.MedicalForm2 size={sizes[7]} />}
          sectionName={t('eApp:review.cert.applicationForm')}
          actionLabel={t('eApp:view')}
        />
        <PdfViewer
          title={pdfTitle}
          onClose={() => setPdfVisible(false)}
          visible={pdfVisible}
          downloadable
          pdfGenerator={pdfGeneratorRef.current}
          initialLanguage={
            my_policyOwnerPersonalInfo?.preferredLanguage === 'bahasaMalaysia'
              ? PreferredLanguage.bahasaMalaysia
              : PreferredLanguage.english
          }
        />
      </Box>
    </SectionContainer>
  );
};
export default PdfSummary;

export const Divider = styled(View)(({ theme: { colors } }) => {
  return {
    height: 1,
    backgroundColor: colors.palette.fwdGrey[100],
  };
});
