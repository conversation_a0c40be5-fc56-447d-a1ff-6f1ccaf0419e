import * as React from 'react';
import Svg, { Path, G, Defs, Rect, ClipPath } from 'react-native-svg';

function BoyCharacter() {
  return (
    <Svg width="128" height="104" viewBox="0 0 128 104" fill="none">
      <G clipPath="url(#clip0_11628_34904)">
        <Path
          d="M43.1388 133.13L59.2175 75.0894C59.8756 73.3079 62.2263 72.839 63.5427 74.2455C64.765 75.7458 71.0648 78.84 73.0394 83.5283C73.3215 84.1846 73.3215 84.9347 73.0394 85.4973C67.3978 97.6868 56.5847 123.847 48.6864 136.787C48.2163 137.537 45.2074 135.943 45.2074 135.943C43.4209 135.943 42.5747 134.724 43.1388 133.13V133.13Z"
          fill="#DCAA86"
        />
        <Path
          d="M90.623 52.9552L82.9128 40.2969L73.04 45.454L76.425 56.4245C76.519 56.5183 76.613 56.5182 76.7071 56.5182C79.998 53.0489 86.2038 54.7367 90.623 52.9552V52.9552Z"
          fill="#DCAA86"
        />
        <Path
          d="M81.8787 20.5156L84.6055 35.7056C83.101 35.9869 82.6309 37.7684 83.1951 39.1749C83.101 39.55 83.101 39.925 83.1951 40.3938C81.0324 42.8317 76.8953 46.3948 70.5014 46.6761C60.5346 47.1449 62.039 27.3605 62.039 27.3605L81.8787 20.5156V20.5156Z"
          fill="#DCAA86"
        />
        <Path
          d="M89.3057 30.7347C89.1176 29.6095 90.0579 26.8904 90.7161 26.0465C95.0413 20.1393 89.3997 17.8889 87.4251 17.2325C85.2625 16.4824 84.8864 12.9193 81.8775 11.6066C77.4583 9.5438 74.5434 13.3882 72.0047 13.5757C69.466 13.6695 67.3034 10.8565 64.2005 13.3882C61.0976 15.9198 62.1319 18.5453 61.2856 19.9517C60.4394 21.3582 57.6186 18.639 55.0798 23.1398C51.2247 29.8908 61.8498 29.8908 61.8498 29.8908C61.8498 29.8908 67.9615 30.9223 74.6375 26.3278C75.5777 25.6714 76.8001 25.6714 77.6463 26.3278C79.997 28.2968 82.9118 40.8614 82.9118 40.8614L84.8864 38.2359C85.0745 37.9546 90.2459 35.7981 89.3057 30.7347V30.7347Z"
          fill="#183028"
        />
        <Path
          d="M83.9477 33.2686C83.7596 34.5813 82.5373 35.4252 81.2209 35.2377C79.9045 35.0502 79.0583 33.8312 79.2463 32.5185C79.4344 31.2058 80.6567 30.3619 81.9731 30.5494C83.1954 30.737 84.1357 31.9559 83.9477 33.2686Z"
          fill="#DCAA86"
        />
        <Path
          d="M58.4646 77.5199C58.4646 77.2386 58.5586 76.9573 58.5586 76.676C61.6615 64.674 69.1837 51.3594 82.6295 51.2656H82.9116C101.905 51.2656 114.881 67.5808 119.206 73.0191C120.052 74.1443 120.146 75.7383 119.3 76.8635L107.735 92.8974C107.359 93.4599 107.171 94.1163 107.171 94.7727V130.122C107.171 131.904 105.76 133.31 103.974 133.31H61.6615C59.875 133.31 58.4646 131.904 58.4646 130.122V77.5199V77.5199Z"
          fill="#E87722"
        />
        <Path
          d="M92.0332 52.3994L86.9557 46.6797L74.2621 49.3051L73.6979 53.6183L92.0332 52.3994Z"
          fill="#E77824"
        />
        <Path
          d="M94.2912 84.6484L107.173 93.7437V102.37L94.2912 88.4928V84.6484Z"
          fill="#F89F60"
        />
        <Path
          d="M76.0478 31.8595C76.1418 32.4221 75.9538 32.9847 75.4836 33.0785C75.0135 33.1722 74.6374 32.7972 74.4493 32.1408C74.3553 31.4845 74.5434 31.0156 75.0135 30.9219C75.4836 30.9219 75.9538 31.2969 76.0478 31.8595Z"
          fill="#183028"
        />
        <Path
          d="M67.0204 33.922C67.1145 34.4846 66.9264 35.0472 66.4563 35.141C65.9861 35.2347 65.61 34.8597 65.422 34.2033C65.328 33.6407 65.516 33.0781 65.9861 32.9844C66.4563 32.9844 66.9264 33.3594 67.0204 33.922Z"
          fill="#183028"
        />
        <Path
          d="M71.3466 39.3607C71.4406 39.3607 71.6287 39.2669 71.6287 39.0794C71.7227 38.8919 71.5346 38.7981 71.4406 38.7044C70.6884 38.5168 70.3123 38.1418 70.2183 37.6729C70.1242 37.0166 70.5003 36.2665 70.8765 35.9852C71.6287 35.4226 71.0645 32.6096 70.7824 31.3907C70.6884 31.2031 70.5944 31.1094 70.4063 31.1094C70.2183 31.2031 70.1242 31.2969 70.1242 31.4844C70.5944 33.3597 70.7824 35.235 70.4063 35.4226C69.9362 35.7976 69.372 36.7353 69.5601 37.7667C69.6541 38.2355 70.0302 38.9856 71.2526 39.3607C71.2526 39.3607 71.2839 39.3607 71.3466 39.3607V39.3607Z"
          fill="#E77824"
        />
        <Path
          d="M72.0989 41.797C71.8169 41.8908 71.4407 41.8908 71.1587 41.8908C70.1244 41.9846 69.3722 41.5157 69.3722 41.5157C69.2781 41.422 69.1841 41.3282 69.2781 41.2344C69.3722 41.1407 69.4662 41.0469 69.5602 41.1407C69.6542 41.2344 72.381 42.4534 73.8854 39.5467C73.9795 39.4529 74.1675 39.3591 74.2616 39.4529C74.3556 39.5467 74.4496 39.7342 74.3556 39.828C73.6974 40.9531 72.8512 41.6095 72.0989 41.797Z"
          fill="#E77825"
        />
        <Path
          d="M72.8517 43.1082C74.3561 42.5456 74.4502 40.6703 74.3561 39.7326C74.3561 39.6389 74.2621 39.4513 74.0741 39.5451C73.98 39.5451 73.792 39.6389 73.886 39.8264C73.886 39.9201 74.2621 42.1705 72.4756 42.7331C72.3816 42.7331 71.6294 43.1082 70.8771 42.8269C70.313 42.6393 69.9369 42.1705 69.6548 41.4204C69.5608 41.3266 69.4667 41.2329 69.3727 41.3266C69.2787 41.4204 69.1847 41.5142 69.2787 41.6079C69.6548 42.4518 70.1249 43.0144 70.8771 43.2957C71.8174 43.6708 72.7577 43.2019 72.7577 43.2019L72.8517 43.1082V43.1082Z"
          fill="#E77825"
        />
        <Path
          d="M72.6629 44.3283C72.1928 44.422 71.8166 44.422 71.8166 44.422C71.7226 44.3283 71.6286 44.2345 71.6286 44.1407C71.7226 44.047 71.8166 43.9532 71.9107 43.9532C71.9107 43.9532 72.3808 44.047 72.945 43.5781C73.039 43.4844 73.2271 43.4844 73.3211 43.5781C73.4151 43.6719 73.4151 43.8594 73.3211 43.9532C73.133 44.1407 72.8509 44.2345 72.6629 44.3283V44.3283Z"
          fill="#E77825"
        />
        <Path
          d="M47.5596 66.7339L45.3969 73.0161C43.6104 72.4535 42.6702 70.5782 43.3284 68.7967C43.8925 67.1089 45.7731 66.1713 47.5596 66.7339Z"
          fill="#E87722"
        />
        <Path
          d="M39.3192 67.5166L41.458 68.2344L41.2481 68.8564L39.1093 68.1387L39.3192 67.5166Z"
          fill="#183028"
        />
        <Path
          d="M41.8656 71.416L42.1582 72.0039L40.1369 73.0045L39.8442 72.4166L41.8656 71.416Z"
          fill="#183028"
        />
        <Path
          d="M43.4011 63.4794L44.4023 65.4961L43.8125 65.7873L42.8112 63.7706L43.4011 63.4794Z"
          fill="#183028"
        />
        <Path
          d="M63.7321 94.8651C63.544 95.3339 63.2619 95.8027 62.6978 96.084C62.2276 96.3653 61.6635 96.3653 61.0993 96.1778L59.6889 95.709C59.2188 95.5214 58.7486 95.2401 58.4666 94.6776C58.1845 94.115 58.1845 93.6461 58.3725 93.0835L61.5695 83.707L66.835 85.4886L63.7321 94.8651V94.8651Z"
          fill="#FBE69D"
        />
        <Path
          d="M60.536 86.8053L60.0659 86.6177C59.7838 86.524 59.5957 86.3364 59.5017 86.1489C59.4077 85.8676 59.4077 85.6801 59.4077 85.3988L60.1599 83.2422L61.5703 83.711L60.536 86.8053V86.8053Z"
          fill="#183028"
        />
        <Path
          d="M54.1411 57.3633C54.0471 61.3952 56.4918 65.2396 60.535 66.5523L65.2363 68.1463L60.3469 82.5861L55.6456 80.9921C51.6024 79.5856 47.3712 81.1796 44.9265 84.4614L54.1411 57.3633V57.3633Z"
          fill="#FED241"
        />
        <Path
          d="M65.4247 67.4883L73.323 70.1137C75.2976 70.7701 77.0841 72.2703 78.1184 74.2394C79.1527 76.2084 79.2467 78.5526 78.5885 80.5216C77.9303 82.4907 76.4259 84.2722 74.4513 85.3037C72.4768 86.3351 70.1261 86.4288 68.1515 85.7725L60.3473 83.1471L65.4247 67.4883Z"
          fill="#FBE69D"
        />
        <Path
          d="M51.2264 54.7352C51.3204 54.2664 51.6965 53.8913 52.0726 53.7038C52.5428 53.5163 53.0129 53.4225 53.483 53.61C53.9532 53.7038 54.3293 54.0789 54.5173 54.4539C54.7054 54.9227 54.7994 55.3916 54.6114 55.8604L44.5505 85.8653C44.4564 86.3341 44.0803 86.7091 43.7042 86.8967C43.2341 87.0842 42.764 87.178 42.2938 86.9904C41.8237 86.8967 41.4476 86.5216 41.2595 86.1465C41.0715 85.7715 40.9774 85.2089 41.1655 84.7401L51.2264 54.7352V54.7352Z"
          fill="#FED241"
        />
        <Path
          d="M76.5195 100.5L73.0405 98.8125L70.0317 102.376L74.545 106.783L76.5195 100.5Z"
          fill="#DCAA86"
        />
        <Path
          d="M71.8181 102.189L73.0404 100.689C74.3568 99.0947 74.0747 96.7506 72.5703 95.4379L64.2959 88.593C62.6974 87.2803 60.3468 87.5616 59.0304 89.0618C57.0558 91.4997 57.4319 95.0628 59.8766 97.1256L66.7406 102.752C68.057 103.97 70.5017 103.689 71.8181 102.189V102.189Z"
          fill="#DCAA86"
        />
        <Path
          d="M71.7244 97.6867C70.7842 97.968 69.7499 97.4055 69.5618 96.374L68.1514 89.3417C67.9633 88.3102 68.5275 86.9975 69.5618 86.81C70.314 86.6225 71.5364 87.0913 71.7244 88.1227L73.1348 95.1551C73.3229 96.2803 72.6647 97.4055 71.7244 97.6867Z"
          fill="#DCAA86"
        />
        <Path
          d="M107.83 92.9008C107.83 92.9008 114.694 112.404 76.6132 100.496L73.0402 98.808L70.1253 102.371L69.4672 103.403C69.4672 103.403 94.9485 124.5 114.506 115.498C114.506 115.498 132.747 107.622 118.361 78.2734L107.83 92.9008V92.9008Z"
          fill="#DCAA86"
        />
        <Path
          d="M27.9951 14.1132C28.8518 12.6289 30.2571 12.6289 31.1138 14.1132L47.3315 42.2009C48.1882 43.6852 47.4892 44.9027 45.772 44.9027H13.3368C11.6234 44.9027 10.9207 43.6889 11.7774 42.2009L27.9951 14.1132Z"
          fill="#E87722"
        />
        <Path
          d="M16.8315 40.0936L28.4461 19.9757C28.9384 19.1227 30.167 19.1227 30.6593 19.9757L42.274 40.0936C42.6535 40.7512 42.18 41.5703 41.421 41.5703H17.6844C16.9292 41.5741 16.4519 40.7512 16.8315 40.0936Z"
          fill="white"
        />
        <Path
          d="M28.5065 31.1614C28.3035 29.6997 28.1382 28.4635 28.0142 27.4602C27.8902 26.4569 27.8301 25.7243 27.8301 25.2658C27.8301 24.8074 27.9879 24.4128 28.2998 24.0859C28.6117 23.759 29.0325 23.5938 29.5548 23.5938C30.0771 23.5938 30.498 23.759 30.8099 24.0859C31.1218 24.4128 31.2796 24.8074 31.2796 25.2658C31.2796 25.7243 31.2157 26.4569 31.0955 27.4602C30.9715 28.4635 30.8061 29.6997 30.6032 31.1614C30.4003 32.6231 30.2462 33.8293 30.1448 34.7762H28.9649C28.8634 33.8293 28.7094 32.6231 28.5065 31.1614ZM28.3637 39.0448C28.0368 38.7179 27.8715 38.3196 27.8715 37.8537C27.8715 37.3877 28.0368 36.9894 28.3637 36.6625C28.6906 36.3356 29.0889 36.1703 29.5548 36.1703C30.0208 36.1703 30.419 36.3356 30.7459 36.6625C31.0729 36.9894 31.2382 37.3877 31.2382 37.8537C31.2382 38.3196 31.0729 38.7179 30.7459 39.0448C30.419 39.3717 30.0208 39.537 29.5548 39.537C29.0889 39.537 28.6906 39.3717 28.3637 39.0448Z"
          fill="#183028"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_11628_34904">
          <Rect
            width="128"
            height="104"
            fill="white"
            transform="matrix(-1 0 0 1 128 0)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default BoyCharacter;
