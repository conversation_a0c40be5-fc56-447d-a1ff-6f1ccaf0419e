import { Fragment, memo, useCallback, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { Button, Icon, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import OthersScenario from './scenarios/others/OthersScenario';
import { decisionsList } from 'features/eApp/hooks/useUWMEDecision';
import { DecisionState, DecisionsType } from 'features/eApp/types/uwmeTypes';
import { useTheme } from '@emotion/react';
import ApprovedScenario from './scenarios/approved/ApprovedScenario';
import RevisedOfferScenario from './scenarios/revisedOffer/RevisedOfferScenario';
import UwmeDecisionTabView from './UwmeDecisionTabView';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { Path, Svg } from 'react-native-svg';

interface Props {
  decisionState: DecisionState;
  decisions: DecisionsType;
  onClose: () => void;
  onPress: () => void;
  setGoToPdfScreen: (value: boolean) => void;
}

export const UwmeDecisionModal = memo(function UwmeDecisionModal({
  decisionState,
  decisions,
  onClose,
  onPress,
  setGoToPdfScreen,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { bottom } = useSafeAreaInsets();
  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();
  const [decisionModalPage, setDecisionModalPage] = useState<number>(() =>
    ['StandardWithExclusionOnly', 'SubStandardWithRatingAndExclusion'].includes(
      decisions,
    )
      ? 1
      : 0,
  );
  const { space } = useTheme();
  const maxContentHeight = (snapPoints[1] as number) - space[37] - bottom;

  const title = useMemo(() => {
    switch (decisions) {
      case decisionsList['Standard']:
        return t('eApp:review.decision.goodNews');
      case decisionsList['Refer/Decline/Postpone/Incomplete']:
        return t('eApp:review.decision.others.title');
      default:
        return t('eApp:review.decision.youAreAlmostThere');
    }
  }, [decisions]);

  const onlyOnePage = useMemo(() => {
    return [
      decisionsList['Standard'],
      decisionsList['SubStandardWithRating'],
      decisionsList['StandardWithAdminRequirements'],
      decisionsList['EvidenceNeeded'],
      decisionsList['Refer/Decline/Postpone/Incomplete'],
    ].includes(decisions);
  }, [decisions]);

  const decisionSnapPoints = useMemo(() => {
    if (decisions === decisionsList['Standard']) return [500];
    if (decisions === decisionsList['Refer/Decline/Postpone/Incomplete'])
      return [550];
    return [snapPoints[1]];
  }, [decisions, snapPoints]);

  const onPressStep = useCallback(() => {
    if (onlyOnePage || decisionModalPage === 2) {
      onPress();
      return;
    }
    if (decisionModalPage === 0) setDecisionModalPage(1);
    if (decisionModalPage === 1) {
      if (
        decisions === decisionsList['SubStandardWithRatingAndAdminRequirements']
      ) {
        onPress();
        return;
      }
      setDecisionModalPage(2);
    }
  }, [
    onPress,
    setDecisionModalPage,
    decisionModalPage,
    onlyOnePage,
    decisions,
  ]);

  const onPressBack = useCallback(() => {
    if (decisionModalPage === 2) setDecisionModalPage(1);
    if (decisionModalPage === 1) setDecisionModalPage(0);
  }, [setDecisionModalPage, decisionModalPage]);

  const { isWideScreen } = useWindowAdaptationHelpers();

  return (
    <BottomSheetModal
      {...bottomSheetProps}
      stackBehavior="push"
      snapPoints={decisionSnapPoints}
      onDismiss={onClose}
      enableContentPanningGesture={false}
      style={styles.bottomSheet}
      footerComponent={() => (
        <ButtonBorderContainer bottomInset={bottom}>
          <Button
            onPress={onPressStep}
            text={
              onlyOnePage ||
              decisionModalPage === 2 ||
              (decisionModalPage === 1 &&
                decisions ===
                  decisionsList['SubStandardWithRatingAndAdminRequirements'])
                ? t('eApp:review.decision.continueToSignature')
                : t('eApp:review.decision.next')
            }
            style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          />
        </ButtonBorderContainer>
      )}>
      {(decisionModalPage === 1 &&
        ![
          decisionsList['StandardWithExclusionOnly'],
          decisionsList['SubStandardWithRatingAndExclusion'],
        ].includes(decisions)) ||
      decisionModalPage === 2 ? (
        <TouchableOpacity onPress={onPressBack}>
          <HeaderPortal>
            <Icon.ArrowLeft size={space[5]} />
            <BackText fontWeight="bold">
              {t('eApp:review.decision.back')}
            </BackText>
          </HeaderPortal>
        </TouchableOpacity>
      ) : (
        <HeaderPortal>
          <ApprovalIcon />
          <HeaderText fontWeight="bold">{title}</HeaderText>
        </HeaderPortal>
      )}

      <BottomSheetView enableFooterMarginAdjustment style={{ flex: 1 }}>
        <DecisionContent
          decisionState={decisionState}
          decisions={decisions}
          decisionModalPage={decisionModalPage}
          maxContentHeight={maxContentHeight}
        />
      </BottomSheetView>
    </BottomSheetModal>
  );
});

interface DecisionContentProps {
  decisionState: DecisionState;
  decisions: DecisionsType;
  decisionModalPage: number;
  maxContentHeight: number;
}

const DecisionContent = memo(
  ({
    decisionState,
    decisions,
    decisionModalPage,
    maxContentHeight,
  }: DecisionContentProps) => {
    const [contentHeight, setContentHeight] = useState(0);

    const scrollable = useMemo(() => {
      return contentHeight > maxContentHeight;
    }, [contentHeight, maxContentHeight]);

    switch (decisions) {
      case decisionsList['Standard']:
        return <ApprovedScenario />;
      case decisionsList['SubStandardWithRating']:
        return (
          <ScrollView
            scrollEnabled={scrollable}
            showsVerticalScrollIndicator={false}>
            <View
              onLayout={e => {
                setContentHeight(e.nativeEvent.layout.height);
              }}>
              <RevisedOfferScenario
                decisions={decisions}
                decisionState={decisionState}
              />
            </View>
          </ScrollView>
        );
      case decisionsList['StandardWithAdminRequirements']:
        return (
          <ScrollView
            scrollEnabled={scrollable}
            showsVerticalScrollIndicator={false}>
            <View
              onLayout={e => {
                setContentHeight(e.nativeEvent.layout.height);
              }}>
              <ApprovedScenario evidences={decisionState.evidences} />
            </View>
          </ScrollView>
        );
      case decisionsList['SubStandardWithRatingAndAdminRequirements']:
        return (
          <UwmeDecisionTabView
            decisions={decisions}
            decisionState={decisionState}
            decisionModalPage={decisionModalPage}
            maxContentHeight={maxContentHeight}
          />
        );
      case decisionsList['StandardWithExclusionOnly']:
        return (
          <UwmeDecisionTabView
            decisions={decisions}
            decisionState={decisionState}
            decisionModalPage={decisionModalPage}
            maxContentHeight={maxContentHeight}
          />
        );

      case decisionsList['SubStandardWithRatingAndExclusion']:
        return (
          <UwmeDecisionTabView
            decisions={decisions}
            decisionState={decisionState}
            decisionModalPage={decisionModalPage}
            maxContentHeight={maxContentHeight}
          />
        );

      case decisionsList['StandardWithExclusionAndAdminRequirements']:
        return (
          <UwmeDecisionTabView
            decisions={decisions}
            decisionState={decisionState}
            decisionModalPage={decisionModalPage}
            maxContentHeight={maxContentHeight}
          />
        );

      case decisionsList['SubStandardWithRatingExclusionAndAdminRequirements']:
        return (
          <UwmeDecisionTabView
            decisions={decisions}
            decisionState={decisionState}
            decisionModalPage={decisionModalPage}
            maxContentHeight={maxContentHeight}
          />
        );
      case decisionsList['EvidenceNeeded']:
        return (
          <ScrollView
            scrollEnabled={scrollable}
            showsVerticalScrollIndicator={false}>
            <View
              onLayout={e => {
                setContentHeight(e.nativeEvent.layout.height);
              }}>
              <ApprovedScenario
                decisions={decisions}
                evidences={decisionState.evidences}
              />
            </View>
          </ScrollView>
        );
      case decisionsList['Refer/Decline/Postpone/Incomplete']:
        return <OthersScenario />;
      default:
        return <Fragment />;
    }
  },
);

export default UwmeDecisionModal;

const styles = StyleSheet.create({
  bottomSheet: {
    padding: 0,
  },
});

const HeaderPortal = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: theme.space[isNarrowScreen ? 2 : 3],
    paddingRight: theme.space[4],
    height: theme.sizes[10],
  };
});

const HeaderText = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onBackground,
  flex: 1,
  marginLeft: theme.space[1],
}));

const BackText = styled(Typography.H8)(({ theme }) => ({
  color: '#E87722',
  flex: 1,
  marginLeft: theme.space[1],
}));

const ButtonBorderContainer = styled.View<{ bottomInset?: number }>(
  ({ theme, bottomInset }) => {
    const { isNarrowScreen } = useWindowAdaptationHelpers();
    return {
      paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
      paddingTop: theme.space[4],
      paddingBottom: theme.space[4] + (bottomInset || 0),
      borderTopWidth: 1,
      borderTopColor: theme.colors.palette.fwdGrey[50],
      flexDirection: 'row',
      justifyContent: 'center',
    };
  },
);

const ApprovalIcon = memo(() => (
  <Svg width={40} height={40} fill="none">
    <Path
      fill="#E87722"
      d="M20 32.857c7.101 0 12.857-5.756 12.857-12.857 0-7.101-5.756-12.857-12.857-12.857-7.1 0-12.857 5.756-12.857 12.857 0 7.1 5.756 12.857 12.857 12.857Z"
    />
    <Path
      fill="#E87722"
      d="M19.918 32.474c6.828 0 12.364-5.536 12.364-12.364 0-6.829-5.536-12.364-12.364-12.364-6.828 0-12.364 5.535-12.364 12.364 0 6.828 5.536 12.364 12.364 12.364Z"
    />
    <Path
      fill="#fff"
      d="M19.78 10.433c-5.263.082-9.485 4.468-9.402 9.732.082 5.263 4.468 9.485 9.732 9.403 5.263-.083 9.485-4.469 9.403-9.732-.083-5.264-4.469-9.485-9.732-9.403Z"
    />
    <Path
      fill="#183028"
      d="M26.47 16.99c.221-.254.387-.591.387-.957 0-.816-.637-1.463-1.44-1.463-.36 0-.693.141-.942.394l-.056.056-6.233 6.216-2.577-2.587-.028-.028a1.392 1.392 0 0 0-.997-.394c-.804 0-1.44.647-1.44 1.462 0 .394.165.732.387 1.013l4.655 4.725 8.228-8.353.055-.085Z"
    />
  </Svg>
));
