import { memo } from 'react';
import styled from '@emotion/native';
import { Box, H6, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import BoyLaptopCharacter from '../../illustrations/BoyLaptopCharacter';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

const Content = styled(Typography.LargeBody)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    color: '#333333',
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    paddingTop: theme.space[4],
  };
});

const OrangeTitle = styled(H6)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    color: theme.colors.palette.fwdOrange['100'],
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
  };
});

export const OthersScenario = memo(function OthersScenario() {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  return (
    <>
      <Row
        alignItems="center"
        justifyContent="space-between"
        mr={space[8]}
        mt={space[4]}>
        <Box flex={1}>
          <OrangeTitle fontWeight="bold">
            {t('eApp:review.decision.others.stayTuned')}
          </OrangeTitle>
        </Box>
        <BoyLaptopCharacter />
      </Row>
      <Content>{t('eApp:review.decision.others.content')}</Content>
    </>
  );
});

export default OthersScenario;
