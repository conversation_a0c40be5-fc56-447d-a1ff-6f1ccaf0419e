import styled from '@emotion/native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import ReviewSectionList from '../common/ReviewSectionList';
import {
  InfoGroup,
  InfoItemType,
  InfoQuestion,
  InfoSectionData,
} from 'features/eApp/types/reviewTypes';
import NavHeader from 'navigation/components/NavHeader';
import { differenceInYears, format } from 'date-fns';
import QuestionContent from '../common/QuestionContent';
import SectionContent from '../common/SectionContent';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { CORRESPONDENCE_ADDRESS_OPTION, PH_COUNTRY } from 'constants/optionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { formatCurrency } from 'utils';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { checkIsInstitution } from 'features/eApp/validations/applicationDetails/personalDetailsValidation';
import { dateFormatUtil } from 'utils/helper/formatUtil';

const notAvailable = 'N/A';

export const PersonalInfoReview = memo(function PersonalInfoReview() {
  const params =
    useRoute<RouteProp<RootStackParamList, 'PersonalInfoReview'>>().params;
  const { data, type, title: rawTitle } = params;

  const quotation = useSelectedQuotation();
  const currency = quotation?.basicInfo.currency || 'PHP';
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const hasPayor = useEAppStore(state => state.hasPayor);
  const reasonForThirdPartyPayment = useEAppStore(
    state => state.payorPersonalInfo.reasonForThirdPartyPayment,
  );
  const hasBeneficialOwner = useEAppStore(state => state.hasBeneficialOwner);

  const getOptionListText = useCallback(
    (
      values?: string[] | string,
      options?: Array<{ value: string; label: string }>,
    ) => {
      if (!values || values.length === 0) {
        return notAvailable;
      }
      if (!options || options.length === 0) {
        return notAvailable;
      }
      if (Array.isArray(values)) {
        return (
          values
            .map(v => {
              const item = options?.find(i => i.value === v);
              return item?.label;
            })
            .join('\n') || notAvailable
        );
      }
      const item = options?.find(i => i.value === values);
      return item?.label || notAvailable;
    },
    [],
  );

  const answer = useMemo(() => {
    let answer: 'yes' | 'no' = 'no';
    if (type === 'beneficialOwner') {
      answer = hasBeneficialOwner ? 'yes' : 'no';
    }
    if (type === 'payor') {
      answer = hasPayor ? 'yes' : 'no';
    }
    return answer;
  }, [type, hasPayor, hasBeneficialOwner]);

  const { t } = useTranslation(['eApp']);
  const title = useMemo(() => {
    return rawTitle ? rawTitle : t(`eApp:review.${type}`);
  }, [type, rawTitle]);

  const { questionTitle, questionItems } = useMemo(() => {
    let questionTitle: string | undefined = undefined;
    const questionItems: Array<{ label: string; text: string }> = [];
    if (type === 'insured') {
      questionItems.push({
        label: t('eApp:review.personal.insured.relationship'),
        text: getOptionListText(
          data.personalDetails.relationship,
          optionList?.OWNER_RELATIONSHIP.options,
        ),
      });
    }
    if (type === 'beneficialOwner') {
      questionTitle = t('eApp:review.personal.beneficialOwner.q1');
      questionItems.push({
        label: t('eApp:review.answer'),
        text: t(`eApp:${answer}`),
      });
    }
    if (type === 'payor') {
      questionTitle = t('eApp:review.personal.payor.q1');
      questionItems.push({
        label: t('eApp:review.answer'),
        text: t(`eApp:${answer}`),
      });
      if (answer === 'yes') {
        questionItems.push({
          label: t('eApp:review.personal.payor.reason'),
          text: reasonForThirdPartyPayment,
        });
        questionItems.push({
          label: t('eApp:review.personal.payor.relationshipWithOwner'),
          text: getOptionListText(
            data.personalDetails.relationshipWithPolicyOwner,
            optionList?.PAYOR_OWNER_RELATIONSHIP.options,
          ),
        });
      }
    }
    if (type === 'beneficiary') {
      questionItems.push({
        label: t('eApp:review.personal.beneficiary.relationshipWithInsured'),
        text: getOptionListText(
          data.personalDetails.relationshipWithInsured,
          optionList?.BENEFICIARY_RELATIONSHIP.options,
        ),
      });
    }
    return {
      questionTitle,
      questionItems,
    };
  }, [
    t,
    type,
    answer,
    reasonForThirdPartyPayment,
    getOptionListText,
    optionList,
    data,
  ]);

  const getProvince = useCallback(
    (province?: string, options?: Array<{ value: string; label: string }>) => {
      return getOptionListText(province, options);
    },
    [getOptionListText],
  );

  const getCity = useCallback(
    (province?: string, city?: string) => {
      const provinceOption = optionList?.PROVINCE.options.find(
        o => o.value === province,
      );
      const cityList = (optionList?.CITY_TOWN.options ?? []).filter(
        city => city.key === provinceOption?.lookupKey,
      );
      return getOptionListText(city, cityList);
    },
    [optionList],
  );

  const sections = useMemo(() => {
    const nameGroup: InfoGroup = {
      title: t('eApp:review.name'),
      items: [
        {
          label: t('eApp:review.salutationTitle'),
          text: getOptionListText(
            data.personalDetails?.title,
            optionList?.TITLE.options,
          ),
        },
        {
          label: t('eApp:review.firstName'),
          text: data.personalDetails?.firstName || notAvailable,
        },
      ],
    };
    if (data.personalDetails?.middleName) {
      nameGroup.items.push({
        label: t('eApp:review.middleName'),
        text: data.personalDetails?.middleName || notAvailable,
      });
    }
    nameGroup.items.push({
      label: t('eApp:review.lastName'),
      text: data.personalDetails?.lastName || notAvailable,
    });
    if (data.personalDetails?.extensionName) {
      nameGroup.items.push({
        label: t('eApp:review.extensionName'),
        text: data.personalDetails?.extensionName || notAvailable,
      });
    }
    let personalInfoGroups: InfoGroup[] | undefined = undefined;
    let beneficiaryOrganizationNameGroups: InfoGroup[] | undefined = undefined;

    if (
      type !== 'beneficiary' ||
      !checkIsInstitution(data.personalDetails.relationshipWithInsured)
    ) {
      personalInfoGroups = [nameGroup];
      if (
        type === 'policyOwner' ||
        type === 'insured' ||
        type === 'payor' ||
        type === 'beneficialOwner'
      ) {
        personalInfoGroups.push({
          title: t('eApp:review.identificationDetails'),
          items: [
            {
              label: t('eApp:review.tinSSSGSIS'),
              text: getOptionListText(
                data.personalDetails.idType,
                optionList?.ID_TYPE.options,
              ),
            },
            {
              label: t('eApp:review.tinSSSGSISNo'),
              text: data.personalDetails.idNumber || '',
            },
          ],
        });
        if (
          type === 'policyOwner' ||
          type === 'insured' ||
          type === 'payor' ||
          type === 'beneficialOwner'
        ) {
          personalInfoGroups[personalInfoGroups.length - 1].items.push(
            {
              label: t('eApp:review.primaryIdentificationType'),
              text: getOptionListText(
                data.personalDetails?.primaryIdType,
                optionList?.PRIMARY_ID_TYPE.options,
              ),
            },
            {
              label: t('eApp:review.primaryIdentificationNumber'),
              text: data.personalDetails?.primaryIdNumber || notAvailable,
            },
            {
              label: t('eApp:review.expiryDate'),
              text: data.personalDetails?.expiryDate
                ? dateFormatUtil(data.personalDetails.expiryDate)
                : notAvailable,
            },
          );
        }
      }
      personalInfoGroups.push({
        title: t('eApp:review.otherDetails'),
        items: [
          {
            label: t('eApp:review.gender'),
            text: data.personalDetails?.gender
              ? t(
                  `eApp:review.gender.${
                    data.personalDetails.gender as 'M' | 'F'
                  }`,
                )
              : notAvailable,
          },
          {
            label: t('eApp:review.dateOfBirth'),
            text: data.personalDetails?.dateOfBirth
              ? dateFormatUtil(data.personalDetails.dateOfBirth)
              : notAvailable,
          },
          {
            label: t('eApp:review.age'),
            text: data.personalDetails?.dateOfBirth
              ? `${getAge(data.personalDetails?.dateOfBirth)} y.o.`
              : notAvailable,
          },
        ],
      });

      if (type === 'policyOwner') {
        personalInfoGroups[personalInfoGroups.length - 1].items.push(
          {
            label: t('eApp:review.maritalStatus'),
            text: data.personalDetails?.maritalStatus
              ? getOptionListText(
                  data.personalDetails.maritalStatus,
                  optionList?.MARITAL_STATUS.options,
                )
              : notAvailable,
          },
          {
            label: t('eApp:review.leadSource'),
            text: data.personalDetails?.leadSource || notAvailable,
          },
        );
      }

      if (type === 'insured') {
        personalInfoGroups[personalInfoGroups.length - 1].items.push({
          label: t('eApp:review.maritalStatus'),
          text: getOptionListText(
            data.personalDetails?.maritalStatus,
            optionList?.MARITAL_STATUS.options,
          ),
        });
      }
    } else {
      beneficiaryOrganizationNameGroups = [
        {
          items: [
            {
              label: t('eApp:review.organizationName'),
              text: data.personalDetails.organizationName || notAvailable,
            },
          ],
        },
      ];
    }

    const contractGroups: InfoGroup[] = [
      {
        title: t('eApp:review.primaryContact'),
        items: [
          {
            label: t('eApp:review.mobileNumber'),
            text: data.contactDetails?.primaryMobile
              ? `+${getCountryCode(data.contactDetails?.primaryCountryCode)} ${
                  data.contactDetails?.primaryMobile
                }`
              : notAvailable,
          },
          {
            label: t('eApp:review.email'),
            text: data.contactDetails?.email || notAvailable,
          },
        ],
      },
    ];
    if (
      (data.contactDetails?.homeMobile ||
        data.contactDetails?.officeMobile ||
        data.contactDetails?.faxMobile) &&
      (type !== 'beneficiary' ||
        !checkIsInstitution(data.personalDetails.relationshipWithInsured))
    ) {
      contractGroups.push({
        title: t('eApp:review.secondaryContact'),
        items: [],
      });
      const items = contractGroups[contractGroups.length - 1].items;
      if (
        data.contactDetails?.homeMobile &&
        data.contactDetails?.homeCountryCode
      ) {
        items.push({
          label: t('eApp:review.homeNumber'),
          text: `+${getCountryCode(data.contactDetails.homeCountryCode)} ${
            data.contactDetails.homeMobile
          }`,
        });
      }
      if (
        data.contactDetails?.officeMobile &&
        data.contactDetails?.officeCountryCode
      ) {
        items.push({
          label: t('eApp:review.officeNumber'),
          text: `+${getCountryCode(data.contactDetails.officeCountryCode)} ${
            data.contactDetails.officeMobile
          }`,
        });
      }
      if (
        data.contactDetails?.faxMobile &&
        data.contactDetails?.faxCountryCode
      ) {
        items.push({
          label: t('eApp:review.faxNumber'),
          text: `+${getCountryCode(data.contactDetails.faxCountryCode)} ${
            data.contactDetails.faxMobile
          }`,
        });
      }
    }

    const addressGroups: InfoGroup[] = [
      {
        title: t('eApp:review.currentAddress'),
        items: [
          {
            label: t('eApp:review.fullAddress'),
            text:
              data.addressInfo?.country === PH_COUNTRY
                ? data.addressInfo?.addressLine1 ||
                  data.addressInfo?.addressLine2
                  ? [
                      data.addressInfo?.addressLine1,
                      data.addressInfo?.addressLine2,
                    ]
                      .filter(Boolean)
                      .join('\n')
                  : notAvailable
                : data.addressInfo?.addressLine1 ||
                  data.addressInfo?.addressLine2 ||
                  data.addressInfo?.addressLine3
                ? [
                    data.addressInfo?.addressLine1,
                    data.addressInfo?.addressLine2,
                    data.addressInfo?.addressLine3,
                  ]
                    .filter(Boolean)
                    .join('\n')
                : notAvailable,
          },
          {
            label: t('eApp:review.country'),
            text: getOptionListText(
              data.addressInfo?.country,
              optionList?.COUNTRY.options,
            ),
          },
        ],
      },
    ];

    if (data.addressInfo?.country === PH_COUNTRY) {
      addressGroups[0].items.push(
        {
          label: t('eApp:review.province'),
          text: getOptionListText(
            data.addressInfo?.province,
            optionList?.PROVINCE.options,
          ),
        },
        {
          label: t('eApp:review.city'),
          text: getCity(data.addressInfo?.province, data.addressInfo?.city),
        },
        {
          label: t('eApp:review.postalCode'),
          text: data.addressInfo?.postalCode || notAvailable,
        },
      );
    }

    if (type !== 'beneficiary') {
      if (data.addressInfo.businessAddress === CORRESPONDENCE_ADDRESS_OPTION) {
        addressGroups.push({
          title: t('eApp:review.businessAddress'),
          items: addressGroups[0].items,
        });
      } else {
        addressGroups.push({
          title: t('eApp:review.businessAddress'),
          items: [
            {
              label: t('eApp:review.fullAddress'),
              text:
                data.addressInfo?.businessCountry === PH_COUNTRY
                  ? data.addressInfo?.businessAddressLine1 ||
                    data.addressInfo?.businessAddressLine2
                    ? `${data.addressInfo?.businessAddressLine1}\n${data.addressInfo?.businessAddressLine2}`
                    : notAvailable
                  : data.addressInfo?.businessAddressLine1 ||
                    data.addressInfo?.businessAddressLine2 ||
                    data.addressInfo?.businessAddressLine3
                  ? `${data.addressInfo?.businessAddressLine1}\n${data.addressInfo?.businessAddressLine2}\n${data.addressInfo?.businessAddressLine3}`
                  : notAvailable,
            },
            {
              label: t('eApp:review.country'),
              text: getOptionListText(
                data.addressInfo?.businessCountry,
                optionList?.COUNTRY.options,
              ),
            },
          ],
        });
        if (data.addressInfo?.businessCountry === PH_COUNTRY) {
          addressGroups[addressGroups.length - 1].items.push(
            {
              label: t('eApp:review.province'),
              text: getProvince(
                data.addressInfo?.businessProvince,
                optionList?.PROVINCE.options,
              ),
            },
            {
              label: t('eApp:review.city'),
              text: getCity(
                data.addressInfo?.businessProvince,
                data.addressInfo?.businessCity,
              ),
            },
            {
              label: t('eApp:review.postalCode'),
              text: data.addressInfo?.businessPostalCode || notAvailable,
            },
          );
        }
      }
    }

    let nationalityDetails: InfoGroup[] | undefined = undefined;
    if (
      type !== 'beneficiary' ||
      !checkIsInstitution(data.personalDetails.relationshipWithInsured)
    ) {
      nationalityDetails = [
        {
          items: [
            {
              label: t('eApp:review.nationality'),
              text: getOptionListText(
                data.nationalityDetails?.nationality,
                optionList?.NATIONALITY.options,
              ),
            },
            {
              label: t('eApp:review.countryOfBirth'),
              text: getOptionListText(
                data.nationalityDetails?.countryOfBirth,
                optionList?.COUNTRY.options,
              ),
            },
            {
              label: t('eApp:review.placeOfBirth'),
              text:
                data.nationalityDetails?.countryOfBirth === PH_COUNTRY
                  ? getOptionListText(
                      data.nationalityDetails?.placeOfBirth,
                      optionList?.CITY_TOWN.options,
                    )
                  : data.nationalityDetails?.placeOfBirth || notAvailable,
            },
          ],
        },
      ];
    }

    let occupationDetails: InfoGroup[] | undefined;
    if ('occupationDetails' in data) {
      occupationDetails = [
        {
          items: [
            {
              label: t('eApp:review.occupation'),
              text: getOptionListText(
                data.occupationDetails?.occupationType,
                optionList?.OCCUPATION_GROUP?.options,
              ),
            },
          ],
        },
      ];
      if (data.occupationDetails?.occupationTypeDetail) {
        occupationDetails[0].items.push({
          label: t('eApp:review.occupationDetails'),
          text: getOptionListText(
            data.occupationDetails?.occupationTypeDetail,
            optionList?.OCCUPATION_SUBGROUP?.options,
          ),
        });
      }
      if (type === 'payor') {
        occupationDetails[0].items.push({
          label: t('eApp:review.occupation.natureOfBusiness'),
          text: getOptionListText(
            data.occupationDetails.natureOfBusiness,
            optionList?.BUSINESS_NATURE.options,
          ),
        });
      }
      occupationDetails[0].items.push(
        {
          label: t('eApp:review.nameOfEmployer'),
          text: data.occupationDetails?.nameOfEmployer || notAvailable,
        },
        {
          label: t('eApp:review.annualIncome'),
          text: data.occupationDetails?.annualIncome
            ? `${currency} ${formatCurrency(
                Number(data.occupationDetails?.annualIncome) || 0,
              )}`
            : notAvailable,
        },
      );
      if (type === 'payor') {
        occupationDetails[0].items.push({
          label: t('eApp:review.personalInfo.additionalDetails.sourceOfFund'),
          text: getOptionListText(
            data.occupationDetails.sourceOfFund,
            optionList?.FUND_SOURCE.options,
          ),
        });
        if (data.occupationDetails.otherSourceOfFund) {
          occupationDetails[0].items.push({
            label: t(
              'eApp:review.personalInfo.additionalDetails.otherSourceOfFund',
            ),
            text: data.occupationDetails.otherSourceOfFund,
          });
        }
      }
    }

    let industryAffiliationQuestions: InfoQuestion[] | undefined;
    if ('industryAffiliation' in data) {
      industryAffiliationQuestions = [
        {
          title: t('eApp:review.personalInfo.industryAffiliation.q1'),
          indexVisible: true,
          items: [
            {
              label: t('eApp:review.answer'),
              text: data.industryAffiliation?.question1
                ? t(
                    `eApp:${
                      data.industryAffiliation.question1 as 'yes' | 'no'
                    }`,
                  )
                : notAvailable,
            },
          ],
        },
        {
          title: t('eApp:review.personalInfo.industryAffiliation.q2'),
          indexVisible: true,
          items: [
            {
              label: t('eApp:review.answer'),
              text: data.industryAffiliation?.question2
                ? t(
                    `eApp:${
                      data.industryAffiliation?.question2 as 'yes' | 'no'
                    }`,
                  )
                : notAvailable,
            },
          ],
        },
      ];
      if (data.industryAffiliation?.question1 === 'yes') {
        industryAffiliationQuestions[0].items.push({
          label: t('eApp:review.exactAffiliation'),
          text: getOptionListText(
            data.industryAffiliation?.question1Affiliation,
            optionList?.EXACT_AFFILIATION_1.options,
          ),
        });
      }

      if (data.industryAffiliation?.question2 === 'yes') {
        industryAffiliationQuestions[1].items.push({
          label: t('eApp:review.exactAffiliation'),
          text: getOptionListText(
            data.industryAffiliation?.question2Affiliation,
            optionList?.EXACT_AFFILIATION_2.options,
          ),
        });
      }
    }

    let additionalDetails: InfoGroup[] | undefined;
    if (type === 'policyOwner') {
      additionalDetails = [
        {
          items: [
            {
              label: t(
                'eApp:review.personalInfo.additionalDetails.purposeOfInsurance',
              ),
              text: getOptionListText(
                data.additionalDetails.purposeOfInsurance,
                optionList?.INSURANCE_PURPOSE.options,
              ),
            },
            data.additionalDetails.otherPurposeOfInsurance
              ? {
                  label: t(
                    'eApp:review.personalInfo.additionalDetails.otherPurposeOfInsurance',
                  ),
                  text: data.additionalDetails.otherPurposeOfInsurance,
                }
              : undefined,
            {
              label: t(
                'eApp:review.personalInfo.additionalDetails.sourceOfFund',
              ),
              text: getOptionListText(
                data.additionalDetails.sourceOfFund,
                optionList?.FUND_SOURCE.options,
              ),
            },
            data.additionalDetails.otherSourceOfFund
              ? {
                  label: t(
                    'eApp:review.personalInfo.additionalDetails.otherSourceOfFund',
                  ),
                  text: data.additionalDetails.otherSourceOfFund,
                }
              : undefined,
            data.additionalDetails.sourceOfPremium
              ? {
                  label: t(
                    'eApp:review.personalInfo.additionalDetails.sourceOfPremium',
                  ),
                  text: getOptionListText(
                    data.additionalDetails.sourceOfPremium,
                    optionList?.PREMIUM_SOURCE.options,
                  ),
                }
              : undefined,
          ].filter(Boolean) as InfoGroup['items'],
        },
      ];
    }

    let usTaxQuestions: InfoQuestion[] | undefined;
    if ('usTaxDeclaration' in data) {
      usTaxQuestions = [
        {
          title: t('eApp:review.personalInfo.usTax.q1'),
          indexVisible: true,
          items: [
            {
              label: t('eApp:review.answer'),
              text: data.usTaxDeclaration?.isUSCitizen
                ? t(`eApp:${data.usTaxDeclaration.isUSCitizen as 'yes' | 'no'}`)
                : notAvailable,
            },
          ],
        },
      ];
      if (data.usTaxDeclaration?.isUSCitizen === 'yes') {
        usTaxQuestions[0].items.push({
          label: t('eApp:review.passportGreenCard'),
          text: data.usTaxDeclaration?.passport || notAvailable,
        });
        usTaxQuestions[0].items.push({
          label: t('eApp:review.taxIDNumber'),
          text: data.usTaxDeclaration?.taxId || notAvailable,
        });
        usTaxQuestions[0].items.push({
          label: t('eApp:review.usResidenceAddress'),
          text: data.usTaxDeclaration?.usAddress || notAvailable,
        });
      }
    }

    let benefitPercentage: InfoGroup[] = [];
    if (type === 'beneficiary') {
      const revocable =
        data.personalDetails?.designation.toLowerCase() === 'revocable';
      benefitPercentage = [
        {
          items: [
            {
              label: t('eApp:review.personalInfo.typeOfBeneficiary'),
              text:
                data.personalDetails?.beneficiaryType === 'PB'
                  ? t('eApp:review.personalInfo.primary')
                  : data.personalDetails?.beneficiaryType === 'SB'
                  ? t('eApp:review.personalInfo.secondary')
                  : notAvailable,
            },
            {
              label: t('eApp:review.personalInfo.revocableBeneficiary'),
              text: revocable ? t('eApp:yes') : t('eApp:no'),
            },
            {
              label: t('eApp:review.personalInfo.percentage'),
              text: `${params.percentage}%`,
            },
          ],
        },
      ];
    }

    const sections: InfoSectionData[] = [
      {
        title: t('eApp:review.contactDetails'),
        type: 'normal',
        data: contractGroups,
      },
      {
        title: t('eApp:review.addressInfo'),
        type: 'normal',
        data: addressGroups,
      },
    ];
    if (personalInfoGroups) {
      sections.unshift({
        title: t('eApp:review.personalInfo'),
        type: 'normal',
        data: personalInfoGroups,
      });
    }
    if (beneficiaryOrganizationNameGroups) {
      sections.unshift({
        title: t('eApp:review.personalInfo'),
        type: 'normal',
        data: beneficiaryOrganizationNameGroups,
      });
    }
    if (nationalityDetails) {
      sections.push({
        title: t('eApp:review.nationalityDetails'),
        type: 'normal',
        data: nationalityDetails,
      });
    }
    if (occupationDetails) {
      sections.push({
        title: t('eApp:review.occupationDetails'),
        type: 'normal',
        data: occupationDetails,
      });
    }
    if (industryAffiliationQuestions) {
      sections.push({
        title: t('eApp:review.industryAffiliation'),
        type: 'question',
        data: industryAffiliationQuestions,
      });
    }
    if (additionalDetails) {
      sections.push({
        title: t('eApp:review.additionalDetails'),
        type: 'normal',
        data: additionalDetails,
      });
    }
    if (usTaxQuestions) {
      sections.push({
        title: t('eApp:review.usTaxDeclaration'),
        type: 'question',
        data: usTaxQuestions,
      });
    }

    if (type === 'beneficiary') {
      sections.unshift({
        title: t('eApp:review.personalInfo.benefitPercentage'),
        type: 'normal',
        data: benefitPercentage,
      });
    }
    if (questionItems.length > 0) {
      sections.unshift({
        title: '',
        type: 'customQuestion',
        data: [
          {
            title: questionTitle,
            indexVisible: false,
            items: questionItems,
          },
        ],
      });
    }
    return sections;
  }, [
    t,
    type,
    answer,
    data,
    params,
    questionTitle,
    questionItems,
    getOptionListText,
    optionList,
    currency,
  ]);

  const showDetail = useMemo(() => {
    if (type === 'payor') {
      return answer === 'yes';
    }
    return true;
  }, [type, answer]);

  const renderItem = useCallback(
    ({
      item,
      section,
      index,
    }: {
      item: InfoItemType;
      section: { title: string; type: string };
      index: number;
    }) => {
      if (item) {
        if (section.type === 'customQuestion') {
          return (
            <QuestionContainer>
              <QuestionContent {...item} index={index} />
            </QuestionContainer>
          );
        }
        if (section.type === 'question') {
          return <QuestionContent {...item} index={index} />;
        }
        return <SectionContent {...item} />;
      }
      return null;
    },
    [],
  );

  return (
    <Container>
      <NavHeader title={title} />
      <ContentContainer>
        {showDetail && (
          <ReviewSectionList sections={sections} renderItem={renderItem} />
        )}
      </ContentContainer>
    </Container>
  );
});
export default PersonalInfoReview;

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));
const ContentContainer = styled.View(() => ({
  flex: 1,
  marginTop: 1,
}));

const QuestionContainer = styled.View(({ theme }) => ({
  paddingTop: theme.space[4],
}));

export const getCountryCode = (raw: string) => {
  try {
    return raw.replace(/[^+0-9]/g, '');
  } catch (e) {
    return notAvailable;
  }
};

const getAge = (date: string | Date) => {
  try {
    return differenceInYears(
      new Date(),
      typeof date === 'string' ? Date.parse(date) : date,
    );
  } catch (e) {
    return 0;
  }
};
