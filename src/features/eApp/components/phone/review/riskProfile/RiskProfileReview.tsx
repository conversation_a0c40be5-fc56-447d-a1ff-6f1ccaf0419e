import styled from '@emotion/native';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  InfoItemType,
  InfoQuestion,
  InfoSectionData,
} from 'features/eApp/types/reviewTypes';
import { questionDataMocks } from 'features/proposal/components/RpqQuestionForm/RpqQuestionForm';
import { QuestionMocksProps } from 'features/proposal/components/RpqQuestionForm/types';
import NavHeader from 'navigation/components/NavHeader';
import { memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import QuestionContent from '../common/QuestionContent';
import ReviewSectionList from '../common/ReviewSectionList';
import SectionContent from '../common/SectionContent';
import ClientWaiverContent from './content/ClientWaiverContent';
import ResultSectionContent from './content/ResultSectionContent';

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const ContentContainer = styled.View(() => ({
  flex: 1,
  marginTop: 1,
}));

export const RiskProfileReview = memo(function RiskProfileReview() {
  const { t } = useTranslation(['proposal', 'eApp']);
  const questionsMocks: QuestionMocksProps[] = questionDataMocks();

  const { rpqResult } =
    useRoute<RouteProp<RootStackParamList, 'RiskProfileReview'>>().params.data;

  const questions: InfoQuestion[] = useMemo(() => {
    if (!rpqResult || !questionsMocks.length) return [];

    const results: InfoQuestion[] = [];
    questionsMocks.forEach(question => {
      const answer = rpqResult?.questions?.[question.name];
      if (answer) {
        const answerId = Number(answer.replace('_1', '')) - 1;
        const currentAnswer = question.listQuestions.find(
          e => e.id === answerId,
        );
        let answerText = currentAnswer?.title || '';
        if (currentAnswer?.value) {
          answerText += `\n${currentAnswer?.value}`;
        }
        results.push({
          title: question.questionTitle,
          indexVisible: true,
          items: [
            {
              label: 'Answer',
              text: answerText,
            },
          ],
        });
      }
    });

    return results;
  }, [questionsMocks, rpqResult]);

  const waiverQuestions = [
    {
      title: t('proposal:rpq.q6'),
      items: [
        { label: t('eApp:review.answer'), text: t('eApp:yes') },
        {
          label: t('proposal:rpq.acceptedDate'),
          text: `${
            rpqResult?.rpqAcceptedDate
              ? dateFormatUtil(new Date(rpqResult?.rpqAcceptedDate))
              : ''
          }`,
        },
      ],
    },
  ];

  const sections: InfoSectionData[] = [
    {
      title: t(`eApp:review.financialProfile`),
      type: 'question',
      data: questions,
    },
    {
      title: t(`eApp:review.rpqResult`),
      type: 'result',
      data: [undefined],
    },
    {
      title: t(`eApp:review.clientWaiver`),
      type: 'waiver',
      data: waiverQuestions,
    },
  ];

  const renderItem = useCallback(
    ({
      item,
      section,
      index,
    }: {
      item: InfoItemType;
      section: { title: string; type: string };
      index: number;
    }) => {
      if (section.type === 'result') {
        return <ResultSectionContent rpqResult={rpqResult} />;
      }
      if (item) {
        if (section.type === 'question') {
          return <QuestionContent {...item} index={index} />;
        }
        if (section.type === 'waiver') {
          return <ClientWaiverContent {...item} index={index} />;
        }
        return <SectionContent {...item} />;
      }
      return null;
    },
    [],
  );

  return (
    <Container>
      <NavHeader title={t(`eApp:review.rpqPolicyOwner`)} />
      <ContentContainer>
        <ReviewSectionList sections={sections} renderItem={renderItem} />
      </ContentContainer>
    </Container>
  );
});
export default RiskProfileReview;
