import styled from '@emotion/native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { InfoQuestion, InfoSectionData } from 'features/eApp/types/reviewTypes';
import NavHeader from 'navigation/components/NavHeader';
import ReviewSectionList from '../common/ReviewSectionList';

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const ContentContainer = styled.View(() => ({
  flex: 1,
  marginTop: 1,
}));

export const DataPrivacyReview = memo(function DataPrivacyReview() {
  const { acceptDataPrivacy, acceptInform } =
    useRoute<RouteProp<RootStackParamList, 'DataPrivacyReview'>>().params;

  const { t } = useTranslation(['eApp']);
  const title = useMemo(() => {
    return t(`eApp:review.dataPrivacyAndConsentDeclaration`);
  }, []);

  const sections = useMemo(() => {
    const questions: InfoQuestion[] = [
      ...t('eApp:consents.dataPrivacy.description.fullNote')
        .split('\n')
        .map((text, index) => ({
          title: index ? `${index}. ${text}` : text,
          indexVisible: false,
          items: [],
        })),
      {
        title: t('eApp:review.privacy.q1'),
        indexVisible: false,
        items: [
          {
            label: t('eApp:review.answer'),
            text: acceptDataPrivacy ? t('eApp:yes') : t('eApp:no'),
          },
        ],
      },
      {
        title: t('eApp:review.privacy.q2'),
        indexVisible: false,
        items: [
          {
            label: t('eApp:review.answer'),
            text: acceptInform ? t('eApp:yes') : t('eApp:no'),
          },
        ],
      },
    ];

    const sections: InfoSectionData[] = [
      {
        title: t(`eApp:review.dataPrivacyAndConsentDeclaration`),
        type: 'question',
        data: questions,
      },
    ];
    return sections;
  }, [t, acceptDataPrivacy, acceptInform]);

  return (
    <Container>
      <NavHeader title={title} />
      <ContentContainer>
        <ReviewSectionList sections={sections} />
      </ContentContainer>
    </Container>
  );
});
export default DataPrivacyReview;
