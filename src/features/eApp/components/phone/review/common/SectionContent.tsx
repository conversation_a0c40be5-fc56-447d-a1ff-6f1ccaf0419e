import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { memo } from 'react';
import LabelView, { LabelViewProps } from './LabelView';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

const Container = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    paddingBottom: theme.space[4],
  };
});

const Title = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.primary,
}));

interface Props extends Omit<LabelViewProps, 'label' | 'text'> {
  title?: string;
  items: Array<{
    label: string;
    text: string;
  }>;
}

export const SectionContent = memo(function SectionContent({
  title,
  items,
  ...rest
}: Props) {
  return (
    <Container>
      {!!title && <Title fontWeight="bold">{title}</Title>}
      {items.map((item, index) => (
        <LabelView
          key={index}
          {...item}
          {...rest}
          topSpacing={index === 0 && !title ? 0 : rest.topSpacing}
        />
      ))}
    </Container>
  );
});
export default SectionContent;
