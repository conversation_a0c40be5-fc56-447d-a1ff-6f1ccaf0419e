import { memo } from 'react';
import ReviewSection from './ReviewSection';
import SectionItem, { SectionItemData, SectionItemProps } from './SectionItem';

export interface ListSectionProps<T>
  extends Pick<SectionItemProps<T>, 'onPress'> {
  title: string;
  data: Array<SectionItemData<T>>;
}

const ListSection = <T,>({ title, data, onPress }: ListSectionProps<T>) => {
  return (
    <ReviewSection title={title}>
      {data.map((i, index) => (
        <SectionItem key={index} {...i} onPress={onPress} index={index} />
      ))}
    </ReviewSection>
  );
};
export default memo(ListSection) as typeof ListSection;
