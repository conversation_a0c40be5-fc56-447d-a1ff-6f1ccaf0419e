import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTheme } from '@emotion/react';
import { Box, Button, Column, LargeBody, LargeLabel } from 'cube-ui-components';
import styled from '@emotion/native';
import {
  FlatList,
  Image,
  Modal,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { usePermissions, getAssetsAsync, Asset } from 'expo-media-library';
import * as Application from 'expo-application';
import { useWindowDimensions } from 'react-native';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlatList,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { goToAppSettings } from 'utils/helper/permissionUtils';

interface Props {
  visible?: boolean;
  panel?: boolean;
  onDismiss?: () => void;
  onDone?: (result: {
    uri: string;
    width: number;
    height: number;
    name: string;
  }) => void;
}

export default function GalleryImagePicker({
  visible,
  panel,
  onDismiss,
  onDone,
}: Props) {
  const { colors, space, borderRadius } = useTheme();
  const { height } = useSafeAreaFrame();
  const { top: topInset } = useSafeAreaInsets();
  const [selectedPhoto, setSelectedPhoto] = useState<Asset | null>();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => [height - space[22]], [height, space]);
  useEffect(() => {
    if (visible) {
      setSelectedPhoto(null);
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.close();
    }
  }, [visible]);

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return React.createElement(BottomSheetBackdrop, {
      ...props,
      appearsOnIndex: 0,
      disappearsOnIndex: -1,
    });
  }, []);
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [colors],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  return (
    <>
      {panel ? (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              ref={bottomSheetModalRef}
              snapPoints={snapPoints}
              onDismiss={onDismiss}
              handleIndicatorStyle={handleIndicatorStyle}
              handleStyle={handleStyle}
              backdropComponent={renderBackdrop}
              backgroundStyle={backgroundStyle}>
              <Header
                style={{
                  paddingHorizontal: space[3],
                  paddingVertical: space[4] - 2,
                }}>
                <PanelButton>
                  <LargeLabel fontWeight="bold">Recent photo</LargeLabel>
                </PanelButton>
                <Box flex={1} />
                <PanelButton
                  disabled={!selectedPhoto}
                  onPress={() => {
                    if (selectedPhoto) {
                      onDone?.({
                        uri: selectedPhoto.uri,
                        width: selectedPhoto.width,
                        height: selectedPhoto.height,
                        name: selectedPhoto.filename,
                      });
                      onDismiss?.();
                    }
                  }}>
                  <LargeBody
                    color={
                      selectedPhoto ? colors.primary : colors.primaryVariant
                    }
                    fontWeight="bold">
                    Select
                  </LargeBody>
                </PanelButton>
              </Header>
              <Gallery
                isBottomSheet
                visible={visible}
                selectedPhoto={selectedPhoto}
                setSelectedPhoto={setSelectedPhoto}
              />
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      ) : (
        <Modal visible={visible} statusBarTranslucent animationType="fade">
          <Column
            flex={1}
            marginTop={topInset}
            backgroundColor={colors.background}>
            <Header style={{ paddingHorizontal: space[4] }}>
              <Title fontWeight="bold">Photo album</Title>
              <ActionButton onPress={onDismiss}>
                <LargeBody>Cancel</LargeBody>
              </ActionButton>
              <Box flex={1} />
              <ActionButton
                disabled={!selectedPhoto}
                onPress={() => {
                  if (selectedPhoto) {
                    onDone?.({
                      uri: selectedPhoto.uri,
                      width: selectedPhoto.width,
                      height: selectedPhoto.height,
                      name: selectedPhoto.filename,
                    });
                    onDismiss?.();
                  }
                }}>
                <LargeBody
                  color={selectedPhoto ? colors.primary : colors.primaryVariant}
                  fontWeight="bold">
                  Done
                </LargeBody>
              </ActionButton>
            </Header>
            <Gallery
              visible={visible}
              selectedPhoto={selectedPhoto}
              setSelectedPhoto={setSelectedPhoto}
            />
          </Column>
        </Modal>
      )}
    </>
  );
}

const Gallery = ({
  isBottomSheet,
  visible,
  selectedPhoto,
  setSelectedPhoto,
}: {
  isBottomSheet?: boolean;
  visible?: boolean;
  selectedPhoto: Asset | null | undefined;
  setSelectedPhoto: Dispatch<SetStateAction<Asset | null | undefined>>;
}) => {
  const { space } = useTheme();
  const { width } = useWindowDimensions();
  const imageSize = width / 4;

  const [permissionResponse, requestPermission] = usePermissions();
  const [photos, setPhotos] = useState<Asset[]>([]);
  useEffect(() => {
    if (permissionResponse?.granted) {
      const getAllPhotos = async () => {
        let page = await getAssetsAsync({
          mediaType: 'photo',
          sortBy: [['creationTime', false]],
        });
        do {
          setPhotos(p => [...p, ...page.assets]);
          page = await getAssetsAsync({
            mediaType: 'photo',
            sortBy: [['creationTime', false]],
            after: page.endCursor,
          });
        } while (page.hasNextPage);
      };
      getAllPhotos();
    }
  }, [permissionResponse?.granted]);

  useEffect(() => {
    if (
      visible &&
      !permissionResponse?.granted &&
      permissionResponse?.canAskAgain
    ) {
      requestPermission();
    }
  }, [
    visible,
    permissionResponse?.granted,
    permissionResponse?.canAskAgain,
    requestPermission,
  ]);

  const onSelectPhoto = useCallback(
    (photo: Asset) =>
      setSelectedPhoto(selectedPhoto =>
        selectedPhoto?.uri !== photo.uri ? photo : null,
      ),
    [],
  );
  const List = isBottomSheet ? BottomSheetFlatList : FlatList;
  return (
    <>
      {permissionResponse ? (
        permissionResponse.granted ? (
          <List
            data={photos}
            keyExtractor={item => item.uri}
            numColumns={4}
            renderItem={({ item }) => (
              <Photo
                item={item}
                selected={item === selectedPhoto}
                onSelectPhoto={onSelectPhoto}
                imageSize={imageSize}
              />
            )}
          />
        ) : (
          <Column flex={1} justifyContent="center" alignItems="center">
            <LargeBody style={{ textAlign: 'center' }}>
              Allow {Application.applicationName} access to your photos.
            </LargeBody>
            <Button
              style={{ marginTop: space[4] }}
              text={
                permissionResponse?.canAskAgain ? 'Turn on' : 'Go to settings'
              }
              onPress={() => {
                if (permissionResponse?.canAskAgain) {
                  requestPermission();
                } else {
                  goToAppSettings();
                }
              }}
            />
          </Column>
        )
      ) : null}
    </>
  );
};

const Header = styled(View)(() => ({
  flexDirection: 'row',
}));

const PanelButton = styled(TouchableOpacity)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const ActionButton = styled(TouchableOpacity)(({ theme: { sizes } }) => ({
  height: sizes[11],
  justifyContent: 'center',
  alignItems: 'center',
}));

const Title = styled(LargeLabel)({
  position: 'absolute',
  alignSelf: 'center',
  left: 0,
  right: 0,
  textAlign: 'center',
});

const Photo = React.memo(
  ({
    item,
    selected,
    imageSize,
    onSelectPhoto,
  }: {
    item: Asset;
    selected: boolean;
    imageSize: number;
    onSelectPhoto: (photo: Asset) => void;
  }) => {
    return (
      <Pressable onPress={() => onSelectPhoto(item)}>
        <Image
          style={{ width: imageSize, height: imageSize }}
          source={{ uri: item.uri }}
          resizeMode="cover"
        />
        {selected && <SelectionForeground />}
      </Pressable>
    );
  },
);

const SelectionForeground = styled(View)(({ theme }) => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: theme.colors.background,
  opacity: 0.7,
}));
