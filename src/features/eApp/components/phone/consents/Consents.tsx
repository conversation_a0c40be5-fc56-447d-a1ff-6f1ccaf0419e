import { useTheme } from '@emotion/react';
import { Body, Box, Checkbox, H6 } from 'cube-ui-components';
import React, { memo, useCallback, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import ConsentQuestionItem from './ConsentQuestionItem';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { ScrollView, Platform, KeyboardAvoidingView } from 'react-native';
import DataPrivacyModal from './modals/DataPrivacyModal';
import { useTranslation } from 'react-i18next';
import FormAction from '../common/FormAction';
import ExistingPolicy from './ExistingPolicy';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import {
  ExistingPoliciesForm,
  ExistingPolicyForm,
  existingPolicySchema,
} from 'features/eApp/validations/consentsValidation';
import DataPrivacyReminderModal from './modals/DataPrivacyReminderModal';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useIncompleteSync } from 'features/eApp/hooks/useIncompleteSync';

export default memo(function Consents() {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);

  const [seeMore, setSeeMore] = useState(false);
  const [dataPrivacyModalVisible, showDataPrivacyModal, hideDataPrivacyModal] =
    useToggle();
  const [
    dataPrivacyReminderVisible,
    showDataPrivacyReminder,
    hideDataPrivacyReminder,
  ] = useToggle();
  const onConfirmDataPrivacy = useCallback(() => {
    setTimeout(showDataPrivacyReminder, 350);
  }, [showDataPrivacyReminder]);

  const consentsAnswers = useEAppStore(state => state.consentsAnswers);
  const updateConsentsDeclarationAnswers = useEAppStore(
    state => state.updateConsentsDeclarationAnswers,
  );
  const updateExistingPolicyData = useEAppStore(
    state => state.updateExistingPolicyData,
  );
  const existingPolicyData = useEAppStore(state => state.existingPolicyData);
  const keyboardShown = useKeyboardShown();

  const isActionDisabled =
    consentsAnswers.declaration.hasExistingInsuranceInForce === null ||
    consentsAnswers.declaration.hasLapsedPolicyToReinstate === null ||
    consentsAnswers.declaration.willPayPremiumsByLoanOrSurrender === null ||
    consentsAnswers.declaration.willReplaceLifeInsurance === null ||
    !consentsAnswers.declaration.confirmCheck;

  const resolver = useEAppValidationResolver(existingPolicySchema);
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid },
    reset,
    setFocus,
  } = useForm<ExistingPoliciesForm>({
    mode: 'onBlur',
    defaultValues: {
      policies: existingPolicyData,
    },
    resolver,
  });

  const scrollViewRef = useRef<KeyboardAwareScrollView>(null);

  const shouldShowExistedPolicy =
    consentsAnswers.declaration.hasExistingInsuranceInForce ||
    consentsAnswers.declaration.hasLapsedPolicyToReinstate ||
    consentsAnswers.declaration.willPayPremiumsByLoanOrSurrender ||
    consentsAnswers.declaration.willReplaceLifeInsurance;

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const disabled = isActionDisabled || (!!shouldShowExistedPolicy && !isValid);
  const incomplete =
    isActionDisabled || (!!shouldShowExistedPolicy && !isValid);
  useIncompleteSync(incomplete, 'consents', undefined, 'declaration');

  return (
    <Box flex={1} backgroundColor={theme.colors.surface}>
      <KeyboardAwareScrollView extraScrollHeight={24} ref={scrollViewRef}>
        <Box px={theme.space[isNarrowScreen ? 3 : 4]} py={theme.space[4]}>
          <H6 fontWeight="bold">Consents</H6>
          <Box
            bgColor={theme.colors.background}
            marginTop={theme.space[4]}
            padding={theme.space[4]}
            borderRadius={theme.sizes[3]}>
            <ConsentQuestionItem
              accessKey="question_one"
              title={t('eApp:consents.question.one')}
              onChange={value =>
                updateConsentsDeclarationAnswers({
                  hasExistingInsuranceInForce: value,
                })
              }
              value={consentsAnswers.declaration.hasExistingInsuranceInForce}
              index={1}
            />
            <ConsentQuestionItem
              accessKey="question_two"
              title={t('eApp:consents.question.two')}
              onChange={value =>
                updateConsentsDeclarationAnswers({
                  hasLapsedPolicyToReinstate: value,
                })
              }
              value={consentsAnswers.declaration.hasLapsedPolicyToReinstate}
              index={2}
            />
            <ConsentQuestionItem
              accessKey="question_three"
              title={t('eApp:consents.question.three')}
              onChange={value =>
                updateConsentsDeclarationAnswers({
                  willReplaceLifeInsurance: value,
                })
              }
              value={consentsAnswers.declaration.willReplaceLifeInsurance}
              index={3}
            />
            <ConsentQuestionItem
              accessKey="question_four"
              title={t('eApp:consents.question.four')}
              onChange={value =>
                updateConsentsDeclarationAnswers({
                  willPayPremiumsByLoanOrSurrender: value,
                })
              }
              value={
                consentsAnswers.declaration.willPayPremiumsByLoanOrSurrender
              }
              index={4}
            />
            <ExistingPolicy
              control={control}
              watch={watch}
              reset={reset}
              setFocus={setFocus}
              enable={!!shouldShowExistedPolicy}
            />
            <Box>
              <Body>
                <Body
                  color={theme.colors.palette.fwdGreyDarkest}
                  fontWeight="bold">
                  {t('eApp:reminders')}
                </Body>
                <Body color={theme.colors.palette.fwdGreyDarkest}>
                  {t(
                    seeMore
                      ? 'eApp:consents.reminder.fullNote'
                      : 'eApp:consents.reminder.shortNote',
                  )}
                </Body>
                <Body
                  suppressHighlighting
                  onPress={() => {
                    setSeeMore(!seeMore);
                    if (!seeMore) {
                      setTimeout(
                        () => scrollViewRef.current?.scrollToEnd(true),
                        200,
                      );
                    }
                  }}
                  fontWeight="bold"
                  color={theme.colors.primary}>
                  {seeMore ? t('eApp:close') : t('eApp:more')}
                </Body>
              </Body>
              <Box mt={theme.space[5]}>
                <Checkbox
                  label={t('eApp:consents.checkbox.confirm')}
                  value={Boolean(consentsAnswers.declaration.confirmCheck)}
                  onChange={checked =>
                    updateConsentsDeclarationAnswers({
                      confirmCheck: checked,
                    })
                  }
                  style={{ alignItems: 'flex-start' }}
                  labelStyle={{
                    flex: 1,
                    marginTop: -theme.space[1],
                  }}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </KeyboardAwareScrollView>
      {!keyboardShown && (
        <FormAction
          primaryDisabled={disabled}
          onPrimaryPress={() => {
            if (shouldShowExistedPolicy) {
              handleSubmit(data => {
                updateExistingPolicyData(
                  data.policies as Array<ExistingPolicyForm>,
                );
                showDataPrivacyModal();
              })();
            } else {
              showDataPrivacyModal();
            }
          }}
        />
      )}
      {dataPrivacyModalVisible && (
        <DataPrivacyModal
          onConfirm={onConfirmDataPrivacy}
          handleClose={hideDataPrivacyModal}
        />
      )}
      {dataPrivacyReminderVisible && (
        <DataPrivacyReminderModal handleClose={hideDataPrivacyReminder} />
      )}
    </Box>
  );
});
