import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { Box, Checkbox, Column, H6, LargeBody, Row } from 'cube-ui-components';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutChangeEvent, useWindowDimensions } from 'react-native';
import { shallow } from 'zustand/shallow';
// @ts-expect-error no export member
import { listenToKeyboardEvents } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaFrame } from 'react-native-safe-area-context';

const KeyboardAwareBottomSheetScrollView = listenToKeyboardEvents(
  BottomSheetScrollView,
);

interface Props {
  onConfirm: () => void;
  handleClose: () => void;
}

export default memo(function DataPrivacyModal({
  onConfirm: onConfirmAction,
  handleClose,
}: Props) {
  const theme = useTheme();

  const { t } = useTranslation(['eApp']);
  const { height } = useSafeAreaFrame();

  const [seeMore, setSeeMore] = useState(false);

  const bottomSheetProps = useBottomSheet();
  const initialSnapPoints = useMemo(
    () => ['CONTENT_HEIGHT', height - theme.space[22]],
    [height, theme.space],
  );

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const onConfirm = useCallback(() => {
    onConfirmAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [onConfirmAction]);
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return <Footer {...props} onConfirm={onConfirm} />;
    },
    [onConfirm],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          index={seeMore ? 1 : 0}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          onChange={snapIndex => {
            if (snapIndex === 0) {
              setSeeMore(false);
            } else {
              setSeeMore(true);
            }
          }}
          footerComponent={renderFooter}>
          <KeyboardAwareBottomSheetScrollView
            style={{
              maxHeight: height - theme.space[22],
              backgroundColor: theme.colors.background,
            }}
            stickyHeaderIndices={[0]}
            onLayout={(e: LayoutChangeEvent) => {
              if (seeMore) return;
              handleContentLayout(e);
            }}>
            <Box
              bgColor={theme.colors.background}
              py={theme.space[4]}
              mx={theme.space[isNarrowScreen ? 3 : 4]}>
              <H6 fontWeight="bold">{t('eApp:consents.dataPrivacy.title')}</H6>
            </Box>
            <Box mx={theme.space[isNarrowScreen ? 3 : 4]}>
              <Column>
                {t(
                  seeMore
                    ? 'eApp:consents.dataPrivacy.description.fullNote'
                    : 'eApp:consents.dataPrivacy.description.shortNote',
                )
                  .split('\n')
                  .map((text, index, arr) => {
                    if (index === 0)
                      return <LargeBody key={text}>{text}</LargeBody>;
                    else
                      return (
                        <Row key={text}>
                          <LargeBody style={{ marginRight: theme.space[2] }}>
                            {index}.
                          </LargeBody>
                          <LargeBody style={{ flex: 1 }}>
                            {text}
                            {index === arr.length - 1 ? (
                              <LargeBody
                                suppressHighlighting
                                onPress={() => {
                                  if (seeMore) {
                                    bottomSheetProps.bottomSheetRef.current?.snapToIndex(
                                      0,
                                    );
                                    setSeeMore(false);
                                  } else {
                                    bottomSheetProps.bottomSheetRef.current?.snapToIndex(
                                      1,
                                    );
                                    setSeeMore(true);
                                  }
                                }}
                                fontWeight="bold"
                                color={theme.colors.primary}>
                                {t(seeMore ? 'eApp:close' : 'eApp:more')}
                              </LargeBody>
                            ) : (
                              ''
                            )}
                          </LargeBody>
                        </Row>
                      );
                  })}
              </Column>
            </Box>
            <Box h={theme.space[seeMore ? 11 : 4]} />
            <BottomSheetFooterSpace />
          </KeyboardAwareBottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const Footer = memo(
  ({
    onConfirm,
    ...props
  }: { onConfirm: () => void } & BottomSheetFooterProps) => {
    const { t } = useTranslation(['eApp']);
    const theme = useTheme();
    const { consentsAnswers, setAcceptDataPrivacy, setAcceptInform } =
      useEAppStore(
        state => ({
          consentsAnswers: state.consentsAnswers,
          setAcceptDataPrivacy: state.setAcceptDataPrivacy,
          setAcceptInform: state.setAcceptInform,
        }),
        shallow,
      );

    const { isNarrowScreen } = useWindowAdaptationHelpers();

    return (
      <BottomSheetFooter {...props}>
        <Box
          borderTop={1}
          borderColor={theme.colors.palette.fwdGrey[100]}
          backgroundColor={theme.colors.background}
          px={theme.space[isNarrowScreen ? 3 : 4]}>
          <Checkbox
            label={t('eApp:consents.dataPrivacy.acceptPolicy')}
            value={Boolean(
              consentsAnswers.dataPrivacyAndConsents.acceptDataPrivacy,
            )}
            onChange={checked => setAcceptDataPrivacy(checked)}
            style={{
              alignItems: 'flex-start',
              marginTop: theme.space[4],
            }}
            labelStyle={{
              marginTop: -theme.space[1],
              flex: 1,
            }}
          />
          <Checkbox
            label={t('eApp:consents.dataPrivacy.acceptInform')}
            value={Boolean(consentsAnswers.dataPrivacyAndConsents.acceptInform)}
            onChange={checked => setAcceptInform(checked)}
            style={{
              alignItems: 'flex-start',
              marginTop: theme.space[4],
            }}
            labelStyle={{
              marginTop: -theme.space[1],
              flex: 1,
            }}
          />
        </Box>
        <FormAction
          hasShadow={false}
          primaryDisabled={
            !consentsAnswers.dataPrivacyAndConsents.acceptDataPrivacy
          }
          onPrimaryPress={onConfirm}
          primaryLabel={t('eApp:consents.dataPrivacy.btn.confirm')}
        />
      </BottomSheetFooter>
    );
  },
);
