import { memo, RefObject, useEffect, useState } from 'react';
import {
  H8,
  Row,
  TextField,
  Box,
  H7,
  RadioButtonGroup,
  RadioButton,
  Checkbox,
  LargeBody,
} from 'cube-ui-components';
import { StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { useTheme } from '@emotion/react';
import { Control, UseFormTrigger, UseFormWatch } from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  creditBankACA,
  prefixCreditCardACA,
  RenewalPaymentSetupForm,
} from 'features/eApp/validations/renewalPaymentSetupValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { CreditCardCompany } from 'types/optionList';
import SearchableDropdown from '../common/SearchableDropdown';

export const AutoCreditArrangement = ({
  watch,
  control,
  trigger,
  scrollViewRef: scrollRef,
}: {
  watch: UseFormWatch<RenewalPaymentSetupForm>;
  control: Control<RenewalPaymentSetupForm>;
  trigger: UseFormTrigger<RenewalPaymentSetupForm>;
  scrollViewRef: RefObject<KeyboardAwareScrollView>;
}) => {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const [showMore, setShowMore] = useState(false);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  useEffect(() => {
    if (watch('creditCardNumber')) {
      trigger('creditCardNumber');
    }
  }, [watch('creditCardCompany'), trigger]);

  return (
    <>
      <Box bgColor={colors.background} padding={16} borderRadius={12} mt={12}>
        <H7 fontWeight="bold">
          {t('eApp:paymentSetup.autoCreditArrangement.creditCardHolderInfo')}
        </H7>
        <Input
          control={control}
          as={TextField}
          name="creditCardHolderName"
          label={t('eApp:paymentSetup.autoCreditArrangement.cardHolderName')}
          style={styles.mt22}
        />
        <Input
          control={control}
          as={SearchableDropdown<CreditCardCompany, string>}
          name="creditCardCompany"
          label={t('eApp:paymentSetup.autoCreditArrangement.cardCompany')}
          disabled={isFetchingOptionList}
          data={optionList?.CREDIT_CARD_COMPANY_LIST.options ?? []}
          getItemValue={item => item.value}
          getItemLabel={item => item.label}
          style={styles.mt22}
          modalTitle={t('eApp:paymentSetup.autoCreditArrangement.cardCompany')}
        />
        <Input
          control={control}
          as={TextField}
          name="creditCardNumber"
          label={t('eApp:paymentSetup.autoCreditArrangement.cardNumber')}
          style={styles.mt22}
          keyboardType={'numeric'}
          left={
            watch('creditCardCompany') === creditBankACA ? (
              <LargeBody>{prefixCreditCardACA}</LargeBody>
            ) : undefined
          }
        />
        <Input
          control={control}
          as={TextField}
          name="creditExpiryDate"
          label={t('eApp:paymentSetup.autoCreditArrangement.expiryDate')}
          style={styles.mt22}
          keyboardType={'numeric'}
          maxLength={5}
        />
        <Box height={20} />
        <H7 fontWeight="bold">
          {t(
            'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.question',
          )}
        </H7>
        <Box height={12} />
        <Input
          control={control}
          as={RadioButtonGroup}
          name="creditIsIssuedBySecBankCorp">
          <Row>
            <RadioButton
              value="yes"
              label={t(
                'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.yes',
              )}
            />
            <Box width={40} />
            <RadioButton
              value="no"
              label={t(
                'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.no',
              )}
            />
          </Row>
        </Input>
      </Box>

      <Box bgColor={colors.background} padding={16} borderRadius={12} mt={12}>
        <Input
          control={control}
          as={Checkbox}
          name="creditAcceptDisclaimer"
          label={t(
            'eApp:paymentSetup.autoCreditArrangement.disclaimer.question',
          )}
          style={{
            alignItems: 'flex-start',
          }}
          labelStyle={{
            marginTop: -space[1],
            flex: 1,
          }}
        />
        <Box height={12} />
        <H8>
          {showMore
            ? t('eApp:paymentSetup.autoCreditArrangement.fullNote')
            : t('eApp:paymentSetup.autoCreditArrangement.shortNote')}
          <H8
            suppressHighlighting
            fontWeight="bold"
            color={colors.primary}
            onPress={() => {
              if (!showMore) {
                setTimeout(() => scrollRef.current?.scrollToEnd(), 200);
              }
              setShowMore(!showMore);
            }}>
            {showMore
              ? t('eApp:paymentSetup.autoCreditArrangement.disclaimer.close')
              : t('eApp:paymentSetup.autoCreditArrangement.disclaimer.more')}
          </H8>
        </H8>
      </Box>
    </>
  );
};

const styles = StyleSheet.create({
  mt22: {
    marginTop: 22,
  },
});
