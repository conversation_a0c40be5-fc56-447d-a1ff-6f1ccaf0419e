import { useTheme } from '@emotion/react';
import Input from 'components/Input/Input';
import {
  Box,
  Dropdown,
  RadioButton,
  RadioButtonGroup,
  Row,
  Typography,
} from 'cube-ui-components';
import { ACRForm } from 'features/eApp/validations/acrValidation';
import React from 'react';
import { Control, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import AdditionalInformationContainer from '../common/AdditionalInformationContainer';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface Props {
  control: Control<ACRForm>;
}

export default function Part1({ control }: Props) {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);

  const watch = useWatch<ACRForm>({ control });

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box
      pb={theme.space[4]}
      pt={theme.space[7]}
      px={theme.space[isNarrowScreen ? 3 : 4]}>
      <Typography.H6 fontWeight="bold">{t('eApp:acr.part1')}</Typography.H6>
      <Box
        backgroundColor={theme.colors.background}
        padding={theme.space[3]}
        mt={theme.space[4]}
        borderRadius={theme.sizes[2]}>
        <Typography.LargeBody fontWeight="bold">
          {t('eApp:acr.part1.relatedToOwner')}
        </Typography.LargeBody>
        <Row mt={theme.space[2]}>
          <Input as={RadioButtonGroup} control={control} name="relatedToOwner">
            <RadioButton
              value="yes"
              label={t('eApp:yes')}
              style={{ flex: 1 }}
            />
            <RadioButton value="no" label={t('eApp:no')} style={{ flex: 1 }} />
          </Input>
        </Row>
        <AdditionalInformationContainer active={watch.relatedToOwner === 'yes'}>
          <Box my={theme.space[4]}>
            <Typography.LargeBody>
              {t('eApp:acr.part1.relationship.question')}
            </Typography.LargeBody>
          </Box>
          <Input
            as={SearchableDropdown<string, string>}
            control={control}
            name="relationship"
            data={['Self', 'Child', 'Spouse', 'Sibling', 'Others']}
            getItemLabel={item => item}
            getItemValue={item => item}
            label={t('eApp:acr.part1.relationship')}
            modalTitle={t('eApp:acr.part1.relationship')}
          />
        </AdditionalInformationContainer>
      </Box>
      <Box
        backgroundColor={theme.colors.background}
        padding={theme.space[3]}
        mt={theme.space[4]}
        borderRadius={theme.sizes[2]}>
        <Typography.LargeBody fontWeight="bold">
          {t('eApp:acr.part1.isRelatedToFWP_FSC')}
        </Typography.LargeBody>
        <Row mt={theme.space[2]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name="isRelatedToFWP_FSC">
            <RadioButton
              value="yes"
              label={t('eApp:yes')}
              style={{ flex: 1 }}
            />
            <RadioButton value="no" label={t('eApp:no')} style={{ flex: 1 }} />
          </Input>
        </Row>
      </Box>
    </Box>
  );
}
