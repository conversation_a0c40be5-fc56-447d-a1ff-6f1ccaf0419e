import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Button, Icon, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import MarkdownText from 'components/MarkdownText';
import DialogPhone from 'components/Dialog.phone';

interface Props {
  visible: boolean;
  handleClose: () => void;
  onAccept: () => void;
  isLoading?: boolean;
}

export default function SkipModal({
  visible,
  handleClose,
  onAccept,
  isLoading,
}: Props) {
  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <DialogPhone visible={visible}>
      <Box alignSelf="center">
        <Icon.InfoCircle size={space[16]} />
      </Box>
      <Box mt={space[4]} gap={space[4]}>
        <Typography.H6 fontWeight="bold" style={{ textAlign: 'center' }}>
          {t('eApp:acr.skipModal.title')}
        </Typography.H6>
        <MarkdownText>{t('eApp:acr.skipModal.description')}</MarkdownText>
      </Box>
      <Row mt={space[4]}>
        <Button
          text={t('eApp:acr.skipModal.stayHere')}
          variant="secondary"
          onPress={handleClose}
          style={{ flex: 1, marginRight: space[1] }}
          disabled={isLoading}
        />
        <Button
          text={t('eApp:ok')}
          loading={isLoading}
          onPress={async () => {
            await onAccept();
            navigation.navigate('Main', {
              screen: 'Home',
            });
          }}
          style={{ flex: 1, marginLeft: space[1] }}
        />
      </Row>
    </DialogPhone>
  );
}
