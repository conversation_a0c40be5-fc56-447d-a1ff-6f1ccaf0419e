import { memo, useEffect, useState } from 'react';
import { useWindowDimensions, ViewProps, Platform } from 'react-native';
import styled from '@emotion/native';

const OtherContainer = styled.View(() => ({
  flex: 1,
}));

const PhoneContainer = styled.View<{ size: { width: number; height: number } }>(
  ({ size: { width, height } }) => ({
    width: height,
    height: width,
    transform: [
      { rotateZ: '-90deg' },
      { translateX: -(height - width) / 2 },
      { translateY: -(height - width) / 2 },
    ],
  }),
);
export const PhoneRotationContainer = memo(function PhoneRotationContainer({
  children,
  rotationDisabled,
  ...props
}: ViewProps & { rotationDisabled?: boolean }) {
  const { width, height } = useWindowDimensions();

  const Container = !rotationDisabled ? PhoneContainer : OtherContainer;

  return (
    <Container size={{ width, height }} {...props}>
      {children}
    </Container>
  );
});
export default PhoneRotationContainer;
