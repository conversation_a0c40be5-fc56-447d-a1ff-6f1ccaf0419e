import styled from '@emotion/native';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { Button, Column, Icon, Switch, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';
import useToggle from 'hooks/useToggle';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native';
import DrawPad, { isDot, isLine } from './DrawPad';
import SignatureModal, { ITerm } from './SignatureModal';
import Dots from './Dots';
import { useTranslation } from 'react-i18next';
import {
  enhancedRemoteSellingEnabled,
  fullRemoteSellingEnabled,
} from 'utils/context';
import { CHANNELS } from 'types/channel';

const Container = styled(SafeAreaView)(({ theme }) => ({
  flexDirection: 'row',
  flex: 1,
  backgroundColor: theme.colors.background,
  paddingLeft: theme.space[8],
}));

const Header = styled.View(({ theme }) => ({
  height: 46,
  flexDirection: 'row',
  alignItems: 'center',
  marginLeft: -theme.space[1],
}));

const CloseButton = styled.TouchableOpacity(({ theme }) => ({
  marginRight: theme.space[4],
  padding: 3,
}));

const HeaderTitle = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onBackground,
  flex: 1,
}));

const NoticeContainer = styled.View(({ theme }) => ({
  marginTop: theme.space[1],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
}));

const Notice = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  flexShrink: 1,
}));

const DetailButton = styled.TouchableOpacity(({ theme }) => ({
  paddingHorizontal: theme.space[1],
}));

const DetailText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.primary,
}));

const Footer = styled.View(({ theme }) => ({
  marginTop: theme.space[2],
  marginBottom: theme.space[4],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
  height: 34,
}));

const RemoteText = styled(Typography.Body)<{ disabled: boolean }>(
  ({ theme, disabled }) => ({
    color: theme.colors.secondaryVariant,
    opacity: disabled ? 0.5 : 1,
  }),
);

const SwitchContainer = styled.View(({ theme }) => ({
  marginLeft: theme.space[2] + 2,
  flex: 1,
}));

const DotContainer = styled.View(({ theme }) => ({
  marginHorizontal: theme.space[3],
  justifyContent: 'center',
}));

interface Props {
  type:
    | 'policyOwner'
    | 'insured'
    | 'agent'
    | 'authorizedRepresentative'
    | 'solicitingOfficer'
    | 'brokerRepresentativeOfficer';
  onNext: (signedTime: number, imageUri: string) => void;
  name: string;
  terms: Array<ITerm>;
  placeOfSigning: string;
  onChangePlaceOfSigning: (value: string) => void;
  onRemoteSignaturePress?: () => void;
  activeIndex: number;
  strokes: string[];
  onChangeStokes: (strokes: string[]) => void;
  onClearPress: () => void;
  initialSignedTime?: number;
  isRemoteSelling?: boolean;
  landscape?: boolean;
  remoteSignatureDisabled?: boolean;
  isRemoteSellingVisible?: boolean;
  onClose?: () => void;
  totalSteps?: number;
  isLoading?: boolean;
  channel?: string;
  readOnly?: boolean;
}

export const Signature = memo(function Signature({
  type,
  onNext,
  name,
  terms,
  placeOfSigning,
  onChangePlaceOfSigning,
  onRemoteSignaturePress,
  activeIndex,
  strokes,
  onChangeStokes,
  onClearPress,
  initialSignedTime,
  isRemoteSelling = false,
  landscape,
  remoteSignatureDisabled = false,
  isRemoteSellingVisible = false,
  onClose,
  totalSteps,
  isLoading,
  channel,
  readOnly,
}: Props) {
  const [viewed, setViewed] = useState(false);
  const drawPadRef = useRef<
    { capture: () => Promise<string | undefined> } | undefined
  >();
  const { t } = useTranslation(['eApp']);
  const disabled = useMemo(() => {
    if (readOnly && viewed) {
      return false;
    }
    //Disabled if one stroke only
    if (strokes.length <= 1) {
      return true;
    }
    //Disabled if dots only
    const dots = strokes.filter(stroke => isDot(stroke));
    if (dots.length === strokes.length) {
      return true;
    }

    //Disabled if lines only
    const lines = strokes.filter(stroke => isLine(stroke));
    if (lines.length === strokes.length) {
      return true;
    }
    return false;
  }, [strokes, readOnly, viewed]);

  const signedTimeCount = useRef(0);

  const signedTime = useMemo(() => {
    signedTimeCount.current += 1;
    if (disabled) {
      return undefined;
    } else {
      if (initialSignedTime && signedTimeCount.current === 1) {
        return initialSignedTime;
      }
      return new Date().getTime();
    }
  }, [disabled, initialSignedTime, strokes]);

  const title = `${name} (${t(`eApp:signature.${type}`).toLowerCase()})${t(
    'eApp:signature.sSignature',
  )}`;

  const { colors } = useTheme();
  const { goBack } = useNavigation();
  const [visible, show, hide] = useToggle();
  const modalTitle = `${t('eApp:signature.declarationMadeBy')} ${t(
    `eApp:signature.${
      type === 'agent'
        ? channel === CHANNELS.BANCA || channel === CHANNELS.AFFINITY
          ? 'financialSolutionsConsultant'
          : 'financialWealthPlanner'
        : type
    }`,
  ).toLowerCase()}`;

  const onContinue = useCallback(async () => {
    // https://github.com/gre/react-native-view-shot/issues/478
    const uri = (await drawPadRef.current?.capture())?.replace(
      /(\r\n|\n|\r)/gm,
      '',
    );

    if (readOnly) {
      // Unused variables
      onNext(0, '');
    }

    if (signedTime && uri) {
      onNext(signedTime, uri);
    }
  }, [onNext, signedTime, readOnly]);

  const onCloseFn = useCallback(() => {
    if (onClose) {
      onClose();
    } else {
      goBack();
    }
  }, [goBack, onClose]);

  const onConfirm = useCallback(() => {
    hide();
    setViewed(true);
  }, [hide, setViewed]);

  return (
    <>
      <Container
        style={{ flex: 1, backgroundColor: colors.background }}
        edges={['right']}>
        <Column flex={1}>
          <Header>
            <CloseButton onPress={onCloseFn}>
              <Icon.Close size={30} fill={colors.onBackground} />
            </CloseButton>
            <HeaderTitle numberOfLines={1} fontWeight="bold">
              {title}
            </HeaderTitle>
          </Header>
          <NoticeContainer>
            <Notice>{t('eApp:signature.importantNoticeSignature')}</Notice>
            <DetailButton onPress={show}>
              <DetailText fontWeight="bold">
                {t('eApp:signature.viewDetails')}
              </DetailText>
            </DetailButton>
          </NoticeContainer>
          <DrawPad
            readOnly={readOnly}
            disabled={!viewed}
            ref={drawPadRef}
            strokes={strokes}
            onChange={onChangeStokes}
            name={name}
            placeOfSigning={placeOfSigning}
            onChangePlaceOfSigning={onChangePlaceOfSigning}
            onClearPress={onClearPress}
            signedTime={signedTime}
            landscape={landscape}
          />
          <Footer>
            {!fullRemoteSellingEnabled &&
              !enhancedRemoteSellingEnabled &&
              isRemoteSellingVisible && (
                <RemoteText disabled={remoteSignatureDisabled}>
                  {t('eApp:signature.remoteSignature')}
                </RemoteText>
              )}
            <SwitchContainer>
              {!fullRemoteSellingEnabled &&
                !enhancedRemoteSellingEnabled &&
                isRemoteSellingVisible && (
                  <Switch
                    style={{ alignSelf: 'flex-start' }}
                    disabled={remoteSignatureDisabled}
                    label={
                      isRemoteSelling
                        ? t('eApp:signature.on').toUpperCase()
                        : t('eApp:signature.off').toUpperCase()
                    }
                    checked={!!isRemoteSelling}
                    onChange={onRemoteSignaturePress}
                    labelStyle={[
                      styles.label,
                      {
                        color: isRemoteSelling
                          ? colors.primary
                          : colors.onBackground,
                      },
                    ]}
                  />
                )}
            </SwitchContainer>
            <Button
              disabled={disabled}
              text={t('eApp:next')}
              variant="primary"
              onPress={onContinue}
              loading={isLoading}
              compact
              style={styles.button}
              contentStyle={styles.buttonContent}
            />
          </Footer>
        </Column>
        <DotContainer>
          <Dots activeIndex={activeIndex} total={totalSteps} />
        </DotContainer>
      </Container>
      <SignatureModal
        visible={visible}
        onClose={hide}
        title={modalTitle}
        name={name}
        terms={terms}
        onConfirm={onConfirm}
      />
    </>
  );
});

export default Signature;
const styles = StyleSheet.create({
  label: {
    fontFamily: 'FWDCircularTT-Bold',
    fontSize: 14,
    lineHeight: 14 * 1.25,
    marginLeft: 6,
  },
  button: {
    height: '100%',
    minWidth: 180,
  },
  buttonContent: {
    height: '100%',
    paddingVertical: 0,
  },
});
