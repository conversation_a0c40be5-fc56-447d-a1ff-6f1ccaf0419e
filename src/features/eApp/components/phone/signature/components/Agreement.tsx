import styled from '@emotion/native';
import { memo, useCallback } from 'react';
import { Button, Column, Icon, Switch, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';
import { ScrollView, StyleSheet, View } from 'react-native';
import { ITerm } from './SignatureModal';
import Dots from './Dots';
import { useTranslation } from 'react-i18next';

import { SafeAreaView } from 'react-native-safe-area-context';
import {
  enhancedRemoteSellingEnabled,
  fullRemoteSellingEnabled,
} from 'utils/context';

const Container = styled(SafeAreaView)(({ theme }) => ({
  flexDirection: 'row',
  flex: 1,
  backgroundColor: theme.colors.background,
  paddingLeft: theme.space[8],
}));

const Header = styled.View(({ theme }) => ({
  height: 46,
  flexDirection: 'row',
  alignItems: 'center',
  marginLeft: -theme.space[1],
}));

const CloseButton = styled.TouchableOpacity(({ theme }) => ({
  marginRight: theme.space[4],
  padding: 3,
}));

const HeaderTitle = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onBackground,
  flex: 1,
}));

const NoticeContainer = styled.View(({ theme }) => ({
  marginTop: theme.space[1],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
}));

const Notice = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  flexShrink: 1,
}));

const DetailButton = styled.TouchableOpacity(({ theme }) => ({
  paddingHorizontal: theme.space[1],
}));

const DetailText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.primary,
}));

const Footer = styled.View(({ theme }) => ({
  marginTop: theme.space[2],
  marginBottom: theme.space[4],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
  height: 34,
}));

const RemoteText = styled(Typography.Body)<{ disabled: boolean }>(
  ({ theme, disabled }) => ({
    color: theme.colors.secondaryVariant,
    opacity: disabled ? 0.5 : 1,
  }),
);

const SwitchContainer = styled.View(({ theme }) => ({
  marginLeft: theme.space[2] + 2,
  flex: 1,
}));

const DotContainer = styled.View(({ theme }) => ({
  marginHorizontal: theme.space[3],
  justifyContent: 'center',
}));

const AgreeText = styled(Typography.H7)(({ theme }) => ({
  color: '#333333',
  marginBottom: theme.space[2],
  marginTop: theme.space[4],
}));

interface Props {
  type: 'policyOwner' | 'insured' | 'agent' | 'authorizedRepresentative';
  onNext: (signedTime: number) => void;
  name: string;
  terms: Array<ITerm>;
  onRemoteSignaturePress?: () => void;
  activeIndex: number;
  switchDisabled?: boolean;
  onClose?: () => void;
  totalSteps?: number;
  isLoading?: boolean;
}

export const Agreement = memo(function Agreement({
  type,
  onNext,
  name,
  terms,
  onRemoteSignaturePress,
  activeIndex,
  switchDisabled = false,
  onClose,
  totalSteps,
  isLoading,
}: Props) {
  const { t } = useTranslation(['eApp']);

  const title = `${name} (${t(`eApp:signature.${type}`).toLowerCase()})${t(
    'eApp:signature.sAgreement',
  )}`;

  const { colors, space, borderRadius } = useTheme();
  const { goBack } = useNavigation();

  const onContinue = useCallback(() => {
    onNext(new Date().getTime());
  }, [onNext]);

  const onCloseFn = useCallback(() => {
    if (onClose) {
      onClose();
    } else {
      goBack();
    }
  }, [onClose, goBack]);

  return (
    <>
      <Container
        style={{ flex: 1, backgroundColor: colors.background }}
        edges={['right']}>
        <Column flex={1}>
          <Header>
            <CloseButton onPress={onCloseFn}>
              <Icon.Close size={30} fill={colors.onBackground} />
            </CloseButton>
            <HeaderTitle numberOfLines={1} fontWeight="bold">
              {title}
            </HeaderTitle>
          </Header>
          <NoticeContainer>
            <Notice>{t('eApp:signature.importantNotice')}</Notice>
          </NoticeContainer>
          <AgreeText fontWeight="bold">
            {t('eApp:signature.agreeTerm', { name })}
          </AgreeText>
          <ScrollView
            style={[styles.sv, { borderRadius: borderRadius.small }]}
            contentContainerStyle={{
              paddingHorizontal: space[4],
              flexGrow: 1,
              paddingTop: 11,
            }}>
            {terms.map((term, index) => (
              <Term key={index} term={term} index={index} />
            ))}
          </ScrollView>
          <Footer>
            {!fullRemoteSellingEnabled && !enhancedRemoteSellingEnabled && (
              <>
                <RemoteText disabled={switchDisabled}>
                  {t('eApp:signature.remoteSignature')}
                </RemoteText>
                <SwitchContainer>
                  <Switch
                    style={{ alignSelf: 'flex-start' }}
                    disabled={switchDisabled}
                    label={t('eApp:signature.on').toUpperCase()}
                    checked={true}
                    onChange={onRemoteSignaturePress}
                    labelStyle={[styles.label, { color: colors.primary }]}
                  />
                </SwitchContainer>
              </>
            )}
            <Button
              disabled={false}
              text={t('eApp:signature.agreeAndContinue')}
              variant="primary"
              onPress={onContinue}
              size={'small'}
              style={styles.button}
              contentStyle={styles.buttonContent}
              loading={isLoading}
            />
          </Footer>
        </Column>
        <DotContainer>
          <Dots activeIndex={activeIndex} total={totalSteps} />
        </DotContainer>
      </Container>
    </>
  );
});

const TermContainer = styled.View<{ index: number }>(({ theme, index }) => ({
  flexDirection: 'row',
  marginTop: index > 0 ? theme.space[5] : 0,
}));

const SubtermContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  marginTop: theme.space[2],
}));

const TermNum = styled(Typography.LargeBody)(({ theme }) => ({
  color: '#333333',
  marginRight: theme.space[2],
}));

const TermText = styled(Typography.LargeBody)(() => ({
  color: '#333333',
  flex: 1,
}));

const Term = memo(({ index, term }: { index: number; term: ITerm }) => {
  if (typeof term === 'string') {
    return (
      <TermContainer index={index}>
        <TermNum>{`${index + 1}.`}</TermNum>
        <TermText>{term}</TermText>
      </TermContainer>
    );
  }
  return (
    <>
      <TermContainer index={index}>
        <TermNum>{`${index + 1}.`}</TermNum>
        <View style={{ flex: 1 }}>
          <TermText>{term.text}</TermText>
          {term.terms.map((term, index) => (
            <SubtermContainer key={index}>
              <TermNum>{`${String.fromCharCode(97 + index)}.`}</TermNum>
              <TermText>{term}</TermText>
            </SubtermContainer>
          ))}
        </View>
      </TermContainer>
    </>
  );
});

export default Agreement;
const styles = StyleSheet.create({
  label: {
    fontFamily: 'FWDCircularTT-Bold',
    fontSize: 14,
    lineHeight: 14 * 1.25,
    marginLeft: 6,
  },
  button: {
    height: '100%',
  },
  buttonContent: {
    height: '100%',
    paddingVertical: 0,
  },
  sv: {
    flex: 1,
    backgroundColor: '#F8F9F9',
  },
});
