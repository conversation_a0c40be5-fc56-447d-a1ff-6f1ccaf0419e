import styled from '@emotion/native';
import { memo, useMemo } from 'react';

const Container = styled.View(() => ({
  width: 14,
  alignItems: 'center',
}));

interface Props {
  total?: number;
  activeIndex: number;
}

export const Dots = memo(function Dots({ activeIndex, total = 3 }: Props) {
  const steps = useMemo(() => new Array(total).fill(''), [total]);
  return (
    <Container>
      {steps.map((_, index) => (
        <Dot key={index} activeIndex={activeIndex} index={index} />
      ))}
    </Container>
  );
});
export default Dots;

const Dot = memo(
  ({ index, activeIndex }: { index: number; activeIndex: number }) => (
    <DotContainer>
      {index === activeIndex ? (
        <ActiveDot1>
          <ActiveDot2 />
        </ActiveDot1>
      ) : (
        <InactiveDot />
      )}
    </DotContainer>
  ),
);

const DotContainer = styled.View(() => ({
  width: 12,
  height: 12,
  marginVertical: 4,
  justifyContent: 'center',
  alignItems: 'center',
}));

const InactiveDot = styled.View(({ theme }) => ({
  width: 10,
  height: 10,
  borderRadius: 5,
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGreyDark,
}));

const ActiveDot1 = styled.View(({ theme }) => ({
  width: 12,
  height: 12,
  borderRadius: 6,
  borderWidth: 2,
  borderColor: theme.colors.primaryVariant2,
}));

const ActiveDot2 = styled.View(({ theme }) => ({
  width: 8,
  height: 8,
  borderRadius: 4,
  borderWidth: 2,
  borderColor: theme.colors.primary,
}));
