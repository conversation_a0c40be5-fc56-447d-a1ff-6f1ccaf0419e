import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Signature from '../components/Signature';
import { F2FSigning, useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { RootStackParamList } from 'types';
import useToggle from 'hooks/useToggle';
import SummaryModal from '../modals/SummaryModal';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from 'utils';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import useEndLandscape from 'features/eApp/hooks/useEndLandscape';
import { StatusBar } from 'expo-status-bar';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useUpdateSignature } from 'features/eApp/hooks/useUpdateSignature';
import useBoundStore from 'hooks/useBoundStore';
import { useGetQuotation } from 'hooks/useGetQuotation';
import GATracking from 'utils/helper/gaTracking';
import { AGENT_BRANCHES } from 'types/channel';
import { useGetInitialPremium } from 'features/eAppV2/common/hooks/useGetInitialPremium';

export const terms = [
  'I have fully answered any questions the Proposed Owner and/or the Proposed Insured asked in a language which they understand.',
  'I have acted under the direction and authority of the Proposed Owner',
  'The Proposed Owner and/or Proposed Insured (where appropriate) have signed this Application Form in our presence.',
  'I affirm the identity of the Proposed Owner and/or Proposed Insured; and',
  'I have seen and verified the original copy of the identification documents submitted in connection with this application for insurance.',
  'I have verified the contact details of the Proposed Owner/Insured and confirm they are true and accurate as of date. I undertake to immediately inform FWD of any changes to the Proposed Owner/Insured’s contact details.',
];

export const AgentSignature = memo(function AgentSignature() {
  const { updateSignature, isLoading: isUpdatingSignature } =
    useUpdateSignature();
  const signatures = useEAppStore(state => state.signatures);
  const renewalPaymentMethod = useEAppStore(
    state => state.renewalPaymentSetup.paymentMethod,
  );
  const initialStrokes = useMemo(() => {
    if (signatures.agent?.signature) {
      return JSON.parse(signatures.agent.signature);
    }
    return [];
  }, []);

  const initialPlace = useMemo(() => {
    return signatures.agent?.place || 'Philippines';
  }, []);

  const initialSignedTime = useMemo(() => {
    return signatures.agent?.signedTime;
  }, []);

  const [strokes, setStokes] = useState<string[]>(initialStrokes);
  const [placeOfSigning, setPlaceOfSigning] = useState(initialPlace);
  const [summaryVisible, showSummary, hideSummary] = useToggle();
  const signedTimeRef = useRef<number | undefined>(undefined);
  const imageURIRef = useRef<string | undefined>(undefined);

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const { params } =
    useRoute<RouteProp<RootStackParamList, 'AgentSignature'>>();
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const { data: agentProfile } = useGetAgentProfile();
  const type:
    | 'policyOwner'
    | 'insured'
    | 'agent'
    | 'brokerRepresentativeOfficer' =
    agentProfile?.branch.name === AGENT_BRANCHES.CODE_XL
      ? 'brokerRepresentativeOfficer'
      : 'agent';
  const name = agentProfile?.person?.fullName ?? 'N/A';

  const activeIndex = typeof params?.index === 'number' ? params?.index : 2;

  const updateF2FSignature = useEAppStore(state => state.updateF2FSignature);
  const activeQuotationId = useEAppStore(state => state.activeQuotationId);

  const save = useCallback(
    async (signedTime: number, imageUri: string) => {
      const res = await updateSignature({
        signedTime,
        imageBase64: imageUri,
        party: 'agent',
      });
      const data: F2FSigning = {
        place: placeOfSigning,
        signature: JSON.stringify(strokes),
        signedTime,
        type: 'F2F',
        imageUri: res.filePath,
      };
      updateF2FSignature(data, 'agent');
    },
    [updateF2FSignature, placeOfSigning, strokes, updateSignature],
  );

  const endLandscape = useEndLandscape();
  const onConfirm = useCallback(async () => {
    if (!signedTimeRef.current || !imageURIRef.current) {
      return;
    }
    await save(signedTimeRef.current, imageURIRef.current);
    hideSummary();
    await endLandscape();
    nextGroup(true);
    navigate('EApp');
  }, [save, nextGroup, endLandscape]);

  const onNext = useCallback(
    (signedTime: number, imageUri: string) => {
      signedTimeRef.current = signedTime;
      imageURIRef.current = imageUri;
      showSummary();
    },
    [showSummary],
  );

  const onClearPress = useCallback(() => {
    updateF2FSignature(undefined, 'agent');
  }, [updateF2FSignature]);

  const { t } = useTranslation(['eApp']);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: quotation } = useGetQuotation(
    caseId || '',
    activeQuotationId || '',
    Boolean(caseId && activeQuotationId),
  );
  const { data: initialPremiumData } = useGetInitialPremium();
  const content = useMemo(() => {
    if (quotation?.plans[0].premiumType === 'SP') {
      return t('eApp:signature.singlePayPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
      });
    } else if (quotation?.plans[0].paymentMode === 'M') {
      return t('eApp:signature.monthlyADAPlanSum', {
        initialAmount: `${formatCurrency(initialPremiumData?.initialPremium)} ${
          initialPremiumData?.currency || 'PHP'
        }`,
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        term: quotation?.plans[0].premiumTerm,
      });
    } else {
      return t('eApp:signature.regularPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        mode: t(
          `eApp:paymentMode.${
            quotation?.plans[0].paymentMode as 'A' | 'S' | 'M' | 'Q'
          }`,
        ).toLowerCase(),
        term: quotation?.plans[0].premiumTerm,
      });
    }
  }, [t, quotation, initialPremiumData]);

  useEffect(() => {
    GATracking.trackScreen(GATracking.SUB_SCREENS.E_APP_AGENT_SIGNATURE);
  }, []);

  return (
    <>
      <StatusBar hidden />
      <Signature
        type={type}
        onNext={onNext}
        name={name}
        terms={terms}
        placeOfSigning={placeOfSigning}
        onChangePlaceOfSigning={setPlaceOfSigning}
        activeIndex={activeIndex}
        strokes={strokes}
        onChangeStokes={setStokes}
        onClearPress={onClearPress}
        initialSignedTime={initialSignedTime}
        totalSteps={params?.totalSteps}
        channel={agentProfile?.channel}
      />
      <SummaryModal
        visible={summaryVisible}
        onClose={hideSummary}
        onConfirm={onConfirm}
        content={content}
        isLoading={isUpdatingSignature}
      />
    </>
  );
});
export default AgentSignature;
