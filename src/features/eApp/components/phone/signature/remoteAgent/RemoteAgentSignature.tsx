import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Signature from '../components/Signature';
import { F2FSigning, useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { RootStackParamList } from 'types';

import { terms } from '../agent/AgentSignature';
import PendingModal from '../modals/PendingModal';
import useToggle from 'hooks/useToggle';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import CustomerInfoModal, {
  CustomerInfoFormValues,
} from '../modals/CustomerInfoModal';
import SummaryModal from '../modals/SummaryModal';
import { formatCurrency } from 'utils';
import { useTranslation } from 'react-i18next';
import useEndLandscape from 'features/eApp/hooks/useEndLandscape';
import { StatusBar } from 'expo-status-bar';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useUpdateSignature } from 'features/eApp/hooks/useUpdateSignature';
import { useGetQuotation } from 'hooks/useGetQuotation';
import useBoundStore from 'hooks/useBoundStore';
import GATracking from 'utils/helper/gaTracking';
import { useStartRemoteSelling } from 'hooks/useStartRemoteSelling';
import {
  enhancedRemoteSellingEnabled,
  fullRemoteSellingEnabled,
} from 'utils/context';
import { AGENT_BRANCHES } from 'types/channel';
import VerifyInfoModal from 'features/eApp/components/phone/signature/modals/VerifyInfoModal';
import { useSendRSEmailConfirm } from 'features/eAppV2/ph/hooks/useSendRSEmailConfirm';
import { addErrorBottomToast, Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useGetInitialPremium } from 'features/eAppV2/common/hooks/useGetInitialPremium';

export const RemoteAgentSignature = memo(function RemoteAgentSignature() {
  const { colors } = useTheme();
  const { updateSignature, isLoading: isUpdatingSignature } =
    useUpdateSignature();
  const signatures = useEAppStore(state => state.signatures);
  const exclusiveLetter = useEAppStore(state => state.exclusiveLetter);
  const renewalPaymentMethod = useEAppStore(
    state => state.renewalPaymentSetup.paymentMethod,
  );
  const initialStrokes = useMemo(() => {
    if (signatures.agent?.signature) {
      return JSON.parse(signatures.agent.signature);
    }
    return [];
  }, []);

  const initialPlace = useMemo(() => {
    return signatures.agent?.place || 'Philippines';
  }, []);

  const initialSignedTime = useMemo(() => {
    return signatures.agent?.signedTime;
  }, []);

  const [strokes, setStokes] = useState<string[]>(initialStrokes);
  const [placeOfSigning, setPlaceOfSigning] = useState(initialPlace);
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const { params } =
    useRoute<RouteProp<RootStackParamList, 'RemoteAgentSignature'>>();
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const caseId = useBoundStore(state => state.case.caseId);

  const { data: agentProfile } = useGetAgentProfile();
  const type:
    | 'policyOwner'
    | 'insured'
    | 'agent'
    | 'brokerRepresentativeOfficer' =
    agentProfile?.branch.name === AGENT_BRANCHES.CODE_XL
      ? 'brokerRepresentativeOfficer'
      : 'agent';
  const name = agentProfile?.person?.fullName ?? 'N/A';

  const activeIndex = typeof params?.index === 'number' ? params?.index : 2;

  const updateF2FSignature = useEAppStore(state => state.updateF2FSignature);
  const activeQuotationId = useEAppStore(state => state.activeQuotationId);
  const { mutateAsync: startRemoteSelling } = useStartRemoteSelling();
  const { mutate: sendRSEmailConfirm, isLoading: sendingEmailConfirm } =
    useSendRSEmailConfirm();

  const [customerInfo, setCustomerInfo] = useState<
    CustomerInfoFormValues | undefined
  >(undefined);

  const save = useCallback(
    async (signedTime: number, imageUri: string) => {
      const res = await updateSignature({
        signedTime,
        imageBase64: imageUri,
        party: 'agent',
      });
      const data: F2FSigning = {
        place: placeOfSigning,
        signature: JSON.stringify(strokes),
        signedTime,
        type: 'F2F',
        imageUri: res.filePath,
      };
      updateF2FSignature(data, 'agent');
    },
    [updateF2FSignature, placeOfSigning, strokes, updateSignature],
  );

  const [customerInfoVisible, showCustomerInfo, hideCustomerInfo] = useToggle();
  const [pendingVisible, showPending, hidePending] = useToggle();
  const [summaryVisible, showSummary, hideSummary] = useToggle();

  const signedTimeRef = useRef<number | undefined>(undefined);
  const imageURIRef = useRef<string | undefined>(undefined);

  // const onNext = useCallback(
  //   (signedTime: number, imageUri: string) => {
  //     save(signedTime, imageUri);
  //     navigate('EApp');
  //     nextGroup();
  //   },
  //   [save, navigate, nextGroup],
  // );
  const onNext = useCallback(
    (signedTime: number, imageUri: string) => {
      signedTimeRef.current = signedTime;
      imageURIRef.current = imageUri;
      showSummary();
    },
    [showSummary],
  );

  const onClearPress = useCallback(() => {
    updateF2FSignature(undefined, 'agent');
  }, [updateF2FSignature]);

  const onCustomerInfoConfirm = useCallback(
    (values: CustomerInfoFormValues) => {
      setCustomerInfo(values);
      hideCustomerInfo();
      showPending();
    },
    [],
  );

  const endLandscape = useEndLandscape();

  const onConfirm = useCallback(async () => {
    if (!signedTimeRef.current || !imageURIRef.current) {
      return;
    }
    await save(signedTimeRef.current, imageURIRef.current);
    if (fullRemoteSellingEnabled && caseId) {
      await startRemoteSelling({ caseId });
    }
    if (enhancedRemoteSellingEnabled && caseId) {
      sendRSEmailConfirm(
        { caseId, exclusiveLetter },
        {
          onSuccess: async () => {
            hideSummary();
            await endLandscape();
            nextGroup(true);
            navigate('EApp');
          },
          onError: () => {
            addErrorBottomToast([
              {
                message:
                  'An error occurred. Please try again later or contact support.',
                IconLeft: <Icon.Warning fill={colors.error} />,
              },
            ]);
          },
        },
      );
    }
    if (!enhancedRemoteSellingEnabled) {
      hideSummary();
      await endLandscape();
      nextGroup(true);
      navigate('EApp');
    }
  }, [
    save,
    caseId,
    hideSummary,
    endLandscape,
    nextGroup,
    navigate,
    startRemoteSelling,
    sendRSEmailConfirm,
    exclusiveLetter,
  ]);

  const onBackHome = useCallback(async () => {
    if (!signedTimeRef.current || !imageURIRef.current) {
      return;
    }
    save(signedTimeRef.current, imageURIRef.current);
    await endLandscape();
    setTimeout(() => {
      navigate('Main', {
        screen: 'Home',
      });
    }, 100);
  }, [useEndLandscape]);

  const { t } = useTranslation(['eApp']);

  const { data: quotation } = useGetQuotation(
    caseId || '',
    activeQuotationId || '',
    Boolean(caseId && activeQuotationId),
  );
  const { data: initialPremiumData } = useGetInitialPremium();

  const content = useMemo(() => {
    if (quotation?.plans[0].premiumType === 'SP') {
      return t('eApp:signature.singlePayPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
      });
    } else if (quotation?.plans[0].paymentMode === 'M') {
      return t('eApp:signature.monthlyADAPlanSum', {
        initialAmount: `${formatCurrency(initialPremiumData?.initialPremium)} ${
          initialPremiumData?.currency || 'PHP'
        }`,
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        term: quotation?.plans[0].premiumTerm,
      });
    } else {
      return t('eApp:signature.regularPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        mode: t(
          `eApp:paymentMode.${
            quotation?.plans[0].paymentMode as 'A' | 'S' | 'M' | 'Q'
          }`,
        ).toLowerCase(),
        term: quotation?.plans[0].premiumTerm,
      });
    }
  }, [t, quotation, initialPremiumData]);

  useEffect(() => {
    GATracking.trackScreen(GATracking.SUB_SCREENS.E_APP_AGENT_SIGNATURE_REMOTE);
  }, []);

  return (
    <>
      <StatusBar hidden />
      <Signature
        type={type}
        onNext={onNext}
        name={name}
        terms={terms}
        placeOfSigning={placeOfSigning}
        onChangePlaceOfSigning={setPlaceOfSigning}
        activeIndex={activeIndex}
        strokes={strokes}
        onChangeStokes={setStokes}
        onClearPress={onClearPress}
        initialSignedTime={initialSignedTime}
        isRemoteSelling={true}
        totalSteps={params?.totalSteps}
        channel={agentProfile?.channel}
      />
      <CustomerInfoModal
        visible={customerInfoVisible}
        onConfirm={onCustomerInfoConfirm}
        onClose={hideCustomerInfo}
      />
      <PendingModal
        visible={pendingVisible}
        onConfirm={() => null}
        onBackHome={onBackHome}
        customer={customerInfo}
      />
      {enhancedRemoteSellingEnabled ? (
        <VerifyInfoModal
          visible={summaryVisible}
          onClose={hideSummary}
          onConfirm={onConfirm}
          isLoading={sendingEmailConfirm}
        />
      ) : (
        <SummaryModal
          visible={summaryVisible}
          onClose={hideSummary}
          onConfirm={onConfirm}
          content={content}
          isLoading={isUpdatingSignature}
        />
      )}
    </>
  );
});
export default RemoteAgentSignature;
