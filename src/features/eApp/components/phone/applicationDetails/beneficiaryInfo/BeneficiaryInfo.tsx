import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import React, { useCallback, useState } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import BeneficiaryInfoTooltipModal from './modals/BeneficiaryInfoTooltipModal';
import FormAction from '../../common/FormAction';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import useBenefitAllocationBeneficiaries from 'features/eApp/hooks/useBenefitAllocationBeneficiaries';
import {
  useAbleToGoNextHQ,
  useButtonActionStatus,
} from 'features/eApp/hooks/useButtonActionStatus';
import { BeneficiaryHeader } from './sections/BeneficiaryHeader';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { useSaveParty } from 'features/eApp/hooks/useParty';
import { PartyRole } from 'types/party';
import { toParty } from 'features/eApp/utils/caseUtils';
import { BeneficiaryBody } from './sections/BeneficiaryBody';
import BenefitPercentage from './beneficiaryAllocation/BenefitPercentage';
import OnSaveHandler from '../OnSaveHandler';
import { useIncompleteSync } from 'features/eApp/hooks/useIncompleteSync';
import { useAutoAssignPB } from 'features/eAppV2/ph/hooks/useAutoAssignPB';
import { useSaveOcrImage } from 'features/eApp/hooks/useSaveOcrImage';

function BeneficiaryInfo({
  beneficiaryType,
}: {
  beneficiaryType: 'PB' | 'SB';
}) {
  const { space } = useTheme();
  const [tooltipVisible, setTooltipVisible] = useState(false);

  const { next, multipleNext } = useEAppProgressBarStore(
    state => ({
      next: state.next,
      multipleNext: state.multipleNext,
      groups: state.groups,
    }),
    shallow,
  );
  const isPrimaryBeneficiary = beneficiaryType === 'PB';
  const autoAssignPB = useAutoAssignPB(isPrimaryBeneficiary);

  const isActionDisabled = useButtonActionStatus({
    beneficiaryType,
    autoAssignPB,
  });
  const allBeneficiaries = useEAppStore(
    state => state.beneficiariesPersonalInfo,
  );
  const {
    beneficiariesPersonalInfo,
    updateBeneficiaryId,
    updateBeneficiaryPersonalInfo,
  } = useEAppStore(
    state => ({
      beneficiariesPersonalInfo: state.beneficiariesPersonalInfo?.filter(
        item => item?.personalDetails?.beneficiaryType === beneficiaryType,
      ),
      updateBeneficiaryId: state.updateBeneficiaryId,
      updateBeneficiaryPersonalInfo: state.updateBeneficiaryPersonalInfo,
    }),
    shallow,
  );
  const {
    isCompletedHealthQuestionPolicyOwner,
    isCompletedHealthQuestionInsured,
    isPIEqualPO,
    hasOwnerRider,
  } = useEAppStore(
    state => ({
      isCompletedHealthQuestionPolicyOwner:
        state.isCompletedHealthQuestionPolicyOwner,
      isCompletedHealthQuestionInsured: state.isCompletedHealthQuestionInsured,
      isPIEqualPO: state.isPIEqualPO,
      hasOwnerRider: state.hasOwnerRider,
    }),
    shallow,
  );
  const benefitAllocationBeneficiaries = useBenefitAllocationBeneficiaries({
    type: beneficiaryType,
  });
  const isAbleToGoToHQ = useAbleToGoNextHQ();
  const isCTAPrimaryTypeBtnDisabled = isActionDisabled;
  const isCTASecondaryTypeBtnDisabled =
    (benefitAllocationBeneficiaries?.length !== 0 && isActionDisabled) ||
    !isAbleToGoToHQ;
  const isChartVisible = isPrimaryBeneficiary
    ? benefitAllocationBeneficiaries.length > 0
    : true;
  const isChartShouldWarning =
    !isPrimaryBeneficiary && benefitAllocationBeneficiaries.length === 0;

  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { saveOcrImage } = useSaveOcrImage();
  const [loading, setLoading] = useState(false);
  const onSubmit = async () => {
    setLoading(true);
    try {
      for (const beneficiaryInfo of allBeneficiaries) {
        const id = await saveParty(
          toParty({ role: PartyRole.BENEFICIARY, info: beneficiaryInfo }),
        );
        updateBeneficiaryId(beneficiaryInfo.index, id);

        const data = beneficiaryInfo;
        if (id) {
          const { fileName, filePath } = await saveOcrImage(
            id,
            data.personalDetails.document.frontImage,
            PartyRole.BENEFICIARY,
          );
          if (fileName && filePath) {
            updateBeneficiaryPersonalInfo({
              id,
              index: data.index,
              personalDetails: {
                ...data.personalDetails,
                document: {
                  ...data.personalDetails.document,
                  frontImage: {
                    name: fileName,
                    thumbnail: filePath,
                    base64: data.personalDetails.document.frontImage.base64,
                    fromOcr: true,
                  },
                },
              },
            });
          } else {
            updateBeneficiaryPersonalInfo({ id, index: data.index });
          }
        }
      }
    } catch (e) {
    } finally {
      setLoading(false);
    }

    if (isPrimaryBeneficiary) {
      next(true);
    } else {
      if (isPIEqualPO) {
        if (isCompletedHealthQuestionPolicyOwner) {
          multipleNext(2, true);
        } else {
          next(true);
        }
      } else {
        if (hasOwnerRider) {
          if (isCompletedHealthQuestionPolicyOwner) {
            if (isCompletedHealthQuestionInsured) {
              multipleNext(3, true);
            } else {
              multipleNext(2, true);
            }
          } else {
            next(true);
          }
        } else {
          if (isCompletedHealthQuestionInsured) {
            multipleNext(2, true);
          } else {
            next(true);
          }
        }
      }
    }
  };

  const onSave = useCallback(async () => {
    for (const beneficiaryInfo of allBeneficiaries) {
      const id = await saveParty(
        toParty({ role: PartyRole.BENEFICIARY, info: beneficiaryInfo }),
      );
      if (id) {
        await saveOcrImage(
          id,
          beneficiaryInfo.personalDetails.document.frontImage,
          PartyRole.BENEFICIARY,
        );
      }
    }
  }, [allBeneficiaries, saveParty, saveOcrImage]);

  const incomplete = isPrimaryBeneficiary
    ? isCTAPrimaryTypeBtnDisabled
    : isCTASecondaryTypeBtnDisabled;
  useIncompleteSync(
    incomplete,
    'appDetail',
    'other',
    isPrimaryBeneficiary ? 'primaryBeneficiary' : 'secondaryBeneficiary',
  );

  return (
    <Box flex={1}>
      <KeyboardAwareScrollView>
        <BeneficiaryHeader
          onTooltipPressed={setTooltipVisible}
          title={
            isPrimaryBeneficiary
              ? `Primary beneficiary`
              : `Secondary beneficiary`
          }
          beneficiaryType={beneficiaryType}
          autoAssignPB={autoAssignPB}
        />
        <BeneficiaryBody
          beneficiaryType={beneficiaryType}
          beneficiariesPersonalInfo={beneficiariesPersonalInfo}
          benefitAllocationBeneficiaries={benefitAllocationBeneficiaries}
          autoAssignPB={autoAssignPB}
        />
        <BenefitPercentage
          title="Benefit percentage"
          shouldNotWarning={isChartShouldWarning}
          visible={isChartVisible}
          beneficiaries={benefitAllocationBeneficiaries}
          beneficiaryType={beneficiaryType}
          beneficiariesPersonalInfo={beneficiariesPersonalInfo}
        />
        <Box h={space[4]} />
      </KeyboardAwareScrollView>
      <FormAction
        primaryLoading={isSavingParty || loading}
        primaryDisabled={
          isPrimaryBeneficiary
            ? isCTAPrimaryTypeBtnDisabled
            : isCTASecondaryTypeBtnDisabled
        }
        onPrimaryPress={onSubmit}
      />
      <OnSaveHandler
        onSave={onSave}
        groupKey="appDetail"
        itemGroupKey="other"
        itemKey={
          isPrimaryBeneficiary ? 'primaryBeneficiary' : 'secondaryBeneficiary'
        }
      />
      <BeneficiaryInfoTooltipModal
        visible={tooltipVisible}
        onDismiss={() => setTooltipVisible(false)}
      />
    </Box>
  );
}

export const PrimaryBeneficiaryInfo = () => (
  <BeneficiaryInfo beneficiaryType="PB" />
);
export const SecondaryBeneficiaryInfo = () => (
  <BeneficiaryInfo beneficiaryType="SB" />
);
