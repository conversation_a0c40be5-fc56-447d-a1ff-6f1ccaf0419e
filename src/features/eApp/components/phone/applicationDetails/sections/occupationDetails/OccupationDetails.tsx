import {
  Box,
  CurrencyTextField,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import { useTranslation } from 'react-i18next';
import TitleApplicationModal from '../modals/TitleApplicationModal';
import IconWork from '../icons/IconWork';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import OccupationDetailsTooltipModal from './modals/OccupationDetailsTooltipModal';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import FormFooter from '../FormFooter';
import {
  OccupationDetailsForm,
  occupationDetailsSchema,
} from 'features/eApp/validations/applicationDetails/occupationDetailsValidation';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useGetOptionList } from 'hooks/useGetOptionList';
import {
  BusinessNature,
  FundSource,
  OccupationGroup,
  OccupationSubgroup,
} from 'types/optionList';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import useJuvenile from 'features/eApp/hooks/useJuvenile';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { PartyType } from 'types/party';

interface Props {
  onDismiss: () => void;
  value: OccupationDetailsForm;
  onDone: (values: OccupationDetailsForm) => void;
}

export const OccupationDetails = ({ onDismiss, value, onDone }: Props) => {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();
  const { clientType, entityName } = useEAppStore(state => {
    return {
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
      entityName: state.policyOwnerPersonalInfo.entityDetails.entityName,
    };
  });

  const isJuvenile = useJuvenile();

  const openOccupationDetails = () => {
    setTooltipVisible(true);
  };

  const itemKey = useEAppProgressBarStore(state => state.itemKey);

  const resolver = useEAppValidationResolver(occupationDetailsSchema);
  const {
    control,
    watch,
    handleSubmit,
    setValue,
    formState: { isValid },
  } = useForm<OccupationDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  useEffect(() => {
    if (itemKey === 'insured' && isJuvenile) {
      setValue('occupationType', '0005');
    }
  }, [itemKey, isJuvenile]);

  useEffect(() => {
    if (clientType === PartyType.ENTITY) {
      setValue('nameOfEmployer', entityName);
    }
  }, [clientType, entityName]);
  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();

  const submit = useCallback(
    (data: OccupationDetailsForm) => {
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  const occupationOptions = useMemo(() => {
    if (clientType === PartyType.ENTITY) {
      return optionList?.OCCUPATION_GROUP_ENTITY.options || [];
    }
    return optionList?.OCCUPATION_GROUP.options || [];
  }, [clientType, optionList]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          index={1}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <Row px={theme.space[isNarrowScreen ? 3 : 4]}>
            <TitleApplicationModal
              icon={<IconWork />}
              content={t('eApp:occupationDetails.title')}
              isShowTooltip={true}
              handleActionTooltip={() => {
                openOccupationDetails();
              }}
            />
          </Row>
          <BottomSheetScrollView
            keyboardDismissMode="on-drag"
            style={{ paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4] }}>
            <Box>
              <Box my={theme.space[3]}>
                <LargeBody fontWeight="bold">
                  {t('eApp:occupationDetails.describesQuestion')}
                </LargeBody>
              </Box>
              <Input
                control={control}
                as={RadioButtonGroup}
                name="occupationType">
                {occupationOptions.map(group => {
                  return (
                    <React.Fragment key={group.value}>
                      <RadioButton
                        value={group.value}
                        label={group.label}
                        labelStyle={{ flex: 1 }}
                        style={{ marginBottom: 16 }}
                        disabled={itemKey === 'insured' && isJuvenile}
                      />
                      {group.value === '0004' &&
                        watch('occupationType') === '0004' && (
                          <Input
                            control={control}
                            as={RadioButtonGroup}
                            name="occupationTypeDetail">
                            {optionList?.OCCUPATION_SUBGROUP?.options.map(
                              subgroup => (
                                <RadioButton
                                  key={subgroup.value}
                                  value={subgroup.value}
                                  label={subgroup.label}
                                  labelStyle={{ flex: 1 }}
                                  style={{ marginBottom: 16, marginLeft: 33 }}
                                />
                              ),
                            )}
                          </Input>
                        )}
                    </React.Fragment>
                  );
                })}
              </Input>
              {itemKey === 'payor' && (
                <Input
                  control={control}
                  as={SearchableDropdown<BusinessNature, string>}
                  name="natureOfBusiness"
                  label="Nature of business"
                  style={{
                    marginTop: theme.space[4],
                  }}
                  disabled={isFetchingOptionList}
                  data={optionList?.BUSINESS_NATURE.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  multiline
                />
              )}
              <Input
                control={control}
                as={TextField}
                name="nameOfEmployer"
                label={t('eApp:occupationDetails.nameOfEmployer')}
                disabled={clientType === PartyType.ENTITY}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Input
                control={control}
                as={CurrencyTextField}
                name="annualIncome"
                label={t('eApp:occupationDetails.annualIncome')}
                style={{
                  marginTop: theme.space[4],
                  marginBottom: itemKey === 'payor' ? 0 : theme.space[4],
                }}
                keyboardType="numeric"
              />
              {itemKey === 'payor' && (
                <>
                  <Input
                    control={control}
                    as={SearchableDropdown<FundSource, string>}
                    name="sourceOfFund"
                    label="Source of fund"
                    style={{
                      marginTop: theme.space[4],
                    }}
                    disabled={isFetchingOptionList}
                    data={optionList?.FUND_SOURCE.options ?? []}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="otherSourceOfFund"
                    label="Other sources (optional)"
                    style={{
                      marginVertical: theme.space[4],
                    }}
                  />
                </>
              )}
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
      <OccupationDetailsTooltipModal
        visible={tooltipVisible}
        onDismiss={() => setTooltipVisible(false)}
        occupationType={
          watch('occupationType') as OccupationGroup['value'] | ''
        }
        setOccupationType={type => setValue('occupationType', type)}
        occupationTypeDetail={
          watch('occupationTypeDetail') as OccupationSubgroup['value'] | ''
        }
        setOccupationTypeDetail={type => setValue('occupationTypeDetail', type)}
      />
    </Portal>
  );
};
