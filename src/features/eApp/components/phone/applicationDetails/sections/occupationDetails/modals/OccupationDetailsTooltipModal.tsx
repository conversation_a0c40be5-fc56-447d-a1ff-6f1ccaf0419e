import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Box, Column, Icon, LargeLabel, Row } from 'cube-ui-components';
import OccupationList from '../OccupationList';
import { Modal, ScrollView, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { OccupationGroup, OccupationSubgroup } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { PartyType } from 'types/party';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';

interface Props {
  visible: boolean;
  occupationType: OccupationGroup['value'] | '';
  setOccupationType: (value: string) => void;
  occupationTypeDetail: OccupationSubgroup['value'] | '';
  setOccupationTypeDetail: (value: string) => void;
  onDismiss: () => void;
}

export default function OccupationDetailsTooltipModal({
  visible,
  occupationType,
  setOccupationType,
  occupationTypeDetail,
  setOccupationTypeDetail,
  onDismiss,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { colors, sizes } = useTheme();
  const { top, bottom } = useSafeAreaInsets();
  const { data: optionList } = useGetOptionList();

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const clientType = useEAppStore(
    state => state.policyOwnerPersonalInfo.personalDetails.customerType,
  );

  const occupationOptions = useMemo(() => {
    if (clientType === PartyType.ENTITY) {
      return optionList?.OCCUPATION_GROUP_ENTITY.options || [];
    }
    return optionList?.OCCUPATION_GROUP.options || [];
  }, [clientType, optionList]);

  return (
    <Modal visible={visible} statusBarTranslucent animationType="fade">
      <Column flex={1} marginTop={top} backgroundColor={colors.background}>
        <Row>
          <LargeLabel
            fontWeight="bold"
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              textAlign: 'center',
              alignSelf: 'center',
            }}>
            {t('eApp:occupationDetails.title')}
          </LargeLabel>
          <TouchableOpacity
            onPress={onDismiss}
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              width: sizes[isNarrowScreen ? 10 : 11],
              height: sizes[11],
            }}>
            <Icon.Close fill={colors.palette.black} />
          </TouchableOpacity>
        </Row>
        <ScrollView>
          {occupationOptions.map(group => {
            return (
              <OccupationList
                key={group.value}
                occupationType={occupationType}
                setOccupationType={setOccupationType}
                occupationTypeDetail={occupationTypeDetail}
                setOccupationTypeDetail={setOccupationTypeDetail}
                paragraphNumber={group.value}
                paragraphTitle={group.label}
              />
            );
          })}
          <Box height={sizes[6] + bottom} />
        </ScrollView>
      </Column>
    </Modal>
  );
}
