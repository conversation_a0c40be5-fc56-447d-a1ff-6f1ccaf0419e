import React, { useCallback } from 'react';
import { Box, PictogramIcon, Row, Text, TextField } from 'cube-ui-components';
import TitleApplicationModal from 'features/eApp/components/phone/applicationDetails/sections/modals/TitleApplicationModal';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useForm } from 'react-hook-form';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import FormFooter from 'features/eApp/components/phone/applicationDetails/sections/FormFooter';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import {
  AuthorizedRepresentativeDetailsForm,
  authorizedRepresentativeDetailsSchema,
  maxPositionLength,
} from 'features/eApp/validations/applicationDetails/authorizedRepresentativeDetailsValidation';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import { Extension, Title } from 'types/optionList';
import Input from 'components/Input';
import { maxNameLength } from 'features/eApp/validations/applicationDetails/personalDetailsValidation';

interface Props {
  onDismiss: () => void;
  value: AuthorizedRepresentativeDetailsForm;
  onDone: (values: AuthorizedRepresentativeDetailsForm) => void;
}

export const AuthorizedRepresentativeDetails = ({
  onDismiss,
  value,
  onDone,
}: Props) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const resolver = useEAppValidationResolver(
    authorizedRepresentativeDetailsSchema,
  );
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<AuthorizedRepresentativeDetailsForm>({
    mode: 'onBlur',
    defaultValues: {
      ...value,
    },
    resolver,
  });

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();

  const submit = useCallback(
    (data: AuthorizedRepresentativeDetailsForm) => {
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <Row px={space[isNarrowScreen ? 3 : 4]}>
            <TitleApplicationModal
              icon={<PictogramIcon.Building2 size={40} />}
              content={t('eApp:authorizedRepresentative.title')}
            />
          </Row>
          <BottomSheetScrollView
            keyboardDismissMode="on-drag"
            style={{
              paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            }}>
            {/* Form */}
            <Box mt={space[1]}>
              <Input
                control={control}
                as={SearchableDropdown<Title, string>}
                name="title"
                style={{
                  marginTop: space[4],
                }}
                label={t('eApp:personalDetails.salutation')}
                modalTitle={t('eApp:personalDetails.salutation')}
                data={optionList?.TITLE.options ?? []}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
              />
              <Input
                control={control}
                as={TextField}
                name="firstName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.firstName')}
                style={{
                  marginTop: space[4],
                }}
              />
              <Input
                control={control}
                as={TextField}
                name="middleName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.middleName')}
                style={{
                  marginTop: space[4],
                }}
              />
              <Input
                control={control}
                as={TextField}
                name="lastName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.lastName')}
                style={{
                  marginTop: space[4],
                }}
              />
              <Input
                control={control}
                as={SearchableDropdown<Extension, string>}
                data={optionList?.EXTENSION.options ?? []}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
                name="extensionName"
                label={t('eApp:personalDetails.extensionName')}
                style={{
                  marginTop: space[4],
                }}
              />
              <Input
                control={control}
                as={TextField}
                name="position"
                maxLength={maxPositionLength}
                label={t('eApp:authorizedRepresentative.position')}
                style={{
                  marginTop: space[4],
                }}
              />
            </Box>
          </BottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
