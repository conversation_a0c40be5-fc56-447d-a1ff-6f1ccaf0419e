import { Box, Row, TextField } from 'cube-ui-components';
import React, { useCallback, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import { useTranslation } from 'react-i18next';
import IconGlobe from '../icons/IconGlobe';
import TitleApplicationModal from '../modals/TitleApplicationModal';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import FormFooter from '../FormFooter';
import {
  NationalityDetailsForm,
  nationalityDetailsSchema,
} from 'features/eApp/validations/applicationDetails/nationalityDetailsValidation';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { CityTown, Country, Nationality } from 'types/optionList';
import { PH_COUNTRY } from 'constants/optionList';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import uniqBy from 'lodash/uniqBy';
import { View } from 'react-native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface Props {
  onDismiss: () => void;
  value: NationalityDetailsForm;
  onDone: (values: NationalityDetailsForm) => void;
}

export const NationalityDetails = ({ onDismiss, value, onDone }: Props) => {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const resolver = useEAppValidationResolver(nationalityDetailsSchema);
  const {
    control,
    watch,
    handleSubmit,
    getValues,
    setValue,
    formState: { isValid },
  } = useForm<NationalityDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  const uniqueCityList = useMemo(
    () => uniqBy(optionList?.CITY_TOWN.options || [], 'value'),
    [optionList?.CITY_TOWN.options],
  );

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = useCallback(
    (data: NationalityDetailsForm) => {
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <View onLayout={handleContentLayout}>
            <Row px={theme.space[isNarrowScreen ? 3 : 4]}>
              <TitleApplicationModal
                icon={<IconGlobe />}
                content={t('eApp:nationalityDetails.title')}
              />
            </Row>
            <BottomSheetScrollView
              keyboardDismissMode="on-drag"
              style={{
                paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
              }}>
              {/* Form */}
              <Box mt={theme.space[1]}>
                <Input
                  control={control}
                  as={SearchableDropdown<Nationality, string>}
                  modalTitle={t('eApp:nationalityDetails.nationality')}
                  name="nationality"
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:nationalityDetails.nationality')}
                  disabled={isFetchingOptionList}
                  data={optionList?.NATIONALITY.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  searchable
                />
                <Input
                  control={control}
                  as={SearchableDropdown<Country, string>}
                  name="countryOfBirth"
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:nationalityDetails.dateOfBirth')}
                  modalTitle={t('eApp:nationalityDetails.dateOfBirth')}
                  disabled={isFetchingOptionList}
                  data={optionList?.COUNTRY.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  searchable
                  onChange={(value: string) => {
                    if (
                      (value === PH_COUNTRY &&
                        getValues('countryOfBirth') !== PH_COUNTRY) ||
                      (value !== PH_COUNTRY &&
                        getValues('countryOfBirth') === PH_COUNTRY)
                    ) {
                      setValue('placeOfBirth', '');
                    }
                  }}
                />
                {watch('countryOfBirth') === PH_COUNTRY ? (
                  <Input
                    control={control}
                    as={SearchableDropdown<CityTown, string>}
                    name="placeOfBirth"
                    style={{
                      marginTop: theme.space[5],
                      marginBottom: theme.space[4],
                    }}
                    label={t('eApp:nationalityDetails.placeOfBirth')}
                    modalTitle={t('eApp:nationalityDetails.placeOfBirth')}
                    disabled={isFetchingOptionList}
                    data={uniqueCityList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                    searchable
                  />
                ) : (
                  <Input
                    control={control}
                    as={TextField}
                    name="placeOfBirth"
                    label={t('eApp:nationalityDetails.placeOfBirth')}
                    style={{
                      marginTop: theme.space[5],
                      marginBottom: theme.space[4],
                    }}
                  />
                )}
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
