import { Box, TextField } from 'cube-ui-components';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import { useTranslation } from 'react-i18next';
import TitleApplicationModal from '../modals/TitleApplicationModal';
import IconDocument from '../icons/IconDocument';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import FormFooter from '../FormFooter';
import {
  AdditionalDetailsForm,
  additionalDetailsSchema,
} from 'features/eApp/validations/applicationDetails/additionalDetailsValidation';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { FundSource, InsurancePurpose, PremiumSource } from 'types/optionList';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { View } from 'react-native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import { CHANNELS } from 'types/channel';
import { PartyType } from 'types/party';

interface Props {
  onDismiss: () => void;
  value: AdditionalDetailsForm;
  onDone: (values: AdditionalDetailsForm) => void;
}

export const AdditionalDetails = ({ onDismiss, value, onDone }: Props) => {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const cubeChannel = useGetCubeChannel();
  const { itemKey } = useEAppProgressBarStore(
    state => ({
      itemKey: state.itemKey,
    }),
    shallow,
  );

  const showPremium =
    cubeChannel === CHANNELS.BANCA && itemKey === 'policyOwner';

  const resolver = useEAppValidationResolver(additionalDetailsSchema);
  const { clientType, relationship } = useEAppStore(
    state => ({
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
      relationship: state.insuredPersonalInfo.personalDetails.relationship,
    }),
    shallow,
  );

  const purposeOfInsurance = useMemo(() => {
    return (
      optionList?.INSURANCE_PURPOSE.options?.filter(
        purpose =>
          purpose?.key?.toLowerCase?.() === clientType?.toLowerCase?.(),
      ) || []
    );
  }, [clientType, optionList]);
  const {
    control,
    handleSubmit,
    formState: { isValid },
    setValue,
  } = useForm<AdditionalDetailsForm>({
    mode: 'onBlur',
    defaultValues: { ...value, showPremium },
    resolver,
  });

  useEffect(() => {
    const purposeOfInsuranceMappingEntity = {
      EEE: 'FB',
      EEEK: 'KI',
      BP: 'PI',
    };
    if (clientType === PartyType.ENTITY) {
      setValue(
        'purposeOfInsurance',
        purposeOfInsuranceMappingEntity[
          relationship as keyof typeof purposeOfInsuranceMappingEntity
        ],
      );
    }
  }, [clientType, relationship]);

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const getSourceOfFund = useCallback(() => {
    if (clientType === PartyType.ENTITY) {
      return optionList?.FUND_SOURCE_ENTITY.options ?? [];
    }
    return optionList?.FUND_SOURCE.options ?? [];
  }, [clientType, optionList]);

  const submit = useCallback(
    (data: AdditionalDetailsForm) => {
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <View onLayout={handleContentLayout}>
            <Box px={theme.space[isNarrowScreen ? 3 : 4]}>
              <TitleApplicationModal
                icon={<IconDocument />}
                content={t('eApp:additionalDetail.title')}
              />
            </Box>
            <BottomSheetScrollView
              keyboardDismissMode="on-drag"
              style={{
                paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
              }}>
              <Box mt={theme.space[1]}>
                <Input
                  control={control}
                  as={SearchableDropdown<InsurancePurpose, string>}
                  name="purposeOfInsurance"
                  style={{
                    marginTop: theme.space[6],
                  }}
                  label={t('eApp:additionalDetail.purposeOfInsurance')}
                  modalTitle={t('eApp:additionalDetail.purposeOfInsurance')}
                  data={purposeOfInsurance ?? []}
                  disabled={
                    isFetchingOptionList || clientType === PartyType.ENTITY
                  }
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                />
                {clientType === PartyType.INDIVIDUAL && (
                  <Input
                    control={control}
                    as={TextField}
                    name="otherPurposeOfInsurance"
                    label={t('eApp:additionalDetail.otherPurpose')}
                    style={{
                      marginTop: theme.space[5],
                    }}
                  />
                )}

                <Input
                  control={control}
                  name="sourceOfFund"
                  as={SearchableDropdown<FundSource, string>}
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:additionalDetail.sourceOfFund')}
                  modalTitle={t('eApp:additionalDetail.sourceOfFund')}
                  data={getSourceOfFund()}
                  disabled={isFetchingOptionList}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                />
                <Input
                  control={control}
                  as={TextField}
                  label={t('eApp:additionalDetail.otherSource')}
                  name="otherSourceOfFund"
                  style={{
                    marginTop: theme.space[5],
                    marginBottom: showPremium ? 0 : theme.space[4],
                  }}
                />
                {showPremium && (
                  <Input
                    control={control}
                    name="sourceOfPremium"
                    as={SearchableDropdown<PremiumSource, string>}
                    style={{
                      marginTop: theme.space[5],
                      marginBottom: theme.space[4],
                    }}
                    label={t('eApp:additionalDetail.sourceOfPremium')}
                    modalTitle={t('eApp:additionalDetail.sourceOfPremium')}
                    data={optionList?.PREMIUM_SOURCE.options ?? []}
                    disabled={isFetchingOptionList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                  />
                )}
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
