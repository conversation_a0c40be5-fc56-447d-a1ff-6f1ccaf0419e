import { BottomSheetFooterProps } from '@gorhom/bottom-sheet';
import { memo } from 'react';
import { Platform } from 'react-native';
import { default as FormFooterIOS } from './FormFooter.ios';
import { default as FormFooterAndroid } from './FormFooter.android';

interface Props extends BottomSheetFooterProps {
  disabled: boolean;
  onPress: () => void;
  buttonTitle?: string;
  loading?: boolean;
}

function FormFooterInner(props: Props) {
  if (Platform.OS === 'android') {
    return <FormFooterAndroid {...props} />;
  }
  return <FormFooterIOS {...props} />;
}

const FormFooter = memo(FormFooterInner);
export default FormFooter;
