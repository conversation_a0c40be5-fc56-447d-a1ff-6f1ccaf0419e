import { useTheme } from '@emotion/react';
import {
  Box,
  DatePicker,
  Dropdown,
  Icon,
  Label,
  Picker,
  Row,
  TextField,
} from 'cube-ui-components';
import { MutableRefObject, useEffect, useMemo, useRef, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { shallow } from 'zustand/shallow';
import { CUSTOMER_TYPES } from 'constants/optionList';
import Input from 'components/Input';
import OptionPicker from 'components/OptionPicker';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import useJuvenile from 'features/eApp/hooks/useJuvenile';
import { useGetAgeByDate } from 'features/eApp/hooks/useGetAgeByDate';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import {
  maxNameLength,
  InsuredPersonalDetailsForm,
} from 'features/eApp/validations/applicationDetails/personalDetailsValidation';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useGetOptionList } from 'hooks/useGetOptionList';
import {
  dateFormatUtil,
  dateFormatWithSlashUtil,
} from 'utils/helper/formatUtil';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import {
  Extension,
  IdType,
  MaritalStatus,
  OwnerRelationship,
  PrimaryIdType,
  Title,
} from 'types/optionList';
import { PartyType } from 'types/party';
import OtherNameButton from './components/OtherNameButton';
import SectionContentContainer from './components/SectionContentContainer';

type Props = {
  optionList: ReturnType<typeof useGetOptionList>;
  form: ReturnType<typeof useForm<InsuredPersonalDetailsForm>>;
  onShowUpdateStpFieldConfirmationDialog: () => void;
  onUpdateCustomer: () => void;
  ocrScanned: MutableRefObject<boolean>;
  lockOCRFields: boolean;
};

const InsuredPersonalDetailsContent = ({
  optionList,
  form,
  onShowUpdateStpFieldConfirmationDialog,
  onUpdateCustomer,
  ocrScanned,
  lockOCRFields,
}: Props) => {
  const { data: optionListData, isFetching } = optionList;
  const { control, getValues, setValue, formState, trigger, watch } = form;

  const { space, colors, sizes, animation } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation('eApp');

  const { minDate, defaultDate, maxDate } = getDateOfBirthDropdownProps();
  const firstNameRef = useRef('');
  const middleNameRef = useRef<string | undefined>('');
  const lastNameRef = useRef('');

  const { isStp, clientType } = useEAppStore(
    state => ({
      isStp: state.insuredPersonalInfo.isSTP,
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
    }),
    shallow,
  );

  const isJuvenile = useJuvenile();

  const ownerRelationship = useMemo(() => {
    return (
      optionListData?.OWNER_RELATIONSHIP.options?.filter(
        purpose =>
          purpose?.key?.toLowerCase?.() === clientType?.toLowerCase?.(),
      ) || []
    );
  }, [clientType, optionListData?.OWNER_RELATIONSHIP.options]);

  const primaryIdTypes = useMemo(() => {
    return isJuvenile
      ? (optionListData?.PRIMARY_ID_TYPE.options || []).filter(el => {
          return ['BC', 'RP', 'SI', 'AC', 'US', 'OH', 'IC'].includes(el.value);
        })
      : optionListData?.PRIMARY_ID_TYPE.options || [];
  }, [optionListData, isJuvenile]);

  const dob = useWatch({
    name: 'dateOfBirth',
    control,
  });

  const disabledExpiryDate = useWatch({
    name: 'disabledExpiryDate',
    control,
  });

  const { data: age } = useGetAgeByDate(dob, age =>
    setValue('age', isNaN(Number(age)) ? 0 : Number(age)),
  );

  const [isOtherNameExpand, setIsOtherNameExpand] = useState(false);
  const otherNameHeight = useSharedValue(isOtherNameExpand ? sizes[73] : 0);
  const chevronRotation = useSharedValue(isOtherNameExpand ? 180 : 0);

  useEffect(() => {
    if (isOtherNameExpand) {
      otherNameHeight.value = withTiming(sizes[73], {
        duration: animation.duration,
      });
      chevronRotation.value = withTiming(180, {
        duration: animation.duration,
      });
    } else {
      otherNameHeight.value = withTiming(0, {
        duration: animation.duration,
      });
      chevronRotation.value = withTiming(0, {
        duration: animation.duration,
      });
    }
  }, [isOtherNameExpand, sizes, animation.duration, otherNameHeight, chevronRotation]);
  const style = useAnimatedStyle(() => {
    return {
      height: otherNameHeight.value,
      overflow: 'hidden',
    };
  }, []);
  const chevronStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${chevronRotation.value}deg` }],
    };
  }, []);

  return (
    <SectionContentContainer isNarrowScreen={isNarrowScreen}>
      <Box>
        <Label fontWeight="bold" color={colors.primary}>
          {t('personalDetails.name')}
        </Label>
        <Row>
          <Input
            control={control}
            as={SearchableDropdown<(typeof CUSTOMER_TYPES)[0], string>}
            disabled
            name="customerType"
            style={{
              flex: 1,
              marginTop: space[4],
              marginRight: space[1],
            }}
            label={t('personalDetails.customerType')}
            modalTitle={t('personalDetails.customerType')}
            data={CUSTOMER_TYPES}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
          <Input
            control={control}
            as={SearchableDropdown<Title, string>}
            disabled
            name="title"
            style={{
              flex: 1,
              marginTop: space[4],
              marginLeft: space[1],
            }}
            label={t('personalDetails.salutation')}
            modalTitle={t('personalDetails.salutation')}
            data={optionListData?.TITLE.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
        </Row>
        <Input
          control={control}
          as={TextField}
          name="firstName"
          maxLength={maxNameLength}
          label={t('personalDetails.firstName')}
          style={{
            marginTop: space[4],
          }}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
            firstNameRef.current = getValues('firstName');
          }}
          onBlur={() => {
            if (
              ocrScanned.current &&
              firstNameRef.current &&
              firstNameRef.current.trim().toLowerCase() !==
                getValues('firstName').trim().toLowerCase()
            ) {
              onUpdateCustomer();
            }
          }}
          disabled={lockOCRFields}
        />
        <Input
          control={control}
          as={TextField}
          name="middleName"
          maxLength={maxNameLength}
          label={t('personalDetails.middleName')}
          style={{
            marginTop: space[4],
          }}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
            middleNameRef.current = getValues('middleName');
          }}
          onBlur={() => {
            if (
              ocrScanned.current &&
              middleNameRef.current?.trim().toLowerCase() !==
                getValues('middleName')?.trim().toLowerCase()
            ) {
              onUpdateCustomer();
            }
          }}
          disabled={lockOCRFields}
        />
        <Input
          control={control}
          as={TextField}
          name="lastName"
          maxLength={maxNameLength}
          label={t('personalDetails.lastName')}
          style={{
            marginTop: space[4],
          }}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
            lastNameRef.current = getValues('lastName');
          }}
          onBlur={() => {
            if (
              ocrScanned.current &&
              lastNameRef.current &&
              lastNameRef.current.trim().toLowerCase() !==
                getValues('lastName').trim().toLowerCase()
            ) {
              onUpdateCustomer();
            }
          }}
          disabled={lockOCRFields}
        />
        <Input
          control={control}
          as={SearchableDropdown<Extension, string>}
          data={optionListData?.EXTENSION.options ?? []}
          getItemLabel={item => item.label}
          getItemValue={item => item.value}
          name="extensionName"
          label={t('personalDetails.extensionName')}
          style={{
            marginTop: space[4],
          }}
        />
        <OtherNameButton
          onPress={() => {
            setIsOtherNameExpand(prev => !prev);
          }}>
          <Label fontWeight="bold" color={colors.primary}>
            {t('personalDetails.otherLegalName')}
          </Label>
          <Animated.View style={chevronStyle}>
            <Icon.ChevronDown />
          </Animated.View>
        </OtherNameButton>
        <Animated.View style={style}>
          <Input
            control={control}
            as={TextField}
            name="otherLegalFirstName"
            maxLength={maxNameLength}
            label={t('personalDetails.otherLegalFirstName')}
            style={{
              marginTop: space[4],
            }}
          />
          <Input
            control={control}
            as={TextField}
            name="otherLegalMiddleName"
            maxLength={maxNameLength}
            label={t('personalDetails.otherLegalMiddleName')}
            style={{
              marginTop: space[4],
            }}
          />
          <Input
            control={control}
            as={TextField}
            name="otherLegalLastName"
            maxLength={maxNameLength}
            label={t('personalDetails.otherLegalLastName')}
            style={{
              marginTop: space[4],
            }}
          />
          <Input
            control={control}
            as={SearchableDropdown<Extension, string>}
            data={optionListData?.EXTENSION.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            name="otherLegalExtensionName"
            label={t('personalDetails.otherLegalExtensionName')}
            disabled={isFetching}
            style={{
              marginTop: space[4],
            }}
          />
        </Animated.View>
        <Row mt={space[6]}>
          <Label fontWeight="bold" color={colors.primary}>
            {t('personalDetails.identificationDetails')}
          </Label>
        </Row>
        <Row>
          <Input
            control={control}
            as={Dropdown<IdType, string>}
            name="idType"
            style={{
              flex: 1,
              marginTop: space[4],
              marginRight: space[1],
            }}
            label={'TIN/SSS/GSIS'}
            modalTitle={'TIN/SSS/GSIS'}
            data={optionListData?.ID_TYPE.options ?? []}
            disabled={isFetching}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
          <Input
            control={control}
            as={TextField}
            name="idNumber"
            style={{
              flex: 1,
              marginTop: space[4],
              marginLeft: space[1],
            }}
            label={'TIN/SSS/GSIS no.'}
            disabled={['None'].includes(watch('idType'))}
          />
        </Row>
        <Input
          control={control}
          as={SearchableDropdown<PrimaryIdType, string>}
          name="primaryIdType"
          style={{
            marginTop: space[4],
          }}
          preventPopup={isStp}
          label={t('personalDetails.primaryIdType')}
          searchable
          searchLabel={t('personalDetails.primaryIdType')}
          data={primaryIdTypes}
          disabled={isFetching}
          getItemLabel={item => item.label}
          getItemValue={item => item.value}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
          }}
        />
        <Input
          control={control}
          as={TextField}
          name="primaryIdNumber"
          label={t('personalDetails.primaryIdNumber')}
          style={{
            marginTop: space[4],
          }}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
          }}
        />
        <Input
          control={control}
          as={DatePicker}
          name="expiryDate"
          preventPopup={isStp}
          label={t('personalDetails.expiryDate')}
          modalTitle={t('personalDetails.expiryDate')}
          style={{
            marginTop: space[4],
          }}
          hint="MM/DD/YYYY"
          formatDate={dateFormatWithSlashUtil}
          onFocus={() => {
            if (isStp) onShowUpdateStpFieldConfirmationDialog();
          }}
          disabled={disabledExpiryDate}
          onChange={() => setTimeout(() => trigger('expiryDate'), 100)}
        />
        <Row mt={space[6]}>
          <Label fontWeight="bold" color={colors.primary}>
            {t('personalDetails.otherDetails')}
          </Label>
        </Row>
        <Input
          as={Picker}
          control={control}
          name="gender"
          label={t('personalDetails.gender')}
          disabled
          items={optionListData?.GENDER.options.map(o => ({
            value: o.value,
            text: o.label,
          }))}
          style={{ marginTop: space[4] }}
        />
        <Row>
          <Input
            control={control}
            as={DatePicker}
            disabled
            name="dateOfBirth"
            style={{
              flex: 1,
              marginTop: space[4],
              marginRight: space[1],
            }}
            label={t('personalDetails.dateOfBirth')}
            modalTitle={t('personalDetails.dateOfBirth')}
            hint="MM/DD/YYYY"
            value={dob ?? defaultDate}
            formatDate={() => (dob ? dateFormatUtil(dob) : '')}
            minDate={minDate}
            maxDate={maxDate}
            onChange={() => trigger('dateOfBirth')}
          />
          <TextField
            style={{
              flex: 1,
              marginTop: space[4],
              marginLeft: space[1],
            }}
            label={t('personalDetails.age')}
            disabled
            value={age !== undefined && age !== '' ? `${age} y/o` : ''}
          />
        </Row>
        <Input
          control={control}
          as={Dropdown<MaritalStatus, string>}
          name="maritalStatus"
          style={{
            marginTop: space[4],
          }}
          label={t('personalDetails.maritalStatus')}
          modalTitle={t('personalDetails.maritalStatus')}
          data={optionListData?.MARITAL_STATUS.options ?? []}
          disabled={isFetching}
          getItemLabel={item => item.label}
          getItemValue={item => item.value}
        />

        {clientType === PartyType.ENTITY ? (
          <OptionPicker
            label={t('personalDetails.relationshipToOwner')}
            items={ownerRelationship}
            disabled
            value={watch('relationship')}
            onChange={value =>
              setValue('relationship', value, { shouldValidate: true })
            }
            error={t(formState.errors.relationship?.message as any)}
            style={{ marginVertical: space[4] }}
          />
        ) : (
          <Input
            control={control}
            as={SearchableDropdown<OwnerRelationship, string>}
            name="relationship"
            style={{
              marginVertical: space[4],
            }}
            label="Relationship"
            data={ownerRelationship}
            disabled={isFetching}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
        )}
      </Box>
      <BottomSheetFooterSpace />
    </SectionContentContainer>
  );
};

export default InsuredPersonalDetailsContent;
