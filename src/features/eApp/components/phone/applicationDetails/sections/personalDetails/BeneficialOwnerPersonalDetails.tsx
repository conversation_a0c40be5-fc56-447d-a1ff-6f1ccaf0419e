import {
  Box,
  Row,
  TextField,
  Typography,
  Label,
  Picker,
  DatePicker,
  Dropdown,
} from 'cube-ui-components';
import React, { useCallback, useEffect } from 'react';
import { useTheme } from '@emotion/react';
import { useForm, useWatch } from 'react-hook-form';
import Input from 'components/Input/Input';
import { useGetAgeByDate } from 'features/eApp/hooks/useGetAgeByDate';
import { useTranslation } from 'react-i18next';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { Portal } from '@gorhom/portal';
import Ocr from 'features/eApp/components/phone/ocr/Ocr';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import FormFooter from '../FormFooter';
import {
  BeneficialOwnerPersonalDetailsForm,
  maleTitles,
  beneficialOwnerPersonalDetailsSchema,
  maxNameLength,
  femaleTitles,
} from 'features/eApp/validations/applicationDetails/personalDetailsValidation';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Extension, IdType, PrimaryIdType, Title } from 'types/optionList';
import { Gender } from 'types/person';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import { PartyRole } from 'types/party';
import { capitalizeFirstLetterOfEachWord } from 'utils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import {
  dateFormatUtil,
  dateFormatWithSlashUtil,
} from 'utils/helper/formatUtil';
import IconPersonalDetail from '../icons/IconPersonalDetail';
import { EventRegister } from 'utils/helper/eventRegister';
import { AppEvent, OcrRole } from 'types/event';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { useOcrImage } from 'features/eApp/hooks/useOcrImage';
import { useHandleOcrImageDeletion } from 'hooks/useHandleOcrImageDeletion';

interface Props {
  onDismiss: () => void;
  value: BeneficialOwnerPersonalDetailsForm;
  onDone: (values: BeneficialOwnerPersonalDetailsForm) => void;
  partyId?: string | undefined;
}

export const BeneficialOwnerPersonalDetails = ({
  onDismiss,
  value,
  onDone,
  partyId,
}: Props) => {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const setOcrSuccess = useEAppStore(
    state => state.setBeneficialOwnerOcrSuccess,
  );
  const resolver = useEAppValidationResolver(
    beneficialOwnerPersonalDetailsSchema,
  );
  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid },
    trigger,
    watch,
    clearErrors,
  } = useForm<BeneficialOwnerPersonalDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  useEffect(() => {
    if (['None'].includes(watch('idType'))) {
      clearErrors('idNumber');
      setValue('idNumber', '');
    }
  }, [watch('idType')]);

  const document = useWatch({
    name: 'document',
    control: control,
  });

  const title = useWatch({
    name: 'title',
    control: control,
  });
  useEffect(() => {
    if (maleTitles.includes(title)) {
      setValue('gender', Gender.MALE);
    } else if (femaleTitles.includes(title)) {
      setValue('gender', Gender.FEMALE);
    }
  }, [title, setValue]);

  const dob = useWatch({
    name: 'dateOfBirth',
    control: control,
  });
  const disabledExpiryDate = useWatch({
    name: 'disabledExpiryDate',
    control: control,
  });

  const primaryIdType = useWatch({
    name: 'primaryIdType',
    control: control,
  });

  const { data: age } = useGetAgeByDate(dob, age =>
    setValue('age', isNaN(Number(age)) ? 0 : Number(age)),
  );
  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();

  useEffect(() => {
    if (
      primaryIdType &&
      optionList?.PRIMARY_ID_TYPE.options &&
      optionList?.PRIMARY_ID_TYPE.options.length
    ) {
      const primaryTypePicked = optionList.PRIMARY_ID_TYPE.options.find(
        e => e.value === primaryIdType,
      );
      if (primaryTypePicked?.dependencyTrigger === 'noPrimaryIdExpiry') {
        setValue('expiryDate', null);
        setValue('disabledExpiryDate', true);
        clearErrors('expiryDate');
      } else {
        setValue('disabledExpiryDate', false);
      }
    }
  }, [primaryIdType, optionList]);

  const { caseId, handleOcrImageDeletion, deleteDocument, isDeletingDocument } =
    useHandleOcrImageDeletion(partyId);
  const submit = useCallback(
    async (data: BeneficialOwnerPersonalDetailsForm) => {
      await handleOcrImageDeletion(data.document.frontImage);
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [bottomSheetProps.bottomSheetRef, handleOcrImageDeletion, onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
          loading={isDeletingDocument}
        />
      );
    },
    [handleSubmit, isValid, submit, isDeletingDocument],
  );

  const { minDate, defaultDate, maxDate } = getDateOfBirthDropdownProps();

  const ocrImage = useOcrImage(partyId);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  useEffect(() => {
    const sub = EventRegister.addEventListener(AppEvent.OcrRendered, () => {
      console.log(
        '[BeneficialOwnerPersonalDetails] received event',
        AppEvent.OcrRendered,
      );
      onDismiss();
    });
    return () => {
      EventRegister.removeEventListener(sub);
    };
  }, [onDismiss]);

  const primaryIdTypeOptions = optionList?.PRIMARY_ID_TYPE.options ?? [];

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          index={1}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <Row alignItems="center" px={theme.space[isNarrowScreen ? 3 : 4]}>
            <IconPersonalDetail />
            <Typography.H7 fontWeight="bold" color={theme.colors.primary}>
              {t('eApp:personalDetails.title')}
            </Typography.H7>
          </Row>
          <BottomSheetScrollView
            keyboardDismissMode="on-drag"
            style={{
              paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
            }}>
            {/* Scan ID card */}
            <Ocr
              allowToRetake={false}
              role={OcrRole.BeneficialOwner}
              imageBase64={ocrImage.base64}
              imageThumbnail={ocrImage.thumbnail}
              imageName={ocrImage.name}
              onFinish={(data, documentType, image) => {
                const isDocumentTypeValid =
                  documentType !== 'OTHER' &&
                  primaryIdTypeOptions.some(
                    option => option.value === documentType,
                  );
                const mappedDocumentType = isDocumentTypeValid
                  ? documentType
                  : '';

                // for beneficiary roles, setOcrSuccess is set to false because cannot detect the verification.
                setOcrSuccess(false);
                if (data.firstName) {
                  setValue(
                    'firstName',
                    capitalizeFirstLetterOfEachWord(data.firstName)?.trim(),
                    {
                      shouldTouch: true,
                      shouldValidate: true,
                    },
                  );
                }
                if (data.lastName) {
                  setValue(
                    'lastName',
                    capitalizeFirstLetterOfEachWord(data.lastName)?.trim(),
                    {
                      shouldTouch: true,
                      shouldValidate: true,
                    },
                  );
                }
                if (data.middleName) {
                  setValue(
                    'middleName',
                    capitalizeFirstLetterOfEachWord(data.middleName)?.trim(),
                    {
                      shouldTouch: true,
                      shouldValidate: true,
                    },
                  );
                }
                if (data.gender === 'FEMALE') {
                  setValue('gender', Gender.FEMALE, {
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                } else if (data.gender === 'MALE') {
                  setValue('gender', Gender.MALE, {
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }
                if (data.dateOfBirth) {
                  setValue('dateOfBirth', data.dateOfBirth, {
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }
                if (data.expiryDate) {
                  setValue('expiryDate', data.expiryDate, {
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }
                setValue('primaryIdType', mappedDocumentType, {
                  shouldTouch: true,
                  shouldValidate: true,
                });

                if (data.idNumber) {
                  setValue('primaryIdNumber', data.idNumber, {
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }

                if (['TN', 'SS'].includes(documentType)) {
                  setValue('idType', documentType);
                  if (data.idNumber) {
                    setValue('idNumber', data.idNumber);
                  }
                }

                setValue('document.frontImage', { ...image, fromOcr: true });
              }}
              onDelete={() => {
                setValue('document.frontImage', {
                  base64: '',
                  name: '',
                  thumbnail: '',
                  fromOcr: false,
                });
              }}
            />
            {/* Form */}
            <Box>
              <Label fontWeight="bold" color={theme.colors.primary}>
                {t('eApp:personalDetails.name')}
              </Label>
              <Input
                control={control}
                as={SearchableDropdown<Title, string>}
                name="title"
                style={{
                  flex: 1,
                  marginTop: theme.space[4],
                }}
                label={t('eApp:personalDetails.salutation')}
                modalTitle={t('eApp:personalDetails.salutation')}
                data={optionList?.TITLE.options ?? []}
                disabled={isFetchingOptionList}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
              />
              <Input
                control={control}
                as={TextField}
                name="firstName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.firstName')}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Input
                control={control}
                as={TextField}
                name="middleName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.middleName')}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Input
                control={control}
                as={TextField}
                name="lastName"
                maxLength={maxNameLength}
                label={t('eApp:personalDetails.lastName')}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Input
                control={control}
                as={SearchableDropdown<Extension, string>}
                disabled={isFetchingOptionList}
                data={optionList?.EXTENSION.options ?? []}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
                name="extensionName"
                label={t('eApp:personalDetails.extensionName')}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Row mt={theme.space[6]}>
                <Label fontWeight="bold" color={theme.colors.primary}>
                  {t('eApp:personalDetails.identificationDetails')}
                </Label>
              </Row>
              <Row>
                <Input
                  control={control}
                  as={Dropdown<IdType, string>}
                  name="idType"
                  style={{
                    flex: 1,
                    marginTop: theme.space[4],
                    marginRight: theme.space[1],
                  }}
                  label={'TIN/SSS/GSIS'}
                  modalTitle={'TIN/SSS/GSIS'}
                  data={optionList?.ID_TYPE.options ?? []}
                  disabled={isFetchingOptionList}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                />
                <Input
                  control={control}
                  as={TextField}
                  name="idNumber"
                  style={{
                    flex: 1,
                    marginTop: theme.space[4],
                    marginLeft: theme.space[1],
                  }}
                  label={'TIN/SSS/GSIS no.'}
                  disabled={['None'].includes(watch('idType'))}
                />
              </Row>
              <Input
                control={control}
                as={SearchableDropdown<PrimaryIdType, string>}
                name="primaryIdType"
                style={{
                  marginTop: theme.space[4],
                }}
                label={t('eApp:personalDetails.primaryIdType')}
                searchable
                searchLabel={t('eApp:personalDetails.primaryIdType')}
                data={optionList?.PRIMARY_ID_TYPE.options ?? []}
                disabled={isFetchingOptionList}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
              />
              <Input
                control={control}
                as={TextField}
                name="primaryIdNumber"
                label={t('eApp:personalDetails.primaryIdNumber')}
                style={{
                  marginTop: theme.space[4],
                }}
              />
              <Input
                control={control}
                as={DatePicker}
                name="expiryDate"
                label={t('eApp:personalDetails.expiryDate')}
                modalTitle={t('eApp:personalDetails.expiryDate')}
                style={{
                  marginTop: theme.space[4],
                }}
                hint="MM/DD/YYYY"
                formatDate={dateFormatWithSlashUtil}
                disabled={disabledExpiryDate}
                onChange={() => setTimeout(() => trigger('expiryDate'), 100)}
              />
              <Row mt={theme.space[6]}>
                <Label fontWeight="bold" color={theme.colors.primary}>
                  {t('eApp:personalDetails.otherDetails')}
                </Label>
              </Row>
              <Input
                as={Picker}
                control={control}
                name="gender"
                label={t('eApp:personalDetails.gender')}
                disabled={
                  maleTitles.includes(title) || femaleTitles.includes(title)
                }
                items={optionList?.GENDER.options.map(o => ({
                  value: o.value,
                  text: o.label,
                }))}
                style={{ marginTop: theme.space[4] }}
              />
              <Row>
                <Input
                  control={control}
                  as={DatePicker}
                  name="dateOfBirth"
                  style={{
                    flex: 1,
                    marginVertical: theme.space[4],
                    marginRight: theme.space[1],
                  }}
                  label={t('eApp:personalDetails.dateOfBirth')}
                  modalTitle={t('eApp:personalDetails.dateOfBirth')}
                  hint="MM/DD/YYYY"
                  value={dob ?? defaultDate}
                  formatDate={() => (dob ? dateFormatUtil(dob) : '')}
                  minDate={minDate}
                  maxDate={maxDate}
                  onChange={() => setTimeout(() => trigger('dateOfBirth'), 100)}
                />
                <TextField
                  style={{
                    flex: 1,
                    marginVertical: theme.space[4],
                    marginLeft: theme.space[1],
                  }}
                  label={t('eApp:personalDetails.age')}
                  disabled
                  value={age !== undefined && age !== '' ? `${age} y/o` : ''}
                />
              </Row>
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
