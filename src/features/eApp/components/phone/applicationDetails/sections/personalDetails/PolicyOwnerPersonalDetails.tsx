import { Row, SmallBody, Typography } from 'cube-ui-components';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTheme } from '@emotion/react';
import { Keyboard } from 'react-native';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetSectionList,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { OcrData, OcrRef } from 'features/eApp/components/phone/ocr/Ocr';
import { format } from 'date-fns';
import FormFooter from '../FormFooter';
import {
  PolicyOwnerPersonalDetailsForm,
  policyOwnerPersonalDetailsSchema,
} from 'features/eApp/validations/applicationDetails/personalDetailsValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { Gender } from 'types/person';

import {
  MismatchFields,
  OcrValidationResult,
  validateOcr,
} from 'features/eApp/utils/validateOcr';
import OcrValidationErrorDialog from './modals/OcrValidationErrorDialog';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { LACustomer, RootStackParamList } from 'types';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import UpdateSTPFieldConfirmationDialog from './modals/UpdateSTPFieldConfirmationDialog';
import useBoundStore from 'hooks/useBoundStore';
import { applyRegionalLogicToOcrValidationResult } from 'features/eAppV2/common/utils/validateOcr';
import HeadShotSvg from 'features/eAppV2/ph/components/applicationDetails/common/assets/HeadShotSvg';
import FireSvg from 'features/eAppV2/ph/components/applicationDetails/common/assets/FireSvg';
import { PartyRole } from 'types/party';
import { useLACustomerLookup } from 'hooks/useLACustomerLookup';
import LAVerificationDialog from './modals/LAVerificationDialog';
import { capitalizeFirstLetterOfEachWord } from 'utils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { OcrResult } from 'types/ocr';
import IconPersonalDetail from '../icons/IconPersonalDetail';
import Portal from 'components/Portal/Portal';
import PromptProvider from 'components/prompt/PromptContext';
import FormHeaderBadgeContainer from './components/FormHeaderBadgeContainer';
import LivenessCheckContent from './LivenessCheckContent';
import PolicyOwnerPersonalDetailsContent from './PolicyOwnerPersonalDetailsContent';
import { useOcrImage } from 'features/eApp/hooks/useOcrImage';
import { useHandleOcrImageDeletion } from 'hooks/useHandleOcrImageDeletion';

interface Props {
  onDismiss: () => void;
  value: PolicyOwnerPersonalDetailsForm;
  onDone: (values: PolicyOwnerPersonalDetailsForm) => void;
  partyId?: string | undefined;
}

export const PolicyOwnerPersonalDetails = ({
  onDismiss,
  value,
  onDone,
  partyId,
}: Props) => {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation(['eApp', 'livenessCheck']);
  const optionList = useGetOptionList();
  const {
    setSTP,
    setOCRSuccess,
    isFaceMatched,
    isLivenessCheckVerified,
    isPIEqualPO,
    policyOwnerPersonalInfo,
    updatePolicyOwnerPersonalInfo,
    isOcrSuccess,
    isRemoteSelling,
  } = useEAppStore(
    state => ({
      setSTP: state.setPolicyOwnerSTP,
      isOcrSuccess: state.policyOwnerPersonalInfo.isOcrSuccess,
      setOCRSuccess: state.setPolicyOwnerOCRSuccess,
      isFaceMatched: state.policyOwnerPersonalInfo.isFaceMatched,
      isLivenessCheckVerified:
        state.policyOwnerPersonalInfo.isLivenessCheckVerified,
      isPIEqualPO: state.isPIEqualPO,
      policyOwnerPersonalInfo: state.policyOwnerPersonalInfo,
      updatePolicyOwnerPersonalInfo: state.updatePolicyOwnerPersonalInfo,
      isRemoteSelling: state.isRemoteSelling,
    }),
    shallow,
  );

  const ocrRef = useRef<OcrRef>(null);
  const [ocrValidationResult, setOcrValidationResult] =
    useState<OcrValidationResult>(OcrValidationResult.Match);
  const [ocrExtractionData, setOcrExtractionData] = useState<OcrData>({
    data: {},
    documentType: '',
    image: { base64: '', name: '' },
  });
  const [ocrValidationMismatchFields, setOcrValidationMismatchFields] =
    useState<MismatchFields>({});
  const [ocrValidationErrorDialogVisible, setOcrValidationErrorDialogVisible] =
    useState(false);
  const [
    updateSTPFieldConfirmationDialogVisible,
    setUpdateSTPFieldConfirmationDialogVisible,
  ] = useState(false);
  const [isVerifyingCustomer, setIsVerifyingCustomer] = useState(false);
  const [laVerificationVisible, setLaVerificationVisible] = useState(false);
  const [laCustomer, setLaCustomer] = useState<LACustomer>();

  const resolver = useEAppValidationResolver(policyOwnerPersonalDetailsSchema);
  const form = useForm<PolicyOwnerPersonalDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });
  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: { isValid },
    watch,
    clearErrors,
  } = form;

  useEffect(() => {
    if (['None'].includes(watch('idType'))) {
      clearErrors('idNumber');
      setValue('idNumber', '');
    }
  }, [clearErrors, setValue, watch]);

  const document = useWatch({
    name: 'document',
    control: control,
  });

  const primaryIdType = useWatch({
    name: 'primaryIdType',
    control: control,
  });

  const populateOcrData = useCallback(
    ({ data, documentType, image }: OcrData) => {
      if (data.firstName) {
        setValue(
          'firstName',
          capitalizeFirstLetterOfEachWord(data.firstName)?.trim(),
          {
            shouldTouch: true,
            shouldValidate: true,
          },
        );
      }
      if (data.lastName) {
        setValue(
          'lastName',
          capitalizeFirstLetterOfEachWord(data.lastName)?.trim(),
          {
            shouldTouch: true,
            shouldValidate: true,
          },
        );
      }
      if (data.middleName) {
        setValue(
          'middleName',
          capitalizeFirstLetterOfEachWord(data.middleName)?.trim(),
          {
            shouldTouch: true,
            shouldValidate: true,
          },
        );
      }
      if (data.expiryDate) {
        setValue('expiryDate', data.expiryDate, {
          shouldTouch: true,
          shouldValidate: true,
        });
      }
      setValue('primaryIdType', documentType, {
        shouldTouch: true,
        shouldValidate: true,
      });

      if (data.idNumber) {
        setValue('primaryIdNumber', data.idNumber, {
          shouldTouch: true,
          shouldValidate: true,
        });
      }

      if (['TN', 'SS'].includes(documentType)) {
        setValue('idType', documentType);
        if (data.idNumber) {
          setValue('idNumber', data.idNumber);
        }
      }

      setValue('document.frontImage', { ...image, fromOcr: true });
    },
    [setValue],
  );

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();

  useEffect(() => {
    if (
      primaryIdType &&
      optionList.data?.PRIMARY_ID_TYPE.options &&
      optionList.data?.PRIMARY_ID_TYPE.options.length
    ) {
      const primaryTypePicked = optionList.data.PRIMARY_ID_TYPE.options.find(
        e => e.value === primaryIdType,
      );
      if (primaryTypePicked?.dependencyTrigger === 'noPrimaryIdExpiry') {
        setValue('expiryDate', null);
        setValue('disabledExpiryDate', true);
        clearErrors('expiryDate');
      } else {
        setValue('disabledExpiryDate', false);
      }
    }
  }, [primaryIdType, optionList.data, setValue, clearErrors]);

  const { caseId, handleOcrImageDeletion, deleteDocument, isDeletingDocument } =
    useHandleOcrImageDeletion(partyId);

  const submit = useCallback(
    async (data: PolicyOwnerPersonalDetailsForm) => {
      await handleOcrImageDeletion(data.document.frontImage);
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [bottomSheetProps.bottomSheetRef, handleOcrImageDeletion, onDone],
  );
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
          loading={isDeletingDocument}
        />
      );
    },
    [handleSubmit, isValid, submit, isDeletingDocument],
  );

  const { reset } = useNavigation<NavigationProp<RootStackParamList>>();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );

  const ocrImage = useOcrImage(partyId);
  const lockOCRFields = !!ocrImage.thumbnail;

  const { mutateAsync: lookup } = useLACustomerLookup();

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const lookupExistingCustomer = useCallback(
    async (
      data: Pick<
        OcrData['data'],
        'firstName' | 'lastName' | 'dateOfBirth' | 'gender' | 'middleName'
      >,
    ) => {
      const customer = await lookup({
        dateOfBirth: data.dateOfBirth
          ? format(data.dateOfBirth, 'yyyy-MM-dd')
          : '',
        firstName: data.firstName || '',
        middleInitial: data.middleName || '',
        lastName: data.lastName || '',
        gender: data.gender === 'FEMALE' ? 'F' : 'M',
      });
      if (!customer.firstName) {
        throw new Error('Customer not found');
      }
      setLaCustomer(customer);
      setLaVerificationVisible(true);
    },
    [lookup],
  );

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const updateCustomer = useCallback(async () => {
    try {
      setAppLoading();
      await lookupExistingCustomer({
        firstName: getValues('firstName'),
        lastName: getValues('lastName'),
        middleName: getValues('middleName'),
        dateOfBirth: getValues('dateOfBirth'),
        gender:
          getValues('gender') === Gender.FEMALE
            ? 'FEMALE'
            : getValues('gender') === Gender.MALE
            ? 'MALE'
            : '',
      });
    } finally {
      setAppIdle();
    }
  }, [getValues, lookupExistingCustomer, setAppIdle, setAppLoading]);

  const ocrScanned = useRef(Boolean(ocrImage?.name));

  const handleOcrFinish = useCallback(
    async (
      data: OcrResult['extract'],
      documentType: string,
      image: {
        base64: string;
        name: string;
        thumbnail?: string;
      },
    ) => {
      bottomSheetProps.bottomSheetRef.current?.present();
      bottomSheetProps.bottomSheetRef.current?.expand();

      ocrScanned.current = true;

      const [result, mismatchFields] = validateOcr(data, {
        firstName: getValues('firstName'),
        lastName: getValues('lastName'),
        dateOfBirth: getValues('dateOfBirth') ?? null,
        gender: getValues('gender') as Gender,
      });

      const [newResult, newMismatchFields] =
        applyRegionalLogicToOcrValidationResult(
          documentType,
          result,
          mismatchFields,
        );

      const primaryIdTypeOptions =
        optionList.data?.PRIMARY_ID_TYPE.options ?? [];
      const isDocumentTypeValid =
        documentType !== 'OTHER' &&
        primaryIdTypeOptions.some(option => option.value === documentType);

      const mappedDocumentType = isDocumentTypeValid ? documentType : '';

      if (!isDocumentTypeValid || newResult !== OcrValidationResult.Match) {
        setOCRSuccess(false);
        setOcrExtractionData({ data, documentType: mappedDocumentType, image });
        setOcrValidationResult(newResult);
        setOcrValidationMismatchFields(newMismatchFields);
        setOcrValidationErrorDialogVisible(true);
        return;
      }

      try {
        setOCRSuccess(true);
        setIsVerifyingCustomer(true);
        const customer = await lookup({
          dateOfBirth: data.dateOfBirth
            ? format(data.dateOfBirth, 'yyyy-MM-dd')
            : '',
          firstName: data.firstName || '',
          middleInitial: data.middleName || '',
          lastName: data.lastName || '',
          gender: data.gender === 'FEMALE' ? 'F' : 'M',
        });
        if (!customer.firstName) {
          throw new Error('Customer not found');
        }
        setLaCustomer(customer);
        setLaVerificationVisible(true);
        setSTP(true);
      } finally {
        setIsVerifyingCustomer(false);
        populateOcrData({ data, documentType: mappedDocumentType, image });
      }
    },
    [
      bottomSheetProps.bottomSheetRef,
      getValues,
      lookup,
      populateOcrData,
      setOCRSuccess,
      setSTP,
      optionList,
    ],
  );

  const renderLADialog = useCallback(
    () => (
      <LAVerificationDialog
        visible={laVerificationVisible}
        data={{
          firstName: laCustomer?.firstName,
          lastName: laCustomer?.lastName,
          gender: laCustomer?.gender,
          dob: laCustomer?.birthDate,
        }}
        onAccept={() => {
          setLaVerificationVisible(false);
          if (laCustomer) {
            if (laCustomer.maritalStatus) {
              setValue('maritalStatus', laCustomer.maritalStatus, {
                shouldTouch: true,
                shouldValidate: true,
              });
            }
            const findPhoneCode = (code: string) =>
              optionList.data?.COUNTRY_CODE.options.find(o =>
                o.value.includes(code),
              )?.value;
            updatePolicyOwnerPersonalInfo({
              contactDetails: {
                ...policyOwnerPersonalInfo.contactDetails,
                email:
                  laCustomer.email ||
                  policyOwnerPersonalInfo.contactDetails.email,
                primaryCountryCode: laCustomer.mobileNo
                  ? findPhoneCode(laCustomer.mobileNo.slice(0, 2)) ||
                    policyOwnerPersonalInfo.contactDetails.primaryCountryCode
                  : policyOwnerPersonalInfo.contactDetails.primaryCountryCode,
                primaryMobile: laCustomer.mobileNo
                  ? laCustomer.mobileNo.slice(2)
                  : policyOwnerPersonalInfo.contactDetails.primaryMobile,
                homeCountryCode: laCustomer.phone1
                  ? findPhoneCode(laCustomer.phone1.slice(0, 2)) ||
                    policyOwnerPersonalInfo.contactDetails.homeCountryCode
                  : policyOwnerPersonalInfo.contactDetails.homeCountryCode,
                homeMobile: laCustomer.phone1
                  ? laCustomer.phone1.slice(2)
                  : policyOwnerPersonalInfo.contactDetails.homeMobile,
                officeCountryCode: laCustomer.phone2
                  ? findPhoneCode(laCustomer.phone2.slice(0, 2)) ||
                    policyOwnerPersonalInfo.contactDetails.officeCountryCode
                  : policyOwnerPersonalInfo.contactDetails.officeCountryCode,
                officeMobile: laCustomer.phone2
                  ? laCustomer.phone2.slice(2)
                  : policyOwnerPersonalInfo.contactDetails.officeMobile,
                faxCountryCode: laCustomer.fax
                  ? findPhoneCode(laCustomer.fax.slice(0, 2)) ||
                    policyOwnerPersonalInfo.contactDetails.faxCountryCode
                  : policyOwnerPersonalInfo.contactDetails.faxCountryCode,
                faxMobile: laCustomer.fax
                  ? laCustomer.fax.slice(2)
                  : policyOwnerPersonalInfo.contactDetails.faxMobile,
              },
              addressInfo: {
                ...policyOwnerPersonalInfo.addressInfo,
                addressLine1:
                  laCustomer.address1 ||
                  policyOwnerPersonalInfo.addressInfo.addressLine1,
                addressLine2:
                  laCustomer.address2 ||
                  policyOwnerPersonalInfo.addressInfo.addressLine2,
                addressLine3:
                  laCustomer.address1 ||
                  policyOwnerPersonalInfo.addressInfo.addressLine3,
                city:
                  laCustomer.address3 ||
                  policyOwnerPersonalInfo.addressInfo.city,
                province:
                  laCustomer.address4 ||
                  policyOwnerPersonalInfo.addressInfo.province,
                country:
                  optionList.data?.COUNTRY.options.find(
                    o => o.label === laCustomer.address5,
                  )?.value || policyOwnerPersonalInfo.addressInfo.country,
                postalCode:
                  laCustomer.zipCode ||
                  policyOwnerPersonalInfo.addressInfo.postalCode,
              },
              occupationDetails: {
                ...policyOwnerPersonalInfo.occupationDetails,
                occupationType:
                  laCustomer.occupationCode ||
                  policyOwnerPersonalInfo.occupationDetails.occupationType,
              },
              nationalityDetails: {
                ...policyOwnerPersonalInfo.nationalityDetails,
                nationality:
                  laCustomer.nationality ||
                  policyOwnerPersonalInfo.nationalityDetails.nationality,
                countryOfBirth:
                  laCustomer.countryCode ||
                  policyOwnerPersonalInfo.nationalityDetails.countryOfBirth,
                placeOfBirth:
                  laCustomer.birthPlace ||
                  policyOwnerPersonalInfo.nationalityDetails.placeOfBirth,
              },
              usTaxDeclaration: {
                ...policyOwnerPersonalInfo.usTaxDeclaration,
                isUSCitizen: laCustomer.fatca
                  ? laCustomer.fatca === '00'
                    ? 'no'
                    : 'yes'
                  : policyOwnerPersonalInfo.nationalityDetails.nationality,
              },
            });
          }
        }}
        onDeny={() => {
          setLaVerificationVisible(false);
        }}
      />
    ),
    [
      laCustomer,
      laVerificationVisible,
      optionList.data?.COUNTRY.options,
      optionList.data?.COUNTRY_CODE.options,
      policyOwnerPersonalInfo.addressInfo,
      policyOwnerPersonalInfo.contactDetails,
      policyOwnerPersonalInfo.nationalityDetails,
      policyOwnerPersonalInfo.occupationDetails,
      policyOwnerPersonalInfo.usTaxDeclaration,
      setValue,
      updatePolicyOwnerPersonalInfo,
    ],
  );

  const renderValidationDialog = useCallback(
    () => (
      <OcrValidationErrorDialog
        type={isPIEqualPO ? 'Insured' : 'PolicyOwner'}
        visible={ocrValidationErrorDialogVisible}
        fields={ocrValidationMismatchFields}
        result={ocrValidationResult}
        onRetake={() => {
          ocrRef?.current?.resetAndOpen();
          setOcrValidationErrorDialogVisible(false);
        }}
        onSkip={async () => {
          setSTP(false);
          ocrRef?.current?.reset();
          setOcrValidationErrorDialogVisible(false);
          updateCustomer();
        }}
        onUpdate={async () => {
          setSTP(false);
          populateOcrData(ocrExtractionData);
          setOcrValidationErrorDialogVisible(false);
          try {
            setAppLoading();
            if (isPIEqualPO) {
              await lookupExistingCustomer({
                ...ocrExtractionData.data,
                dateOfBirth: getValues('dateOfBirth'),
                gender:
                  getValues('gender') === Gender.FEMALE
                    ? 'FEMALE'
                    : getValues('gender') === Gender.MALE
                    ? 'MALE'
                    : '',
              });
            } else {
              await lookupExistingCustomer(ocrExtractionData.data);
            }
          } finally {
            setAppIdle();
          }
        }}
        onRecreateQuote={() => {
          setOcrValidationErrorDialogVisible(false);
          bottomSheetProps.bottomSheetRef.current?.close();
          clearActiveCase();
          reset({
            index: 0,
            routes: [{ name: 'Main' }, { name: 'CoverageDetailsScreen' }],
          });
        }}
      />
    ),
    [
      clearActiveCase,
      getValues,
      isPIEqualPO,
      lookupExistingCustomer,
      ocrExtractionData,
      ocrValidationErrorDialogVisible,
      ocrValidationMismatchFields,
      ocrValidationResult,
      populateOcrData,
      reset,
      setAppIdle,
      setAppLoading,
      setSTP,
      updateCustomer,
    ],
  );

  const renderConfirmationDialog = useCallback(
    () => (
      <UpdateSTPFieldConfirmationDialog
        visible={updateSTPFieldConfirmationDialogVisible}
        onAccept={() => {
          setSTP(false);
          setUpdateSTPFieldConfirmationDialogVisible(false);
        }}
        onCancel={() => {
          Keyboard.dismiss();
          setUpdateSTPFieldConfirmationDialogVisible(false);
        }}
      />
    ),
    [setSTP, updateSTPFieldConfirmationDialogVisible],
  );

  const ocrLogic = useMemo(
    () => ({
      ocrImage,
      onFinish: handleOcrFinish,
      ocrRef,
      ocrValidationMismatchFields,
      isVerifyingCustomer,
      isOcrSuccess,
      renderConfirmationDialog,
      renderLADialog,
      renderValidationDialog,
    }),
    [
      ocrImage,
      handleOcrFinish,
      isOcrSuccess,
      isVerifyingCustomer,
      ocrValidationMismatchFields,
      renderConfirmationDialog,
      renderLADialog,
      renderValidationDialog,
    ],
  );

  // TODO: OcrScreen seems not in use anymore, delete this when confirmed no impact
  // useEffect(() => {
  //   const sub = EventRegister.addEventListener(AppEvent.OcrRendered, () => {
  //     console.log(
  //       'PolicyOwnerPersonalDetails: received event',
  //       AppEvent.OcrRendered,
  //     );
  //     onDismiss();
  //   });
  //   return () => {
  //     EventRegister.removeEventListener(sub);
  //   };
  // }, [onDismiss]);

  const sections = useMemo(
    () => [
      {
        icon: <HeadShotSvg size={sizes[10]} />,
        title: t('livenessCheck:identityVerification.title'),
        badge: (
          <FormHeaderBadgeContainer
            backgroundColor={colors.palette.fwdYellow[100]}>
            <FireSvg />
            <SmallBody fontWeight={'medium'}>
              {t('livenessCheck:identityVerification.badgeLabel')}
            </SmallBody>
          </FormHeaderBadgeContainer>
        ),
        data: ['livenessCheck'],
      },
      {
        icon: <IconPersonalDetail />,
        title: t('eApp:personalDetails.title'),
        data: ['personalDetails'],
      },
    ],
    [colors.palette.fwdYellow, sizes, t],
  );

  const renderSectionHeader = useCallback(
    ({
      section,
    }: {
      section: {
        icon?: JSX.Element;
        title: string;
        badge?: JSX.Element;
      };
    }) => (
      <Row
        alignItems={'center'}
        px={space[isNarrowScreen ? 3 : 4]}
        backgroundColor={colors.background}>
        {section.icon}
        <Typography.H7
          fontWeight="bold"
          color={colors.primary}
          style={{ marginRight: space[1] }}>
          {section.title}
        </Typography.H7>
        {section.badge}
      </Row>
    ),
    [colors.background, colors.primary, isNarrowScreen, space],
  );

  const renderItem = useCallback(
    ({ item }: { item: string }) => {
      if (item === 'livenessCheck') {
        return (
          <PromptProvider>
            <LivenessCheckContent
              role={PartyRole.PROPOSER}
              isFaceMatched={isFaceMatched}
              onSetIsFaceMatched={isFaceMatched => {
                updatePolicyOwnerPersonalInfo({ isFaceMatched });
              }}
              isLivenessCheckVerified={isLivenessCheckVerified}
              onSetIsLivenessCheckVerified={isLivenessCheckVerified => {
                updatePolicyOwnerPersonalInfo({ isLivenessCheckVerified });
              }}
              ocrLogic={ocrLogic}
              caseId={caseId}
              partyId={policyOwnerPersonalInfo.id}
              disableFaceRecognition={isRemoteSelling}
            />
          </PromptProvider>
        );
      }

      return (
        <PolicyOwnerPersonalDetailsContent
          optionList={optionList}
          form={form}
          onShowUpdateStpFieldConfirmationDialog={() =>
            setUpdateSTPFieldConfirmationDialogVisible(true)
          }
          onUpdateCustomer={updateCustomer}
          ocrScanned={ocrScanned}
          lockOCRFields={lockOCRFields}
        />
      );
    },
    [
      caseId,
      form,
      ocrImage,
      ocrLogic,
      optionList,
      policyOwnerPersonalInfo.id,
      updateCustomer,
      isRemoteSelling,
      lockOCRFields,
    ],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <BottomSheetSectionList
            sections={sections}
            keyboardDismissMode="on-drag"
            renderSectionHeader={renderSectionHeader}
            renderItem={renderItem}
            stickySectionHeadersEnabled
          />
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
