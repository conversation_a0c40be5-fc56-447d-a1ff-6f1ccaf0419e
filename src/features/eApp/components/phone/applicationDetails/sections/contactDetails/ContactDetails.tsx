import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  H7,
  H8,
  PictogramIcon,
  Row,
  TextField,
  Box,
  Checkbox,
  Column,
} from 'cube-ui-components';
import { StyleSheet, Linking, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { useForm } from 'react-hook-form';

import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eApp/hooks/useEAppSnapPoint';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import FormFooter from '../FormFooter';
import SearchableDropdown from 'features/eApp/components/phone/common/SearchableDropdown';
import {
  ContactDetailsForm,
  contactDetailsSchema,
  maxEmailLength,
  maxMobileLength,
} from 'features/eApp/validations/applicationDetails/contactDetailsValidation';
import { useEAppValidationResolver } from 'features/eApp/hooks/useEAppValidationResolver';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { CountryCode } from 'types/optionList';
import {
  getCountryCodeDisplayedLabel,
  getCountryCodeValue,
  getOptionListLabel,
  PH_OPTION_LIST,
} from 'constants/optionList';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import useValidateEmail from 'features/lead/hooks/useValidateEmail';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useValidatePhone from 'features/lead/hooks/useValidatePhone';

export const ContactDetails = memo(function ContactDetails({
  onDismiss,
  value,
  onDone,
  isInstitution,
  autoAssignPB,
}: {
  onDismiss: () => void;
  value: ContactDetailsForm;
  onDone: (values: ContactDetailsForm) => void;
  isInstitution?: boolean;
  autoAssignPB?: boolean;
}) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const resolver = useEAppValidationResolver(contactDetailsSchema);
  const {
    setValue,
    getValues,
    reset,
    handleSubmit,
    control,
    formState: { isValid },
    setError,
  } = useForm<ContactDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();
  const snapPoints2 = useEAppSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints2);

  const itemKey = useEAppProgressBarStore(state => state.itemKey);

  const [isReusedFromPolicyOwner, setIsReusedFromPolicyOwner] = useState(
    value.sameAsPo,
  );
  const policyOwnerContactDetails = useEAppStore(
    state => state.policyOwnerPersonalInfo.contactDetails,
  );
  useEffect(() => {
    if (isReusedFromPolicyOwner) {
      reset({
        ...policyOwnerContactDetails,
        sameAsPo: true,
      });
    } else {
      reset({
        ...value,
        sameAsPo: false,
      });
    }
  }, [isReusedFromPolicyOwner, reset, policyOwnerContactDetails, value]);

  const { mutateAsync: onValidateEmail } = useValidateEmail();
  const { mutateAsync: onValidateMobileNumber } = useValidatePhone();

  const [isValidating, setValidating] = useState(false);

  const submit = useCallback(
    async (data: ContactDetailsForm) => {
      setValidating(true);
      try {
        const promises = [
          onValidateEmail(data.email),
          onValidateMobileNumber(
            `${data.primaryCountryCode?.split('-')?.[0]?.trim()}${
              data.primaryMobile
            }`,
          ),
        ];

        if (data.homeMobile) {
          promises.push(
            onValidateMobileNumber(
              `${data.homeCountryCode?.split('-')?.[0]?.trim()}${
                data.homeMobile
              }`,
            ),
          );
        } else {
          promises.push(Promise.resolve({ valid: true, message: '' }));
        }
        if (data.officeMobile) {
          promises.push(
            onValidateMobileNumber(
              `${data.officeCountryCode?.split('-')?.[0]?.trim()}${
                data.officeMobile
              }`,
            ),
          );
        } else {
          promises.push(Promise.resolve({ valid: true, message: '' }));
        }
        if (data.faxMobile) {
          promises.push(
            onValidateMobileNumber(
              `${data.faxCountryCode?.split('-')?.[0]?.trim()}${
                data.faxMobile
              }`,
            ),
          );
        } else {
          promises.push(Promise.resolve({ valid: true, message: '' }));
        }
        const [
          { valid: validEmail },
          { valid: validPrimaryMobile },
          { valid: validHomeMobile },
          { valid: validOfficeMobile },
          { valid: validFaxMobile },
        ] = await Promise.all(promises);

        if (!validEmail) {
          setError('email', {
            message: 'eApp:validation.error.invalidFormat',
          });
          setValue('email', getValues('email'), {
            shouldTouch: true,
          });
        }
        if (!validPrimaryMobile) {
          setError('primaryMobile', {
            message: 'eApp:validation.error.invalidFormat',
          });
          setValue('primaryMobile', getValues('primaryMobile'), {
            shouldTouch: true,
          });
        }
        if (!validHomeMobile) {
          setError('homeMobile', {
            message: 'eApp:validation.error.invalidFormat',
          });
          setValue('homeMobile', getValues('homeMobile'), {
            shouldTouch: true,
          });
        }
        if (!validOfficeMobile) {
          setError('officeMobile', {
            message: 'eApp:validation.error.invalidFormat',
          });
          setValue('officeMobile', getValues('officeMobile'), {
            shouldTouch: true,
          });
        }
        if (!validFaxMobile) {
          setError('faxMobile', {
            message: 'eApp:validation.error.invalidFormat',
          });
          setValue('faxMobile', getValues('faxMobile'), {
            shouldTouch: true,
          });
        }

        const valid =
          validEmail &&
          validPrimaryMobile &&
          validHomeMobile &&
          validOfficeMobile &&
          validFaxMobile;
        if (valid) {
          onDone(data);
          bottomSheetProps.bottomSheetRef.current?.close();
        }
      } finally {
        setValidating(false);
      }
    },
    [onDone, setError, setValue, getValues],
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          disabled={!isValid || isValidating}
          onPress={handleSubmit(submit)}
          loading={isValidating}
        />
      );
    },
    [handleSubmit, isValid, submit, isValidating],
  );

  const showCheckBox = useMemo(() => {
    return itemKey !== 'policyOwner';
  }, [itemKey]);

  const secondaryContactVisible = useMemo(() => {
    if (
      itemKey === 'primaryBeneficiary' ||
      itemKey === 'secondaryBeneficiary'
    ) {
      return !isInstitution;
    }
    return true;
  }, [itemKey, isInstitution]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  useEffect(() => {
    if (autoAssignPB) {
      setIsReusedFromPolicyOwner(true);
    }
  }, [autoAssignPB]);

  const renderSecondaryContact = () => {
    return (
      <>
        <Box h={space[6]} />
        <H8 fontWeight="bold" color={colors.primary}>
          {t('eApp:contactDetails.secondaryContact')}
        </H8>
        <Row mt={space[5]}>
          <Input
            disabled={isReusedFromPolicyOwner || isFetchingOptionList}
            control={control}
            as={SearchableDropdown<CountryCode, string>}
            name="homeCountryCode"
            label={t('eApp:contactDetails.countryCode')}
            data={optionList?.COUNTRY_CODE.options ?? []}
            getItemValue={getCountryCodeValue}
            getItemLabel={getOptionListLabel}
            getDisplayedLabel={getCountryCodeDisplayedLabel}
            style={styles.countryCode}
            modalTitle={t('eApp:contactDetails.countryCode')}
            searchable
          />
          <Input
            disabled={isReusedFromPolicyOwner}
            control={control}
            as={TextField}
            name="homeMobile"
            maxLength={maxMobileLength}
            label={t('eApp:contactDetails.homePhone')}
            style={styles.phoneNumber}
            keyboardType="number-pad"
            returnKeyType="done"
          />
        </Row>
        <Row mt={space[5]}>
          <Input
            disabled={isReusedFromPolicyOwner || isFetchingOptionList}
            control={control}
            as={SearchableDropdown<CountryCode, string>}
            name="officeCountryCode"
            label={t('eApp:contactDetails.countryCode')}
            data={optionList?.COUNTRY_CODE.options ?? []}
            getItemValue={getCountryCodeValue}
            getItemLabel={getOptionListLabel}
            getDisplayedLabel={getCountryCodeDisplayedLabel}
            style={styles.countryCode}
            modalTitle={t('eApp:contactDetails.countryCode')}
            searchable
          />
          <Input
            disabled={isReusedFromPolicyOwner}
            control={control}
            as={TextField}
            name="officeMobile"
            maxLength={maxMobileLength}
            label={t('eApp:contactDetails.officePhone')}
            style={styles.phoneNumber}
            keyboardType="number-pad"
            returnKeyType="done"
          />
        </Row>
        <Row mt={space[5]} mb={space[4]}>
          <Input
            disabled={isReusedFromPolicyOwner || isFetchingOptionList}
            control={control}
            as={SearchableDropdown<CountryCode, string>}
            name="faxCountryCode"
            label={t('eApp:contactDetails.countryCode')}
            data={optionList?.COUNTRY_CODE.options ?? []}
            getItemValue={getCountryCodeValue}
            getItemLabel={getOptionListLabel}
            getDisplayedLabel={getCountryCodeDisplayedLabel}
            style={styles.countryCode}
            modalTitle={t('eApp:contactDetails.countryCode')}
            searchable
          />
          <Input
            disabled={isReusedFromPolicyOwner}
            control={control}
            as={TextField}
            name="faxMobile"
            label={t('eApp:contactDetails.faxPhone')}
            style={styles.phoneNumber}
            keyboardType="number-pad"
            maxLength={maxMobileLength}
            returnKeyType="done"
          />
        </Row>
      </>
    );
  };

  const renderHeader = () => {
    return (
      <Row alignItems="center" px={space[isNarrowScreen ? 3 : 4]}>
        <PictogramIcon.Call2 size={sizes[10]} />
        <H7 fontWeight="bold" color={colors.primary}>
          {t('eApp:contactDetails.title')}
        </H7>
      </Row>
    );
  };

  const renderContent = () => {
    return (
      <BottomSheetScrollView
        keyboardDismissMode="on-drag"
        style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
        <Column mt={space[5]}>
          {showCheckBox && (
            <Input
              control={control}
              as={Checkbox}
              name="sameAsPo"
              label="Information is same as the policy owner"
              style={{ marginBottom: 22, opacity: autoAssignPB ? 0.5 : 1 }}
              onChange={checked => {
                setIsReusedFromPolicyOwner(checked);
              }}
              disabled={autoAssignPB}
            />
          )}
        </Column>
        <Row>
          <Input
            disabled={isReusedFromPolicyOwner || isFetchingOptionList}
            control={control}
            as={SearchableDropdown<CountryCode, string>}
            name="primaryCountryCode"
            label={t('eApp:contactDetails.countryCode')}
            data={optionList?.COUNTRY_CODE.options ?? []}
            getItemValue={getCountryCodeValue}
            getItemLabel={getOptionListLabel}
            getDisplayedLabel={getCountryCodeDisplayedLabel}
            style={styles.countryCode}
            modalTitle={t('eApp:contactDetails.countryCode')}
            searchable
          />
          <Input
            disabled={isReusedFromPolicyOwner}
            control={control}
            as={TextField}
            name="primaryMobile"
            label={t('eApp:contactDetails.mobileNumber')}
            style={styles.phoneNumber}
            maxLength={maxMobileLength}
            keyboardType={'numeric'}
            returnKeyType="done"
          />
        </Row>
        <Input
          disabled={isReusedFromPolicyOwner}
          control={control}
          as={TextField}
          name="email"
          label={t('eApp:contactDetails.email')}
          style={styles.mt20}
          maxLength={maxEmailLength}
          keyboardType="email-address"
        />
        {itemKey === 'policyOwner' && (
          <>
            <Input
              control={control}
              as={
                SearchableDropdown<
                  (typeof PH_OPTION_LIST.POLICY_DELIVERY_MODES)[0],
                  string
                >
              }
              name="deliveryMode"
              label={t('eApp:contactDetails.policy')}
              data={PH_OPTION_LIST.POLICY_DELIVERY_MODES}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              style={styles.mt20}
              modalTitle={t('eApp:contactDetails.policy')}
              disabled
            />
            <Box mt={space[3]}>
              <H8>
                {t('eApp:contactDetails.note1')}
                <H8
                  fontWeight="bold"
                  color={colors.primary}
                  onPress={() => Linking.openURL('tel:63288888388')}>
                  {t('eApp:contactDetails.note.phone')}
                </H8>
                ,{' '}
                <H8
                  fontWeight="bold"
                  color={colors.primary}
                  style={{ textDecorationLine: 'underline' }}
                  onPress={() =>
                    Linking.openURL(
                      `mailto:${t('eApp:contactDetails.note.email')}`,
                    )
                  }>
                  {t('eApp:contactDetails.note.email')}
                </H8>
                {t('eApp:contactDetails.note2')}
              </H8>
            </Box>
          </>
        )}
        {secondaryContactVisible && renderSecondaryContact()}
        {!secondaryContactVisible && <Box h={space[6]} />}
        <BottomSheetFooterSpace />
      </BottomSheetScrollView>
    );
  };

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={secondaryContactVisible ? 1 : 0}
          onDismiss={onDismiss}
          snapPoints={secondaryContactVisible ? snapPoints : animatedSnapPoints}
          handleHeight={
            !secondaryContactVisible ? animatedHandleHeight : undefined
          }
          contentHeight={
            !secondaryContactVisible ? animatedContentHeight : undefined
          }
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          {!secondaryContactVisible && (
            <View onLayout={handleContentLayout}>
              {renderHeader()}
              {renderContent()}
            </View>
          )}
          {secondaryContactVisible && renderHeader()}
          {secondaryContactVisible && renderContent()}
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const styles = StyleSheet.create({
  contentContainer: {
    paddingTop: 12,
  },
  scrollView: {
    height: 600,
  },
  countryCode: {
    flex: 1,
    marginRight: 10,
  },
  phoneNumber: {
    flex: 1.5,
  },
  mt20: {
    marginTop: 20,
  },
});
