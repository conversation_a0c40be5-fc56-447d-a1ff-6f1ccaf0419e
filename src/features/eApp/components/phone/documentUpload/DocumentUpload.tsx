import { useTheme } from '@emotion/react';
import { Box, H6, Label, LargeLabel } from 'cube-ui-components';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import FormAction from '../common/FormAction';
import { useEAppProgressBarStore } from 'features/eApp/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import {
  Document,
  DocumentList,
  DocumentStorage,
  DocumentPartyType,
  RenewalPaymentOption,
  UPLOAD_STATUS,
  ImageType,
} from 'features/eApp/types/documentTypes';
import { PartyType } from 'types/party';
import { UploadButton } from './UploadButton';
import { shallow } from 'zustand/shallow';
import { DocumentType } from 'types/document';
import {
  mapToCustomerType,
  mapToDocumentType,
  showDesWithPaymentOptions,
} from 'features/eApp/utils/documentUtils';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { filterIrrevocableBeneficiaries } from 'features/eApp/utils/filterIrrevocableBeneficiaries';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useIncompleteSync } from 'features/eApp/hooks/useIncompleteSync';
import { country } from 'utils/context';
import useBoundStore from 'hooks/useBoundStore';
import { useDeleteDocument } from 'features/eApp/hooks/useParty';

const DocumentUpload = () => {
  const { space, sizes, colors } = useTheme();
  const { t } = useTranslation(['eApp']);

  const [isAllowShowModal, setIsAllowShowModal] = useState(true);

  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const itemKey = useEAppProgressBarStore(state => state.itemKey);
  const groups = useEAppProgressBarStore(state => state.groups);
  const arePreviousStepsComplete = useMemo(() => {
    let allComplete = true;
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      if (group.routeKey === 'documentUpload') {
        break;
      } else {
        allComplete &&= Boolean(group.completed);
      }
    }
    return allComplete;
  }, [groups]);

  const {
    policyOwnerPersonalInfo,
    insuredPersonalInfo,
    beneficiariesPersonalInfo,
    payorPersonalInfo,
    beneficialOwnerPersonalInfo,
    updatePolicyOwnerPersonalInfo,
    updateInsuredPersonalInfo,
    updateBeneficiaryPersonalInfo,
    hasPayor,
    updatePayorPersonalInfo,
    hasBeneficialOwner,
    beneficialOwnerIsUSCitizen,
    updateBeneficialOwnerPersonalInfo,
    hasBankDetails,
    renewalPaymentSetup,
    isPIEqualPO,
    bankDetails,
  } = useEAppStore(
    state => ({
      policyOwnerPersonalInfo: state.policyOwnerPersonalInfo,
      insuredPersonalInfo: state.insuredPersonalInfo,
      beneficiariesPersonalInfo: state.beneficiariesPersonalInfo,
      payorPersonalInfo: state.payorPersonalInfo,
      beneficialOwnerPersonalInfo: state.beneficialOwnerPersonalInfo,
      updatePolicyOwnerPersonalInfo: state.updatePolicyOwnerPersonalInfo,
      updateInsuredPersonalInfo: state.updateInsuredPersonalInfo,
      updateBeneficiaryPersonalInfo: state.updateBeneficiaryPersonalInfo,
      hasPayor: state.hasPayor,
      hasBankDetails: state.hasBankDetails,
      updatePayorPersonalInfo: state.updatePayorPersonalInfo,
      hasBeneficialOwner: state.hasBeneficialOwner,
      beneficialOwnerIsUSCitizen:
        state.beneficialOwnerPersonalInfo.beneficialOwnerIsUSCitizen,
      updateBeneficialOwnerPersonalInfo:
        state.updateBeneficialOwnerPersonalInfo,
      renewalPaymentSetup: state.renewalPaymentSetup,
      bankDetails: state.bankDetails,
      isPIEqualPO: state.isPIEqualPO,
    }),
    shallow,
  );

  const paymentOption = useMemo(() => {
    const { paymentMethod } = renewalPaymentSetup;
    if (hasBankDetails) {
      return RenewalPaymentOption.BANK_DETAILS;
    }
    switch (paymentMethod) {
      case 'aca':
        return RenewalPaymentOption.ACA;
      case 'adda':
        return RenewalPaymentOption.ADDA;
      default:
        return RenewalPaymentOption.OTHER;
    }
  }, [renewalPaymentSetup, hasBankDetails]);

  const policyOwner = policyOwnerPersonalInfo.personalDetails;
  const authorizedRepresentative =
    policyOwnerPersonalInfo.authorizedRepresentativeDetails;
  const insuredPersonal = insuredPersonalInfo.personalDetails;
  const beneficiariesPersonals = beneficiariesPersonalInfo;
  const payorPersonal = payorPersonalInfo.personalDetails;
  const beneficialOwnerPersonal = beneficialOwnerPersonalInfo.personalDetails;

  const applicationNum = useEAppStore(state => state.applicationNum);

  const { data: optionList } = useGetOptionList();

  const hasAdditionalDocumentForPolicyOwner = useMemo(() => {
    if (hasBankDetails) {
      return true;
    }

    return (
      paymentOption === RenewalPaymentOption.ACA ||
      paymentOption === RenewalPaymentOption.ADDA ||
      paymentOption === RenewalPaymentOption.BANK_DETAILS
    );
  }, [hasBankDetails, paymentOption]);

  const dataDocument = useMemo(() => {
    const createDocument = (
      partyId = '',
      title: string,
      document: DocumentStorage,
      partyType: DocumentPartyType,
      description: string,
      hasAdditionalDocument: boolean,
      documentType: string,
    ): Document => {
      if (document.isEntityBeneficialOwner) {
        return {
          partyId,
          title,
          customerType: PartyType.ENTITY,
          partyType: DocumentPartyType.BENEFICIAL_OWNER,
          description,
          certOnBO: {
            listImagesUpload: [
              {
                id: document.certOnBO.name || '',
                base64: document.certOnBO.base64 || '',
                name: document.certOnBO.name || '',
                thumbnail: document.certOnBO.thumbnail || '',
                status: UPLOAD_STATUS.Done,
              },
            ].filter(item => item.thumbnail && item.name),
            multiple: false,
          },
          irswForm: {
            listImagesUpload: [
              {
                id: document.irswForm?.name || '',
                base64: document.irswForm?.base64 || '',
                name: document.irswForm?.name || '',
                thumbnail: document.irswForm?.thumbnail || '',
                status: UPLOAD_STATUS.Done,
              },
            ].filter(item => item.thumbnail && item.name),
            multiple: false,
          },

          documentType,
        };
      } else {
        return {
          partyId,
          title,
          customerType: PartyType.INDIVIDUAL,
          partyType: partyType,
          description,
          frontOfIdList: {
            listImagesUpload: [
              {
                id: document.frontImage.name || '',
                base64: document.frontImage.base64 || '',
                name: document.frontImage.name || '',
                thumbnail: document.frontImage.thumbnail || '',
                status: UPLOAD_STATUS.Done,
                fromOcr: document.frontImage.fromOcr,
              },
            ].filter(item => item.thumbnail && item.name),
            multiple: false,
          },
          backOfIdList: {
            listImagesUpload: [
              {
                id: document.backImage.name || '',
                base64: document.backImage.base64 || '',
                name: document.backImage.name || '',
                thumbnail: document.backImage.thumbnail || '',
                status: UPLOAD_STATUS.Done,
              },
            ].filter(item => item.thumbnail && item.name),
            multiple: false,
          },
          additionalDocumentList: {
            listImagesUpload:
              document.additionalDocument
                ?.map(item => ({
                  id: item.name || '',
                  base64: item.base64 || '',
                  name: item.name || '',
                  thumbnail: item.thumbnail || '',
                  status: UPLOAD_STATUS.Done,
                }))
                .filter(item => item.thumbnail && item.name) || [],
            multiple: true,
          },
          hasAdditionalDocument: hasAdditionalDocument,
          documentType,
        };
      }
    };

    const findDocumentTypeLabel = (type?: string) =>
      optionList?.PRIMARY_ID_TYPE.options.find(opt => opt.value === type)
        ?.label || '';

    const dataList: Array<Document> =
      policyOwner.customerType === PartyType.ENTITY
        ? [
          createDocument(
            policyOwnerPersonalInfo.id,
            t('eApp:documentUpload.authorizedRepresentative'),
            { isEntityBeneficialOwner: false, ...policyOwner.document },
            DocumentPartyType.POLICY_OWNER,
            `${authorizedRepresentative.firstName} ${authorizedRepresentative.middleName} ${authorizedRepresentative.lastName}`,
            hasAdditionalDocumentForPolicyOwner,
            'ID',
          ),
        ]
        : [
          createDocument(
            policyOwnerPersonalInfo.id,
            t('eApp:documentUpload.policyOwner'),
            { ...policyOwner.document, isEntityBeneficialOwner: false },
            DocumentPartyType.POLICY_OWNER,
            `${policyOwner.firstName} ${policyOwner.middleName} ${policyOwner.lastName}`,
            hasAdditionalDocumentForPolicyOwner,
            findDocumentTypeLabel(policyOwner.primaryIdType),
          ),
        ];

    if (!isPIEqualPO) {
      dataList.push(
        createDocument(
          insuredPersonalInfo.id,
          t('eApp:documentUpload.insured'),
          { ...insuredPersonal.document, isEntityBeneficialOwner: false },
          DocumentPartyType.INSURED,
          `${insuredPersonal.firstName} ${insuredPersonal.middleName} ${insuredPersonal.lastName}`,
          false,
          findDocumentTypeLabel(insuredPersonal.primaryIdType),
        ),
      );
    }
    if (hasBeneficialOwner) {
      if (policyOwner.customerType === PartyType.ENTITY) {
        dataList.push(
          createDocument(
            beneficialOwnerPersonalInfo.id,
            t('eApp:documentUpload.beneficialOwner'),
            {
              isEntityBeneficialOwner: true,
              certOnBO: beneficialOwnerPersonal.document.certOnBO,
              irswForm: beneficialOwnerPersonal.document.irswForm,
            },
            DocumentPartyType.BENEFICIAL_OWNER,
            beneficialOwnerPersonal.firstName +
            beneficialOwnerPersonal.middleName +
            beneficialOwnerPersonal.lastName,
            false,
            findDocumentTypeLabel(beneficialOwnerPersonal.primaryIdType),
          ),
        );
      } else {
        dataList.push(
          createDocument(
            beneficialOwnerPersonalInfo.id,
            t('eApp:documentUpload.beneficialOwner'),
            {
              ...beneficialOwnerPersonal.document,
              isEntityBeneficialOwner: false,
            },
            DocumentPartyType.BENEFICIAL_OWNER,
            beneficialOwnerPersonal.firstName +
            beneficialOwnerPersonal.middleName +
            beneficialOwnerPersonal.lastName,
            false,
            findDocumentTypeLabel(beneficialOwnerPersonal.primaryIdType),
          ),
        );
      }
    }

    if (hasPayor) {
      dataList.push(
        createDocument(
          payorPersonalInfo.id,
          t('eApp:documentUpload.partyPayor'),
          { ...payorPersonal.document, isEntityBeneficialOwner: false },
          DocumentPartyType.PARTY_PAYOR,
          payorPersonal.firstName +
          payorPersonal.middleName +
          payorPersonal.lastName,
          false,
          findDocumentTypeLabel(payorPersonal.primaryIdType),
        ),
      );
    }

    const filterBeneficiariesIrrevocable = filterIrrevocableBeneficiaries(
      beneficiariesPersonals,
    );

    filterBeneficiariesIrrevocable
      .filter(
        item => item.personalDetails.age && item.personalDetails.age >= 18,
      )
      .map((item, index) => {
        const beneficiariesPersonalItem = item.personalDetails;
        if (beneficiariesPersonalItem !== undefined) {
          const { firstName, middleName, lastName } = beneficiariesPersonalItem;
          let description = '';
          if (firstName) {
            description += firstName;
          } else if (middleName) {
            description += ' ' + middleName;
          } else if (lastName) {
            description += ' ' + lastName;
          }
          dataList.push(
            createDocument(
              item.id,
              `${t('eApp:documentUpload.beneficiary')} ${index + 1}`,
              {
                ...beneficiariesPersonalItem.document,
                isEntityBeneficialOwner: false,
              },
              DocumentPartyType.BENEFICIARY,
              description,
              false,
              '',
            ),
          );
        }
      });

    filterBeneficiariesIrrevocable
      .filter(item => item.personalDetails.age && item.personalDetails.age < 18)
      .map((item, index) => {
        const beneficiariesPersonalItem = item.personalDetails;
        if (beneficiariesPersonalItem !== undefined) {
          dataList.push(
            createDocument(
              item.id,
              `${t('eApp:documentUpload.trustee')} ${index + 1}`,
              {
                ...beneficiariesPersonalItem.document,
                isEntityBeneficialOwner: false,
              },
              DocumentPartyType.TRUSTEE,
              beneficiariesPersonalItem.nameOfTrustee
                ? beneficiariesPersonalItem.nameOfTrustee
                : '',
              false,
              '',
            ),
          );
        }
      });

    return dataList;
  }, [
    policyOwner.customerType,
    policyOwner.document,
    policyOwner.firstName,
    policyOwner.middleName,
    policyOwner.lastName,
    policyOwner.primaryIdType,
    policyOwnerPersonalInfo.id,
    t,
    authorizedRepresentative.firstName,
    authorizedRepresentative.middleName,
    authorizedRepresentative.lastName,
    hasAdditionalDocumentForPolicyOwner,
    isPIEqualPO,
    hasBeneficialOwner,
    hasPayor,
    beneficiariesPersonals,
    optionList?.PRIMARY_ID_TYPE.options,
    insuredPersonalInfo.id,
    insuredPersonal.document,
    insuredPersonal.firstName,
    insuredPersonal.middleName,
    insuredPersonal.lastName,
    insuredPersonal.primaryIdType,
    beneficialOwnerPersonalInfo.id,
    beneficialOwnerPersonal.document,
    beneficialOwnerPersonal.firstName,
    beneficialOwnerPersonal.middleName,
    beneficialOwnerPersonal.lastName,
    beneficialOwnerPersonal.primaryIdType,
    payorPersonalInfo.id,
    payorPersonal.document,
    payorPersonal.firstName,
    payorPersonal.middleName,
    payorPersonal.lastName,
    payorPersonal.primaryIdType,
  ]);

  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    let isValidDocument = true;

    dataDocument.map(item => {
      if (
        item.partyType === DocumentPartyType.BENEFICIAL_OWNER &&
        item.customerType === PartyType.ENTITY
      ) {
        const certOnBO = item.certOnBO;
        const irswForm = item.irswForm;
        const isValidEachDocument =
          (hasBeneficialOwner
            ? certOnBO.listImagesUpload.filter(
              item => item.thumbnail && item.name,
            ).length >= 1
            : true) &&
          (beneficialOwnerIsUSCitizen
            ? irswForm.listImagesUpload.filter(
              item => item.thumbnail && item.name,
            ).length >= 1
            : true);
        if (!isValidEachDocument) {
          isValidDocument = false;
          return;
        }
      } else {
        const backOfIdList = item.backOfIdList;
        const frontOfIdList = item.frontOfIdList;
        const additionalDocumentList = item.additionalDocumentList;
        let isValidEachDocument =
          backOfIdList.listImagesUpload.filter(
            item => item.thumbnail && item.name,
          ).length >= 1 &&
          frontOfIdList.listImagesUpload.filter(
            item => item.thumbnail && item.name,
          ).length >= 1;
        if (item.hasAdditionalDocument) {
          isValidEachDocument =
            isValidEachDocument &&
            additionalDocumentList.listImagesUpload.filter(
              item => item.thumbnail && item.name,
            ).length >= 1;
        }
        if (!isValidEachDocument) {
          isValidDocument = false;
          return;
        }
      }
    });

    setIsValid(isValidDocument);
  }, [dataDocument, beneficialOwnerIsUSCitizen]);

  const createDocumentStore = useCallback(
    (document: Document, additionalDocumentRequired: boolean) => {
      if (
        document.partyType === DocumentPartyType.BENEFICIAL_OWNER &&
        document.customerType === PartyType.ENTITY
      ) {
        const documentStore: {
          certOnBO: {
            base64: string;
            name: string;
            thumbnail: string;
          };
          irswForm: {
            base64: string;
            name: string;
            thumbnail: string;
          };
        } = {
          certOnBO: {
            base64:
              document.certOnBO.listImagesUpload.length > 0
                ? document.certOnBO.listImagesUpload[0].base64
                : '',
            name:
              document.certOnBO.listImagesUpload.length > 0
                ? document.certOnBO.listImagesUpload[0].name
                : '',
            thumbnail:
              document.certOnBO.listImagesUpload.length > 0
                ? document.certOnBO.listImagesUpload[0].thumbnail || ''
                : '',
          },
          irswForm: {
            base64:
              document.irswForm.listImagesUpload.length > 0
                ? document.irswForm.listImagesUpload[0].base64
                : '',
            name:
              document.irswForm.listImagesUpload.length > 0
                ? document.irswForm.listImagesUpload[0].name
                : '',
            thumbnail:
              document.irswForm.listImagesUpload.length > 0
                ? document.irswForm.listImagesUpload[0].thumbnail || ''
                : '',
          },
        };
        return documentStore;
      } else {
        const documentStore: {
          frontImage: {
            base64: string;
            name: string;
            thumbnail: string;
            fromOcr?: boolean;
          };
          backImage: {
            base64: string;
            name: string;
            thumbnail: string;
          };
          additionalDocument?: {
            base64: string;
            name: string;
            thumbnail: string;
          }[];
        } = {
          frontImage: {
            base64:
              document.frontOfIdList.listImagesUpload.length > 0
                ? document.frontOfIdList.listImagesUpload[0].base64
                : '',
            name:
              document.frontOfIdList.listImagesUpload.length > 0
                ? document.frontOfIdList.listImagesUpload[0].name
                : '',
            thumbnail:
              document.frontOfIdList.listImagesUpload.length > 0
                ? document.frontOfIdList.listImagesUpload[0].thumbnail || ''
                : '',
            fromOcr:
              document.frontOfIdList.listImagesUpload.length > 0
                ? document.frontOfIdList.listImagesUpload[0].fromOcr ||
                undefined
                : undefined,
          },
          backImage: {
            base64:
              document.backOfIdList.listImagesUpload.length > 0
                ? document.backOfIdList.listImagesUpload[0].base64
                : '',
            name:
              document.backOfIdList.listImagesUpload.length > 0
                ? document.backOfIdList.listImagesUpload[0].name
                : '',
            thumbnail:
              document.backOfIdList.listImagesUpload.length > 0
                ? document.backOfIdList.listImagesUpload[0].thumbnail || ''
                : '',
          },
        };
        if (additionalDocumentRequired) {
          documentStore.additionalDocument =
            document.additionalDocumentList.listImagesUpload.map(itemBank => ({
              base64: itemBank.base64,
              name: itemBank.name,
              thumbnail: itemBank.thumbnail || '',
            }));
        }
        return documentStore;
      }
    },
    [],
  );

  const updateDocument = useCallback(
    (document: Document) => {
      const beneficiary = beneficiariesPersonals.find(
        b => b.id === document.partyId,
      );
      switch (document.partyType) {
        case DocumentPartyType.POLICY_OWNER:
          if (document.frontOfIdList.listImagesUpload.length === 0) {
            updatePolicyOwnerPersonalInfo({
              isSTP: false,
              personalDetails: {
                ...policyOwner,
                document: createDocumentStore(
                  document,
                  hasAdditionalDocumentForPolicyOwner,
                ),
              },
            });
          } else {
            updatePolicyOwnerPersonalInfo({
              personalDetails: {
                ...policyOwner,
                document: createDocumentStore(
                  document,
                  hasAdditionalDocumentForPolicyOwner,
                ),
              },
            });
          }
          break;
        case DocumentPartyType.INSURED:
          if (document.frontOfIdList.listImagesUpload.length === 0) {
            updateInsuredPersonalInfo({
              isSTP: false,
              personalDetails: {
                ...insuredPersonal,
                document: createDocumentStore(document, false),
              },
            });
          } else {
            updateInsuredPersonalInfo({
              personalDetails: {
                ...insuredPersonal,
                document: createDocumentStore(document, false),
              },
            });
          }
          break;
        case DocumentPartyType.PARTY_PAYOR:
          updatePayorPersonalInfo({
            personalDetails: {
              ...payorPersonal,
              document: createDocumentStore(document, false),
            },
          });
          break;
        case DocumentPartyType.BENEFICIAL_OWNER:
          updateBeneficialOwnerPersonalInfo({
            personalDetails: {
              ...beneficialOwnerPersonal,
              document: createDocumentStore(document, false),
            },
          });
          break;
        case DocumentPartyType.BENEFICIARY:
        case DocumentPartyType.TRUSTEE:
          if (beneficiary) {
            const data = {
              personalDetails: {
                ...beneficiary.personalDetails,
                document: createDocumentStore(document, false),
              },
              index: beneficiary.index,
            };
            updateBeneficiaryPersonalInfo(data);
          }
          break;
      }
    },
    [
      policyOwner,
      insuredPersonal,
      payorPersonal,
      beneficialOwnerPersonal,
      beneficiariesPersonals,
    ],
  );

  const updateDataUpload = useCallback(
    (
      id: string,
      document: DocumentList,
      base64: string,
      name: string,
      filePath: string,
      status: UPLOAD_STATUS,
      fromOcr?: boolean,
    ) => {
      const indexDocument = document.listImagesUpload.findIndex(
        item => item.id === id,
      );
      switch (status) {
        case UPLOAD_STATUS.Uploading:
        case UPLOAD_STATUS.Done:
          if (indexDocument > -1) {
            document.listImagesUpload[indexDocument] = {
              id: id,
              base64: base64,
              name: name,
              thumbnail: filePath,
              filePath,
              status,
              fromOcr,
            };
          } else {
            document.listImagesUpload.push({
              id: id,
              base64: base64,
              name: name,
              thumbnail: filePath,
              filePath,
              status,
              fromOcr,
            });
          }
          break;
        case UPLOAD_STATUS.Failed:
          removeDataUpload(document, id);
          return;
      }
    },
    [],
  );

  const updateDocumentImage = useCallback(
    (
      id: string,
      partyId: string,
      typeImage: ImageType,
      base64: string,
      name: string,
      filePath: string,
      status: UPLOAD_STATUS,
      fromOcr?: boolean,
    ) => {
      const indexDocument = dataDocument.findIndex(
        item => item.partyId === partyId,
      );
      const dataDocumentSelected = dataDocument[indexDocument];

      if (
        dataDocumentSelected.partyType === DocumentPartyType.BENEFICIAL_OWNER &&
        dataDocumentSelected.customerType === PartyType.ENTITY
      ) {
        switch (typeImage) {
          case ImageType.CERT_ON_BO:
            updateDataUpload(
              id,
              dataDocumentSelected.certOnBO,
              base64,
              name,
              filePath,
              status,
            );
            break;
          case ImageType.IRSW_FORM:
            updateDataUpload(
              id,
              dataDocumentSelected.irswForm,
              base64,
              name,
              filePath,
              status,
            );
            break;
        }
      } else {
        switch (typeImage) {
          case ImageType.FRONT_IMAGE:
            updateDataUpload(
              id,
              dataDocumentSelected.frontOfIdList,
              base64,
              name,
              filePath,
              status,
              fromOcr,
            );
            break;
          case ImageType.BACK_IMAGE:
            updateDataUpload(
              id,
              dataDocumentSelected.backOfIdList,
              base64,
              name,
              filePath,
              status,
            );
            break;
          case ImageType.ADDITIONAL_DOCUMENT:
            updateDataUpload(
              id,
              dataDocumentSelected.additionalDocumentList,
              base64,
              name,
              filePath,
              status,
            );
            break;
        }
      }
      if (status === UPLOAD_STATUS.Done || status === UPLOAD_STATUS.Failed) {
        updateDocument(dataDocumentSelected);
      }
    },
    [dataDocument],
  );

  const removeDataUpload = useCallback((document: DocumentList, id: string) => {
    const indexDocument = document.listImagesUpload.findIndex(
      item => item.id === id,
    );
    document.listImagesUpload.splice(indexDocument, 1);
  }, []);

  const deleteDocumentImage = useCallback(
    (partyId: string, typeImage: string, id: string) => {
      const newDataUpload = [...dataDocument];
      const indexDocument = dataDocument.findIndex(
        item => item.partyId === partyId,
      );

      const dataUploadSelected = newDataUpload[indexDocument];

      if (
        dataUploadSelected.partyType === DocumentPartyType.BENEFICIAL_OWNER &&
        dataUploadSelected.customerType === PartyType.ENTITY
      ) {
        switch (typeImage) {
          case ImageType.CERT_ON_BO:
            removeDataUpload(dataUploadSelected.certOnBO, id);
            break;
          case ImageType.IRSW_FORM:
            removeDataUpload(dataUploadSelected.irswForm, id);
            break;
        }
      } else {
        switch (typeImage) {
          case ImageType.FRONT_IMAGE:
            removeDataUpload(dataUploadSelected.frontOfIdList, id);
            break;
          case ImageType.BACK_IMAGE:
            removeDataUpload(dataUploadSelected.backOfIdList, id);
            break;
          case ImageType.ADDITIONAL_DOCUMENT:
            removeDataUpload(dataUploadSelected.additionalDocumentList, id);
            break;
        }
      }

      updateDocument(dataUploadSelected);
    },
    [dataDocument],
  );

  const onSetIsAllowShowModal = useCallback(() => {
    setIsAllowShowModal(false);
  }, []);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  useIncompleteSync(!isValid, 'documentUpload', undefined, itemKey);

  const caseId = useBoundStore(state => state.case.caseId);

  const { mutateAsync: deleteDocument } = useDeleteDocument();

  const handleDelete = async (
    partyId: string,
    document: DocumentList,
    imageType: ImageType,
    imageItemId: string,
  ) => {
    if (!caseId) throw new Error('missing case id');
    if (partyId) {
      const matchedItem = document.listImagesUpload.find(
        imageItem => imageItem.id === imageItemId,
      );
      if (!matchedItem) return;
      const response = await deleteDocument({
        caseId,
        fileName: matchedItem.name || '',
      });
      if (response) {
        deleteDocumentImage(partyId, imageType, imageItemId);
      }
    }
  };

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <ScrollView>
        <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">{t('eApp:documentUpload.title')}</H6>
          <Label
            color={colors.palette.fwdGreyDarker}
            style={{ marginTop: space[2] }}>
            {t('eApp:documentUpload.limit')}
          </Label>
          {dataDocument.map((item, index) => {
            if (
              item.partyType === DocumentPartyType.BENEFICIAL_OWNER &&
              item.customerType === PartyType.ENTITY
            ) {
              return (
                <Box
                  key={item.title}
                  style={{ marginTop: space[4] }}
                  px={space[4]}
                  py={space[4]}
                  backgroundColor={colors.palette.white}
                  borderRadius={sizes[3]}>
                  <LargeLabel fontWeight="bold" color={colors.secondary}>
                    {item.title}
                  </LargeLabel>
                  <LargeLabel
                    fontWeight="medium"
                    style={{ marginTop: space[4] }}>
                    {t('eApp:documentUpload.certificateOnBeneficialOwner')}
                  </LargeLabel>
                  <UploadButton
                    partyId={item.partyId}
                    document={item.certOnBO}
                    customerType={mapToCustomerType(item.partyType)}
                    customerSeq="1"
                    docType={DocumentType.CertOnBO}
                    applicationNum={applicationNum}
                    onSetIsAllowShowModal={onSetIsAllowShowModal}
                    isAllowShowModal={isAllowShowModal}
                    multiple={item.certOnBO?.multiple}
                    onUpload={(id, base64, name, filePath, status) => {
                      updateDocumentImage(
                        id,
                        item.partyId,
                        ImageType.CERT_ON_BO,
                        base64,
                        name,
                        filePath,
                        status,
                      );
                    }}
                    onDelete={(id: string) =>
                      handleDelete(
                        item.partyId,
                        item.certOnBO,
                        ImageType.CERT_ON_BO,
                        id,
                      )
                    }
                  />
                  {beneficialOwnerIsUSCitizen ? (
                    <>
                      <Box
                        style={{
                          width: '100%',
                        }}
                        height={1}
                        backgroundColor={'#D9D9D9'}
                        marginTop={space[4]}
                      />
                      <LargeLabel
                        fontWeight="medium"
                        style={{ marginTop: space[4] }}>
                        {t('eApp:documentUpload.IRSWForm')}
                      </LargeLabel>
                      <UploadButton
                        partyId={item.partyId}
                        document={item.irswForm}
                        customerType={mapToCustomerType(item.partyType)}
                        customerSeq="2"
                        docType={DocumentType.IRSWForm}
                        applicationNum={applicationNum}
                        onSetIsAllowShowModal={onSetIsAllowShowModal}
                        isAllowShowModal={isAllowShowModal}
                        multiple={item.irswForm?.multiple}
                        onUpload={(id, base64, name, filePath, status) => {
                          updateDocumentImage(
                            id,
                            item.partyId,
                            ImageType.IRSW_FORM,
                            base64,
                            name,
                            filePath,
                            status,
                          );
                        }}
                        onDelete={(id: string) => {
                          handleDelete(
                            item.partyId,
                            item.irswForm,
                            ImageType.IRSW_FORM,
                            id,
                          );
                        }}
                      />
                    </>
                  ) : null}
                </Box>
              );
            }

            return (
              <Box
                key={item.title}
                style={{ marginTop: space[4] }}
                px={space[4]}
                py={space[4]}
                backgroundColor={colors.palette.white}
                borderRadius={sizes[3]}>
                <LargeLabel fontWeight="bold" color={colors.secondary}>
                  {item.title}
                </LargeLabel>
                <Label
                  style={{ marginTop: space[1] }}
                  color={colors.palette.fwdGreyDarker}>
                  {item.description}
                </Label>
                <LargeLabel fontWeight="medium" style={{ marginTop: space[4] }}>
                  {t('eApp:documentUpload.frontOfId', {
                    type: item.documentType || 'Document',
                  })}
                </LargeLabel>
                <UploadButton
                  partyId={item.partyId}
                  document={item.frontOfIdList}
                  customerType={mapToCustomerType(item.partyType)}
                  customerSeq="1"
                  docType={DocumentType.FrontID}
                  applicationNum={applicationNum}
                  onSetIsAllowShowModal={onSetIsAllowShowModal}
                  isAllowShowModal={isAllowShowModal}
                  multiple={item.frontOfIdList?.multiple}
                  onUpload={(id, base64, name, filePath, status, fromOcr) => {
                    updateDocumentImage(
                      id,
                      item.partyId,
                      ImageType.FRONT_IMAGE,
                      base64,
                      name,
                      filePath,
                      status,
                      // no need to pass fromOcr if the image is uploaded from document upload section
                    );
                  }}
                  onDelete={
                    item.frontOfIdList.listImagesUpload.length > 0 &&
                      !item.frontOfIdList.listImagesUpload[0].fromOcr
                      ? (id: string) =>
                        handleDelete(
                          item.partyId,
                          item.frontOfIdList,
                          ImageType.FRONT_IMAGE,
                          id,
                        )
                      : undefined
                  }
                  silentOCREnabled={
                    (item.customerType === PartyType.INDIVIDUAL &&
                      item.partyType === DocumentPartyType.POLICY_OWNER) ||
                    item.partyType === DocumentPartyType.INSURED
                  }
                />
                <Box
                  style={{
                    width: '100%',
                  }}
                  height={1}
                  backgroundColor={'#D9D9D9'}
                  marginTop={space[4]}
                />
                <LargeLabel fontWeight="medium" style={{ marginTop: space[4] }}>
                  {t('eApp:documentUpload.backOfId', {
                    type: item.documentType || 'Document',
                  })}
                </LargeLabel>
                <UploadButton
                  partyId={item.partyId}
                  document={item.backOfIdList}
                  customerType={mapToCustomerType(item.partyType)}
                  customerSeq="2"
                  docType={DocumentType.BackID}
                  applicationNum={applicationNum}
                  onSetIsAllowShowModal={onSetIsAllowShowModal}
                  isAllowShowModal={isAllowShowModal}
                  multiple={item.backOfIdList?.multiple}
                  onUpload={(id, base64, name, filePath, status) => {
                    updateDocumentImage(
                      id,
                      item.partyId,
                      ImageType.BACK_IMAGE,
                      base64,
                      name,
                      filePath,
                      status,
                    );
                  }}
                  onDelete={(id: string) => {
                    handleDelete(
                      item.partyId,
                      item.backOfIdList,
                      ImageType.BACK_IMAGE,
                      id,
                    );
                  }}
                />
                {item.hasAdditionalDocument ? (
                  <Box>
                    <Box
                      style={{
                        width: '100%',
                      }}
                      height={1}
                      backgroundColor={'#D9D9D9'}
                      marginTop={space[4]}
                    />
                    <LargeLabel
                      fontWeight="medium"
                      style={{ marginTop: space[4] }}>
                      {showDesWithPaymentOptions(t, paymentOption)}
                    </LargeLabel>
                    <UploadButton
                      partyId={item.partyId}
                      document={item.additionalDocumentList}
                      customerType={mapToCustomerType(item.partyType)}
                      customerSeq="3"
                      docType={mapToDocumentType(paymentOption, bankDetails)}
                      applicationNum={applicationNum}
                      onSetIsAllowShowModal={onSetIsAllowShowModal}
                      isAllowShowModal={isAllowShowModal}
                      multiple={item.additionalDocumentList.multiple}
                      onUpload={(id, base64, name, filePath, status) => {
                        updateDocumentImage(
                          id,
                          item.partyId,
                          ImageType.ADDITIONAL_DOCUMENT,
                          base64,
                          name,
                          filePath,
                          status,
                        );
                      }}
                      onDelete={(id: string) => {
                        handleDelete(
                          item.partyId,
                          item.additionalDocumentList,
                          ImageType.ADDITIONAL_DOCUMENT,
                          id,
                        );
                      }}
                    />
                  </Box>
                ) : null}
              </Box>
            );
          })}
        </Box>
      </ScrollView>
      <FormAction
        primaryDisabled={!isValid || !arePreviousStepsComplete}
        onPrimaryPress={() => {
          nextGroup(true);
        }}
      />
    </Box>
  );
};

export default DocumentUpload;
