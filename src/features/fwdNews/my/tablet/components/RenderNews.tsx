import React from 'react';
import {
  Linking,
  useWindowDimensions,
} from 'react-native';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import WebView from 'react-native-webview';
import { renderHTML } from './templateJS';
import { usePortal } from '@gorhom/portal';
import ShowImg from './ShowImg';

export type additionalInformation = {
  type: string;
  title: string;
  fileName: string;
  url: string;
};

const RenderNews = ({
  content,
  additionalInfo,
  additionalInfoCallback,
}:{
  content: string | undefined;
  additionalInfo?: {
    title: string;
    data: additionalInformation[];
  } | undefined;
  additionalInfoCallback?: (data: any) => void;
}) => {
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const pop = usePortal();
  const { width } = useWindowDimensions();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const template = renderHTML({
    width,
    isTabletMode,
    content,
    additionalInfo,
  });
  // console.log('----------template', template);
  
  return (
    <WebView
      style={{ flex: 1 }}
      originWhitelist={['*']}
      source={{ html: template }}
      allowsFullscreenVideo
      allowsInlineMediaPlayback
      mediaPlaybackRequiresUserAction={false}
      onLoadStart={() => {
        // setAppLoading();
      }}
      onLoadEnd={() => {
        // setAppIdle();
      }}
      onMessage={(event) => {
        const obj = JSON.parse(event.nativeEvent.data);
        // console.log('obj', obj);
        switch (obj.element) {
          case 'a':
            Linking.openURL(obj.url);
            break;
          case 'additionalInfoList': {
            const info = JSON.parse(obj.data);
            // console.log('info', info);
            additionalInfoCallback && additionalInfoCallback(info);
            break;
          }
          case 'img': {
            const info = JSON.parse(obj.data);
            // console.log('info', info);
            pop.addPortal(
              'showImg',
              <ShowImg
                img={info}
                onClose={() => {
                  pop.removePortal('showImg');
                }}
              />,
            );
            break;
          }
          default:
            break;
        }
      }}
    />
  );
};

export default RenderNews;
