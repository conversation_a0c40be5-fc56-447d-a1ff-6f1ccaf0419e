import { country } from 'utils/context';
import { PHNewsTagButtonConfig } from './ph';
import { MYNewsTagButtonConfig } from './my';

export type NewsTagButtonConfigType = {
  label: string;
  isActive: boolean;
  type_of_news_post: string;
}[];

const getNewsTagButtonConfig = (country: string): NewsTagButtonConfigType => {
  switch (country) {
    case 'my':
    case 'ib':
    case 'id':
      return MYNewsTagButtonConfig;
    case 'ph':
      return PHNewsTagButtonConfig;
    default:
      return [];
  }
};

const NewsTagButtonConfig = getNewsTagButtonConfig(country);

export { NewsTagButtonConfig };
