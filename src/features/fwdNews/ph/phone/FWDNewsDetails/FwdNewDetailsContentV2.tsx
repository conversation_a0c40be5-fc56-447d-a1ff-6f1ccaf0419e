import { View, TouchableOpacity, Share, Platform } from 'react-native';
import React, { useEffect } from 'react';
import {
  RouteProp,
  useRoute,
  NavigationProp,
  useNavigation,
} from '@react-navigation/native';
import { Icon, Typography, Row, addToast } from 'cube-ui-components';
import { RootStackParamList } from 'types';
import { ContentStackNewsItem } from 'types/news';
import { useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Clipboard from 'expo-clipboard';
import FwdNewBookmarkButton from '../../../components/FwdNewBookmarkButton';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { build, irisUrl } from 'utils/context';

import { useGetContentStackNewsDetail } from 'hooks/useGetNews';
import RenderNews, {
  additionalInformation,
} from 'features/fwdNews/my/tablet/components/RenderNews';

export const irisShareUrl =
  irisUrl + '/iris_share/public/event/{language}/{parentEventId}/detail';

export const contentStackNewsUrl =
  build === 'prd'
    ? 'https://cube-news-1-prod.contentstackapps.com/news_share/public/event/en/{uid}/detail'
    : 'https://cube-news-1.contentstackapps.com/news_share/public/event/en/{uid}/detail';

export default function FwdNewDetailsContent() {
  const { t } = useTranslation('news');
  const {
    params: { id: newsId, title },
  } = useRoute<RouteProp<RootStackParamList, 'FWDNewsDetails'>>();

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

  const { colors, sizes } = useTheme();

  const { data: detailsToDisplay } = useGetContentStackNewsDetail(newsId);

  const additionalInformation: additionalInformation[] = [];
  let additionalInfo:
    | { title: string; data: additionalInformation[] }
    | undefined;
  if (detailsToDisplay?.pdf_upload_field?.url) {
    additionalInformation.push({
      type: 'pdf',
      title: 'View PDF',
      fileName: detailsToDisplay.pdf_upload_field?.filename,
      url: detailsToDisplay.pdf_upload_field.url,
    });
  }
  if (additionalInformation.length) {
    additionalInfo = {
      title: t('details.additionalInformation'),
      data: additionalInformation,
    };
  }

  return (
    <>
      <View style={{ flex: 1 }}>
        <RenderNews
          content={
            detailsToDisplay?.news_content_v2 ||
            detailsToDisplay?.news_details?.replace(/\n/g, '<br />')
          }
          additionalInfo={additionalInfo}
          additionalInfoCallback={info => {
            if (info.type === 'pdf') {
              navigate('PdfViewer', {
                title: info.fileName,
                fileName: info.fileName,
                downloadable: true,
                // sharable: true,
                // shareType: 'social-media',
                url: info.url,
                iconLeft: 'arrow',
              });
            }
          }}
        />
      </View>

      <ButtonsRow>
        <FWDNewsShareButton
          isSharable={Boolean(detailsToDisplay?.display_share_button)}
          detailsToDisplay={detailsToDisplay}
        />
        <ButtonSeparator />
        <FwdNewBookmarkButton
          newsId={newsId}
          iconSize={sizes[6]}
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 44,
          }}>
          <Typography.H7 fontWeight={'bold'} color={colors.primary}>
            {t('bookmark.button')}
          </Typography.H7>
        </FwdNewBookmarkButton>
      </ButtonsRow>
    </>
  );
}

// TODO: Update share action (remove Iris share)
const FWDNewsShareButton = ({
  isSharable,
  detailsToDisplay,
}: {
  isSharable: boolean;
  detailsToDisplay: ContentStackNewsItem | undefined | null;
}) => {
  const { t } = useTranslation('news');
  const { colors, sizes } = useTheme();

  useEffect(() => {
    const keyboardListener = Clipboard.addClipboardListener(
      ({ contentTypes }) => {
        if (!contentTypes.includes(Clipboard.ContentType.PLAIN_TEXT)) {
          return;
        }
        Clipboard.getStringAsync().then(content => {
          if (!content.includes('https://')) {
            return;
          }
          addToast([{ message: 'Link Copied.', IconLeft: <Icon.Link /> }]);
        });
      },
    );
    return () => {
      Clipboard.removeClipboardListener(keyboardListener);
    };
  }, []);

  const onShareAction = (
    detailsToDisplay: ContentStackNewsItem | undefined | null,
  ) => {
    const shareUrl = contentStackNewsUrl.replace(
      '{uid}',
      `${detailsToDisplay?.uid ?? ''}`,
    );
    const shareConfig =
      Platform.OS === 'ios' ? { url: shareUrl } : { message: shareUrl };

    Share.share(shareConfig)
      .then()
      .catch(error => console.log('error', error));
  };

  const onPressHandler = () => {
    onShareAction(detailsToDisplay);
  };

  return (
    <ShareButton
      disabled={!isSharable}
      isSharable={isSharable}
      onPress={onPressHandler}>
      <Row
        style={{
          gap: sizes[2],
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Icon.Share height={sizes[6]} width={sizes[6]} />
        <Typography.H7 fontWeight={'bold'} color={colors.primary}>
          {t('share.button')}
        </Typography.H7>
      </Row>
    </ShareButton>
  );
};

const ButtonSeparator = styled(View)(({ theme }) => ({
  alignSelf: 'center',
  height: theme.sizes[6],
  borderRightWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
}));

const ButtonsRow = styled(Row)(({ theme }) => {
  const { bottom } = useSafeAreaInsets();
  return {
    borderTopWidth: 1,
    borderTopColor: theme.colors.palette.fwdGrey[50],
    backgroundColor: theme.colors.background,
    paddingTop: theme.sizes[4],
    paddingBottom: theme.sizes[4] + bottom,
  };
});

const ShareButton = styled(TouchableOpacity)(
  ({ isSharable }: { isSharable: boolean }) => {
    const { sizes } = useTheme();
    return {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: sizes[11],
      opacity: isSharable ? 1 : 0.5,
    };
  },
);
