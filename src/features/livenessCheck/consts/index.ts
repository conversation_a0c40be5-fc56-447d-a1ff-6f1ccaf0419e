import { Face } from 'react-native-vision-camera-face-detector';

export const FACE_OVAL_WIDTH = 288;
export const FACE_OVAL_HEIGHT = 368;
export const FACE_OVAL_SCALE = FACE_OVAL_WIDTH / FACE_OVAL_HEIGHT;
export const MOBILE_FACE_OVAL_WIDTH_SCALE = 0.4;
export const TABLET_FACE_OVAL_WIDTH_SCALE = 0.258;

// Contours location from MLKit doc https://developers.google.com/ml-kit/vision/face-detection
const LIP_MIDDLE_POINT_INDEX = 4;

export const emojiDetectionCallbackMap: {
  [emoji: string]: (face?: Face, options?: { [key: string]: any }) => boolean;
} = {
  astonished: (face?: Face, options = { isCompareAxisY: true }) => {
    'worklet';
    if (!face) return false;
    const upperLipMiddlePt =
      face.contours?.['UPPER_LIP_BOTTOM']?.[LIP_MIDDLE_POINT_INDEX];
    const lowerLipMiddlePt =
      face.contours?.['LOWER_LIP_TOP']?.[LIP_MIDDLE_POINT_INDEX];
    if (!face.bounds.height || !upperLipMiddlePt || !lowerLipMiddlePt)
      return false;

    const lipDistance = options.isCompareAxisY
      ? lowerLipMiddlePt.y - upperLipMiddlePt.y
      : lowerLipMiddlePt.x - upperLipMiddlePt.x;

    // TODO: See if need to compare with initial lip distance rather than 10% of faceBound height
    // Distance of upper lip and lower lip should further than at least 10% of faceBound height
    return lipDistance >= face.bounds.height * 0.1;
  },
  slight_smile: (face?: Face) => {
    'worklet';
    if (!face) return false;
    return face.smilingProbability >= 0.5;
  },
};
