import { useTheme } from '@emotion/react';
import { Row, Column, Box, Icon, Button, LargeLabel } from 'cube-ui-components';
import { ReactNode } from 'react';
import { Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import WarningMessage from './WarningMessage';

type Props = {
  headerComponent: ReactNode;
  imageSource: {
    uri: string;
  };
  isMatched: boolean;
  onUploadButtonPress: () => void;
};

const FinishedView = ({
  headerComponent,
  isMatched,
  imageSource,
  onUploadButtonPress,
}: Props) => {
  const { t } = useTranslation(['livenessCheck', 'common']);

  const { space, colors, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const thumbnailSize = isTabletMode ? sizes[20] : sizes[12];

  return (
    <Column gap={isTabletMode ? space[3] : space[2]}>
      {headerComponent}
      <Row alignItems="center" marginTop={isTabletMode ? space[3] : space[1]}>
        <Row flex={1} paddingLeft={space[4]} gap={space[3]}>
          <Box
            borderRadius={sizes[2]}
            overflow="hidden"
            border={1}
            width={thumbnailSize}
            borderColor={colors.palette.fwdGrey[100]}>
            <Image
              source={imageSource}
              style={{
                width: thumbnailSize,
                height: thumbnailSize,
                backgroundColor: colors.palette.fwdGrey[50],
              }}
              resizeMode="cover"
            />
          </Box>
          {!isMatched && isTabletMode && (
            <WarningMessage
              message={t('livenessCheck:finishedView.manualVerification')}
            />
          )}
        </Row>
        {isMatched && (
          <Row gap={space[2]} alignItems="center">
            <Icon.TickCircleFill fill={colors.palette.alertGreen} />
            <LargeLabel fontWeight="bold" color={colors.palette.alertGreen}>
              {t('livenessCheck:finishedView.verified')}
            </LargeLabel>
          </Row>
        )}
        {!isMatched && (
          <Button
            onPress={onUploadButtonPress}
            variant="secondary"
            text={t('livenessCheck:finishedView.retake')}
            style={{ width: isTabletMode ? sizes[35] : sizes[25] }}
          />
        )}
      </Row>
      {!isMatched && !isTabletMode && (
        <WarningMessage
          message={t('livenessCheck:finishedView.manualVerification')}
          style={{ marginTop: space[1], marginLeft: space[4] }}
        />
      )}
    </Column>
  );
};

export default FinishedView;
