import { useTheme } from '@emotion/react';
import LottieView from 'lottie-react-native';
import { Column, LargeBody, Row } from 'cube-ui-components';
import React, { ReactNode } from 'react';
import { circularLoadingLottie } from 'features/livenessCheck/assets/lotties';

type Props = {
  headerComponent?: ReactNode;
  message: string;
};

const LoadingView = ({ headerComponent, message }: Props) => {
  const { colors, space } = useTheme();

  return (
    <Column gap={space[3]}>
      {headerComponent}
      <Row alignItems={'center'} paddingLeft={space[4]} gap={space[2]}>
        <LottieView
          source={circularLoadingLottie}
          autoPlay
          style={{
            width: space[6],
            height: space[6],
          }}
          resizeMode="contain"
          speed={1}
          loop
        />
        <LargeBody color={colors.secondaryVariant}>{message}</LargeBody>
      </Row>
    </Column>
  );
};

export default LoadingView;
