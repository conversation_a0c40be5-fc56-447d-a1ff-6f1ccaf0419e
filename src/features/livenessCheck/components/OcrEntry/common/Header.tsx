import { useTheme } from '@emotion/react';
import { Row, LargeLabel } from 'cube-ui-components';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

type Props = {
  title?: string;
  children?: ReactNode;
};
export default function Header({ title, children }: Props) {
  const { t } = useTranslation('common');
  const { colors, space } = useTheme();

  return (
    <Row alignItems="center" gap={space[1]}>
      <LargeLabel fontWeight="bold" color={colors.secondary}>
        {title || t('ocr.upload.title')}
      </LargeLabel>
      {children}
    </Row>
  );
}
