import { useTheme } from '@emotion/react';
import { Body, Button, LargeBody, Row, Icon } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

type Props = {
  onPress?: () => void;
};

const IdleView = ({ onPress }: Props) => {
  const { space, sizes, colors } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('common');

  const description = t('ocr.upload.description');

  const TextWrapper = isTabletMode ? LargeBody : Body;

  return (
    <Row
      gap={2}
      alignItems={'center'}
      paddingTop={space[1]}
      paddingLeft={space[4]}>
      <TextWrapper color={colors.secondaryVariant} style={{ flex: 1 }}>
        {description}
      </TextWrapper>
      <Button
        text={t('ocr.upload')}
        icon={<Icon.Upload size={18} />}
        onPress={onPress}
        style={{ width: isTabletMode ? sizes[35] : sizes[25] }}
      />
    </Row>
  );
};

export default IdleView;
