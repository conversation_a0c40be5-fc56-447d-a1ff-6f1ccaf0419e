import { useTheme } from '@emotion/react';
import { DeviceMotionOrientation } from 'expo-sensors';
import throttle from 'lodash/throttle';
import { RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import { Platform, Vibration } from 'react-native';
import { Camera } from 'react-native-vision-camera';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  CubeVisionCamera,
  CubeVisionCameraProps,
  ShutterOverlay,
} from 'components/CubeVisionCamera';
import { useLivenessCheck } from 'features/livenessCheck/hooks/useLivenessCheck';
import { emojiDetectionCallbackMap } from 'features/livenessCheck/consts';
import { LivenessCheckOverlay } from 'features/livenessCheck/components/LivenessCheckEntry/LivenessCheckOverlay';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { StatusBar } from 'expo-status-bar';

const EMOJI_DETECTION_INTERVAL_IN_MS = 500;

type Props = {
  cameraRef: RefObject<Camera>;
  selfieUri: string;
  verifyingEmoji?: string;
  onDismiss: () => void;
  onTakePhoto: () => Promise<void>;
  onUpdateEmojiVerifiedResult: (emoji: string, isMatch: boolean) => void;
  onLivenessCheckComplete: () => void;
  isCameraPaused?: boolean;
};

const LivenessCheckCamera = ({
  cameraRef,
  selfieUri,
  verifyingEmoji,
  onDismiss,
  onTakePhoto,
  onUpdateEmojiVerifiedResult,
  onLivenessCheckComplete,
  isCameraPaused = false,
}: Props) => {
  const { colors } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { detectedFaceRef, frameProcessor, isFaceWithinBorder, orientation } =
    useLivenessCheck();
  const isPortrait = orientation === DeviceMotionOrientation.Portrait;

  const [isShowShutterOverlay, setIsShowShutterOverlay] = useState(false);

  useEffect(() => {
    if (!selfieUri || !verifyingEmoji) return;
    if (!detectedFaceRef.current || !isFaceWithinBorder) return;

    const throttledDetection = throttle(() => {
      const detectionCallback = emojiDetectionCallbackMap[verifyingEmoji];
      if (!detectionCallback) {
        throw new Error(
          `[LivenessCheck] No callback for detecting emoji ${verifyingEmoji}`,
        );
      }

      let options = undefined;
      if (verifyingEmoji === 'astonished') {
        options = {
          isCompareAxisY: Platform.OS === 'android' || !isPortrait,
        };
      }

      const isMatch = detectionCallback(detectedFaceRef.current, options);
      console.log(`Matching ${verifyingEmoji}...`, isMatch);
      onUpdateEmojiVerifiedResult(verifyingEmoji, isMatch);
    }, EMOJI_DETECTION_INTERVAL_IN_MS);

    const timer = setInterval(
      throttledDetection,
      EMOJI_DETECTION_INTERVAL_IN_MS,
    );

    return () => {
      console.log('clear emoji detection timer');
      clearInterval(timer);
      throttledDetection.cancel();
    };
  }, [
    selfieUri,
    verifyingEmoji,
    detectedFaceRef,
    isFaceWithinBorder,
    isPortrait,
    onUpdateEmojiVerifiedResult,
  ]);

  const onShutter = useCallback(() => {
    setIsShowShutterOverlay(true);
    Vibration.vibrate(300);
    setTimeout(() => setIsShowShutterOverlay(false), 300);
  }, []);

  const cameraProps: CubeVisionCameraProps = useMemo(
    () => ({
      cameraRef,
      cameraDevice: {
        cameraPosition: 'front',
      },
      cameraProps: {
        frameProcessor,
        onShutter,
      },
      isPaused: isCameraPaused,
    }),
    [frameProcessor, onShutter, isCameraPaused],
  );

  return (
    <SafeAreaView
      style={{
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        backgroundColor: colors.palette.black,
      }}>
      <StatusBar hidden />
      <CubeVisionCamera {...cameraProps} />
      <LivenessCheckOverlay
        photo={selfieUri}
        onTakePhoto={onTakePhoto}
        isFaceWithinBorder={isFaceWithinBorder}
        verifyingEmoji={verifyingEmoji}
        isPortrait={isTabletMode && isPortrait}
        onDismiss={onDismiss}
        onComplete={onLivenessCheckComplete}
      />
      <ShutterOverlay isVisible={isShowShutterOverlay} />
    </SafeAreaView>
  );
};

export default LivenessCheckCamera;
