import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path, Circle } from 'react-native-svg';

export function IconDeclined(props: SvgIconProps) {
  return (
    <Svg
      width={64}
      height={64}
      viewBox="0 0 64 64"
      fill="none"
      {...props}
    >
      <Path
        d="M17.394 12.412c-.365 0-.777.137-1.051.457-.274.274-.457.686-.457 1.052v39.131c0 .366.137.777.457 1.051.274.275.686.458 1.051.458h29.212c.365 0 .777-.138 1.051-.458.275-.274.457-.685.457-1.05V13.92c0-.366-.137-.777-.457-1.052-.274-.274-.685-.457-1.051-.457H17.394z"
        fill="#F3BB90"
      />
      <Path
        d="M19.406 50.218V15.932a.82.82 0 01.823-.823h23.588a.82.82 0 01.823.823v34.286a.82.82 0 01-.823.823H20.23c-.458 0-.823-.412-.823-.823z"
        fill="#fff"
      />
      <Path
        d="M41.166 37.875H22.88a.82.82 0 00-.823.823.82.82 0 00.823.823h18.286a.82.82 0 00.823-.823.82.82 0 00-.823-.823zM41.166 32.206H22.88a.82.82 0 00-.823.823.82.82 0 00.823.823h18.286a.82.82 0 00.823-.823.82.82 0 00-.823-.823zM31.2 43.817h-8.32a.82.82 0 00-.823.823.82.82 0 00.823.823h8.32a.82.82 0 00.823-.823.82.82 0 00-.823-.823z"
        fill="#E87722"
      />
      <Path
        d="M35.726 12.412c-.412 0-.731-.274-.823-.686-.32-1.326-1.509-2.286-2.88-2.286-1.417 0-2.606.96-2.88 2.286-.091.412-.411.686-.823.686h-.914a.82.82 0 00-.823.823v1.783a.82.82 0 00.823.822h9.234a.82.82 0 00.823-.822v-1.783a.82.82 0 00-.823-.823h-.914zm-4.983 0c0-.686.549-1.28 1.28-1.28.686 0 1.28.594 1.28 1.28h-2.56z"
        fill="#183028"
      />
      <Path
        d="M22.149 25.03c0-1.372 1.051-2.332 2.285-2.332 1.326 0 1.92.869 2.057 1.509l-.914.274c-.091-.32-.366-.823-1.143-.823-.594 0-1.28.412-1.28 1.372 0 .822.595 1.325 1.28 1.325.777 0 1.098-.503 1.143-.823l.96.275c-.137.594-.731 1.554-2.103 1.554-1.28-.046-2.285-.96-2.285-2.331zM27.132 27.224v-4.526h.96v4.526h-.96zM29.6 25.44l.732-.09c.183-.047.228-.092.228-.23 0-.182-.137-.32-.457-.32a.548.548 0 00-.549.458l-.822-.183c.045-.457.457-1.052 1.371-1.052 1.006 0 1.371.549 1.371 1.189v1.509c0 .228.046.457.046.502h-.868c0-.045-.046-.137-.046-.365a.983.983 0 01-.869.457c-.685 0-1.097-.457-1.097-.96.046-.549.457-.869.96-.914zm.96.55v-.138l-.594.091c-.183.046-.366.138-.366.366 0 .183.092.32.366.32.32 0 .594-.137.594-.64zM32.754 22.606c.32 0 .549.229.549.549 0 .32-.229.549-.549.549a.528.528 0 01-.548-.549c0-.32.228-.549.548-.549zm-.457 4.618v-3.109h.96v3.109h-.96zM34.08 27.223v-3.108h.914v.366c.137-.275.549-.458.914-.458.457 0 .777.183.915.503.228-.365.548-.503.96-.503.594 0 1.142.366 1.142 1.189v2.011h-.914v-1.782c0-.275-.137-.503-.503-.503-.32 0-.502.274-.502.548v1.783h-.96v-1.783c0-.274-.138-.503-.503-.503-.32 0-.503.275-.503.549v1.783h-.96v-.092zM40.252 26.173c0 .228.182.411.502.411.229 0 .366-.137.366-.274 0-.092-.091-.229-.32-.274l-.365-.092c-.686-.137-.915-.503-.915-.96 0-.549.503-1.051 1.189-1.051.914 0 1.234.548 1.234.914l-.777.137c-.046-.229-.137-.366-.457-.366-.183 0-.32.092-.32.275 0 .***************.228l.412.092c.64.137.96.502.96.96 0 .548-.412 1.051-1.235 1.051-.96 0-1.28-.64-1.325-.96l.823-.091z"
        fill="#E87722"
      />
      <Circle cx={47} cy={47} r={11} fill="#fff" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M58 47c0 6.075-4.925 11-11 11s-11-4.925-11-11 4.925-11 11-11 11 4.925 11 11zm-9.503-.027l3.639-3.638-.39-.39a1.65 1.65 0 00-2.332 0l-2.472 2.472-2.472-2.471a1.65 1.65 0 00-2.333 0l-.389.389 3.639 3.638-3.639 3.638.39.389a1.65 1.65 0 002.332 0l2.472-2.472L49.414 51a1.65 1.65 0 002.333 0l.389-.389-3.639-3.638z"
        fill="#8B8E8F"
      />
    </Svg>
  );
}
