import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export function ClaimCancelled(props: SvgIconProps) {
  return (
    <Svg width="28" height="28" viewBox="0 0 28 28" fill="none" {...props}>
      <Path
        d="M7.03712 5.27441C6.87438 5.27441 6.71165 5.33716 6.58616 5.46068C6.46068 5.58616 6.3999 5.74889 6.3999 5.91163V22.689C6.3999 22.8517 6.46264 23.0145 6.58616 23.14C6.70969 23.2654 6.87438 23.3262 7.03712 23.3262H19.5677C19.7304 23.3262 19.8932 23.2635 20.0187 23.14C20.1422 23.0145 20.2049 22.8537 20.2049 22.689V5.91163C20.2049 5.74889 20.1422 5.5842 20.0187 5.46068C19.8951 5.33519 19.7304 5.27441 19.5677 5.27441H7.03712Z"
        fill="#F3BB90" />
      <Path
        d="M7.88623 21.4832V6.51553C7.88623 6.3175 8.047 6.15869 8.24307 6.15869H18.3601C18.5581 6.15869 18.7169 6.31947 18.7169 6.51553V21.4832C18.7169 21.6812 18.5561 21.8401 18.3601 21.8401H8.24307C8.04504 21.8401 7.88623 21.6793 7.88623 21.4832Z"
        fill="#E87722" />
      <Path
        d="M17.2244 16.2051H9.37784C9.17981 16.2051 9.021 16.3659 9.021 16.5619C9.021 16.7599 9.18177 16.9188 9.37784 16.9188H17.2244C17.4224 16.9188 17.5812 16.758 17.5812 16.5619C17.5812 16.3659 17.4224 16.2051 17.2244 16.2051Z"
        fill="white" />
      <Path
        d="M17.2244 13.6523H9.37784C9.17981 13.6523 9.021 13.8131 9.021 14.0092C9.021 14.2072 9.18177 14.366 9.37784 14.366H17.2244C17.4224 14.366 17.5812 14.2053 17.5812 14.0092C17.5812 13.8112 17.4224 13.6523 17.2244 13.6523Z"
        fill="white" />
      <Path
        d="M17.2244 11.0995H9.37784C9.17981 11.0995 9.021 11.2603 9.021 11.4563C9.021 11.6544 9.18177 11.8132 9.37784 11.8132H17.2244C17.4224 11.8132 17.5812 11.6524 17.5812 11.4563C17.5812 11.2583 17.4224 11.0995 17.2244 11.0995Z"
        fill="white" />
      <Path
        d="M12.9443 18.7579H9.37784C9.17981 18.7579 9.021 18.9187 9.021 19.1148C9.021 19.3128 9.18177 19.4716 9.37784 19.4716H12.9443C13.1423 19.4716 13.3011 19.3108 13.3011 19.1148C13.3011 18.9187 13.1423 18.7579 12.9443 18.7579Z"
        fill="white" />
      <Path
        d="M15.2876 8.5448H11.3173C11.1193 8.5448 10.9604 8.70557 10.9604 8.90164C10.9604 9.09967 11.1212 9.25848 11.3173 9.25848H15.2876C15.4857 9.25848 15.6445 9.09771 15.6445 8.90164C15.6445 8.70557 15.4837 8.5448 15.2876 8.5448Z"
        fill="white" />
      <Path
        d="M14.8891 5.27443C14.7166 5.27443 14.5813 5.14895 14.5421 4.98229C14.4087 4.41958 13.9048 4 13.301 4C12.6971 4 12.1932 4.41958 12.0599 4.98229C12.0207 5.14895 11.8834 5.27443 11.7128 5.27443H11.3227C11.1246 5.27443 10.9658 5.4352 10.9658 5.63127V6.40377C10.9658 6.6018 11.1266 6.76061 11.3227 6.76061H15.2812C15.4793 6.76061 15.6381 6.59984 15.6381 6.40377V5.63127C15.6381 5.43324 15.4773 5.27443 15.2812 5.27443H14.8891ZM12.7559 5.27443C12.7559 4.97249 13.001 4.72741 13.3029 4.72741C13.6049 4.72741 13.85 4.97249 13.85 5.27443H12.7559Z"
        fill="#183028" />
      <Path
        d="M18.7166 23.936C20.3734 23.936 21.7164 22.593 21.7164 20.9362C21.7164 19.2795 20.3734 17.9364 18.7166 17.9364C17.0599 17.9364 15.7168 19.2795 15.7168 20.9362C15.7168 22.593 17.0599 23.936 18.7166 23.936Z"
        fill="#183028" />
      <Path
        d="M20.3106 21.8322L19.5636 21.0852C19.4812 21.0028 19.4812 20.8695 19.5636 20.7872L20.3106 20.0401C20.3929 19.9578 20.3929 19.8245 20.3106 19.7421L19.9126 19.3441C19.8302 19.2618 19.6969 19.2618 19.6145 19.3441L18.8675 20.0911C18.7852 20.1735 18.6519 20.1735 18.5695 20.0911L17.8225 19.3441C17.7401 19.2618 17.6068 19.2618 17.5245 19.3441L17.1265 19.7421C17.0441 19.8245 17.0441 19.9578 17.1265 20.0401L17.8735 20.7872C17.9558 20.8695 17.9558 21.0028 17.8735 21.0852L17.1265 21.8322C17.0441 21.9145 17.0441 22.0479 17.1265 22.1302L17.5245 22.5282C17.6068 22.6106 17.7401 22.6106 17.8225 22.5282L18.5695 21.7812C18.6519 21.6989 18.7852 21.6989 18.8675 21.7812L19.6145 22.5282C19.6969 22.6106 19.8302 22.6106 19.9126 22.5282L20.3106 22.1302C20.3929 22.0479 20.3929 21.9145 20.3106 21.8322Z"
        fill="white" />
    </Svg>
  );
}
