import React from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { View } from 'react-native';
import { claimStatus } from 'features/agentAssist/hooks/type';

interface Props {
  position: number
  completed?: boolean
  lastCompleted?: boolean
  status: string
}
export const DotComponent = (props: Props) => {
  const { colors } = useTheme();
  const {position, completed, lastCompleted, status} = props
  const statusStyle = React.useMemo(() => {
    const info = {
      [claimStatus.todo]: {
        textColor: colors.primary,
        background: colors.palette.fwdOrange['20'],
        outline: colors.palette.fwdOrange['50'],
        shadow: colors.palette.fwdLightGreen['50']
      },
      [claimStatus.pending]: {
        textColor: colors.palette.alertRed,
        background: colors.palette.alertRedLight,
        outline: colors.palette.fwdOrange['20'],
        shadow: colors.palette.fwdOrangeTransparent['50']
      },
      [claimStatus.completed]: {
        textColor: colors.palette.fwdLightGreen['100'],
        background: colors.palette.fwdLightGreen['20'],
        outline: colors.palette.fwdLightGreen['50'],
        shadow: colors.palette.fwdLightGreen['50']
      },
      [claimStatus.cancelled]: {
        textColor: colors.palette.fwdGreyDarkest,
        background: colors.palette.fwdGrey['100'],
        outline: colors.palette.fwdGreyDark,
        shadow: colors.palette.fwdGrey['100']
      },
    }
    return info[status] || {
      textColor: colors.primary,
      background: colors.palette.fwdOrange['20'],
      outline: colors.palette.fwdOrange['50'],
      shadow: colors.palette.fwdLightGreen['50']
    }
  }, [status])
  const getProgressColor = React.useMemo(() => {
    return statusStyle.outline
  }, [statusStyle])
  const getChildColor = React.useMemo(() => {
    return statusStyle.textColor
  }, [statusStyle])
  const getFocusProgress = React.useMemo(() => {
    return statusStyle.outline
  }, [statusStyle])
  const getFocusShadow = React.useMemo(() => {
    return statusStyle.shadow
  }, [statusStyle])


  const getStyleParent = React.useMemo(() => {
    const styles: any = {
      backgroundColor: colors.palette.fwdGrey['100']
    }
    if (position === 1) styles.left = -2
    if (position === 3) styles.right = -2
    if (completed) styles.backgroundColor = getProgressColor
    return styles
  }, [colors, position, completed, getProgressColor])

  const getStyleChild = React.useMemo(() => {
    const styles: any = {
      backgroundColor: colors.palette.fwdGrey['50']
    }
    if (completed) styles.backgroundColor = getChildColor
    return styles
  }, [colors, completed, getChildColor])
  if (lastCompleted) {
    return (
      <DotFocus style={{
        ...getStyleParent,
        backgroundColor: getFocusShadow
      }}>
        <DotChildFocus style={{
          backgroundColor: getFocusProgress,
        }}>
          <ChildDot style={getStyleChild}/>
        </DotChildFocus>
      </DotFocus>
    )
  }
  return (
    <Dot style={getStyleParent}>
      <ChildDot style={getStyleChild}/>
    </Dot>
  )
}
const Dot = styled(View)(({ theme: { space, borderRadius } }) => ({
  width: space[4],
  height: space[4],
  borderRadius: borderRadius.full,
  marginTop: -5.5,
  alignItems: 'center',
  justifyContent: 'center'
}));
const DotFocus = styled(View)(({ theme: { colors, space, borderRadius } }) => ({
  width: space[5],
  height: space[5],
  borderRadius: borderRadius.full,
  marginTop: -8,
  shadowColor: colors.palette.black,
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
  alignItems: 'center',
  justifyContent: 'center'
}));
const DotChildFocus = styled(View)(({ theme: { space, borderRadius } }) => ({
  width: space[4],
  height: space[4],
  borderRadius: borderRadius.full,
  alignItems: 'center',
  justifyContent: 'center'
}));
const ChildDot = styled(View)(({ theme: { space, borderRadius } }) => ({
  width: space[3] - 2,
  height: space[3] - 2,
  borderRadius: borderRadius.full,
}));
