import styled from '@emotion/native';
import { View } from 'react-native';
import { Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

//TODO: design update
export const PendingIssue = () => {
  const { space, colors } = useTheme();
  const { t } = useTranslation('agentAssist');

  const renderIssue = () => (
    <ViewIssue>
      <Row gap={space[2]}>
        <Typography.H6 fontWeight={'bold'} color={colors.palette.alertRed}>
          {t('agentAssist.detail.certificate')}
        </Typography.H6>
      </Row>
      <Typography.LargeLabel fontWeight={'bold'} color={colors.palette.fwdGreyDarker}>
        {t('agentAssist.detail.document')}
      </Typography.LargeLabel>
      <Typography.LargeBody>
        <Typography.LargeBody>
          {'BP2 - Complete Blood Count (CBC), Fasting Blood Sugar (FBS), Blood Urea Nitrogen (BUN), Creatinine (Crea), Cholesterol, Serum glutamic oxaloacetic transaminase (SGOT), Serum glutamic pyruvic transaminase (SGPT'}
        </Typography.LargeBody>
        <Typography.LargeBody fontWeight={'bold'} color={colors.primary}>
          {' ...more'}
        </Typography.LargeBody>
      </Typography.LargeBody>
    </ViewIssue>
  )
  return (
    <Root>
      <Row alignItems={'center'} gap={space[2]}>
        <Typography.LargeLabel fontWeight={'bold'}>
          {t('agentAssist.detail.outstanding', {count: 2})}
        </Typography.LargeLabel>
      </Row>
      {renderIssue()}
      {renderIssue()}
      {renderIssue()}

    </Root>
  )
}
const Root = styled(View)(({ theme: { colors, space, borderRadius } }) => ({
  backgroundColor: colors.background,
  padding: space[4],
  marginHorizontal: space[3],
  marginTop: space[3],
  borderRadius: borderRadius.medium
}));
const ViewIssue = styled(View)(({ theme: { colors, space, borderRadius } }) => ({
  padding: space[3],
  backgroundColor: colors.palette.fwdGrey['20'],
  borderRadius: borderRadius.medium,
  gap: space[2],
  marginTop: space[2],
  marginBottom: space[1]
}));
