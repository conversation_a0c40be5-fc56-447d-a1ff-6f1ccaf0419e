export default {
  'agentAssist.empty': 'Empty record',
  'agentAssist.customer': 'Customer',
  'agentAssist.submitDate': 'Submit date',
  'agentAssist.policyNumber': 'Policy number',
  'agentAssist.goSecure': 'GoSecure',
  'agentAssist.search.title': 'I’m looking for claim',
  'agentAssist.search.hint': 'Please enter keyword',
  'agentAssist.search.des': 'e.g. Customer’s name, policy number',
  'agentAssist.search.empty': 'No results found.\n' +
    'Adjust keyword to get better result or try another search.',
  'agentAssist.overview.submitted': 'Claims submitted',
  'agentAssist.overview.pending': 'Claims pending',
  'agentAssist.overview.completed': 'Claims completed',
  'agentAssist.overview.cancelled': 'Claims cancelled',
  'agentAssist.overview.note': 'Would you like to make a claim?',
  'agentAssist.overview.note3': 'Only loss of life claims are supported.',
  'agentAssist.overview.start': 'Start claim',
  'agentAssist.overview.des': 'Year to date claim status',
  'agentAssist.overview.recent': 'Recent claims',
  'agentAssist.overview.note2': 'Displaying data in last 90 days',
  'agentAssist.tracking.status.submitted': 'Submitted',
  'agentAssist.tracking.status.pending': 'Pending',
  'agentAssist.tracking.status.completed': 'Completed',
  'agentAssist.tracking.filter': 'Filter by',
  'agentAssist.tracking.status.approved': 'Approved',
  'agentAssist.tracking.status.declined': 'Declined',
  'agentAssist.tracking.status.cancelled': 'Cancelled',
  'agentAssist.tracking.total': 'Total ({{count}})',
  'agentAssist.tracking.newest': 'Newest',
  'agentAssist.tracking.oldest': 'Oldest',
  'agentAssist.tracking.note': 'Displaying data from last 12 months',
  'agentAssist.claim.selectPolicy': 'Select policy',
  'agentAssist.claim.note': 'e.g. Policy owner\'s name, policy number',
  'agentAssist.claim.customer': 'Customer ({{count}})',
  'agentAssist.claim.policy': 'Policy ({{count}})',
  'agentAssist.claim.mobile': 'Mobile',
  'agentAssist.claim.issuedDate': 'Policy issued date',
  'agentAssist.claim.reinstatement': 'Policy reinstatement date',
  'agentAssist.claim.next': 'Next',
  'agentAssist.claim.details': 'Claim details',
  'agentAssist.claim.search': 'Search policy or customer',
  'agentAssist.claim.search.result': 'Search result ({{count}})',
  'agentAssist.claim.search.empty': 'No results found.\nAdjust keyword to get better result or try another search.',
  'agentAssist.claim.search.empty1': 'No results found.\nAdjust filters to get better results.',
  'agentAssist.detail.payAmount': 'Item pay amount',
  'agentAssist.detail.method': 'Payment method',
  'agentAssist.detail.date': 'Payment date',
  'agentAssist.detail.payDetail': 'Pay details ({{count}})',
  'agentAssist.detail.decline': 'Decline decision',
  'agentAssist.detail.outstanding': 'Outstanding items ({{count}})',
  'agentAssist.detail.document': 'Document description ',
  'agentAssist.detail.certificate': 'Death certificate',
  'agentAssist.detail.type': 'Claim type',
  'agentAssist.detail.life': 'Loss of life',
  'agentAssist.detail.total': 'Total pay amount',
  'agentAssist.detail.resubmit': 'Claim resubmit',
  'agentAssist.detail.claimStatus': 'Claim status',
  'agentAssist.detail.claimNumber': 'Claim number',
  'agentAssist.detail.ref': 'CUBE reference number',
  'agentAssist.detail.claimDate': 'Claim submission date',
  'agentAssist.detail.policyNumber': 'Policy Number',
  'agentAssist.detail.policyName': 'Policy owner\'s name',
  'agentAssist.detail.expressClaim': 'Express Claim',
  'agentAssist.status.todo': 'Submitted',
  'agentAssist.status.pending': 'Pending',
  'agentAssist.status.cancelled': 'Cancelled',
  'agentAssist.status.completed.approved': 'Completed - approved',
  'agentAssist.status.completed.declined': 'Completed - declined',
  'agentAssist.status.completed': 'Completed',
};
