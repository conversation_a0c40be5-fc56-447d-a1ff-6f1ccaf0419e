import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { DocumentListSection } from './DocumentListSection';
import { categoryItem, switchTabTitleObject } from 'types/documents';
import { SwitchTab } from '../tablet/SwitchTab';
import { useTranslation } from 'react-i18next';
import { Column, Row, Typography } from 'cube-ui-components';
import SectionTitle from './SectionTitle';
import TabLayout from './TabLayout';

export const UWForms = ({ UWFormsItem }: { UWFormsItem?: categoryItem }) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('document');

  type SwitchTab_TabsName = (typeof switchTabList)[number]['name'];
  const switchTabList: switchTabTitleObject[] = [
    { name: 'medical', label: t('medical') },
    { name: 'nonMedical', label: t('nonMedical') },
  ];

  const [focusedTab, setFocusedTab] = useState<SwitchTab_TabsName>(
    switchTabList[0].name,
  );

  const MedicalList = UWFormsItem?.file.filter(item => item.tag === 'Medical');
  const NonMedicalList = UWFormsItem?.file.filter(
    item => item.tag === 'Non-medical',
  );
  const EndorsementFormList = UWFormsItem?.file.filter(item => {
    return item.tag === 'Endorsement Forms';
  });
  const OthersList = UWFormsItem?.file.filter(item => {
    return item.tag === 'Others';
  });

  return (
    <TabLayout>
      {/* Questionnaries */}
      <View>
        <SectionTitleRow
          style={{
            marginTop: 0,
            alignItems: 'center',
            height: space[8],
          }}>
          <Column flex={1}>
            <SectionTitle>{t('questionnaires')}</SectionTitle>
          </Column>
          <Column width={space[48]}>
            <SwitchTab
              tabButtonStyle={{
                height: sizes[8],
                paddingHorizontal: space[3],
              }}
              buttonRowStyle={{
                gap: space[2],
              }}
              tabList={switchTabList}
              focusedTab={focusedTab}
              setFocusedTab={setFocusedTab}
            />
          </Column>
        </SectionTitleRow>

        {focusedTab === 'medical' && (
          <DocumentListSection documentList={MedicalList} />
        )}
        {focusedTab === 'nonMedical' && (
          <DocumentListSection documentList={NonMedicalList} />
        )}
      </View>

      {/* Endorsement Form */}
      <View>
        <SectionTitleRow>
          <SectionTitle>{t('endorsementForm')}</SectionTitle>
        </SectionTitleRow>
        <DocumentListSection documentList={EndorsementFormList} />
      </View>

      {/* Others */}
      <View>
        <SectionTitleRow>
          <SectionTitle>{t(`others`)}</SectionTitle>
        </SectionTitleRow>
        <DocumentListSection documentList={OthersList} />
        <DisclaimerText>{t('disclaimerText')}</DisclaimerText>
      </View>
    </TabLayout>
  );
};

const DisclaimerText = styled(Typography.Body)(({ theme }) => ({
  marginTop: theme.space[8],
  color: 'rgba(138, 142, 143, 1)',
  fontFamily: 'FWDCircularTT-Book',
}));

const SectionTitleRow = styled(Row)(({ theme }) => ({
  marginBottom: theme.space[4],
  marginTop: theme.space[8],
}));
