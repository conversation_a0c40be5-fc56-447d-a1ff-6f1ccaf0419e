import { ScrollView, ScrollViewProps, View } from 'react-native';

import React from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';

export default function TabLayout({ children }: ScrollViewProps) {
  const { sizes, colors } = useTheme();
  return (
    <TabContainer>
      <ScrollView
        contentContainerStyle={{
          backgroundColor: colors.surface,
          paddingHorizontal: sizes[8],
          paddingTop: sizes[5],
          paddingBottom: sizes[10],
        }}>
        {children}
      </ScrollView>
    </TabContainer>
  );
}

const TabContainer = styled(View)(({ theme }) => ({
  backgroundColor: theme.colors.surface,
  height: '100%',
  flex: 1,
}));
