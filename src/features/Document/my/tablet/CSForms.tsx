import { useTheme } from '@emotion/react';
import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { DocumentListSection } from './DocumentListSection';
import { categoryItem } from 'types/documents';
import { useTranslation } from 'react-i18next';
import { Row, Typography } from 'cube-ui-components';
import TabLayout from '../../tablet/TabLayout';

export default function CSForms({
  CSFormsItem,
}: {
  CSFormsItem?: categoryItem;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('document');
  const [dataList, setDataList] = useState(CSFormsItem?.file);
  useEffect(() => {
    setDataList(CSFormsItem?.file);
  }, [CSFormsItem?.file]);

  return (
    <TabLayout>
      <View>
        <Row mt={space[4]} mb={space[2]}>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {t('totalDocuments', { count: dataList?.length ?? 0 })}
          </Typography.Body>
        </Row>

        <DocumentListSection documentList={dataList} />
      </View>
    </TabLayout>
  );
}
