import { Alert } from 'react-native';
import React, { useEffect, useState } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import AppTopTabBar from 'components/AppTopTabBar';
import UWForms from './UWForms';
import CSForms from './CSForms';
import { fetchDocument } from 'hooks/useDocuments';
import {
  MODULAR_BLOCKS_LIST,
  categoryItem,
  categoryTitle,
} from 'types/documents';
import { useTranslation } from 'react-i18next';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTheme } from '@emotion/react';
import { Icon, addErrorBottomToast } from 'cube-ui-components';

const DocumentTab = createMaterialTopTabNavigator();

export default function DocumentScreenContent() {
  const [listData, setListData] = useState<MODULAR_BLOCKS_LIST>();
  const [UWFormItem, setUWFormItem] = useState<categoryItem | undefined>();
  const [CSFormItem, setCSFormItem] = useState<categoryItem | undefined>();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors } = useTheme();
  const { t } = useTranslation('document');

  useEffect(() => {
    const fetchDocumentFromStack = async () => {
      const res: MODULAR_BLOCKS_LIST | null = await fetchDocument();
      if (!res) {
        console.log('NO DOcument list ', res);
        addErrorBottomToast([
          {
            message: 'Error: No data, please try again later!',
            IconLeft: <Icon.AlarmAlert fill={colors.error} />,
          },
        ]);
        return;
      }
      setListData(res);
    };
    fetchDocumentFromStack();
  }, [navigation]);

  useEffect(() => {
    if (listData) {
      setUWFormItem(getTabList(listData, 'Underwriting Forms'));
      setCSFormItem(getTabList(listData, 'Certificate Servicing Forms'));
    }
  }, [listData]);

  const getTabList = (
    listData: MODULAR_BLOCKS_LIST,
    tabName: categoryTitle,
  ) => {
    return listData.filter(item => item.category.category_title === tabName)[0]
      ?.category;
  };

  return (
    <>
      <DocumentTab.Navigator
        tabBar={props => (
          <AppTopTabBar
            activeTabIndicatorStyle={{ backgroundColor: colors.primary }}
            activeTabLabelStyle={{ color: colors.primary }}
            isCustomizedNormalTabLabel
            variant="scrollable"
            {...props}
          />
        )}
        screenOptions={{ swipeEnabled: false }}
        initialRouteName="UW Forms">
        <DocumentTab.Screen
          name="UWForms"
          options={{ tabBarLabel: t('UWForms') }}
          children={() => <UWForms UWFormsItem={UWFormItem} />}
        />
        <DocumentTab.Screen
          name="CSForms"
          options={{ tabBarLabel: t('CSForms') }}
          children={() => <CSForms CSFormsItem={CSFormItem} />}
        />
      </DocumentTab.Navigator>
    </>
  );
}
