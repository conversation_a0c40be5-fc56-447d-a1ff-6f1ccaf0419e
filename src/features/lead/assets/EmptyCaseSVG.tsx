import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Rect, Path } from 'react-native-svg';

export default function EmptyCaseSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 160}
      height={props.height || 134}
      viewBox="0 0 160 134"
      fill="none">
      <Rect x="19" y="13" width="112" height="97" rx="8" fill="#F8F9F9" />
      <Rect x="27" y="21" width="96" height="41" rx="4" fill="#D1D6D4" />
      <Rect x="27" y="70" width="76" height="4" rx="2" fill="#D1D6D4" />
      <Rect x="27" y="82" width="66" height="4" rx="2" fill="#D1D6D4" />
      <Rect x="27" y="94" width="56" height="4" rx="2" fill="#D1D6D4" />
      <Path
        d="M107.037 107.873L110.181 107.841L138.766 79.2555L145.922 86.4114C147.21 87.6995 147.188 89.812 145.873 91.1269L119.67 117.33L116.526 117.362L107.037 107.873Z"
        fill="#D1D6D4"
      />
      <Path
        d="M96.3346 93.9945L122.538 67.7915C123.853 66.4766 125.965 66.4548 127.253 67.7429L134.098 74.5877L105.513 103.173L105.48 106.316L96.3022 97.1382L96.3346 93.9945Z"
        fill="#D1D6D4"
      />
      <Path
        d="M105.521 103.181L134.106 74.5955L138.773 79.2624L110.188 107.847L107.044 107.88L105.488 106.324L105.521 103.181Z"
        fill="#8B9793"
      />
      <Path
        d="M96.3304 94.0005L106.931 95.0668L107.998 105.668L118.599 106.734L119.665 117.335C119.197 117.803 118.601 118.125 117.957 118.259L94.3823 123.166C93.2161 123.408 92.126 123.02 91.3855 122.28C90.9219 121.816 90.5975 121.217 90.4777 120.546C90.4553 120.413 90.4392 120.277 90.431 120.139C90.4176 119.86 90.4389 119.574 90.4994 119.283L95.406 95.709C95.5401 95.0646 95.8621 94.4689 96.3304 94.0005Z"
        fill="#FFECDE"
      />
      <Path
        d="M91.3837 122.384C92.1242 123.124 93.2144 123.512 94.3805 123.27L101.827 121.72L92.0316 112.017L90.4977 119.387C90.4371 119.678 90.4159 119.963 90.4292 120.243C90.4375 120.38 90.4536 120.517 90.476 120.65C90.5958 121.32 90.9201 121.92 91.3837 122.384Z"
        fill="#F3BB90"
      />
      <Path
        d="M32.756 28.648C32.756 29.4987 32.4333 30.2467 31.788 30.892C31.172 31.5373 30.3507 31.86 29.324 31.86C28.1507 31.86 27.212 31.4493 26.508 30.628C25.8333 29.8067 25.496 28.8093 25.496 27.636C25.496 26.1693 25.716 24.9373 26.156 23.94C26.6253 22.9133 27.1973 22.092 27.872 21.476C28.5467 20.8307 29.28 20.3613 30.072 20.068C30.864 19.7453 31.612 19.5547 32.316 19.496V22.18C31.4653 22.3267 30.6587 22.7227 29.896 23.368C29.1627 23.984 28.7667 24.8053 28.708 25.832C28.9427 25.6853 29.2507 25.612 29.632 25.612C30.6293 25.612 31.392 25.8907 31.92 26.448C32.4773 26.976 32.756 27.7093 32.756 28.648ZM41.424 28.648C41.424 29.4987 41.1013 30.2467 40.456 30.892C39.84 31.5373 39.0187 31.86 37.992 31.86C36.8187 31.86 35.88 31.4493 35.176 30.628C34.5013 29.8067 34.164 28.8093 34.164 27.636C34.164 26.1693 34.384 24.9373 34.824 23.94C35.2933 22.9133 35.8653 22.092 36.54 21.476C37.2147 20.8307 37.948 20.3613 38.74 20.068C39.532 19.7453 40.28 19.5547 40.984 19.496V22.18C40.1333 22.3267 39.3267 22.7227 38.564 23.368C37.8307 23.984 37.4347 24.8053 37.376 25.832C37.6107 25.6853 37.9187 25.612 38.3 25.612C39.2973 25.612 40.06 25.8907 40.588 26.448C41.1453 26.976 41.424 27.7093 41.424 28.648Z"
        fill="#8B9793"
      />
      <Path
        d="M108.496 54.708C108.496 53.8573 108.819 53.1093 109.464 52.464C110.139 51.8187 110.989 51.496 112.016 51.496C113.189 51.496 114.113 51.9213 114.788 52.772C115.492 53.5933 115.844 54.5907 115.844 55.764C115.844 57.2307 115.609 58.4773 115.14 59.504C114.7 60.5307 114.143 61.3813 113.468 62.056C112.793 62.7013 112.06 63.1853 111.268 63.508C110.476 63.8307 109.728 64.0213 109.024 64.08V61.396C109.875 61.2493 110.667 60.8533 111.4 60.208C112.163 59.5333 112.573 58.6827 112.632 57.656C112.397 57.8027 112.089 57.876 111.708 57.876C110.711 57.876 109.919 57.5827 109.332 56.996C108.775 56.4093 108.496 55.6467 108.496 54.708ZM117.12 54.708C117.12 53.8573 117.443 53.1093 118.088 52.464C118.763 51.8187 119.613 51.496 120.64 51.496C121.813 51.496 122.737 51.9213 123.412 52.772C124.116 53.5933 124.468 54.5907 124.468 55.764C124.468 57.2307 124.233 58.4773 123.764 59.504C123.324 60.5307 122.767 61.3813 122.092 62.056C121.417 62.7013 120.684 63.1853 119.892 63.508C119.1 63.8307 118.352 64.0213 117.648 64.08V61.396C118.499 61.2493 119.291 60.8533 120.024 60.208C120.787 59.5333 121.197 58.6827 121.256 57.656C121.021 57.8027 120.713 57.876 120.332 57.876C119.335 57.876 118.543 57.5827 117.956 56.996C117.399 56.4093 117.12 55.6467 117.12 54.708Z"
        fill="#8B9793"
      />
    </Svg>
  );
}
