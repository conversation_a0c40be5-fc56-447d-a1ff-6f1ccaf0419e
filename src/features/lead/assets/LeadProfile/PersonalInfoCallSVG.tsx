import { View, Text } from 'react-native';
import React from 'react';
import Svg, { Rect, Path } from 'react-native-svg';
import { IconProps } from 'cube-ui-components/dist/cjs/components/Button/buttonRenderUtils';

export default function PersonalInfoCallSVG(props: Omit<IconProps, 'source'>) {
  return (
    <Svg width={41} height={40} viewBox="0 0 41 40" fill="none" {...props}>
      <Rect x={1.5} y={1} width={38} height={38} rx={19} fill="#fff" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.134 15.412a.66.66 0 01.474-.197.66.66 0 01.474.197l2.903 2.903a.66.66 0 01.197.474.665.665 0 01-.197.474l-.948.949-.002-.001c-.863.867.032 3.157 1.993 5.121 1.963 1.966 4.258 2.854 5.12 1.991l.052.052-.052-.052.95-.948a.674.674 0 01.948 0l2.902 2.905c.*************.195.475a.67.67 0 01-.195.474l-.945.946-.003.003c-2.162 2.155-7.228.59-11.317-3.5-4.094-4.089-5.657-9.16-3.498-11.32l.949-.946z"
        fill="#E87722"
      />
      <Path
        d="M24.674 23.618a7.951 7.951 0 110-15.903 7.951 7.951 0 010 15.903z"
        fill="#F3BB90"
      />
      <Path
        d="M16.723 22.997v-6.711c0-.343.231-.62.514-.62h5.586c.46 0 .688.666.363 1.057L17.6 23.437c-.323.389-.877.112-.877-.44z"
        fill="#F3BB90"
      />
      <Path
        d="M23.391 15.667a.494.494 0 01-.988 0 .495.495 0 11.988 0zM25.169 15.667a.497.497 0 11-.494-.497.497.497 0 01.494.497zM26.945 15.667a.494.494 0 01-.494.494.497.497 0 110-.991.499.499 0 01.494.497z"
        fill="#183028"
      />
      <Path
        d="M24.671 21.3a5.608 5.608 0 01-4.694-2.517.582.582 0 01.226-.837l.168-.086a.58.58 0 01.746.197 4.262 4.262 0 003.554 1.892 4.26 4.26 0 003.815-2.337c.231-.452.377-.932.437-1.432a.578.578 0 01.574-.514h.189c.345 0 .62.3.582.643a5.64 5.64 0 01-.58 1.923 5.609 5.609 0 01-5.017 3.068zM19.849 15.665h-.189c-.346 0-.62-.3-.583-.64a5.543 5.543 0 01.58-1.92 5.603 5.603 0 015.014-3.074c1.912 0 3.657.949 4.695 2.517a.582.582 0 01-.226.838l-.169.085a.574.574 0 01-.742-.194 4.277 4.277 0 00-3.786-1.889 4.327 4.327 0 00-3.732 2.643 4.182 4.182 0 00-.291 1.117.576.576 0 01-.572.518z"
        fill="#fff"
      />
      <Rect
        x={1.5}
        y={1}
        width={38}
        height={38}
        rx={19}
        stroke="#FAE4D3"
        strokeWidth={2}
      />
    </Svg>
  );
}
