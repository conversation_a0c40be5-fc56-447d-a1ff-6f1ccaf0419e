import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function EmptyProtectionScoreChart(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 99}
      height={props.height || 99}
      viewBox="0 0 99 99"
      fill="none">
      <Circle cx="49.5" cy="49" r="43" fill="#CED8D6" />
      <Path
        d="M37.62 44.144V41.192H47.376V44.144H37.62ZM50.6278 44.144V41.192H60.3838V44.144H50.6278Z"
        fill="white"
      />
      <Path
        d="M36.774 72H35.5L40.302 62.074H41.576L36.774 72ZM44.1183 72V65.602H41.9203V64.636C42.555 64.6173 43.0776 64.454 43.4883 64.146C43.899 63.8287 44.1556 63.4227 44.2583 62.928H45.4623V72H44.1183ZM48.5639 67.464C48.5639 68.864 48.8299 69.844 49.3619 70.404C49.5392 70.5907 49.7399 70.7353 49.9639 70.838C50.1972 70.9407 50.4772 70.992 50.8039 70.992C51.1305 70.992 51.4059 70.9407 51.6299 70.838C51.8632 70.7353 52.0685 70.5907 52.2459 70.404C52.7779 69.844 53.0439 68.864 53.0439 67.464C53.0439 66.064 52.7779 65.084 52.2459 64.524C52.0685 64.3373 51.8632 64.1927 51.6299 64.09C51.4059 63.9873 51.1305 63.936 50.8039 63.936C50.4772 63.936 50.1972 63.9873 49.9639 64.09C49.7399 64.1927 49.5392 64.3373 49.3619 64.524C48.8299 65.084 48.5639 66.064 48.5639 67.464ZM47.2059 67.464C47.2059 66.8387 47.2665 66.246 47.3879 65.686C47.5092 65.126 47.7192 64.622 48.0179 64.174C48.3165 63.726 48.6945 63.3713 49.1519 63.11C49.6092 62.8487 50.1599 62.718 50.8039 62.718C51.4479 62.718 51.9985 62.8487 52.4559 63.11C52.9132 63.3713 53.2912 63.726 53.5899 64.174C53.8885 64.622 54.0985 65.126 54.2199 65.686C54.3412 66.246 54.4019 66.8387 54.4019 67.464C54.4019 68.0893 54.3412 68.682 54.2199 69.242C54.0985 69.802 53.8885 70.306 53.5899 70.754C53.2912 71.202 52.9132 71.5567 52.4559 71.818C51.9985 72.0793 51.4479 72.21 50.8039 72.21C50.1599 72.21 49.6092 72.0793 49.1519 71.818C48.6945 71.5567 48.3165 71.202 48.0179 70.754C47.7192 70.306 47.5092 69.802 47.3879 69.242C47.2665 68.682 47.2059 68.0893 47.2059 67.464ZM57.1635 67.464C57.1635 68.864 57.4295 69.844 57.9615 70.404C58.1388 70.5907 58.3395 70.7353 58.5635 70.838C58.7968 70.9407 59.0768 70.992 59.4035 70.992C59.7301 70.992 60.0055 70.9407 60.2295 70.838C60.4628 70.7353 60.6681 70.5907 60.8455 70.404C61.3775 69.844 61.6435 68.864 61.6435 67.464C61.6435 66.064 61.3775 65.084 60.8455 64.524C60.6681 64.3373 60.4628 64.1927 60.2295 64.09C60.0055 63.9873 59.7301 63.936 59.4035 63.936C59.0768 63.936 58.7968 63.9873 58.5635 64.09C58.3395 64.1927 58.1388 64.3373 57.9615 64.524C57.4295 65.084 57.1635 66.064 57.1635 67.464ZM55.8055 67.464C55.8055 66.8387 55.8661 66.246 55.9875 65.686C56.1088 65.126 56.3188 64.622 56.6175 64.174C56.9161 63.726 57.2941 63.3713 57.7515 63.11C58.2088 62.8487 58.7595 62.718 59.4035 62.718C60.0475 62.718 60.5981 62.8487 61.0555 63.11C61.5128 63.3713 61.8908 63.726 62.1895 64.174C62.4881 64.622 62.6981 65.126 62.8195 65.686C62.9408 66.246 63.0015 66.8387 63.0015 67.464C63.0015 68.0893 62.9408 68.682 62.8195 69.242C62.6981 69.802 62.4881 70.306 62.1895 70.754C61.8908 71.202 61.5128 71.5567 61.0555 71.818C60.5981 72.0793 60.0475 72.21 59.4035 72.21C58.7595 72.21 58.2088 72.0793 57.7515 71.818C57.2941 71.5567 56.9161 71.202 56.6175 70.754C56.3188 70.306 56.1088 69.802 55.9875 69.242C55.8661 68.682 55.8055 68.0893 55.8055 67.464Z"
        fill="#183028"
      />
      <Circle cx="49.5" cy="49" r="47" stroke="#EDEFF0" stroke-width="4" />
    </Svg>
  );
}
