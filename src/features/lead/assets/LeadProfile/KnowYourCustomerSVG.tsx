import { View, Text } from 'react-native';
import React from 'react';
import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SvgProps } from 'react-native-svg';
import { IconProps } from 'cube-ui-components/dist/cjs/components/Button/buttonRenderUtils';

export default function KnowYourCustomerSVG(props: Omit<IconProps, 'source'>) {
  const iconWidth = props.width ?? 329;
  const iconHeight = props.height ?? 183;
  return (
    <Svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 329 183"
      fill="none"
      {...props}>
      <Path
        d="M227.646 53.17l.7 9.993-.33.308a8.708 8.708 0 01-11.813.164l-.165-7.51 11.608-2.955z"
        fill="#F8E3D2"
      />
      <Path
        d="M231.309 32.28l-.844 16.518c-1.647-.062-2.614 1.703-2.305 3.242a3.048 3.048 0 00-.268 1.21c-2.861 2.053-8.068 4.7-14.757 3.428-10.496-2.011-4.157-22.224-4.157-22.224l22.331-2.175z"
        fill="#FBE2D0"
      />
      <Path
        d="M236.27 40.86c3.314-7.1-2.182-15.165-4.898-17.279-4.117-3.221-8.562-3.734-15.869-1.764-2.799.759-7.038 1.334-10.393 5.663-5.125 6.628 2.511 9.419 2.511 9.419s6.853 2.524 14.942-.636c1.111-.431 2.428-.185 3.128.759 1.935 2.627 1.955 16.416 1.955 16.416l2.697-2.216s2.346-2.688 5.927-10.363z"
        fill="#183028"
      />
      <Path
        d="M230.299 46.05a2.548 2.548 0 01-4.7.033 2.525 2.525 0 01.532-2.767 2.545 2.545 0 012.769-.569 2.562 2.562 0 011.378 1.366 2.55 2.55 0 01.021 1.938zM205.785 132.932l-3.56-.287-1.359 4.062 5.29 2.196-.371-5.971z"
        fill="#FBE2D0"
      />
      <Path
        d="M202.267 135.886l.556-1.683a3.464 3.464 0 00-2.202-4.37l-9.406-3.119a3.489 3.489 0 00-2.646.196 3.477 3.477 0 00-1.738 1.999 5.236 5.236 0 00.302 3.989 5.26 5.26 0 003.033 2.619l7.718 2.544a3.43 3.43 0 002.648-.173 3.424 3.424 0 001.735-2.002z"
        fill="#FBE2D0"
      />
      <Path
        d="M200.706 132.129c-.721.615-1.77.41-2.305-.431l-3.561-5.561c-.535-.841-.432-2.093.35-2.627.617-.41 1.77-.41 2.305.431l3.561 5.561c.535.841.391 2.032-.35 2.627zM179.053 62.036l-1.152-3.55-4.487.308v5.992l5.639-2.75z"
        fill="#FBE2D0"
      />
      <Path
        d="M174.753 59.819l1.852-.144a3.643 3.643 0 002.481-1.248 3.621 3.621 0 00.874-2.63l-.782-10.322a3.626 3.626 0 00-1.252-2.473 3.646 3.646 0 00-2.638-.872 5.517 5.517 0 00-3.743 1.896 5.483 5.483 0 00-1.32 3.973l.638 8.475a3.626 3.626 0 001.252 2.474c.729.626 1.677.94 2.638.87z"
        fill="#FBE2D0"
      />
      <Path
        d="M177.777 56.804c-.885-.452-1.111-1.519-.514-2.38l3.951-5.664c.597-.862 1.853-1.273 2.676-.718.658.43 1.111 1.518.514 2.38l-3.951 5.663c-.597.842-1.791 1.17-2.676.719z"
        fill="#FBE2D0"
      />
      <Path
        d="M204.719 76.646s-6.854 26.101-12.72 26.573c-5.001.39-10.167-30.061-11.566-38.947a2.52 2.52 0 00-2.799-2.113l-3.376.41a2.523 2.523 0 00-2.223 2.422c-.349 10.773-.967 58.112 14.84 59.077 17.844 1.087 20.21-12.21 20.21-12.21l-2.366-35.212z"
        fill="#0095A8"
      />
      <Path
        d="M252.156 137.467V78.575a4.099 4.099 0 00-.741-2.34c-12.02-17.298-26.365-14.446-28.855-13.79-.25.062-.505.103-.762.124-2.367.164-14.695 1.56-17 13.707a4.71 4.71 0 00-.062.76c.021 9.131.515 51.361.68 65.992a4.075 4.075 0 001.214 2.86 4.101 4.101 0 002.881 1.182h33.033a9.65 9.65 0 006.795-2.822 9.594 9.594 0 002.817-6.781z"
        fill="#8DD4DF"
      />
      <Path
        d="M252.117 77.324s13.995 32.196-3.869 48.653c-17.865 16.457-42.089 13.646-42.089 13.646v-9.275s21.919-1.847 28.937-16.457c7.018-14.61-2.799-27.25-2.799-27.25"
        fill="#0095A8"
      />
      <Path
        d="M237.586 104.16a25.173 25.173 0 01-2.491 9.747c-5.886 12.251-23.03 15.575-28.422 16.334a1.902 1.902 0 00-1.647 1.888v5.889a1.904 1.904 0 001.832 1.908c6.133.247 26.426-.143 41.41-13.953 6.483-5.971 8.747-13.995 9.035-21.813h-19.717z"
        fill="#0095A8"
      />
      <Path
        d="M223.2 42.357a.824.824 0 01-1.111.328.793.793 0 01-.374-.496.77.77 0 01.107-.612.793.793 0 011.35-.133.809.809 0 01.138.292.794.794 0 01-.11.621zM212.682 42.502a.822.822 0 01-1.111.328.792.792 0 01-.374-.496.785.785 0 01.106-.612.81.81 0 01.479-.413.8.8 0 01.872.28.79.79 0 01.028.913z"
        fill="#183028"
      />
      <Path
        d="M217.189 49.353c.535-.164 1.008-.574 1.091-.841a.282.282 0 00-.206-.349.265.265 0 00-.283.091.253.253 0 00-.046.094c-.216.26-.513.441-.844.513a1.178 1.178 0 01-1.008-.308c-.165-.144-.535-.78-.186-3.283.206-1.437.556-2.77.556-2.79a.288.288 0 00-.03-.218.288.288 0 00-.526.074c0 .02-.349 1.374-.576 2.852-.308 2.072-.185 3.324.371 3.796.217.195.478.335.761.41.307.072.627.057.926-.04zM216.614 52.411c-.34.005-.68-.036-1.009-.123a5.005 5.005 0 01-1.831-.923.277.277 0 01.37-.41c.103.081 2.552 2.236 5.043-.411a.278.278 0 01.467.176.28.28 0 01-.056.193c-1.05 1.129-2.099 1.478-2.984 1.498zM216.737 53.868a1.717 1.717 0 01-.885-.205.283.283 0 01.309-.472c.021.02.494.287 1.255-.062a.287.287 0 01.226-.018.283.283 0 01.19.265.29.29 0 01-.023.113.295.295 0 01-.067.094.294.294 0 01-.099.06 2.783 2.783 0 01-.906.225z"
        fill="#E77825"
      />
      <Path
        d="M127.41 68.033c-14.294 0-25.872 11.586-25.872 25.88 0 7.573 3.257 14.381 8.433 19.119.247.207.494.422.74.645 4.046 3.687 6.586 8.99 6.586 14.891v2.253h20.226v-2.253a20.059 20.059 0 016.585-14.891c.247-.223.494-.438.741-.645 5.175-4.738 8.432-11.546 8.432-19.12.008-14.293-11.578-25.87-25.871-25.879z"
        fill="#E87722"
      />
      <Path
        d="M144.761 93.912c0 9.579-7.772 17.343-17.351 17.343-9.587 0-17.351-7.764-17.351-17.343 0-9.58 7.764-17.36 17.351-17.36 9.579 0 17.351 7.78 17.351 17.36z"
        fill="#fff"
      />
      <Path
        d="M138.932 142.837c0 .422-.159.844-.486 1.163-.318.318-.74.486-1.162.486h-19.756c-.422 0-.844-.16-1.163-.486a1.666 1.666 0 01-.486-1.163v-9.595a4.127 4.127 0 014.125-4.125h14.803a4.126 4.126 0 014.125 4.125v9.595z"
        fill="#F3BB90"
      />
      <Path d="M138.94 132.66h-23.053v1.704h23.053v-1.704z" fill="#F3BB90" />
      <Path d="M125.252 97.465h-4.3v31.661h4.3V97.465z" fill="#183028" />
      <Path
        d="M133.86 97.465h-10.758v31.661h10.75l.008-31.661z"
        fill="#183028"
      />
      <Path
        d="M133.868 97.465l-4.316-7.453h-4.292l-4.301 7.453h12.909z"
        fill="#F3BB90"
      />
      <Path
        d="M129.552 90.011l-1.577-2.731a.658.658 0 00-1.139 0l-1.568 2.731h4.284zM144.578 66.246a.944.944 0 01-.494-.135.98.98 0 01-.358-1.346l2.978-5.152a.98.98 0 011.346-.358.98.98 0 01.358 1.345l-2.978 5.152a.967.967 0 01-.852.494zM157.183 78.844a.988.988 0 01-.501-1.84l5.152-2.978a.986.986 0 011.345.359.986.986 0 01-.358 1.345l-5.152 2.979a.899.899 0 01-.486.135zM167.742 96.06h-5.956a.987.987 0 110-1.974h5.956a.987.987 0 010 1.975zM92.946 96.06H86.99a.992.992 0 01-.988-.987c0-.541.446-.987.988-.987h5.956a.987.987 0 110 1.975zM97.557 78.844a.944.944 0 01-.494-.136l-5.152-2.978a.98.98 0 01-.358-1.346.98.98 0 011.346-.358l5.152 2.978a.98.98 0 01.358 1.346.976.976 0 01-.852.494zM110.154 66.246a.989.989 0 01-.86-.493l-2.978-5.152a.986.986 0 01.358-1.346.987.987 0 011.346.358l2.978 5.152a.986.986 0 01-.844 1.481zM127.37 61.636a.987.987 0 01-.987-.987v-5.956a.987.987 0 111.975 0v5.956a.993.993 0 01-.988.987z"
        fill="#183028"
      />
      <Path
        d="M144.578 66.246a.944.944 0 01-.494-.135.98.98 0 01-.358-1.346l2.978-5.152a.98.98 0 011.346-.358.98.98 0 01.358 1.345l-2.978 5.152a.967.967 0 01-.852.494zM157.183 78.844a.988.988 0 01-.501-1.84l5.152-2.978a.986.986 0 011.345.359.986.986 0 01-.358 1.345l-5.152 2.979a.899.899 0 01-.486.135zM92.946 96.06H86.99a.992.992 0 01-.988-.987c0-.541.446-.987.988-.987h5.956a.987.987 0 110 1.975zM167.742 96.06h-5.956a.987.987 0 110-1.974h5.956a.987.987 0 010 1.975zM97.557 78.844a.944.944 0 01-.494-.136l-5.152-2.978a.98.98 0 01-.358-1.346.98.98 0 011.346-.358l5.152 2.978a.98.98 0 01.358 1.346.976.976 0 01-.852.494zM110.154 66.246a.989.989 0 01-.86-.493l-2.978-5.152a.986.986 0 01.358-1.346.987.987 0 011.346.358l2.978 5.152a.986.986 0 01-.844 1.481zM127.37 61.636a.987.987 0 01-.987-.987v-5.956a.987.987 0 111.975 0v5.956a.993.993 0 01-.988.987z"
        fill="#183028"
      />
      <G clipPath="url(#clip0_20654_9405)">
        <Path
          d="M166.268 60.658v82.914a3.295 3.295 0 01-1.174 2.474 4.232 4.232 0 01-2.829 1.024h-78.63a4.302 4.302 0 01-2.829-1.024 3.281 3.281 0 01-1.174-2.474V60.217c27.205 0 86.636.44 86.636.44z"
          fill="#E87722"
        />
        <Path
          d="M178.276 82.864l-38.126 38.335 3.672 3.69 38.126-38.336-3.672-3.69zM182.047 86.593l-38.133 38.329 3.671 3.69 38.133-38.329-3.671-3.69z"
          fill="#183028"
        />
        <Path
          d="M185.618 90.24l-38.126 38.336 3.672 3.69L189.29 93.93l-3.672-3.69z"
          fill="#183028"
        />
        <Path
          d="M151.159 132.295l-10.03 2.688-3.664-3.669 2.688-10.082 11.006 11.063z"
          fill="#fff"
        />
        <Path
          d="M151.159 132.295l-3.664-3.697-7.597 5.161 1.231 1.223 10.03-2.687z"
          fill="#fff"
        />
        <Path
          d="M141.129 134.984l-3.678.995a.786.786 0 01-.774-.204.802.802 0 01-.202-.777l.99-3.697 3.664 3.683z"
          fill="#183028"
        />
        <Path
          d="M189.271 86.598l-3.672 3.69 3.671 3.69 3.672-3.69-3.671-3.69z"
          fill="#FBB684"
        />
        <Path
          d="M181.936 79.225l-3.672 3.69 7.341 7.38 3.672-3.689-7.341-7.381z"
          fill="#FBB684"
        />
        <Path
          d="M193.812 89.41l-.849.853-3.664-3.683.849-.853a7.803 7.803 0 002.166-4.201 7.832 7.832 0 00-.652-4.686 7.774 7.774 0 012.851 2.334 7.83 7.83 0 011.211 7.078 7.812 7.812 0 01-1.912 3.158z"
          fill="#E87722"
        />
        <Path
          d="M190.148 85.712l-.849.853-7.342-7.38.849-.853a7.736 7.736 0 015.503-2.29 7.635 7.635 0 013.353.769 7.662 7.662 0 01.764 3.37 7.794 7.794 0 01-2.278 5.531z"
          fill="#E87722"
        />
        <Path
          d="M109.793 130.76H95.519c-.578 0-1.132.23-1.54.641a2.197 2.197 0 000 3.097c.408.411.962.641 1.54.641h14.274c.578 0 1.132-.23 1.541-.641a2.199 2.199 0 000-3.097 2.173 2.173 0 00-1.541-.641zM127.251 119.824H95.519a2.167 2.167 0 00-1.55.652 2.187 2.187 0 00-.628 1.566c0 .581.23 1.138.638 1.549.408.411.962.641 1.54.641h31.732c.578 0 1.132-.23 1.54-.641.409-.411.639-.968.639-1.549a2.21 2.21 0 00-.628-1.566 2.166 2.166 0 00-1.551-.652zM112.113 112.887c11.032 0 19.976-8.99 19.976-20.079 0-11.088-8.944-20.078-19.976-20.078-11.032 0-19.975 8.99-19.975 20.078 0 11.089 8.943 20.079 19.975 20.079z"
          fill="#fff"
        />
        <Path
          d="M121.677 95.866l-.495-.143-5.56-2.047h-7.215a370.012 370.012 0 01-5.319 2.047c-.184 0-.382.114-.566.171a3.703 3.703 0 00-2.035 1.315 3.737 3.737 0 00-.795 2.297v2.474c.015.803.344 1.569.916 2.13a3.031 3.031 0 002.14.87h18.702c.807 0 1.581-.322 2.151-.895a3.062 3.062 0 00.891-2.162v-2.431a3.741 3.741 0 00-.784-2.302 3.708 3.708 0 00-2.031-1.324z"
          fill="#FBB684"
        />
        <Path
          d="M111.929 94.5a7.166 7.166 0 01-5.067-2.116 7.24 7.24 0 01-2.105-5.093v-.996a7.24 7.24 0 012.105-5.093 7.167 7.167 0 015.067-2.116 7.17 7.17 0 015.068 2.116 7.245 7.245 0 012.105 5.093v.996a7.26 7.26 0 01-2.109 5.09 7.184 7.184 0 01-5.064 2.119z"
          fill="#FBB684"
        />
        <Path
          d="M166.268 60.9H79.632v-3.897a7.512 7.512 0 012.19-5.299 7.439 7.439 0 015.28-2.18h71.753c1.974 0 3.866.788 5.262 2.19a7.5 7.5 0 012.179 5.289v3.896h-.028z"
          fill="#183028"
        />
        <Path
          d="M160.27 56.747h-1.599a.72.72 0 01-.721-.725v-1.963a.714.714 0 01.211-.513.725.725 0 01.51-.212h1.599a.72.72 0 01.721.725v1.963a.736.736 0 01-.721.725zM154.144 56.747h-1.627a.715.715 0 01-.519-.208.731.731 0 01-.216-.517v-1.963a.72.72 0 01.721-.725h1.627a.725.725 0 01.722.725v1.963a.725.725 0 01-.708.725zM148.004 56.747h-1.627a.72.72 0 01-.721-.725v-1.963a.714.714 0 01.211-.513.725.725 0 01.51-.212h1.627a.725.725 0 01.722.725v1.963a.725.725 0 01-.722.725zM159.265 70.525h-14.274a2.17 2.17 0 00-1.54.642 2.194 2.194 0 000 3.097c.408.41.962.641 1.54.641h14.274c.578 0 1.132-.23 1.541-.641.409-.411.638-.968.638-1.549a2.21 2.21 0 00-.642-1.544 2.188 2.188 0 00-1.537-.646zM159.265 80.693h-14.274a2.17 2.17 0 00-1.54.642 2.195 2.195 0 000 3.097c.408.41.962.641 1.54.641h14.274c.578 0 1.132-.23 1.541-.641.409-.411.638-.968.638-1.549 0-.58-.229-1.138-.638-1.548a2.175 2.175 0 00-1.541-.642z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_20654_9405">
          <Path
            fill="#fff"
            transform="translate(79.632 49.523)"
            d="M0 0H116.458V97.5462H0z"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
