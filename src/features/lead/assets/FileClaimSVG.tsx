import React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function FileClaimSVG({
  width,
  height,
}: {
  width?: number;
  height?: number;
}) {
  return (
    <Svg
      width={width ?? 22}
      height={height ?? 30}
      viewBox="0 0 22 30"
      fill="none">
      <Path
        d="M1.871 2.757a.868.868 0 00-.657.286.984.984 0 00-.285.657v24.457c0 .229.085.486.285.657a.985.985 0 00.657.286H20.13a.868.868 0 00.657-.286.984.984 0 00.285-.657V3.7a.868.868 0 00-.285-.657.984.984 0 00-.657-.286H1.87z"
        fill="#F3BB90"
      />
      <Path
        d="M3.129 26.386V4.957c0-.286.228-.514.514-.514h14.743c.286 0 .514.228.514.514v21.429a.512.512 0 01-.514.514H3.643a.531.531 0 01-.514-.514z"
        fill="#E87722"
      />
      <Path
        d="M16.729 18.672H5.3a.512.512 0 00-.514.514c0 .285.228.514.514.514H16.73a.512.512 0 00.514-.514.512.512 0 00-.514-.514zM16.729 15.129H5.3a.512.512 0 00-.514.514c0 .286.228.514.514.514H16.73a.512.512 0 00.514-.514.512.512 0 00-.514-.514zM10.5 22.386H5.3a.512.512 0 00-.514.514c0 .286.228.514.514.514h5.2a.512.512 0 00.514-.514.512.512 0 00-.514-.514z"
        fill="#fff"
      />
      <Path
        d="M13.329 2.757a.52.52 0 01-.515-.428A1.863 1.863 0 0011.014.9c-.885 0-1.628.6-1.8 1.429a.52.52 0 01-.514.428H8.13a.512.512 0 00-.515.514v1.115c0 .285.229.514.515.514H13.9a.512.512 0 00.514-.514V3.27a.512.512 0 00-.514-.514h-.571zm-3.115 0c0-.428.343-.8.8-.8.429 0 .8.372.8.8h-1.6z"
        fill="#183028"
      />
      <Path
        d="M4.843 10.643c0-.857.657-1.457 1.428-1.457.829 0 1.2.543 1.286.943l-.571.171c-.057-.2-.229-.514-.715-.514-.371 0-.8.257-.8.857 0 .514.372.829.8.829.486 0 .686-.315.715-.515l.6.172c-.086.371-.457.971-1.315.971-.8-.028-1.428-.6-1.428-1.457zM7.957 12.014V9.186h.6v2.828h-.6zM9.5 10.9l.457-.057c.115-.029.143-.057.143-.143 0-.114-.085-.2-.286-.2-.2 0-.314.143-.342.286l-.515-.114c.029-.286.286-.658.857-.658.629 0 .858.343.858.743v.943c0 .143.028.286.028.314h-.543c0-.028-.028-.085-.028-.228a.614.614 0 01-.543.286c-.429 0-.686-.286-.686-.6.029-.343.286-.543.6-.572zm.6.343v-.086l-.371.057c-.115.029-.229.086-.229.229 0 .114.057.2.229.2.2 0 .371-.086.371-.4zM11.472 9.129c.2 0 .342.143.342.343a.33.33 0 01-.342.342.33.33 0 01-.343-.342c0-.2.143-.343.343-.343zm-.286 2.885v-1.942h.6v1.942h-.6zM12.3 12.014v-1.942h.571v.228c.086-.171.343-.286.572-.286.286 0 .486.115.571.315.143-.229.343-.315.6-.315.372 0 .714.229.714.743v1.257h-.57V10.9c0-.171-.087-.314-.315-.314-.2 0-.314.171-.314.343v1.114h-.6v-1.114c0-.172-.086-.315-.315-.315-.2 0-.314.172-.314.343v1.115h-.6v-.058zM16.157 11.357c0 .143.114.257.314.257.143 0 .229-.085.229-.171 0-.057-.057-.143-.2-.171l-.229-.058c-.428-.085-.571-.314-.571-.6 0-.342.314-.657.743-.657.571 0 .771.343.771.572l-.485.085c-.029-.142-.086-.228-.286-.228-.114 0-.2.057-.2.171 0 .086.057.143.143.143l.257.057c.4.086.6.315.6.6 0 .343-.257.657-.772.657-.6 0-.8-.4-.828-.6l.514-.057z"
        fill="#fff"
      />
    </Svg>
  );
}
