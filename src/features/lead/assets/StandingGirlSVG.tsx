import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export const STANDING_GIRL_WIDTH = 108;
export const STANDING_GIRL_Height = 232;

function StandingGirlSVG(props: SvgIconProps) {
  return (
    <Svg
      width={STANDING_GIRL_WIDTH}
      height={STANDING_GIRL_Height}
      viewBox="0 0 107 230"
      fill="none"
      {...props}>
      <Path
        d="M29.767 172.887s-8.533 41.233-5.57 45.498c2.963 4.147 7.703 8.412 7.822 10.664.118 2.251-15.525-2.844-16.592-5.332-1.066-2.37-.237-5.924.119-16.233.355-10.308 2.607-31.398 2.607-31.398l11.614-3.199zM48.491 173.36s-1.778 42.063 1.778 45.736c3.674 3.673 9.007 7.109 9.362 9.36.474 2.133-15.762-.356-17.303-2.607-1.422-2.251-1.185-5.806-2.607-16.114-1.422-10.189-2.489-31.398-2.489-31.398l11.259-4.977z"
        fill="#FAE1CE"
      />
      <Path
        d="M21.945 90.896h35.08c6.754 13.507 10.191 27.844 10.191 42.299v59.243c0 3.436-3.555 6.279-7.821 6.279h-38.99c-4.267 0-7.823-2.843-7.823-6.279v-59.243c0-14.337 3.2-28.673 9.363-42.299z"
        fill="#F3BB90"
      />
      <Path
        d="M89.14 86.63l2.963 1.778-1.304 3.91-5.333-1.304 3.674-4.384z"
        fill="#FAE1CE"
      />
      <Path
        d="M90.208 90.896l.592-1.66a3.359 3.359 0 014.267-2.132l9.125 2.962a3.357 3.357 0 012.133 4.266c-.829 2.606-3.792 4.028-6.4 3.199l-7.466-2.488c-1.896-.474-2.844-2.37-2.251-4.147z"
        fill="#FAE1CE"
      />
      <Path
        d="M93.526 88.881c.237.948 1.066 1.303 2.014.948l5.926-2.251c.948-.356 1.541-1.422 1.304-2.251-.237-.711-1.067-1.304-2.015-.948l-5.926 2.25c-.948.356-1.422 1.423-1.303 2.252zM19.1 118.028l1.423 3.199-3.318 2.488-3.674-4.147 5.57-1.54z"
        fill="#FAE1CE"
      />
      <Path
        d="M17.56 122.175l1.304-1.067c1.422-1.185 3.555-.829 4.74.593l5.807 7.583c1.067 1.422.83 3.554-.592 4.739-2.252 1.659-5.452 1.303-7.11-.948l-4.86-6.161c-1.066-1.422-.71-3.555.711-4.739z"
        fill="#FAE1CE"
      />
      <Path
        d="M21.47 122.413c-.355.829.238 1.777 1.186 2.014l6.162 1.422c.948.237 2.015-.237 2.252-1.185.237-.711-.119-1.777-1.067-1.896l-6.162-1.421c-1.067-.237-2.015.237-2.37 1.066z"
        fill="#FAE1CE"
      />
      <Path
        d="M36.877 2.98c-2.014 1.184-3.674 2.724-4.385 4.502-.83 2.132-1.066 4.976-3.318 6.516-1.777 1.185-4.503.948-6.162 2.37-2.015 1.659-1.304 4.858-2.963 6.872-2.252 2.962-7.822 2.133-10.43 4.858-2.132 2.37-.947 6.28 1.778 8.057 2.726 1.777 6.4 1.777 9.6 1.185 1.303-.237 2.726-.592 4.03-.474 1.54.118 2.843.948 4.147 1.54 5.689 2.607 12.325 2.726 18.725 1.896 3.2-.355 6.874-1.185 8.414-3.791 1.54-2.489.711-5.806 1.778-8.531.948-2.37 3.081-4.147 3.91-6.517 1.186-3.555-1.066-7.228-2.606-10.664-.712-1.658-1.304-3.554-2.49-4.976C54.418 2.505 49.44-.101 44.937.254c-2.37.119-5.451 1.067-8.059 2.725z"
        fill="#183028"
      />
      <Path
        d="M40.196 40.185s20.028 3.791 26.31 13.507c6.28 9.716 8.769 14.692 11.969 18.01 2.251 2.37 8.296 7.464 11.377 9.834 1.066.83 1.303 2.251.83 3.436l-2.845 6.161c-.83 1.659-2.963 2.133-4.385 1.067-6.28-4.977-15.999-9.242-23.465-20.38 0 0 1.067 3.792-1.778 19.195-.118.83-.118 1.54-.83 1.895-5.095 1.778-36.738.593-36.5-.355.829-2.607-.356-24.763-.356-24.763s-8.533 15.284-8.415 22.512c0 5.924 7.111 20.972 9.481 25.829.474.83.119 1.896-.83 2.252l-7.11 3.436c-.83.355-1.896 0-2.252-.83C8.91 116.015 1.442 99.546.14 85.801c-1.66-16.35 11.97-34.242 19.08-38.863 9.244-6.398 20.977-6.753 20.977-6.753z"
        fill="#E87722"
      />
      <Path
        d="M56.55 24.307s1.303-1.185 2.014-.474c1.54 2.014-1.067 3.554-2.489 3.554l.474-3.08z"
        fill="#FAE1CE"
      />
      <Path
        d="M48.728 3.453c-6.044-1.185-12.206 2.606-13.984 8.412a12.093 12.093 0 00-.355 5.806c.592 4.62 1.066 8.294 2.133 11.137 1.066 2.963 1.066 6.162-.119 9.005L35.1 40.775c6.044 5.214 9.836 2.607 9.836 2.607l2.133-7.227c7.703 1.066 10.31-13.152 11.377-17.18 0-.12.119-.238.119-.475 1.659-6.753-2.726-13.744-9.837-15.047z"
        fill="#FAE1CE"
      />
      <Path
        d="M44.698 15.301c-2.725 1.896-5.57 3.792-7.821 6.28-.237.237-2.134 2.251-1.778 2.725-1.54-2.014-2.252-4.502-2.726-6.99-.83-4.266.83-8.768 3.911-11.73 3.081-2.963 7.23-3.792 11.377-3.081 4.385.71 8.77 3.436 10.548 7.701 1.659 4.029.592 8.413-.119 12.441.83-4.62-4.385-9.597-7.703-12.085.118.118-3.674 3.199-3.91 3.317l-1.779 1.422z"
        fill="#183028"
      />
      <Path
        d="M37.114 21.937c-.118-.474-.355-.83-.71-1.067-.712-.592-2.134-1.303-3.32-.473-1.777 1.066.12 5.687 3.556 4.502-.118 0 .948-.71.474-2.962z"
        fill="#FAE1CE"
      />
      <Path
        d="M43.04 20.04c.118.475.593.712.948.593.474-.118.711-.592.593-.948-.119-.474-.593-.71-.949-.592-.474 0-.71.474-.592.948zM52.994 22.767c.119.474.593.71.949.592.474-.118.71-.592.592-.948-.118-.474-.592-.71-.948-.592s-.711.474-.593.948z"
        fill="#183028"
      />
      <Path
        d="M48.491 28.572c-.474-.237-.83-.83-.83-1.066 0-.119.12-.238.238-.238s.237.119.237.238c0 .118.237.473.71.71.356.119.712.119 1.067 0 .237-.118.711-.592.948-3.08.119-1.422.119-2.725.119-2.844 0-.118.118-.237.237-.237s.237.119.237.237c0 0 0 1.422-.118 2.844-.238 2.014-.712 3.199-1.304 3.554-.356.119-.593.237-.83.237-.118-.118-.474-.237-.71-.355zM47.543 31.416c.356.119.711.119.948.119 1.067 0 1.897-.355 2.015-.474.118-.118.237-.237.118-.355-.118-.119-.236-.237-.355-.119-.119.119-2.963 1.54-4.74-1.659-.119-.118-.238-.237-.356-.118-.119.118-.237.237-.119.355.712 1.422 1.66 2.015 2.49 2.252zM47.07 32.719c.473.118.829 0 .947 0 .119 0 .237-.237.119-.355-.119-.119-.237-.237-.356-.119 0 0-.592.119-1.185-.355-.118-.119-.237-.119-.355 0-.119.118-.119.237 0 .355.355.356.592.474.83.474z"
        fill="#F3BB90"
      />
    </Svg>
  );
}

export default StandingGirlSVG;
