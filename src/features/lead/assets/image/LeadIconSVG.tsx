import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function LeadIconSVG(props: SvgIconProps) {
  const iconHeight = props.size ?? props.height ?? 120;
  const iconWidth = props.size ?? props.width ?? 120;
  return (
    <Svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 120 120"
      fill="none">
      <Circle
        cx="60"
        cy="60"
        r="51.5"
        fill="#FAE4D3"
        stroke="white"
        stroke-width="9"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M60.0002 46.1168C57.1884 46.1168 54.9002 48.4041 54.9002 51.2168C54.9002 54.0294 57.1884 56.3168 60.0002 56.3168C62.8128 56.3168 65.1002 54.0294 65.1002 51.2168C65.1002 48.4041 62.8128 46.1168 60.0002 46.1168ZM60.0002 59.7168C55.3133 59.7168 51.5002 55.9037 51.5002 51.2168C51.5002 46.5299 55.3133 42.7168 60.0002 42.7168C64.6871 42.7168 68.5002 46.5299 68.5002 51.2168C68.5002 55.9037 64.6871 59.7168 60.0002 59.7168ZM72.7502 75.0168H71.9002V71.406C71.9002 67.7671 68.9498 64.8168 65.311 64.8168H54.6894C51.0505 64.8168 48.1002 67.7671 48.1002 71.406V75.0168H47.2502C45.8417 75.0168 44.7002 73.8752 44.7002 72.4668V71.406C44.7002 65.8895 49.1729 61.4168 54.6894 61.4168H65.311C70.8275 61.4168 75.3002 65.8895 75.3002 71.406V72.4668C75.3002 73.8752 74.1586 75.0168 72.7502 75.0168Z"
        fill="#E87722"
      />
    </Svg>
  );
}
