import { TouchableOpacity } from 'react-native';
import React, { Fragment } from 'react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import {
  demoLeadProfileInApp,
  demoLeadProfileSi,
} from 'features/lead/tablet/demoData';
import {
  LeadProfileTabs,
  RootStackParamList,
  leadProfileTabsArray,
} from 'types';
import useTabConfigForLeadProfile from 'features/lead/my/LeadProfile/tablet/useTabConfigForLeadProfile';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  useGetSiProposalsForLead,
  useGetInAppProposalsForLead,
} from 'features/lead/hooks/useGetProposalsForLead';

export default function LeadProfileSideTabBar({
  currentTab,
  setCurrentTab,
}: {
  currentTab: LeadProfileTabs;
  setCurrentTab: React.Dispatch<React.SetStateAction<LeadProfileTabs>>;
}) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation('leadProfile');
  const { tabConfig } = useTabConfigForLeadProfile();
  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();
  const leadId = route?.params?.id ?? '';
  console.log(
    '🤲🤲🤲 ~ file: LeadProfileSideTabBar.tablet.tsx:34 ~ leadId:',
    leadId,
  );

  const { siProposalList } = useGetSiProposalsForLead(leadId, 'newest');
  const { inAppProposalList } = useGetInAppProposalsForLead(leadId, 'newest');

  const siNumber = siProposalList?.length ?? '--';
  const inAppNumber = inAppProposalList?.length ?? '--';

  const numberTagHandler = (currentTab: LeadProfileTabs) => {
    switch (currentTab) {
      case 'si':
        return `(${siNumber})`;
      case 'inApp':
        return `(${inAppNumber})`;
      default:
        return '';
    }
  };

  return (
    <>
      {leadProfileTabsArray.map(tabKey => {
        const TabIcon = tabConfig[tabKey].Icon;
        const isActive = tabKey === currentTab;
        // if (tabKey === 'inApp' || tabKey === 'si') {
        //   return <Fragment key={tabKey} />;
        // }
        return (
          <TouchableOpacity
            key={tabKey}
            style={[
              {
                minHeight: space[12],
                paddingHorizontal: space[3],
                paddingVertical: space[2],
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: space[4],
              },
              isActive && {
                backgroundColor: colors.background,
                borderRadius: borderRadius.medium,
              },
            ]}
            onPress={() => {
              setCurrentTab(tabKey);
            }}>
            <Row gap={space[3]} alignItems="center">
              <TabIcon
                fill={isActive ? colors.primary : colors.secondary}
                size={space[5]}
              />
              <Typography.H7
                fontWeight={isActive ? 'bold' : 'normal'}
                color={isActive ? colors.primary : colors.secondary}>
                {t(`leadProfile.sideBar.${tabConfig[tabKey].key}`) +
                  ' ' +
                  numberTagHandler(tabKey)}
              </Typography.H7>
            </Row>
            <Icon.ChevronRight
              fill={isActive ? colors.primary : colors.secondary}
              size={space[4]}
            />
          </TouchableOpacity>
        );
      })}
    </>
  );
}
