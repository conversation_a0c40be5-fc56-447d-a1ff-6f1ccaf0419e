import React, { useEffect, useMemo, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@emotion/react';
import {
  Typo<PERSON>,
  Box,
  Button,
  Column,
  H6,
  H8,
  Icon,
  Row,
} from 'cube-ui-components';
import styled from '@emotion/native';
import TableListCard, {
  TableRowProps,
} from 'features/lead/tablet/components/TableListCard';
import { Card } from 'features/lead/components/LeadProfile/Card';
import LogActivity from './LogActivity';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList, TransactionsAction } from 'types';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTranslation } from 'react-i18next';
import EmptyActRecord from './EmptyActRecord';
import CubeFonts from 'cube-ui-components/dist/cjs/fonts';
import { country } from 'utils/context';
import useBoundStore from 'hooks/useBoundStore';

export type TableHeaderItem = {
  type: string;
  name: string;
  width: string | number;
  isDate?: boolean;
};

export type FeedbackConfig = {
  type: string;
  label: string;
  icon: React.JSX.Element;
}[];

export type TableConfig = {
  tableHeader: TableHeaderItem[];
  data: ActivitiesDataType[];
};

export type ActivitiesDataType = {
  id: number;
  activityType: string;
  feedback: string;
  details?: string;
  date: string;
};
export type ActivityTypeConfig = {
  type: TransactionsAction;
  label: string;
};

const activityTypeConfig: ActivityTypeConfig[] = [
  { type: 'contact', label: 'Contacted' },
  {
    type: 'appointment',
    label: 'Appointment',
  },
  {
    type: 'illustrate',
    label: 'Illustration',
  },
  { type: 'submit', label: 'Submitted' },
];

export default function LeadProfileDetailsActivitiesTab() {
  const { space, colors, sizes } = useTheme();
  const setSortBy = useBoundStore(
    state => state.leadActions.activities.toggleSort,
  );
  const resetSort = useBoundStore(
    state => state.leadActions.activities.resetSort,
  );
  const sortByNewest = useBoundStore(
    state => state.lead.activities.sortByNewest,
  );
  const sortBy = useMemo(() => {
    return sortByNewest ? 'newest' : 'oldest';
  }, [sortByNewest]);
  const { bottom } = useSafeAreaInsets();
  const { t } = useTranslation('leadProfile');

  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();
  const leadId = route.params.id;

  const { isLoading, isError, data: lead } = useGetLeadByLeadId(leadId);
  const leadActivityRecord = useMemo(
    () =>
      (lead?.transactions ?? [])
        .filter(
          ({ action }) =>
            action === 'contact' ||
            action === 'appointment' ||
            action === 'illustrate' ||
            // action === 'defer' ||
            action === 'not_interested' ||
            action === 'submit',
        )
        // .slice(-5)
        .reverse(),
    [lead],
  );

  const sortedLeadActivityRecord = useMemo(
    () =>
      leadActivityRecord
        .sort((a, b) =>
          sortBy == 'newest'
            ? new Date(b.actionAt).getTime() - new Date(a.actionAt).getTime()
            : new Date(a.actionAt).getTime() - new Date(b.actionAt).getTime(),
        )
        .map((data, idx) => {
          const mappingConfig = descriptionIconConfig.find(config => {
            return config.type === data.action;
          });
          const activityType = mappingConfig
            ? mappingConfig.label == '--'
              ? mappingConfig.label
              : t(mappingConfig.label)
            : '';
          const descriptionContent = mappingConfig
            ? t(mappingConfig.description)
            : '';
          const descriptionIcon = mappingConfig ? (
            data.extra?.feedback == 'notInterested' &&
            'notInterestedIcon' in mappingConfig ? (
              mappingConfig?.notInterestedIcon
            ) : data.extra?.feedback == 'deferred' ? (
              mappingConfig?.deferIcon
            ) : (
              mappingConfig.icon
            )
          ) : (
            <Icon.CallText size={24} />
          );
          return [
            {
              key: 'ActivityType_' + idx,
              content: activityType,
              width: '23%',
              textColor: colors.secondary,
            },
            {
              key: 'Descrition_' + idx,
              content: data.extra?.feedback
                ? t(
                    `leadProfile.activityRecord.feedback.${data.extra.feedback}`,
                  )
                : descriptionContent,
              width: '58%',
              icon: descriptionIcon,
              details: data?.reason ? data?.reason : '',
              subDetails: data?.extra?.feedbackDetails
                ? data?.extra?.feedbackDetails
                : '',
            },
            {
              key: 'Date_' + idx,
              content: dateFormatUtil(data.actionAt),
              width: '19%',
            },
          ] satisfies TableRowProps[];
        }),
    [leadActivityRecord, sortBy],
  );

  const contentListConfig = sortedLeadActivityRecord;

  useEffect(() => {
    return () => {
      resetSort();
    };
  }, []);

  return (
    <Column paddingBottom={bottom + space[6]} paddingTop={space[2]}>
      <Row gap={30} alignItems="center" justifyContent="space-between">
        <Title fontWeight="bold">{t('leadProfile.activities')}</Title>
        <AddLeadActivityButtonWithModal />
      </Row>

      <Row gap={space[3]} style={{ paddingTop: 7, paddingBottom: 20 }}>
        {activityTypeConfig.map(config => {
          const count =
            config.type === 'contact'
              ? leadActivityRecord.filter(
                  data =>
                    data.action === 'contact' ||
                    data.action === 'not_interested',
                ).length
              : leadActivityRecord.filter(data => data.action === config.type)
                  .length;
          return (
            <CardStyle key={config.type}>
              <Row gap={10} style={{ justifyContent: 'space-between' }}>
                <H8 fontWeight="bold">{config.label}</H8>
                <H8 fontWeight="bold">{count}</H8>
              </Row>
            </CardStyle>
          );
        })}
      </Row>

      <TableContainer>
        <TableListCard
          headerConfig={headerConfig}
          contentListConfig={contentListConfig}
          sortBy={sortBy}
          setSortBy={setSortBy}
          tableConfig={{
            sortIconColor: colors.palette.fwdAlternativeOrange[100],
            dateIconColor: colors.palette.fwdAlternativeOrange[100],
            iconSize: sizes[5],
          }}
          ListEmptyComponent={() => <EmptyActRecord />}
        />
      </TableContainer>
    </Column>
  );
}

function AddLeadActivityButtonWithModal() {
  const { colors, typography } = useTheme();

  const [isFormModalActive, setIsFormModalActive] = useState(false);

  const { t } = useTranslation('leadProfile');

  const IconComp = country === 'id' ? Icon.ArrowRight : Icon.Plus;
  return (
    <>
      <Button
        text={t('leadProfile.logActivityForm.logActivity')}
        icon={
          <IconComp size={20} fill={colors.palette.fwdAlternativeOrange[100]} />
        }
        variant="text"
        textStyle={{
          fontSize: typography.largeLabel.size,
          fontFamily: CubeFonts.FWDCircularTT.Bold,
          color: colors.palette.fwdAlternativeOrange[100],
        }}
        onPress={() => setIsFormModalActive(true)}
      />
      <LogActivity
        showModal={isFormModalActive}
        setShowModal={setIsFormModalActive}
      />
    </>
  );
}

const headerConfig: TableRowProps = [
  { key: 'Activity Type', content: 'Activity Type', width: '23%' },
  {
    key: 'Description',
    content: 'Description',
    width: '58%',
  },
  {
    key: 'Date',
    content: 'Date',
    width: '19%',
    isDate: true,
  },
];

const Title = styled(H6)(({ theme }) => ({
  paddingVertical: theme.space[5],
}));

const CardStyle = styled(Card)(({ theme: { space, borderRadius } }) => ({
  borderRadius: borderRadius.large,
  height: 41,
  flex: 1,
  paddingVertical: space[2],
  paddingRight: space[4],
  paddingLeft: space[3],
  justifyContent: 'center',
}));

const TableContainer = styled(Card)(({ theme: { borderRadius } }) => ({
  borderRadius: borderRadius.large,
}));

const descriptionIconConfig = [
  {
    type: 'submit',
    label: `leadProfile.activityRecord.action.submit`,
    description: 'leadProfile.activityRecord.action.applicationSubmitted',
    icon: <Icon.TickCircle size={24} />,
  },
  {
    type: 'illustrate',
    label: `leadProfile.activityRecord.action.illustrate`,
    description: 'leadProfile.activityRecord.action.illustrateCreated',
    icon: <Icon.Document size={24} />,
  },
  {
    type: 'defer',
    label: '--',
    description: 'leadProfile.activityRecord.action.defer',
    icon: <Icon.Thinking size={24} />,
  },
  {
    type: 'not_interested',
    label: 'leadProfile.activityRecord.action.contact',
    description: 'leadProfile.activityRecord.action.not_interested',
    icon: <Icon.NotInterested size={24} />,
  },
  {
    type: 'contact',
    label: `leadProfile.activityRecord.action.contact`,
    description: 'leadProfile.activityRecord.action.contact',
    icon: <Icon.VeryInterested size={24} />,
    notInterestedIcon: <Icon.NotInterested size={24} />,
    deferIcon: <Icon.Thinking size={24} />,
  },
  {
    type: 'appointment',
    label: `leadProfile.activityRecord.action.appointment`,
    description: 'leadProfile.activityRecord.chart.appointment',
    icon: <Icon.VeryInterested size={24} />,
    notInterestedIcon: <Icon.NotInterested size={24} />,
    deferIcon: <Icon.Thinking size={24} />,
  },
] as const;
