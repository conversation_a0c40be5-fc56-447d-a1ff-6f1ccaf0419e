import React, { useMemo } from 'react';
import { Card } from 'features/lead/components/LeadProfile/Card';
import styled from '@emotion/native';
import { Column, PictogramIcon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import FnaLabel from './FnaLabel';
import SavingNeedsCard from 'features/lead/components/LeadProfile/Fna/SavingNeedsCard';
import { addOrdinalSuffixOf } from 'utils/helper/numberToOrdinal';
import { getTagColor } from './SavingNeeds';
import { FnaGoalData } from 'types/leadProfile';
import { goalsByNeed } from 'features/fna/constants/lifeJourney';
import MoneyBagIcon from 'features/fna/components/icons/MoneyBagIcon';
import MedicineIcon from 'features/fna/components/icons/MedicineIcon';
import MoneyUnderUmbrellaIcon from 'features/fna/components/icons/MoneyUnderUmbrellaIcon';

export default function ProtectionGoals({ data }: { data: FnaGoalData }) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('leadProfile');

  const summary = useMemo(() => {
    let totalTarget = 0;
    let totalCoverage = 0;
    let totalGap = 0;
    let priority = 0;

    goalsByNeed['PROTECTION'].forEach(goalType => {
      const goal = data[goalType];
      if (goalType) {
        totalTarget += goal?.targetAmount || 0;
        totalCoverage += goal?.coverageAmount || 0;
        totalGap += goal?.gapAmount || 0;
        priority += goal?.priority ?? 0;
      }
    });

    return {
      totalTarget,
      totalCoverage,
      totalGap,
      priority,
    };
  }, [data]);

  const protectionGoalsData = [
    {
      priority: data.incomeProtectionGoal?.priority,
      label: 'leadProfile.fna.incomeProtection',
      icon: MoneyBagIcon,
      targetAmount: data?.incomeProtectionGoal?.targetAmount ?? 0,
      coverageAmount: data?.incomeProtectionGoal?.coverageAmount ?? 0,
    },
    {
      priority: data.healthProtectionGoal?.priority,
      label: 'leadProfile.fna.healthProtection',
      icon: MedicineIcon,
      targetAmount: data?.healthProtectionGoal?.targetAmount ?? 0,
      coverageAmount: data?.healthProtectionGoal?.coverageAmount ?? 0,
    },
    // {
    //   priority: data.legacyPlanningGoal?.priority,
    //   label: 'leadProfile.fna.legalPlanning',
    //   icon: PictogramIcon.Family,
    //   targetAmount: data?.legacyPlanningGoal?.targetAmount ?? 0,
    //   coverageAmount: data?.legacyPlanningGoal?.coverageAmount ?? 0,
    // },
  ].sort((a, b) => (a?.priority ?? 0) - (b?.priority ?? 0));

  return (
    <Container gap={space[7]} paddingBottom={space[15]}>
      <Row alignItems="center">
        <Row gap={space[2]} flex={1} alignItems="center">
          <PictogramIcon.Insurance5 width={sizes[11]} height={sizes[11]} />
          <Typography.H6 color={colors.secondary} fontWeight="bold">
            {t('leadProfile.fna.protectionGoals')}
          </Typography.H6>
        </Row>

        <FnaLabel
          totalNeeds={summary?.totalTarget}
          totalCurrentCoverage={summary?.totalCoverage}
          totalGapToTargetAmount={summary?.totalGap}
          currency={t('leadProfile.fna.currency')} //TODO:
        />
      </Row>
      <Row marginTop={space[4]} gap={space[6]}>
        {protectionGoalsData.map((item, i) => (
          <Column key={item.label} flex={1} alignItems="center" gap={19}>
            <SavingNeedsCard
              Icon={item?.icon}
              labelText={t(item?.label as any)}
              currency={t('leadProfile.fna.currency')}
              target={item?.targetAmount}
              progress={item?.coverageAmount}
              chartConfig={{ isShowLabel: true, isShowArrow: true }}
              tagConfig={{
                isShowTag: true,
                // isShowTag: item?.priority <= 3,
                tagColor: getTagColor(item?.priority, colors),
              }}
              tagText={t('leadProfile.fna.priority', {
                number: addOrdinalSuffixOf(item?.priority),
              })}
            />
          </Column>
        ))}
      </Row>
    </Container>
  );
}

const Container = styled(Card)(({ theme: { borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  paddingHorizontal: space[5],
  paddingVertical: space[5],
}));
