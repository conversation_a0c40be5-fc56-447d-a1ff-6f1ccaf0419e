import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import FlagLabel from 'components/FlagLabel';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import LeadIconSVG from 'features/lead/assets/image/LeadIconSVG';
import EntityIcon from 'features/lead/components/AddLeadForm/EntityIcon';
import LeadCard from 'features/lead/my/LeadProfile/components/LeadCard';
import { contactHelper } from 'features/lead/utils';
import React from 'react';
import { GenderCodeUnion, Lead } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function LeadBriefInfo({ data }: { data: Lead | undefined }) {
  const { space, colors } = useTheme();

  const isCustomer = false;
  const isEntity = data?.isIndividual === false;
  const leadName = isEntity
    ? data?.companyName ?? '--'
    : `${data?.firstName ?? ''} ${data?.lastName ?? ''} `;

  const briefInfoMap = {
    leadName: leadName,
    leadCreatedDate: dateFormatUtil(data?.createdAt ?? '', 'dash'),
    leadGenderCode: data?.genderCode,
    leadContactNo: data?.mobilePhoneCountryCode
      ? '+' + data?.mobilePhoneCountryCode + data?.mobilePhoneNumber
      : data?.mobilePhoneNumber,
    leadEmail: data?.email,
  };

  return (
    <LeadCard
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}>
      <Row gap={space[3]} alignItems="center">
        {isEntity ? (
          <EntityIcon isActive isTablet iconSize={space[15]} />
        ) : (
          <LeadIcon gender={briefInfoMap.leadGenderCode} />
        )}
        <Column gap={space[1]}>
          <Row alignItems="center" gap={space[2]}>
            <Typography.H6 color={colors.primary} fontWeight="bold">
              {briefInfoMap.leadName}
            </Typography.H6>
            {/* //! Demo only */}
            <Box>
              {isCustomer && (
                <FlagLabel
                  type={'normal_lightBlue'}
                  content={'Existing Customer'}
                  Icon={() => (
                    <Column marginRight={space[1]}>
                      <Icon.SuccessAccount
                        size={space[4]}
                        fill={colors.palette.fwdBlue[100]}
                      />
                    </Column>
                  )}
                />
              )}
            </Box>
          </Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            Lead created on {briefInfoMap.leadCreatedDate}
          </Typography.Body>
        </Column>
      </Row>
      <Row gap={space[4]} alignItems="center">
        {briefInfoMap.leadContactNo && (
          <ContactButton
            onPress={() => {
              contactHelper({
                method: 'call',
                phoneNumber: briefInfoMap.leadContactNo ?? '',
              });
            }}>
            <Icon.Call size={space[6]} fill={colors.primary} />
          </ContactButton>
        )}

        {briefInfoMap.leadEmail && (
          <ContactButton
            onPress={() => {
              contactHelper({
                method: 'email',
                emailAddress: briefInfoMap.leadEmail ?? '',
              });
            }}>
            <Icon.Email size={space[6]} fill={colors.primary} />
          </ContactButton>
        )}
      </Row>
    </LeadCard>
  );
}

function LeadIcon({
  gender,
}: {
  gender: GenderCodeUnion | string | undefined;
}) {
  const { space, colors, borderRadius } = useTheme();

  switch (gender) {
    case 'M':
    case 'F':
    default:
      return <LeadIconSVG size={space[14]} />;
  }
}

const ContactButton = styled.TouchableOpacity(({ theme }) => ({
  backgroundColor: theme.colors.primaryVariant2,
  height: theme.space[10],
  width: theme.space[10],
  borderRadius: theme.borderRadius.full,
  justifyContent: 'center',
  alignItems: 'center',
}));
