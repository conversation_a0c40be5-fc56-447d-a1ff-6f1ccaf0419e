import { Icon } from 'cube-ui-components';
import LeadProfileDetailsActivitiesTab from 'features/lead/my/LeadProfile/tabletV2/LeadProfileDetailsActivitiesTab';
import LeadProfileDetailsSavedProposalsTab from 'features/lead/my/LeadProfile/tabletV2/LeadProfileDetailsSavedProposalsTab';
import LeadProfileDetailsCFFSection from 'features/lead/my/LeadProfile/tabletV2/LeadProfileDetailsCFFSection';
import {
  CommonProfileTabBarConfig,
  LeadProfileConfig,
  EntityProfileConfig,
} from 'features/lead/my/LeadProfile/type';
import NoteSVG from 'features/lead/ib/LeadProfile/assets/NoteSVG';
import PeopleSVG from 'features/lead/ib/LeadProfile/assets/PeopleSVG';
import PhoneSVG from 'features/lead/ib/LeadProfile/assets/PhoneSVG';

import LeadProfileDetailsProfileTab from 'features/lead/my/LeadProfile/tabletV2/LeadProfileDetailsProfileTab';
import LogActivity from 'features/lead/my/LeadProfile/tabletV2/LogActivity';
import LeadProfileOpportunityTab from '../../tabletV2/LeadProfileOpportunityTab';
import LightBulbSVG from 'features/lead/ib/LeadProfile/assets/LightBulbSVG';
import ContactModal from 'features/lead/components/LeadProfile/ContactModal';
import LeadProfileFNASection from '../../tabletV2/LeadProfileDetailsFNASection';

export const LeadProfileTabBarConfig: CommonProfileTabBarConfig = {
  tabList: [
    {
      type: 'Proposal',
      label: 'leadProfile.bottomBar.proposal',
      icon: NoteSVG,
      havePlus: true,
      navigatorPage: 'CoverageDetailsScreen',
      action: 'NavigateToOtherScreen',
    } as const,
    {
      type: 'Activity',
      label: 'leadProfile.bottomBar.activity',
      icon: PeopleSVG,
      havePlus: true,
      modal: LogActivity,
      action: 'showModal',
    } as const,
    {
      type: 'Contact',
      label: 'leadProfile.bottomBar.contact',
      icon: PhoneSVG,
      havePlus: false,
      modal: ContactModal,
      action: 'showModal',
    } as const,
  ],
};

export const LeadTabConfig: LeadProfileConfig = {
  profile: {
    key: 'profile',
    label: 'leadProfile.profile',
    icon: Icon.User,
    component: LeadProfileDetailsProfileTab,
  },
  opp: {
    key: 'opp',
    label: 'leadProfile.opp',
    icon: LightBulbSVG,
    component: LeadProfileOpportunityTab,
  },
  cff: {
    key: 'cff',
    label: 'leadProfile.cff',
    icon: Icon.DocumentCopy,
    component: LeadProfileFNASection,
  },
  proposals: {
    key: 'proposals',
    label: 'leadProfile.proposals',
    icon: Icon.Document,
    component: LeadProfileDetailsSavedProposalsTab,
  },
  activities: {
    key: 'activities',
    label: 'leadProfile.activities',
    icon: Icon.History,
    component: LeadProfileDetailsActivitiesTab,
  },
};

export const EntityTabConfig: EntityProfileConfig = {
  profile: {
    key: 'profile',
    label: 'leadProfile.profile',
    icon: Icon.User,
    component: LeadProfileDetailsProfileTab,
  },
  opp: {
    key: 'opp',
    label: 'leadProfile.opp',
    icon: LightBulbSVG,
    component: LeadProfileOpportunityTab,
  },
  proposals: {
    key: 'proposals',
    label: 'leadProfile.proposals',
    icon: Icon.Document,
    component: LeadProfileDetailsSavedProposalsTab,
  },
  activities: {
    key: 'activities',
    label: 'leadProfile.activities',
    icon: Icon.History,
    component: LeadProfileDetailsActivitiesTab,
  },
};
