import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useRoute } from '@react-navigation/native';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import { getCountryCodeValue, MY_MOBILE_CODE } from 'constants/optionList';
import {
  addToast,
  Box,
  Button,
  Dropdown,
  H5,
  Icon,
  Row,
  TextField,
} from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import EntityIcon from 'features/lead/components/AddLeadForm/EntityIcon';
import NameTextField from 'features/lead/components/AddLeadForm/NameTextField';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import useGetNatureOfBusinessOption from 'features/lead/hooks/useGetNatureOfBusinessOption';
import TabButton from 'features/lead/tablet/components/TabButton';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React, { useMemo } from 'react';
import {
  SubmitError<PERSON>and<PERSON>,
  SubmitHandler,
  useForm,
  UseFormHandleSubmit,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import {
  CreateEntityRequest,
  EntityLeadFormValues,
  TypesOfNewLeadForm,
} from 'types';
import { BusinessNatureEntity, CountryCode } from 'types/optionList';
import { country } from 'utils/context';
import {
  addNewEntityLeadSchema,
  initialLeadData,
} from 'utils/validation/my/addNewEntityLeadSchema';

export type AddNewEntityFormProps = {
  onClose: () => void;
  setLeadType: React.Dispatch<React.SetStateAction<TypesOfNewLeadForm>>;
};

const BUTTON_WIDTH = 200;

export default function AddNewEntityForm({
  onClose,
  setLeadType,
}: AddNewEntityFormProps) {
  const defaultDate = new Date(new Date().getFullYear() - 18, 0, 1);
  const { t } = useTranslation('lead');
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList();
  const route = useRoute();
  const natureOfBusinessOptions = useGetNatureOfBusinessOption();
  const { countryCodeOptions } = useMemo(() => {
    return {
      titleOptions: optionList?.TITLE?.options || [],
      // extensionOptions: optionList?.EXTENSION?.options || [],
      countryCodeOptions:
        optionList?.COUNTRY_CODE?.options?.map(data => ({
          label: data.label,
          value: getCountryCodeValue(data),
        })) || [],
    };
  }, [optionList]);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
    getValues,
  } = useForm<EntityLeadFormValues>({
    defaultValues: {
      ...initialLeadData,
      mobilePhoneCountryCode:
        (country === 'my' && MY_MOBILE_CODE) ||
        (country === 'ib' && MY_MOBILE_CODE) ||
        '',
    },
    mode: 'onBlur',
    resolver: yupResolver(addNewEntityLeadSchema),
  });

  const signatoryName = watch('firstName');
  const fullName = watch('companyName');
  const mobilePhoneNumber = watch('mobilePhoneNumber');

  const { mutate: mutateToCreateLead, isLoading } = useCreateLead();

  let isMandatoryFieldsFilled;
  if (country === 'my') {
    isMandatoryFieldsFilled = fullName !== '' && mobilePhoneNumber !== '';
  } else if (country === 'ib') {
    isMandatoryFieldsFilled =
      fullName !== '' && mobilePhoneNumber !== '' && signatoryName !== '';
  }
  isMandatoryFieldsFilled =
    fullName !== '' && mobilePhoneNumber !== '' && signatoryName !== '';

  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;

  const closeModalHandler = () => {
    onClose();
    reset();
  };

  const onValidSubmit: SubmitHandler<HandleSubmitSchema> = (
    data: EntityLeadFormValues,
  ) => {
    const newData: CreateEntityRequest = {
      // ...data,
      //
      companyName: data.companyName,
      occupationIndustryCode: data.natureOfBusiness,
      firstName: data.firstName,
      mobilePhoneCountryCode: data.mobilePhoneCountryCode,
      mobilePhoneNumber: data.mobilePhoneNumber,
      email: data.email,
      isIndividual: false,
      lastName: ' ',
    };
    mutateToCreateLead(newData, {
      onSuccess: () => {
        onClose();
        addToast([
          {
            IconLeft: Icon.Tick,
            message: 'A new lead is added.',
          },
        ]);
        reset();
      },
      onError: e => {
        addToast([
          {
            message: 'Please try again later.',
          },
        ]);
        console.log('onError: ', e);
      },
    });
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    console.log(
      '🔴🔴🔴🔴 ~ file: AddNewLeadModal ~ AddNewLeadForm ~ error:',
      error,
    );
  };

  return (
    <View
      style={{
        paddingHorizontal: sizes[12],
        paddingTop: sizes[15],
        paddingBottom: sizes[12],
        width: '100%',
      }}>
      <LeadAvatar />
      <FormContainer>
        <Box
          alignItems="center"
          gap={space[4]}
          paddingTop={space[3]}
          paddingBottom={space[1]}>
          <HeaderText>{t('addLead.formFields.addNewLead')}</HeaderText>
          <Row gap={space[2]}>
            <TabButton
              containerStyle={{
                height: undefined,
                flex: undefined,
                width: 152,
                paddingVertical: space[1],
              }}
              Icon={<Icon.User />}
              label="Individual"
              isActive={false}
              isActiveMedium
              onPress={() => setLeadType('INDIVIDUAL')}
            />
            <TabButton
              containerStyle={{
                height: undefined,
                flex: undefined,
                paddingVertical: space[1],

                width: 152,
              }}
              Icon={<Icon.Office />}
              label="Organization"
              isActive
              isActiveMedium
            />
          </Row>
        </Box>
        <Row style={{ gap: space[7], marginBottom: space[1] }}>
          <Input
            control={control}
            as={NameTextField}
            name="firstName"
            style={{ flex: 1 }}
            label={'Authorised signatory name'}
            error={errors.firstName?.message}
          />
          <Input
            control={control}
            as={NameTextField}
            name="companyName"
            style={{ flex: 1 }}
            label={'Company name'}
            error={errors.companyName?.message}
          />
        </Row>
        <Row gap={space[5]}>
          <Row gap={space[3]} flex={1}>
            <Input
              control={control}
              as={Dropdown<CountryCode, string>}
              name="mobilePhoneCountryCode"
              label={'Country code'}
              modalTitle={'Country code'}
              data={countryCodeOptions}
              style={{ flex: 1.2 }}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              getDisplayedLabel={item =>
                '+' + parseInt(getCountryCodeValue(item))
              }
              keyExtractor={item => item.value + item.label}
              disabled
            />
            <Input
              control={control}
              as={PhoneField}
              name="mobilePhoneNumber"
              label={t('addLead.formFields.businessPhoneNumber')}
              style={{ flex: 3 }}
              keyboardType="numeric"
              error={errors.mobilePhoneNumber?.message}
              size={'large'}
            />
          </Row>
          <Input
            control={control}
            as={Dropdown<BusinessNatureEntity, string>}
            name="natureOfBusiness"
            label={'Nature of business (optional)'}
            modalTitle={'Nature of business (optional)'}
            data={natureOfBusinessOptions}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
          />
        </Row>
        <Row style={{ gap: space[5] }}>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('addLead.formFields.email.optional')}
            style={{ flex: 1 }}
            error={errors.email?.message}
          />
        </Row>
        {/*
        <Row style={{ gap: space[5], marginBottom: space[4] }}>
          <Input
            control={control}
            as={NameTextField}
            name="fullName"
            style={{ flex: 1 }}
            label={t('addLead.formFields.fullName')}
            error={errors.fullName?.message}
          />

          <Row style={{ flex: 1, gap: space[3] }}>
            <Input
              control={control}
              as={Dropdown<CountryCode, string>}
              name="mobilePhoneCountryCode"
              label={t('addLead.formFields.countryCode')}
              modalTitle={'Country code'}
              data={countryCodeOptions}
              style={{ flex: 0.8 }}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              getDisplayedLabel={item => getCountryCodeValue(item)}
              keyExtractor={item => item.value + item.label}
            />
            <Input
              control={control}
              as={TextField}
              name="mobilePhoneNumber"
              label={t('addLead.formFields.phoneNumber')}
              style={{ flex: 1 }}
              keyboardType="numeric"
              error={errors.mobilePhoneNumber?.message}
              size={'large'}
            />
          </Row>
        </Row>

        <Row style={{ gap: space[5], marginBottom: space[4] }}>
          <Row style={{ gap: space[3], flex: 1 }}>
            <Input
              control={control}
              as={DatePicker}
              name="birthDate"
              style={{ flex: 1 }}
              label={t('addLead.formFields.dateOfBirth')}
              hint="DD/MM/YYYY"
              error={errors.birthDate?.message}
              defaultDate={defaultDate}
              maxDate={DatePickerMaxDate}
            />
            <TextField
              disabled={true}
              label={t('addLead.formFields.age')}
              value={age}
              style={{ flex: 0.3 }}
            />
          </Row>

          <Input
            control={control}
            as={Picker}
            name="genderCode"
            type="text"
            label={t('addLead.formFields.gender.optional')}
            style={{ flex: 1 }}
            error={errors.genderCode?.message}
            items={[
              {
                value: 'M',
                text: 'Male',
              },
              {
                value: 'F',
                text: 'Female',
              },
            ]}
          />
        </Row>

        <Row style={{ gap: space[5] }}>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('addLead.formFields.email.optional')}
            style={{ flex: 1 }}
            error={errors.email?.message}
          />
          <Input
            control={control}
            as={Dropdown<string, string>}
            name="interestedCategories"
            label={t('addLead.formFields.interestedProductCategory')}
            modalTitle={'Interested product (optional)'}
            data={['Education', 'Legacy', 'Protection', 'Wealth']}
            getItemValue={item => item}
            getItemLabel={item => item}
            style={{ flex: 1 }}
          />
        </Row> */}

        <Row style={{ gap: space[5] }}>
          <Button
            text={t('addLead.formFields.cancel')}
            variant="secondary"
            onPress={closeModalHandler}
            style={{ width: BUTTON_WIDTH }}
          />
          <Button
            text={t('addLead.formFields.saveButton')}
            variant="primary"
            onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
            disabled={!isMandatoryFieldsFilled || isLoading}
            style={{ width: BUTTON_WIDTH }}
            loading={isLoading}
            gaParams={{
              eventType: 'lead_created',
              formSource: route.name.includes('Lead')
                ? 'menu_leads_and_customers'
                : 'homepage_cta',
            }}
          />
        </Row>
      </FormContainer>
    </View>
  );
}

function LeadAvatar() {
  const { space, colors, borderRadius } = useTheme();
  const isTakaful = country === 'my';

  return (
    <LeadIconStyle>
      <EntityIcon isActive isTablet />
    </LeadIconStyle>
  );
}

const HeaderText = styled(H5)(({ theme }) => ({
  fontFamily: 'FWDCircularTT-Bold',
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const LeadIconStyle = styled(View)(({ theme }) => ({
  alignSelf: 'center',
  zIndex: 10,
  position: 'absolute',
  top: -60,
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.full,
}));

const FormContainer = styled.View(({ theme }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  gap: sizes[5],
}));
