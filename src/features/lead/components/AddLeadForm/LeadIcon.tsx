import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { FC } from 'react';
import Svg, { Mask, Circle, G, Path, Defs, ClipPath } from 'react-native-svg';
import { GenderCodeUnion } from 'types';

type LeadIconProps = {
  gender?: // TODO_Alex: update to share types from Lead Form
  GenderCodeUnion | undefined | null;
  isActive?: boolean;
};

export default function LeadIcon({ gender, isActive }: LeadIconProps) {
  const { isWideScreen } = useWindowAdaptationHelpers();
  const iconSize = isWideScreen ? 124 : 88;

  if (isActive === false) {
    return (
      <Svg width={iconSize} height={iconSize} viewBox="0 0 88 88" fill="none">
        <Circle cx={44} cy={44} r={44} fill="#F8F9F9" />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M44 32.022a4.405 4.405 0 00-4.4 4.4c0 2.427 1.974 4.4 4.4 4.4 2.427 0 4.4-1.973 4.4-4.4 0-2.426-1.973-4.4-4.4-4.4zm0 11.733c-4.044 0-7.333-3.29-7.333-7.333S39.957 29.09 44 29.09c4.044 0 7.333 3.29 7.333 7.333 0 4.044-3.29 7.334-7.333 7.334zm11 13.2h-.733V53.84a5.685 5.685 0 00-5.685-5.684h-9.164a5.685 5.685 0 00-5.685 5.684v3.115H33a2.2 2.2 0 01-2.2-2.2v-.915a8.619 8.619 0 018.618-8.618h9.164A8.619 8.619 0 0157.2 53.84v.915a2.2 2.2 0 01-2.2 2.2z"
          fill="#DBDFE1"
        />
      </Svg>
    );
  }
  // * SVG transform done by https://transform.tools/svg-to-react-native
  if (gender === 'M') {
    return <MaleLeadIcon iconSize={iconSize} />;
  }

  if (gender === 'F') {
    return <FemaleLeadIcon iconSize={iconSize} />;
  }

  return (
    <Svg
      width={iconSize}
      height={iconSize}
      viewBox={`0 0 88 88`}
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
    >
      <Path
        d="M88 44c0 24.3-19.7 44-44 44S0 68.3 0 44 19.7 0 44 0s44 19.7 44 44z"
        fill="#FAE4D3"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M44 32.542a4.588 4.588 0 00-4.583 4.583A4.588 4.588 0 0044 41.71a4.588 4.588 0 004.583-4.584A4.588 4.588 0 0044 32.542zm0 12.222c-4.212 0-7.639-3.427-7.639-7.639s3.427-7.639 7.639-7.639 7.639 3.427 7.639 7.64c0 4.211-3.427 7.638-7.639 7.638zm11.458 13.75h-.764V55.27a5.922 5.922 0 00-5.921-5.922h-9.546a5.922 5.922 0 00-5.921 5.922v3.245h-.764a2.292 2.292 0 01-2.292-2.292v-.953a8.978 8.978 0 018.977-8.977h9.546a8.978 8.978 0 018.977 8.977v.953a2.292 2.292 0 01-2.292 2.292z"
        fill="#E87722"
      />
    </Svg>
  );
}

const FemaleLeadIcon: FC<{ iconSize: number }> = ({ iconSize }) => (
  <Svg width={iconSize} height={iconSize} viewBox="0 0 88 88" fill="none">
    <Mask id="a" maskUnits="userSpaceOnUse" x={0} y={0} width={88} height={88}>
      <Circle cx={44} cy={44} r={44} fill="#D9D9D9" />
    </Mask>
    <G mask="url(#a)">
      <Circle cx={44} cy={44} r={44} fill="#FEF9F4" />
      <G clipPath="url(#clip0_5594_136593)">
        <Path
          d="M27.206 19.965c-1.061 2.78-.735 5.806-1.715 8.587-.898 2.617-3.102 4.743-5.306 6.38-1.714 1.226-3.591 2.37-4.49 4.252-2.04 4.09 1.388 9.323-.326 13.576-.816 1.881-2.694 3.435-2.939 5.562-.245 1.553.653 3.19 1.878 4.252 1.224.982 2.857 1.472 4.408 1.636 1.388.164 2.775.164 4.081-.082 1.552-.245 3.02-.654 4.49-.9 3.102-.572 6.368-.408 9.47.41 2.367.654 4.653 1.717 7.02 1.717 1.551 0 3.184-.409 4.653-.981 1.633-.573 3.266-1.309 4.572-2.536 1.877-1.717 2.938-4.089 3.51-6.543.571-2.453.816-4.907 1.388-7.36.734-3.108 2.04-6.134 3.918-8.751.735-1.145 1.633-2.208 2.04-3.517.246-.9.327-1.8.246-2.78-.327-3.926-2.776-7.688-6.204-9.651-.898-.49-1.796-.9-2.531-1.472-.735-.654-1.388-1.472-1.96-2.29a60.73 60.73 0 00-3.428-4.58c-.571-.736-1.224-1.554-2.04-2.045-.98-.572-2.123-.9-3.266-1.063-5.796-.818-15.102 1.881-17.47 8.179z"
          fill="#183028"
        />
        <Path
          d="M49.49 49.244s9.062-.245 12.409 2.044c2.367 1.636 5.224 11.041 5.224 11.041l-6.04 5.235s-.735 6.542-3.919 13.003l-1.306 9.16s-10.612 6.216-25.061 0c0 0-2.776-25.598-5.306-23.39-4.327 3.435-9.388-2.945-9.388-2.945s1.061-10.959 9.633-14.557c8.326-3.435 14.693-.164 14.693-.164l9.062.573z"
          fill="#B7E0D5"
        />
        <Path
          d="M52.349 28.307s.734-1.39 1.469-.982c1.877 1.145.245 3.272-.898 3.762l-.572-2.78z"
          fill="#FBE2D0"
        />
        <Path
          d="M39.369 13.422c-5.388.982-9.225 5.889-8.898 11.368.081 1.8.653 3.435 1.47 4.907 1.877 3.599 3.51 6.543 5.305 8.588 1.796 2.044 2.857 4.743 2.776 7.442l-.082 2.78c6.694 2.373 9.224.737 9.224.737l-.897-8.097c6.694-1.554 4.408-14.067 3.918-17.829 0-.163 0-.245-.082-.409-.653-6.215-6.449-10.55-12.734-9.487z"
          fill="#FBE2D0"
        />
        <Path
          d="M39.777 24.463c-1.633 2.453-3.428 4.907-4.571 7.688-.082.245-.98 2.617-.653 2.78-1.878-1.145-3.266-3.026-4.408-4.907-2.041-3.271-2.041-7.442-.409-10.877 1.633-3.435 4.735-5.398 8.409-6.134 3.918-.736 8.326.082 11.101 3.108 2.613 2.862 3.184 6.706 3.837 10.387-.734-4.09-6.612-6.543-10.04-7.525.081.082-2.041 3.844-2.205 4.008a22.94 22.94 0 00-1.06 1.472z"
          fill="#183028"
        />
        <Path
          d="M35.614 32.396c-.245-.327-.571-.572-.898-.654-.734-.245-2.122-.409-2.938.572-1.225 1.473 1.877 4.744 4.326 2.618 0 0 .653-.818-.49-2.536zM47.042 59.549l-7.184-11.041 9.306.736-2.122 10.305z"
          fill="#FBE2D0"
        />
        <Path
          d="M47.042 59.548L39.94 46.626l-3.02.818 3.51 6.951 2.286-.736-.735 2.127 5.061 3.762zM47.042 59.548l1.96-12.104 3.102 1.881-1.388 4.99-1.714-.655.816 2.045-2.776 3.843z"
          fill="#74C7AF"
        />
        <Path
          d="M64.185 54.723l21.551 38.275a2.646 2.646 0 01-.735 3.68l-4.734 3.108c-1.143.736-2.776.491-3.592-.654L53.736 65.436"
          fill="#B7E0D5"
        />
        <Path
          d="M39.614 29.124c.081.491.49.9.816.736.408-.081.571-.572.49-1.063-.082-.49-.49-.9-.817-.736-.408.082-.571.573-.49 1.063zM48.838 27.816c.082.49.49.9.816.736.408-.082.572-.573.49-1.064-.081-.49-.49-.9-.816-.735-.327.081-.572.49-.49 1.063z"
          fill="#183028"
        />
        <Path
          d="M46.063 33.377c-.082 0-.245 0-.245-.082-.081-.082-.081-.245.082-.409.49-.409.735-.818.653-1.227-.082-.572-.653-1.063-1.061-1.144-.735-.246-1.225-2.618-1.388-3.68 0-.164.081-.246.245-.246.163 0 .245.082.245.245.245 1.636.734 3.19.98 3.272.49.163 1.224.736 1.387 1.553.082.41 0 1.064-.898 1.718z"
          fill="#E77824"
        />
        <Path
          d="M31.205 64.292l-3.755 1.881S32.838 81.549 33 85.311h1.96c-.082 0-1.796-19.056-3.755-21.019zM48.349 67.073a1.39 1.39 0 001.387-1.39c0-.769-.621-1.391-1.387-1.391a1.39 1.39 0 00-1.388 1.39c0 .768.621 1.39 1.388 1.39zM48.349 76.232a1.39 1.39 0 001.387-1.39 1.389 1.389 0 10-2.775 0c0 .767.621 1.39 1.388 1.39zM47.776 84.575a1.39 1.39 0 001.388-1.39 1.39 1.39 0 00-1.388-1.391 1.39 1.39 0 00-1.388 1.39c0 .768.622 1.39 1.388 1.39z"
          fill="#74C7AF"
        />
        <Path
          d="M16.021 63.393s-4.49 21.1 1.061 35.167l3.266 8.26c.408 1.063 1.388 1.554 2.367 1.227l5.633-2.045c1.061-.409 1.633-1.717 1.224-3.026-1.877-5.397-4.163-20.2-2.122-36.394"
          fill="#B7E0D5"
        />
        <Path
          d="M46.797 36.73a6.47 6.47 0 00.735-.326c.734-.41 1.142-1.063 1.142-1.145.082-.082.082-.246-.081-.327-.082-.082-.245-.082-.327.081-.081.082-1.387 2.29-3.918.9-.082-.082-.245 0-.327.082-.081.082 0 .245.082.327 1.061.572 1.96.572 2.694.409zM47.042 37.794c.408-.082.572-.327.572-.41.081-.081.081-.245-.082-.326-.082-.082-.245-.082-.327.081 0 0-.245.328-.98.246-.08 0-.244.082-.244.245 0 .***************.245.326.082.571 0 .816-.081z"
          fill="#E77825"
        />
      </G>
    </G>
    <Defs>
      <ClipPath id="clip0_5594_136593">
        <Path
          fill="#fff"
          transform="translate(6.47 11.648)"
          d="M0 0H88V214.824H0z"
        />
      </ClipPath>
    </Defs>
  </Svg>
);

const MaleLeadIcon: FC<{ iconSize: number }> = ({ iconSize }) => (
  <Svg width={iconSize} height={iconSize} viewBox="0 0 88 88" fill="none">
    <Mask id="a" maskUnits="userSpaceOnUse" x={0} y={0} width={88} height={88}>
      <Circle cx={44} cy={44} r={44} fill="#D9D9D9" />
    </Mask>
    <G mask="url(#a)">
      <Circle cx={44} cy={44} r={44} fill="#FEF9F4" />
      <G clipPath="url(#clip0_5867_199146)">
        <Path
          d="M48.14 99.412l-.932 24.9-7.914 57.944c-.56 11.233-4.377 42.967-5.401 52.047-.186 1.404-1.397 2.434-2.7 2.34l-11.267-.094c-1.49 0-2.7-1.216-2.7-2.714.093-6.834.559-25.836 2.793-51.485-1.396-31.078-3.445-62.25 8.567-80.41 12.29-18.722 19.553-2.528 19.553-2.528z"
          fill="#04A2AF"
        />
        <Path
          d="M45.252 41.28l.187 11.608-10.243-.561-.651-17.88 10.707 6.834z"
          fill="#F8E3D2"
        />
        <Path
          d="M46.929 42.404c1.396-.093 2.7-.28 3.445-.842 4.097-5.991-.559-22.279-.559-22.279l-6.61-2.715-8.38 5.242-.467 10.953a8.756 8.756 0 003.725 7.488c2.793 1.405 5.866 2.247 8.846 2.153z"
          fill="#FBE2D0"
        />
        <Path
          d="M37.245 27.988c.465-1.778-.838-5.99 1.024-5.803 1.397.28 5.96.468 7.542.468 3.725 0 10.336-1.404 10.336-7.115 0-2.808-1.77-4.306-3.539-5.055-1.676-.655-3.631 1.311-5.4 1.966-4.097 1.404-12.57 2.528-14.712 2.153-2.42-.468-5.028 1.966-5.214 3.557-.466 3.277 1.769 8.238 3.352 11.514.931 1.779 2.141 3.464 3.724 4.868l2.887-6.553z"
          fill="#183028"
        />
        <Path
          d="M32.496 28.363c.838-.75 2.328-.562 3.446.28 1.024.843 1.303 2.247.465 2.902-.838.75-2.328.562-3.445-.28-1.117-.843-1.304-2.153-.466-2.902z"
          fill="#FBE2D0"
        />
        <Path
          d="M11.825 69.27c2.048-8.893 7.915-16.475 16.201-20.032 3.352-1.498 7.356-2.434 12.012-2.434 3.631 0 6.704.28 9.404.842.062 0 .124.031.186.094 8.101 1.778 17.32 11.14 17.32 25.368l-.187 45.213c0 2.902-2.421 5.429-5.4 5.429H24.395l.931-46.617-.93 46.617h-11.36c-2.142 0-3.911-1.591-4.284-3.744-2.7-21.062.745-40.814 3.073-50.736z"
          fill="#FBE69E"
        />
        <Path
          d="M67.878 72.172l17.133 32.95c.652 1.217.28 2.808-.838 3.651l-7.356 5.055c-1.21.842-2.98.561-3.91-.656L52.421 83.779c-.838-1.217-.652-2.902.558-3.838l10.708-8.612c1.304-1.123 3.352-.749 4.19.843z"
          fill="#FBE69E"
        />
        <Path
          d="M39.573 27.801c.28.375.652.468 1.024.188.28-.281.372-.656.186-1.03-.28-.375-.651-.468-1.024-.187a.765.765 0 00-.186 1.03zM49.163 27.146c.28.374.652.468 1.025.187.279-.28.372-.655.186-1.03-.28-.374-.652-.467-1.025-.187-.279.188-.465.656-.186 1.03z"
          fill="#183028"
        />
        <Path
          d="M47.022 33.699c-.465-.094-1.024-.468-1.024-.656-.093-.093 0-.28.093-.28.093-.094.28 0 .28.093.093.094.372.375.838.375.279.093.651-.094.838-.375.093-.093.465-.749-.093-2.995-.28-1.31-.745-2.528-.745-2.528-.094-.093 0-.28.093-.28.093-.094.279 0 .279.093 0 0 .466 1.217.745 2.527.465 1.873.465 2.996 0 3.558-.186.28-.466.374-.652.468h-.652zM47.115 36.413c.28 0 .652-.093.931-.187.931-.28 1.583-1.03 1.583-1.03a.286.286 0 000-.374.282.282 0 00-.372 0c-.093.094-2.142 2.247-4.656.094a.282.282 0 00-.372 0 .286.286 0 000 .374c1.117 1.03 2.141 1.217 2.886 1.123zM47.115 37.817c.465-.093.744-.28.838-.28.093-.094.093-.281.093-.375-.093-.094-.28-.094-.373-.094 0 0-.465.281-1.117.094-.093-.094-.28 0-.28.187-.093.094 0 .281.187.281.093.187.465.187.652.187z"
          fill="#E77825"
        />
        <Path
          d="M23.185 67.772v55.791H61.36s5.122.187 5.401-5.897l.186-48.49s-2.98-20.5-20.95-21.998c0 0-8.752-1.591-16.667 1.404-2.514.936-6.61 9.268-6.145 19.19z"
          fill="#FED241"
        />
        <Path
          d="M35.009 47.178l5.959 11.233c.745 1.404 2.793 1.03 3.166-.468l1.862-10.765s-4.19-.842-10.987 0z"
          fill="#fff"
        />
      </G>
    </G>
    <Defs>
      <ClipPath id="clip0_5867_199146">
        <Path
          fill="#fff"
          transform="translate(7.765 10.352)"
          d="M0 0H88V240.706H0z"
        />
      </ClipPath>
    </Defs>
  </Svg>
);
