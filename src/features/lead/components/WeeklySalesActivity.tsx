import { Modal, TextInput, View } from 'react-native';
import styled from '@emotion/native';
import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  addErrorBottomToast,
  Box,
  Button,
  H7,
  Icon,
  Row,
  Typography,
} from 'cube-ui-components';
import InfoSVG from 'features/teamManagement/assets/InfoSVG';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useTranslation } from 'react-i18next';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { StyleSheet } from 'react-native';
import SalesActivityChart from './SalesActivityChart';
import {
  ConversionDetails,
  RequestDataType,
  RequestTarget,
  SummaryTargetDetails,
  TargetDetails,
} from 'types/leadTracking';
import SalesActivitySetTarget from 'features/lead/components/SalesActivitySetTarget';
import useBoundStore from 'hooks/useBoundStore';
import { TargetListDetails } from './MontlySalesActivity';
import { usePostLeadTracking } from '../hooks/usePostSalesActivity';
import { country } from 'utils/context';
import NoRecordSVG from '../assets/NoRecordSVG';
import { AxiosError } from 'axios';
import { TARGET_SETTING_INVALID_ERROR_CODE } from 'constants/errors';

export default function WeeklySalesActivity(props: {
  conversions: ConversionDetails[] | undefined;
  targets: TargetDetails[] | undefined;
  year: string | undefined;
  month: string | undefined;
  summary?: SummaryTargetDetails | undefined;
}) {
  const { conversions, targets, year, month, summary } = props;
  const { colors, space } = useTheme();
  const [currentWeek, setCurrentWeek] = useState<number | undefined>(1);

  const agentId = useBoundStore(store => store.auth.agentCode);
  const { mutateAsync: updateLeadTracking, isLoading } = usePostLeadTracking();

  const useGetCurrentWeek = (targets: TargetDetails[] | undefined) => {
    if (!targets) return 0;

    const currentDate = new Date().getDate();

    const thisWeek = targets.findIndex(({ startDate, endDate }) => {
      const start = new Date(startDate).getDate();
      const end = new Date(endDate).getDate();
      return start <= currentDate && currentDate <= end;
    });

    return thisWeek !== -1 ? thisWeek + 1 : 0;
  };

  const currentItem = useGetCurrentWeek(targets);

  useEffect(() => {
    setCurrentWeek(currentItem);
  }, [currentItem]);

  const targetValuesByWeek = useMemo(() => {
    if (currentItem && targets) {
      const target = targets[currentItem - 1];
      return {
        week: target.week,
        contact: target.contact,
        appt: target.appointment,
        illus: target.illustrate,
        submitted: target.submit,
      };
    }
  }, [targets, currentItem]);

  const parsedLabels = targetValuesByWeek
    ? [
        {
          week: targetValuesByWeek.week,
          values: [
            { name: 'Contacted', value: String(targetValuesByWeek.contact) },
            { name: 'Appt', value: String(targetValuesByWeek.appt) },
            { name: 'Illus', value: String(targetValuesByWeek.illus) },
            { name: 'Submitted', value: String(targetValuesByWeek.submitted) },
          ],
        },
      ]
    : [];

  const [labels, setLabels] = useState<TargetListDetails[]>(parsedLabels);

  const { t } = useTranslation(['lead']);

  const [isEditModeActive, setIsEditModeActive] = useState(false);
  const { shouldAdaptWideLayout, isNarrowScreen } =
    useWindowAdaptationHelpers();
  const [showAlert, setShowAlert] = useState(false);

  const handlePress = () => {
    setShowAlert(true);
  };

  const handleClose = () => {
    setShowAlert(false);
  };

  const handleInfoSVGPress = () => {
    setIsEditModeActive(true);
  };

  const handleSaveBtn = async () => {
    try {
      await updateLeadTracking({
        agentId: agentId,
        requestData: updateTarget({
          labels,
          year,
          month,
          targets,
        }),
      });
    } catch (e) {
      const error = e as AxiosError<
        {
          messageList?: Array<{ code: string; content: string }>;
        },
        unknown
      >;
      if (error.response) {
        const content = error.response.data.messageList?.[0]?.content;
        if (country === 'id' && content === TARGET_SETTING_INVALID_ERROR_CODE) {
          addErrorBottomToast([
            {
              message: t('lead:minimumRequirement'),
            },
          ]);
          return;
        }
      }
      console.error('error:', error);
    }
    setIsEditModeActive(false);
  };

  const isNoTargetSet =
    targets &&
    targets.filter(
      i =>
        i.appointment !== 0 ||
        i.contact !== 0 ||
        i.submit !== 0 ||
        i.illustrate !== 0,
    ).length === 0;

  return (
    <>
      <SalesActivityChart
        conversions={conversions}
        isContainerToShow={'A'}
        targets={targets}
        currentWeek={currentWeek}
      />
      <View>
        <Row
          h={space[6]}
          alignItems="center"
          alignContent="center"
          justifyContent="space-between">
          <Row gap={space[1]} alignItems="center">
            <H7 fontWeight="bold">
              {country === 'id'
                ? t('lead:targetOfThisWeek')
                : t('lead:targetForYourNext7Days')}
            </H7>

            {/* ----- time frame model */}
            {isEditModeActive ? null : (
              <TouchableOpacity
                onPress={handlePress}
                hitSlop={{ top: 1, left: 1, bottom: 1, right: 1 }}>
                <Icon.InfoCircle size={sizes[5]} fill={colors.secondary} />
                <AlertContainer
                  handleClose={handleClose}
                  visible={showAlert}
                  targets={targets}
                />
              </TouchableOpacity>
            )}
          </Row>
          {!isEditModeActive && !(!targets || isNoTargetSet) && (
            <TouchableOpacity onPress={handleInfoSVGPress}>
              <InfoSVG />
            </TouchableOpacity>
          )}
        </Row>
        {(!targets || isNoTargetSet) && !isEditModeActive ? (
          <Box alignItems="center">
            <Box boxSize={space[4]} />
            <NoRecordSVG width={120} height={100} />
            <Box boxSize={space[4]} />
            <Typography.LargeBody color={colors.palette.fwdGreyDarker}>
              {t('lead:emptyOperationData')}
            </Typography.LargeBody>
            <Box boxSize={space[5]} />
            {targets && (
              <Button
                size="medium"
                onPress={handleInfoSVGPress}
                variant="secondary"
                text={t('lead:setTarget')}
                style={{ width: 200, alignSelf: 'center' }}
              />
            )}
          </Box>
        ) : (
          <Row flex={1} w={'100%'} alignItems="center" mt={space[2]}>
            <View
              style={{
                height: 36,
                alignSelf: 'flex-end',
                alignItems: 'center',
                marginRight: shouldAdaptWideLayout ? sizes[2] : sizes[1],
                justifyContent: 'center',
              }}>
              <Typography.H8>{`WK${currentItem}`}</Typography.H8>
            </View>
            <View
              style={{
                flex: 1,
                justifyContent: 'space-between',
              }}>
              <Row flex={1} alignItems="center" justifyContent="space-between">
                {labels?.[0]?.values?.map((label, index) => (
                  <Typography.ExtraSmallLabel
                    key={'label_' + index}
                    style={{
                      paddingBottom: sizes[4],
                      marginTop: sizes[2],
                      textAlign: 'center',
                      width: isNarrowScreen
                        ? 43
                        : shouldAdaptWideLayout
                        ? 74
                        : 50,
                    }}>
                    {label.name}
                  </Typography.ExtraSmallLabel>
                ))}
              </Row>

              <TextAndIconRightContainer>
                {labels?.[0]?.values?.map((label, labelIndex) => (
                  <TextContainer key={'Target_' + labelIndex}>
                    <Row gap={1} flex={1} alignItems="center">
                      <SalesActivitySetTarget
                        isEditModeActive={isEditModeActive}
                        year={year}
                        month={month}
                        isFirst={labelIndex === 0}
                        editable
                        value={label.value}
                        onChangeValue={(newValue: string) => {
                          setLabels(prev => {
                            const newLabels = prev.slice();
                            // if newValue is empty, set it to 0
                            newLabels[0].values[labelIndex].value =
                              newValue || '0';

                            return newLabels;
                          });
                        }}
                      />
                      {labelIndex < labels?.[0]?.values?.length - 1 ? (
                        <View
                          style={{
                            height: 36,
                            gap: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginHorizontal: shouldAdaptWideLayout ? 24 : 3,
                          }}>
                          <Icon.ChevronRight size={sizes[4]} />
                        </View>
                      ) : null}
                    </Row>
                  </TextContainer>
                ))}
              </TextAndIconRightContainer>
            </View>
          </Row>
        )}

        {isEditModeActive ? (
          <View
            style={{
              marginVertical: sizes[4],
              marginHorizontal: 76,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Button
              style={{
                width: sizes[40],
              }}
              text="Save"
              onPress={handleSaveBtn}
              variant="primary"
            />
          </View>
        ) : null}
      </View>
    </>
  );
}

type AlertProps = {
  handleClose: () => void;
  visible: boolean;
  targets: TargetDetails[] | undefined;
};
function AlertContainer(props: AlertProps) {
  const { handleClose, visible, targets } = props;
  const { t } = useTranslation(['lead']);

  return (
    <View style={styles.container}>
      <Modal visible={visible} transparent={true} onRequestClose={handleClose}>
        <View style={styles.overlay}>
          <View style={styles.content}>
            <Typography.H6 style={styles.header} fontWeight="bold">
              {t('lead:thisWeekCycle')}
            </Typography.H6>
            <View style={styles.textBox}>
              {targets &&
                targets?.map((item, index) => {
                  const currentDate = new Date();
                  const startDate = new Date(item.startDate);
                  const endDate = new Date(item.endDate);
                  if (startDate <= currentDate && currentDate <= endDate) {
                    const formatter = new Intl.DateTimeFormat('en-US', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                    });

                    const formattedStartDate = formatter.format(startDate);
                    const formattedEndDate = formatter.format(endDate);

                    const dateRange = `${formattedStartDate.slice(
                      0,
                      -6,
                    )} - ${formattedEndDate}`;

                    return (
                      <Typography.H7
                        key={'remainder_' + index}
                        style={styles.message}>
                        Week {index + 1}: {dateRange}
                      </Typography.H7>
                    );
                  }
                })}
            </View>
            <Button
              style={{
                width: 279,
              }}
              text={t('lead:igotIt')}
              onPress={handleClose}
              variant="primary"
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 24,
    width: 327,
  },
  header: {
    textAlign: 'left',
    marginBottom: sizes[2],
  },
  textBox: {
    marginBottom: sizes[6],
  },
  message: {
    lineHeight: sizes[6],
  },
});

const TextAndIconRightContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  height: sizes[9],
  alignItems: 'center',
  // backgroundColor: 'yellow',
}));

const TextContainer = styled.View(({ theme }) => ({
  alignItems: 'center',
  lineHeight: 17,
  justifyContent: 'space-between',
}));

function updateTarget({
  targets,
  labels,
  year,
  month,
}: {
  labels: TargetListDetails[];
  year: string | undefined;
  month: string | undefined;
  targets: TargetDetails[] | undefined;
}) {
  if (targets) {
    targets?.map(
      target =>
        target.week === labels[0].week &&
        ((target.contact = parseInt(labels[0].values[0].value)),
        (target.appointment = parseInt(labels[0].values[1].value)),
        (target.illustrate = parseInt(labels[0].values[2].value)),
        (target.submit = parseInt(labels[0].values[3].value))),
    );
  }

  const requestData: RequestDataType = {
    year: year,
    month: month,
    targets: targets as RequestTarget[],
  };

  return requestData;
}
