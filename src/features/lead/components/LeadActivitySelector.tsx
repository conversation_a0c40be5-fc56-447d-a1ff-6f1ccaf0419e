import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { px } from 'cube-ui-components';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { useContext } from 'react';
import { PressableProps, View, ViewProps } from 'react-native';
import { feedback } from './LeadActivityModal';

export type ToggleState = 'default' | 'selected' | 'disabled' | 'error';
export type ToggleMode = 'outlined' | 'flat';

export interface ToggleProps extends Omit<PressableProps, 'children'> {
  value: string;
  mode?: ToggleMode;
  onSelect?: (selected: boolean) => void;
  children?:
    | React.ReactNode
    | ((data: {
        color: string;
        normal: boolean;
        selected: boolean;
        disabled: boolean;
        isError: boolean;
        state: ToggleState;
      }) => React.ReactElement<unknown>);
  selected?: boolean;
  disabled?: boolean;
  isError?: boolean;
  rounded?: boolean;
  disabledWithoutChangingTheme?: boolean;
}

export default function LeadActivitySelector({
  value,
  mode = 'outlined',
  onSelect,
  children,
  selected: selectedProp,
  disabled: disabledProp,
  isError: isErrorProp,
  rounded,
  disabledWithoutChangingTheme: disabledWithoutChangingThemeProp,
  ...viewProps
}: ToggleProps) {
  const {
    components: {
      toggle: { colors },
    },
    colors: themeColor,
  } = useTheme();

  let selected = selectedProp;
  let isError = isErrorProp;
  let disabled = disabledProp;
  let disabledWithoutChangingTheme = disabledWithoutChangingThemeProp;
  const toggleCtx = useToggleContext();
  if (toggleCtx !== undefined) {
    selected = toggleCtx.value === value;
    isError = toggleCtx.isError;
    disabled = toggleCtx.disabled;
    disabledWithoutChangingTheme = toggleCtx.disabledWithoutChangingTheme;
  }

  let contentColor = colors[mode].content.default;
  let state: ToggleState = 'default';
  if (disabled) {
    if (!disabledWithoutChangingTheme)
      contentColor = colors[mode].content.disabled;
    state = 'disabled';
  } else if (selected) {
    if (disabled) {
      if (disabledWithoutChangingTheme)
        contentColor = colors[mode].content.selected;
      else contentColor = colors[mode].content.selectedDisabled;
    } else {
      contentColor = colors[mode].content.selected;
    }
    state = 'selected';
  } else if (isError) {
    contentColor = colors[mode].content.error;
    state = 'error';
  }

  const onPress = () => {
    onSelect?.(!selected);
    toggleCtx?.onChange?.(value);
  };

  return (
    <Container
      mode={mode}
      disabled={disabled}
      rounded={rounded}
      isError={isError}
      selected={selected}
      onPress={onPress}
      disabledWithoutChangingTheme={disabledWithoutChangingTheme}
      {...viewProps}>
      <View
        style={{
          ...(!selected && {
            borderWidth: 1,
            borderColor: themeColor.background,
          }),
        }}>
        {typeof children === 'function'
          ? children({
              color: contentColor,
              selected: Boolean(selected),
              disabled: Boolean(disabled),
              isError: Boolean(isError),
              normal: !selected && !disabled && !isError,
              state,
            })
          : children}
      </View>
    </Container>
  );
}

type ContainerProps = PressableProps &
  Pick<
    ToggleProps,
    | 'mode'
    | 'disabled'
    | 'rounded'
    | 'selected'
    | 'isError'
    | 'disabledWithoutChangingTheme'
  >;
const Container = styled.Pressable<ContainerProps>`
  // justify-content: center;
  // align-items: center;
  flex-direction: row;
  padding: ${({ mode, rounded, theme: { space } }) => {
    if (mode === 'outlined') {
      if (rounded) return px(space[4]);
      return px(space[4]);
    }
    return px(space[0]);
  }};
  border-radius: ${({ mode, rounded, theme: { borderRadius } }) => {
    if (mode === 'outlined') {
      if (rounded) return px(borderRadius.full);
      return px(borderRadius['small']);
    }
    return px(borderRadius.none);
  }};
  background-color: ${({
    mode = 'outlined',
    isError,
    selected,
    disabled,
    disabledWithoutChangingTheme,
    theme: {
      components: { toggle },
    },
  }) => {
    if (disabled && !disabledWithoutChangingTheme) {
      return toggle.colors[mode].background.disabled;
    }
    if (selected) {
      if (disabled && !disabledWithoutChangingTheme) {
        return toggle.colors[mode].background.selectedDisabled;
      }
      return toggle.colors[mode].background.selected;
    }
    if (isError) {
      return toggle.colors[mode].background.error;
    }
    return toggle.colors[mode].background.default;
  }};
  border-color: ${({
    mode = 'outlined',
    isError,
    selected,
    disabled,
    disabledWithoutChangingTheme,
    theme: {
      components: { toggle },
    },
  }) => {
    if (disabled && !disabledWithoutChangingTheme)
      return toggle.colors[mode].border.disabled;
    if (selected) {
      if (disabled && !disabledWithoutChangingTheme) {
        return toggle.colors[mode].border.selectedDisabled;
      }
      return toggle.colors[mode].border.selected;
    }
    if (isError) {
      return toggle.colors[mode].border.error;
    }
    return toggle.colors[mode].border.default;
  }};
  border-width: ${({ mode, selected }) => {
    if (mode === 'outlined') {
      if (selected) return '2px';
      return '1px';
    }
    return '0px';
  }};
`;

export interface ToggleGroupProps
  extends Pick<
      ToggleProps,
      'mode' | 'disabled' | 'rounded' | 'disabledWithoutChangingTheme'
    >,
    ViewProps {
  value?: string;
  onChange?: (value: keyof typeof feedback) => void;
  isError?: boolean;
  disabled?: boolean;
}

type ToggleContextType = Pick<
  ToggleProps,
  'mode' | 'disabled' | 'isError' | 'disabledWithoutChangingTheme'
> & {
  value?: string;
  onChange?: (item: string) => void;
};

const LeadActivitySelectorContext = React.createContext<
  ToggleContextType | undefined
>(undefined);

export const LeadActivitySelectorGroup = forwardRef(
  (
    {
      mode,
      value,
      onChange,
      isError,
      disabled,
      children,
      disabledWithoutChangingTheme,
    }: ToggleGroupProps,
    ref: React.ForwardedRef<unknown>, // ref is not consumed by toggle group, passing ref to remove warning
  ): JSX.Element => {
    useImperativeHandle(ref, () => null);
    const context = useMemo(
      () => ({
        value,
        onChange,
        mode,
        disabled,
        isError,
        disabledWithoutChangingTheme,
      }),
      [value, onChange, mode, disabled, isError, disabledWithoutChangingTheme],
    );
    return (
      <LeadActivitySelectorContext.Provider value={context}>
        {children}
      </LeadActivitySelectorContext.Provider>
    );
  },
);

const useToggleContext = () => useContext(LeadActivitySelectorContext);

export { useToggleContext };
