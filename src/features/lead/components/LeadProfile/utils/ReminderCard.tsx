import React from 'react';
import styled from '@emotion/native';
import {
  Box,
  H6,
  Icon,
  Label,
  LargeBody,
  SmallLabel,
  XView,
} from 'cube-ui-components';
import RoundButton from './RoundButton';
import { LeadProfileFNAStatus } from 'types/leadProfile';
import { useTranslation } from 'react-i18next';
import { TextStyle, View, ViewStyle } from 'react-native';
import { useTheme } from '@emotion/react';
import PDFSVG from '../assets/icons/PDFSVG';

interface ReminderCardProps {
  onPress: () => void;
  status: LeadProfileFNAStatus;
  actionTitle: string;
  content: string;
  title: string;
  type: 'fna' | 'rpq';
}

export default function ReminderCard({
  status,
  onPress,
  actionTitle,
  content,
  title,
  type,
}: ReminderCardProps) {
  const { space } = useTheme();
  const { t } = useTranslation('leadProfile');
  const iconTitle =
    status === 'Completed' ? (
      <Icon.Tick width={40} height={40} />
    ) : (
      <Icon.Warning width={30} height={30} />
    );

  const statusTitle =
    status === 'Completed'
      ? t('leadProfile.fna.valid')
      : t('leadProfile.fna.expired');

  return (
    <View>
      <TitleAndBtnContainer>
        <IconAndTitleContainer>
          {iconTitle}
          <Box w={space[1]} />
          <H6 fontWeight="bold">{title}</H6>
        </IconAndTitleContainer>

        {type === 'fna' && <RoundButton text={actionTitle} onPress={onPress} />}
      </TitleAndBtnContainer>
      <ContentWrapper>
        {content != '' && (
          <Content>
            {status !== 'Start' && t('leadProfile.fna.expiryDate') + ': '}
            {content}
          </Content>
        )}
        {status !== 'Start' && content != '' && (
          <StatusBadge status={status}>
            <StatusTitle status={status}>{statusTitle}</StatusTitle>
          </StatusBadge>
        )}
      </ContentWrapper>
    </View>
  );
}

const TitleAndBtnContainer = styled(XView)(({ theme }) => ({
  alignItems: 'center',
  justifyContent: 'space-between',
  flexDirection: 'row',
}));

const IconAndTitleContainer = styled(XView)(({ theme }) => ({
  alignItems: 'center',
  flexDirection: 'row',
}));

const ContentWrapper = styled(XView)(({ theme }) => ({
  alignItems: 'center',
  flexDirection: 'row',
  marginTop: theme.space[2],
}));

const Content = styled(LargeBody)(({ theme }) => ({
  marginLeft: 40,
}));

const StatusBadge = styled(Box)<ViewStyle & { status: LeadProfileFNAStatus }>(
  ({ theme, status }) => ({
    paddingHorizontal: theme.space[1],
    paddingVertical: 2,
    backgroundColor:
      status === 'Completed'
        ? theme.colors.palette.alertGreen
        : theme.colors.palette.alertRedLight,
    marginLeft: theme.space[2],
    borderRadius: 2,
  }),
);

const StatusTitle = styled(SmallLabel)<
  TextStyle & { status: LeadProfileFNAStatus }
>(({ theme, status }) => ({
  color:
    status === 'Completed'
      ? theme.colors.palette.white
      : theme.colors.palette.alertRed,
}));

const IconTitle = styled(Label)(({ theme }) => ({
  marginLeft: theme.space[1],
  color: theme.colors.primary,
}));
