import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import ResponsiveView from 'components/ResponsiveView';
import {
  Box,
  Card,
  Column,
  H6,
  H7,
  Icon,
  LargeBody,
  Row,
  SmallBody,
} from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import { Modal, Pressable, ScrollView, TouchableOpacity } from 'react-native';
import DigitalCXAward from './assets/DigitalCXAward';
import ProtectionScore from './assets/icons/ProtectionScore';
import LeadProfileProtectionScoreBar from './LeadProfileProtectionScoreBar';
import { WINDOW_HEIGHT } from '@gorhom/bottom-sheet';

interface ModalProps {
  visible: boolean;
  closeHandler: () => void;
}

export default function ViewAllScoreModal({
  visible,
  closeHandler,
}: ModalProps) {
  const { t } = useTranslation(['leadProfile']);
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { sizes, borderRadius, colors, animation, space } = useTheme();

  const PROTECTION_GROUP = [
    {
      title: t('leadProfile:leadProfile.viewAllScoreLevels.underProtected'),
      score: 40,
      scoreRange: '0 - 40',
      description: t(
        'leadProfile:leadProfile.viewAllScoreLevels.underProtectedDesc',
      ),
    },
    {
      title: t(
        'leadProfile:leadProfile.viewAllScoreLevels.moderatelyProtected',
      ),
      score: 80,
      scoreRange: '41 - 80',
      description: t(
        'leadProfile:leadProfile.viewAllScoreLevels.moderatelyProtectedDesc',
      ),
    },
    {
      title: t('leadProfile:leadProfile.viewAllScoreLevels.wellProtected'),
      score: 100,
      scoreRange: '81 - 100',
      description: t(
        'leadProfile:leadProfile.viewAllScoreLevels.wellProtectedDesc',
      ),
    },
  ];

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={closeHandler}>
      <Container narrowStyle={{ paddingHorizontal: sizes[3] }}>
        <ModalCard
          isWideScreen={isWideScreen}
          isNarrowScreen={isNarrowScreen}
          borderRadius="x-large">
          <Column
            style={{ paddingHorizontal: isNarrowScreen ? space[3] : space[6] }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={closeHandler}>
                <Icon.Close fill={colors.onBackground}></Icon.Close>
              </TouchableOpacity>
            </Row>
            <Row>
              <H6 fontWeight="bold">
                {t(
                  'leadProfile:leadProfile.viewAllScoreLevels.protectionScore',
                )}
              </H6>
            </Row>
          </Column>
          <ScrollView
            showsVerticalScrollIndicator
            scrollIndicatorInsets={{ right: 8 }}
            style={{ paddingHorizontal: isNarrowScreen ? space[3] : space[6] }}>
            <Row justifyContent="center" paddingY={space[4]}>
              <Column alignItems="center" gap={space[2]}>
                <DigitalCXAward />
                <SmallBody color={colors.palette.fwdGreyDarker}>
                  {t('leadProfile:leadProfile.viewAllScoreLevels.digitalAward')}
                </SmallBody>
              </Column>
            </Row>

            <Column gap={space[6]}>
              {PROTECTION_GROUP.map((item, index) => {
                return (
                  <Box key={index} gap={space[4]}>
                    <Row justifyContent="space-between" alignItems="center">
                      <H6 fontWeight="bold">{item.title}</H6>
                      <Row gap={6}>
                        <ProtectionScore />
                        <H7>{item.scoreRange}</H7>
                      </Row>
                    </Row>
                    <LeadProfileProtectionScoreBar score={item.score} />
                    <LargeBody style={{ paddingRight: space[10] }}>
                      {item.description}
                    </LargeBody>
                  </Box>
                );
              })}
            </Column>
          </ScrollView>
        </ModalCard>
      </Container>
    </Modal>
  );
}

const Container = styled(ResponsiveView)(({ theme }) => {
  return {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: theme.space[4],
  };
});
const ModalCard = styled(Card)<{
  isWideScreen: boolean;
  isNarrowScreen: boolean;
}>(({ theme, isWideScreen, isNarrowScreen }) => {
  return {
    width: isWideScreen ? '60%' : '100%',
    paddingVertical: theme.space[6],
    paddingHorizontal: isNarrowScreen ? theme.space[2] : 0,
    maxHeight: isNarrowScreen ? WINDOW_HEIGHT * 0.8 : WINDOW_HEIGHT * 0.85,
  };
});
