import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import ProgressBar from 'features/lead/components/LeadProfile/Fna/ProtectionScoreBar';
import RoundBorderTag from 'components/RoundBorderTag';
import { Column, Row, Typography } from 'cube-ui-components';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import { useTranslation } from 'react-i18next';
import { SvgProps } from 'react-native-svg';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';

const SavingNeedsCard = ({
  tagText,
  Icon,
  labelText,
  currency,
  target,
  progress,
  tagConfig,
  chartConfig,
}: {
  tagText: string;
  Icon:
    | ((props: SvgPictogramProps) => JSX.Element)
    | ((props: SvgProps) => React.JSX.Element);
  labelText: string;
  currency: string;
  target: number;
  progress: number;
  tagConfig?: { tagColor?: string; isShowTag?: boolean };
  chartConfig?: {
    isShowLabel?: boolean;
    isShowArrow?: boolean;
  };
}) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation('leadProfile');
  return (
    <CardContainer>
      <Column gap={space[2]} alignItems="center">
        <Column alignItems="center">
          {tagConfig?.isShowTag ? (
            <RoundBorderTag text={tagText} color={tagConfig?.tagColor} />
          ) : (
            <Column height={17} />
          )}
          <Icon width={58} height={58} />
          <Typography.LargeLabel color={colors.secondary} fontWeight="bold">
            {labelText}
          </Typography.LargeLabel>
        </Column>

        <Column alignItems="center">
          <Typography.SmallBody color="#878787">
            {t('leadProfile.fna.savingNeeds')}
          </Typography.SmallBody>
          <Row gap={2}>
            <Column paddingTop={2}>
              <Typography.SmallLabel color={colors.primary}>
                {currency}
              </Typography.SmallLabel>
            </Column>
            <Typography.LargeLabel color={colors.primary} fontWeight="bold">
              {/* TODO:  */}
              {numberToThousandsFormat(target)}
            </Typography.LargeLabel>
          </Row>
        </Column>

        <ProgressBar current={progress} target={target} config={chartConfig} />
      </Column>
    </CardContainer>
  );
};

const CardContainer = styled(Column)(({ theme: { space, borderRadius } }) => ({
  gap: 19,
  flex: 1,
  alignItems: 'center',
  overflow: 'hidden',
}));

export default SavingNeedsCard;
