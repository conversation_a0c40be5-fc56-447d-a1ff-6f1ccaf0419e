import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

function ProtectionScore() {
  return (
    <Svg width={20} height={20} viewBox="0 0 20 20" fill="none">
      <Circle cx={10} cy={10} r={10} fill="#FED141" />
      <Circle cx={10.0009} cy={10.0009} r={7.69231} fill="#E8A63A" />
      <Path
        d="M14.383 9.422l-2.015 1.843.595 2.737a.49.49 0 01-.181.515c-.155.109-.362.136-.517.028l-2.299-1.437-2.298 1.437c-.155.108-.362.08-.517-.027a.49.49 0 01-.18-.516l.593-2.737L5.55 9.422a.478.478 0 01-.13-.543.513.513 0 01.414-.352l2.686-.244 1.059-2.602a.477.477 0 01.439-.298c.18 0 .362.108.439.298l1.059 2.602 2.686.244c.181.027.362.163.414.352-.026.217-.104.434-.233.543z"
        fill="#FFF6D9"
      />
    </Svg>
  );
}

export default ProtectionScore;
