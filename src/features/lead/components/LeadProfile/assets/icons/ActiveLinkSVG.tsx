import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

export default function ActiveLinkSVG(props: SvgIconProps) {
  return (
    <Svg width="24" height="27" viewBox="0 0 24 27" fill="none">
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0 7V12H10.2607C13.0221 12 15.2607 14.2386 15.2607 17C15.2607 19.7614 13.0221 22 10.2607 22H0V27H10C15.5228 27 20 22.5228 20 17C20 11.4772 15.5228 7 10 7H0Z"
        fill="#FEF9F4"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4 10C4 4.47715 8.47715 0 14 0H24V5H13.7402C10.9788 5 8.74023 7.23858 8.74023 10C8.74023 12.7614 10.9788 15 13.7402 15H24V20H14C8.47715 20 4 15.5228 4 10Z"
        fill="#F3BB90"
      />
      <Rect x="4" y="7" width="6" height="5" fill="#FEF9F4" />
    </Svg>
  );
}
