import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Icon, Typography, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Feedback } from '../type';
import Button from './Button';
import { country } from 'utils/context';

const IS_PH = country === 'ph';

export default function LeadActivityFirstPrompt({
  title,
  onSelect,
  onSkip,
  onSkipAll,
  onCall,
  isSkipAllAllowed,
}: {
  title: string;
  onSelect: (value: Feedback) => void;
  onSkip: () => void;
  onSkipAll: () => void;
  onCall: () => void;
  isSkipAllAllowed: boolean;
}) {
  const { t } = useTranslation(['lead']);
  const { colors, space, sizes, animation } = useTheme();

  const showDeferredOption = IS_PH;

  const [selectedBtn, setSelectedBtn] = React.useState<Feedback | string>('');

  const LEADS_FEEDBACK_TOGGLE_CONFIG = [
    {
      label: t('lead:activity.interestedLabel'),
      value: Feedback.I,
      Icon: Icon.VeryInterested,
    },
    {
      label: t('lead:activity.deferredLabel'),
      value: Feedback.D,
      Icon: Icon.Thinking,
    },
    {
      label: t('lead:activity.notInterestedLabel'),
      value: Feedback.NI,
      Icon: Icon.NotInterested,
    },
  ];

  const handleSelect = (value: Feedback) => {
    setSelectedBtn(value);
    setTimeout(() => onSelect(value), 300);
  };

  return (
    <Animated.View
      style={{ width: 343 }}
      entering={FadeIn.duration(animation.duration * 0.5)}
      exiting={FadeOut.duration(animation.duration)}>
      <ModalCard>
        <TitleContainer>
          <Typography.H6 fontWeight="bold">{title}</Typography.H6>
          <Typography.LargeBody>
            {t('lead:activity.leadResponse')}
          </Typography.LargeBody>
        </TitleContainer>

        <BtnContainer>
          {LEADS_FEEDBACK_TOGGLE_CONFIG.map(({ value, Icon, label }) => {
            if (value === Feedback.D && !showDeferredOption) return null; // Hide the deferred option if not applicable
            return (
              <React.Fragment key={value}>
                <Button
                  isSelected={selectedBtn === value}
                  onPress={() => handleSelect(value)}
                  label={label}
                  Icon={Icon}
                />
              </React.Fragment>
            );
          })}
        </BtnContainer>

        <OtherOptionsContainer>
          <CallNowSection>
            <Typography.LargeLabel>
              {t('lead:activity.haveNotContacted')}
            </Typography.LargeLabel>
            <TouchableOpacity onPress={() => onCall()}>
              <Row alignItems="center" gap={space[1]}>
                <Icon.Call
                  size={sizes[5]}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
                <Typography.Label
                  fontWeight="bold"
                  children={t('lead:activity.callNow')}
                  color={colors.palette.fwdAlternativeOrange[100]}
                />
              </Row>
            </TouchableOpacity>
          </CallNowSection>
          <Separator />
          <Column gap={space[2]} alignItems="center">
            <TouchableOpacity
              onPress={() => (isSkipAllAllowed ? onSkipAll() : onSkip())}>
              <Typography.Label
                fontWeight="bold"
                color={colors.palette.fwdAlternativeOrange[100]}>
                {isSkipAllAllowed
                  ? t('lead:activity.skipAllButton')
                  : t('lead:activity.skip')}
              </Typography.Label>
            </TouchableOpacity>

            {isSkipAllAllowed && (
              <Typography.Label
                color={colors.palette.fwdGreyDarker}
                style={{ textAlign: 'center' }}>
                {t('lead:activity.skipHint')}
              </Typography.Label>
            )}
          </Column>
        </OtherOptionsContainer>
      </ModalCard>
    </Animated.View>
  );
}

const TitleContainer = styled(Column)(({ theme: { space } }) => {
  return {
    gap: space[4],
  };
});

const BtnContainer = styled(Column)(({ theme: { space } }) => {
  return {
    marginTop: space[3],
    gap: space[3],
  };
});
const ModalCard = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => {
    return {
      backgroundColor: colors.background,
      padding: space[6],
      borderRadius: borderRadius.large,
    };
  },
);

const OtherOptionsContainer = styled(Column)(({ theme: { space } }) => {
  return {
    marginTop: space[4],
    gap: space[4],
  };
});

const CallNowSection = styled(Row)(() => {
  return {
    alignItems: 'center',
    justifyContent: 'space-between',
  };
});

const Separator = styled(Column)(({ theme: { colors } }) => {
  return {
    backgroundColor: colors.palette.fwdGrey[100],
    height: 1,
    width: '100%',
  };
});
