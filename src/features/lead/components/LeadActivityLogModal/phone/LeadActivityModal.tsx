import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H6, Icon, LargeBody } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useState } from 'react';
import { Feedback } from '../type';
import React from 'react';
import Button from './Button';
import { country } from 'utils/context';

const IS_PH = country === 'ph';

export default function LeadActivityModal({
  onSelect,
}: {
  onSelect: (value: Feedback | string) => void;
}) {
  const { t } = useTranslation(['lead']);
  const { sizes, animation } = useTheme();

  const showDeferredOption = IS_PH;

  const [selectedValue, setSelectedValue] = useState('');

  const LEADS_FEEDBACK_TOGGLE_CONFIG = [
    {
      label: t('lead:activity.interestedLabel'),
      value: Feedback.I,
      Icon: Icon.VeryInterested,
    },
    {
      label: t('lead:activity.deferredLabel'),
      value: Feedback.D,
      Icon: Icon.Thinking,
    },
    {
      label: t('lead:activity.notInterestedLabel'),
      value: Feedback.NI,
      Icon: Icon.NotInterested,
    },
    {
      label: t('lead:activity.noAnswerLabel'),
      value: Feedback.N,
      Icon: Icon.Neutral,
    },
  ];

  const handleSelect = (value: Feedback) => {
    setSelectedValue(value);
    setTimeout(() => onSelect(value), 300);
  };

  return (
    <Animated.View
      style={{ width: 343 }}
      entering={FadeIn.duration(animation.duration * 0.5)}
      exiting={FadeOut.duration(animation.duration)}>
      <ModalCard>
        <H6 fontWeight="bold" style={{ padding: sizes[1] }}>
          {t('lead:activity.callFeedBack')}
        </H6>
        <LeadResponse>{t('lead:activity.leadResponse')}</LeadResponse>
        <ButtonContainer>
          {LEADS_FEEDBACK_TOGGLE_CONFIG.map(({ value, label, Icon }) => {
            if (value === Feedback.D && !showDeferredOption) return null; // Hide the deferred option if not applicable
            return (
              <React.Fragment key={value}>
                <Button
                  isSelected={value === selectedValue}
                  onPress={() => {
                    handleSelect(value);
                  }}
                  label={label}
                  Icon={Icon}
                />
              </React.Fragment>
            );
          })}
        </ButtonContainer>
      </ModalCard>
    </Animated.View>
  );
}

const ModalCard = styled(Column)(({ theme }) => {
  return {
    padding: theme.space[6],
    justifyContent: 'center',
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.large,
  };
});

const LeadResponse = styled(LargeBody)(({ theme }) => {
  return {
    marginTop: theme.space[4],
    marginBottom: theme.space[2],
  };
});

const ButtonContainer = styled(Column)(({ theme }) => {
  return {
    width: '100%',
    gap: theme.space[3],
  };
});
