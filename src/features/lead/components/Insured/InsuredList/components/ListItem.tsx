import styled from '@emotion/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { memo, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { EntityInsured } from 'types/entityInsured';
import { getFullName } from 'features/lead/utils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export interface ListItemProps {
  data: EntityInsured;
  onPress?: (data: EntityInsured) => void;
}

const Wrapper = styled.View<{ isNarrowScreen: boolean }>(
  ({ theme, isNarrowScreen }) => ({
    paddingTop: theme.space[5],
    paddingHorizontal: isNarrowScreen ? theme.space[3] : theme.space[4],
  }),
);
const Container = styled.TouchableHighlight<{ isNarrowScreen: boolean }>(
  ({ theme, isNarrowScreen }) => ({
    backgroundColor: theme.colors.background,
    paddingVertical: theme.space[3],
    paddingLeft: isNarrowScreen ? theme.space[3] : theme.space[4],
    paddingRight: theme.space[2],
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.borderRadius.large,
  }),
);

interface Props {
  data: EntityInsured;
  onPress?: (data: EntityInsured) => void;
  disabled?: boolean;
}

const ListItem = memo(function ListItem({ data, onPress, disabled }: Props) {
  const { colors, space } = useTheme();

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const label = useMemo(
    () =>
      getFullName({
        firstName: data.firstName,
        lastName: data.lastName,
        middleName: data.middleName,
      }),
    [data],
  );
  return (
    <Wrapper isNarrowScreen={isNarrowScreen}>
      <Container
        isNarrowScreen={isNarrowScreen}
        disabled={disabled}
        onPress={() => onPress?.(data)}
        underlayColor={colors.palette.fwdGrey[50]}>
        <Row alignItems="center">
          <Column flex={1}>
            <Typography.LargeLabel fontWeight="bold" color={colors.primary}>
              {label}
            </Typography.LargeLabel>
            <Box h={space[1]} />
            <Typography.Label color={colors.onBackground}>
              {`${
                data.mobilePhoneCountryCode
                  ? `+${data.mobilePhoneCountryCode} `
                  : ''
              }${data.mobilePhoneNumber}`}
            </Typography.Label>
            <Typography.Label color={colors.onBackground}>
              {data.email}
            </Typography.Label>
          </Column>
          <Icon.ChevronRight fill={colors.secondaryVariant} />
        </Row>
      </Container>
    </Wrapper>
  );
});

export default ListItem;
