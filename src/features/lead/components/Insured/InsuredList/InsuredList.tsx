import { forwardRef, memo, useMemo } from 'react';
import styled from '@emotion/native';
import ListItem, { ListItemProps } from './components/ListItem';
import { Footer } from './components/Footer';
import {
  FlatList,
  FlatListProps,
  RefreshControl,
  ViewStyle,
} from 'react-native';
import Animated, { AnimatedStyleProp } from 'react-native-reanimated';
import { EntityInsured } from 'types/entityInsured';
import { useTheme } from '@emotion/react';

type RNGHFlatListProps<T> = Animated.AnimateProps<
  FlatListProps<T> & {
    ref: React.Ref<FlatList<T>>;
  }
>;

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList,
) as unknown as <T>(props: RNGHFlatListProps<T>) => React.ReactElement;

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.surface,
}));
interface Props extends Partial<FlatListProps<EntityInsured>> {
  data: EntityInsured[];
  onPressItem: ListItemProps['onPress'];
  refreshing?: boolean;
  onRefresh?: () => void;
  loadingMore?: boolean;
  onLoadMore?: () => void;
  renderFooter?: (
    loading?: boolean,
  ) => FlatListProps<EntityInsured>['ListFooterComponent'];
  refreshEnable?: boolean;
  loadMoreEnable?: boolean;
  headerStyle?: AnimatedStyleProp<ViewStyle>;
  progressViewOffset?: number;
  headerLast?: boolean;
}

const InsuredList = memo(
  forwardRef<FlatList<EntityInsured>, Props>(function InsuredList(
    {
      data,
      onPressItem,
      onLoadMore,
      loadingMore,
      renderFooter,
      refreshing,
      onRefresh,
      refreshEnable,
      loadMoreEnable,
      headerStyle,
      progressViewOffset,
      headerLast,
      ...rest
    }: Props,
    ref,
  ) {
    const { space } = useTheme();
    const renderItem = ({
      item,
      index,
    }: {
      item: EntityInsured;
      index: number;
    }) => <ListItem onPress={() => onPressItem?.(item)} data={item} />;

    const footer = useMemo(
      () =>
        renderFooter ? (
          renderFooter(loadingMore)
        ) : (
          <Footer loading={loadingMore} />
        ),
      [renderFooter, loadingMore],
    );

    return (
      <Container>
        <AnimatedFlatList
          ref={ref}
          data={data}
          renderItem={renderItem}
          ListFooterComponent={footer}
          contentContainerStyle={{ paddingBottom: space[23] }}
          onEndReached={loadMoreEnable && onLoadMore ? onLoadMore : undefined}
          refreshControl={
            refreshEnable ? (
              <RefreshControl
                refreshing={!!refreshing}
                onRefresh={onRefresh}
                progressViewOffset={progressViewOffset}
              />
            ) : undefined
          }
          {...rest}
        />
      </Container>
    );
  }),
);

export default InsuredList;
