import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import ResponsiveView from 'components/ResponsiveView';
import {
  Body,
  Card,
  H6,
  H7,
  Icon,
  LargeBody,
  SvgIconProps,
} from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, TouchableOpacity } from 'react-native';
import LeadActivitySelector, {
  LeadActivitySelectorGroup,
} from 'features/lead/ph/tablet/components/LeadActivitySelector';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

export type messageProps = {
  message:
    | 'interested'
    | 'notInterested'
    | 'noAnswer'
    | ''
    | 'callFeedBack'
    | 'deferred';
  leadName?: string;
};

interface ModalProps {
  visible: boolean;
  closeHandler: () => void;
  message: messageProps;
  onSelect: (value: keyof typeof feedback | null) => void;
  isFourth?: boolean;
  loadingNext?: boolean;
}
export enum feedback {
  I,
  D,
  NI,
  N,
}
export default function LeadActivityModalV2({
  visible,
  closeHandler,
  message,
  onSelect,
  loadingNext,
  isFourth = false,
}: ModalProps) {
  const { t } = useTranslation(['lead']);
  const [value, setValue] = useState('');
  const [disabled, setDisabled] = useState(false);
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const LEADS_FEEDBACK_TOGGLE_CONFIG = [
    {
      label: t('lead:activity.interestedLabel'),
      value: feedback[0],
      icon: Icon.VeryInterested,
    },
    // {
    //   label: t('lead:activity.deferredLabel'),
    //   value: feedback[1],
    //   icon: Icon.Thinking,
    // },
    {
      label: t('lead:activity.notInterestedLabel'),
      value: feedback[2],
      icon: Icon.NotInterested,
    },
    {
      label: t('lead:activity.noAnswerLabel'),
      value: feedback[3],
      icon: Icon.Neutral,
    },
  ];
  const handleSelect = (value: keyof typeof feedback) => {
    setDisabled(true);
    setValue(value);
    onSelect(value);
  };
  useEffect(() => {
    if (visible) {
      setDisabled(false);

      setValue('');
    }
  }, [visible]);
  useEffect(() => {
    if (loadingNext) {
      setDisabled(false);
      setValue('');
    }
  }, [loadingNext]);
  const { sizes, borderRadius, colors, animation } = useTheme();
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={closeHandler}>
      <Container narrowStyle={{ paddingHorizontal: sizes[3] }}>
        {!loadingNext ? (
          <ModalCard
            isWideScreen={isWideScreen}
            isNarrowScreen={isNarrowScreen}
            borderRadius="x-large">
            <Animated.View
              entering={FadeIn.duration(animation.duration * 0.5)}
              exiting={FadeOut.duration(animation.duration)}>
              <H6 fontWeight="bold" style={{ padding: sizes[1] }}>
                {t(
                  `lead:activity.${
                    message?.message ? message?.message : 'noAnswer'
                  }`,
                  {
                    leadName: message?.leadName,
                  },
                )}
              </H6>
              <LeadResponse>{t('lead:activity.leadResponse')}</LeadResponse>
              <LeadActivitySelectorGroup
                value={value}
                onChange={handleSelect}
                disabled={disabled}
                disabledWithoutChangingTheme>
                <ToggleGroupContainer narrowStyle={{ gap: sizes[2] }}>
                  {LEADS_FEEDBACK_TOGGLE_CONFIG.map((item, i) => {
                    return (
                      <ToggleContainer
                        key={'LEADS_FEEDBACK_BTN_' + item.value + i}>
                        <LeadActivitySelector
                          value={item.value}
                          style={{
                            borderRadius: borderRadius['x-small'],
                            minWidth: sizes[37],
                            maxWidth: sizes[42],
                            height: sizes[32],
                          }}>
                          {({ color, selected }) => (
                            <ToggleChildren>
                              <item.icon size={sizes[14]} />
                              <ToggleResponse
                                color={selected ? color : color}
                                fontWeight={selected ? 'bold' : 'bold'}>
                                {item.label}
                              </ToggleResponse>
                            </ToggleChildren>
                          )}
                        </LeadActivitySelector>
                      </ToggleContainer>
                    );
                  })}
                </ToggleGroupContainer>
              </LeadActivitySelectorGroup>
              {isFourth && (
                <>
                  <TouchableOpacity onPress={() => onSelect(null)}>
                    <SkipAll fontWeight="bold">
                      {t('lead:activity.skipAllButton')}
                    </SkipAll>
                  </TouchableOpacity>
                  <SkipHint>{t('lead:activity.skipHint.tablet')}</SkipHint>
                </>
              )}
            </Animated.View>
          </ModalCard>
        ) : (
          <></>
        )}
      </Container>
    </Modal>
  );
}

const Container = styled(ResponsiveView)(({ theme }) => {
  return {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: theme.space[4],
  };
});
const ModalCard = styled(Card)<{
  isWideScreen: boolean;
  isNarrowScreen: boolean;
}>(({ theme, isWideScreen, isNarrowScreen }) => {
  return {
    // width: isWideScreen ? '60%' : '100%',
    paddingVertical: theme.space[12],
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: isNarrowScreen ? theme.space[5] : theme.space[12],
  };
});
// const ModalCard = Animated.createAnimatedComponent(StyledModalCard);

const LeadResponse = styled(H7)(({ theme }) => {
  return {
    marginTop: theme.space[4],
    marginBottom: theme.space[4],
    padding: theme.space[1],
  };
});
const ToggleGroupContainer = styled(ResponsiveView)(({ theme }) => {
  return {
    gap: theme.space[4],
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  };
});
const ToggleContainer = styled.View(({ theme }) => {
  return {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.space[1],
  };
});
const ToggleChildren = styled.View(({ theme }) => {
  return {
    flexDirection: 'column',
    alignItems: 'center',
    gap: theme.space[2],
  };
});

const ToggleResponse = styled(LargeBody)(({ theme }) => {
  return {};
});
const SkipHint = styled(Body)(({ theme }) => {
  return {
    textAlign: 'center',
    color: theme.colors.placeholder,
  };
});
const SkipAll = styled(Body)(({ theme }) => {
  return {
    textAlign: 'center',
    color: theme.colors.primary,
    paddingTop: theme.space[6],
    paddingBottom: theme.space[4],
  };
});
