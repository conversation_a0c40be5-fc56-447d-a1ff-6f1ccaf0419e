import { useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import useBoundStore from 'hooks/useBoundStore';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

// refactored from useClearCase at commit fa370470b967995de8d7d465c7f08abe4b4d339a
// for removing Require Cycle warning

export default function useClearCaseFocusEffect() {
  // reset caseId to Zustand if the agent quit the sales flow and re-enter the lead profile
  const caseActions = useBoundStore(store => store.caseActions);
  const resetFnaStoreState = useFnaStore(state => state.resetFnaStoreState);
  useFocusEffect(
    useCallback(() => {
      resetFnaStoreState();
      caseActions.clearActiveCase();
    }, []),
  );
}
