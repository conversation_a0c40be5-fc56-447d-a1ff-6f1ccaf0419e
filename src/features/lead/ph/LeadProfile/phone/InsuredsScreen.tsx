import React, { useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';

import { FlatList, LayoutChangeEvent, StyleSheet } from 'react-native';
import { LoadingMessage } from 'components/Message/LoadingMessage';
import { HEADER_HEIGHT } from 'features/savedProposals/proposalList/components/Header';
import {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import { QUERY_STATUS_HEIGHT } from 'features/savedProposals/components/QueryStatus';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import InsuredsEmptyCase from 'features/lead/components/Insured/Insureds/InsuredsEmptyCase';
import InsuredList from 'features/lead/components/Insured/InsuredList';
import { EntityInsured } from 'types/entityInsured';
import { UseQueryResult } from '@tanstack/react-query';
import { useGetEntityInsureds } from 'hooks/useGetEntityInsureds';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTheme } from '@emotion/react';
import { Box, FloatingButton, Icon } from 'cube-ui-components';
import ScrollScreenWrapper from '../components/ScrollScreenWrapper';

type InsuredsScreenProps = UseQueryResult<EntityInsured[], unknown> & {
  leadId: string;
};

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
  flexDirection: 'column-reverse',
}));

// export default function InsuredsScreen({ leadId }: InsuredsScreenProps) {
function InsuredsScreenInner({
  leadId,
  ...getInsuredsProps
}: InsuredsScreenProps) {
  const { t } = useTranslation(['lead']);
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

  const listRef = useRef<FlatList<EntityInsured>>(null);
  const contentHeight = useSharedValue(0);
  const svHeight = useSharedValue(0);
  const scrollClamp = useSharedValue(0);
  const floatingProgress = useSharedValue(1);

  const { isLoading, isRefetching, refetch, data } = getInsuredsProps;

  const onContentSizeChange = useCallback((w: number, h: number) => {
    contentHeight.value = h;
  }, []);

  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      svHeight.value = e.nativeEvent.layout.height;
    },
    [svHeight],
  );

  const onPressItem = useCallback(
    (data: EntityInsured) => {
      navigate('InsuredDetails', { data });
    },
    [navigate],
  );

  const scrollHandler = useAnimatedScrollHandler<{ prevY: number }>({
    onScroll: (event, ctx) => {
      const maxScroll = contentHeight.value - svHeight.value;
      const scrollY = event.contentOffset.y;
      if (scrollY > maxScroll) {
        return;
      }
      const diff = scrollY - ctx.prevY;
      const same =
        scrollY > 0 && scrollY >= maxScroll && ctx.prevY >= maxScroll;
      scrollClamp.value =
        scrollY <= 0
          ? 0
          : same
          ? scrollClamp.value
          : Math.min(
              Math.max(0, scrollClamp.value + diff),
              QUERY_STATUS_HEIGHT,
            );
      if (!same) {
        if (diff > 0 && scrollY > 0) {
          floatingProgress.value = 0;
        }
        if (diff < 0 || scrollY === 0) {
          floatingProgress.value = 1;
        }
      }
      ctx.prevY = scrollY;
    },
    onBeginDrag: (event, ctx) => {
      ctx.prevY = event.contentOffset.y;
    },
  });

  return (
    <>
      {Array.isArray(data) && data.length > 0 && (
        <Container>
          <InsuredList
            ref={listRef}
            data={data}
            onPressItem={onPressItem}
            refreshEnable={false}
            refreshing={isRefetching}
            onScroll={scrollHandler}
            scrollEventThrottle={16}
            scrollIndicatorInsets={scrollIndicatorInsets}
            progressViewOffset={HEADER_HEIGHT}
            onContentSizeChange={onContentSizeChange}
            onLayout={onLayout}
          />
          {isLoading && (
            <LoadingMessage
              style={{ ...StyleSheet.absoluteFillObject, top: HEADER_HEIGHT }}
              message={t('lead:lead.insured.loadingInsuredsMessage')}
            />
          )}
        </Container>
      )}
      {(!Array.isArray(data) || data.length === 0) && (
        <InsuredsEmptyCase leadId={leadId} />
      )}
    </>
  );
}

export default function InsuredsScreen({
  showLeadInfo,
  hideLeadInfo,
  leadId,
}: {
  showLeadInfo: () => void;
  hideLeadInfo: () => void;
  leadId: string;
}) {
  const getInsuredsProps = useGetEntityInsureds(leadId!, !!leadId);

  const { data } = getInsuredsProps;

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { space, colors } = useTheme();

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();

  const onPress = useCallback(() => {
    navigate('CreateInsured', { leadId });
  }, [leadId, navigate]);

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <ScrollScreenWrapper
        screen={<InsuredsScreenInner leadId={leadId} {...getInsuredsProps} />}
        showLeadInfo={showLeadInfo}
        hideLeadInfo={hideLeadInfo}
      />
      {data && data.length > 0 && (
        <FloatingButton
          onPress={onPress}
          style={{
            position: 'absolute',
            zIndex: 10,
            right: isNarrowScreen ? space[3] : space[4],
            bottom: space[4],
          }}
          elevation="low">
          <Icon.AddAccount fill={colors.onPrimary} />
        </FloatingButton>
      )}
    </Box>
  );
}

const scrollIndicatorInsets = { top: HEADER_HEIGHT };
