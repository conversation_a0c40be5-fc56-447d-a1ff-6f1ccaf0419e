import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { EmptyBottomView } from 'features/lead/components/LeadProfile/utils/leadProfileCommonStyle';
import ResponsiveView from 'components/ResponsiveView';
import InterestedToKnowAboutCard from 'features/lead/components/LeadProfile/Opportunities/InterestedToKnowAboutCard';
import LockedPersonaTypeCard from 'features/lead/components/LeadProfile/Opportunities/LockedPersonaTypeCard';
import LockedGapAnalysisCard from 'features/lead/components/LeadProfile/Opportunities/LockedGapAnalysisCard';
import LockedProtectionScoreCard from 'features/lead/components/LeadProfile/Opportunities/LockedProtectionScoreCard';
import ProductPeopleLikeYouHavePurchasedSection from 'features/lead/components/LeadProfile/Opportunities/ProductPeopleLikeYouHavePurchasedSection';
import { useTranslation } from 'react-i18next';
import i18n from 'utils/translation';
import ProtectionScoreCard from 'features/lead/components/LeadProfile/Opportunities/ProtectionScoreCard';

export default function OpportunitiesScreen({
  leadId,
  customerId,
}: {
  leadId?: string;
  customerId?: string;
}) {
  const { space } = useTheme();

  return (
    <>
      <MainContainer
        wideStyle={{
          padding: space[6],
          gap: space[5],
        }}>
        {/* <InterestedToKnowAboutCard leadId={leadId} customerId={customerId} /> */}
        <ProtectionScoreCard />
        {/* <LockedPersonaTypeCard /> */}

        {/* <LockedGapAnalysisCard /> */}

        {/* <LockedProtectionScoreCard /> */}

        <EmptyBottomView />
      </MainContainer>

      {/* // TODO-Mario: Temporary hide */}
      {/* <ProductPeopleLikeYouHavePurchasedSection /> */}
    </>
  );
}

const MainContainer = styled(ResponsiveView)(({ theme }) => ({
  padding: theme.space[4],
  gap: theme.space[4],
}));
