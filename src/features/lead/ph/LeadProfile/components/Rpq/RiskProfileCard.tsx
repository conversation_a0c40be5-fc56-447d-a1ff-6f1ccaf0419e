import React from 'react';
import styled from '@emotion/native';
import {
  Box,
  H5,
  H6,
  H8,
  LargeBody,
  LargeLabel,
  Row,
  SmallBody,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useTheme } from '@emotion/react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import ResponsiveView from 'components/ResponsiveView';
import { Rpq } from 'types/leadProfile';
import RiskLevelChart from './RiskLevelChart';
import { country } from 'utils/context';
import ResponsiveText from 'components/ResponsiveTypography';

export default function RiskProfileCard({ data }: { data: Rpq }) {
  const { t } = useTranslation('leadProfile');
  const { space, colors } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const riskLevel = data?.rpqScore ?? 0;
  const isIB = country === 'ib';

  return (
    <Container>
      <Row>
        <FirstColumn>
          <ScoreTitle>{t('leadProfile.fna.score')}</ScoreTitle>
          {isNarrowScreen ? (
            <SmallValueTitle fontWeight="bold">{data.rpqScore}</SmallValueTitle>
          ) : (
            <ValueTitle fontWeight="bold">{data.rpqScore}</ValueTitle>
          )}
        </FirstColumn>
        <SecondColumn>
          <ScoreTitle>{t('leadProfile.fna.riskLevel')}</ScoreTitle>
          <ResponsiveText
            TypographyDefault={isIB ? H6 : H5}
            TypographyNarrow={isIB ? H8 : H6}
            color={colors.primary}
            fontWeight="bold">
            {data.productRisk && data.productRisk.en}
          </ResponsiveText>
        </SecondColumn>
        <ThirdColumn
          narrowStyle={{
            width: 75,
          }}>
          <ScoreTitle>{t('leadProfile.fna.suitableProductRisk')}</ScoreTitle>
          {isNarrowScreen ? (
            <SmallValueTitle fontWeight="bold">
              {data.riskLevelLabel && data.riskLevelLabel.en}
            </SmallValueTitle>
          ) : (
            <ValueTitle fontWeight="bold">
              {data.riskLevelLabel && data.riskLevelLabel.en}
            </ValueTitle>
          )}
        </ThirdColumn>
      </Row>
      <RiskLevelChart riskLevel={riskLevel} />
      <RiskProfileInformation>
        <RiskProfileTitle fontWeight="bold">
          {t('leadProfile.fna.riskProfileTitle')}
        </RiskProfileTitle>
        <Box h={space[2]} />
        <RiskProfileDescription>
          {data.policyStatement && data.policyStatement.en}
        </RiskProfileDescription>
      </RiskProfileInformation>
    </Container>
  );
}

const RiskProfileTitle = styled(LargeLabel)(() => ({
  color: '#333333',
}));

const RiskProfileDescription = styled(LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const RiskProfileInformation = styled(View)(() => ({
  flex: 1,
}));

const Container = styled(View)(() => ({}));

const ThirdColumn = styled(ResponsiveView)(() => ({
  alignItems: 'flex-start',
  flexWrap: 'nowrap',
  // width: 80,
  flex: 2,
}));

const SecondColumn = styled(ResponsiveView)(() => ({
  flex: 3,
  alignItems: 'flex-start',
  flexWrap: 'nowrap',
}));
const FirstColumn = styled(ResponsiveView)(() => ({
  alignItems: 'flex-start',
  flex: 1,
}));
const ScoreTitle = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

const ValueTitle = styled(H5)(({ theme }) => ({
  color: theme.colors.primary,
}));

const SmallValueTitle = styled(H6)(({ theme }) => ({
  color: theme.colors.primary,
}));
