import React, { useMemo } from 'react';
import { Card } from 'features/lead/components/LeadProfile/Card';
import styled from '@emotion/native';
import { PictogramIcon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import FnaLabel from './FnaLabel';
import FamilySVG from 'features/lead/ib/LeadProfile/assets/FamilySVG';
import SavingNeedsCard from 'features/lead/components/LeadProfile/Fna/SavingNeedsCard';
import { addOrdinalSuffixOf } from 'utils/helper/numberToOrdinal';
import Timeline from 'features/lead/ib/LeadProfile/assets/Timeline';
import { FnaGoalData } from 'types/leadProfile';
import { goalsByNeed } from 'features/fna/constants/lifeJourney';

export const getTagColor = (priority: number, colors: any) => {
  switch (priority) {
    case 1:
      return colors.palette.fwdYellow[100];
    case 2:
      return colors.palette.fwdYellow[50];
    case 3:
      return colors.palette.fwdGrey[50];
    default:
      return colors.palette.fwdGrey[50];
  }
};

export default function SavingNeeds({ data }: { data: FnaGoalData }) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('leadProfile');

  const summary = useMemo(() => {
    let totalTarget = 0;
    let totalCoverage = 0;
    let totalGap = 0;

    goalsByNeed['SAVINGS'].forEach(goalType => {
      const goal = data[goalType];
      if (goalType) {
        totalTarget += goal?.targetAmount || 0;
        totalCoverage += goal?.coverageAmount || 0;
        totalGap += goal?.gapAmount || 0;
      }
    });

    return {
      totalTarget,
      totalCoverage,
      totalGap,
    };
  }, [data]);

  const savingNeedsData = [
    {
      priority: 5,
      label: 'leadProfile.fna.childEducation',
      icon: PictogramIcon.PeopleKnowledge,
      targetAmount: data?.educationGoal?.targetAmount ?? 0,
      coverageAmount: data?.educationGoal?.coverageAmount ?? 0,
      years: data?.educationGoal?.yearsToAchieve ?? '--',
    },
    {
      priority: 2,
      label: 'leadProfile.fna.retirement',
      icon: FamilySVG,
      targetAmount: data?.retirementGoal?.targetAmount ?? 0,
      coverageAmount: data?.retirementGoal?.coverageAmount ?? 0,
      years: data?.retirementGoal?.yearsToAchieve ?? '--',
    },
    {
      priority: 4,
      label: 'leadProfile.fna.lifeAchievement',
      icon: PictogramIcon.MoneyInvestment,
      targetAmount: data?.investmentGoal?.targetAmount ?? 0,
      coverageAmount: data?.investmentGoal?.coverageAmount ?? 0,
      years: data?.investmentGoal?.yearsToAchieve ?? '--',
    },
  ];

  return (
    <Container>
      <Row alignItems="center">
        <Row gap={space[2]} flex={1} alignItems="center">
          <PictogramIcon.Cash width={sizes[11]} height={sizes[11]} />
          <Typography.H6 color={colors.secondary} fontWeight="bold">
            {t('leadProfile.fna.savingNeeds')}
          </Typography.H6>
        </Row>

        <FnaLabel
          totalNeeds={summary?.totalTarget}
          totalCurrentCoverage={summary?.totalCoverage}
          totalGapToTargetAmount={summary?.totalGap}
          currency={t('leadProfile.fna.currency')} //TODO:
        />
      </Row>

      <Row marginTop={space[4]} marginBottom={space[4]} gap={space[10]}>
        {savingNeedsData.map((item, i) => (
          <SavingNeedsCard
            Icon={item?.icon}
            labelText={t(item?.label as any)}
            currency={t('leadProfile.fna.currency')}
            target={item?.targetAmount}
            progress={item?.coverageAmount}
            chartConfig={{ isShowLabel: true, isShowArrow: true }}
            tagConfig={{
              isShowTag: false,
              // isShowTag: item?.priority <= 3,
              tagColor: getTagColor(item?.priority, colors),
            }}
            tagText={t('leadProfile.fna.priority', {
              number: addOrdinalSuffixOf(item?.priority),
            })}
          />
        ))}
      </Row>
      <Timeline
        labels={savingNeedsData.map(({ years }) =>
          t('leadProfile.fna.inYears', { years }),
        )}
        color={colors.palette.fwdGreyDark}
        spaceBetween={space[10]}
      />
    </Container>
  );
}

const Container = styled(Card)(({ theme: { borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  paddingHorizontal: space[5],
  paddingVertical: space[5],
}));
