import React, { useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import { Fna } from 'types/case';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { formatCurrency } from 'utils';
import { Card } from '../../../../components/LeadProfile/Card';
import { Gender as GenderType } from 'types/person';
import CoupleOld from 'features/fna/components/illustrations/CoupleOld';
import CoupleRetired from 'features/fna/components/illustrations/CoupleRetired';
import CoupleWithKids from 'features/fna/components/illustrations/CoupleWithKids';
import CoupleWithoutKids from 'features/fna/components/illustrations/CoupleWithoutKids';
import FemaleOldSingle from 'features/fna/components/illustrations/FemaleOldSingle';
import FemaleRetiredSingle from 'features/fna/components/illustrations/FemaleRetiredSingle';
import FemaleWithDependents from 'features/fna/components/illustrations/FemaleWithDependents';
import FemaleYoungSingle from 'features/fna/components/illustrations/FemaleYoungSingle';
import MaleOldSingle from 'features/fna/components/illustrations/MaleOldSingle';
import MaleRetiredSingle from 'features/fna/components/illustrations/MaleRetiredSingle';
import MaleWithDependents from 'features/fna/components/illustrations/MaleWithDependents';
import MaleYoungSingle from 'features/fna/components/illustrations/MaleYoungSingle';
import { Trans, useTranslation } from 'react-i18next';

interface InfoFieldProps {
  label: string;
  content: string | number;
  columnNum: number;
  dataLength: number;
  index: number;
}

export const lifeStageLabelMapping = {
  SINGLE: 'fna:single',
  SINGLE_WITH_DEPENDENT: 'fna:singleWithDepend',
  COUPLE: 'fna:couple',
  COUPLE_WITH_KIDS: 'fna:coupleWithKids',
  EMPTY_NESTER: 'fna:emptyNester',
  RETIRED: 'fna:retired',
} as const;

export default function FnaInfo({ data }: { data?: Fna }) {
  const { space } = useTheme();
  const { t } = useTranslation(['fna']);

  let incomeData = '--';
  if (!data?.monthlyIncome?.from && data?.monthlyIncome?.to) {
    incomeData = `${t('fna:income.lessThan', {to: formatCurrency(data.monthlyIncome.to)})}`;
  }
  if (data?.monthlyIncome?.from && !data?.monthlyIncome?.to) {
    incomeData = t('fna:income.moreThan', {from: formatCurrency(data.monthlyIncome.from)});
  }
  if (data?.monthlyIncome?.from && data?.monthlyIncome?.to) {
    incomeData = t('fna:income.between', {from: formatCurrency(data.monthlyIncome.from), to: formatCurrency(data.monthlyIncome.to)});
  }
  const displayData = [
    {
      type: 'lifeStage',
      label: 'lifeStage',
      content: data?.lifeStage
        ? t(lifeStageLabelMapping[data?.lifeStage])
        : '--',
    },
    {
      type: 'birthday',
      label: 'lifeStage.detail.question.dob',
      content: data?.dateOfBirth?.date
        ? dateFormatUtil(data?.dateOfBirth?.date)
        : '--',
    },
    {
      type: 'retirementAge',
      label: 'lifeStage.detail.question.ageToRetire',
      content: data?.ageToRetire ?? '--',
    },
    {
      type: 'monthlyIncome',
      label: 'lifeStage.detail.question.income',
      content: incomeData,
    },
    {
      type: 'monthlyExpense',
      label: 'lifeStage.detail.question.financialBudget',
      content: data?.goalPercentage?.to ? `${data?.goalPercentage?.to}%` : '--',
    },
  ];

  const lifeStageIllustrations = useMemo(() => {
    switch (data?.lifeStage) {
      case 'SINGLE':
        return data?.gender === GenderType.MALE
          ? [MaleYoungSingle, MaleOldSingle]
          : [FemaleYoungSingle, FemaleOldSingle];
      case 'SINGLE_WITH_DEPENDENT':
        return data?.gender === GenderType.MALE
          ? [MaleWithDependents, MaleOldSingle]
          : [FemaleWithDependents, FemaleOldSingle];
      case 'COUPLE':
        return [CoupleWithoutKids, CoupleWithKids, CoupleOld];
      case 'COUPLE_WITH_KIDS':
        return [CoupleWithKids, CoupleOld];
      case 'EMPTY_NESTER':
      case 'RETIRED':
        return data?.havePartner
          ? [CoupleRetired]
          : data?.gender === GenderType.MALE
          ? [MaleRetiredSingle]
          : [FemaleRetiredSingle];
      default:
        return [];
    }
  }, [data?.gender, data?.havePartner, data?.lifeStage]);

  return (
    <FnaInfoContainer>
      <Row flexWrap="wrap" rowGap={space[6]}>
        {displayData.map(({ type, label, content }, index) => (
          <InfoField
            key={type}
            index={index}
            dataLength={displayData?.length}
            columnNum={3}
            label={t(label as never)}
            content={content}
          />
        ))}
      </Row>

      <PuzzleContainer>
        {lifeStageIllustrations.map((Stage, idx, arr) => (
          <Box key={idx} flexShrink={1} ml={idx === 0 ? 0 : 1}>
            <Stage mode={arr.length === 2 ? 'half' : 'one-third'} />
          </Box>
        ))}
      </PuzzleContainer>
    </FnaInfoContainer>
  );
}

function InfoField({
  index,
  label,
  content,
  columnNum,
  dataLength,
}: InfoFieldProps) {
  const { colors, sizes, space } = useTheme();
  const round = (number: number, step: number) => {
    return Math.ceil(number / step) * step;
  };

  return (
    <Column
      width={
        index == dataLength - 1 && dataLength % columnNum != 0
          ? `${
              (100 / columnNum) *
              (round(dataLength, columnNum) - (dataLength - 1))
            }%`
          : `${100 / columnNum}%`
      }
      paddingRight={space[2]}
      gap={sizes[1]}>
      <Typography.SmallLabel color={colors.palette.fwdGreyDarkest}>
        {label}
      </Typography.SmallLabel>

      <Typography.H7 color={colors.palette.fwdDarkGreen[100]}>
        {content ?? '--'}
      </Typography.H7>
    </Column>
  );
}

const FnaInfoContainer = styled(Card)(({ theme: { borderRadius, space } }) => ({
  width: '100%',
  padding: space[5],
  borderRadius: borderRadius.large,
  gap: space[5],
}));

const PuzzleContainer = styled(Row)(({ theme: { borderRadius } }) => ({
  alignSelf: 'center',
  justifyContent: 'center',
  borderRadius: borderRadius['x-small'],
  overflow: 'hidden',
}));
