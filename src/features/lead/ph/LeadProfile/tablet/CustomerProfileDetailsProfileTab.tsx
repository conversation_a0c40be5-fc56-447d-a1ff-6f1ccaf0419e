import { <PERSON><PERSON>, Column, H6, H7, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Modal, View, TouchableOpacity } from 'react-native';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useMemo, useState } from 'react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { RootStackParamList } from 'types';
import { Close } from 'cube-ui-components/dist/cjs/icons';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { differenceInYears } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { Card } from 'features/lead/components/LeadProfile/Card';
import { useGetCustomerProfileByCustomerId } from 'hooks/useGetCustomerProfile';

export default function CustomerProfileDetailsProfileTab() {
  const { t } = useTranslation(['lead', 'leadProfile']);
  const { colors, space } = useTheme();
  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();
  const customerId = route.params.customerId;

  const [viewDetailModal, setViewDetailModal] = useState(false);

  const { data: optionList } = useGetOptionList();
  const { isLoading, data: customerData } =
    useGetCustomerProfileByCustomerId(customerId);

  // const leadRecordFromAffiliate = lead?.leads.filter(
  //   leadRecord => leadRecord.sourceId === 'Affiliate',
  // );

  // const leadRecordFromAffiliateOrBLTS = lead?.leads.filter(
  //   leadRecord => leadRecord.sourceId === 'Affiliate' || 'BLTS',
  // );

  const occupation = customerData?.occupationCode ?? '--';

  const mappedOccupationFromGroup = useMemo(
    () =>
      optionList?.OCCUPATION_GROUP?.options.find(
        occuGrp => occuGrp.value === occupation,
      )?.label,
    [occupation],
  );
  const mappedOccupationFromSubGroup = useMemo(
    () =>
      optionList?.OCCUPATION_SUBGROUP?.options.find(
        occuSubGrp => occuSubGrp.value === occupation,
      )?.label,
    [occupation],
  );

  const profileDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: `+${customerData?.mobilePhoneCountryCode} ${customerData?.mobilePhoneNumber}`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: customerData?.email,
    },
    {
      label: t('leadProfile:leadProfile.leadInfoSection.occupation'),
      content:
        mappedOccupationFromGroup ?? mappedOccupationFromSubGroup ?? '--',
    },
  ];

  const personalInfoData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.salutation'),
      content:
        getLabelFromValue(
          optionList?.TITLE?.options,
          customerData?.title?.toUpperCase(),
        ) ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.firstName'),
      content: customerData?.firstName,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.middleName'),
      content: customerData?.middleName,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.lastName'),
      content: customerData?.lastName,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.extensionName'),
      content:
        getLabelFromValue(
          optionList?.EXTENSION?.options,
          customerData?.extensionName,
        ) ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.gender'),
      content:
        getLabelFromValue(optionList?.GENDER?.options, customerData?.gender) ??
        '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.dob'),
      content: customerData?.birthDate
        ? dateFormatUtil(customerData?.birthDate)
        : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.age'),
      content: customerData?.birthDate
        ? differenceInYears(new Date(), new Date(customerData?.birthDate)) +
          ' y.o.'
        : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),
      content:
        getLabelFromValue(
          optionList?.MARITAL_STATUS?.options,
          customerData?.maritalStatus,
        ) ?? '--',
    },
    {
      label: 'Customer source',
      content: '--',
      // getSourceLabels(customerData?.)
      //   .map(label => t(`lead:source.${label}`))
      //   .join(', ') ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.leadInfoSection.occupation'),
      content:
        mappedOccupationFromGroup ?? mappedOccupationFromSubGroup ?? '--',
    },
  ];

  const contactDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: `+${customerData?.mobilePhoneCountryCode} ${customerData?.mobilePhoneNumber}`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: customerData?.email,
    },
  ];

  const round = (number: number, step: number) => {
    return Math.ceil(number / step) * step;
  };

  const Header = () => {
    return (
      <Row py={space[5]} justifyContent="space-between" alignItems="center">
        <H6 fontWeight="bold" children={'Profile'} />
        <Button
          text={t('leadProfile:leadProfile.leadInfoSection.viewProfile')}
          variant="secondary"
          size="medium"
          textStyle={{ fontWeight: 'bold' }}
          onPress={() => setViewDetailModal(true)}
        />
      </Row>
    );
  };

  const ProfileDetailField = ({
    label,
    content,
    columnNum,
    dataLength,
    idx,
  }: {
    label: string;
    content: string;
    columnNum: number;
    dataLength: number;
    idx: number;
  }) => {
    return (
      <Column
        width={
          idx == dataLength - 1 && dataLength % columnNum != 0
            ? `${
                (100 / columnNum) *
                (round(dataLength, columnNum) - (dataLength - 1))
              }%`
            : `${100 / columnNum}%`
        }
        height={
          dataLength % columnNum != 0
            ? `${(100 / round(dataLength, columnNum)) * columnNum}%`
            : `${(100 / dataLength) * columnNum}%`
        }
        gap={sizes[1]}>
        <Typography.SmallLabel color={colors.placeholder}>
          {label}
        </Typography.SmallLabel>

        <Typography.H7 color={colors.palette.fwdDarkGreen[100]}>
          {!isLoading ? content : '--'}
        </Typography.H7>
      </Column>
    );
  };

  const DetailModal = () => {
    const CrossContainer = () => {
      return (
        <Row justifyContent="flex-end">
          <TouchableOpacity
            onPress={() => {
              setViewDetailModal(false);
            }}>
            <Close size={sizes[6]} fill={colors.palette.fwdDarkGreen[100]} />
          </TouchableOpacity>
        </Row>
      );
    };

    const ProfileDetailContainer = () => {
      return (
        <>
          <Column marginBottom={space[4]}>
            <H6 fontWeight="bold">
              {t('leadProfile:leadProfile.profileDetails.profileDetails')}
            </H6>
          </Column>

          <Column flex={3}>
            <Column paddingBottom={space[4]}>
              <H7 fontWeight="bold" color={colors.palette.fwdDarkGreen[50]}>
                {t('leadProfile:leadProfile.profileDetails.personalInfo')}
              </H7>
            </Column>
            <Row flex={1} flexWrap="wrap">
              {personalInfoData.map((item, index) => (
                <ProfileDetailField
                  dataLength={personalInfoData.length}
                  columnNum={4}
                  key={index}
                  label={item.label}
                  content={item.content ? item.content : '--'}
                  idx={index}
                />
              ))}
            </Row>
          </Column>
        </>
      );
    };

    const ContactDetailContainer = () => {
      return (
        <Column flex={2}>
          <Column paddingBottom={space[4]}>
            <H7 fontWeight="bold" color={colors.palette.fwdDarkGreen[50]}>
              {t('leadProfile:leadProfile.profileDetails.contactDetails')}
            </H7>
          </Column>

          <Row flexWrap="wrap">
            {contactDetailData.map((item, index) => (
              <ProfileDetailField
                dataLength={contactDetailData.length}
                columnNum={4}
                key={index}
                label={item.label}
                content={item.content ? item.content : '--'}
                idx={index}
              />
            ))}
          </Row>
        </Column>
      );
    };

    return (
      <Modal visible={viewDetailModal} transparent={true}>
        <ModalMainContainer>
          <ModalBody>
            <CrossContainer />
            <Column style={{ paddingHorizontal: space[6] }} flex={1}>
              <ProfileDetailContainer />
              <Divider />
              <Column flex={1}>
                <ContactDetailContainer />
              </Column>
            </Column>
          </ModalBody>
        </ModalMainContainer>
      </Modal>
    );
  };

  return (
    <>
      <DetailModal />
      <MainContainer>
        <Header />
        <ProfileDetailContainer>
          <Row flexWrap="wrap" flex={1}>
            {profileDetailData.map((item, index) => (
              <ProfileDetailField
                dataLength={profileDetailData.length}
                columnNum={3}
                key={index}
                label={item.label}
                content={item.content ? item.content : '--'}
                idx={index}
              />
            ))}
          </Row>
        </ProfileDetailContainer>
      </MainContainer>
    </>
  );
}

export const MainContainer = styled(View)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.white,
  flex: 1,
  marginBottom: space[5],
}));

export const ProfileDetailContainer = styled(Card)(
  ({ theme: { borderRadius, space } }) => ({
    width: '100%',
    padding: space[5],
    lineHeight: 1,
    borderRadius: borderRadius.large,
  }),
);

export const ModalMainContainer = styled(View)(({ theme: { space } }) => ({
  flex: 1,
  backgroundColor: '#000000aa',
  paddingHorizontal: space[20],
  paddingVertical: space[48],
  justifyContent: 'center',
}));

export const ModalBody = styled(View)(
  ({ theme: { borderRadius, space, colors } }) => ({
    flex: 1,
    backgroundColor: colors.palette.white,
    borderRadius: borderRadius.large,
    paddingHorizontal: space[6],
    paddingTop: space[6],
    paddingBottom: space[12],
    minHeight: 444,
  }),
);

export const Divider = styled(View)(({ theme: { colors, space } }) => ({
  marginBottom: space[4],
  height: 1,
  backgroundColor: colors.palette.fwdGrey[100],
}));
