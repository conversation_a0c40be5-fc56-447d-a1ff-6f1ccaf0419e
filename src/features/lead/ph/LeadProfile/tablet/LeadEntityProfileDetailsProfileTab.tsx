import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { Card } from 'features/lead/components/LeadProfile/Card';
import { getSourceLabels } from 'features/lead/utils';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import { RootStackParamList } from 'types';
import { CHANNELS } from 'types/channel';
import getLabelFromValue from 'utils/helper/getLabelFromValue';

export default function LeadEntityProfileDetailsProfileTab() {
  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();
  const leadId = route?.params?.id;

  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile']);

  const [isDetailsModalVisible, setIsProfileDetailsModalVisible] =
    useState(false);

  const channel = useGetCubeChannel();
  const showReferralDetail =
    channel === CHANNELS.BANCA || channel === CHANNELS.AFFINITY;

  const { data: optionList } = useGetOptionList();
  const { isLoading, data: lead } = useGetLeadByLeadId(leadId);

  const leadRecordFromAffiliateOrBLTS = lead?.leads.filter(
    leadRecord => leadRecord.sourceId === 'Affiliate' || 'BLTS',
  );

  /**
   * Profile data (Profile card)
   */
  const PROFILE_DETAILS_DATA = [
    {
      type: 'primaryMobileNumber',
      label: t('leadProfile:leadProfile.profileDetails.primaryMobileNumber'),
      value: `+${lead?.mobilePhoneCountryCode} ${lead?.mobilePhoneNumber}`,
    },
    {
      type: 'email',
      label: t('leadProfile:leadProfile.profileDetails.email'),
      value: lead?.email,
    },
    {
      type: 'natureOfBusiness',
      label: t('leadProfile:leadProfile.profileDetails.natureOfBusiness'),
      value: lead?.occupationIndustryCode ?? '--',
    },
  ];

  /**
   * Personal info data (Modal)
   */
  const PERSONAL_INFO_DATA = [
    {
      type: 'entityName',
      label: t('leadProfile:leadProfile.profileDetails.entityName'),
      value: lead?.companyName,
    },
    {
      type: 'natureOfBusiness',
      label: t('leadProfile:leadProfile.profileDetails.natureOfBusiness'),
      value: lead?.occupationIndustryCode ?? '--',
    },
    {
      type: 'leadOrigin',
      label: t('leadProfile:leadProfile.profileDetails.leadOrigin'),
      value:
        getSourceLabels(lead?.sourceIds)
          .map(label => t(`lead:source.${label}`))
          .join(', ') ?? '--',
    },
  ];

  /**
   * Primary contact details data (Modal)
   */
  const PRIMARY_CONTACT_DETAILS_DATA = [
    {
      type: 'primaryMobileNumber',
      label: t('leadProfile:leadProfile.profileDetails.primaryMobileNumber'),
      value: `+${lead?.mobilePhoneCountryCode} ${lead?.mobilePhoneNumber}`,
    },
    {
      type: 'email',
      label: t('leadProfile:leadProfile.profileDetails.email'),
      value: lead?.email,
    },
  ];

  /**
   * Secondary contact details data (Modal)
   */
  const SECONDARY_CONTACT_DATA = [
    {
      type: 'secondaryMobileNumber',
      label: t('leadProfile:leadProfile.profileDetails.secondaryMobileNumber'),
      value:
        (lead?.workPhoneCountryCode || lead?.homePhoneCountryCode) &&
        (lead?.workPhoneNumber || lead?.homePhoneNumber)
          ? `+${
              lead?.workPhoneCountryCode
                ? lead?.workPhoneCountryCode
                : lead?.homePhoneCountryCode
                ? lead?.homePhoneCountryCode
                : ''
            } ${
              lead?.workPhoneNumber
                ? lead.workPhoneNumber
                : lead?.homePhoneNumber ?? ''
            }`
          : '--',
    },
  ];

  /**
   * Authorized details data (Modal)
   */
  const AUTHORIZED_DETAILS_DATA = [
    {
      type: 'salutation',
      label: t('leadProfile:leadProfile.profileDetails.salutation'),
      value:
        getLabelFromValue(
          optionList?.TITLE?.options,
          lead?.salutation?.toUpperCase(),
        ) ?? '--',
    },
    {
      type: 'firstName',
      label: t('leadProfile:leadProfile.profileDetails.firstName'),
      value: lead?.firstName ?? '--',
    },
    {
      type: 'middleName',
      label: t('leadProfile:leadProfile.profileDetails.middleName'),
      value: lead?.middleName ?? '--',
    },
    {
      type: 'lastName',
      label: t('leadProfile:leadProfile.profileDetails.lastName'),
      value: lead?.lastName ?? '--',
    },
    {
      type: 'extensionName',
      label: t('leadProfile:leadProfile.profileDetails.extensionName'),
      value:
        getLabelFromValue(
          optionList?.EXTENSION?.options,
          lead?.nameExtension,
        ) ?? '--',
    },
    {
      type: 'position',
      label: t('leadProfile:leadProfile.profileDetails.position'),
      value: lead?.jobTitle ?? '--',
    },
  ];

  /**
   * Referral details data (Modal)
   */
  const REFERRAL_DETAILS_DATA = [
    {
      type: 'bltsRefNumber',
      label: t('leadProfile:leadProfile.profileDetails.bltsRefNumber'),
      value: lead?.extra?.alts_blts_ref_num ?? '--',
    },
    {
      type: 'servicingBranch',
      label: t('leadProfile:leadProfile.profileDetails.servicingBranch'),
      value: lead?.extra?.service_branch ?? '--',
    },
    {
      type: 'bankCustomerId',
      label: t('leadProfile:leadProfile.profileDetails.bankCustomerId'),
      value: lead?.extra?.bank_customer_id ?? '--',
    },
    {
      type: 'referrerCode',
      label: t('leadProfile:leadProfile.profileDetails.referrerCode'),
      value: lead?.extra?.referrer_code ?? '--',
    },
  ];

  const DetailModal = () => {
    const { height: screenHeight } = useWindowDimensions();

    const ProfileDetailContainer = () => {
      return (
        <Column
          flex={showReferralDetail ? undefined : 3}
          height={showReferralDetail ? 140 : 'auto'}>
          <Column flex={1}>
            <Typography.H7
              fontWeight="bold"
              children={t(
                'leadProfile:leadProfile.profileDetails.personalInfo',
              )}
              color={colors.palette.fwdDarkGreen[50]}
              style={{ paddingBottom: space[4] }}
            />

            <Row flex={1} flexWrap="wrap">
              {PERSONAL_INFO_DATA?.map((item, index) => (
                <ProfileDetailField
                  key={'personalInfo_' + item?.type}
                  index={index}
                  columnNum={2}
                  dataLength={PERSONAL_INFO_DATA?.length}
                  label={item?.label}
                  content={item?.value ?? '--'}
                />
              ))}
            </Row>
          </Column>
        </Column>
      );
    };

    const ContactDetailContainer = () => {
      return (
        <Column
          flex={showReferralDetail ? undefined : 2}
          height={showReferralDetail ? 120 : 'auto'}>
          <Typography.H7
            fontWeight="bold"
            children={t(
              'leadProfile:leadProfile.profileDetails.contactDetails',
            )}
            color={colors.palette.fwdDarkGreen[50]}
            style={{ paddingBottom: space[4] }}
          />

          <Row flex={1}>
            <Column flex={3} gap={space[2]}>
              <Typography.H8
                fontWeight="bold"
                children={t(
                  'leadProfile:leadProfile.profileDetails.primaryContactDetails',
                )}
                color={colors.palette.fwdOrange[100]}
              />

              <Row flexWrap="wrap" flex={1}>
                {PRIMARY_CONTACT_DETAILS_DATA?.map((item, index) => (
                  <ProfileDetailField
                    key={'primaryContact_' + item?.type}
                    index={index}
                    columnNum={3}
                    dataLength={PRIMARY_CONTACT_DETAILS_DATA?.length}
                    label={item?.label}
                    content={item?.value ?? '--'}
                  />
                ))}
              </Row>
            </Column>

            <Column flex={1} gap={space[2]}>
              <Typography.H8
                fontWeight="bold"
                children={t(
                  'leadProfile:leadProfile.profileDetails.secondaryContactDetails',
                )}
                color={colors.palette.fwdOrange[100]}
              />

              <Row flexWrap="wrap" flex={1}>
                {SECONDARY_CONTACT_DATA?.map((item, index) => (
                  <ProfileDetailField
                    key={'secondaryContact_' + item?.type}
                    index={index}
                    columnNum={1}
                    dataLength={SECONDARY_CONTACT_DATA?.length}
                    label={item?.label}
                    content={item?.value ?? '--'}
                  />
                ))}
              </Row>
            </Column>
          </Row>
        </Column>
      );
    };

    const AuthorizedDetailContainer = () => {
      return (
        <Column
          flex={showReferralDetail ? undefined : 3}
          height={showReferralDetail ? 150 : 'auto'}>
          <Typography.H7
            fontWeight="bold"
            children={t(
              'leadProfile:leadProfile.profileDetails.authorizedDetails',
            )}
            color={colors.palette.fwdDarkGreen[50]}
            style={{ paddingBottom: space[4] }}
          />

          <Row flexWrap="wrap" flex={1}>
            {AUTHORIZED_DETAILS_DATA?.map((item, index) => (
              <ProfileDetailField
                key={'authorizedDetails_' + item?.type}
                index={index}
                columnNum={4}
                dataLength={AUTHORIZED_DETAILS_DATA?.length}
                label={item?.label}
                content={item?.value ?? '--'}
              />
            ))}
          </Row>
        </Column>
      );
    };

    const ReferralDetailContainer = () => {
      return (
        <Column
          flex={showReferralDetail ? undefined : 2}
          height={showReferralDetail ? 100 : 'auto'}>
          <Typography.H7
            fontWeight="bold"
            children={t(
              'leadProfile:leadProfile.profileDetails.referralDetails',
            )}
            color={colors.palette.fwdDarkGreen[50]}
            style={{ paddingBottom: space[4] }}
          />

          <Row flexWrap="wrap" flex={1}>
            {REFERRAL_DETAILS_DATA?.map((item, index) => (
              <ProfileDetailField
                key={'referralDetails_' + item?.type}
                index={index}
                columnNum={4}
                dataLength={REFERRAL_DETAILS_DATA?.length}
                label={item?.label}
                content={item?.value ?? '--'}
              />
            ))}
          </Row>
        </Column>
      );
    };

    return (
      <Modal
        isVisible={isDetailsModalVisible}
        animationIn={'fadeIn'}
        animationOut={'fadeOut'}
        backdropOpacity={0.5}>
        <View
          style={{
            width: 'auto',
            maxHeight: screenHeight * 0.75,
            borderRadius: borderRadius.large,
            backgroundColor: colors.background,
            //
            paddingTop: space[6],
            paddingBottom: space[12] - space[4], // minus LeadProfileDetailsProfileTab paddingBottom:space[4]
            paddingLeft: space[12],
            paddingRight: space[6],
          }}>
          <TouchableOpacity
            onPress={() => setIsProfileDetailsModalVisible(false)}
            style={{ alignSelf: 'flex-end' }}>
            <Icon.Close size={sizes[6]} fill={colors.secondary} />
          </TouchableOpacity>

          <Typography.H6
            fontWeight="bold"
            children={t(
              'leadProfile:leadProfile.profileDetails.profileDetails',
            )}
            style={{ paddingBottom: space[4] }}
          />

          <ScrollView>
            <ProfileDetailContainer />
            <Divider />
            <ContactDetailContainer />
            <Divider />
            <AuthorizedDetailContainer />
            {showReferralDetail && (
              <>
                <Divider />
                <ReferralDetailContainer />
              </>
            )}
          </ScrollView>
        </View>
      </Modal>
    );
  };

  return (
    <>
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        <Row
          justifyContent="space-between"
          alignItems="center"
          paddingTop={space[8]}
          paddingBottom={space[5]}>
          <Typography.H6 fontWeight="bold" children={'Profile'} />

          <TouchableOpacity
            disabled={isLoading}
            onPress={() => setIsProfileDetailsModalVisible(true)}
            style={{
              flexDirection: 'row',
              alignSelf: 'flex-start',
              alignItems: 'center',
              gap: space[1],
            }}>
            <Icon.ArrowRight
              size={sizes[5]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
            <Typography.H7
              fontWeight="bold"
              children={t('leadProfile:leadProfile.leadInfoSection.viewMore')}
              color={colors.palette.fwdAlternativeOrange[100]}
            />
          </TouchableOpacity>
        </Row>

        <Container>
          <Row flexWrap="wrap" flex={1}>
            {PROFILE_DETAILS_DATA?.map((item, index) => (
              <ProfileDetailField
                key={'display_' + item?.type}
                dataLength={PROFILE_DETAILS_DATA?.length}
                index={index}
                columnNum={3}
                label={item?.label}
                content={item?.value ?? '--'}
              />
            ))}
          </Row>
        </Container>
      </View>

      <DetailModal />
    </>
  );
}

function ProfileDetailField({
  index,
  columnNum,
  dataLength,
  label,
  content,
  //
  showInfoButton,
  infoButtonOnPress,
}: {
  index: number;
  columnNum: number;
  dataLength: number;
  label: string;
  content: React.ReactNode;
  //
  showInfoButton?: boolean;
  infoButtonOnPress?: () => void;
}) {
  const { colors, space } = useTheme();
  return (
    <Column
      width={
        index == dataLength - 1 && dataLength % columnNum != 0
          ? `${
              (100 / columnNum) *
              (round(dataLength, columnNum) - (dataLength - 1))
            }%`
          : `${100 / columnNum}%`
      }
      gap={space[1]}
      paddingBottom={space[4]}>
      <Row gap={space[1]}>
        <Typography.SmallLabel children={label} color={colors.placeholder} />

        {showInfoButton && (
          <Pressable onPress={() => infoButtonOnPress?.()}>
            <Icon.InfoCircle
              size={sizes[4]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
          </Pressable>
        )}
      </Row>

      {typeof content === 'string' ? (
        <Typography.LargeLabel
          children={content ?? '--'}
          color={colors.palette.fwdDarkGreen[100]}
        />
      ) : (
        <View>{content}</View>
      )}
    </Column>
  );
}

const round = (number: number, step: number) => {
  return Math.ceil(number / step) * step;
};

const Container = styled(Card)(({ theme: { borderRadius, space } }) => ({
  width: '100%',
  paddingTop: space[5],
  paddingHorizontal: space[5],
  paddingBottom: space[1], // ProfileDetailField has own paddingBottom (space[4])
  borderRadius: borderRadius.large,
}));

const Divider = styled(View)(({ theme: { colors, space } }) => ({
  marginVertical: space[4],
  height: 1,
  backgroundColor: colors.palette.fwdGrey[100],
}));
