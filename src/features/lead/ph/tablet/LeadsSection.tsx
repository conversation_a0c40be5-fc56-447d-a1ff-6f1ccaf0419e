import { useTheme } from '@emotion/react';
import { Row, Typography } from 'cube-ui-components';
import ConversionReport from 'features/lead/ph/tablet/ConversionReport';
import LeadManagementSection from 'features/lead/ph/tablet/LeadManagementSection';
import TabButton from 'features/lead/tablet/components/TabButton';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';

export default function LeadsSection() {
  const { colors } = useTheme();
  return (
    <View style={{ paddingTop: 0, backgroundColor: colors.background }}>
      <Row>
        <LeadManagementSection />
        <ConversionReportSection />
      </Row>
    </View>
  );
}

function ConversionReportSection() {
  const { colors, space } = useTheme();
  const [currentTab, setCurrentTab] = useState('thisWeek');
  const scrollRef = useRef<ScrollView>(null);
  const { t } = useTranslation('lead');
  const scrollToEnd = () => scrollRef?.current?.scrollToEnd({ animated: true });

  return (
    <ScrollView
      ref={scrollRef}
      style={{
        flex: 1,
        backgroundColor: colors.background,
        paddingHorizontal: space[6],
      }}
      contentContainerStyle={{
        gap: space[5],
        paddingVertical: space[6],
      }}>
      <Typography.H6 fontWeight="bold">{t('lead.salesActivity')}</Typography.H6>
      <Row style={{ gap: space[2] }}>
        <TabButton
          isActive={currentTab === 'thisWeek'}
          label={t('weekly')}
          onPress={() => setCurrentTab('thisWeek')}
        />
        <TabButton
          isActive={currentTab === 'thisMonth'}
          label={t('monthly')}
          onPress={() => setCurrentTab('thisMonth')}
        />
      </Row>
      <ConversionReport tab={currentTab} scrollToEnd={scrollToEnd} />
    </ScrollView>
  );
}
