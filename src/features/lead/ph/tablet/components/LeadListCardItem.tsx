import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import StatusTag from 'components/StatusTag';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import {
  add,
  differenceInHours,
  differenceInMinutes,
  isPast,
  parseISO,
} from 'date-fns';
import LeadScore from 'features/lead/components/LeadProfile/LeadScore';
import LeadStatusTag from 'features/lead/components/LeadStatusTag';
import { checkIsMarketingLead, getSourceLabels } from 'features/lead/utils';
import { useRootStackNavigation } from 'hooks/useRootStack';
import _ from 'lodash';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import Animated, { FadeInUp, LinearTransition } from 'react-native-reanimated';
import { Lead, LeadSource } from 'types/lead';

export default function LeadListCardItem(
  props: Partial<Lead> & { isToday?: boolean; isLoading?: boolean },
) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile']);
  const { navigate } = useRootStackNavigation();

  const isEntity = !props.isIndividual;
  const isMarketingLead = checkIsMarketingLead(
    props?.sourceIds as LeadSource[],
  );

  const leadName = isEntity
    ? props?.companyName ?? '--'
    : `${props?.firstName} ${props?.lastName}`;

  const sourcesLabel = getSourceLabels(props.sourceIds)
    .map(label => t(`lead:source.${label}`))
    .join(', ');

  const campaignName = props?.campaignName;

  const interestProduct = !_.isEmpty(props?.interestedProducts)
    ? props?.interestedProducts?.join(', ')
    : !_.isEmpty(props?.interestedCategories)
    ? props?.interestedCategories?.join(', ')
    : undefined;

  /**
   * Time calculations
   */
  const opportunityTime =
    props.updatedAt ?? props.opportunityUpdatedAt ?? props.createdAt;
  const expirationTime = add(parseISO(opportunityTime ?? ''), { hours: 24 });
  const timeInMinutesLeftToExpire = differenceInMinutes(
    expirationTime,
    new Date(),
  );
  const timeInHoursLeftToExpire = differenceInHours(expirationTime, new Date());
  const isMinutes = timeInHoursLeftToExpire < 1;
  const isToday = props.isToday ?? false;
  const isNew =
    isToday &&
    !isPast(add(parseISO(props.createdAt ?? ''), { hours: 24 })) &&
    props.status === 'created';

  return (
    <TouchableOpacity
      onPress={() => navigate('LeadProfile', { id: props?.id ?? '' })}>
      <LeadListCardContainer
        layout={LinearTransition.duration(200)}
        entering={FadeInUp}>
        <Column flex={1} justifyContent="space-between" gap={space[1]}>
          <Column gap={space[1]}>
            <Row alignItems="center">
              <Box mr={space[1]}>
                {isEntity ? (
                  <Icon.Office size={space[4]} />
                ) : (
                  <Icon.Account size={space[4]} />
                )}
              </Box>
              <Typography.LargeLabel
                fontWeight="bold"
                children={leadName}
                color={colors.primary}
              />
            </Row>

            {isMarketingLead && props?.extra?.engagement_score && (
              <Row alignItems="center">
                <Typography.Label
                  children={t('lead:leadScore') + ': '}
                  color={colors.secondary}
                />
                <LeadScore leadScore={props?.extra?.engagement_score} />
              </Row>
            )}

            <Typography.Label
              children={`${t('lead:leadOrigin') + ':'} ${sourcesLabel}`}
              color={colors.palette.fwdDarkGreen[100]}
            />

            {isMarketingLead && campaignName && (
              <Typography.Label
                numberOfLines={1}
                ellipsizeMode="tail"
                children={`${t('lead:campaign') + ':'} ${campaignName}`}
                color={colors.palette.fwdDarkGreen[100]}
                style={{ width: '75%' }}
              />
            )}

            {interestProduct && (
              <Typography.Label
                numberOfLines={1}
                ellipsizeMode="tail"
                children={t('lead:interestedIn') + ' ' + interestProduct}
                color={colors.palette.fwdDarkGreen[50]}
                style={{ width: '75%' }}
              />
            )}
          </Column>

          <Row justifyContent="space-between">
            <LeftTagContainer>
              <Row gap={space[1]}>
                {isNew && props?.status === 'created' && (
                  <StatusTag
                    text={'New'}
                    backgroundColor={colors.primaryVariant2}
                    textColor={colors.primary}
                  />
                )}
                {props?.status && <LeadStatusTag.Ph status={props.status} />}
              </Row>
            </LeftTagContainer>

            <RightTagContainer>
              {isToday && (
                <TimeCountDownTag
                  isAlert={timeInHoursLeftToExpire < 1}
                  labelType={isMinutes ? 'minutes' : 'hours'}
                  diff={
                    isMinutes
                      ? timeInMinutesLeftToExpire
                      : timeInHoursLeftToExpire
                  }
                />
              )}
            </RightTagContainer>
          </Row>
        </Column>

        <Column justifyContent="center">
          <Icon.ChevronRight
            fill={colors.palette.fwdDarkGreen[50]}
            size={sizes[5]}
          />
        </Column>
      </LeadListCardContainer>
    </TouchableOpacity>
  );
}

function TimeCountDownTag({
  isAlert = false,
  diff,
  labelType,
}: {
  isAlert?: boolean;
  diff: number;
  labelType: 'minutes' | 'hours';
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const colorStyle = {
    default: {
      textColor: colors.palette.fwdBlue[100],
      backgroundColor: colors.palette.alertGreenLight,
    },
    alert: {
      textColor: colors.palette.alertRed,
      backgroundColor: colors.palette.alertRedLight,
    },
  } as const;

  const colorMode: keyof typeof colorStyle = isAlert ? 'alert' : 'default';
  return (
    <Row
      pl={space[1]}
      pr={space[2]}
      py={space[1] / 2}
      gap={space[1]}
      alignItems={'center'}
      backgroundColor={colorStyle[colorMode].backgroundColor}
      borderRadius={borderRadius.large}>
      <Icon.OperatingHours
        size={sizes[4]}
        fill={colorStyle[colorMode].textColor}
      />
      <Typography.SmallLabel
        color={colorStyle[colorMode].textColor}
        children={(diff ?? '') + ' ' + (labelType ?? '') + ' left'}
        // children={dateTimeElapsed.elapsedTime + ' ' + dateTimeElapsed.unit + ' ago'}
      />
    </Row>
  );
}

const LeadListCardContainer = styled(Animated.View)(({ theme }) => ({
  flexDirection: 'row',
  minHeight: theme.sizes[28],
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  paddingVertical: theme.space[3],
  paddingLeft: theme.space[4],
  paddingRight: theme.space[2],
  gap: theme.space[2],
}));

const LeftTagContainer = styled(Row)(({ theme }) => ({
  gap: theme.space[1],
}));

const RightTagContainer = styled(Column)(({ theme }) => ({
  display: 'flex',
  alignSelf: 'center',
  gap: theme.space[1],
}));
