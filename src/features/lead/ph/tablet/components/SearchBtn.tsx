import React from 'react';
import { useTheme } from '@emotion/react';
import { TouchableOpacity } from 'react-native';
import { Box, Icon, Row, Typography } from 'cube-ui-components';

export function SearchBtn({
  label,
  onPress,
}: {
  label: string;
  onPress: () => void;
}) {
  const { space, colors, borderRadius } = useTheme();

  return (
    <Row
      alignItems={'center'}
      minHeight={space[11]}
      justifyContent={'space-between'}>
      <Typography.H6 fontWeight="bold">{label}</Typography.H6>

      <TouchableOpacity onPress={onPress}>
        <Box
          h={space[11]}
          w={space[11]}
          border={1}
          borderColor={colors.primary}
          borderRadius={borderRadius.full}
          alignItems="center"
          justifyContent="center"
          backgroundColor={colors.palette.white}>
          <Icon.Search />
        </Box>
      </TouchableOpacity>
    </Row>
  );
}
