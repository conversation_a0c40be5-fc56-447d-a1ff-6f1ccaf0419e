import { ScrollView, Dimensions, FlatList } from 'react-native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Row, Typography, Column, Icon, Chip } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import styled from '@emotion/native';

import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import EmptyCaseFunnelSVGMedium from 'features/lead/assets/EmptyCaseFunnelSVGMedium';
import {
  LeadSource,
  LeadStatus,
  LeadsFilters,
  GetLeadFiltersResponse,
  GetLeadsResponse,
  SELF_GEN_LEAD_SOURCES,
  MARKETING_LEAD_SOURCES,
  NOT_CONTACTED_LEAD_STATUS,
} from 'types';
import CustomSearchingBar from 'components/CustomSearchingBar';
import { useSearchLead } from 'hooks/useGetLeads';
import { isWithinInterval, sub } from 'date-fns';
import LeadListCardItem from './components/LeadListCardItem';
import LeadFilterModalButton from './LeadFilterModalButton';
import AddNewLeadButton from 'features/lead/tablet/AddNewLeadButton';
import { checkHasFilter } from 'features/lead/tablet/helpers';
import { clearFilters } from '../../utils';

export default function LeadSearchSection({
  setIsSearching,
  isSearching,
}: {
  setIsSearching: (value: boolean) => void;
  isSearching: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const startOffsetY = useRef(0);
  const { bottom } = useSafeAreaInsets();
  const { t } = useTranslation('lead');

  const [leadData, setLeadData] = useState<GetLeadsResponse['data']>([]);
  const [filters, setFilters] = useState<LeadsFilters>({
    type: {},
    source: {},
    status: {},
  });
  const [curTabFilters, setCurTabFilters] = useState<GetLeadFiltersResponse>();
  const [listHeight, setListHeight] = useState(0);
  const [searchKeyWords, setSearchKeyWords] = useState<string>('');

  const {
    data: leadResult,
    isFetching: isFetchingLead,
    remove: removeLeadQuery,
  } = useSearchLead({
    queryText: searchKeyWords,
    enabled: Boolean(searchKeyWords),
  });

  useEffect(() => {
    setLeadData(leadResult?.data || []);
    setFilters(clearFilters(filters));
    const totalCount = leadResult?.data?.length;
    if (totalCount) {
      const { sourceStats, statusStats } = leadResult;
      const individualCount = leadResult.data.filter(
        item => !!item.isIndividual,
      ).length;
      const entityCount = totalCount - individualCount;
      setCurTabFilters({
        typeStats: [
          {
            count: individualCount,
            type: 'individual',
          },
          {
            count: entityCount,
            type: 'entity',
          },
        ],
        sourceStats,
        statusStats,
      });
    }
  }, [leadResult]);

  useEffect(() => {
    if (filters) {
      let isTypeFilter = 0;
      if (filters?.type?.individual && !filters?.type?.entity) {
        isTypeFilter = 1;
      } else if (filters?.type?.entity && !filters?.type?.individual) {
        isTypeFilter = 2;
      }

      const typeFilterData = leadResult?.data?.filter(item => {
        if (isTypeFilter > 0) {
          return item.isIndividual == (isTypeFilter === 2 ? false : true);
        } else {
          return true;
        }
      });

      let sourceKeys: string[] = Object.keys(
        filters.source as Record<LeadSource, boolean>,
      )?.filter(k => !!filters.source[k as LeadSource]);
      sourceKeys = sourceKeys.reduce((o, c) => {
        let k = [c];
        if (c === 'SELF') {
          k = SELF_GEN_LEAD_SOURCES;
        } else if (c === 'Marketing') {
          k = MARKETING_LEAD_SOURCES;
        }
        return [...o, ...k];
      }, [] as string[]);

      let sourceFilterData = typeFilterData;
      if (sourceKeys?.length) {
        sourceFilterData = typeFilterData?.filter(item => {
          return sourceKeys.some(s => item.sourceIds.includes(s as LeadSource));
        });
      }

      let statusKeys: string[] = Object.keys(
        filters.status as Record<LeadStatus, boolean>,
      )?.filter(k => !!filters.status[k as LeadStatus]);
      statusKeys = statusKeys.reduce((o, c) => {
        let k = [c];
        if (c === 'not_contacted') {
          k = NOT_CONTACTED_LEAD_STATUS;
        }
        return [...o, ...k];
      }, [] as string[]);

      let statusFilterData = sourceFilterData;
      if (statusKeys?.length) {
        statusFilterData = sourceFilterData?.filter(item => {
          return statusKeys.includes(item.status);
        });
      }

      setLeadData(statusFilterData || []);
    }
  }, [filters]);

  const isSomeFilterActive = useMemo(
    () => (filters ? checkHasFilter(filters) : false),
    [filters],
  );

  const onFiltered = (f: LeadsFilters) => {
    setFilters(f);
  };

  return (
    <AnimatedViewWrapper
      style={{
        flex: 1,
        paddingTop: space[5],
        paddingHorizontal: space[6],
        backgroundColor: colors.palette.fwdGrey[50],
      }}>
      <CustomSearchingBar
        onExitSearch={() => setIsSearching(false)}
        isFetching={isFetchingLead}
        setSearchKeyWords={setSearchKeyWords}
        placeholderText={t('lead.leads.search.placeholder')}
        BelowBarLabelComponent={
          <Typography.SmallLabel
            style={{
              marginTop: space[2],
              alignSelf: 'flex-start',
            }}
            color={colors.palette.fwdGreyDarker}>
            {t('search.leadsHint')}
          </Typography.SmallLabel>
        }
      />
      {searchKeyWords.trim().length > 0 && !isFetchingLead && (
        <>
          <Column height={space[8]} />
          <Column gap={space[4]}>
            <Row>
              <Typography.H7 fontWeight="bold" style={{ flex: 1 }}>
                Search result ({numberToThousandsFormat(leadData?.length || 0)})
              </Typography.H7>
            </Row>

            {!!leadResult?.data?.length && (
              <Row alignItems="center">
                <Row style={{ gap: space[2], alignItems: 'center', flex: 1 }}>
                  <Typography.Label color={colors.palette.fwdGreyDarkest}>
                    {t('filterBy')}
                  </Typography.Label>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{
                      gap: space[1],
                    }}>
                    <Chip
                      size="medium"
                      textPosition="right"
                      label={t('addLead.category.individual')}
                      focus={filters.type.individual ?? false}
                      icon={() => (
                        <Icon.Account
                          size={space[4]}
                          fill={
                            filters.type.individual
                              ? colors.primary
                              : colors.secondary
                          }
                        />
                      )}
                      onPress={() =>
                        setFilters({
                          ...filters,
                          type: {
                            ...filters.type,
                            individual: !filters.type.individual,
                          },
                        })
                      }
                    />
                    <Chip
                      size="medium"
                      textPosition="right"
                      label={t('addLead.category.entity')}
                      focus={filters.type.entity ?? false}
                      icon={() => (
                        <Icon.Office
                          size={space[4]}
                          fill={
                            filters.type.entity
                              ? colors.primary
                              : colors.secondary
                          }
                        />
                      )}
                      onPress={() =>
                        setFilters({
                          ...filters,
                          type: {
                            ...filters.type,
                            entity: !filters.type.entity,
                          },
                        })
                      }
                    />
                  </ScrollView>
                </Row>
                <LeadFilterModalButton
                  curTab="all"
                  onFiltered={onFiltered}
                  filtered={filters}
                  hasFiltered={isSomeFilterActive}
                  tabFilters={curTabFilters}
                />
              </Row>
            )}
          </Column>
          <Column height={space[3]} />
          <FlatList
            onLayout={e => {
              setListHeight(e.nativeEvent.layout.height);
            }}
            style={{ flex: 1 }}
            showsHorizontalScrollIndicator={false}
            data={leadData}
            ListEmptyComponent={() => {
              return (
                <>
                  <Box h={listHeight}>
                    <Column
                      paddingTop={space[20]}
                      justifyContent="center"
                      alignItems="center"
                      gap={space[4]}>
                      <EmptyCaseFunnelSVGMedium />
                      <Typography.LargeBody
                        style={{
                          textAlign: 'center',
                          maxWidth: 335,
                        }}
                        color={colors.palette.fwdGreyDarker}>
                        {t('search.noResults')}
                      </Typography.LargeBody>
                    </Column>
                  </Box>
                  <AddNewLeadButton />
                </>
              );
            }}
            renderItem={({ item }) => (
              <LeadListCardItem
                isToday={
                  item.opportunityUpdatedAt
                    ? isWithinPast24Hours(new Date(item.opportunityUpdatedAt))
                    : false
                }
                {...item}
              />
            )}
            contentContainerStyle={{
              paddingBottom: space[4] + bottom,
            }}
            ItemSeparatorComponent={() => <Spacer height={space[2]} />}
            keyExtractor={item => item.id as any}
            onScrollEndDrag={({ nativeEvent }) => {
              const { contentOffset } = nativeEvent;
              startOffsetY.current = contentOffset.y;
            }}
          />
        </>
      )}
    </AnimatedViewWrapper>
  );
}

const Spacer = styled.View(({ height }: { height: number }) => ({
  height,
}));

function isWithinPast24Hours(date: Date) {
  const now = new Date();
  const oneDayAgo = sub(now, { hours: 24 });
  return isWithinInterval(date, { start: oneDayAgo, end: now });
}
