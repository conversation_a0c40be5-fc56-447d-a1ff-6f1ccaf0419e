import React, { Fragment } from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { XView } from 'cube-ui-components';
import { View } from 'react-native';
import { H8 } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';

import ResponsiveView from 'components/ResponsiveView';
import { useTranslation } from 'react-i18next';

export default function LeadTableTitleRow() {
  const { t } = useTranslation('lead');
  const { colors } = useTheme();

  return (
    <View style={{ backgroundColor: colors.palette.fwdGrey[50] }}>
      <TableTitleRow>
        <XView>
          {leadTableConfig.certificatePending.listHeader.map((item, index) => (
            <Fragment key={'leadTableConfig' + index}>
              <View style={{ width: item.width, alignItems: 'flex-start' }}>
                <TitleContainer>
                  <TitleStyle fontWeight="bold">{t(item.name)}</TitleStyle>
                </TitleContainer>
              </View>
              {index <
                leadTableConfig.certificatePending.listHeader.length - 1 && (
                <TitleSeparatorLine />
              )}
            </Fragment>
          ))}
        </XView>
      </TableTitleRow>
    </View>
  );
}

export const leadTableConfig = {
  certificatePending: {
    listHeader: [
      {
        type: 'LeadsName',
        name: 'lead.leadsName',
        width: '40%',
      },
      {
        type: 'LeadSource',
        name: 'lead.leadSource',
        width: '15%',
      },
      {
        type: 'Status',
        name: 'lead.status',
        width: '15%',
      },
      {
        type: 'MobileNumber',
        name: 'lead.mobileNumber',
        width: '30%',
      },
    ],
  },
} as const;

export const TitleSeparatorLine = styled.View(({ theme }) => ({
  width: 1,
  height: '50%',
  alignSelf: 'center',
  backgroundColor: theme.colors.background,
}));

export const OutstandingItemStyle = styled(H8)(() => ({
  color: colors.alertRed,
  textAlign: 'center',
}));

export const InfoStyle = styled.View(({ theme: { sizes } }) => ({
  paddingVertical: sizes[3],
}));

export const IconUp = styled.TouchableOpacity(({ theme: { sizes } }) => ({
  marginRight: sizes[1],
}));

export const TitleContainer = styled.View(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

export const Spacer = styled.View(({ height }: { height: number }) => ({
  height,
}));

export const DrawerContainer = styled(ResponsiveView)(() => ({
  flexDirection: 'row',
  height: '100%',
}));

export const TitleStyle = styled(H8)(({ theme: { sizes } }) => ({
  color: colors.white,
  padding: sizes[4],
}));

export const TableTitleRow = styled.View(({ theme }) => [
  {
    width: '100%',
    height: 50,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.space[4],
    backgroundColor: theme.colors.primary,
  },
]);
