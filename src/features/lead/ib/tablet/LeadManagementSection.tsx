import { useTheme } from '@emotion/react';
import { Box, Row, Typography, Chip } from 'cube-ui-components';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';

import React, { useState } from 'react';
import { CurrentLeadTabKeys } from 'types';
import { useTranslation } from 'react-i18next';
import TodayLeadList from 'features/lead/tablet/TodayLeadList';
import OtherLeadList from 'features/lead/tablet/OtherLeadList';

export default function LeadManagementSection() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('lead');
  const [currentTab, setCurrentTab] =
    useState<CurrentLeadTabKeys>('todayLeads');

  return (
    <>
      <AnimatedViewWrapper
        style={{
          flex: 1,
          paddingTop: space[4],
          paddingHorizontal: space[8],
          backgroundColor: colors.palette.fwdGrey[50],
        }}>
        <Box flex={1} gap={space[4]}>
          <Row
            alignItems={'center'}
            minHeight={space[11]}
            justifyContent={'space-between'}>
            <Typography.H6 fontWeight="bold">{t('yourLeads')}</Typography.H6>
          </Row>
          <Row gap={space[1]}>
            <Chip
              size="medium"
              label={t('todayLeads')}
              focus={currentTab === 'todayLeads'}
              onPress={() => setCurrentTab('todayLeads')}
            />
            <Chip
              size="medium"
              focus={currentTab === 'otherLeads'}
              label={t('otherLeads')}
              onPress={() => setCurrentTab('otherLeads')}
            />
          </Row>
          {currentTab === 'todayLeads' && (
            <TodayLeadList setCurrentTab={setCurrentTab} />
          )}
          {currentTab === 'otherLeads' && (
            <OtherLeadList setCurrentTab={setCurrentTab} />
          )}
        </Box>
      </AnimatedViewWrapper>
    </>
  );
}
