import React, { useState } from 'react';
import { Fna } from 'types/case';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { lifeStageLabelMapping } from '../../../ph/LeadProfile/components/Fna/FnaInfo';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import DependentSummary from 'features/lead/ib/LeadProfile/components/Fna/DependentSummary';
import DialogPhone from 'components/Dialog.phone';
import { TouchableOpacity } from 'react-native';
import { country } from 'utils/context';

const ProfileDetailsCard = ({ fnaData }: { fnaData: Fna }) => {
  const { t } = useTranslation(['fna', 'leadProfile']);
  const { colors, space } = useTheme();
  const hasExistingPolicies = fnaData.existingPolicies?.length ?? 0 > 0;
  const dependents = fnaData.dependents.filter(
    dependent => dependent.relationship && dependent.gender,
  );
  const hasDependents = dependents.length > 0;
  const fieldRows = [
    {
      label: 'fna:lifeStage.detail.question.income',
      value: displayIncomeRange(
        fnaData.annuallyIncome.from,
        fnaData.annuallyIncome.to,
      ),
    },
    {
      label: 'fna:expectedIncomeIncrement',
      value: fnaData?.expectedIncomeIncrement + ' %',
    },
    {
      label: 'fna:lifeStage.detail.question.financialBudget.part1',
      value: displayGoalPercentage(
        fnaData.goalPercentage.from,
        fnaData.goalPercentage.to,
      ),
    },
    {
      label: 'fna:totalAssets',
      value: `RM ${numberToThousandsFormat(fnaData.totalAssets)}`,
      tooltip: 'fna:totalAssets.reminder',
    },
    {
      label: 'fna:totalLiabilities',
      value: `RM ${numberToThousandsFormat(fnaData.totalLiabilities)}`,
      tooltip: 'fna:totalLiabilities.reminder',
    },
    {
      label: 'fna:existingPolicies',
      value: hasExistingPolicies ? 'Yes' : 'No',
    },
    {
      label: 'fna:lifeStage.detail.question.lifeStage',
      value: t(lifeStageLabelMapping[fnaData.lifeStage]),
    },
    {
      label: 'fna:savingsGoals.retirement.ageToRetire',
      value: fnaData?.ageToRetire ? `${fnaData.ageToRetire} years old` : '--',
    },
    {
      label: 'leadProfile:leadProfile.fna.noOfDependent',
      value: hasDependents ? 'Yes' : 'No',
    },
  ] satisfies FieldRowsArray;

  return (
    <Box gap={space[2]}>
      {fieldRows.map((item, index) => (
        <Row gap={space[4]} id={`profile_details_${index}`}>
          <Row width={'40%'} gap={space[1]} alignItems="center">
            <Typography.H8 color={colors.palette.fwdGreyDarker}>
              {t(item.label)}
            </Typography.H8>
            {item.tooltip && <ToolTipButton item={item} />}
          </Row>
          <Box width={'60%'}>
            <Typography.H8>{item.value}</Typography.H8>
          </Box>
        </Row>
      ))}
      {hasDependents && (
        <Box key={`dependant`}>
          <Typography.SmallBody color={colors.palette.fwdGreyDarker}>{`${t(
            'leadProfile:leadProfile.fna.dependant',
          )} (${dependents.length}): `}</Typography.SmallBody>
          <DependentSummary data={dependents} />
        </Box>
      )}
    </Box>
  );
};

const ToolTipButton = ({ item }: { item: FieldRow }) => {
  const { t } = useTranslation(['fna', 'leadProfile']);
  const [isVisible, setIsVisible] = useState(false);
  const { colors } = useTheme();
  const content = item.tooltip as
    | 'fna:totalAssets.reminder'
    | 'fna:totalLiabilities.reminder';
  return (
    <TouchableOpacity onPress={() => setIsVisible(true)}>
      {country === 'ph' ? (
        <Icon.Tooltip size={14} />
      ) : (
        <Icon.InfoCircle size={14} fill={colors.palette.alertRed} />
      )}
      {isVisible && (
        <ToolTipModal
          title={t(item.label)}
          content={t(content)}
          isVisible={isVisible}
          onClose={() => setIsVisible(false)}
        />
      )}
    </TouchableOpacity>
  );
};

const ToolTipModal = ({
  title,
  content,
  isVisible,
  onClose,
}: {
  title: string;
  content: string;
  isVisible: boolean;
  onClose: () => void;
}) => {
  const { colors, space, sizes } = useTheme();

  return (
    <DialogPhone visible={isVisible}>
      <TouchableOpacity onPress={onClose} style={{ alignItems: 'flex-end' }}>
        <Icon.Close size={sizes[5]} fill={colors.palette.black} />
      </TouchableOpacity>
      <Box gap={space[4]}>
        <Typography.H6 fontWeight="bold">{title}</Typography.H6>
        <Typography.Body>{content}</Typography.Body>
      </Box>
    </DialogPhone>
  );
};

const displayIncomeRange = (min: number | null, max: number | null) => {
  if (min && max)
    return `RM ${numberToThousandsFormat(min)} - RM ${numberToThousandsFormat(
      max,
    )}`;
  else if (min) return `> RM ${numberToThousandsFormat(min)}`;
  else if (max) return `< RM ${numberToThousandsFormat(max)}`;
};

const displayGoalPercentage = (min: number | null, max: number | null) => {
  if (min && max) return `${min}% to ${max}%`;
  else if (min) return `${min}% and above`;
  else if (max) return `Below ${max}%`;
  return;
};

type FieldRow = {
  label:
    | 'fna:lifeStage.detail.question.income'
    | 'fna:expectedIncomeIncrement'
    | 'fna:lifeStage.detail.question.financialBudget.part1'
    | 'fna:totalAssets'
    | 'fna:totalLiabilities'
    | 'fna:existingPolicies'
    | 'fna:lifeStage.detail.question.lifeStage'
    | 'fna:savingsGoals.retirement.ageToRetire'
    | 'leadProfile:leadProfile.fna.noOfDependent';
  value: string | undefined;
  tooltip?: string;
};

type FieldRowsArray = FieldRow[];

export default ProfileDetailsCard;
