import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import AppTopTabBar, {
  AppTopTabBarAnimatedProps,
} from 'components/AppTopTabBar';
import useBoundStore from 'hooks/useBoundStore';
import React from 'react';
import { useTranslation } from 'react-i18next';
import MyLeads from 'features/lead/ib/phone/MyLeads';
import SalesActivityScreen from 'screens/LeadScreen/SalesActivityScreen';
import { country } from 'utils/context';

const LeadTab = createMaterialTopTabNavigator<{
  MyLeads: undefined;
  SalesActivity: undefined;
}>();

const LeadsSection = ({
  animatedProps,
}: {
  animatedProps: AppTopTabBarAnimatedProps | undefined;
}) => {
  const { t } = useTranslation(['navigation']);

  const secondaryBarHeight = useBoundStore(
    store => store.lead.secondaryBarHeight,
  );
  const updateSecondaryBarHeight = useBoundStore(
    store => store.leadActions.updateSecondaryBarHeight,
  );
  const isIB = country === 'ib' ? true : false;

  return (
    <LeadTab.Navigator
      initialRouteName="MyLeads"
      screenOptions={{ swipeEnabled: false }}
      tabBar={props => (
        <AppTopTabBar
          isCustomizedNormalTabLabel={isIB}
          {...props}
          animatedProps={animatedProps}
          onLayout={e => {
            e.nativeEvent.layout.height > secondaryBarHeight &&
              updateSecondaryBarHeight(e.nativeEvent.layout.height);
          }}
        />
      )}>
      <LeadTab.Screen
        name="MyLeads"
        component={MyLeads}
        options={{ tabBarLabel: t('navigation:tabScreen.MyLeads') }}
      />
      <LeadTab.Screen
        name="SalesActivity"
        component={SalesActivityScreen}
        options={{ tabBarLabel: t('navigation:tabScreen.SalesActivity') }}
      />
    </LeadTab.Navigator>
  );
};

export default LeadsSection;
