import { useTheme } from '@emotion/react';
import { Box, Column, LargeLabel, Row, SmallBody, SvgIconProps } from 'cube-ui-components';
import { ConcernId } from 'features/fna/types/concern';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { G, Path, Rect, Svg } from 'react-native-svg';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';

interface Props {
  concernId?: ConcernId;
  needType?: 'SAVINGS' | 'PROTECTION';
  goal: {
    isMonthly?: boolean;
    coverageAmount: number | null;
    gapAmount: number | null;
    targetAmount: number | null;
    yearsToAchieve?: number | null;
  };
  currencyCode?: string;
}

const AnimatedSvg = Animated.createAnimatedComponent(Svg);

export default function CoverageScoreBar({
  goal,

  // needType,
  //   concernId,
  currencyCode,
}: Props) {
  const { t } = useTranslation(['fna', 'leadProfile']);

  const [barMaxWidth, setBarMaxWidth] = useState(0);
  const coverageWidth = useSharedValue(0);
  const { colors, space, sizes } = useTheme();
  const dashSpacing = space[2];
  const currencyCodeDisplayed = currencyCode ?? t('fna:php');

  const yDashes = new Array(Math.floor(space[4] / dashSpacing)).fill(null);

  const percentage = useMemo(() => {
    if (
      goal.coverageAmount === null ||
      goal.coverageAmount === undefined ||
      goal.targetAmount === null ||
      goal.targetAmount === undefined
    )
      return 0;
    if (goal.coverageAmount > goal.targetAmount) {
      return 1;
    }
    return goal.coverageAmount / goal.targetAmount;
  }, [goal]);

  // const coverageDash = useMemo(() => {
  //   return new Array(Math.round((barMaxWidth * percentage) / dashSpacing)).fill(
  //     null,
  //   );
  // }, [barMaxWidth]);
  const coverageDash = useMemo(() => {
    const dashCount = Math.max(
      1,
      Math.round((barMaxWidth * percentage) / dashSpacing),
    );
    return new Array(dashCount).fill(null);
  }, [barMaxWidth, percentage, dashSpacing]);

  const remainingGapDash = useMemo(() => {
    return new Array(Math.floor(barMaxWidth / dashSpacing)).fill(null);
  }, [barMaxWidth]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: coverageWidth.value,
      height: sizes[3] + 2, // Add-on 2 pixel for border
      backgroundColor: colors.palette.fwdDarkGreen[100],
      borderRadius: sizes[2],
      borderWidth: coverageWidth.value === 0 ? 0 : 1,
      borderLeftWidth: 0,
      borderColor: colors.background,
    };
  });

  const currentAssetAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: coverageWidth.value,
    };
  });

  const remainingAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: barMaxWidth - coverageWidth.value - space[1],
    };
  });

  useEffect(() => {
    coverageWidth.value = withTiming(barMaxWidth * percentage, {
      duration: 500,
    });
  }, [barMaxWidth, percentage]);

  const monthlyGoal = goal.isMonthly;

  return (
    <Column>
      <Box mb={space[1]}>
        <SmallBody>
          {/* {needType === 'SAVINGS'
            ? t('fna:goals.currentAssets')
            : t('fna:goals.currentCoverage')} */}
          {t('leadProfile:leadProfile.current')}
          {': '}
          {currencyCodeDisplayed}{' '}
          {goal.coverageAmount !== null && goal.coverageAmount !== undefined
            ? numberToThousandsFormat(goal.coverageAmount)
            : '--'}
        </SmallBody>
      </Box>
      <Row>
        <Svg height={sizes[4]} width={sizes[1] / 2 + 1}>
          {yDashes.map((_, index) => (
            <Rect
              key={index}
              x={1}
              y={0}
              width={dashSpacing / 4}
              height={dashSpacing / 3}
              fill={colors.palette.fwdDarkGreen[100]}
              translateY={dashSpacing * index}
            />
          ))}
        </Svg>
        <AnimatedSvg height={sizes[3]} style={currentAssetAnimatedStyle}>
          {coverageDash.map((_, index) => (
            <Rect
              key={index}
              x={0}
              y={0}
              width={dashSpacing / 2}
              height={dashSpacing / 4}
              fill={colors.palette.fwdDarkGreen[100]}
              translateX={space[2] * index}
            />
          ))}
          {yDashes.map((_, index) => (
            <Rect
              key={index}
              x={barMaxWidth * percentage - space[1]}
              y={0}
              width={dashSpacing / 4}
              height={dashSpacing / 3}
              fill={colors.palette.fwdDarkGreen[100]}
              translateY={dashSpacing * index}
            />
          ))}
        </AnimatedSvg>
        <Box pos="absolute" right={1} top={2}>
          <BluePolygon />
        </Box>
      </Row>
      <Row
        alignItems="center"
        bgColor={colors.palette.alertRed}
        h={sizes[3] + 2}
        borderRadius={sizes[2]}
        border={1}
        borderColor={colors.background}
        overflow="hidden"
        onLayout={e => {
          setBarMaxWidth(e.nativeEvent.layout.width);
        }}>
        <Animated.View style={animatedStyle} />
      </Row>
      <Row alignItems="center" justifyContent="flex-end">
        <AnimatedSvg height={sizes[5]} style={remainingAnimatedStyle}>
          <G>
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={space[1]}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.palette.alertRed}
                translateY={dashSpacing * index}
              />
            ))}
            {remainingGapDash.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={sizes[4]}
                width={dashSpacing / 2}
                height={dashSpacing / 4}
                fill={colors.palette.alertRed}
                translateX={dashSpacing * index}
              />
            ))}
          </G>
        </AnimatedSvg>
        <Svg height={sizes[5]} width={sizes[1] / 2}>
          <G>
            {yDashes.map((_, index) => (
              <Rect
                key={index}
                x={0}
                y={space[1]}
                width={dashSpacing / 4}
                height={dashSpacing / 2}
                fill={colors.palette.alertRed}
                translateY={dashSpacing * index}
              />
            ))}
          </G>
        </Svg>
      </Row>
      <Row alignItems="center" justifyContent="flex-end">
        <SmallBody color={colors.palette.alertRed}>
          {t('fna:goals.gapToTarget')}
        </SmallBody>
        <Row>
          <SmallBody fontWeight="bold" color={colors.palette.alertRed}>
            {currencyCodeDisplayed}{' '}
          </SmallBody>
          <LargeLabel fontWeight="bold" color={colors.palette.alertRed}>
            {(goal.targetAmount !== null && goal.targetAmount !== undefined) ||
            (goal.coverageAmount !== null && goal.coverageAmount !== undefined)
              ? goal.gapAmount && goal.gapAmount >= 0
                ? monthlyGoal
                  ? `${numberToThousandsFormat(goal.gapAmount)}` +
                    t('leadProfile:leadProfile.month')
                  : `${numberToThousandsFormat(goal.gapAmount)}`
                : '0' + (monthlyGoal ? t('leadProfile:leadProfile.month') : '')
              : '--' + t('leadProfile:leadProfile.month')}
          </LargeLabel>
        </Row>
      </Row>
    </Column>
  );
}

export function BluePolygon(props: Omit<SvgIconProps, 'source'>) {
  return (
    <Svg width={12} height={10} viewBox="0 0 12 10" fill="none" {...props}>
      <Path
        d="M6.857 8.57a1 1 0 01-1.714 0L.909 1.515A1 1 0 011.766 0h8.468a1 1 0 01.857 1.514L6.857 8.571z"
        fill="#0097A9"
      />
    </Svg>
  );
}
