import React from 'react';
import { country } from 'utils/context';
import MYAddNewEntityForm, {
  AddNewEntityFormProps as MYAddNewEntityFormProps,
} from '../../../my/tablet/AddNewEntityForm';
import PHAddNewEntityForm, {
  AddNewEntityFormProps as PHAddNewEntityFormProps,
} from '../../../ph/tablet/AddNewEntityForm';

export default function AddNewEntityForm(
  props: MYAddNewEntityFormProps | PHAddNewEntityFormProps,
) {
  switch (country) {
    case 'ph':
      return <PHAddNewEntityForm {...(props as PHAddNewEntityFormProps)} />;
    case 'my':
      return <MYAddNewEntityForm {...(props as MYAddNewEntityFormProps)} />;
    default:
      return <MYAddNewEntityForm {...(props as MYAddNewEntityFormProps)} />;
  }
}
