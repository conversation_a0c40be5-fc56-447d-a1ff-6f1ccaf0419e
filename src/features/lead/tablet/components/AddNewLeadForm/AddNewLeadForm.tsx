import React from 'react';
import { country } from 'utils/context';
import MYAddNewLeadForm, {
  AddNewLeadFormProps as MYAddNewLeadFormProps,
} from 'features/lead/my/tablet/AddNewLeadForm';
import IBAddNewLeadForm, {
  AddNewLeadFormProps as IBAddNewLeadFormProps,
} from 'features/lead/ib/tablet/AddNewLeadForm';
import PHAddNewLeadForm, {
  AddNewLeadFormProps as PHAddNewLeadFormProps,
} from 'features/lead/ph/tablet/AddNewLeadForm';

export default function AddNewLeadForm(
  props: MYAddNewLeadFormProps | PHAddNewLeadFormProps,
) {
  console.log('country', country);
  switch (country) {
    case 'ph':
      return <PHAddNewLeadForm {...(props as PHAddNewLeadFormProps)} />;
    case 'my':
      return <MYAddNewLeadForm {...(props as MYAddNewLeadFormProps)} />;
    case 'ib':
    case 'id':
      return <IBAddNewLeadForm {...(props as IBAddNewLeadFormProps)} />;
    default:
      return <MYAddNewLeadForm {...(props as MYAddNewLeadFormProps)} />;
  }
}
