import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import React from 'react';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export default function TabButton(
  props: {
    isActive: boolean;
    isActiveColor?: string;
    label: string;
    containerStyle?: ViewStyle;
    Icon?: React.ReactNode;
    isActiveMedium?: boolean;
    labelStyle?: TextStyle;
  } & TouchableOpacityProps,
) {
  const { colors, space, borderRadius } = useTheme();
  const { isActive, isActiveColor, label, containerStyle, isActiveMedium, labelStyle, ...rest } = props;
  return (
    <TouchableOpacity
      style={[
        {
          flex: 1,
          borderWidth: isActive ? 2 : 1,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: borderRadius['x-large'],
          paddingHorizontal: space[4],
          // paddingVertical: space[2] + 2,
          borderColor: isActive ? colors.primary : colors.palette.fwdGrey[100],
          backgroundColor: isActive
            ? colors.primaryVariant3
            : colors.background,
          height: space[10],
        },
        containerStyle,
      ]}
      {...rest}>
      <Row alignItems="center" gap={space[1]}>
        {props?.Icon}
        <Typography.LargeLabel
          fontWeight={
            isActive ? (isActiveMedium ? 'medium' : 'bold') : 'normal'
          }
          color={isActive ? (isActiveColor ? isActiveColor : colors.primary) : colors.secondary}
          style={labelStyle ? labelStyle : {}}
          >
          {label}
        </Typography.LargeLabel>
      </Row>
    </TouchableOpacity>
  );
}
