import { View } from 'react-native';
import React, { useEffect, useMemo, useState } from 'react';
import { Box, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import CustomSearchingBar from 'components/CustomSearchingBar';
import { useTranslation } from 'react-i18next';
import { NBIssued } from 'types/policy';
import { IssuedTable } from './IssuedTable';
import { country } from 'utils/context';

export type SortKeys = 'newest' | 'oldest';

export default function SearchSection({
  setIsSearching,
  data,
}: {
  data: NBIssued[];
  setIsSearching: (value: boolean) => void;
}) {
  const { t } = useTranslation('policy');
  const { sizes, space, colors } = useTheme();
  const [searchKeyWords, setSearchKeyWords] = useState<string>('');

  const searchResultList = useMemo(() => {
    return data.filter(
      item =>
        item.displayName.en
          ?.toLowerCase()
          .includes(searchKeyWords.toLowerCase()) ||
        item.policyNo?.toLowerCase().includes(searchKeyWords.toLowerCase()),
    );
  }, [data, searchKeyWords]);

  return (
    <AnimatedViewWrapper
      style={{
        paddingTop: space[5],
        paddingHorizontal: space[6],
        backgroundColor: colors.palette.fwdGrey[50],
        position: 'relative',
        flex: 1,
      }}>
      <CustomSearchingBar
        onExitSearch={() => setIsSearching(false)}
        setSearchKeyWords={setSearchKeyWords}
        placeholderText={t('lookForCertificateOwnerName')}
      />
      {searchKeyWords.trim().length > 0 && (
        <Box flex={1}>
          <Box pt={space[2]} pb={space[3]}>
            <View style={{ paddingVertical: sizes[4] }}>
              <Typography.H7 fontWeight="bold">
                {country == 'ib'
                  ? t('search.searchResult')
                  : t('searchResult', { count: searchResultList?.length ?? 0 })}
              </Typography.H7>
            </View>
            {country === 'my' ? (
              <Typography.H8 color={colors.palette.fwdGreyDarkest}>
                {t('search.resultCount.pending.displaying.text')}
              </Typography.H8>
            ) : (
              <Typography.H8 color={colors.palette.fwdGreyDarkest}>
                {t('search.resultCount.with90DaysLimit', {
                  count: searchResultList?.length ?? 0,
                })}
              </Typography.H8>
            )}
          </Box>
          <IssuedTable data={searchResultList} />
        </Box>
      )}
    </AnimatedViewWrapper>
  );
}
