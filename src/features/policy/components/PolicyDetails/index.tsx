import { country } from 'utils/context';
import PoliciesScreenTablet from './tablet';
import PoliciesScreenPhone, {
  DetailProps,
} from '../../ph/phone/PolicyDetails/PolicyDetailScreen';
import PoliciesScreenPhoneV2 from './phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import PolicyDetailsScreenPH from 'features/policy/ph/tablet/PolicyDetails';
import NotFoundScreen from 'screens/NotFoundScreen';

export default function PolicyDetailsScreen(props: DetailProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PolicyDetailsScreenTablet />
  ) : (
    <PolicyDetailsScreenPhone {...props} />
  );
}

function PolicyDetailsScreenTablet() {
  switch (country) {
    case 'ph':
      return <PolicyDetailsScreenPH />;
    case 'my':
    case 'ib':
    default:
      return <PoliciesScreenTablet />;
  }
}

function PolicyDetailsScreenPhone(props: DetailProps) {
  switch (country) {
    case 'ph':
      return <PoliciesScreenPhone {...props} />;
    case 'my':
    case 'ib':
    case 'id':
      return <PoliciesScreenPhoneV2 />;

    default:
      return <NotFoundScreen />;
  }
}
