import { useTheme } from '@emotion/react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  PolicyDetailMapping,
  PolicyHeaderLabelStatus,
  PolicyInfoFromList,
} from 'types/policy';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function PolicyDetailsHeaderFootnote({
  status,
  policyInfoByParams,
  policyDetailMapping,
}: {
  status: PolicyHeaderLabelStatus | undefined;
  policyDetailMapping: PolicyDetailMapping;
  policyInfoByParams: PolicyInfoFromList | undefined;
}) {
  const { t } = useTranslation('policy');
  const { colors, space } = useTheme();

  const dueDate = policyInfoByParams?.dueDate
    ? dateFormatUtil(policyInfoByParams?.dueDate)
    : '--';

  const freelookDate = policyInfoByParams?.freeLookDate
    ? dateFormatUtil(policyInfoByParams?.freeLookDate)
    : '--';

  const issueDate = policyDetailMapping?.issueDate
    ? dateFormatUtil(policyDetailMapping?.issueDate)
    : '--';

  if (status === 'due') {
    return (
      <>
        <Row gap={4} style={{ marginRight: 48 }}>
          <Icon.Warning fill={colors.palette.white} height={18} width={18} />
          <Typography.Label
            color={colors.palette.white}
            style={{ marginRight: 48 }}>
            <Typography.Label fontWeight="bold" color={colors.palette.white}>
              {t('detail.dueWarningEmphasis')}
            </Typography.Label>
            {t('detail.dueWarningText')}
          </Typography.Label>
        </Row>
        <Row gap={4}>
          <Icon.Calendar fill={colors.palette.white} height={18} width={18} />
          <Typography.Label color={colors.palette.white}>
            {t('detail.dueDate') + ':'}
          </Typography.Label>
          <Typography.Label color={colors.palette.white}>
            {dueDate}
          </Typography.Label>
        </Row>
      </>
    );
  }
  if (status === 'overdue') {
    return (
      <>
        <Row gap={space[1]} marginRight={space[12]}>
          <Icon.Warning fill={colors.palette.white} height={18} width={18} />
          <Typography.Label
            color={colors.palette.white}
            style={{ marginRight: 48 }}>
            <Typography.Label fontWeight="bold" color={colors.palette.white}>
              {t('detail.overdueWarningEmphasis') + ' '}
            </Typography.Label>
            {t('detail.overdueWarningText')}
          </Typography.Label>
        </Row>
        <Row gap={4}>
          <Icon.Calendar fill={colors.palette.white} height={18} width={18} />
          <Typography.Label color={colors.palette.white}>
            {t('detail.dueDate') + ': '}
          </Typography.Label>
          <Typography.Label color={colors.palette.white}>
            {dueDate}
          </Typography.Label>
        </Row>
      </>
    );
  }
  if (status === 'freelook') {
    return (
      <>
        <Row gap={4} style={{ marginRight: 48 }}>
          <Icon.Warning fill={colors.palette.white} height={18} width={18} />
          <Typography.Label
            color={colors.palette.white}
            style={{ marginRight: 48 }}>
            <Typography.Label fontWeight="bold" color={colors.palette.white}>
              {t('detail.freelook.description')}
            </Typography.Label>
          </Typography.Label>
        </Row>
        <Row gap={4}>
          <Icon.Calendar fill={colors.palette.white} height={18} width={18} />
          <Typography.Label color={colors.palette.white}>
            {t('detail.freelook.issuedDate') + ':'}
          </Typography.Label>
          <Typography.Label color={colors.palette.white}>
            {issueDate}
          </Typography.Label>
        </Row>
      </>
    );
  }

  return <></>;
}
