import { TouchableOpacity } from 'react-native';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ApproveModal } from './ApproveModal';
import { RejectModal } from './RejectModal';
import { useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UIMode } from 'types';

export function ApprovalFooter({
  mode = 'tablet',
  caseId,
  isVisible,
}: {
  mode?: UIMode;
  caseId: string | undefined;
  isVisible: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();

  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  return (
    <>
      <ApproveModal
        mode={mode}
        caseId={caseId}
        setShowApproveModal={setShowApproveModal}
        isVisible={showApproveModal}
      />
      <RejectModal
        mode={mode}
        caseId={caseId}
        setShowRejectModal={setShowRejectModal}
        isVisible={showRejectModal}
      />
      {isVisible ? (
        <Row
          justifyContent="flex-end"
          style={{
            backgroundColor: colors.background,
            borderTopColor: colors.palette.fwdGrey[100],
            borderTopWidth: 1,
            paddingHorizontal: space[6],
            // paddingVertical: space[4],
            paddingTop: space[4],
            paddingBottom: space[4] + bottom,
            gap: space[5],
          }}>
          <TouchableOpacity
            onPress={() => {
              setShowRejectModal(true);
            }}
            style={{
              width: mode == 'tablet' ? 200 : undefined,
              flex: mode == 'tablet' ? undefined : 1,
              height: 52,
              borderRadius: space[1],
              borderColor: colors.primary,
              borderWidth: 2,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Typography.LargeLabel color={colors.primary} fontWeight="bold">
              Reject
            </Typography.LargeLabel>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setShowApproveModal(true);
            }}
            style={{
              width: mode == 'tablet' ? 200 : undefined,
              flex: mode == 'tablet' ? undefined : 1,
              height: 52,
              borderRadius: space[1],
              borderColor: colors.primary,
              borderWidth: 2,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Typography.LargeLabel color={colors.primary} fontWeight="bold">
              Approve
            </Typography.LargeLabel>
          </TouchableOpacity>
        </Row>
      ) : null}
    </>
  );
}
