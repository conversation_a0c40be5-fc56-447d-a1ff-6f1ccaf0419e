import { useTheme } from '@emotion/react';
import { useIsFocused } from '@react-navigation/native';
import CustomSearchingBar from 'components/CustomSearchingBar';
import {
  Box,
  Chip,
  LoadingIndicator,
  Row,
  Typography,
} from 'cube-ui-components';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { BulkApproveModal } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview/BulkApprovelModal';
import { LeaderReviewTable } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview/LeaderReviewTable';
import DefaultSearchBtn from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission/DefaultSearchBtn';
import MyDefaultSearchBtn from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission/MyDefaultSeachBtn';
import useBoundStore from 'hooks/useBoundStore';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import { useGetSavedProposalsFilters } from 'hooks/useGetSavedProposalsFilters';
import React, {
  Dispatch,
  SetStateAction,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, ScrollView, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CaseStatus } from 'types/case';
import { SortDirectionKeys } from 'types/eRecruit';
import { PartyRole } from 'types/party';
import {
  NBPolicyLeaderReviewStatus,
  NBPolicyLeaderReviewSubmissionStatus,
  NBSubmissionForReviewStatus,
} from 'types/policy';
import { PaymentMode } from 'types/proposal';
import { country } from 'utils/context';
import { filterLeaderReviewStatus } from './config';
import { CaseStatusToNBSubmissionForReviewStatusMap } from 'features/policy/hooks/useGetCaseObjInfo';

// export to IB mobile - ReviewApplicationScreen
export type NBLeaderReviewTableContent = {
  caseId: string;
  certNo: string;
  certOwner: string;
  contribution: number | undefined;
  paymentMode: string | undefined;
  agentName: string;
  uwDecision: string;
  status: NBPolicyLeaderReviewStatus;
  submissionStatus: NBPolicyLeaderReviewSubmissionStatus | '';
  submissionDate: string;
  pendingReasons: string[];
};

export default function NewBusinessLeaderReviewScreen() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('policy');
  const { bottom } = useSafeAreaInsets();

  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const [filterBy, setFilterBy] = useState<NBPolicyLeaderReviewStatus[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchKeyWords, setSearchKeyWords] = useState<string>('');
  const [isBulkSelect, setIsBulkSelect] = useState<boolean>(false);
  const [bulkSelectedCases, setBulkSelectedCase] = useState<string[]>([]);
  const [showApproveModal, setShowApproveModal] = useState<boolean>(false);

  const isFocused = useIsFocused();
  const setIsShowWhiteBottom = useBoundStore(
    store => store.homeActions.setIsShowWhiteBottom,
  );
  useEffect(() => {
    if (!isFocused) {
      setIsBulkSelect(false);
      setIsShowWhiteBottom(false);
    }
  }, [isFocused]);

  const [statusFilterParams, setsStatusFilterParams] = useState<CaseStatus[]>([
    CaseStatus.PENDING_FOR_LEADER,
    CaseStatus.EXPIRED_AFTER_15_DAYS,
    CaseStatus.REJECTED_BY_LEADER,
    CaseStatus.APPROVED_BY_LEADER,
  ]);

  const {
    isLoading: isSavedProposalsLoading,
    isFetchingNextPage,
    isRefetching,
    refetch,
    hasNextPage,
    fetchNextPage,
    data,
  } = useGetSavedProposals({
    status: statusFilterParams,
    sort_by: order === 'oldest' ? '+updatedAt' : '-updatedAt',
    q: undefined,
    downlineOnly: true,
  });

  const policyCasesToBeReviewed = useMemo(() => {
    if (!data) {
      return [];
    }
    return data?.pages.map(page => page.data).flat();
  }, [data]);

  const tableContent: NBLeaderReviewTableContent[] = useMemo(() => {
    // already sorted by backend
    const dataWithDateSort = policyCasesToBeReviewed;

    const filteredList =
      dataWithDateSort && filterBy.length > 0
        ? dataWithDateSort.filter(item => {
            const latestStatus = item.latestStatus;
            if (latestStatus in CaseStatusToNBSubmissionForReviewStatusMap) {
              const frontendKey =
                CaseStatusToNBSubmissionForReviewStatusMap?.[latestStatus];
              return frontendKey ? filterBy.includes(frontendKey) : false;
            }
            return true;
          })
        : dataWithDateSort;

    return filteredList.map(item => {
      const clientType = item.parties.find(party =>
        party.roles.includes(PartyRole.PROPOSER),
      )?.clientType;
      const policyNo = item?.application?.policyNum ?? '--';
      const policyOwnerNameObj = item.parties.find(party =>
        party.roles.includes(PartyRole.PROPOSER),
      )?.person?.name;
      const entityOwnerName =
        item.parties.find(party => party.roles.includes(PartyRole.PROPOSER))
          ?.entity?.name ?? '--';
      const latestStatus = item.latestStatus;

      const pendingReasons = item?.pendingReasons || [];

      const frontendKey =
        latestStatus in CaseStatusToNBSubmissionForReviewStatusMap
          ? CaseStatusToNBSubmissionForReviewStatusMap[latestStatus] ??
            'leaderNotResponded'
          : 'leaderNotResponded';

      const policyPayment =
        item.quotation?.basicInfo?.paymentMode ??
        item.quotation?.plans?.find(item => item?.paymentMode)?.paymentMode;
      const policyPremium =
        policyPayment === PaymentMode.ANNUAL
          ? item.quotation?.summary?.annualPrem
          : policyPayment === PaymentMode.SEMI_ANNUAL
          ? item.quotation?.summary?.semiAnnualPrem
          : policyPayment === PaymentMode.QUARTERLY
          ? item.quotation?.summary?.quarterlyPrem
          : policyPayment === PaymentMode.MONTHLY
          ? item.quotation?.summary?.monthlyPrem
          : policyPayment === PaymentMode.SINGLE ||
            policyPayment === PaymentMode.ONE_TIME
          ? item.quotation?.summary?.totalPrem
          : undefined;

      return {
        caseId: item.caseId,
        certNo: policyNo,
        certOwner:
          clientType === 'INDIVIDUAL'
            ? policyOwnerNameObj?.firstName ??
              '' + policyOwnerNameObj?.lastName ??
              ''
            : entityOwnerName,
        contribution: policyPremium,
        paymentMode: policyPayment,
        agentName: item.agent?.fullName ?? '--',
        uwDecision: '--',
        status: frontendKey ?? '',
        submissionStatus: item.submissionResult?.status ?? '',
        submissionDate: item?.submissionResult?.submittedAt
          ? item?.submissionResult?.submittedAt
          : item.updatedAt ?? '--',
        pendingReasons,
      };
    });
  }, [order, filterBy, policyCasesToBeReviewed]);

  const searchResultList = useMemo(() => {
    return tableContent.filter(
      item =>
        item.certOwner?.toLowerCase().includes(searchKeyWords.toLowerCase()) ||
        item.certNo?.toLowerCase().includes(searchKeyWords.toLowerCase()),
    );
  }, [tableContent, searchKeyWords]);

  if (isSearching) {
    return (
      <AnimatedViewWrapper
        style={{
          paddingHorizontal: space[6],
          backgroundColor: colors.palette.fwdGrey[50],
          position: 'relative',
          flex: 1,
        }}>
        {showApproveModal && (
          <BulkApproveModal
            setShowApproveModal={setShowApproveModal}
            caseId={bulkSelectedCases}
            setIsBulkSelect={setIsBulkSelect}
          />
        )}
        <CustomSearchingBar
          onExitSearch={() => setIsSearching(false)}
          setSearchKeyWords={setSearchKeyWords}
          placeholderText={t('search.nbLeaderReview.placeholder')}
        />
        {searchKeyWords.trim().length > 0 && (
          <>
            <Box pt={space[6]} pb={space[3]}>
              <Typography.H7 fontWeight="bold">
                {country === 'my' || country === 'ib'
                  ? t('search.searchResultWithCount', {
                      count: searchResultList?.length ?? 0,
                    })
                  : t('search.searchResult')}
              </Typography.H7>
              <LeaderReviewFilterSection
                filterBy={filterBy}
                setFilterBy={setFilterBy}
              />
              <Row justifyContent="space-between" pb={space[4]}>
                <Typography.Label color={colors.palette.fwdGreyDarkest}>
                  {country === 'my'
                    ? t('search.resultCount.review.application.displaying.text')
                    : country === 'ib'
                    ? t('submissionStaticText')
                    : t('search.resultCount.with90DaysLimit')}
                </Typography.Label>
                <BulkApproveButton
                  isBulkSelect={isBulkSelect}
                  setIsBulkSelect={setIsBulkSelect}
                />
              </Row>
            </Box>
            <LeaderReviewTable
              data={searchResultList}
              isListProcessing={isSavedProposalsLoading}
              isRefreshing={isRefetching}
              onRefresh={refetch}
              order={order}
              setOrder={setOrder}
              isBulkSelect={isBulkSelect}
              setBulkSelectedCase={setBulkSelectedCase}
              bulkSelectedCases={bulkSelectedCases}
            />
            <Box h={bottom ? bottom + space[1] : space[5]} />
          </>
        )}
        {isBulkSelect && (
          <BulkSelectFooter
            isBulkSelect={isBulkSelect}
            setIsBulkSelect={setIsBulkSelect}
            setShowApproveModal={setShowApproveModal}
            bulkSelectedCases={bulkSelectedCases}
          />
        )}
      </AnimatedViewWrapper>
    );
  }

  return (
    <>
      <AnimatedViewWrapper
        style={{
          flex: 1,
          paddingHorizontal: space[6],
          backgroundColor: colors.palette.fwdGrey[50],
        }}>
        {showApproveModal && (
          <BulkApproveModal
            setShowApproveModal={setShowApproveModal}
            caseId={bulkSelectedCases}
            setIsBulkSelect={setIsBulkSelect}
          />
        )}
        <Row
          alignItems="center"
          minH={space[11]}
          justifyContent="space-between">
          <Typography.H6 fontWeight="bold">Review application</Typography.H6>
          {country == 'ib' ? (
            <DefaultSearchBtn setIsSearching={setIsSearching} />
          ) : (
            <MyDefaultSearchBtn setIsSearching={setIsSearching} />
          )}
        </Row>
        <LeaderReviewFilterSection
          filterBy={filterBy}
          setFilterBy={setFilterBy}
        />
        <Row
          style={{ paddingBottom: space[4], justifyContent: 'space-between' }}>
          <Typography.Label color={colors.palette.fwdGreyDarkest}>
            {t('search.resultCount.with90DaysLimit.review', {
              count: tableContent.length ?? 0,
            })}
          </Typography.Label>
          <BulkApproveButton
            isBulkSelect={isBulkSelect}
            setIsBulkSelect={setIsBulkSelect}
          />
        </Row>
        <View
          style={{
            flex: 1,
            marginBottom: isBulkSelect ? space[22] : space[4],
          }}>
          <LeaderReviewTable
            data={tableContent}
            isListProcessing={isSavedProposalsLoading}
            order={order}
            setOrder={setOrder}
            isBulkSelect={isBulkSelect}
            setBulkSelectedCase={setBulkSelectedCase}
            bulkSelectedCases={bulkSelectedCases}
            isRefreshing={isRefetching}
            onRefresh={refetch}
          />
        </View>
      </AnimatedViewWrapper>
      {isBulkSelect && (
        <BulkSelectFooter
          isBulkSelect={isBulkSelect}
          setIsBulkSelect={setIsBulkSelect}
          setShowApproveModal={setShowApproveModal}
          bulkSelectedCases={bulkSelectedCases}
        />
      )}
    </>
  );
}

function BulkApproveButton({
  isBulkSelect,
  setIsBulkSelect,
}: {
  isBulkSelect: boolean;
  setIsBulkSelect: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { colors, space, borderRadius } = useTheme();
  const setIsShowWhiteBottom = useBoundStore(
    store => store.homeActions.setIsShowWhiteBottom,
  );
  return (
    <TouchableOpacity
      onPress={() => {
        setIsBulkSelect(!isBulkSelect);
        setIsShowWhiteBottom(!isBulkSelect);
      }}>
      {!isBulkSelect && (
        <Typography.H7
          fontWeight="bold"
          color={colors.palette.fwdAlternativeOrange[100]}>
          Select to approve
        </Typography.H7>
      )}
    </TouchableOpacity>
  );
}

function BulkSelectFooter({
  isBulkSelect,
  setIsBulkSelect,
  setShowApproveModal,
  bulkSelectedCases,
}: {
  isBulkSelect: boolean;
  setIsBulkSelect: any;
  setShowApproveModal: Dispatch<SetStateAction<boolean>>;
  bulkSelectedCases: string[];
}) {
  const { colors, space, borderRadius } = useTheme();
  const setIsShowWhiteBottom = useBoundStore(
    store => store.homeActions.setIsShowWhiteBottom,
  );
  const slideAnimation = React.useMemo(() => new Animated.Value(0), []);
  useEffect(() => {
    if (isBulkSelect) {
      Animated.timing(slideAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        delay: 100,
      }).start();
    } else {
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        delay: 200,
      }).start();
    }
  }, [isBulkSelect, slideAnimation]);
  const modalTranslateY = slideAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1000, 0],
  });
  return (
    <Animated.View
      style={{
        transform: [{ translateY: modalTranslateY }],
      }}>
      <Row
        justifyContent="flex-end"
        style={{
          height: 84,
          backgroundColor: colors.background,
          borderTopColor: colors.palette.fwdGrey[100],
          borderTopWidth: 1,
          paddingHorizontal: space[6],
          paddingVertical: space[4],
          gap: space[5],
          position: 'absolute',
          bottom: 0,
          right: 0,
          width: '110%',
        }}>
        <TouchableOpacity
          onPress={() => {
            setIsBulkSelect(false);
            setIsShowWhiteBottom(false);
          }}
          style={{
            backgroundColor: colors.background,
            width: 116,
            height: 52,
            borderRadius: space[1],
            borderColor: colors.primary,
            borderWidth: 2,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Typography.LargeLabel color={colors.primary} fontWeight="bold">
            Cancel
          </Typography.LargeLabel>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            setShowApproveModal(true);
          }}
          disabled={bulkSelectedCases.length === 0}
          style={{
            width: 200,
            height: 52,
            borderRadius: space[1],
            borderColor:
              bulkSelectedCases.length === 0
                ? colors.palette.fwdOrange[50]
                : colors.primary,
            borderWidth: 2,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor:
              bulkSelectedCases.length === 0
                ? colors.palette.fwdOrange[50]
                : colors.primary,
          }}>
          <Typography.LargeLabel color={colors.background} fontWeight="bold">
            Approve
          </Typography.LargeLabel>
        </TouchableOpacity>
      </Row>
    </Animated.View>
  );
}

function LeaderReviewFilterSection({
  filterBy,
  setFilterBy,
}: {
  filterBy: NBPolicyLeaderReviewStatus[];
  setFilterBy: React.Dispatch<
    React.SetStateAction<NBPolicyLeaderReviewStatus[]>
  >;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('policy');
  const { data: filterData, isLoading: isFilterStatLoading } =
    useGetSavedProposalsFilters({
      isDownlineOnly: true,
    });

  // Part of "Show filter when data count is not 0" logic, disabled now
  // const statusFilterCountMap = useMemo(
  //   () =>
  //     filterData?.latestStatusStats.reduce(
  //       (acc: { [key in CaseStatus]?: number }, cur) => {
  //         switch ('latestStatus' in cur && cur.latestStatus) {
  //           case CaseStatus.PENDING_FOR_LEADER:
  //           case CaseStatus.REJECTED_BY_LEADER:
  //           case CaseStatus.APP_SUBMITTED:
  //           case CaseStatus.APPROVED_BY_LEADER:
  //           case CaseStatus.EXPIRED_AFTER_15_DAYS:
  //             acc[cur.latestStatus] = cur.count;
  //             return acc;
  //           default:
  //             return acc;
  //         }
  //       },
  //       {} satisfies { [key in CaseStatus]?: number },
  //     ),
  //   [filterData],
  // );

  // const NBPolicyLeaderReviewStatusToCaseStatus = {
  //   leaderApprovedToSubmit: CaseStatus.APPROVED_BY_LEADER,
  //   pendingLeaderDeclaration: CaseStatus.PENDING_FOR_LEADER,
  //   leaderNotResponded: CaseStatus.EXPIRED_AFTER_15_DAYS,
  //   leaderRejected: CaseStatus.REJECTED_BY_LEADER,
  // };

  return (
    <Row
      py={space[3]}
      justifyContent="center"
      alignItems="center"
      gap={space[2]}>
      <Typography.H8 color={colors.palette.fwdGreyDarkest}>
        {t('search.filteredBy')}
      </Typography.H8>
      <ScrollView
        horizontal
        style={{
          flex: 1,
        }}
        contentContainerStyle={{
          gap: space[1],
        }}>
        {isFilterStatLoading ? (
          <Box minHeight={30} justifyContent="center" alignItems="center">
            <Box h={space[5]} width={space[5]}>
              <LoadingIndicator size={space[5]} />
            </Box>
          </Box>
        ) : filterLeaderReviewStatus?.length === 0 ? (
          <Typography.H8 color={colors.palette.fwdGreyDarkest}>
            {'--'}
          </Typography.H8>
        ) : (
          filterLeaderReviewStatus.map(({ type, label }) => {
            // Part of "Show filter when data count is not 0" logic, disabled now
            // const currentCaseStatus =
            //   NBPolicyLeaderReviewStatusToCaseStatus[type];

            // const submittedCaseStatus =
            //   type === 'leaderApprovedToSubmit'
            //     ? CaseStatus.APP_SUBMITTED
            //     : null;

            // const isCaseInStatusFound =
            //   type === 'leaderApprovedToSubmit' && submittedCaseStatus !== null
            //     ? // * leaderApprovedToSubmit should include both leaderApprovedToSubmit and APP_SUBMITTED
            //       statusFilterCountMap?.[currentCaseStatus] ||
            //       statusFilterCountMap?.[submittedCaseStatus]
            //     : statusFilterCountMap?.[currentCaseStatus];
            // if (!isCaseInStatusFound) {
            //   return null;
            // }
            return (
              <Chip
                key={type}
                focus={filterBy.includes(type)}
                label={t(label)}
                onPress={() =>
                  filterBy.includes(type)
                    ? setFilterBy(setStatus =>
                        setStatus.filter(item => item !== type),
                      )
                    : setFilterBy(setStatus => [...setStatus, type])
                }
              />
            );
          })
        )}
      </ScrollView>
    </Row>
  );
}
