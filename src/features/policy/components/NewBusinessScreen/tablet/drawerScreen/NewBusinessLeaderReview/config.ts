import { NBPolicyLeaderReviewStatus } from 'types/policy';

export const demoLeaderReviewData = [
  {
    certNo: '13511835',
    certOwner: '<PERSON>',
    contribution: 123.55,
    agent<PERSON>ame: '<PERSON>',
    uwDecision: 'Approved',
    status: 'pendingLeaderDeclaration',
    submissionDate: '2024-01-21',
  },
  {
    certNo: '13511435',
    certOwner: '<PERSON>',
    contribution: 123.55,
    agent<PERSON>ame: '<PERSON>',
    uwDecision: 'Approved',
    status: 'pendingLeaderDeclaration',
    submissionDate: '2024-01-21',
  },
  {
    certNo: '13511235',
    certOwner: '<PERSON>',
    contribution: 123.55,
    agent<PERSON>ame: '<PERSON>',
    uwDecision: 'Approved',
    status: 'pendingLeaderDeclaration',
    submissionDate: '2024-01-21',
  },
  {
    certNo: '13511836',
    certOwner: '<PERSON>',
    contribution: 123.55,
    agent<PERSON>ame: '<PERSON>',
    uwDecision: 'Refer',
    status: 'leaderRejected',
    submissionDate: '2024-01-14',
  },
  {
    certNo: '13511837',
    certOwner: '<PERSON> <PERSON>u <PERSON>',
    contribution: 123.55,
    agentName: '<PERSON> Chan',
    uwDecision: 'Refer',
    status: 'leaderNotResponded',
    submissionDate: '2024-01-15',
  },
];

export const filterLeaderReviewStatus = [
  {
    type: 'pendingLeaderDeclaration',
    label: 'newBusiness.leaderReviewApplication.filter.pending',
  },
  {
    type: 'leaderRejected',
    label: 'newBusiness.leaderReviewApplication.filter.rejected',
  },
  {
    type: 'leaderApprovedToSubmit',
    label: 'newBusiness.leaderReviewApplication.filter.approved',
  },
  {
    type: 'leaderNotResponded',
    label: 'newBusiness.leaderReviewApplication.filter.leaderNotResponded',
  },
] satisfies Array<{
  type: NBPolicyLeaderReviewStatus;
  label:
    | 'newBusiness.leaderReviewApplication.filter.pending'
    | 'newBusiness.leaderReviewApplication.filter.rejected'
    | 'newBusiness.leaderReviewApplication.filter.approved'
    | 'newBusiness.leaderReviewApplication.filter.leaderNotResponded';
}>;
