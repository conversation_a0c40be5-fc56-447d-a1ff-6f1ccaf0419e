import React, { ComponentType, ReactNode } from 'react';
import { ExtraSmallLabel, FloatingButton } from 'cube-ui-components';
import { NewBusinessParamList } from 'types';
import styled from '@emotion/native';
import Animated, {
  SharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { Icon } from 'cube-ui-components/';
import { useTheme } from '@emotion/react';
import ResponsiveView from 'components/ResponsiveView';
import { NBStatus } from 'types/policy';
import { country } from 'utils/context';
import { useTranslation } from 'react-i18next';

type StatusType = NewBusinessParamList['NBList']['status'];

export function withAnimated(
  WrappedComponent: React.ComponentType<any>,
): ComponentType<any> {
  const displayName =
    WrappedComponent.displayName || WrappedComponent.name || 'Component';

  class WithAnimated extends React.Component {
    static displayName = `WithAnimated(${displayName})`;
    render(): React.ReactNode {
      return <WrappedComponent {...this.props} />;
    }
  }

  return Animated.createAnimatedComponent(WithAnimated);
}

const ICON_SIZE = 20; // sizes[5]

export default function PoliciesDrawer({
  expanded,
  scrollToTop,
  routeStatus,
  onChange,
}: {
  expanded: SharedValue<boolean>;
  scrollToTop: () => void;
  routeStatus: StatusType;
  onChange: (selectedStatus: NBStatus) => void;
}) {
  const { space, colors, animation, opacity } = useTheme();
  const { t } = useTranslation(['policy']);

  const handlePress = (status: StatusType) => {
    scrollToTop();
    onChange(status);
  };

  const AnimatedFloatingButton = withAnimated(FloatingButton);
  const AnimatedLabel = withAnimated(Label);

  const DRAWER_TABS_CONFIG: {
    label:
      | 'newBusiness.reviewSubmission'
      | 'newBusiness.pending'
      | 'newBusiness.issued';
    route: StatusType;
    icon: ReactNode;
  }[] = [
    {
      label: 'newBusiness.reviewSubmission',
      route: 'submission',
      icon: <Icon.DocumentCopy size={ICON_SIZE} fill={colors.background} />,
    },
    {
      label: 'newBusiness.pending',
      route: 'pending',
      icon: <Icon.Warning size={ICON_SIZE} fill={colors.background} />,
    },
    {
      label: 'newBusiness.issued',
      route: 'issued',
      icon: <Icon.TickCircle size={ICON_SIZE} fill={colors.background} />,
    },
  ];

  /**
   * Animation
   */
  const animatedStyle = useAnimatedStyle(() => {
    return {
      marginHorizontal: withTiming(expanded.value ? space[4] : space[2], {
        duration: animation.duration * 0.1,
      }),
    };
  });

  const animatedFloatingButtonStyle = useAnimatedStyle(() => ({
    width: withTiming(expanded.value ? space[10] : space[8], {
      duration: animation.searchBar.duration,
    }),
    height: withTiming(expanded.value ? space[10] : space[8], {
      duration: animation.searchBar.duration,
    }),
  }));

  const animatedLabelStyle = useAnimatedStyle(() => ({
    display: expanded.value ? 'flex' : 'none',
  }));

  /**
   * Check if the country is IB
   */
  const isIB = country === 'ib';

  return (
    <Container style={[animatedStyle]}>
      {DRAWER_TABS_CONFIG?.map(({ label, route, icon }, index) => {
        return (
          <FloatingButtonContainer key={index}>
            <AnimatedFloatingButton
              key={`floatingButton_${index}`}
              variant="primary"
              style={[
                animatedFloatingButtonStyle,
                {
                  backgroundColor:
                    routeStatus === route
                      ? colors.primary
                      : colors.palette.fwdOrange[50],
                },
              ]}
              elevation="none"
              onPress={() => handlePress(route)}>
              {icon}
            </AnimatedFloatingButton>
            <AnimatedLabel
              style={animatedLabelStyle}
              status={routeStatus === route}>
              {t(`policy:${label}`)}
            </AnimatedLabel>
          </FloatingButtonContainer>
        );
      })}
    </Container>
  );
}

export const Container = Animated.createAnimatedComponent(
  styled(ResponsiveView)(({ theme }) => ({
    paddingTop: theme.space[3],
    gap: theme.space[4],
  })),
);

export const FloatingButtonContainer = styled(ResponsiveView)(() => ({
  alignItems: 'center',
  maxWidth: 76,
}));

export const Label = styled(ExtraSmallLabel)<{ status: boolean }>(
  ({ theme, status }) => ({
    color: status ? theme.colors.primary : theme.colors.placeholder,
    marginTop: theme.space[1],
  }),
);
