import React, { Fragment, useEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import { SortDirectionKeys } from 'types/eRecruit';
import { Icon, XView } from 'cube-ui-components';
import { View } from 'react-native';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import {
  IconUp,
  TableTitleRow,
  TitleContainer,
  TitleSeparatorLine,
  TitleStyle,
} from './POSStyle';
import { useTranslation } from 'react-i18next';
import {
  DueTableConfigItem,
  FreelookTableConfigItem,
  ListHeaderItem,
  OverdueTableConfigItem,
} from './type';
import { PolicyKey } from 'utils/translation/i18next';
import {
  dueTableConfig,
  freelookTableConfig,
  overdueTableConfig,
} from './config';
import { country } from 'utils/context';

export default function POSTableTitleRow({
  sortOrder,
  setSortOrder,
  currentState,
}: {
  sortOrder: string;
  setSortOrder: (sortOrder: SortDirectionKeys) => void;
  currentState: string;
}) {
  const { t } = useTranslation('policy');
  const { colors } = useTheme();
  const [currentIcon, setCurrentIcon] = useState<React.ReactNode>(
    <Icon.ArrowDown size={sizes[4]} fill={colors.palette.white} />,
  );

  const handleIconPress = () => {
    const newSortOrder = sortOrder === 'oldest' ? 'newest' : 'oldest';
    const newIcon =
      sortOrder === 'oldest' ? (
        <Icon.ArrowUp size={sizes[4]} fill={colors.palette.white} />
      ) : (
        <Icon.ArrowDown size={sizes[4]} fill={colors.palette.white} />
      );
    setSortOrder(newSortOrder);
    setCurrentIcon(newIcon);
  };

  const TitleFragment = ({ currentState }: { currentState: string }) => {
    const [currentConfig, setCurrentConfig] = useState<ListHeaderItem>(
      dueTableConfig[country].certificateDue.listHeader,
    );

    useEffect(() => {
      if (currentState === 'due') {
        setCurrentConfig(dueTableConfig[country].certificateDue.listHeader);
      } else if (currentState === 'overdue') {
        setCurrentConfig(
          overdueTableConfig[country].certificateOverdue.listHeader,
        );
      } else if (currentState === 'freelook') {
        setCurrentConfig(
          freelookTableConfig[country].certificateFreelook.listHeader,
        );
      }
    }, [currentState]);

    return (
      <>
        {currentConfig.map((item, index) => (
          <Fragment key={'dueTableConfig' + index}>
            <View
              style={{
                width: item.width,
                alignItems: 'flex-start',
              }}>
              <TitleContainer>
                <TitleStyle fontWeight="bold">
                  {t(item?.name as PolicyKey)}
                </TitleStyle>

                {index === currentConfig.length - 1 && (
                  <IconUp onPress={handleIconPress}>{currentIcon}</IconUp>
                )}
              </TitleContainer>
            </View>
            {index < currentConfig.length - 1 && <TitleSeparatorLine />}
          </Fragment>
        ))}
      </>
    );
  };

  return (
    <View style={{ backgroundColor: colors.palette.fwdGrey[50] }}>
      <TableTitleRow>
        <XView>
          <TitleFragment currentState={currentState} />
        </XView>
      </TableTitleRow>
    </View>
  );
}
