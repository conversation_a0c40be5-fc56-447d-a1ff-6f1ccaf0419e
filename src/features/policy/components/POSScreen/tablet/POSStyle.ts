import styled from '@emotion/native';
import ResponsiveView from 'components/ResponsiveView';
import { H8, SmallLabel } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import Animated from 'react-native-reanimated';
import { StyledProps } from './type';

export const Container = Animated.createAnimatedComponent(
  styled(ResponsiveView)(({ theme }) => ({
    paddingHorizontal: theme.space[4],
    paddingTop: theme.space[4],
    gap: theme.space[3],
    backgroundColor: theme.colors.background,
  })),
);

export const Label = styled(SmallLabel)<StyledProps>(
  ({ theme, isFocused }) => ({
    color: isFocused ? theme.colors.primary : theme.colors.placeholder,
    marginTop: theme.space[1],
    alignItems: 'center',
  }),
);

export const ButtonStyle = styled.Pressable<StyledProps>(
  ({ theme, isFocused }) => ({
    backgroundColor: isFocused
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    color: isFocused ? theme.colors.primary : theme.colors.primaryVariant2,
    borderWidth: isFocused ? 2 : 0,
    width: 88,
    height: 92,
    borderRadius: theme.space[4],
    borderColor: isFocused ? theme.colors.primary : theme.colors.background,
    paddingVertical: theme.space[4],
    paddingHorizontal: isFocused ? theme.space[0] : theme.space[1],
    alignItems: 'center',
    justifyContent: 'center',
  }),
);

export const DrawerContainer = styled(ResponsiveView)(
  ({ theme: { sizes } }) => ({
    flexDirection: 'row',
    height: '100%',
  }),
);

export const TitleStyle = styled(H8)(({ theme: { sizes } }) => ({
  color: colors.white,
  padding: sizes[4],
}));

export const TableTitleRow = styled.View(({ theme }) => [
  {
    width: '100%',
    height: 50,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    backgroundColor: theme.colors.primary,
  },
]);

export const TitleSeparatorLine = styled.View(({ theme }) => ({
  width: 1,
  height: '50%',
  alignSelf: 'center',
  backgroundColor: theme.colors.background,
}));

export const SeparatorLine = styled.View(({ theme }) => ({
  width: 1,
  height: '50%',
  alignSelf: 'center',
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

export const RowSeparator = styled.TouchableOpacity(({ theme }) => [
  {
    width: '100%',
    height: 50,
    borderBottomWidth: 1,
    borderColor: theme.colors.palette.fwdGrey[100],
  },
]);

export const InfoText = styled(H8)(({ theme: { sizes } }) => ({
  color: colors.fwdDarkGreen[100],
  paddingHorizontal: sizes[4],
  paddingVertical: 13,
}));

export const OutstandingItemStyle = styled(H8)(({ theme: { sizes } }) => ({
  color: colors.alertRed,
  textAlign: 'center',
}));

export const InfoStyle = styled.View(({ theme: { sizes } }) => ({
  paddingVertical: sizes[3],
}));

export const IconUp = styled.TouchableOpacity(({ theme: { sizes } }) => ({}));

export const TitleContainer = styled.View(({ theme: { sizes } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingRight: sizes[4],
  width: '100%',
}));

export const Spacer = styled.View(({ height }: { height: number }) => ({
  height,
}));

export const SmallTitleStyle = styled(H8)(({ theme: { sizes, colors } }) => ({
  color: colors.palette.fwdGreyDarkest,
  paddingTop: sizes[2],
  paddingBottom: sizes[3],
}));

export const SmallTitleSearchPage = styled(H8)(
  ({ theme: { sizes, colors } }) => ({
    color: colors.palette.fwdGreyDarkest,
    paddingBottom: sizes[3],
  }),
);
