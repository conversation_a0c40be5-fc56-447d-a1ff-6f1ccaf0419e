import React, { useEffect, useRef } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { AppTopTabBarComponentRef } from 'components/AppTopTabBar';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { PoliciesTabParamList } from 'types/navigation';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@emotion/react';
import NewBusinessScreenTablet from 'features/policy/components/NewBusinessScreen/tablet/NewBusinessScreen';
import POSScreenTablet from 'features/policy/components/POSScreen/tablet/POSScreen';
import AppTopTabBarV2 from 'components/AppTopTabBarV2';
import { Column } from 'cube-ui-components';

const Tab = createMaterialTopTabNavigator<PoliciesTabParamList>();

export default function PoliciesScreenTablet() {
  const { t } = useTranslation(['navigation']);
  const route = useRoute<RouteProp<PoliciesTabParamList>>();
  const TabBarRef = useRef<AppTopTabBarComponentRef>();

  const { top } = useSafeAreaInsets();
  const { colors } = useTheme();

  useEffect(() => {
    if (route.name && TabBarRef.current) {
      TabBarRef.current.onChangeTab(route.name);
    }
  }, [route.name]);

  return (
    <Column flex={1} paddingTop={top} backgroundColor={colors.background}>
      <Tab.Navigator
        tabBar={props => <AppTopTabBarV2 {...props} />}
        initialRouteName={route.name}
        screenOptions={{ swipeEnabled: false }}>
        <Tab.Screen
          name="NewBusiness"
          children={NewBusinessScreenTablet}
          options={{ tabBarLabel: t(`navigation:tabScreen.NewBusiness`) }}
        />
        <Tab.Screen
          name="POS"
          children={POSScreenTablet}
          options={{ tabBarLabel: t(`navigation:tabScreen.POS`) }}
        />
      </Tab.Navigator>
    </Column>
  );
}
