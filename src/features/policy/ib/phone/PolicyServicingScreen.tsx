import { useTheme } from '@emotion/react';
import { Icon, Column, H7, Row, Chip, Box } from 'cube-ui-components';
import {
  CompositeNavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import useBoundStore from 'hooks/useBoundStore';
import React, {
  Fragment,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  TouchableOpacity,
  ScrollView as ScrollViewIOS,
  View,
} from 'react-native';
import PoliciesScreenContext from 'screens/PoliciesScreen/PoliciesScreenContext';
import { PosPramList, RootStackParamList } from 'types/navigation';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useGetPosPolicyByAgentId } from 'hooks/useGetPolicyList';
import PolicySearch from 'features/policy/components/PolicySearch';
import useSearchPolicies from 'features/policy/hooks/useSearchPolicies';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useBottomBar } from 'hooks/useBottomBar';
import { FlashList } from '@shopify/flash-list';
import { Placeholder } from './components/PlaceHolder';
import InomataSVG from 'features/policy/assets/InomataSVG';
import { PosPolicyDetail, TabConfig } from 'types/policy';
import {
  Line,
  TabButton,
  TabContainer,
} from 'features/policy/components/phone/TabButton';
import { ScrollView as ScrollViewAOS } from 'react-native-gesture-handler';
import { SortDirectionKeys } from 'features/savedProposals/types';
import { POSItem } from 'features/policy/components/POSScreen/phone/POSItem';
import { sortDateHandler } from 'utils/helper/dateUtil';
import CountAndSortHeader from './components/CountAndSortHeader';
import { dateFormatUtil } from 'utils/helper/formatUtil';
// import getPOSDummyData from './POSDummyData';

const ICON_SIZE = 24; // sizes[6]
const PADDING_X = 16; // space[4] - Padding horizontal of the main container

type StatusType = {
  type: 'dues' | 'overdues' | 'freelooks';
  value: string;
  label: string;
};

const POS_CHIP_FILTER_CONFIG: StatusType[] = [
  {
    type: 'dues',
    value: 'dues',
    label: 'Due',
  },
  {
    type: 'overdues',
    value: 'overdues',
    label: 'Overdue',
  },
  {
    type: 'freelooks',
    value: 'freelooks',
    label: 'Freelook Cancellation',
  },
] as const;

export default function PolicyServicingScreen() {
  const { t } = useTranslation(['policy']);
  const { colors, space, animation, sizes } = useTheme();
  const { showBottomBar, hideBottomBar } = useBottomBar();
  const navigation = useNavigation<
    CompositeNavigationProp<
      NativeStackNavigationProp<RootStackParamList, 'PoliciesNewBusiness'>, // refer to IbStackNavigator
      NativeStackNavigationProp<PosPramList, 'POSList'> // refer to PoliciesNewBusinessNavigator
    >
  >();

  const ScrollView = Platform.OS === 'ios' ? ScrollViewIOS : ScrollViewAOS;

  const lastScrollY = useSharedValue(0);
  const { viewingAgentCode } = useContext(PoliciesScreenContext);
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);

  /**
   * Filter
   */
  // CUBEFIB-3171: keith - always pick the chip with data != null, and if all chips have data, then start with the first chip from left to right [Due, Overdue, Freelook cancellation]. Otherwise, the first chip will be selected by default
  // const [filterBy, setFilterBy] = useState<string | null>(null);
  // const isFiltered = Boolean(filterBy);
  const [activeChip, setActiveChip] =
    useState<(typeof POS_CHIP_FILTER_CONFIG)[number]['type']>();

  /**
   * Sort
   */
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  /**
   * Data
   */
  const { data, isLoading } = useGetPosPolicyByAgentId(loginAgentCode || '');
  // const data = getPOSDummyData();
  // const isLoading = false;

  // Add type for each item
  const posDuesData = data?.dues?.map(item => {
    return { ...item, type: 'dues' };
  });

  const posOverduesData = data?.overdues?.map(item => {
    return { ...item, type: 'overdues' };
  });

  const posFreelooksData = data?.freeLooks?.map(item => {
    return { ...item, type: 'freelooks' };
  });

  const allPolicyServicingData = useMemo(() => {
    return [
      ...(posDuesData ?? []),
      ...(posOverduesData ?? []),
      ...(posFreelooksData ?? []),
    ];
  }, [posDuesData, posFreelooksData, posOverduesData]);

  /**
   * Search results
   */
  const [showSearch, setShowSearch] = useState(false);

  const { searched, setSearched, query, setQuery, result, handleSearch } =
    useSearchPolicies(allPolicyServicingData);

  const dueResults = result?.filter(item => item?.type === 'dues');
  const overdueResults = result?.filter(item => item?.type === 'overdues');
  const freeLookResults = result?.filter(item => item?.type === 'freelooks');

  const handleFilterChipByDefault = () => {
    const resultMap: {
      [key in StatusType['type']]: PosPolicyDetail[] | undefined;
    } = {
      dues: dueResults,
      overdues: overdueResults,
      freelooks: freeLookResults,
    };
    const defaultFilter = POS_CHIP_FILTER_CONFIG.find(
      item => resultMap[item.type]?.length ?? 0 > 0,
    );
    setActiveChip(defaultFilter?.type ?? POS_CHIP_FILTER_CONFIG[0].type);
    setOrder('newest');
  };

  useEffect(() => {
    handleFilterChipByDefault();
  }, [result]);

  const handleShowSearch = () => {
    setShowSearch(prep => !prep);
    setQuery('');
    setSearched(false);
    showBottomBar();
  };

  const handleSetQuery = (q: string) => setQuery(q);

  const handleResetSearch = () => setSearched(false);

  const animatedSearchStyle = useAnimatedStyle(() => ({
    width: withTiming(showSearch ? '85%' : 0, {
      duration: animation.duration,
    }),
  }));

  /**
   *  Filter search results
   */
  const filteredPOSPolicyList = useMemo(() => {
    if (!result) return [];

    const dateField: keyof PosPolicyDetail =
      activeChip === 'dues' || activeChip === 'overdues'
        ? 'dueDate'
        : activeChip === 'freelooks'
        ? 'freeLookDate'
        : 'dueDate';

    const sortedList = [...result].sort((a, b) =>
      sortDateHandler({
        aDate: a[dateField],
        bDate: b[dateField],
        sortOrder: order,
      }),
    );

    return (filteredList = activeChip
      ? sortedList?.filter(item => item?.type === activeChip)
      : sortedList);
  }, [result, activeChip, order]);

  /**
   * Tabs config
   */
  const STATUS_TAB_CONFIG = [
    {
      status: 'due',
      label: 'pos.due',
      icon: <Icon.Alarm size={ICON_SIZE} fill={colors.background} />,
      count: data?.dues?.length ?? 0,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isLoading,
      onPress: () => {
        navigation.push('PoliciesPOS', {
          screen: 'POSList',
          params: {
            status: 'due',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
    {
      status: 'overdue',
      label: 'pos.overdue',
      icon: <Icon.AlarmAlert size={ICON_SIZE} fill={colors.background} />,
      count: data?.overdues?.length ?? 0,
      countIcon: (
        <Icon.Warning size={sizes[5]} fill={colors.palette.alertRed} />
      ),
      countColor: colors.palette.alertRed,
      isLoading: isLoading,
      onPress: () => {
        navigation.push('PoliciesPOS', {
          screen: 'POSList',
          params: {
            status: 'overdue',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
    {
      status: 'freelookCancellation',
      label: 'pos.freelookCancellation',
      icon: <InomataSVG size={ICON_SIZE} />,
      count: data?.freeLooks?.length ?? 0,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isLoading,
      onPress: () => {
        navigation.push('PoliciesPOS', {
          screen: 'POSList',
          params: {
            status: 'freelook',
            viewingAgentCode: undefined, // * Update if necessary
          },
        });
      },
    },
  ] satisfies TabConfig[];

  /**
   * Reset search and filter when screen is unfocused
   */
  useFocusEffect(
    useCallback(() => {
      return () => {
        setQuery('');
        setSearched(false);
        setShowSearch(false);
      };
    }, []),
  );

  const commonBgColor = colors.palette.fwdGrey[50];

  const isFiltered = false;

  if (showSearch) {
    return (
      <Column bgColor={commonBgColor} flex={1}>
        <Column px={PADDING_X}>
          <PolicySearch
            show={showSearch}
            handleShowSearch={handleShowSearch}
            animated={animatedSearchStyle}
            query={query}
            searched={searched}
            handleSetQuery={handleSetQuery}
            handleSetSearch={handleSearch}
            handleResetSearch={handleResetSearch}
            isCustomPlaceholderHints={true}
          />
        </Column>

        {searched && (
          <Box flex={1}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{
                marginHorizontal: PADDING_X,
                marginTop: space[2],
                maxHeight: sizes[10],
              }}
              contentContainerStyle={{
                gap: space[1],
                alignItems: 'center',
              }}>
              {POS_CHIP_FILTER_CONFIG.map(({ type, label }) => (
                <Chip
                  key={type}
                  label={label}
                  focus={activeChip === type}
                  onPress={() => {
                    setActiveChip(prevValue => {
                      return prevValue === type ? undefined : type;
                    });
                    setOrder('newest');
                  }}
                />
              ))}
            </ScrollView>

            <Row px={PADDING_X} py={space[2]}>
              <CountAndSortHeader
                title="Search result"
                listDataCount={filteredPOSPolicyList?.length ?? 0}
                order={order}
                onPress={() =>
                  setOrder(order === 'newest' ? 'oldest' : 'newest')
                }
                isFiltered={isFiltered}
              />
            </Row>

            <View style={{ flex: 1 }}>
              <FlashList
                data={filteredPOSPolicyList}
                keyExtractor={(item, index) => `${item?.policyNo}-${index}`}
                contentContainerStyle={{
                  paddingHorizontal: PADDING_X,
                  paddingBottom: 127, // List item estimatedItemSize
                }}
                renderItem={({ item }) => {
                  if (item.type === 'dues') {
                    return (
                      <POSItem.DueItem
                        type="default"
                        item={item as unknown as PosPolicyDetail}
                        disabled={false}
                        onPress={() => {
                          navigation.push('PoliciesPOS', {
                            screen: 'POSDetail',
                            params: {
                              type: 'policy',
                              policyId: item?.policyNo,
                              status: 'due',
                              policyInfo: {
                                dueDate: item?.dueDate
                                  ? dateFormatUtil(new Date(item.dueDate))
                                  : '--',
                              },
                            },
                          });
                        }}
                      />
                    );
                  }
                  if (item.type === 'overdues') {
                    return (
                      <POSItem.OverdueItem
                        {...item}
                        type="default"
                        item={item as unknown as PosPolicyDetail}
                        disabled={false}
                        onPress={() => {
                          navigation.push('PoliciesPOS', {
                            screen: 'POSDetail',
                            params: {
                              type: 'policy',
                              policyId: item?.policyNo,
                              status: 'overdue',
                              policyInfo: {
                                dueDate: item?.dueDate
                                  ? dateFormatUtil(new Date(item.dueDate))
                                  : '--',
                              },
                            },
                          });
                        }}
                      />
                    );
                  }
                  if (item.type === 'freelooks') {
                    return (
                      <POSItem.FreeLookItem
                        {...item}
                        type="default"
                        item={item as unknown as PosPolicyDetail}
                        disabled={false}
                        onPress={() => {
                          navigation.push('PoliciesPOS', {
                            screen: 'POSDetail',
                            params: {
                              type: 'policy',
                              policyId: item?.policyNo,
                              status: 'freelook',
                              policyInfo: {
                                issueDate: item?.freeLookDate
                                  ? dateFormatUtil(new Date(item.freeLookDate))
                                  : '--',
                              },
                            },
                          });
                        }}
                      />
                    );
                  }
                  return <></>;
                }}
                estimatedItemSize={127}
                ListEmptyComponent={
                  <>
                    {isFiltered ? (
                      <Placeholder.FilteredEmptyRecord />
                    ) : (
                      <Placeholder.SearchEmptyRecord />
                    )}
                  </>
                }
                onScrollBeginDrag={e => {
                  const startOffset = e.nativeEvent.contentOffset.y;
                  lastScrollY.value = startOffset;
                }}
                onScrollEndDrag={e => {
                  const endOffset = e.nativeEvent.contentOffset.y;
                  if (endOffset === 0) {
                    showBottomBar();
                  } else {
                    if (endOffset > lastScrollY.value) {
                      hideBottomBar();
                    } else {
                      showBottomBar();
                    }
                  }
                  lastScrollY.value = endOffset;
                }}
                bounces={false}
                ItemSeparatorComponent={() => <Row paddingBottom={space[2]} />}
              />
            </View>
          </Box>
        )}
      </Column>
    );
  }

  return (
    <Column px={space[4]} bgColor={commonBgColor} flex={1}>
      <Row py={space[5]} alignItems="center" justifyContent="space-between">
        <H7 fontWeight="bold" children={t('policy:pos.status')} />
        <Row alignItems="center" gap={space[2]}>
          <H7 fontWeight="bold" children={t('policy:case')} />
          <TouchableOpacity onPress={handleShowSearch}>
            <Icon.Search size={ICON_SIZE} fill={colors.onBackground} />
          </TouchableOpacity>
        </Row>
      </Row>

      <TabContainer>
        {STATUS_TAB_CONFIG?.map((item, idx) => (
          <Fragment key={item?.status}>
            <TabButton {...item} />
            {idx == STATUS_TAB_CONFIG.length - 1 ? null : <Line />}
          </Fragment>
        ))}
      </TabContainer>
    </Column>
  );
}
