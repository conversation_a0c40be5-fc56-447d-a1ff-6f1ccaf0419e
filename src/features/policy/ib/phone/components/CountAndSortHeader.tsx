import { useTheme } from '@emotion/react';
import { Body, Icon, Row, Typography } from 'cube-ui-components';
import { TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

/**
 * For data count and sorting press display
 *
 * Sample useState of using this component :
 * const [order, setOrder] = useState<'newest' | 'oldest'>('newest');
 */
export default function CountAndSortHeader({
  listDataCount,
  order,
  onPress,
  isFiltered,
  title,
}: {
  listDataCount: number;
  order: 'newest' | 'oldest';
  onPress: () => void;
  isFiltered?: boolean;
  title?: string;
}) {
  const { t } = useTranslation('policy');
  const { colors, space, sizes } = useTheme();

  const isEmptyList = listDataCount === 0;
  const sortButtonColor = colors.palette.fwdAlternativeOrange[100];
  return (
    <Row alignItems="center" gap={space[2]}>
      <Body
        children={
          isFiltered
            ? `Filtered result (${listDataCount ?? 0})`
            : `${title ? title : 'Total cases'} (${listDataCount ?? 0})`
        }
        color={colors.palette.fwdGreyDarkest}
      />
      <TouchableOpacity
        disabled={isEmptyList}
        onPress={onPress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',

          opacity: isEmptyList ? 0.5 : 1,
        }}>
        <Typography.Label
          fontWeight="bold"
          color={sortButtonColor}
          children={order === 'newest' ? t('newest') : t('oldest')}
        />
        {order === 'newest' ? (
          <Icon.ArrowDown size={sizes[4]} fill={sortButtonColor} />
        ) : (
          <Icon.ArrowUp size={sizes[4]} fill={sortButtonColor} />
        )}
      </TouchableOpacity>
    </Row>
  );
}
