import { useTheme } from '@emotion/react';
import {
  CompositeNavigationProp,
  CompositeScreenProps,
  useNavigation,
} from '@react-navigation/native';
import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';
import { FlashList } from '@shopify/flash-list';
import CoolingOffIcon from 'components/IconCoolingOff';
import { Body, Column, H7, Icon, Row } from 'cube-ui-components';
import { NoResults } from 'features/lead/components/NoResult';
import { POSItem } from 'features/policy/components/POSScreen/phone/POSItem';
import PolicySearch from 'features/policy/components/PolicySearch';
import {
  Line,
  TabButton,
  TabContainer,
} from 'features/policy/components/phone/TabButton';
import useSearchPolicies from 'features/policy/hooks/useSearchPolicies';
import { useBottomBar } from 'hooks/useBottomBar';
import useBoundStore from 'hooks/useBoundStore';
import { useGetPosPolicyByAgentIdPH } from 'hooks/useGetPolicyList';
import { useGetTeamPosPolicyByAgentId } from 'hooks/useGetTeam';
import debounce from 'lodash/debounce';
import React, {
  Fragment,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import PoliciesScreenContext from 'screens/PoliciesScreen/PoliciesScreenContext';
import { PosPramList, RootStackParamList } from 'types/navigation';
import {
  AgentPosPolicyPH,
  FilterOptionSet,
  POSStatus,
  STATUS_DUE,
  STATUS_FREELOOK,
  STATUS_OVERDUE,
  TabConfig,
} from 'types/policy';

export type PosProps = NativeStackScreenProps<
  RootStackParamList,
  'PoliciesPOS'
>;

export type POSHomeProps = CompositeScreenProps<
  NativeStackScreenProps<RootStackParamList, 'PoliciesPOS'>,
  NativeStackScreenProps<PosPramList>
>;

type POSNavigationCompositeProps = CompositeNavigationProp<
  NativeStackNavigationProp<RootStackParamList, 'PoliciesPOS', undefined>,
  NativeStackNavigationProp<PosPramList, keyof PosPramList, undefined>
>;

const ICON_SIZE = 24; // size[6]
const PADDING_X = 16; // space[4] - Padding horizontal of the main container

export default function POSScreen() {
  const { t } = useTranslation(['policy']);
  const { colors, space, sizes, animation } = useTheme();
  const navigation = useNavigation<POSNavigationCompositeProps>();
  const { showBottomBar, hideBottomBar } = useBottomBar();

  const { viewingAgentCode } = useContext(PoliciesScreenContext);
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);

  const lastScrollY = useSharedValue(0);
  const [showSearch, setShowSearch] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterOption, setFilterOption] = useState<{
    filtered: boolean;
    options: FilterOptionSet;
  }>({
    filtered: false,
    options: {
      due: false,
      overdue: false,
      freelook: false,
    },
  });

  /**
   * Data
   */
  const { data: teamPOSPolicies, isLoading: isTeamPOSPoliciesLoading } =
    useGetTeamPosPolicyByAgentId(viewingAgentCode ?? '');
  const { data: agentPOSPolicies, isLoading: isAgentPOSPoliciesLoading } =
    useGetPosPolicyByAgentIdPH(loginAgentCode || '');

  const posPolicyData = viewingAgentCode ? teamPOSPolicies : agentPOSPolicies;

  const isLoading = viewingAgentCode
    ? isTeamPOSPoliciesLoading
    : isAgentPOSPoliciesLoading;

  const due = useMemo(
    () =>
      posPolicyData && posPolicyData.length > 0
        ? posPolicyData?.filter(data => data.status === STATUS_DUE)
        : [],
    [posPolicyData],
  );
  const overdue = useMemo(
    () =>
      posPolicyData && posPolicyData.length > 0
        ? posPolicyData?.filter(data => data.status === STATUS_OVERDUE)
        : [],
    [posPolicyData],
  );
  const freelook = useMemo(
    () =>
      posPolicyData && posPolicyData.length > 0
        ? posPolicyData?.filter(data => data.status === STATUS_FREELOOK)
        : [],
    [posPolicyData],
  );
  const allPOS = useMemo(
    () => [...(due || []), ...(overdue || []), ...(freelook || [])],
    [posPolicyData],
  );

  /**
   * Searching
   */
  const { searched, setSearched, query, setQuery, result, handleSearch } =
    useSearchPolicies(allPOS ?? []);

  const handleShowSearch = () => {
    setShowSearch(prep => !prep);
    setQuery('');
    setSearched(false);
    handleClearFilter();
    showBottomBar();
  };

  const handleSetQuery = (q: string) => setQuery(q);

  const handleResetSearch = () => setSearched(false);

  const animatedSearchStyle = useAnimatedStyle(() => ({
    width: withTiming(showSearch ? '85%' : 0, {
      duration: animation.duration,
    }),
  }));

  /**
   * Filtering
   */
  const handleClearFilter = () => {
    setFilterOption({
      filtered: false,
      options: { due: false, overdue: false, freelook: false },
    });
  };

  const handleSetFilter = (option: FilterOptionSet) => {
    setFilterOption({
      filtered: true,
      options: option,
    });
  };

  const handleResetFilteredState = () => {
    setFilterOption({
      ...filterOption,
      filtered: false,
    });
  };

  const handleCloseFilter = () => setFilterVisible(false);

  const handleShowFilter = () => setFilterVisible(true);

  const filterProps = {
    handleClearFilter,
    handleSetFilter,
    filterOption,
    handleResetFilteredState,
    handleShowFilter,
    handleCloseFilter,
    filterVisible,
  };

  const filteredResult = useMemo(() => {
    const filtered = result?.filter(
      item =>
        (filterOption.options.due && item.status === STATUS_DUE) ||
        (filterOption.options.overdue && item.status === STATUS_OVERDUE) ||
        (filterOption.options.freelook && item.status === STATUS_FREELOOK),
    );
    return filtered;
  }, [filterOption.options]);

  /**
   * Unmount search on blur
   */
  useEffect(() => {
    const unsubscribe = navigation.addListener('blur', () => {
      setShowSearch(false);
      setQuery('');
      setSearched(false);
    });
    return unsubscribe;
  }, [navigation]);

  /**
   * Tabs config
   */
  const STATUS_TAB_CONFIG = [
    {
      status: 'due',
      label: 'pos.due',
      icon: <Icon.Alarm size={ICON_SIZE} fill={colors.background} />,
      count: due?.length ?? 0,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isLoading,
      onPress: debounce(
        () =>
          navigation.push('PoliciesPOS', {
            screen: 'POSList',
            params: {
              status: 'due',
              viewingAgentCode: viewingAgentCode,
            },
          }),
        300,
      ),
    },
    {
      status: 'overdue',
      label: 'pos.overdue',
      icon: <Icon.AlarmAlert size={ICON_SIZE} fill={colors.background} />,
      count: overdue?.length ?? 0,
      countIcon: (
        <Icon.Warning size={ICON_SIZE} fill={colors.palette.alertRed} />
      ),
      countColor: colors.palette.alertRed,
      isLoading: isLoading,
      onPress: debounce(
        () =>
          navigation.push('PoliciesPOS', {
            screen: 'POSList',
            params: {
              status: 'overdue',
              viewingAgentCode: viewingAgentCode,
            },
          }),
        300,
      ),
    },
    {
      status: 'freelook',
      label: 'pos.freelookCancellation',
      icon: <CoolingOffIcon size={ICON_SIZE} fill={colors.background} />,
      count: freelook?.length ?? 0,
      countIcon: undefined,
      countColor: undefined,
      isLoading: isLoading,
      onPress: debounce(
        () =>
          navigation.push('PoliciesPOS', {
            screen: 'POSList',
            params: {
              status: 'freelook',
              viewingAgentCode: viewingAgentCode,
            },
          }),
        300,
      ),
    },
  ] satisfies TabConfig[];

  if (showSearch) {
    return (
      <>
        <Column px={PADDING_X}>
          <PolicySearch
            show={showSearch}
            handleShowSearch={handleShowSearch}
            animated={animatedSearchStyle}
            query={query}
            searched={searched}
            handleSetQuery={handleSetQuery}
            handleSetSearch={handleSearch}
            handleResetSearch={handleResetSearch}
            filterProps={filterProps}
          />
        </Column>

        {searched && (
          <Column width={'100%'} height={'100%'}>
            <Body
              children={t('policy:searchResult', {
                count: filterOption?.filtered
                  ? filteredResult?.length
                  : result?.length,
              })}
              color={colors.placeholder}
              style={{ paddingHorizontal: PADDING_X, paddingBottom: space[2] }}
            />
            <FlashList
              contentContainerStyle={{
                paddingHorizontal: PADDING_X,
                paddingBottom: sizes[50],
              }}
              renderItem={({ item }) => <ListItem item={item} />}
              data={filterOption.filtered ? filteredResult : result && result}
              keyExtractor={({ policyNumber, policyOwner }, i) =>
                `policy_pos_${policyNumber}_${policyOwner}_${i}`
              }
              estimatedItemSize={118}
              ListEmptyComponent={<NoResults />}
              getItemType={item => item?.status}
              onScrollBeginDrag={e => {
                const startOffset = e.nativeEvent.contentOffset.y;
                lastScrollY.value = startOffset;
              }}
              onScrollEndDrag={e => {
                const endOffset = e.nativeEvent.contentOffset.y;
                if (endOffset === 0) {
                  showBottomBar();
                } else {
                  if (endOffset > lastScrollY.value) {
                    hideBottomBar();
                  } else {
                    showBottomBar();
                  }
                }
                lastScrollY.value = endOffset;
              }}
              ItemSeparatorComponent={() => <Row paddingBottom={space[3]} />}
            />
          </Column>
        )}
      </>
    );
  }

  return (
    <Column px={PADDING_X}>
      <Row py={space[5]}>
        <Row flex={1} alignItems="center" justifyContent="space-between">
          <H7 fontWeight="bold">{t('policy:pos.status')}</H7>
          <Row gap={space[2]} alignItems="center">
            <H7 fontWeight="bold">{t('policy:case')}</H7>
            <TouchableOpacity onPress={handleShowSearch}>
              <Icon.Search size={ICON_SIZE} fill={colors.onBackground} />
            </TouchableOpacity>
          </Row>
        </Row>
      </Row>

      <TabContainer>
        {STATUS_TAB_CONFIG?.map((item, idx) => (
          <Fragment key={item?.status}>
            <TabButton {...item} />
            {idx == STATUS_TAB_CONFIG.length - 1 ? null : <Line />}
          </Fragment>
        ))}
      </TabContainer>
    </Column>
  );
}

function ListItem({ item }: { item: AgentPosPolicyPH }) {
  const navigation = useNavigation<POSNavigationCompositeProps>();

  let status: POSStatus;

  switch (item?.status) {
    case STATUS_DUE:
      status = 'due';
      break;
    case STATUS_OVERDUE:
      status = 'overdue';
      break;
    case STATUS_FREELOOK:
      status = 'freelook';
      break;
    default:
      break;
  }

  const handlePress = debounce(() => {
    navigation.push('PoliciesPOS', {
      screen: 'POSDetail',
      params: {
        type: 'policy',
        policyId: item?.policyNumber,
        status: status,
      },
    });
  }, 300);

  if (item?.status === STATUS_DUE) {
    return (
      <POSItem.DueItem
        type="ph"
        item={item}
        onPress={handlePress}
        isSearchItem
      />
    );
  }
  if (item?.status === STATUS_OVERDUE) {
    return (
      <POSItem.OverdueItem
        type="ph"
        item={item}
        onPress={handlePress}
        isSearchItem
      />
    );
  }
  if (item?.status === STATUS_FREELOOK) {
    return (
      <POSItem.FreeLookItem
        type="ph"
        item={item}
        onPress={handlePress}
        isSearchItem
      />
    );
  }
  return <></>;
}
