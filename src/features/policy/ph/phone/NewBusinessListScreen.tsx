import {
  NativeStackNavigationProp,
  NativeStackScreenProps,
} from '@react-navigation/native-stack';
import { Column, LoadingIndicator, Row, Typography } from 'cube-ui-components';
import React, { useMemo, useRef, useState } from 'react';
import { Dimensions } from 'react-native';
import {
  AgentNewBusinessPolicyPH,
  NBStatus,
  PolicyStatus,
  STATUS_ISSUED,
  STATUS_PENDING,
  STATUS_SUBMISSION,
} from 'types/policy';
import PoliciesDrawer from '../../components/NewBusinessScreen/phone/NBListDrawer';
import { useTheme } from '@emotion/react';
import { compareAsc, parseISO } from 'date-fns';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useHeaderHeight } from '@react-navigation/elements';
import NBList from 'features/policy/ph/phone/components/NBList';
import { FlashList } from '@shopify/flash-list';
import { useTranslation } from 'react-i18next';
import TopLabel from 'features/policy/components/TopLabel';
import styled from '@emotion/native';
import ResponsiveText from 'components/ResponsiveTypography';
import { NewBusinessParamList } from 'types/navigation';
import { useGetNewBusinessPolicyByAgentIdPH } from 'hooks/useGetPolicyList';
import useBoundStore from 'hooks/useBoundStore';
import { useGetTeamNewBusinessPolicyByAgentId } from 'hooks/useGetTeam';
import { setAllFalse } from 'features/lead/utils';
import PendingFilterPanel, {
  OptionType,
} from '../../components/NewBusinessScreen/PendingFilterPanel';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';

export type NewBusinessProps = NativeStackScreenProps<
  NewBusinessParamList,
  'NBList'
>;

type ReturnTranslationKeys =
  | 'submissionStaticText'
  | 'pendingStaticText'
  | 'issuedStaticText'
  | 'dueStaticText'
  | 'overdueStaticText'
  | 'freelookStaticText';

export function statusToTranslationKeysUtil(
  status: PolicyStatus,
): ReturnTranslationKeys | undefined {
  switch (status) {
    case 'submission':
      return 'submissionStaticText';
    case 'pending':
      return 'pendingStaticText';
    case 'issued':
      return 'issuedStaticText';
    case 'due':
      return 'dueStaticText';
    case 'overdue':
      return 'overdueStaticText';
    case 'freelook':
      return 'freelookStaticText';
  }
}

export default function NewBusinessListScreen() {
  const route = useRoute<RouteProp<NewBusinessParamList, 'NBList'>>();
  const navigation =
    useNavigation<
      NativeStackNavigationProp<NewBusinessParamList, 'NBList', undefined>
    >();
  const { status, viewingAgentCode } = route.params;

  const { colors, space, animation } = useTheme();
  const { t } = useTranslation(['policy']);

  const loginAgentCode = useBoundStore()?.auth?.agentCode;

  const [selectedViewingStatus, setSelectedViewingStatus] =
    useState<NBStatus>(status);
  const [sortByNewest, setSortByNewest] = useState(true);
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterOption, setFilterOption] = useState({
    filtered: false,
    options: {
      outstandingItems: false,
      sentToReview: false,
    },
  });
  const { data: teamNBPolicies, isLoading: isTeamNBPoliciesLoading } =
    useGetTeamNewBusinessPolicyByAgentId(viewingAgentCode ?? '');
  const { data: agentNBPolicies, isLoading: isAgentNBPoliciesLoading } =
    useGetNewBusinessPolicyByAgentIdPH(loginAgentCode || '');

  const data = viewingAgentCode ? teamNBPolicies : agentNBPolicies;
  const isLoading = viewingAgentCode
    ? isTeamNBPoliciesLoading
    : isAgentNBPoliciesLoading;

  const businessList = useMemo(() => {
    switch (selectedViewingStatus) {
      case 'issued':
        return data && data?.length > 0
          ? data?.filter(data => data?.status === STATUS_ISSUED)
          : [];
      case 'pending': {
        const pendingData =
          data && data?.length > 0
            ? data?.filter(data => data?.status === STATUS_PENDING)
            : [];
        if (filterOption?.filtered === true) {
          const filteredData =
            pendingData && pendingData?.length > 0
              ? pendingData.filter(
                  item =>
                    (filterOption?.options?.outstandingItems &&
                      item?.statusCode === 'NP') ||
                    (filterOption.options.sentToReview &&
                      item?.statusCode !== 'NP'),
                )
              : [];
          return filteredData;
        }
        return pendingData;
      }
      case 'submission':
        return data && data.length > 0
          ? data?.filter(data => data.status === STATUS_SUBMISSION)
          : [];
      default:
        return [];
    }
  }, [selectedViewingStatus, isLoading, filterOption]);

  const dataSort = useMemo(() => {
    const businessListSlice = businessList?.slice();
    if (businessListSlice) {
      return businessListSlice.sort(
        (a: AgentNewBusinessPolicyPH, b: AgentNewBusinessPolicyPH) => {
          const dateA =
            a?.statusChangeDate != null
              ? parseISO(String(a?.statusChangeDate))
              : a?.submitDate != null
              ? parseISO(String(a?.submitDate))
              : a?.issueDate != null
              ? parseISO(String(a?.issueDate))
              : 0;
          const dateB =
            b?.statusChangeDate != null
              ? parseISO(String(b?.statusChangeDate))
              : b?.submitDate != null
              ? parseISO(String(b?.submitDate))
              : b?.issueDate != null
              ? parseISO(String(b?.issueDate))
              : 0;

          return compareAsc(dateA, dateB) * (sortByNewest ? -1 : 1);
        },
      );
    }
  }, [businessList, sortByNewest]);

  const onPressSort = () => setSortByNewest(prev => !prev);

  const handleSetFilter = (option: Record<OptionType, boolean>) => {
    setFilterOption({
      filtered: true,
      options: option,
    });
  };

  const handleResetFilteredState = () => {
    setFilterOption({
      ...filterOption,
      filtered: false,
    });
  };

  const handleClearFilter = () => {
    const resetOptions = { ...filterOption.options };
    setAllFalse(resetOptions);
    setFilterOption({
      filtered: false,
      options: resetOptions,
    });
  };

  const handleShowFilter = () => setFilterVisible(true);
  const handleCloseFilter = () => setFilterVisible(false);

  const filterProps = {
    filterOption,
    handleSetFilter,
    handleClearFilter,
    handleResetFilteredState,
    filterVisible,
    handleShowFilter,
    handleCloseFilter,
  };

  const handleChangeStatus = (status: NBStatus) => {
    setSelectedViewingStatus(status);
    setSortByNewest(true);
  };

  /**
   * Animation
   */
  const scrollY = useSharedValue(0);
  const expanded = useSharedValue(true);
  const scrollRef = useRef<FlashList<AgentNewBusinessPolicyPH> | null>(null);
  const headerHeight = useHeaderHeight();
  const windowHeight = Dimensions.get('window').height;
  const animatedStyle = useAnimatedStyle(() => {
    if (expanded.value === true) {
      return {
        maxHeight: withTiming(windowHeight, {
          duration: animation.duration * 0.2,
        }),

        top: withTiming(0, {
          duration: animation.duration * 0.2,
        }),
        opacity: withTiming(1, {
          duration: animation.duration * 1.2,
        }),
      };
    } else {
      return {
        maxHeight: withTiming(0, {
          duration: animation.duration * 0.3,
        }),

        top: withTiming(-headerHeight * 2, {
          duration: animation.duration * 0.2,
        }),
        opacity: withTiming(0, { duration: animation.duration * 0.75 }),
      };
    }
  });

  const scrollToTop = () => {
    scrollRef.current?.scrollToOffset({ animated: true, offset: 0 });
    expanded.value = true;
  };

  const transKey = statusToTranslationKeysUtil(selectedViewingStatus);

  return (
    <>
      <Row flex={1}>
        <PoliciesDrawer
          expanded={expanded}
          scrollToTop={() => scrollToTop()}
          routeStatus={selectedViewingStatus}
          onChange={handleChangeStatus}
        />

        <Column flex={1}>
          <Animated.View layout={LinearTransition} style={animatedStyle}>
            <Column p={space[3]}>
              <TopLabel
                sortProps={sortByNewest}
                onPressSort={onPressSort}
                filterFunction={
                  selectedViewingStatus === 'pending'
                    ? handleShowFilter
                    : undefined
                }
                filtered={filterOption.filtered}
              />
              <HeaderCase
                TypographyDefault={Typography.Body}
                children={t('policy:allCase', {
                  status: selectedViewingStatus,
                  count: businessList?.length ?? 0,
                })}
              />
              <HeaderBody
                color={colors.placeholder}
                TypographyDefault={Typography.Body}
                children={transKey ? t(`policy:${transKey}`) : '--'}
              />
            </Column>
          </Animated.View>

          {isLoading ? (
            <Row justifyContent="center">
              <LoadingIndicator />
            </Row>
          ) : (
            <NBList
              data={dataSort && dataSort}
              navigation={navigation}
              scrollY={scrollY}
              expanded={expanded}
              scrollRef={scrollRef}
              status={selectedViewingStatus}
              isViewingByTeam={
                viewingAgentCode ? viewingAgentCode?.length > 0 : false
              }
            />
          )}
        </Column>
      </Row>

      <PendingFilterPanel filterProps={filterProps} />
    </>
  );
}

/**
 * Export to POSListScreen
 */
export const HeaderCase = styled(ResponsiveText)(({ theme }) => ({
  marginBottom: theme.space[1],
}));

export const HeaderBody = styled(ResponsiveText)(({ theme }) => ({
  color: theme.colors.placeholder,
}));
