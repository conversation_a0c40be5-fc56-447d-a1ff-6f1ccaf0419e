import { useTheme } from '@emotion/react';
import { MaterialTopTabScreenProps } from '@react-navigation/material-top-tabs';
import { useGetPolicy } from 'hooks/useGetPolicyDetail';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PoliciesDetailTabParamList } from 'types';
import { PolicyDetailsPolicy, PolicyLabelStatus } from 'types/policy';
import { capitalFirst, dateFormatUtil } from 'utils/helper/formatUtil';
import { numberToThousandsFormatWithDecimal } from 'utils/helper/numberToThousandsFormatWithDecimal';
import HeaderPolicyStatusLabel from '../../../components/HeaderPolicyStatusLabel';
import ScrollScreenWrapper from '../../../components/PolicyDetails/ScrollScreenWrapper';
import {
  Container,
  InnerContainer,
  Item,
  ItemContainer,
  Label,
  LabelContainer,
  LabelStyle,
  Line,
  Title,
} from '../../../components/PolicyDetails/StyledComponents';
import { Row } from 'cube-ui-components';

type DetailTabScreenProps = MaterialTopTabScreenProps<
  PoliciesDetailTabParamList,
  'Policy'
>;

export default function Policy({ navigation, route }: DetailTabScreenProps) {
  const { status, policyNo } = route.params;

  const { colors } = useTheme();
  const { t } = useTranslation(['policy']);

  const { data } = useGetPolicy(policyNo);

  const policy = useMemo(() => {
    const policyData: PolicyDetailsPolicy = {
      policyNumber: '--',
      agentCode: '--',
      submitDate: '--',
      issueDate: '--',
      billFrequency: '--',
      billingChannel: '--',
      premiumStatusCode: '--',
      premiumStatusCodeDesc: '--',
      statusCode: '--',
      statusCodeDesc: '--',
      planCode: '--',
      planCodeDesc: '--',
      singlePremium: '--',
      installmentPremium: '--',
      lifeSumInsured: '--',
      paymentMode: '--',
      paymentMethod: '--',
      paymentMethodDesc: '--',
      clientCode: '--',
      firstName: '--',
      middleName: '--',
      surname: '--',
      ownerBirthdate: '--',
      dispatchClientCode: '--',
      billToDate: '--',
      totalInstallmentPremium: '--',
      ageAtCommencement: '--',
      planCategory: '--',
      planCategoryDesc: '--',
      riskCessationDate: '--',
      currencyCode: '--',
      currency: '--',
      planShortDesc: '--',
      showFundProjection: '--',
      fixPremium: '--',
      riskCessationAge: '--',
      riskCessationTerm: '--',
      agentSupervisor: '--',
      owEmail: '--',
      owMobile: '--',
      lfFirstName: '--',
      lfMiddleName: '--',
      lfSurname: '--',
      lfEmail: '--',
      lfMobile: '--',
      basicPremium: '--',
      riderPremium: '--',
      paymentFrequency: '--',
      premiumHoliday: '--',
      activationAdaAca: '--',
      rtu: '--',
      fundValue: '--',
      dueDate: '--',
      maturityDate: '--',
    };

    if (data === undefined || !data) {
      return policyData;
    }
    for (const [key, value] of Object.entries(data)) {
      if ((value || value == 0) && value != '') {
        // replace {false}, not {null/undefined/''}
        policyData[key] = value;
      }
      if (typeof value === 'number') {
        policyData[key] = numberToThousandsFormatWithDecimal(value);
      }
    }
    return policyData;
  }, [data]);

  const getOwnerName = () => {
    if (policy?.firstName === '--') {
      return policy?.surname ?? '--';
    }
    if (policy?.surname === '--') {
      return policy?.firstName;
    }
    return `${policy?.firstName} ${policy?.surname}`;
  };

  const ApplicationInfoSection = () => {
    return (
      <LabelContainer>
        <Title
          fontWeight="bold"
          children={t('policy:policyDetail.policy.applicationInfo')}
        />

        {/* CUBEPH-2362/CUBEPH-2562: Hide missing field */}
        {/* <LabelStyle>
          <Label>{t('policy:policyDetail.policy.applicationNo')}</Label>
          <ItemContainer>
            <Item>{'--'}</Item>
          </ItemContainer>
        </LabelStyle> */}

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.policyNo')} />
          <ItemContainer>
            <Item children={policy?.policyNumber} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.servicingAgent')} />
          <ItemContainer>
            <Item children={policy?.servicingAgentName} />
          </ItemContainer>
        </LabelStyle>

        {/* <LabelStyle>
          <Label>{t('policy:policyDetail.policy.servicingGroup')}</Label>
          <ItemContainer>
            <Item>{policy?.servicingGroup}</Item>
          </ItemContainer>
        </LabelStyle> */}

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.supervisorAgent')} />
          <ItemContainer>
            <Item children={policy?.supervisorAgentName} />
          </ItemContainer>
        </LabelStyle>
      </LabelContainer>
    );
  };

  const PolicyHolderInfoSection = () => {
    return (
      <LabelContainer>
        <Title
          fontWeight="bold"
          children={t('policy:policyDetail.policy.policyHolderDetails')}
        />

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.name')} />
          <ItemContainer>
            <Item children={getOwnerName()} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.email')} />
          <ItemContainer>
            <Item children={policy?.owEmail} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.mobileNumber')} />
          <ItemContainer>
            <Item children={policy?.owMobile} />
          </ItemContainer>
        </LabelStyle>

        <Title
          fontWeight="bold"
          children={t('policy:policyDetail.policy.mainInsuredDetails')}
        />

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.name')} />
          <ItemContainer>
            <Item children={policy?.lfFirstName + ' ' + policy?.lfSurname} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.email')} />
          <ItemContainer>
            <Item children={policy?.lfEmail} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.mobileNumber')} />
          <ItemContainer>
            <Item children={policy?.lfMobile} />
          </ItemContainer>
        </LabelStyle>
      </LabelContainer>
    );
  };

  const PolicyInfoSection = () => {
    return (
      <LabelContainer>
        <Title
          fontWeight="bold"
          children={t('policy:policyDetail.policy.policyInfo')}
        />

        <LabelStyle>
          <Label children={t('policy:product')} />
          <ItemContainer>
            <Item children={policy?.planCodeDesc} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:status')} />
          <ItemContainer>
            <Item>
              {policy.statusCode && policy.statusCodeDesc && (
                <HeaderPolicyStatusLabel
                  status={
                    policy?.statusCodeDesc?.toLowerCase() as PolicyLabelStatus
                  }
                  customLabel={policy?.statusCodeDesc}
                  colors={colors}
                />
              )}
            </Item>
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:detail.paymentMethod')} />
          <ItemContainer>
            <Item children={policy?.paymentMethodDesc} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.currency')} />
          <ItemContainer>
            <Item children={policy?.currencyCode} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.premiumHoliday')} />
          <ItemContainer>
            <Item>
              {policy.premiumHoliday == '--'
                ? 'No'
                : policy.premiumHoliday
                ? 'Yes'
                : 'No'}
            </Item>
          </ItemContainer>
        </LabelStyle>

        {/* CUBEPH-2362/CUBEPH-2562: Hide missing field */}
        {/* <LabelStyle>
          <Label>{t('policy:policyDetail.policy.virtualAccount')}</Label>
          <ItemContainer>
            <Item>{'--'}</Item>
          </ItemContainer>
        </LabelStyle> */}

        <LabelStyle>
          <Label children={t('policy:registerDate')} />
          <ItemContainer>
            <Item
              children={
                policy.submitDate ? dateFormatUtil(policy?.submitDate) : '--'
              }
            />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:issueDate')} />
          <ItemContainer>
            <Item
              children={
                policy?.issueDate ? dateFormatUtil(policy?.issueDate) : '--'
              }
            />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:pos.dueDate')} />
          <ItemContainer>
            <Item>
              {isShowDueAndMaturityDate(policy.statusCode || '')
                ? policy?.dueDate
                  ? dateFormatUtil(policy?.dueDate)
                  : '--'
                : '--'}
            </Item>
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.maturityDate')} />
          <ItemContainer>
            <Item>
              {isShowDueAndMaturityDate(policy?.statusCode || '')
                ? policy?.maturityDate
                  ? dateFormatUtil(policy?.maturityDate)
                  : '--'
                : '--'}
            </Item>
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:detail.paymentFrequency')} />
          <ItemContainer>
            <Item
              children={
                policy?.paymentFrequency
                  ? capitalFirst(policy?.paymentFrequency)
                  : '--'
              }
            />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:policyDetail.policy.totalSumAssured')} />
          <ItemContainer>
            <Item children={policy?.lifeSumInsured} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:basicPremium')} />
          <ItemContainer>
            <Item children={policy?.basicPremium} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:riderPremium')} />
          <ItemContainer>
            <Item children={policy?.riderPremium} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:singlePremium')} />
          <ItemContainer>
            <Item children={policy?.singlePremium} />
          </ItemContainer>
        </LabelStyle>

        <LabelStyle>
          <Label children={t('policy:regularTopUp')} />
          <ItemContainer>
            <Item children={policy?.rtu} />
          </ItemContainer>
        </LabelStyle>
      </LabelContainer>
    );
  };

  return (
    <ScrollScreenWrapper>
      <Container>
        <InnerContainer>
          <ApplicationInfoSection />
          <Line />
          <PolicyHolderInfoSection />
          <Line />
          <PolicyInfoSection />
        </InnerContainer>
      </Container>

      {/* Extra height for UI handling */}
      <Row h={120} />
    </ScrollScreenWrapper>
  );
}

export const isShowDueAndMaturityDate = (policyStatusCode: string) => {
  // CUBEPH-2362 follow Iris Web logic for maturity date show no show
  switch (policyStatusCode) {
    // Inforced, Lapsed, Surrender, CFI (CF)
    // show both dates
    case 'IF':
    case 'LA':
    case 'SU':
    case 'CF':
      return true;

    // Declined, Freelook, Postpone, Proposal, UW, Pending(NP), CounterOffer(NC)
    // hide both dates
    case 'DR':
    case 'FL':
    case 'PO':
    case 'PS':
    case 'UW':
    case 'NP':
    case 'NC':
      return false;

    // Jessica: unmentioned default hide, check mapping excel, above logic came from iris web filter
    default:
      return false;
  }
};
