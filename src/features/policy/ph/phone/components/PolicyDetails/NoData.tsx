import React from 'react';
import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import NoDataSVG from 'features/policy/assets/NoDataSVG';
import { useTranslation } from 'react-i18next';
import ScrollScreenWrapper from 'features/policy/components/PolicyDetails/ScrollScreenWrapper';
import {
  Container,
  EmptyStateDescription,
} from 'features/policy/components/PolicyDetails/StyledComponents';

export default function NoData() {
  const { space } = useTheme();
  const { t } = useTranslation(['policy']);

  return (
    <ScrollScreenWrapper>
      <Container>
        <View
          style={{
            paddingTop: space[35],
            alignItems: 'center',
            gap: space[4],
          }}>
          <NoDataSVG />
          <EmptyStateDescription>
            {t('policy:policyDetail.beneficiary.noData')}
          </EmptyStateDescription>
        </View>
      </Container>
    </ScrollScreenWrapper>
  );
}
