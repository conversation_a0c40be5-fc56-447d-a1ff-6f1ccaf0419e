import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Body, H8, Icon, TextField, addToast } from 'cube-ui-components';
import React from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { PolicyDetailsPolicy, PolicyStatus } from 'types/policy';

export default function SupportEmailForm(props: {
  onBack: () => void;
  // turnOffActionPanel: () => void;
  data: PolicyDetailsPolicy | undefined;
  policyNo: string | undefined | null;
  modalVisible: boolean;
  status: PolicyStatus;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['policy']);

  const { onBack, data, policyNo, modalVisible, status } = props;

  const { handleSubmit, control } = useForm({
    defaultValues: {
      email: '<EMAIL>',
      name: `${status} Policy #${policyNo}`,
      message: `Please help me check the status of this ${status} policy #${policyNo}, policy owner: ${
        data?.firstName + ' ' + data?.surname
      }. Thank you!`,
    },
  });

  const onSubmit = () => {
    onBack();
    addToast([
      {
        message: 'Feedback has been sent to FWD support',
        IconLeft: () => <Icon.Tick fill={colors.background} />,
      },
    ]);
  };

  return (
    <>
      <TouchableOpacity
        onPress={onBack}
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          paddingTop: space[4],
        }}>
        <Icon.ArrowLeft />

        <H8
          fontWeight="bold"
          color={colors.primary}
          children={t(`policy:email.back`)}
        />
      </TouchableOpacity>

      <View style={{ marginTop: space[4], marginBottom: space[3] }}>
        <Body color={colors.secondary} children={t(`policy:email.enquire`)} />
      </View>

      <Section>
        <Input
          control={control}
          as={TextField}
          editable={false}
          disabled
          name="email"
          label="To"
          style={{ color: colors.onBackground, fontSize: space[4] }}
        />

        <Input
          control={control}
          as={TextField}
          name="name"
          label="Subject"
          editable={false}
          style={{ marginVertical: space[4] }}
        />

        <Input
          control={control}
          as={TextField}
          name="message"
          label="Your message"
          autoExpand
          multiline
          numberOfLines={200}
          inputContainerStyle={{ height: 343 }}
        />
      </Section>
    </>
  );
}
const Section = styled.ScrollView(({ theme }) => ({
  paddingTop: theme.space[4],
  paddingBottom: theme.space[6],
  backgroundColor: theme.colors.palette.white,
}));
