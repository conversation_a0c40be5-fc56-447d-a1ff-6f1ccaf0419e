import { useTheme } from '@emotion/react';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import useBoundStore from 'hooks/useBoundStore';
import { useGetNewBusinessPolicyByAgentIdPH } from 'hooks/useGetPolicyList';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { SortDirectionKeys } from 'types/eRecruit';
import { STATUS_ISSUED } from 'types/policy';
import SearchSection from '../../components/SearchSection';
import IssueTableSet from './IssueTable';

export default function NewBusinessIssueScreen() {
  const { t } = useTranslation('policy');
  const { colors, space, borderRadius } = useTheme();
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);

  const [isSearching, setIsSearching] = useState(false);
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const {
    data,
    isLoading,
    // isRefetching,
    // refetch
  } = useGetNewBusinessPolicyByAgentIdPH(loginAgentCode ?? '');

  const submissionCount =
    data?.filter(data => data.status === STATUS_ISSUED).length ?? 0;

  const sortedData = useMemo(() => {
    if (!data) return [];

    const sortedDataList = [...data]
      .filter(item => item.status === STATUS_ISSUED)
      .sort((a, b) => {
        if (a.submitDate === undefined || b.submitDate === undefined) return 0;
        const dateA = new Date(a.submitDate);
        const dateB = new Date(b.submitDate);
        return order === 'newest'
          ? dateB.getTime() - dateA.getTime()
          : dateA.getTime() - dateB.getTime();
      });

    return sortedDataList;
  }, [data, order]);

  const isDefaultConfig = true;

  if (isSearching) {
    return (
      <SearchSection
        isDefaultConfig={isDefaultConfig}
        setIsSearching={setIsSearching}
        data={sortedData}
      />
    );
  }

  return (
    <AnimatedViewWrapper
      style={{
        flex: 1,
        paddingTop: space[5],
        paddingHorizontal: space[6],
        backgroundColor: colors.palette.fwdGrey[50],
      }}>
      <Row alignItems="center" minH={space[11]} justifyContent="space-between">
        <Typography.H6 fontWeight="bold">
          {t('newBusiness.issued')}
        </Typography.H6>
        <TouchableOpacity onPress={() => setIsSearching(!isSearching)}>
          <Box
            h={space[11] - 2}
            w={space[11]}
            border={1}
            justifyContent="center"
            alignItems="center"
            backgroundColor={colors.palette.white}
            borderColor={colors.primary}
            borderRadius={borderRadius.full}>
            <Icon.Search />
          </Box>
        </TouchableOpacity>
      </Row>

      {isDefaultConfig ? (
        <Box h={space[2]} />
      ) : (
        <>
          {/* <SubmissionFiltersSection
            statusFilterParams={statusFilterParams}
            setStatusFilterParams={setStatusFilterParams}
          /> */}
        </>
      )}

      <Typography.H8
        color={colors.palette.fwdGreyDarkest}
        children={`${t('newBusiness.totalCounts', {
          count: submissionCount ?? 0,
        })} | ${t('pendingStaticText')}`}
        style={{ paddingBottom: space[3] }}
      />

      <IssueTableSet.Table
        type="default"
        // isRefreshing={isRefetching}
        // onRefresh={refetch}
        isLoading={isLoading}
        HeaderComponent={() => (
          <IssueTableSet.Header
            sortOrder={order}
            setSortOrder={setOrder}
            isDefaultConfig={isDefaultConfig}
          />
        )}
        data={sortedData}
        isDefaultConfig={isDefaultConfig}
      />
    </AnimatedViewWrapper>
  );
}
