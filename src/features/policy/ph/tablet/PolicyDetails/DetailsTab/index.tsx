import { useTheme } from '@emotion/react';
import { Column, Box } from 'cube-ui-components';
import React, { useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { RouteProp } from '@react-navigation/native';
import { PoliciesDetailTabParamListTablet } from 'types';
import { PolicyDetailsPolicy } from 'types/policy';
import ScrollScreenWrapper from 'features/policy/components/PolicyDetails/ScrollScreenWrapper';
import { useScrollToTop } from 'features/policy/hooks/useScrollToTop';
import Tag from './Tag';
import PolicyCard from './PolicyCard';
import AccountCard from './AccountCard';
import ProductCard from './ProductCard';
import BeneficiaryCard from './BeneficiaryCard';
import CollapsibleCard from 'components/CollapsibleCard';
import FundSVG from 'features/policy/assets/FundSVG';
import HistorySVG from 'features/policy/assets/HistorySVG';
import FundInformationCard from './FundInformationCard';
import History from './History';

const PolicyDetails = ({
  route,
  data,
}: {
  route: RouteProp<PoliciesDetailTabParamListTablet, 'policyDetails'>;
  data?: PolicyDetailsPolicy;
}) => {
  const { policyNo } = route.params;
  const { t } = useTranslation('policy');
  const { bottom } = useSafeAreaInsets();
  const { colors, space } = useTheme();
  const { ref } = useScrollToTop();
  const tagPosition = React.useRef<number[]>([]);
  const [currentTag, setCurrentTag] = useState(0);
  const setTag = (y: number) => {
    let current = 0;
    tagPosition.current.forEach((p, index) => {
      if (y > p - 1) {
        current = index;
      }
    });
    setCurrentTag(current);
  };

  return (
    <ScrollScreenWrapper ref={ref} bgColor={colors.surface} onScroll={setTag}>
      <Tag
        currentTag={currentTag}
        setCurrentTag={setCurrentTag}
        tagPosition={tagPosition}
        scrollRef={ref}
      />
      <Column gap={space[4]} px={space[24]} pb={space[10] + bottom}>
        <Box
          onLayout={e => {
            tagPosition.current[0] = e.nativeEvent.layout.y;
          }}>
          <PolicyCard data={data ?? {}} />
        </Box>
        <Box
          onLayout={e => {
            tagPosition.current[1] = e.nativeEvent.layout.y;
          }}>
          <AccountCard policyNo={policyNo} />
        </Box>
        <Box
          onLayout={e => {
            tagPosition.current[2] = e.nativeEvent.layout.y;
          }}>
          <ProductCard policyNo={policyNo} />
        </Box>
        <Box
          onLayout={e => {
            tagPosition.current[3] = e.nativeEvent.layout.y;
          }}>
          <BeneficiaryCard policyNo={policyNo} />
        </Box>
        <Box
          onLayout={e => {
            tagPosition.current[4] = e.nativeEvent.layout.y;
          }}>
          <CollapsibleCard
            title={t('detail.tag.fundInformation')}
            TitleIcon={FundSVG}>
            <FundInformationCard policyNo={policyNo} />
          </CollapsibleCard>
        </Box>
        <Box
          onLayout={e => {
            tagPosition.current[5] = e.nativeEvent.layout.y;
          }}>
          <CollapsibleCard
            title={t('detail.tag.history')}
            TitleIcon={HistorySVG}>
            <History policyNo={policyNo} />
          </CollapsibleCard>
        </Box>
      </Column>
    </ScrollScreenWrapper>
  );
};
export default PolicyDetails;
