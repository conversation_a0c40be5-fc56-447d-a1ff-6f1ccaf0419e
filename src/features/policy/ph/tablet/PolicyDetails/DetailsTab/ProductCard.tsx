import React from 'react';
import { useTranslation } from 'react-i18next';
import { useGetProduct } from 'hooks/useGetPolicyDetail';
import CollapsibleCard from 'components/CollapsibleCard';
import ProductSVG from 'features/policy/assets/ProductSVG';
import { numberToThousandsFormatWithDecimal } from 'utils/helper/numberToThousandsFormatWithDecimal';
import {
  owbCodeToLabelStatus,
  OWBStatusCodes,
  Product as ProductType,
} from 'types/policy';
import { RenderSection, sectionData } from './PolicyCard';
import { dateFormatUtil } from 'utils/helper/formatUtil';

const ProductCard = ({ policyNo }: { policyNo: string }) => {
  const { data = [] } = useGetProduct(policyNo);
  const { t } = useTranslation('policy');

  const productDataTemplate = (
    item: ProductType = {},
    index?: number,
    hasSeparator?: boolean,
  ) => {
    return {
      title: index
        ? `${t('policyDetail.product.plan')} ${index}`
        : t('policyDetail.product.plan'),
      data: [
        {
          label: t('policyDetail.product.planName'),
          value: item?.planCodeDesc,
        },
        {
          label: t('policyDetail.product.insuredName'),
          value: item?.insuredName,
        },
        {
          label: t('policyDetail.product.ageAtEntry'),
          value: item?.ageAtCommencement,
        },
        {
          label: t('policyDetail.product.status'),
          value: t(
            `exitingPolicy.status.${
              owbCodeToLabelStatus?.[item?.status as OWBStatusCodes]
            }`,
          ),
        },
        {
          label: t('policyDetail.product.sumInsured'),
          value: item?.lifeSumInsured,
        },
        {
          label: t('policyDetail.product.premiumAmount'),
          value: item?.installmentPremium,
        },
        {
          label: t('policyDetail.product.subPlan'),
          value:
            item?.benefitPlan && item?.units
              ? `${item?.benefitPlan}/${item?.units}`
              : '--',
        },
        {
          label: t('policyDetail.product.rcd'),
          value: dateFormatUtil(item?.riskCessationDate as string),
        },
      ],
      hasSeparator: hasSeparator || false,
    };
  };
  let productData = [productDataTemplate()];
  if (data?.length) {
    productData = data.map((item, index) => {
      const i = index + 1;
      const hasSeparator = i < data.length;
      return productDataTemplate(item, i, hasSeparator);
    });
  }

  const newProductData = productData.reduce(
    (o: sectionData[], c: sectionData) => {
      const { data, ...other } = c;
      const newData = data.map(item => {
        const { value, ...otherItem } = item;
        let newValue = value || value === 0 ? value : '--';
        if (typeof newValue === 'number') {
          newValue = numberToThousandsFormatWithDecimal(newValue);
        }
        return { ...otherItem, value: newValue };
      });
      return [...o, { data: newData, ...other }];
    },
    [],
  );
  return (
    <CollapsibleCard title={t('detail.tag.product')} TitleIcon={ProductSVG}>
      {<RenderSection sectionData={newProductData} />}
    </CollapsibleCard>
  );
};

export default ProductCard;
