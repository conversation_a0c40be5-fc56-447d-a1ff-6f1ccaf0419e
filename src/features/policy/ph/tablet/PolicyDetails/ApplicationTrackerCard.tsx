import { useTheme } from '@emotion/react';
import { Column, PictogramIcon } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { RouteProp } from '@react-navigation/native';
import { PoliciesDetailTabParamListTablet } from 'types';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ApplicationTracker from 'features/policy/components/PolicyDetails/ApplicationTracker';
import ScrollScreenWrapper from 'features/policy/components/PolicyDetails/ScrollScreenWrapper';
import DetailsCard from '../../../components/DetailsCard';
const ApplicationTrackerCard = ({
  route,
}: {
  route: RouteProp<PoliciesDetailTabParamListTablet, 'applicationTracker'>;
}) => {
  const { policyNo, status } = route.params;
  const { t } = useTranslation('policy');
  const { bottom } = useSafeAreaInsets();
  const { colors, space, sizes } = useTheme();
  return (
    <ScrollScreenWrapper bgColor={colors.surface}>
      <Column
        mt={space[6]}
        gap={space[4]}
        px={space[24]}
        pb={space[10] + bottom}>
        <DetailsCard
          title={t('tabScreen.applicationTracker')}
          TitleIcon={PictogramIcon.Track}
          titleIconSize={sizes[10]}>
          <View style={{ paddingHorizontal: space[4] }}>
            <ApplicationTracker policyNo={policyNo} />
          </View>
        </DetailsCard>
      </Column>
    </ScrollScreenWrapper>
  );
};
export default ApplicationTrackerCard;
