import React from 'react';
import Svg, { Path } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';

function GallerySVG(props: SvgPictogramProps) {
  let body = null;

  body = (
    <>
      <Path
        d="M109.341 99.52h-70.96c-2.89 0-5.25-2.36-5.25-5.25V40.25c0-2.89 2.36-5.25 5.25-5.25h70.96c2.89 0 5.25 2.36 5.25 5.25v54.03c-.01 2.88-2.37 5.24-5.25 5.24z"
        fill="#EDEFF0"
      />
      <Path d="M104.019 45.64h-77.4v45.53h77.4V45.64z" fill="#fff" />
      <Path
        d="M100.1 43.68H31.08c-3.35 0-6.08 2.72-6.08 6.07V98.4a6.09 6.09 0 006.08 6.08h69.02a6.09 6.09 0 006.08-6.08V49.75c0-3.35-2.73-6.07-6.08-6.07zM31.08 48.1h69.02c.91 0 1.65.74 1.65 1.65v29.9a16.752 16.752 0 00-17.98-3.81l-9.18 3.57c-5.61 2.19-12.02.59-15.94-3.98l-3.27-3.81c-2.18-2.53-5.33-3.85-8.66-3.62-3.14.22-5.94 1.78-7.76 4.31l-3.11 4.84a12.34 12.34 0 01-6.42 5.03V49.75c-.01-.91.73-1.65 1.65-1.65z"
        fill="#DBDFE1"
      />
      <Path
        d="M75.13 69.46a7.96 7.96 0 100-15.92 7.96 7.96 0 000 15.92z"
        fill="#B3B6B8"
      />
    </>
  );

  return (
    <Svg
      width={props.width || props.size || 140}
      height={props.height || props.size || 140}
      viewBox="0 0 140 140"
      fill="none">
      {body}
    </Svg>
  );
}

export default GallerySVG;
