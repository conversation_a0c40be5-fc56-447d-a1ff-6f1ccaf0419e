import { useTheme } from '@emotion/react';
import { Typography, ExtraLargeBody } from 'cube-ui-components';
import { useCallback, useEffect, useState } from 'react';
import { LayoutChangeEvent, ViewStyle } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

type Props = {
  message?: string;
  rotateDeg?: number;
};

export const HelperMessageToast = ({ message, rotateDeg = 0 }: Props) => {
  const {
    colors,
    space,
    animation: { duration },
  } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [size, setSize] = useState({ width: 0, height: 0 });
  const opacity = useSharedValue(0);

  const [debouncedMessage, setDebouncedMessage] = useState<string | undefined>(
    message,
  );
  useEffect(() => {
    if (message) setDebouncedMessage(message);
  }, [message]);

  const style: ViewStyle = {
    position: 'absolute',
    left: '50%',
    top: '50%',
    paddingVertical: space[2],
    paddingHorizontal: space[3],
    borderRadius: space[3],
    backgroundColor: 'rgba(255, 255, 255, 0.65)',
    transformOrigin: isTabletMode ? undefined : 'top left',
    transform: [
      { rotate: `${rotateDeg}deg` },
      { translateY: -size.height / 2 },
      { translateX: -size.width / 2 },
    ],
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  useEffect(() => {
    const isShow = !!message;
    opacity.value = withTiming(isShow ? 1 : 0, { duration });

    // Auto hide toast after 3s
    // if (isShow) {
    //   setTimeout(() => {
    //     opacity.value = withTiming(0, { duration });
    //   }, 3000);
    // }
  }, [duration, message, opacity]);

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    const { width, height } = e.nativeEvent.layout;
    setSize({ width, height });
  }, []);

  const TextComponent = isTabletMode ? ExtraLargeBody : Typography.Text;

  return (
    <Animated.View style={[style, animatedStyle]} onLayout={onLayout}>
      <TextComponent
        fontWeight={'medium'}
        color={colors.palette.fwdDarkGreen[100]}>
        {debouncedMessage}
      </TextComponent>
    </Animated.View>
  );
};
