import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useMemo } from 'react';
import { getScreenFlags } from '../utils/getScreenFlags';

export const useHasTakeOver = () => {
  const { caseObj } = useGetActiveCase();
  const quotation = useSelectedQuotation();
  const channel = useGetCubeChannel();
  return useMemo(() => {
    const { hasTakeOver } = getScreenFlags(caseObj, quotation, channel);
    return hasTakeOver;
  }, [caseObj, channel, quotation]);
};
