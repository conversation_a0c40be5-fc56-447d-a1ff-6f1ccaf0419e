import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  partyToRepresentativeInfo,
  representativeInfoDefaultValue,
  RepresentativeInfoForm,
  representativeInfoSchema,
  representativeInfoToParty,
} from 'features/eAppV2/ib/validations/applicationDetails/representative/representativeInfoValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useSaveParty } from 'hooks/useParty';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';

export const useRepresentativeInfo = () => {
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);
  const { saveParty, isLoading: isSavingParty } = useSaveParty();

  const { caseObj } = useGetActiveCase();
  const companyParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const { data: optionList } = useGetOptionList<'ib'>();

  const resolver = useYupResolver(representativeInfoSchema);
  const representativeInfoForm = useEAppForm({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => partyToRepresentativeInfo(companyParty),
      [], //eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver,
    context: {
      optionList: optionList,
    },
  });

  const isDone = useSchemaValid(
    representativeInfoForm.control,
    representativeInfoDefaultValue,
    representativeInfoSchema,
  );

  const onSave = useCallback(
    async (data: RepresentativeInfoForm) => {
      if (!caseObj)
        throw new Error('missing case data while saving representative info');
      if (!companyParty)
        throw new Error(
          'missing company party data while saving representative info',
        );

      if (!companyParty.entity)
        throw new Error(
          'missing company entity data while saving representative info',
        );

      const newCompanyParty = representativeInfoToParty(
        data,
        companyParty.entity,
      );
      await saveParty(
        {
          ...companyParty,
          ...newCompanyParty,
        },
        {
          overridingRoles: false,
          preventCreatingParty: true,
        },
      );
      nextSubgroup(true);
    },
    [caseObj, nextSubgroup, companyParty, saveParty],
  );

  return {
    onSave,
    isDone,
    isLoading: isSavingParty,
    representativeInfoForm,
  };
};
