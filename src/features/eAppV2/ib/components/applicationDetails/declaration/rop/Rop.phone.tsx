import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  CurrencyTextField,
  H6,
  H7,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import SectionWithTitle from 'features/eAppV2/common/components/SectionWithTitle';
import Input from 'components/Input';
import {
  fromReplacementInfo,
  ropSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/declarationValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useRop } from './useRop';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useHasTakeOver } from 'features/eAppV2/ib/hooks/useHasTakeOver';
import { PartyRole } from 'types/party';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
const RopPhone = () => {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const { disabledForm } = useDisabledEAppForm();

  const { caseObj } = useGetActiveCase();
  const resolver = useYupResolver(ropSchema);
  const ropForm = useEAppForm({
    mode: 'onBlur',
    defaultValues: fromReplacementInfo(caseObj?.application?.replacementInfo),
    resolver,
  });
  const control = ropForm.control;

  const { hasReplacePlan, terminateInfluence, saveRop } = useRop(ropForm);

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control,
      watch: ropForm.watch,
      schema: ropSchema,
      scrollRef,
      scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  const hasTakeOver = useHasTakeOver();

  const { next, nextGroup } =
    useEAppProgressBarStore(state => ({
      next: state.next,
      nextGroup: state.nextGroup,
    }));

  const onSubmit = async () => {
    await saveRop();

    const disabled = caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER))?.uw?.decisionResponse

    if (!hasTakeOver && disabled) {
      nextGroup(true)
    } else {
      next(true);
    }
  };

  return (
    <Box flex={1}>
      <KeyboardAwareScrollView
        ref={scrollRef}
        contentContainerStyle={{ gap: space[5], padding: space[4] }}>
        <H6 fontWeight="bold">{t('eApp:declaration.rop')}</H6>
        <SectionWithTitle title={t('eApp:declaration.rop.title')}>
          <Content>
            <Box>
              <LargeBody fontWeight="bold">
                {t('eApp:declaration.note')}
                <LargeBody fontWeight="normal">
                  {t('eApp:declaration.rop.note')}
                </LargeBody>
              </LargeBody>
            </Box>
            <Row mt={space[5]} gap={space[2]}>
              <H7 fontWeight="bold">
                {t('eApp:declaration.rop.question.number.1')}
              </H7>
              <Box flex={1}>
                <H7 fontWeight="bold">
                  {t('eApp:declaration.rop.question.1')}
                </H7>
                <Row mt={space[2]} gap={space[8]}>
                  <Input
                    control={control}
                    as={RadioButtonGroup}
                    name={'hasReplacePlan'}>
                    <RadioButton value="yes" label={t('eApp:yes')} disabled={disabledForm} />
                    <RadioButton value="no" label={t('eApp:no')} disabled={disabledForm} />
                  </Input>
                </Row>
              </Box>
            </Row>

            {hasReplacePlan === 'yes' && (
              <Box mt={30} gap={22}>
                <Input
                  control={control}
                  as={TextField}
                  name={'replacementReason'}
                  label={t('eApp:declaration.rop.replacementReason')}
                />
                <Input
                  control={control}
                  as={TextField}
                  name={'insuranceOperator'}
                  label={t('eApp:declaration.rop.insuranceOperator')}
                />
                <Input
                  control={control}
                  as={TextField}
                  name={'planName'}
                  label={t('eApp:declaration.rop.planName')}
                />
                <Input
                  control={control}
                  as={CurrencyTextField}
                  name={'sumAssured'}
                  label={t('eApp:declaration.rop.sumAssured')}
                />
              </Box>
            )}

            <Row mt={space[5]} gap={space[2]}>
              <H7 fontWeight="bold">
                {t('eApp:declaration.rop.question.number.2')}
              </H7>
              <Box flex={1}>
                <H7 fontWeight="bold">
                  {t('eApp:declaration.rop.question.2')}
                </H7>
                <Row mt={space[2]} gap={space[8]}>
                  <Input
                    control={control}
                    as={RadioButtonGroup}
                    name={'terminateInfluence'}>
                    <RadioButton value="yes" label={t('eApp:yes')} disabled={disabledForm} />
                    <RadioButton value="no" label={t('eApp:no')} disabled={disabledForm} />
                  </Input>
                </Row>
              </Box>
            </Row>

            {terminateInfluence === 'yes' && (
              <>
                <Row mt={space[5]} gap={space[2]} mb={30}>
                  <H7 fontWeight="bold">
                    {t('eApp:declaration.rop.question.number.2a')}
                  </H7>
                  <Box flex={1}>
                    <H7 fontWeight="bold">
                      {t('eApp:declaration.rop.question.2a')}
                    </H7>
                    <Row mt={space[2]} gap={space[8]}>
                      <Input
                        control={control}
                        as={RadioButtonGroup}
                        name={'satisfiedWithExplanation'}>
                        <RadioButton value="yes" label={t('eApp:yes')} disabled={disabledForm} />
                        <RadioButton value="no" label={t('eApp:no')} disabled={disabledForm} />
                      </Input>
                    </Row>
                  </Box>
                </Row>
                <Input
                  control={control}
                  as={TextField}
                  name={'comment'}
                  label={t('eApp:declaration.rop.comment')}
                />
              </>
            )}
          </Content>
        </SectionWithTitle>
      </KeyboardAwareScrollView>
      <EAppFooterPhone
        progressLock='appDetail-declaration-rop'
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        primaryLabel={t('eApp:next')}
        primarySubLabel={
          hasTakeOver
            ? t('eApp:declaration.takeOver')
            : t('eApp:bar.healthQuestion')
        }
        primaryDisabled={!ropForm.formState.isValid || disabledForm}
        onPrimaryPress={onSubmit}
        primaryLoading={ropForm.formState.isSubmitting}
      />
    </Box>
  );
};

export default RopPhone;

const Content = styled(Box)(({ theme: { space, colors } }) => {
  return {
    backgroundColor: colors.background,
    padding: space[4],
    paddingTop: space[3],
  };
});

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export const RadioButtonTopAlign = styled(RadioButton)(() => {
  return {
    alignItems: 'flex-start',
  };
});
