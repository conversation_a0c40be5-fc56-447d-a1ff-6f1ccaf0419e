import { LOW_INCOME_RANGES, SGP_COUNTRY } from 'constants/optionList';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import {
  FatcaForm,
  IncomeRank,
  initialTaxResidencyFormData,
  NoTinReason,
  toFatca,
} from 'features/eAppV2/ib/validations/applicationDetails/declarationValidation';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useCallback, useEffect, useMemo } from 'react';
import {
  Control,
  useFieldArray,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { PartyRole } from 'types/party';

export const useFatca = ({
  control,
  getValues,
  setValue,
  handleSubmit,
}: {
  handleSubmit: UseFormHandleSubmit<FatcaForm>;
  control: Control<FatcaForm>;
  setValue: UseFormSetValue<FatcaForm>;
  getValues: UseFormGetValues<FatcaForm>;
}) => {
  const { caseObj } = useGetActiveCase();
  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => isRole(p, PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const policyOwnerIncomeRange = policyOwner?.person?.occupation?.incomeRange;
  const policyOwnerBusinessAddress = useMemo(
    () => policyOwner?.addresses.find(a => a.addressType === 'WORK'),
    [policyOwner?.addresses],
  );
  const hasSingaporeAddress =
    policyOwnerBusinessAddress?.countryCode === SGP_COUNTRY;
  const incomeRank = useMemo(() => {
    if (policyOwnerIncomeRange) {
      return LOW_INCOME_RANGES.includes(policyOwnerIncomeRange)
        ? IncomeRank.LOW
        : IncomeRank.HIGH;
    }
    return null;
  }, [policyOwnerIncomeRange]);

  const { fields, append, remove, update, replace } = useFieldArray({
    name: 'taxResidencies',
    control,
  });

  const hasOutsideTaxResidency = useWatch({
    name: 'hasOutsideTaxResidency',
    control: control,
  });

  useEffect(() => {
    if (hasSingaporeAddress) {
      setValue('hasOutsideTaxResidency', 'yes');
      if (incomeRank === IncomeRank.HIGH) {
        Array(fields.length)
          .fill(null)
          .forEach((_, idx) => {
            update(idx, {
              ...getValues(`taxResidencies.${idx}`),
              hasTin: 'yes',
              noTinReason: undefined,
            });
          });
      }
    }
  }, [
    fields.length,
    getValues,
    hasSingaporeAddress,
    incomeRank,
    setValue,
    update,
  ]);

  const hasUsTaxResidency = useWatch({
    name: 'hasUsTaxResidency',
    control: control,
  });

  const updateTINReasonWhenSelectingNoOption = useCallback(
    (index: number) => {
      if (hasSingaporeAddress) {
        if (incomeRank === IncomeRank.LOW) {
          const taxResidencies = getValues('taxResidencies');
          if (taxResidencies && taxResidencies[index]) {
            update(index, {
              ...taxResidencies[index],
              hasTin: 'no',
              noTinReason: NoTinReason.UNABLE_TO_OBTAIN,
            });
          }
        }
      }
    },
    [getValues, hasSingaporeAddress, incomeRank, update],
  );

  const isDisableAllReasons =
    hasSingaporeAddress && incomeRank === IncomeRank.LOW;

  const isDisableHasTin = hasSingaporeAddress && incomeRank === IncomeRank.HIGH;

  useEffect(() => {
    if (
      hasOutsideTaxResidency !== 'yes' &&
      hasUsTaxResidency !== 'yes' &&
      fields.length > 0
    ) {
      remove();
    } else if (
      (hasOutsideTaxResidency === 'yes' || hasUsTaxResidency === 'yes') &&
      fields.length === 0
    ) {
      replace([
        {
          ...initialTaxResidencyFormData,
        },
      ]);
    }
  }, [
    replace,
    fields.length,
    hasOutsideTaxResidency,
    hasUsTaxResidency,
    remove,
  ]);

  const isDisabledHasOutsideTaxResidency = useMemo(() => {
    return hasSingaporeAddress;
  }, [hasSingaporeAddress]);

  const onAdd = useCallback(() => {
    if (hasSingaporeAddress && incomeRank === IncomeRank.HIGH) {
      append(
        {
          ...initialTaxResidencyFormData,
          hasTin: 'yes',
        },
        { shouldFocus: false },
      );
    } else {
      append(initialTaxResidencyFormData, { shouldFocus: false });
    }
  }, [append, hasSingaporeAddress, incomeRank]);

  const agentId = useBoundStore(state => state.auth.agentCode);
  const { mutateAsync: getCase } = useGetCaseManually();
  const { mutateAsync: saveApplication } = useCreateApplication();
  const saveFatca = useCallback(async () => {
    if (!caseObj || !agentId) return;
    return handleSubmit(async value => {
      const latestCase = await getCase(caseObj.id);
      await saveApplication({
        caseId: latestCase.id,
        data: {
          ...latestCase.application,
          proposerConsent: toFatca(
            value,
            agentId,
            Boolean(caseObj.application?.proposerConsent?.[0]?.pdpCheck),
          ),
        },
      });
    })();
  }, [agentId, caseObj, getCase, handleSubmit, saveApplication]);

  return {
    fields,
    append,
    remove,
    update,
    hasOutsideTaxResidency,
    hasUsTaxResidency,
    updateTINReasonWhenSelectingNoOption,
    isDisableAllReasons,
    isDisableHasTin,
    hasSingaporeAddress,
    incomeRank,
    policyOwner,
    isDisabledHasOutsideTaxResidency,
    onAdd,
    saveFatca,
  };
};
