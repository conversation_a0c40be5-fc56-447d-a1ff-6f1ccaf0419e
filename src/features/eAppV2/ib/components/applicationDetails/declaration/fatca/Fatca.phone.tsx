import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Button,
  H6,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import SectionWithTitle from 'features/eAppV2/common/components/SectionWithTitle';
import React, { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import TaxResidencyForm from './TaxResidencyForm';
import {
  fatcaSchema,
  fromFatca,
} from 'features/eAppV2/ib/validations/applicationDetails/declarationValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { MY_COUNTRY } from 'constants/optionList';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useFatca } from './useFatca';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import DeclarationInfo from '../declarationInfo/DeclarationInfo';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
const MAX_TAX_RESIDENCY = 5;

const FatcaPhone = () => {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { caseObj } = useGetActiveCase();
  const resolver = useYupResolver(fatcaSchema);
  const fatcaForm = useEAppForm({
    mode: 'onBlur',
    defaultValues: fromFatca(caseObj?.application?.proposerConsent),
    resolver,
  });
  const { disabledForm } = useDisabledEAppForm();
  const control = fatcaForm.control;

  const {
    fields,
    remove,
    updateTINReasonWhenSelectingNoOption,
    isDisableAllReasons,
    isDisableHasTin,
    policyOwner,
    isDisabledHasOutsideTaxResidency,
    onAdd,
    saveFatca,
  } = useFatca(fatcaForm);

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control,
      watch: fatcaForm.watch,
      schema: fatcaSchema,
      scrollRef,
      scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  const next = useEAppProgressBarStore(state => state.next);
  const onSubmit = async () => {
    await saveFatca();
    next(true);
  };

  return (
    <Box flex={1}>
      <KeyboardAwareScrollView
        ref={scrollRef}
        enableResetScrollToCoords={false}
        contentContainerStyle={{ gap: space[5], padding: space[4] }}>
        <H6 fontWeight="bold">{t('eApp:declaration.fatca')}</H6>
        <Box>
          <SectionWithTitle title={t('eApp:declaration.fatca.title')}>
            <Content>
              <LargeBody fontWeight="medium">
                {t('eApp:declaration.fatca.message')}
              </LargeBody>
              {policyOwner?.person?.nationality !== MY_COUNTRY && (
                <HintContainer>
                  <LargeBody color={colors.primary}>
                    {t('eApp:declaration.fatca.hint')}
                  </LargeBody>
                </HintContainer>
              )}
              <Row gap={space[2]} mt={space[5]}>
                <LargeBody fontWeight="bold">1.</LargeBody>
                <Box flex={1}>
                  <LargeBody fontWeight="bold">
                    {t('eApp:declaration.fatca.question.1')}
                  </LargeBody>
                  <Row mt={space[2]} gap={space[8]}>
                    <Input
                      control={control}
                      as={RadioButtonGroup}
                      name={'hasOutsideTaxResidency'}>
                      <RadioButton
                        value="yes"
                        label={t('eApp:yes')}
                        disabled={isDisabledHasOutsideTaxResidency || disabledForm}
                      />
                      <RadioButton
                        value="no"
                        label={t('eApp:no')}
                        disabled={isDisabledHasOutsideTaxResidency || disabledForm}
                      />
                    </Input>
                  </Row>
                </Box>
              </Row>

              <Row gap={space[2]} mt={space[5]}>
                <LargeBody fontWeight="bold">2.</LargeBody>
                <Box flex={1}>
                  <LargeBody fontWeight="bold">
                    {t('eApp:declaration.fatca.question.2')}
                  </LargeBody>
                  <Row mt={space[2]} gap={space[8]}>
                    <Input
                      control={control}
                      as={RadioButtonGroup}
                      name={'hasUsTaxResidency'}>
                      <RadioButton value="yes" label={t('eApp:yes')} disabled={disabledForm} />
                      <RadioButton value="no" label={t('eApp:no')} disabled={disabledForm} />
                    </Input>
                  </Row>
                </Box>
              </Row>
              {fields.map((field, index) => (
                <View key={field.id}>
                  <TaxResidencyForm
                    control={control}
                    index={index}
                    onDelete={index > 0 && !disabledForm ? () => remove(index) : undefined}
                    isDisableAllReasons={isDisableAllReasons || disabledForm}
                    isDisableHasTin={isDisableHasTin || disabledForm}
                    updateTINReasonWhenSelectingNoOption={
                      updateTINReasonWhenSelectingNoOption
                    }
                  />
                  {index < fields.length - 1 && (
                    <Box flex={1} mt={space[6]}>
                      <Divider />
                    </Box>
                  )}
                </View>
              ))}

              {fields.length > 0 && fields.length < MAX_TAX_RESIDENCY && (
                <Button
                  text={t('eApp:add')}
                  variant="secondary"
                  size="default"
                  mini={true}
                  onPress={onAdd}
                  disabled={disabledForm}
                  style={{ marginTop: space[5], minWidth: 70 }}
                />
              )}
            </Content>
          </SectionWithTitle>
          <DeclarationInfo />
        </Box>
      </KeyboardAwareScrollView>
      <EAppFooterPhone
        progressLock="appDetail-declaration-fatca"
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        primaryLabel={t('eApp:next')}
        primarySubLabel={t('eApp:declaration.rop')}
        primaryDisabled={!fatcaForm.formState.isValid || disabledForm}
        onPrimaryPress={onSubmit}
        primaryLoading={fatcaForm.formState.isSubmitting}
      />
    </Box>
  );
};

export default FatcaPhone;

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[4],
  marginTop: space[3],
  marginBottom: space[4],
}));

const HintContainer = styled(Box)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    paddingHorizontal: space[4],
    paddingVertical: 14,
    marginTop: space[5],
    borderRadius: borderRadius['small'],
  }),
);

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export const RadioButtonTopAlign = styled(RadioButton)(() => {
  return {
    alignItems: 'flex-start',
  };
});
