import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import {
  INCOME_GREATER_THAN_200K,
  NON_INCOME_OCC_GROUP,
  getOptionListLabel,
  getOptionListValue,
} from 'constants/optionList';
import {
  Body,
  Box,
  Column,
  CurrencyTextField,
  PictogramIcon,
  Row,
  SmallLabel,
  TextField,
} from 'cube-ui-components';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import {
  OccupationDetailsForm,
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/occupationDetails';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Fragment, useEffect, useMemo } from 'react';
import { UseFormReturn, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IncomeRange, Nationality, Occupation } from 'types/optionList';
import ReadOnlyField from 'features/eAppV2/common/components/ReadOnlyField';

interface Props extends UseFormReturn<OccupationDetailsForm> {
  role: 'main-part' | 'payor';
}

export default function OccupationDetailsTablet({ role, control }: Props) {
  const { t } = useTranslation(['eApp']);
  const { space, borderRadius, colors } = useTheme();
  const { data: optionList } = useGetOptionList();
  const isDone = useSchemaValid(
    control,
    occupationDetailsDefaultValue,
    occupationDetailsSchema,
  );

  const occupation = useWatch({ control, name: 'occupation' });
  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({ control, name: 'occupationGroup' });
  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: 'occupationDescription', control });

  const { occupationClass, occupationGroup, occupationDescription } =
    useOccupationClass(occupation);

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
    onChangeOccupationGroup(occupationGroup);
  }, [
    occupationDescription,
    occupationGroup,
    onChangeOccupationDescription,
    onChangeOccupationGroup,
  ]);

  const annualIncomeValue = useWatch({
    name: 'annualIncome',
    control: control,
  });

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: 'annualIncomeAmount', control });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  const occupationLabel = useMemo(() => {
    return optionList?.OCCUPATION.options.find(item => item.value === occupation)?.label ?? occupation;
  }, [occupation, optionList]);

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.occupationDetails')}
      icon={<PictogramIcon.Work3 size={40} />}
      isDone={isDone}>
      <Box mt={space[5]} px={space[6]} gap={space[5]}>
        <Row gap={space[6]}>
          <ReadOnlyField
            value={occupationLabel}
            label={t('eApp:occupation')}
          />
          {role === 'payor' ? (
            <Input
              control={control}
              as={TextField}
              name="nameOfBusiness"
              label={t('eApp:nameOfBusiness')}
              style={eAppCommonStyles.tabletTextField}
            />
          ) : (
            <Box flex={1} />
          )}
        </Row>
        {role === 'main-part' && (
          <Fragment key={occupationGroup}>
            <Row
              gap={space[8]}
              borderRadius={borderRadius.small}
              borderWidth={1}
              borderColor={colors.palette.fwdGrey[100]}
              padding={space[4]}>
              <Column flex={1} gap={space[2]}>
                <SmallLabel color={colors.placeholder}>
                  {t('eApp:occupationClass')}
                </SmallLabel>
                <Body>{occupationClass?.label.en}</Body>
              </Column>
              <Column flex={4} gap={space[2]}>
                <SmallLabel color={colors.placeholder}>
                  {t('eApp:occupationDescription')}
                </SmallLabel>
                <Body>{occupationDescription}</Body>
              </Column>
            </Row>
            <Row gap={space[6]}>
              <Input
                control={control}
                as={TextField}
                name="nameOfBusiness"
                label={t('eApp:nameOfBusiness')}
                style={eAppCommonStyles.tabletTextField}
                shouldHighlightOnUntouched={value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP && !value
                }
              />
              <Input
                control={control}
                as={AutocompletePopup<Nationality, string>}
                name="natureOfWork"
                label={t('eApp:natureOfWork')}
                data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
                getItemLabel={getOptionListLabel}
                getItemValue={getOptionListValue}
                style={eAppCommonStyles.tabletTextField}
                searchable
                shouldHighlightOnUntouched={value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP && !value
                }
              />
            </Row>

            <Row gap={space[6]}>
              <Input
                control={control}
                as={TextField}
                name="exactDuties"
                label={t('eApp:exactDuties')}
                hint={t('eApp:exactDuties.hint')}
                style={eAppCommonStyles.tabletTextField}
                shouldHighlightOnUntouched={value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP && !value
                }
              />
              <Input
                control={control}
                as={Autocomplete<IncomeRange, string>}
                name="annualIncome"
                style={eAppCommonStyles.tabletTextField}
                label={t('eApp:annualIncome')}
                data={optionList?.INCOME_RANGE.options ?? []}
                getItemLabel={getOptionListLabel}
                getItemValue={getOptionListValue}
                shouldHighlightOnUntouched={value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP && !value
                }
              />
            </Row>
            <Row gap={space[6]}>
              <Input
                control={control}
                as={CurrencyTextField}
                name="annualIncomeAmount"
                label={t('eApp:annualIncomeAmount')}
                style={eAppCommonStyles.tabletTextField}
                disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
                shouldHighlightOnUntouched={value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP && !value
                }
              />
              <Box flex={1} />
            </Row>
          </Fragment>
        )}
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
