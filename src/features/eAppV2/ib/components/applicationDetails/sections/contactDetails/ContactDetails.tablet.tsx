import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import {
  MY_OPTION_LIST,
  getCountryCodeDisplayedLabel,
  getCountryCodeValue,
  getOptionListLabel,
  getOptionListValue,
} from 'constants/optionList';
import { Box, Picker, PictogramIcon, Row, TextField } from 'cube-ui-components';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import {
  ContactDetailsForm,
  contactDetailsDefaultValue,
  contactDetailsSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/contactDetails';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useHidePreferLanguageSelection from 'hooks/useHidePreferLanguageSelection';
import { TFuncKey } from 'i18next';
import { useMemo } from 'react';
import { FieldValues, UseFormReturn, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CountryCode, PrefContactMode } from 'types/optionList';
import { ValidateOptions } from 'yup';
import { useAgentContactValidationTrigger } from './useAgentContactValidationTrigger';

interface Props extends UseFormReturn<ContactDetailsForm> {
  role: 'owner' | 'insured' | 'others';
  options?: ValidateOptions;
  primaryId: string;
  setValue: UseFormReturn<ContactDetailsForm>['setValue'];
}

export default function ContactDetailsTablet({
  role,
  control,
  options,
  primaryId,
  setValue,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList();
  const isDone = useSchemaValid(
    control,
    contactDetailsDefaultValue,
    contactDetailsSchema,
    options,
  );
  const shouldHighlight = useMemo(
    () => role === 'owner' || role === 'insured',
    [role],
  );

  const isDisabledPreferedLanguage = useHidePreferLanguageSelection();

  useAgentContactValidationTrigger({
    setValue,
    control,
  });

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.contactDetails')}
      icon={<PictogramIcon.Phone size={40} />}
      isDone={isDone}>
      <Box mt={space[5]} px={space[6]} gap={space[5]}>
        <Row gap={space[6]}>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="mobileCountryCode"
              label={t('eApp:countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="mobileNumber"
              label={t('eApp:mobileNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
              shouldHighlightOnUntouched={value => shouldHighlight && !value}
            />
          </Row>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('eApp:email')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
        </Row>

        <Row gap={space[6]}>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="homeCountryCode"
              label={t('eApp:countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="homeNumber"
              label={t('eApp:homeNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
          </Row>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="officeCountryCode"
              label={t('eApp:countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="officeNumber"
              label={t('eApp:officeNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
          </Row>
        </Row>

        {role === 'owner' && (
          <Row gap={space[6]}>
            <Input
              control={control}
              as={Autocomplete<PrefContactMode, string>}
              name="preferredCopy"
              label={t('eApp:preferredCopy')}
              data={MY_OPTION_LIST.PREF_COPY ?? []}
              getItemValue={getOptionListValue}
              getItemLabel={i => t(`eApp:${i.label as TFuncKey<'eApp'>}`)}
              style={eAppCommonStyles.tabletTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />

            <Input
              control={control}
              as={Picker}
              name="preferredLanguage"
              label={t('eApp:preferredLanguage')}
              items={optionList?.PREF_DOC_LANG.options ?? []}
              style={eAppCommonStyles.tabletTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              disabled={isDisabledPreferedLanguage}
            />
          </Row>
        )}
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
