import { NEW_NRIC, PASSPORT } from 'constants/optionList';
import Ocr, { OcrRef } from 'components/Ocr/Ocr';
import { MainPartyPersonalDetailsForm } from 'features/eAppV2/ib/validations/applicationDetails/sections/mainPartyPersonalDetails';
import { ComponentProps, useMemo, useRef, useState } from 'react';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { Gender } from 'types/person';
import { useDeleteDocument, useGetPartyFromActiveCase } from 'hooks/useParty';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import useBoundStore from 'hooks/useBoundStore';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import { PartyRole } from 'types/party';
import {
  MismatchFields,
  OcrValidationResult,
  validateOcr,
} from 'features/eAppV2/common/utils/validateOcr';
import useToggle from 'hooks/useToggle';
import { OcrResult } from 'types/ocr';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useTranslation } from 'react-i18next';

import { usePromptContext } from 'components/prompt/PromptContext';
import { getAlertActions } from 'components/prompt/PromptDialog';

export interface OcrCapture {
  base64: string;
  name: string;
  thumbnail?: string;
}

export const useOcr = ({
  partyId = '',
  getValues,
  setValue,
  shouldOverwriteOnSuccess,
  onIdUpdated,
}: {
  partyId?: string;
  setValue: UseFormSetValue<MainPartyPersonalDetailsForm>;
  getValues: UseFormGetValues<MainPartyPersonalDetailsForm>;
  shouldOverwriteOnSuccess?: boolean;
  onIdUpdated?: () => void;
}) => {
  const { caseObj } = useGetActiveCase();
  const ocrRef = useRef<OcrRef>(null);
  const ocrImage = useMemo(() => {
    const fileName = caseObj?.files?.find(
      f => f.partyId === partyId && f.fromOcr,
    )?.fileName;
    return {
      name: fileName,
      thumbnail: fileName,
    };
  }, [caseObj?.files, partyId]);

  const { reset } = useRootStackNavigation();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );
  const resetFnaStoreState = useFnaStore(state => state.resetFnaStoreState);
  const [ocrData, setOcrData] = useState<OcrResult['extract']>();
  const [ocrValidationResult, setOcrValidationResult] =
    useState<OcrValidationResult>(OcrValidationResult.Match);
  const [ocrValidationMismatchFields, setOcrValidationMismatchFields] =
    useState<MismatchFields>({});
  const ocrCapture = useRef<{
    base64: string;
    name: string;
    thumbnail?: string;
  }>();
  const [
    ocrValidationErrorVisible,
    showOcrValidationError,
    hideOcrValidationError,
  ] = useToggle();
  const [verifiedSuccessVisible, showVerifiedSuccess, hideVerifiedSuccess] =
    useToggle();

  const onDeleteOcr = () => {
    ocrCapture.current = undefined;
  };

  const { prompt } = usePromptContext();
  const { t } = useTranslation(['eApp']);

  const primaryIdType = getValues('primaryIdType');
  const onFinishOcr: ComponentProps<typeof Ocr>['onFinish'] = async (
    data,
    type,
    image,
  ) => {
    ocrCapture.current = image;
    const [result, mismatchFields] = validateOcr(
      {
        firstName: '',
        lastName: '',
        fullName: data.fullName,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
      },
      {
        firstName: '',
        lastName: '',
        fullName: getValues('fullName'),
        dateOfBirth: getValues('dob') ?? null,
        gender: getValues('gender') as Gender,
      },
    );
    setOcrData(data);
    setOcrValidationResult(result);
    setOcrValidationMismatchFields(mismatchFields);

    // handle unknown ID type
    if (
      type === 'unknown' ||
      (primaryIdType === NEW_NRIC && type === 'passport') ||
      (primaryIdType === PASSPORT && type === 'malaysia_identity_card') ||
      (primaryIdType === PASSPORT && type === 'mykid_id_card') ||
      (primaryIdType === PASSPORT && type === 'mykad_id_card')
    ) {
      const res = await prompt<{ accept: string }>({
        title: t('eApp:ocr.error.invalidPhoto.title'),
        description: t('eApp:ocr.error.invalidPhoto.desc'),
        actions: getAlertActions,
        config: {
          accept: t('eApp:ocr.error.retake'),
        },
        onClose: () => {
          onSkip();
        },
        closable: true,
      });

      if (res) {
        onRetake();
      }
      return;
    }

    // handle passport card
    if (type === 'passport' && primaryIdType === PASSPORT) {
      showVerifiedSuccess();
      return;
    }

    if (result === OcrValidationResult.Match) {
      showVerifiedSuccess();
      if (shouldOverwriteOnSuccess) {
        setValue('fullName', data.fullName || '');
        setValue(
          'gender',
          data.gender === 'MALE' ? Gender.MALE : Gender.FEMALE,
        );
      }
      setValue('primaryId', data.idNumber || '');
      setValue('primaryIdType', NEW_NRIC);
      if (data.idNumber) onIdUpdated?.();
    } else {
      showOcrValidationError();
    }
  };

  const onRetake = () => {
    ocrRef?.current?.resetAndOpen();
    hideOcrValidationError();
  };
  const onSkip = () => {
    ocrRef?.current?.reset();
    // ocrCapture.current = undefined;
    onDeleteOcr();
    hideOcrValidationError();
  };
  const onUpdate = () => {
    hideOcrValidationError();
    setValue('fullName', ocrData?.fullName || '');
    setValue('primaryId', ocrData?.idNumber || '');
    setValue('primaryIdType', NEW_NRIC);
    if (ocrData?.idNumber) onIdUpdated?.();
  };
  const onRecreateQuote = () => {
    hideOcrValidationError();
    clearActiveCase();
    resetFnaStoreState();
    reset({
      index: 0,
      routes: [{ name: 'Main' }, { name: 'Fna' }],
    });
  };

  return {
    ocrRef,
    ocrImage,
    ocrCapture,
    ocrValidationResult,
    ocrValidationMismatchFields,
    ocrValidationErrorVisible,
    onDeleteOcr,
    onFinishOcr,
    onRetake,
    onSkip,
    onUpdate,
    onRecreateQuote,
    verifiedSuccessVisible,
    showVerifiedSuccess,
    hideVerifiedSuccess,
  };
};

export const useSaveOcr = (partyId?: string, ocrCapture?: OcrCapture) => {
  const { caseObj } = useGetActiveCase();
  const { saveOcrImage, isLoading: isSavingOcrDocument } = useSaveOcrImage();
  const { mutateAsync: deleteDocument, isLoading: isDeletingDocument } =
    useDeleteDocument();
  const saveOcr = (
    role: PartyRole,
    selectingPartyId = partyId,
    selectingOcrCapture = ocrCapture,
  ) => {
    if (selectingPartyId && caseObj) {
      if (selectingOcrCapture) {
        return saveOcrImage(
          selectingPartyId,
          {
            base64: selectingOcrCapture.base64,
            name: selectingOcrCapture.name,
            thumbnail: selectingOcrCapture.thumbnail,
          },
          role,
        );
      } else {
        const ocrFileName = caseObj.files?.find(
          f => f.partyId === selectingPartyId && f.fromOcr,
        )?.fileName;
        if (ocrFileName) {
          return deleteDocument({
            caseId: caseObj.id,
            fileName: ocrFileName,
          });
        }
      }
    }
  };
  return {
    saveOcr,
    isLoading: isSavingOcrDocument || isDeletingDocument,
  };
};
