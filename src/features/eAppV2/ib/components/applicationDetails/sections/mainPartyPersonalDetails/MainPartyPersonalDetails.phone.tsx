import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import IdNumberField from 'components/IdNumberField';
import Input from 'components/Input';
import NameField from 'components/NameField';
import {
  IB_CAMPAIGN_CODES,
  IB_CAMPAIGN_CODE_VALUES,
  MALAYSIAN,
  NEW_NRIC,
  PASSPORT,
  getOptionListLabel,
  getOptionListValue,
} from 'constants/optionList';
import {
  Box,
  Picker,
  PictogramIcon,
  Row,
  TextField,
  Icon,
  LoadingIndicator,
  LargeLabel,
  Label,
  Button,
} from 'cube-ui-components';
import { useFormatDobAndAge } from 'features/eAppV2/common/hooks/useFormatDobAndAge';
import ReadOnlyField from 'features/eAppV2/common/components/ReadOnlyField';
import Ocr from 'components/Ocr/Ocr';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import {
  MainPartyPersonalDetailsForm,
  mainPartyPersonalDetailsSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/mainPartyPersonalDetails';
import { useGetOptionList, useGetTitleList } from 'hooks/useGetOptionList';
import {
  Component,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  CubeTitle,
  Ethnicity,
  Marital,
  MaritalStatus,
  Religion,
} from 'types/optionList';
import { Gender } from 'types/person';
import CheckBoxList from 'components/CheckBoxList';
import Tooltip from 'components/Tooltip';
import OcrValidationErrorDialog from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialog';
import { ApplicationDetailsPhoneForm } from 'features/eAppV2/common/components/ApplicationDetailsPhoneSection';
import { useYupResolver } from 'utils/validation/useYupResolver';
import {
  BottomSheetScrollView,
  BottomSheetScrollViewMethods,
} from '@gorhom/bottom-sheet';
import ApplicationDetailsBottomSheet from 'features/eAppV2/common/components/ApplicationDetailsBottomSheet';
import SearchableDropdown from 'components/SearchableDropdown';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { useOcr, useSaveOcr } from './useOcr';
import styled from '@emotion/native';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useCheckOcrEnable } from 'hooks/useCheckOcrEnable';
import { VerifiedSuccess } from './VerifiedSuccess';
import { UseFormReturn } from 'react-hook-form';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetPopulateCase } from 'hooks/useGetPopulateCase';
import { Case } from 'types/case';
import { PartyRole } from 'types/party';
import ApplicationsModal from '../PopulatableApplications/ApplicationsModal';
import { Platform, TouchableOpacity } from 'react-native';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { OcrValidationResult } from 'features/eAppV2/common/utils/validateOcr';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
export default function MainPartyPersonalDetailsPhone({
  value,
  onDismiss,
  onDone,
  isGettingApplications,
  role,
  nationality,
  residencyType,
}: ApplicationDetailsPhoneForm<MainPartyPersonalDetailsForm> & {
  role: 'owner' | 'insured';
  isGettingApplications?: boolean;
  nationality: string;
  residencyType: string;
}) {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { data: optionList } = useGetOptionList();

  const isEntity = useCheckEntity();

  const resolver = useYupResolver(mainPartyPersonalDetailsSchema);
  const personalDetailsForm = useEAppForm({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
    context: {
      isEntity,
    },
  });

  const gender = personalDetailsForm.watch('gender');
  const genderLabel = useMemo(
    () => optionList?.GENDER.options.find(o => o.value === gender)?.label,
    [gender, optionList?.GENDER.options],
  );

  const dob = personalDetailsForm.watch('dob');
  const formattedDobAndAge = useFormatDobAndAge(dob);

  const smokingHabit = personalDetailsForm.watch('smokingHabit');
  const smokingHabitLabel = useMemo(
    () =>
      optionList?.SMK_STATUS.options.find(o => o.value === smokingHabit)?.label,
    [optionList?.SMK_STATUS.options, smokingHabit],
  );

  const { maleTitles, femaleTitles } = useGetTitleList(
    optionList?.CUBE_TITLE.options || [],
  );

  const primaryId = personalDetailsForm.watch('primaryId');
  const primaryIdType = personalDetailsForm.watch('primaryIdType');
  const additionalIdType = personalDetailsForm.watch('additionalIdType');
  const campaignCode = personalDetailsForm.watch('campaignCode');

  useEffect(() => {
    if (campaignCode !== IB_CAMPAIGN_CODE_VALUES.JFW2024) {
      personalDetailsForm.setValue('secondaryAgentId', '');
    }
  }, [campaignCode, personalDetailsForm]);

  const relationship = personalDetailsForm.watch('relationship');
  const relationshipLabel = useMemo(
    () =>
      optionList?.RELATIONSHIP.options.find(o => o.value === relationship)
        ?.label,
    [optionList?.RELATIONSHIP.options, relationship],
  );

  const onChangeAdditionalIdType = useCallback(
    (value: string | null) => {
      if (value === additionalIdType || value === '') {
        personalDetailsForm.resetField?.('additionalId');
        personalDetailsForm.resetField?.('additionalIdType');
      }
    },
    [additionalIdType, personalDetailsForm],
  );

  const scrollRef = useRef<BottomSheetScrollViewMethods | null>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control: personalDetailsForm.control,
      schema: mainPartyPersonalDetailsSchema,
      watch: personalDetailsForm.watch,
      scrollRef: scrollRef as RefObject<Component<unknown>>,
      scrollTo: offset => scrollRef.current?.scrollTo(offset),
      context: {
        isEntity,
      },
    });

  const triggerPopulate = async () => {
    const isValidId = await personalDetailsForm.trigger(['primaryId']);
    if (isValidId) {
      if (caseObj?.prepopulatedBy) return;
      const applications = await getPopulatableApplications();
      if (applications.length) {
        setPopulatableApplications(applications);
        setAppsModalVisible(true);
      }
    }
  };

  const {
    ocrRef,
    ocrImage,
    ocrCapture,
    ocrValidationResult,
    ocrValidationMismatchFields,
    ocrValidationErrorVisible,
    onFinishOcr,
    onDeleteOcr,
    onRetake,
    onSkip,
    onUpdate,
    onRecreateQuote,
    verifiedSuccessVisible,
    hideVerifiedSuccess,
  } = useOcr({
    partyId: value.id,
    getValues: personalDetailsForm.getValues,
    setValue: personalDetailsForm.setValue,
    onIdUpdated: triggerPopulate,
  });

  const partyId = value.id;
  const { saveOcr } = useSaveOcr(partyId, ocrCapture.current);

  const isOcrEnabled = useCheckOcrEnable();

  const { caseObj } = useGetActiveCase();

  const [populatableApplications, setPopulatableApplications] = useState<
    Case[]
  >([]);
  const [appsModalVisible, setAppsModalVisible] = useState(false);

  const policyOwnerParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const {
    isGettingPopulatableApplications,
    getPopulatableApplications,
    setPopulateCaseId,
  } = useGetPopulateCase({
    ownerParty: policyOwnerParty,
    formTrigger: personalDetailsForm.trigger,
  });

  useEffect(() => {
    if (nationality === MALAYSIAN || residencyType === 'Y') {
      personalDetailsForm.setValue('primaryIdType', NEW_NRIC);
    } else {
      personalDetailsForm.setValue('primaryIdType', PASSPORT);
    }
  }, [nationality, personalDetailsForm, residencyType]);

  useEffect(() => {
    if (primaryId) {
      personalDetailsForm.trigger('primaryId');
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <>
      <ApplicationDetailsBottomSheet
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnNextIncompleteField}
        onDismiss={onDismiss}
        disabled={
          !personalDetailsForm.formState.isValid ||
          personalDetailsForm.formState.disabled
        }
        onPress={personalDetailsForm.handleSubmit(async data => {
          await saveOcr(
            role === 'owner' ? PartyRole.PROPOSER : PartyRole.INSURED,
          );
          onDone(data);
        })}
        loading={personalDetailsForm.formState.isSubmitting}>
        <Row alignItems="center" px={space[4]} gap={space[1]}>
          <PictogramIcon.ManWithShield size={40} />
          <LargeLabel fontWeight="bold" color={colors.primary}>
            {t('eApp:applicationDetails.personalDetails')}
          </LargeLabel>
        </Row>
        <BottomSheetScrollView
          ref={scrollRef}
          keyboardDismissMode="on-drag"
          contentContainerStyle={{ paddingBottom: space[4] }}
          style={{ paddingHorizontal: space[4] }}>
          {isOcrEnabled ? (
            <>
              <Ocr
                caseId={caseObj?.id}
                partyId={partyId}
                ref={ocrRef}
                defaultImage={ocrImage}
                onFinish={onFinishOcr}
                onDelete={onDeleteOcr}
                disabled={personalDetailsForm.formState.disabled}
              />
              <OcrValidationErrorDialog
                visible={ocrValidationErrorVisible}
                fields={ocrValidationMismatchFields}>
                <Button
                  style={{ alignSelf: 'stretch' }}
                  text={t('eApp:ocr.error.retake')}
                  onPress={onRetake}
                />
                {ocrValidationResult === OcrValidationResult.NameMismatch ? (
                  <Box
                    gap={space[6]}
                    pt={space[3]}
                    alignSelf="stretch"
                    alignItems="center">
                    <Button
                      style={{ alignSelf: 'stretch' }}
                      variant="secondary"
                      text={t('eApp:ocr.error.update')}
                      onPress={onUpdate}
                    />
                    <TouchableOpacity onPress={onSkip}>
                      <LargeLabel fontWeight="bold" color={colors.primary}>
                        {t('eApp:ocr.error.skip')}
                      </LargeLabel>
                    </TouchableOpacity>
                  </Box>
                ) : (
                  <Box
                    gap={space[6]}
                    pt={space[3]}
                    alignSelf="stretch"
                    alignItems="center">
                    <Button
                      style={{ alignSelf: 'stretch' }}
                      variant="secondary"
                      text={t('eApp:ocr.error.skip')}
                      onPress={onSkip}
                    />
                    <TouchableOpacity onPress={onRecreateQuote}>
                      <LargeLabel fontWeight="bold" color={colors.primary}>
                        {t('eApp:ocr.error.newApplication')}
                      </LargeLabel>
                    </TouchableOpacity>
                  </Box>
                )}
              </OcrValidationErrorDialog>
            </>
          ) : (
            <Box marginTop={space[3]} />
          )}
          <Box mt={space[1]}>
            {role === 'insured' && (
              <ReadOnlyField
                label={t('eApp:relationshipWithOwner')}
                value={relationshipLabel}
              />
            )}
          </Box>
          {role === 'owner' && (
            <Box mb={space[6]}>
              <SectionTitle fontWeight="bold">
                {t('eApp:sourceLead')}
              </SectionTitle>
              <Input
                control={personalDetailsForm.control}
                as={Picker}
                name="source"
                type="chip"
                size="large"
                items={optionList?.LEAD_SOURCE.options || []}
                containerStyle={eAppCommonStyles.pickerContainer}
                shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              />
            </Box>
          )}
          <SectionTitle fontWeight="bold">{t('eApp:identity')}</SectionTitle>
          <Box gap={space[3]}>
            <Input
              control={personalDetailsForm.control}
              as={SearchableDropdown<CubeTitle, string>}
              name="title"
              label={t('eApp:title')}
              data={gender === Gender.FEMALE ? femaleTitles : maleTitles}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />
            <Input
              control={personalDetailsForm.control}
              as={NameField}
              name="fullName"
              label={t('eApp:fullName')}
              style={eAppCommonStyles.phoneTextField}
            />
            <Input
              disabled
              control={personalDetailsForm.control}
              as={Picker}
              name="primaryIdType"
              type="chip"
              label={t('eApp:idType')}
              size="large"
              items={optionList?.ID_TYPE.options || []}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              style={eAppCommonStyles.tabletNonTextField}
              onChange={value => {
                if (value !== primaryIdType && value === NEW_NRIC) {
                  personalDetailsForm.setValue('primaryId', '');
                }
              }}
            />
            <Input
              control={personalDetailsForm.control}
              as={IdNumberField}
              name="primaryId"
              idType={primaryIdType}
              label={t('eApp:idNumber')}
              hint={primaryIdType === NEW_NRIC ? t('eApp:nricHint') : ''}
              placeholder={
                primaryIdType === NEW_NRIC ? t('eApp:nricPlaceholder') : ''
              }
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              style={eAppCommonStyles.phoneTextField}
              right={
                isGettingPopulatableApplications ? <LoadingIndicator /> : null
              }
              onBlur={triggerPopulate}
            />
            <ReadOnlyField
              label={t('eApp:dateOfBirth')}
              value={formattedDobAndAge}
            />
            <ReadOnlyField label={t('eApp:gender')} value={genderLabel} />
            <Input
              control={personalDetailsForm.control}
              as={SearchableDropdown<Marital, string>}
              name="maritalStatus"
              data={optionList?.MARITAL?.options || []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              label={t('eApp:maritalStatus')}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />
            <ReadOnlyField
              label={t('eApp:smokingHabit')}
              value={smokingHabitLabel}
              withRowHasInput
            />
            <Input
              control={personalDetailsForm.control}
              as={SearchableDropdown<Religion, string>}
              name="religion"
              data={optionList?.RELIGION?.options || []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              label={t('eApp:religion')}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />
            <Input
              control={personalDetailsForm.control}
              as={SearchableDropdown<Ethnicity, string>}
              name="race"
              data={optionList?.ETHNICITY?.options || []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              label={t('eApp:race')}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />
            {role === 'insured' && (
              <Input
                control={personalDetailsForm.control}
                as={Picker}
                name="source"
                type="chip"
                label={t('eApp:sourceLead')}
                items={optionList?.LEAD_SOURCE.options || []}
                labelStyle={eAppCommonStyles.pickerLabel}
                containerStyle={eAppCommonStyles.pickerContainer}
                shouldHighlightOnUntouched={
                  isEntity ? false : Input.defaultHighlightCheckEApp
                }
                size="large"
              />
            )}
            <Input
              control={personalDetailsForm.control}
              as={SearchableDropdown<MaritalStatus, string>}
              name="additionalIdType"
              data={optionList?.ADD_ID_TYPE?.options || []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              label={t('eApp:additionalIdType')}
              onChange={onChangeAdditionalIdType}
              style={eAppCommonStyles.phoneTextField}
            />
            <Input
              control={personalDetailsForm.control}
              as={TextField}
              name="additionalId"
              label={
                additionalIdType
                  ? t('eApp:additionalIdNumber.required')
                  : t('eApp:additionalIdNumber')
              }
              disabled={!additionalIdType}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={
                value =>
                  Boolean(additionalIdType) &&
                  Input.defaultHighlightCheckEApp(value) &&
                  Platform.OS === 'ios' // TODO: remove later
              }
            />
            {role === 'owner' && (
              <>
                <Input
                  control={personalDetailsForm.control}
                  as={
                    SearchableDropdown<{ label: string; value: string }, string>
                  }
                  name="campaignCode"
                  data={IB_CAMPAIGN_CODES}
                  getItemLabel={getOptionListLabel}
                  getItemValue={getOptionListValue}
                  label={t('eApp:campaignCode')}
                  style={eAppCommonStyles.phoneTextField}
                />
                {campaignCode === IB_CAMPAIGN_CODE_VALUES.JFW2024 && (
                  <Input
                    control={personalDetailsForm.control}
                    as={TextField}
                    name="secondaryAgentId"
                    label={t('eApp:application.owner.secondaryAgentId')}
                    style={eAppCommonStyles.phoneTextField}
                  />
                )}
              </>
            )}
          </Box>
          {(role === 'owner' || isEntity) && (
            <Box mt={space[6]}>
              <Row gap={space[1]} alignItems="center">
                <Icon.Health size={20} fill={colors.primary} />
                <Label color={colors.primary} fontWeight="bold">
                  {t('eApp:application.owner.sourceOfWealth')}
                </Label>
                <Tooltip
                  placement="overlay"
                  icon={
                    <Icon.Tooltip
                      size={20}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  }
                  title={t('eApp:application.owner.sourceOfWealth')}
                  content={t('eApp:application.owner.wealth.tooltip.sow')}
                />
              </Row>
              <Input
                control={personalDetailsForm.control}
                as={CheckBoxList<{ label: string; value: string }, string>}
                name="sourcesOfWealth"
                data={optionList?.SOURCE_OF_WEALTH?.options || []}
                getItemLabel={getOptionListLabel}
                getItemValue={getOptionListValue}
                label={t('eApp:campaignCode')}
                shouldHighlightOnUntouched={
                  (value: string[]) =>
                    role === 'owner' &&
                    (!value || value.length === 0) &&
                    Platform.OS === 'ios' // TODO: remove later
                }
              />
            </Box>
          )}
          {(role === 'owner' || isEntity) && (
            <Box mt={space[6]}>
              <Row alignItems="center" gap={space[1]}>
                <Icon.Coin2 size={20} fill={colors.primary} />
                <Label color={colors.primary} fontWeight="bold">
                  {t('eApp:application.owner.sourceOfFund')}
                </Label>
                <Tooltip
                  placement="overlay"
                  icon={
                    <Icon.Tooltip
                      size={20}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  }
                  title={t('eApp:application.owner.sourceOfFund')}
                  content={t('eApp:application.owner.wealth.tooltip.sof')}
                />
              </Row>
              <Input
                control={personalDetailsForm.control}
                as={CheckBoxList<{ label: string; value: string }, string>}
                name="sourcesOfFund"
                data={optionList?.SOURCE_OF_FUND?.options || []}
                getItemLabel={getOptionListLabel}
                getItemValue={getOptionListValue}
                label={t('eApp:campaignCode')}
                shouldHighlightOnUntouched={
                  (value: string[]) =>
                    role === 'owner' &&
                    (!value || value.length === 0) &&
                    Platform.OS === 'ios' // TODO: remove later
                }
              />
            </Box>
          )}
          <BottomSheetFooterSpace />
        </BottomSheetScrollView>
      </ApplicationDetailsBottomSheet>
      <VerifiedSuccess
        visible={verifiedSuccessVisible}
        onDismiss={hideVerifiedSuccess}
      />
      <ApplicationsModal
        isVisible={appsModalVisible}
        onClose={() => setAppsModalVisible(false)}
        onConfirm={setPopulateCaseId}
        applications={populatableApplications}
        owner={policyOwnerParty}
      />
    </>
  );
}

const SectionTitle = styled(Label)(({ theme }) => ({
  marginBottom: theme.space[3],
  color: theme.colors.primary,
}));
