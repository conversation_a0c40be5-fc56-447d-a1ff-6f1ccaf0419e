import { MALAYSIAN, MY_COUNTRY, NEW_NRIC } from 'constants/optionList';
import { NationalityDetailsForm } from 'features/eAppV2/ib/validations/applicationDetails/sections/nationalityDetails';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback, useEffect, useMemo } from 'react';
import {
  Control,
  UseFormClearErrors,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';

export const useNationality = ({
  control,
  setValue,
  clearErrors,
  primaryIdType,
}: {
  control: Control<NationalityDetailsForm>;
  setValue: UseFormSetValue<NationalityDetailsForm>;
  clearErrors: UseFormClearErrors<NationalityDetailsForm>;
  primaryIdType?: string;
}) => {
  const { data: optionList } = useGetOptionList<'ib'>();
  const nationality = useWatch({ control, name: 'nationality' });
  const nationalityLabel = useMemo(
    () =>
      optionList?.NATIONALITY.options.find(o => o.value === nationality)?.label,
    [nationality, optionList?.NATIONALITY.options],
  );

  const residencyType = useWatch({ control, name: 'residencyType' });
  const residencyTypeLabel = useMemo(
    () =>
      optionList?.MY_PR?.options.find(o => o.value === residencyType)?.label,
    [residencyType, optionList?.MY_PR?.options],
  );

  const countryOfBirth = useWatch({ control, name: 'countryOfBirth' });
  const stateOfBirth = useWatch({ control, name: 'stateOfBirth' });
  const cityOfBirth = useWatch({ control, name: 'cityOfBirth' });

  const stateList = useMemo(() => {
    const countryOption = countryOfBirth
      ? (optionList?.COUNTRY.options ?? []).find(
          i => String(i.value) === countryOfBirth,
        )
      : undefined;

    return countryOption
      ? (optionList?.STATE.options ?? []).filter(
          i => i.key === countryOption.lookupKey,
        )
      : [];
  }, [countryOfBirth, optionList]);

  const cityList = useMemo(() => {
    const stateOption = stateOfBirth
      ? (optionList?.STATE.options ?? []).find(
          i => String(i.value) === stateOfBirth,
        )
      : undefined;

    return stateOption
      ? (optionList?.CITY.options ?? []).filter(
          i => i.key === stateOption.lookupKey,
        )
      : [];
  }, [stateOfBirth, optionList]);

  const isMY = useMemo(
    () => countryOfBirth === MY_COUNTRY || countryOfBirth === '',
    [countryOfBirth],
  );

  useEffect(() => {
    if (isMY) {
      if (!cityOfBirth) {
        setValue('cityName', '');
      } else {
        const cityOption = optionList?.CITY.options.find(
          i => i.value === cityOfBirth,
        );
        setValue('cityName', cityOption?.label || '');
      }
    }
  }, [cityOfBirth, isMY, optionList, setValue]);

  const onChangeCountryOfBirth = useCallback(
    (value: string) => {
      const isMY = value === MY_COUNTRY;
      if (!isMY) {
        setValue('cityOfBirth', 'Other');
        setValue('cityName', '');
      }
      clearErrors('cityOfBirth');
      clearErrors('cityName');
      if (cityOfBirth && isMY) {
        const cityOption = optionList?.CITY.options.find(
          i => i.value === cityOfBirth,
        );
        setValue('cityName', cityOption?.label || '');
      } else {
        setValue('cityName', '');
      }
    },
    [cityOfBirth, clearErrors, optionList?.CITY.options, setValue],
  );

  const onChangeStateOfBirth = useCallback(() => {
    setValue('cityOfBirth', isMY ? '' : 'Other');
    setValue('cityName', '');
  }, [isMY, setValue]);

  return {
    countryOfBirth,
    stateOfBirth,
    cityOfBirth,
    isMY,
    nationalityLabel,
    residencyTypeLabel,
    stateList,
    cityList,
    onChangeCountryOfBirth,
    onChangeStateOfBirth,
  };
};
