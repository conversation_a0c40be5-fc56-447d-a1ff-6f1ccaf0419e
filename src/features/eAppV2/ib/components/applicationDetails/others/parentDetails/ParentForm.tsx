import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Control,
  useController,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import {
  Column,
  Picker,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import Autocomplete from 'components/Autocomplete';
import { CubeTitle } from 'types/optionList';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { calculateAge } from 'utils/helper/calculateAge';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { convertNewNRICtoDOBAndGender } from 'utils/helper/idNumberUtils';
import IdNumberField from 'components/IdNumberField';
import { NEW_NRIC } from 'constants/optionList';
import NameField from 'components/NameField';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { useGetOptionList, useGetTitleList } from 'hooks/useGetOptionList';
import { Gender } from 'types/person';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { ParentInfoForm } from 'features/eAppV2/ib/validations/applicationDetails/parent/parentInfoValidation';
import { useNricForm } from 'features/eAppV2/common/hooks/useNricForm';
import { useAutoPopulateGender } from 'features/eAppV2/common/hooks/useAutoPopulateGender';

interface Props {
  control: Control<ParentInfoForm>;
  trigger: UseFormTrigger<ParentInfoForm>;
}

const { defaultDate, maxDate, minDate } = getDateOfBirthDropdownProps();

const ParentForm = (props: Props) => {
  const { control, trigger } = props;
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList();

  const personalDob = useWatch({
    name: `dob`,
    control: control,
  });

  const primaryIdType = useWatch({
    name: `primaryIdType`,
    control: control,
  });

  const {
    field: { value: primaryId, onChange: onChangePrimaryId },
  } = useController({
    name: `primaryId`,
    control: control,
  });

  const personGender = useWatch({
    name: `gender`,
    control: control,
  });

  const personFullName = useWatch({
    name: `fullName`,
    control: control,
  });

  const personalTitle = useWatch({
    name: `title`,
    control: control,
  });

  const isValid = useMemo(() => {
    return (
      personalTitle &&
      personFullName &&
      primaryIdType &&
      primaryId &&
      personGender &&
      personalDob
    );
  }, [
    personalDob,
    personFullName,
    personGender,
    primaryId,
    primaryIdType,
    personalTitle,
  ]);

  const isNricNew = primaryIdType === NEW_NRIC;

  const personalAge = useMemo(() => {
    return personalDob ? `${calculateAge(new Date(personalDob))}` : '';
  }, [personalDob]);

  useAutoPopulateGender({
    control,
    gender: 'gender',
    title: 'title',
  });

  useNricForm({
    control,
    trigger,
    title: 'title',
    dob: 'dob',
    gender: 'gender',
    idType: 'primaryIdType',
    id: 'primaryId',
  });

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:other.parentDetails.details')}
      icon={<PictogramIcon.Lanyard size={40} />}
      isDone={Boolean(isValid)}>
      <Content>
        {/* 1 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<CubeTitle, string>}
            name="title"
            label={t('eApp:certificate.form.title')}
            data={optionList?.CUBE_TITLE.options ?? []}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={NameField}
            name={`fullName`}
            label={t('eApp:fullName')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 2 */}
        <Row flex={1} gap={space[6]}>
          <Input
            control={control}
            as={Picker}
            name="primaryIdType"
            type="chip"
            label={t('eApp:idType')}
            items={optionList?.ID_TYPE.options || []}
            labelStyle={eAppCommonStyles.pickerLabel}
            style={eAppCommonStyles.tabletNonTextField}
            onChange={value => {
              if (value !== primaryIdType && value === NEW_NRIC) {
                onChangePrimaryId('');
              }
            }}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={IdNumberField}
            idType={primaryIdType}
            name={'primaryId'}
            hint={isNricNew ? t('eApp:nricHint') : ''}
            placeholder={isNricNew ? t('eApp:nricPlaceholder') : ''}
            label={t('eApp:idNumber')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </Row>

        {/* 3 */}
        <RowBox>
          <Row flex={1} gap={space[3]}>
            <Input
              control={control}
              as={DatePickerCalendar}
              name={`dob`}
              label={t('eApp:dateOfBirth')}
              hint={t('eApp:dateFormat')}
              defaultDate={defaultDate}
              minDate={minDate}
              maxDate={maxDate}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              disabled={isNricNew}
              style={eAppCommonStyles.tabletDob}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              initialHighlight={false}
            />
            <TextField
              label={t('eApp:certificate.form.age')}
              value={personalAge}
              disabled
              style={eAppCommonStyles.tabletAge}
            />
          </Row>
          <Input
            control={control}
            as={Picker}
            name={`gender`}
            label={t('eApp:gender')}
            disabled
            items={optionList?.GENDER.options || []}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};
export default ParentForm;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  gap: space[5],
  paddingHorizontal: space[6],
  marginTop: space[5],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  flexDirection: 'row',
  flex: 1,
  gap: space[6],
}));
