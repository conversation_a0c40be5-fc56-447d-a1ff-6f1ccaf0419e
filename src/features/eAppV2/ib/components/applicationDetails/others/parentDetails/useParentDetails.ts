import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { PartyRole } from 'types/party';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useYupResolver } from 'utils/validation/useYupResolver';
import {
  ParentInfoForm,
  parentInfoSchema,
  parentInfoToParty,
  partyToParentInfo,
} from 'features/eAppV2/ib/validations/applicationDetails/parent/parentInfoValidation';
import { useSaveParty } from 'hooks/useParty';
import useToggle from 'hooks/useToggle';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
const useParentDetails = () => {
  const { caseObj } = useGetActiveCase();
  const { saveParty } = useSaveParty();

  const ownerPrimaryId = useMemo(
    () =>
      caseObj?.parties
        ?.find(({ roles }) => roles.includes(PartyRole.PROPOSER))
        ?.person?.registrations?.find(r => r.type === 'DEFAULT'),
    [caseObj?.parties],
  );
  const insuredPrimaryId = useMemo(
    () =>
      caseObj?.parties
        ?.find(({ roles }) => roles.includes(PartyRole.INSURED))
        ?.person?.registrations?.find(r => r.type === 'DEFAULT'),
    [caseObj?.parties],
  );

  const {
    control: parentFormControl,
    handleSubmit: handleSubmitParent,
    setValue: setValueParent,
    formState: { isValid: isParentInfoValid, isSubmitting: isSubmittingParent },
    watch: watchParent,
    trigger: triggerParent,
  } = useEAppForm<ParentInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => partyToParentInfo(getPartiesByRole(caseObj, PartyRole.PARENT)?.[0]),
      [], //eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver: useYupResolver(parentInfoSchema),
    context: {
      policyOwnerIdNumber: ownerPrimaryId?.id,
      policyOwnerIdType: ownerPrimaryId?.idType,
      insuredIdNumber: insuredPrimaryId?.id,
      insuredIdType: insuredPrimaryId?.idType,
    },
  });

  const [isLoading, showLoading, hideLoading] = useToggle();
  const saveParent = useCallback(async () => {
    if (!caseObj) throw new Error('missing case data while saving parent');
    showLoading();
    try {
      await handleSubmitParent(async value => {
        const id = await saveParty(parentInfoToParty(value));
        setValueParent('id', id);
      })();
    } catch {
      throw new Error('Failed to save parent');
    } finally {
      hideLoading();
    }
  }, [
    caseObj,
    handleSubmitParent,
    hideLoading,
    saveParty,
    setValueParent,
    showLoading,
  ]);

  return {
    parentFormControl,
    handleSubmitParent,
    setValueParent,
    isParentInfoValid,
    isSubmittingParent,
    watchParent,
    triggerParent,
    saveParent,
    isLoading,
  };
};

export default useParentDetails;
