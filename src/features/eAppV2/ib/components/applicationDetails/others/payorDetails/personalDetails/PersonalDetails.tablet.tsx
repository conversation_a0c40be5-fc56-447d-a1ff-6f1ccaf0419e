import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Control,
  FormState,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import {
  Box,
  Button,
  Column,
  H6,
  Icon,
  LargeLabel,
  Picker,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import Autocomplete from 'components/Autocomplete';
import DatePickerCalendar from 'components/DatePickerCalendar';
import {
  AddIDType,
  CubeTitle,
  Marital,
  Nationality,
  Relationship,
  Religion,
} from 'types/optionList';
import IdNumberField from 'components/IdNumberField';
import {
  NEW_NRIC,
  getOptionListLabel,
  getOptionListValue,
} from 'constants/optionList';
import NameField from 'components/NameField';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import usePayorPersonalDetails from 'features/eAppV2/ib/components/applicationDetails/others/payorDetails/personalDetails/usePayorPersonalDetails';
import { PayorPersonalDetailsForm } from 'features/eAppV2/ib/validations/applicationDetails/sections/payorPersonalDetails';
import Tooltip from 'components/Tooltip';
import CheckBoxList from 'components/CheckBoxList';
import { useOcr } from '../../../sections/mainPartyPersonalDetails/useOcr';
import { MainPartyPersonalDetailsForm } from 'features/eAppV2/ib/validations/applicationDetails/sections/mainPartyPersonalDetails';
import { useCheckOcrEnable } from 'hooks/useCheckOcrEnable';
import Ocr from 'components/Ocr/Ocr';
import OcrValidationErrorDialog from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialog';
import { VerifiedSuccess } from '../../../sections/mainPartyPersonalDetails/VerifiedSuccess';
import { TouchableOpacity } from 'react-native';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
interface Props {
  control: Control<PayorPersonalDetailsForm>;
  setValue: UseFormSetValue<PayorPersonalDetailsForm>;
  getValues: UseFormGetValues<PayorPersonalDetailsForm>;
  trigger: UseFormTrigger<PayorPersonalDetailsForm>;
  ocrCapture: ReturnType<typeof useOcr>['ocrCapture'];
}

const { defaultDate, maxDate, minDate } = getDateOfBirthDropdownProps();

export default function PersonalDetails(props: Props) {
  const { t } = useTranslation(['eApp']);
  const { space, sizes, colors } = useTheme();
  const { disabledForm } = useDisabledEAppForm();

  const {
    control,
    isValid,
    optionList,
    isLoadingOptionList,
    primaryIdType,
    onChangePrimaryId,
    isNricNew,
    personalAge,
    relationships,
    onChangeAdditionalIdType,
    additionalIdType,
  } = usePayorPersonalDetails(props);

  const partyId = useWatch({ name: 'id', control: props.control });
  const {
    ocrRef,
    ocrImage,
    ocrCapture,
    ocrValidationMismatchFields,
    ocrValidationErrorVisible,
    onFinishOcr,
    onDeleteOcr,
    onRetake,
    onSkip,
    onUpdate,
    onRecreateQuote,
    verifiedSuccessVisible,
    hideVerifiedSuccess,
  } = useOcr({
    partyId: partyId,
    getValues:
      props.getValues as unknown as UseFormGetValues<MainPartyPersonalDetailsForm>,
    setValue:
      props.setValue as unknown as UseFormSetValue<MainPartyPersonalDetailsForm>,
    shouldOverwriteOnSuccess: true,
  });
  const isOcrEnabled = useCheckOcrEnable();
  props.ocrCapture.current = ocrCapture?.current;

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.personalDetails')}
      icon={<PictogramIcon.Lanyard size={sizes[10]} />}
      isDone={Boolean(isValid)}>
      <Column px={space[6]} gap={space[5]}>
        {isOcrEnabled ? (
          <>
            <Ocr
              ref={ocrRef}
              defaultImage={ocrImage}
              onFinish={onFinishOcr}
              onDelete={onDeleteOcr}
              disabled={disabledForm}
            />
            <OcrValidationErrorDialog
              visible={ocrValidationErrorVisible}
              fields={ocrValidationMismatchFields}>
              <Button
                style={{ alignSelf: 'stretch' }}
                text={t('eApp:ocr.error.retake')}
                onPress={onRetake}
              />
              <Box gap={space[6]} pt={space[3]}>
                <Button
                  style={{ alignSelf: 'stretch' }}
                  variant="secondary"
                  text={t('eApp:ocr.error.update')}
                  onPress={onUpdate}
                />
                <TouchableOpacity onPress={onSkip}>
                  <LargeLabel fontWeight="bold" color={colors.primary}>
                    {t('eApp:ocr.error.skip')}
                  </LargeLabel>
                </TouchableOpacity>
              </Box>
            </OcrValidationErrorDialog>
            <VerifiedSuccess
              visible={verifiedSuccessVisible}
              onDismiss={hideVerifiedSuccess}
            />
          </>
        ) : (
          <Box marginTop={space[5]} />
        )}
        {/* 1 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<CubeTitle, string>}
            name="title"
            label={t('eApp:title')}
            data={optionList?.CUBE_TITLE.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={NameField}
            name="fullName"
            label={t('eApp:fullName')}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={Picker}
            name="primaryIdType"
            type="chip"
            label={t('eApp:idType')}
            items={optionList?.ID_TYPE.options || []}
            labelStyle={eAppCommonStyles.pickerLabel}
            style={eAppCommonStyles.tabletNonTextField}
            onChange={value => {
              if (value !== primaryIdType && value === NEW_NRIC) {
                onChangePrimaryId('');
              }
            }}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={IdNumberField}
            idType={primaryIdType}
            name="primaryId"
            label={t('eApp:idNumber')}
            hint={primaryIdType === NEW_NRIC ? t('eApp:nricHint') : ''}
            placeholder={
              primaryIdType === NEW_NRIC ? t('eApp:nricPlaceholder') : ''
            }
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 3 */}
        <RowBox>
          <Row flex={1} gap={space[3]}>
            <Input
              control={control}
              as={DatePickerCalendar}
              name="dob"
              label={t('eApp:dateOfBirth')}
              hint={t('eApp:dateFormat')}
              defaultDate={defaultDate}
              minDate={minDate}
              maxDate={maxDate}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              disabled={isNricNew}
              style={eAppCommonStyles.tabletDob}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              initialHighlight={false}
            />
            <TextField
              label={t('eApp:certificate.form.age')}
              value={personalAge}
              disabled
              style={eAppCommonStyles.tabletAge}
            />
          </Row>
          <Input
            control={control}
            as={Picker}
            name="gender"
            disabled
            label={t('eApp:gender')}
            items={optionList?.GENDER.options || []}
            style={eAppCommonStyles.tabletTextField}
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Religion, string>}
            name="religion"
            label={t('eApp:religion')}
            data={optionList?.RELIGION?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={Autocomplete<Nationality, string>}
            name="race"
            label={t('eApp:race')}
            data={optionList?.ETHNICITY?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 5 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Marital, string>}
            name="maritalStatus"
            label={t('eApp:maritalStatus')}
            data={optionList?.MARITAL?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={Autocomplete<Relationship<string, 'ib'>, string>}
            name="relationship"
            label={'Relationship'}
            data={relationships ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 6 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<{ label: string; value: string }, string>}
            name="purposeOfTransaction"
            label={t('eApp:purposeOfTransaction')}
            data={optionList?.PURPOSE_OF_TRANSACTION?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={Autocomplete<AddIDType, string>}
            name="additionalIdType"
            data={optionList?.ADD_ID_TYPE?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('eApp:additionalIdType')}
            onChange={onChangeAdditionalIdType}
            style={eAppCommonStyles.tabletNonTextField}
          />
        </RowBox>

        {/* 7 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="additionalId"
            label={
              additionalIdType
                ? t('eApp:additionalIdNumber.required')
                : t('eApp:additionalIdNumber')
            }
            disabled={!additionalIdType}
            style={eAppCommonStyles.tabletNonTextField}
            shouldHighlightOnUntouched={value =>
              Boolean(additionalIdType) &&
              Input.defaultHighlightCheckEApp(value)
            }
            initialHighlight={false}
          />
          <Box flex={1} />
        </RowBox>

        <Box>
          <Row gap={space[1]}>
            <Icon.Health fill={colors.palette.fwdDarkGreen[100]} />
            <H6 color={colors.palette.fwdDarkGreen[100]} fontWeight="bold">
              {t('eApp:application.owner.sourceOfWealth')}
            </H6>
            <Tooltip
              icon={
                <Icon.Tooltip fill={colors.palette.fwdAlternativeOrange[100]} />
              }
              content={t('eApp:application.owner.wealth.tooltip.sow')}
            />
          </Row>
          <Input
            control={control}
            as={CheckBoxList<{ label: string; value: string }, string>}
            name="sourcesOfWealth"
            data={optionList?.SOURCE_OF_WEALTH?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('eApp:campaignCode')}
            shouldHighlightOnUntouched={(value: string[]) =>
              !value || value.length === 0
            }
            initialHighlight={false}
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
            }}
            itemStyle={{
              width: '50%',
            }}
          />
        </Box>

        <Box mt={space[1]}>
          <Row gap={space[1]}>
            <Icon.Coin2 fill={colors.palette.fwdDarkGreen[100]} />
            <H6 color={colors.palette.fwdDarkGreen[100]} fontWeight="bold">
              {t('eApp:application.owner.sourceOfFund')}
            </H6>
            <Tooltip
              icon={
                <Icon.Tooltip fill={colors.palette.fwdAlternativeOrange[100]} />
              }
              content={t('eApp:application.owner.wealth.tooltip.sof')}
            />
          </Row>
          <Input
            control={control}
            as={CheckBoxList<{ label: string; value: string }, string>}
            name="sourcesOfFund"
            data={optionList?.SOURCE_OF_FUND?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('eApp:campaignCode')}
            shouldHighlightOnUntouched={(value: string[]) =>
              !value || value.length === 0
            }
            initialHighlight={false}
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
            }}
            itemStyle={{
              width: '50%',
            }}
          />
        </Box>
      </Column>
    </ApplicationDetailsTabletSectionContainer>
  );
}

const RowBox = styled(Row)(({ theme: { space } }) => ({
  flexDirection: 'row',
  gap: space[6],
}));
