import {
  Box,
  Row,
  Typography,
  PictogramIcon,
  TextField,
  Column,
  SmallLabel,
  Body,
  CurrencyTextField,
} from 'cube-ui-components';
import React, { Component, Fragment, RefObject, useRef } from 'react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import SearchableDropdown from 'components/SearchableDropdown';
import { useYupResolver } from 'utils/validation/useYupResolver';
import BottomSheetFooterSpace from 'features/eAppV2/common/components/BottomSheetFooterSpace';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import {
  getOptionListLabel,
  getOptionListValue,
  INCOME_GREATER_THAN_200K,
  NON_INCOME_OCC_GROUP,
} from 'constants/optionList';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import {
  occupationDetailsSchema,
  OccupationDetailsForm,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/occupationDetails';
import usePayorOccupationDetails from 'features/eAppV2/ib/components/applicationDetails/others/payorDetails/occupationDetails/usePayorOccupationDetails';
import { ApplicationDetailsPhoneForm } from 'features/eAppV2/common/components/ApplicationDetailsPhoneSection';
import ApplicationDetailsBottomSheet from 'features/eAppV2/common/components/ApplicationDetailsBottomSheet';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { IncomeRange, Nationality } from 'types/optionList';
import { Platform } from 'react-native';
import {
  BottomSheetScrollView,
  BottomSheetScrollViewMethods,
} from '@gorhom/bottom-sheet';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
export default function OccupationDetailsPhone({
  value,
  onDismiss,
  onDone,
}: ApplicationDetailsPhoneForm<OccupationDetailsForm>) {
  const { space, colors, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp']);

  const resolver = useYupResolver(occupationDetailsSchema);
  const form = useEAppForm<OccupationDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  const {
    control,
    optionList,
    isLoadingOptionList,
    annualIncomeValue,
    occupationDescription,
    occupationClass,
    occupationGroup,
  } = usePayorOccupationDetails(form);

  const scrollRef = useRef<BottomSheetScrollViewMethods | null>(null);

  const incompleteFields = useIncompleteFields({
    schema: occupationDetailsSchema,
    watch: form.watch,
    control: control,
    scrollRef: scrollRef as RefObject<Component<unknown>>,
    scrollTo: offset => scrollRef.current?.scrollTo(offset),
  });

  return (
    <ApplicationDetailsBottomSheet
      onDismiss={onDismiss}
      disabled={
        !form.formState.isValid || form.formState.disabled
      }
      onPress={form.handleSubmit(data => {
        onDone(data);
      })}
      totalIncompleteRequiredFields={
        incompleteFields.totalIncompleteRequiredFields
      }
      focusOnIncompleteField={incompleteFields.focusOnNextIncompleteField}>
      <Row alignItems="center" px={space[4]} gap={space[1]}>
        <PictogramIcon.Briefcase size={sizes[10]} />
        <Typography.H7 fontWeight="bold" color={colors.primary}>
          {t('eApp:applicationDetails.occupationDetails')}
        </Typography.H7>
      </Row>
      {/* Form */}
      <BottomSheetScrollView
        ref={scrollRef}
        keyboardDismissMode="on-drag"
        contentContainerStyle={{ paddingBottom: space[4] }}
        style={{ paddingHorizontal: space[4] }}>
        <Box mt={space[6]} gap={space[4]}>
          <Input
            control={control}
            as={SearchableDropdown<{ value: string; label: string }, string>}
            name="occupation"
            label={t('eApp:occupation')}
            data={optionList?.OCCUPATION.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            searchable
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Fragment key={occupationGroup}>
            {occupationClass?.label.en && (
              <Column
                gap={space[4]}
                borderRadius={borderRadius.small}
                borderWidth={1}
                borderColor={colors.palette.fwdGrey[100]}
                padding={space[4]}>
                <Column flex={1} gap={space[2]}>
                  <SmallLabel color={colors.placeholder}>
                    {t('eApp:occupationClass')}
                  </SmallLabel>
                  <Body>{occupationClass?.label.en}</Body>
                </Column>
                <Column flex={4} gap={space[2]}>
                  <SmallLabel color={colors.placeholder}>
                    {t('eApp:occupationDescription')}
                  </SmallLabel>
                  <Body>{occupationDescription}</Body>
                </Column>
              </Column>
            )}
            <Input
              control={control}
              as={TextField}
              name="nameOfBusiness"
              label={t('eApp:nameOfBusiness')}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={
                value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP &&
                  !value &&
                  Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={false}
            />
            <Input
              control={control}
              as={SearchableDropdown<Nationality, string>}
              name="natureOfWork"
              label={t('eApp:natureOfWork')}
              data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              style={eAppCommonStyles.phoneTextField}
              searchable
              shouldHighlightOnUntouched={
                value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP &&
                  !value &&
                  Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={false}
            />

            <Input
              control={control}
              as={TextField}
              name="exactDuties"
              label={t('eApp:exactDuties')}
              hint={t('eApp:exactDuties.hint')}
              style={eAppCommonStyles.phoneTextField}
              shouldHighlightOnUntouched={
                value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP &&
                  !value &&
                  Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={false}
            />
            <Input
              control={control}
              as={SearchableDropdown<IncomeRange, string>}
              name="annualIncome"
              style={eAppCommonStyles.phoneTextField}
              label={t('eApp:annualIncome')}
              data={optionList?.INCOME_RANGE.options ?? []}
              getItemLabel={getOptionListLabel}
              getItemValue={getOptionListValue}
              shouldHighlightOnUntouched={
                value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP &&
                  !value &&
                  Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={false}
            />
            <Input
              control={control}
              as={CurrencyTextField}
              name="annualIncomeAmount"
              label={t('eApp:annualIncomeAmount')}
              style={eAppCommonStyles.phoneTextField}
              disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
              shouldHighlightOnUntouched={
                value =>
                  occupationGroup !== NON_INCOME_OCC_GROUP &&
                  !value &&
                  Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={false}
            />
          </Fragment>
        </Box>
        <BottomSheetFooterSpace />
      </BottomSheetScrollView>
    </ApplicationDetailsBottomSheet>
  );
}
