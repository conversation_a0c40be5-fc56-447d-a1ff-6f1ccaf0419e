import { useMemo } from 'react';
import {
  Control,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { NationalityDetailsForm } from 'features/eAppV2/ib/validations/applicationDetails/sections/nationalityDetails';
import { MALAYSIAN, NEW_NRIC } from 'constants/optionList';

interface Props {
  control: Control<NationalityDetailsForm>;
  setValue: UseFormSetValue<NationalityDetailsForm>;
  trigger: UseFormTrigger<NationalityDetailsForm>;
  primaryIdType: string;
}

const usePayorNationalityDetails = (props: Props) => {
  const { control, setValue, trigger } = props;

  /**
   *  Watched values
   */
  const nationality = useWatch({ name: 'nationality', control });
  const residencyType = useWatch({ name: 'residencyType', control });

  /**
   *  Option lists
   */
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const residencyTypeOptions = useMemo(() => {
    return (
      optionList?.MY_PR?.options?.map(option => ({
        value: option?.value,
        label: option?.label,
      })) || []
    );
  }, [optionList?.MY_PR?.options]);

  /**
   *  Check form validity
   */
  const isValid = useMemo(
    () => nationality && residencyType,
    [nationality, residencyType],
  );

  const nationalityList = useMemo(() => {
    if (props.primaryIdType !== NEW_NRIC) {
      return optionList?.NATIONALITY.options.filter(o => o.value !== MALAYSIAN);
    }
    return optionList?.NATIONALITY.options;
  }, [optionList?.NATIONALITY.options, props.primaryIdType]);

  return {
    control,
    setValue,
    trigger,
    nationality,
    residencyType,
    residencyTypeOptions,
    isValid,
    optionList,
    isLoadingOptionList,
    nationalityList,
  };
};

export default usePayorNationalityDetails;
