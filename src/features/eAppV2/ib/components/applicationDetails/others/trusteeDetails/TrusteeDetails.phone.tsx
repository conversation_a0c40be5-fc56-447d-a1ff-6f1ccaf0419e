import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
  Box,
  Column,
  Switch,
  H6,
  H7,
  H8,
  <PERSON><PERSON>,
  <PERSON><PERSON>ogram<PERSON><PERSON>,
  SmallLabel,
  Row,
} from 'cube-ui-components';

import { PartyRole, TrusteeType } from 'types/party';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useTrusteeDetails from 'features/eAppV2/ib/components/applicationDetails/others/trusteeDetails/useTrusteeDetails';
import { useAlert } from 'hooks/useAlert';
import ApplicationDetailsPhoneSection from 'features/eAppV2/common/components/ApplicationDetailsPhoneSection';
import IndividualDetailsFormPhone from './individual/IndividualDetailsForm.phone';
import {
  TrusteeIndividualInfoForm,
  TrusteeIndividualPersonalInfoForm,
  partyToTrusteeIndividualInfo,
  trusteeIndividualPersonalInfoSchema,
  trusteeIndividualContactInfoSchema,
  TrusteeIndividualContactInfoForm,
  TrusteeIndividualAddressInfoForm,
  trusteeIndividualAddressInfoSchema,
  trusteeIndividualInfoSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/trustee/trusteeIndividualInfoValidation';
import {
  TrusteeCompanyInfoForm,
  TrusteeCompanyContactInfoForm,
  trusteeCompanyContactInfoSchema,
  TrusteeCompanyAddressInfoForm,
  trusteeCompanyAddressInfoSchema,
  trusteeCompanyPersonalInfoSchema,
  TrusteeCompanyPersonalInfoForm,
  trusteeCompanyInfoSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/trustee/trusteeCompanyInfoValidation';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import IndividualContactFormPhone from './individual/IndividualContactForm.phone';
import IndividualAddressFormPhone from './individual/IndividualAddressForm.phone';
import { partyToTrusteeCompanyInfo } from 'features/eAppV2/ib/validations/applicationDetails/trustee/trusteeCompanyInfoValidation';
import CompanyContactFormPhone from './company/CompanyContactForm.phone';
import CompanyAddressFormPhone from './company/CompanyAddressForm.phone';
import CompanyDetailsFormPhone from './company/CompanyDetailsForm.phone';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { partiesToNominationInfo } from 'features/eAppV2/ib/validations/applicationDetails/nomination/nominationInfoValidation';
import { useGetNextOtherPartyStep } from '../useOthersInfo';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm'; 
export default function TrusteeDetailsPhone() {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp', 'common']);
  const { disabledForm } = useDisabledEAppForm();
  const { caseObj } = useGetActiveCase();

  const { data: optionList } = useGetOptionList();

  const trustee = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.TRUSTEE)?.[0],
    [caseObj],
  );

  const [individualData, setIndividualData] = useState(() =>
    partyToTrusteeIndividualInfo(trustee, optionList),
  );

  const [companyData, setCompanyData] = useState(() =>
    partyToTrusteeCompanyInfo(trustee, optionList),
  );

  const nominationFormRelationships = useMemo(() => {
    return partiesToNominationInfo(
      getPartiesByRole(caseObj, PartyRole.BENEFICIARY),
      optionList,
    ).nominees.map(n => n.relationship);
  }, [caseObj, optionList]);

  const {
    hasTrustee,
    setHasTrustee,
    trusteeEnabled,
    trusteeType,
    setTrusteeType,
    saveTrustee,
    isLoading,
    populateHasTrustee,
    isTrusteeEnabledInitialized,
  } = useTrusteeDetails({
    nominationFormRelationships,
    setIndividualId: useCallback(
      (id: string) => setIndividualData(data => ({ ...data, id })),
      [],
    ),
    getIndividualFormData: useCallback(
      saveFn => saveFn(individualData),
      [individualData],
    ),
    setCompanyId: useCallback(
      (id: string) => setCompanyData(data => ({ ...data, id })),
      [],
    ),
    getCompanyFormData: useCallback(
      saveFn => saveFn(companyData),
      [companyData],
    ),
  });

  useEffect(() => {
    if (isTrusteeEnabledInitialized) {
      populateHasTrustee();
    }
  }, [populateHasTrustee, isTrusteeEnabledInitialized]);

  const { alertError } = useAlert();

  const onIndividualSectionDone = useCallback(
    (data?: Partial<TrusteeIndividualInfoForm>) => {
      setIndividualData(individualData => ({
        ...individualData,
        ...data,
      }));
    },
    [],
  );

  const onCompanySectionDone = useCallback(
    (data?: Partial<TrusteeCompanyInfoForm>) => {
      setCompanyData(individualData => ({
        ...individualData,
        ...data,
      }));
    },
    [],
  );

  const next = useEAppProgressBarStore(state => state.next);

  const onSubmit = useCallback(async () => {
    saveTrustee()
      .then(() => next(true))
      .catch(() => alertError('eApp:failedToSaveData'));
  }, [saveTrustee, next, alertError]);

  const nextStepLabel = useGetNextOtherPartyStep('trustee');

  return (
    <Box flex={1}>
      <KeyboardAwareScrollView>
        <Column padding={space[4]}>
          <H6 fontWeight="bold">
            {t('eApp:other.trusteeDetails.information')}
          </H6>
          <Row mt={space[5]}>
            <H7 fontWeight="bold">
              {t('eApp:application.appointmentOfNomineeAndTrustee.note')}
              {`: `}
              <H7>
                {t('eApp:application.appointmentOfNomineeAndTrustee.description')}
              </H7>
            </H7>
          </Row>
          <Box
            bgColor={colors.palette.white}
            borderRadius={borderRadius['medium']}
            mt={space[3]}
            px={space[2]}
            py={space[3]}>
            <H7 fontWeight="bold">{t('eApp:other.trusteeDetails.title')}</H7>
            <Box h={space[4]} />
            <Switch
              label={hasTrustee ? t('eApp:yes') : t('eApp:no')}
              checked={hasTrustee}
              onChange={value => {
                if (trusteeEnabled) {
                  setHasTrustee(value);
                } else {
                  alertError(t('eApp:other.trusteeDetails.trusteeNotAllowed'));
                }
              }}
              disabled={disabledForm}
            />

            {hasTrustee && (
              <Column gap={space[2]} mt={space[4]}>
                <SmallLabel
                  fontWeight="medium"
                  color={colors.palette.fwdGreyDarker}>
                  {t('eApp:other.trusteeDetails.type.trustee')}
                </SmallLabel>
                <Picker
                  type="chip"
                  size="large"
                  items={optionList?.TRUSTEE_TYPE.options || []}
                  value={trusteeType}
                  onChange={type => {
                    setTrusteeType(type as TrusteeType);
                  }}
                  disabled={disabledForm}
                />
              </Column>
            )}
          </Box>

          {hasTrustee && trusteeType === TrusteeType.INDIVIDUAL && (
            <Box
              bgColor={colors.palette.white}
              borderRadius={borderRadius['medium']}
              mt={space[3]}
              px={space[2]}
              py={space[3]}>
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.ManWithShield}
                name={t('eApp:applicationDetails.personalDetails')}
                done={trusteeIndividualPersonalInfoSchema.isValidSync(
                  individualData,
                )}
                form={IndividualDetailsFormPhone}
                value={individualData as TrusteeIndividualPersonalInfoForm}
                onDone={data => onIndividualSectionDone(data)}
              />
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.ActivePhoneCall}
                name={t('eApp:certificate.contactTitle')}
                done={trusteeIndividualContactInfoSchema.isValidSync(
                  individualData,
                )}
                form={IndividualContactFormPhone}
                value={individualData as TrusteeIndividualContactInfoForm}
                onDone={data => onIndividualSectionDone(data)}
              />
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.House2}
                name={t('eApp:applicationDetails.addressInformation')}
                done={trusteeIndividualAddressInfoSchema.isValidSync(
                  individualData,
                  {
                    context: { optionList },
                  },
                )}
                form={IndividualAddressFormPhone}
                value={individualData as TrusteeIndividualAddressInfoForm}
                onDone={data => onIndividualSectionDone(data)}
              />
            </Box>
          )}

          {hasTrustee && trusteeType === TrusteeType.ENTITY && (
            <Box
              bgColor={colors.palette.white}
              borderRadius={borderRadius['medium']}
              mt={space[3]}
              px={space[2]}
              py={space[3]}>
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.ManWithShield}
                name={t('eApp:other.trusteeDetails.details')}
                done={trusteeCompanyPersonalInfoSchema.isValidSync(companyData)}
                form={CompanyDetailsFormPhone}
                value={companyData as TrusteeCompanyPersonalInfoForm}
                onDone={data => onCompanySectionDone(data)}
              />
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.ActivePhoneCall}
                name={t('eApp:certificate.contactTitle')}
                done={trusteeCompanyContactInfoSchema.isValidSync(companyData)}
                form={CompanyContactFormPhone}
                value={companyData as TrusteeCompanyContactInfoForm}
                onDone={data => onCompanySectionDone(data)}
              />
              <ApplicationDetailsPhoneSection
                icon={PictogramIcon.House2}
                name={t('eApp:applicationDetails.addressInformation')}
                done={trusteeCompanyAddressInfoSchema.isValidSync(companyData, {
                  context: { optionList },
                })}
                form={CompanyAddressFormPhone}
                value={companyData as TrusteeCompanyAddressInfoForm}
                onDone={data => onCompanySectionDone(data)}
              />
            </Box>
          )}
        </Column>
      </KeyboardAwareScrollView>
      <EAppFooterPhone
        progressLock='appDetail-others-trustee'
        primaryLoading={isLoading}
        primaryDisabled={
          disabledForm ||
          hasTrustee
            ? trusteeType === TrusteeType.INDIVIDUAL
              ? !trusteeIndividualInfoSchema.isValidSync(individualData)
              : !trusteeCompanyInfoSchema.isValidSync(companyData)
            : false
        }
        onPrimaryPress={onSubmit}
        primarySubLabel={nextStepLabel}
      />
    </Box>
  );
}
