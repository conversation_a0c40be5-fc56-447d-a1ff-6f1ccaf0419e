import { useTheme } from '@emotion/react';
import {
  Box,
  ExtraLargeBody,
  Center,
  Icon,
  Typography,
} from 'cube-ui-components';
import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import Svg, { Circle, Path, Text } from 'react-native-svg';
import { StyleSheet } from 'react-native';
import styled from '@emotion/native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const R = 75;
const CIRCLE_LENGTH = R * 2 * Math.PI;
const STROKE = 24;
const CX = R + STROKE / 2;
const CY = R + STROKE / 2;
const OUTER_RADIUS = R + STROKE / 2;

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface Props {
  pieColors: string[];
  allocations: (number | string | undefined)[];
  totalPercentage: number;
  labelName?: string;
  title?: string;
  error?: string;
}

export default function CircularProgressChart(props: Props) {
  const { t } = useTranslation(['eApp']);
  const {
    pieColors,
    allocations,
    totalPercentage,
    labelName = t('eApp:other.nominationDetails.nominee'),
    title,
    error,
  } = props;
  const { space, colors } = useTheme();

  const accumulatedValues: number[] = [];
  let lastValidIdx = -1;
  for (let i = 0; i < allocations.length; i++) {
    accumulatedValues.push(Number(allocations[i] || 0));
    if (Number(allocations[i] || 0) <= 0) {
      accumulatedValues[i] = 0;
    } else {
      if (lastValidIdx >= 0) {
        accumulatedValues[i] += accumulatedValues[lastValidIdx];
      }
      lastValidIdx = i;
    }
  }
  const reversedAccumulatedValues = accumulatedValues.reverse();

  const [containerWidth, setContainerWidth] = useState(0);
  const leftEdgePos = containerWidth / 2 - OUTER_RADIUS;
  const [labelVisible, setLabelVisible] = useState(false);
  const timeout = useRef<NodeJS.Timeout>();
  useEffect(() => {
    setLabelVisible(false);
    timeout.current = setTimeout(() => setLabelVisible(true), 1000);
    () => timeout.current && clearTimeout(timeout.current);
  }, [allocations]);

  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Box mt={space[isTabletMode ? 2 : 4]}>
      <Center
        onLayout={e => setContainerWidth(e.nativeEvent.layout.width)}
        mb={space[isTabletMode ? 16 : 4]}>
        <Box w={R * 2 + STROKE} h={R * 2 + STROKE}>
          <Svg>
            {reversedAccumulatedValues.map((accumulatedValue, index) => {
              if (accumulatedValue === 0) return null;
              const value = Math.round(
                (accumulatedValue / totalPercentage) * 100,
              );
              return (
                <AnimatedItem
                  key={index}
                  value={value}
                  color={
                    pieColors[reversedAccumulatedValues.length - index - 1]
                  }
                />
              );
            })}
          </Svg>
          <Box
            style={StyleSheet.absoluteFill}
            alignItems="center"
            justifyContent="center">
            <ExtraLargeBody fontWeight="bold">
              {totalPercentage}%
            </ExtraLargeBody>
          </Box>
          {title && (
            <Box mt={space[10]} alignItems="center" justifyContent="center">
              <ExtraLargeBody fontWeight="bold">{title}</ExtraLargeBody>
            </Box>
          )}
        </Box>

        {error && (
          <AllocationErrorContainer>
            <Icon.Warning height={24} width={24} fill={colors.error} />
            <Box pr={space[4]}>
              <Typography.Body color={colors.palette.alertRed}>
                {error}
              </Typography.Body>
            </Box>
          </AllocationErrorContainer>
        )}
        {labelVisible && (
          <Box
            alignSelf="center"
            position="absolute"
            w={'100%'}
            h={2 * (R + STROKE + 45)}>
            <Svg>
              {reversedAccumulatedValues.map((accumulatedValue, index) => {
                if (accumulatedValue === 0) return null;
                const label = `${labelName} ${allocations.length - index}`;
                const value = Math.round(
                  (accumulatedValue / totalPercentage) * 100,
                );
                const preValue = Math.round(
                  (index === reversedAccumulatedValues.length - 1
                    ? 0
                    : reversedAccumulatedValues[index + 1] / totalPercentage) *
                    100,
                );
                const midAngle =
                  (2 * ((value - preValue) / 2 + preValue)) / 100;
                const sin = Math.sin(Math.PI * midAngle);
                const cos = Math.cos(Math.PI * midAngle);
                const sx = CX + OUTER_RADIUS * cos + leftEdgePos;
                const sy = CY + OUTER_RADIUS * sin + STROKE / 2 + 45;
                const mx = CX + (OUTER_RADIUS + 15) * cos + leftEdgePos;
                const my = CY + (OUTER_RADIUS + 15) * sin + STROKE / 2 + 45;
                const ex = mx + (cos >= 0 ? 1 : -1) * 1;
                const ey = my;
                const textX = CX + cos * (OUTER_RADIUS + 20) + leftEdgePos;
                const textY = CY + sin * (OUTER_RADIUS + 20) + STROKE / 2 + 45;
                const textAnchor =
                  textX > mx ? 'start' : textX < mx ? 'end' : 'middle';
                return (
                  <>
                    {isTabletMode ? (
                      <React.Fragment key={index}>
                        <Path
                          d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
                          stroke={colors.secondary}
                          fill="none"
                        />
                        <Text
                          x={textX}
                          y={textY}
                          textAnchor={textAnchor}
                          fontSize={space[3]}
                          fontFamily={'FWDCircularTT-Book'}
                          fill={colors.secondary}>
                          {label}
                        </Text>
                      </React.Fragment>
                    ) : (
                      <></>
                    )}
                  </>
                );
              })}
            </Svg>
          </Box>
        )}
      </Center>
    </Box>
  );
}

const AnimatedItem = ({ value, color }: { value: number; color: string }) => {
  const progress = useSharedValue(0);

  const animatedProps = useAnimatedProps(() => ({
    strokeDashoffset: CIRCLE_LENGTH * (1 - progress.value),
  }));

  useEffect(() => {
    progress.value = withTiming(value / 100, {
      duration: 1000,
    });
  }, [progress, value]);

  return (
    <AnimatedCircle
      cx={CX}
      cy={CY}
      r={R}
      stroke={color}
      strokeWidth={STROKE}
      strokeDasharray={CIRCLE_LENGTH}
      animatedProps={animatedProps}
      strokeLinecap={'butt'}
      fill={'transparent'}
    />
  );
};

const AllocationErrorContainer = styled.View(({ theme: { space } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingTop: space[2],
  gap: space[1],
}));
