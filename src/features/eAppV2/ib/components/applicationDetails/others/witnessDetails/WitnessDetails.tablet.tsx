import { useTheme } from '@emotion/react';
import { Control, UseFormSetValue, UseFormTrigger } from 'react-hook-form';
import {
  Box,
  Column,
  H8,
  Icon,
  LargeBody,
  Row,
  Switch,
} from 'cube-ui-components';
import React, { RefObject } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { WitnessInfoForm } from 'features/eAppV2/ib/validations/applicationDetails/witness/witnessInfoValidation';
import WitnessForm from './WitnessForm';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';

interface Props {
  control: Control<WitnessInfoForm>;
  hasWitness: boolean;
  setHasWitness: (value: boolean) => void;
  triggerWitnessValidation: UseFormTrigger<WitnessInfoForm>;
  scrollRef?: RefObject<KeyboardAwareScrollView>;
}

const WitnessDetails = (props: Props) => {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp']);
  const {
    control,
    hasWitness,
    setHasWitness,
    triggerWitnessValidation,
    scrollRef,
  } = props;
  const { disabledForm } = useDisabledEAppForm();

  return (
    <KeyboardAwareScrollView
      ref={scrollRef}
      keyboardDismissMode="interactive"
      keyboardShouldPersistTaps="handled"
      extraScrollHeight={20}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      enableAutomaticScroll={true}>
      <Column paddingY={space[6]}>
        <Row alignItems="center">
          <LargeBody fontWeight="medium">
            {t('eApp:other.witnessDetails.title')}
          </LargeBody>
          <Box ml={space[3]}>
            <Switch
              label={!hasWitness ? t('eApp:yes') : t('eApp:no')}
              checked={!hasWitness}
              onChange={value => {
                setHasWitness(!value);
              }}
              disabled={disabledForm}
            />
          </Box>
        </Row>

        {hasWitness && (
          <>
            <Box mt={space[4]}>
              <LargeBody>
                <LargeBody fontWeight="bold">
                  {t('eApp:declaration.note')}
                </LargeBody>
                {t('eApp:other.witnessDetails.note')}
              </LargeBody>
            </Box>
            <Box pr={space[6]} mt={space[4]}>
              <Row
                py={space[5]}
                px={space[6]}
                gap={space[2]}
                borderTopRightRadius={borderRadius.large}
                borderTopLeftRadius={borderRadius.large}
                backgroundColor={colors.primaryVariant3}
                alignItems="center">
                <Icon.InfoCircle size={24} fill={colors.secondary} />
                <LargeBody>{t('eApp:witnessWarning.note')}</LargeBody>
              </Row>
              <WitnessForm
                control={control}
                triggerWitnessValidation={triggerWitnessValidation}
              />
            </Box>
          </>
        )}
      </Column>
    </KeyboardAwareScrollView>
  );
};
export default WitnessDetails;
