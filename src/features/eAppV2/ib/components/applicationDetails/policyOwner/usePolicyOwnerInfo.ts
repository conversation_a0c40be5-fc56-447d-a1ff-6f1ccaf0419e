import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  PARENT_LOWER_BOUND_AGE,
  PARENT_UPPER_BOUND_AGE,
} from 'features/eAppV2/ib/constants/partyConfig';
import {
  PolicyOwnerInfoForm,
  policyOwnerInfoToParty,
} from 'features/eAppV2/ib/validations/applicationDetails/policyOwner/policyOwnerInfoValidation';
import { useAlert } from 'hooks/useAlert';
import { useCreateApplication } from 'hooks/useCreateApplication';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useDeletePartiesByRole, useSaveParty } from 'hooks/useParty';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';
import { Religion } from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';
import GATracking from 'utils/helper/gaTracking';

export const usePolicyOwnerInfo = () => {
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const next = useEAppProgressBarStore(state => state.next);
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);
  const markCompletedByKeys = useEAppProgressBarStore(
    state => state.markCompletedByKeys,
  );
  const { data: optionList } = useGetOptionList<'ib'>();
  const { caseObj } = useGetActiveCase();
  const policyOwnerParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const { alertError } = useAlert();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { deletePartiesByRole, isDeletingParties } = useDeletePartiesByRole();
  const onSave = useCallback(
    async (value: PolicyOwnerInfoForm) => {
      if (!caseObj) throw new Error('missing case data while saving PO');
      if (!policyOwnerParty)
        throw new Error('missing PO party data while saving PO');
      if (!optionList) throw new Error('missing option list while saving PO');
      try {
        const newPOParty = policyOwnerInfoToParty(
          value,
          policyOwnerParty.roles,
          optionList,
        );
        await saveParty(newPOParty, {
          overridingRoles: false,
          preventCreatingParty: true,
        });
        // saving secondary agent
        if (value.secondaryAgentId) {
          await saveApplication({
            caseId: caseObj.id,
            data: {
              ...caseObj.application,
              secondaryAgent: {
                agentCode: value.secondaryAgentId,
              },
            },
          });
        }
        // reset others completed status, base on marital status change
        if (policyOwnerParty.roles.includes(PartyRole.INSURED)) {
          // PI == PO, possibly has trustee
          if (
            newPOParty.person?.maritalStatus !==
            policyOwnerParty.person?.maritalStatus
          ) {
            markCompletedByKeys(['appDetail', 'others', 'trustee'], false);
          }
        }

        // delete parent when PO's age is out of allowed parenting age
        const age = value.dob ? calculateAge(value.dob) : 0;
        if (age < PARENT_LOWER_BOUND_AGE || age > PARENT_UPPER_BOUND_AGE) {
          await deletePartiesByRole(PartyRole.PARENT);
        }

        // delete trustee when PO's is islam
        if (value.religion === Religion.ISLAM) {
          await deletePartiesByRole(PartyRole.TRUSTEE);
        }
        if (isTabletMode) {
          nextSubgroup();
        } else {
          next(true);
        }

        GATracking.logCustomEvent('application', {
          action_type: 'eapp_submit_details',
          application_type: 'F2F',
        });
      } catch {
        alertError(t('eApp:failedToSaveData'));
      }
    },
    [
      alertError,
      caseObj,
      deletePartiesByRole,
      isTabletMode,
      markCompletedByKeys,
      next,
      nextSubgroup,
      optionList,
      policyOwnerParty,
      saveApplication,
      saveParty,
      t,
    ],
  );
  return {
    onSave,
    isLoading: isSavingParty || isSavingApplication || isDeletingParties,
  };
};
