import { Box, PictogramIcon } from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormReturn,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  CompanyAddressInformationForm, initialCompanyInfo,
  companyAddressInformationSchema
} from 'features/eAppV2/ib/validations/applicationDetails/company/companyInfoValidation';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { useTheme } from '@emotion/react';
import { AddressInformationForm } from '../../../sections/addressInformation/AddressInformation.tablet';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';

export default function BusinessRegistrationTablet({
  control,
  setValue,
  getValues,
  trigger,
}: UseFormReturn<CompanyAddressInformationForm>) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();

  const isDone = useSchemaValid(
    control,
    initialCompanyInfo,
    companyAddressInformationSchema,
  );

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.businessRegistration')}
      icon={<PictogramIcon.Briefcase size={40} />}
      isDone={isDone}>
      <Box mt={space[5]}>
        <AddressInformationForm
          addressTypes={['correspondence']}
          control={control as Control<AddressInfo>}
          setValue={setValue as UseFormSetValue<AddressInfo>}
          getValues={getValues as UseFormGetValues<AddressInfo>}
          trigger={trigger as UseFormTrigger<AddressInfo>}
          shouldHighlight
          hideLegend
        />
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
