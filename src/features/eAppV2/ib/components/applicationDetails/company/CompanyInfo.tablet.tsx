import styled from '@emotion/native';
import { Box } from 'cube-ui-components';
import React from 'react';
import TabletSections, {
  TabletSectionsItem,
} from 'features/eAppV2/common/components/TabletSections';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import {
  CompanyAddressInformationForm,
  CompanyContactDetailsForm,
  CompanyDetailsForm,
  CompanyInfoForm,
  CompanyTaxDetailsForm,
  companyInfoValidationSchema,
  partyToCompanyInfo,
} from 'features/eAppV2/ib/validations/applicationDetails/company/companyInfoValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback, useMemo, useRef, useState } from 'react';
import { UseFormReturn, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import ApplicationDetailsHeader from '../sections/ApplicationDetailsHeader';
import CompanyDetailsTablet from './sections/companyDetails/CompanyDetails.tablet';
import ContactDetailsTablet from './sections/contactDetails/ContactDetails.tablet';
import BusinessRegistrationTablet from './sections/businessRegistration/BusinessRegistration.tablet';
import TaxDetailsTablet from './sections/taxDetails/TaxDetails.tablet';
import { useTheme } from '@emotion/react';
import { useCompanyInfo } from './useCompanyInfo';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { usePopulateRegistrationDateForEntityLead } from 'features/eAppV2/ib/hooks/usePopulateRegistrationDateForEntityLead';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
export default function CompanyInfoTablet() {
  const { t } = useTranslation(['eApp']);
  const { disabledForm } = useDisabledEAppForm();
  const [activePath, setActivePath] = useState('company');
  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { space } = useTheme();
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);

  const { data: optionList } = useGetOptionList<'ib'>();
  const { caseObj } = useGetActiveCase();
  const companyParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const { data: lead } = useGetLeadByLeadId(companyParty?.leadId);

  const resolver = useYupResolver(companyInfoValidationSchema);
  const companyInfoForm = useEAppForm<CompanyInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => partyToCompanyInfo(companyParty, optionList),
      [companyParty, optionList],
    ),
    resolver,
    context: {
      optionList,
    }
  });

  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control: companyInfoForm.control,
      schema: companyInfoValidationSchema,
      watch: companyInfoForm.watch,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition?.(0, option.y || 0, true),
    });

  const { onSave } = useCompanyInfo();

  const onNext = useCallback(async () => {
    await companyInfoForm.handleSubmit(async data => {
      await onSave(data);
    })();
    nextSubgroup(true);
  }, [onSave, companyInfoForm, nextSubgroup]);

  const companyName = companyInfoForm.watch('companyName');

  usePopulateRegistrationDateForEntityLead(
    companyInfoForm as unknown as UseFormReturn<CompanyDetailsForm>,
    lead?.createdAt,
  );

  const sections = useMemo<TabletSectionsItem[]>(
    () => [
      {
        name: 'company',
        title: t('eApp:bar.company'),
        subtitle: companyName,
        content: (
          <ScrollViewContainer ref={scrollRef}>
            <ApplicationDetailsHeader
              title={t('eApp:bar.company')}
              content={t('eApp:policyOwnerInfo.declaration')}
            />
            <CompanyDetailsTablet
              {...(companyInfoForm as unknown as UseFormReturn<CompanyDetailsForm>)}
            />
            <ContactDetailsTablet
              {...(companyInfoForm as unknown as UseFormReturn<CompanyContactDetailsForm>)}
            />
            <BusinessRegistrationTablet
              {...(companyInfoForm as unknown as UseFormReturn<CompanyAddressInformationForm>)}
            />
            <TaxDetailsTablet
              {...(companyInfoForm as unknown as UseFormReturn<CompanyTaxDetailsForm>)}
            />
            <Box h={space[6]} />
          </ScrollViewContainer>
        ),
      },
    ],
    [t, companyName, companyInfoForm, space],
  );

  return (
    <>
      <TabletSections
        activePath={activePath}
        setActivePath={setActivePath}
        items={sections}
      />
      <EAppFooterTablet
        progressLock="appDetail-company"
        primaryDisabled={!companyInfoForm.formState.isValid || disabledForm}
        primaryLabel={t('eApp:next')}
        onPrimaryPress={onNext}
        primaryLoading={companyInfoForm.formState.isSubmitting}
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
      />
    </>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
