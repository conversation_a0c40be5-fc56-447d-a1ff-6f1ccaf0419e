import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  InsuredInfoForm,
  insuredInfoToParty,
  partyToInsuredInfo,
} from 'features/eAppV2/ib/validations/applicationDetails/insured/insuredInfoValidation';
import { useAlert } from 'hooks/useAlert';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole } from 'types/party';

export const useInsuredInfo = () => {
  const { t } = useTranslation(['eApp']);
  const next = useEAppProgressBarStore(state => state.next);
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { alertError } = useAlert();
  const { data: optionList } = useGetOptionList<'ib'>();
  const { caseObj } = useGetActiveCase();
  const insured = useMemo(
    () => getPartiesByRole(caseObj, PartyRole.INSURED)?.[0],
    [caseObj],
  );
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const onSave = useCallback(
    async (value: InsuredInfoForm) => {
      if (!caseObj) throw new Error('missing case data while saving PI');
      if (!insured) throw new Error('missing PI party data while saving PI');
      if (!optionList) throw new Error('missing option list while saving PI');
      try {
        await saveParty(insuredInfoToParty(value, insured.roles, optionList), {
          overridingRoles: false,
          preventCreatingParty: true,
        });
        if (isTabletMode) {
          nextSubgroup();
        } else {
          next(true);
        }
      } catch {
        alertError(t('eApp:failedToSaveData'));
      }
    },
    [
      caseObj,
      insured,
      optionList,
      saveParty,
      isTabletMode,
      nextSubgroup,
      next,
      alertError,
      t,
    ],
  );
  return {
    onSave,
    isLoading: isSavingParty,
  };
};
