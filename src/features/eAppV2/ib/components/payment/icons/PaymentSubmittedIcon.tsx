import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
const PaymentSubmittedIcon = (props: SvgIconProps) => (
  <Svg width={80} height={80} fill="none" {...props}>
    <Path
      fill="#F3BB90"
      fillRule="evenodd"
      d="m34.277 62.051-3.45-3.457-8.053 8.073-8.054-8.073-8.053 8.073V5l8.053 8.073L22.774 5l8.054 8.073L38.882 5l8.053 8.073L55 5V35.016c-.276-.01-.554-.016-.833-.016-11.506 0-20.834 9.327-20.834 20.833 0 2.166.33 4.255.944 6.218Z"
      clipRule="evenodd"
    />
    <Path
      stroke="#E87722"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={4}
      d="M54.167 76.667C65.672 76.667 75 67.339 75 55.833S65.672 35 54.167 35c-11.506 0-20.834 9.327-20.834 20.833 0 11.506 9.328 20.834 20.834 20.834Z"
    />
    <Path
      fill="#fff"
      d="M41.667 23.334H18.333a3.333 3.333 0 0 0 0 6.667h23.334a3.333 3.333 0 0 0 0-6.667ZM25 38.334h-6.667a3.333 3.333 0 0 0 0 6.667H25a3.333 3.333 0 0 0 0-6.667Z"
    />
    <Path
      fill="#183028"
      d="M61.667 60.016a5.89 5.89 0 0 1-1.653 4.079 6.328 6.328 0 0 1-3.264 1.812v1.206c0 1.371-1.16 2.486-2.583 2.486-1.424 0-2.582-1.115-2.582-2.486V66.05H50.48c-1.425 0-2.583-1.115-2.583-2.485 0-1.37 1.158-2.485 2.583-2.485h4.917c.609 0 1.106-.477 1.106-1.064 0-.586-.497-1.065-1.106-1.065h-2.459c-3.458 0-6.271-2.706-6.271-6.033 0-1.514.587-2.963 1.654-4.08a6.327 6.327 0 0 1 3.264-1.812V45.82c0-1.37 1.158-2.485 2.582-2.485 1.423 0 2.583 1.115 2.583 2.485v1.065h1.105c1.424 0 2.583 1.114 2.583 2.485 0 1.37-1.159 2.485-2.583 2.485h-4.917c-.61 0-1.106.477-1.106 1.064 0 .586.497 1.063 1.106 1.063h2.459c1.673 0 3.247.629 4.432 1.77 1.186 1.14 1.839 2.655 1.838 4.265Z"
    />
  </Svg>
);
export default PaymentSubmittedIcon;
