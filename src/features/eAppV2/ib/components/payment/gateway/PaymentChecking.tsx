import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import CircleLoadingIndicator from './CircleLoadingIndicator';

const PaymentChecking = () => {
  const { colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  return (
    <>
      <Box
        flex={1}
        alignContent="center"
        justifyContent="center"
        alignItems="center"
        backgroundColor={colors.surface}>
        <CircleLoadingIndicator
          isLoading={true}
          title={t('eApp:payment.checking')}
        />
      </Box>
    </>
  );
};
export default PaymentChecking;
