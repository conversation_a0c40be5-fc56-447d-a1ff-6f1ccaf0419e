import { memo } from 'react';
import PaymentCheckingStatus from './gateway/PaymentCheckingStatus';
import PaymentStatus from './gateway/PaymentStatus';
import {
  PaymentMethod,
  SubPaymentMethod,
} from './form/methods/paymentMethodTypes';
import InAppPaymentModal from './gateway/InAppPaymentModal';
import PaymentFormTablet from './form/PaymentForm.tablet';
import EMandatePaymentTablet from './eMandate/EMandatePayment.tablet';
import PaymentSlip from './slip/PaymentSlip.tablet';
import usePayment from './usePayment';

const PaymentTablet = memo(function Payment() {
  const {
    isShowingInAppPayment,
    showInAppPayment,
    hideInAppPayment,
    isShowingEMandatePayment,
    showEMandatePayment,
    paymentMethod,
    setPaymentMethod,
    subPaymentMethod,
    setSubPaymentMethod,
    setInitialPayment,
    paymentResponse,
    setPaymentResponse,
    paymentTransactionId,
    paymentUrl,
    onBackPress,
    initialPayment,
  } = usePayment();

  // render PaymentSlip if paymentMethod == SLIP
  if (paymentMethod === PaymentMethod.SLIP) {
    return <PaymentSlip onBackPress={onBackPress} />;
  }

  // render EMandatePayment if isShowingEMandatePayment == true
  if (isShowingEMandatePayment) {
    return <EMandatePaymentTablet />;
  }

  // if has status response, show payment status
  if (paymentResponse) {
    return (
      <PaymentStatus
        info={paymentResponse}
        paymentMethod={paymentMethod}
        onBackPress={onBackPress}
        onStartEMandate={showEMandatePayment}
      />
    );
  }

  // if don't have status response
  // and has payment method + transaction id
  if (paymentMethod && paymentTransactionId) {
    // show in app payment if in-app payment is selected
    if (
      subPaymentMethod === SubPaymentMethod.IN_APP &&
      isShowingInAppPayment &&
      paymentUrl
    ) {
      return (
        <InAppPaymentModal
          paymentUrl={paymentUrl}
          visible
          onDismiss={hideInAppPayment}
        />
      );
    }
    // otherwise show checking status
    return (
      <PaymentCheckingStatus
        paymentLink={initialPayment?.paymentUrl || ''}
        paymentTransactionId={paymentTransactionId}
        onPaymentStatusResponse={setPaymentResponse}
        onBackPress={onBackPress}
        isInAppPayment={subPaymentMethod === SubPaymentMethod.IN_APP}
        onPressInAppPayment={showInAppPayment}
      />
    );
  }

  // render default payment form if no payment method + transaction id
  return (
    <PaymentFormTablet
      paymentMethod={paymentMethod}
      setPaymentMethod={setPaymentMethod}
      subPaymentMethod={subPaymentMethod}
      setSubPaymentMethod={setSubPaymentMethod}
      setInitialPayment={setInitialPayment}
      setPaymentResponse={setPaymentResponse}
      onShowInAppPayment={showInAppPayment}
    />
  );
});

export default PaymentTablet;
