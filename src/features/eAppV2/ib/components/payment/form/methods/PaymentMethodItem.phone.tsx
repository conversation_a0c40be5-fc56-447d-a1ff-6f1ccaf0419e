import { useTheme } from '@emotion/react';
import React, { useCallback } from 'react';
import { Pressable } from 'react-native';

import { Body, Box, LargeLabel, RadioButton, Row } from 'cube-ui-components';
import {
  PaymentMethod,
  PaymentMethodsProps,
  SubPaymentMethod,
} from './paymentMethodTypes';

type PaymentMethodItemProps = PaymentMethodsProps & {
  setPaymentMethod: (method: PaymentMethod | null) => void;
  selectedPaymentMethod: PaymentMethod | null;
  selectedSubPaymentMethod: SubPaymentMethod | null;
  marginBottom?: number;
};

const PaymentMethodItem = ({
  desc,
  label,
  value,
  icon,
  hidden,
  disabled,
  selectedPaymentMethod,
  selectedSubPaymentMethod,
  setPaymentMethod,
}: PaymentMethodItemProps) => {
  const { space, sizes, colors, borderRadius } = useTheme();

  const setPayment = useCallback(() => {
    setPaymentMethod(value as PaymentMethod);
  }, [setPaymentMethod, value]);

  if (hidden) return <React.Fragment key={value} />;
  const isSelected =
    selectedPaymentMethod === value || selectedSubPaymentMethod === value;
  return (
    <Pressable disabled={disabled} onPress={setPayment}>
      <Row alignItems="flex-start">
        <RadioButton
          selected={isSelected}
          onSelect={setPayment}
          value={value}
          disabled={disabled}
        />
        <Box marginLeft={sizes[2]} flex={1}>
          <LargeLabel
            fontWeight="bold"
            color={disabled ? colors.palette.fwdGrey[100] : colors.secondary}>
            {label}
          </LargeLabel>
          <Body color={colors.placeholder}>{desc}</Body>
        </Box>
        <Box marginLeft={sizes[2]}>{icon}</Box>
      </Row>
    </Pressable>
  );
};

export default PaymentMethodItem;
