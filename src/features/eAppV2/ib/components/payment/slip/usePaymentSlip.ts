import { useMemo, useRef, useState } from 'react';
import { useController, useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useCreateApplication } from 'hooks/useCreateApplication';
import useBoundStore from 'hooks/useBoundStore';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { formatNewNricNumber } from 'utils/helper/idNumberUtils';
import { useAlert } from 'hooks/useAlert';
import { NEW_NRIC } from 'constants/optionList';
import {
  PaymentSlipForm,
  caseToPaymentSlip,
  paymentSlipSchema,
  paymentSlipToApplication,
} from 'features/eAppV2/ib/validations/paymentSlipValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import { PartyRole } from 'types/party';
import { DocumentType } from 'types/document';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';

const usePaymentSlip = () => {
  const [isGoingBack, setIsGoingBack] = useState(false);
  const { t } = useTranslation(['eApp']);
  const { alertError } = useAlert();
  const { caseObj } = useGetActiveCase();
  const quotation = useSelectedQuotation();
  const caseId = useBoundStore(state => state.case.caseId);
  const { mutateAsync: saveApplication } = useCreateApplication();

  const payor = useMemo(
    () => caseObj?.parties?.find(p => isRole(p, PartyRole.PAYER)),
    [caseObj?.parties],
  );

  const {
    control: control,
    handleSubmit: handleFormSubmit,
    getValues,
    watch,
    formState: { isValid: isPaymentSlipValid, isSubmitting },
    trigger: triggerValidation,
  } = useForm<PaymentSlipForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => caseToPaymentSlip(caseObj, quotation, payor),
      [], //eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver: useYupResolver(paymentSlipSchema),
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
    useIncompleteFields({
      control,
      watch,
      schema: paymentSlipSchema,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
    });

  const fullNamePayor = useWatch({
    name: 'fullNamePayor',
    control,
  });

  const applicationNumber = useWatch({
    name: 'applicationNumber',
    control,
  });

  const quotationNumber = useWatch({
    name: 'quotationNumber',
    control,
  });

  const idNumber = useWatch({
    name: 'idNumber',
    control,
  });

  const initialPremiumAmount = useWatch({
    name: 'initialPremiumAmount',
    control,
  });

  const idType = useMemo(
    () => payor?.person?.registrations?.find(r => r.type === 'DEFAULT')?.idType,
    [payor?.person?.registrations],
  );

  const isDocumentValid = useMemo(
    () =>
      (caseObj?.files?.filter(f => f.docType === DocumentType.InSlipCheque)
        .length ?? 0) > 0,
    [caseObj?.files],
  );
  const [isOverPay, setIsOverPay] = useState<boolean>(false);
  const [isShortPay, setIsShortPay] = useState<boolean>(false);
  const isSubmitDisabled =
    !isPaymentSlipValid ||
    !isDocumentValid ||
    isShortPay ||
    (isOverPay && watch('isForMultipleApplication') === '');

  const onSubmit = async () => {
    try {
      await handleFormSubmit(async data => {
        const over =
          Number.parseInt(data.paymentAmount) >
          Number.parseInt(data.initialPremiumAmount);
        const short =
          Number.parseInt(data.paymentAmount) <
          Number.parseInt(data.initialPremiumAmount);
        setIsOverPay(over);
        setIsShortPay(short);
        if (short) {
          return;
        }
        if (over && data.isForMultipleApplication === '') {
          return;
        }
        if (!caseObj || !caseId) return;
        await saveApplication({
          caseId,
          data: {
            ...caseObj.application,
            ...paymentSlipToApplication(data, quotation),
          },
        });
      })();
    } catch {
      alertError(t('eApp:failedToSaveData'));
    }
  };

  const {
    fieldState: { error: paymentAmountError },
  } = useController({
    name: 'paymentAmount',
    control,
    shouldUnregister: false,
  });

  const identificationNumber = useMemo(() => {
    return idType === NEW_NRIC ? formatNewNricNumber(idNumber) : idNumber;
  }, [idNumber, idType]);

  const totalPaymentSlipIncompleteFields =
    totalIncompleteRequiredFields + (isDocumentValid ? 0 : 1);

  return {
    caseObj,
    isGoingBack,
    setIsGoingBack,
    scrollRef,
    control,
    setIsShortPay,
    isShortPay,
    getValues,
    setIsOverPay,
    fullNamePayor,
    applicationNumber,
    quotationNumber,
    identificationNumber,
    paymentAmountError,
    initialPremiumAmount,
    isOverPay,
    isSubmitting,
    onSubmit,
    isSubmitDisabled,
    totalPaymentSlipIncompleteFields,
    focusOnNextIncompleteField,
    triggerValidation
  };
};

export default usePaymentSlip;
