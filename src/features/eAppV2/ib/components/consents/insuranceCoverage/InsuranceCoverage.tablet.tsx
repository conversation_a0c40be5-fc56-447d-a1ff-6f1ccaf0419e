import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, H6, LargeBody, PictogramIcon, Row } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';

export default function InsuranceCoverageTablet() {
  const { t } = useTranslation(['eApp']);
  const { space, borderRadius, colors } = useTheme();

  return (
    <Box
      backgroundColor={colors.background}
      borderRadius={borderRadius.large}
      my={space[6]}
      mr={space[8]}
      p={space[6]}>
      <Row mb={space[5]} gap={space[2]} alignItems="center">
        <PictogramIcon.DocumentSign2 size={40} />
        <H6 fontWeight="bold">{t('eApp:insuranceCoverage')}</H6>
      </Row>
      <Box gap={space[6]}>
        <LargeBody>{t('eApp:insuranceCoverage.1')}</LargeBody>
        <LargeBody>{t('eApp:insuranceCoverage.2')}</LargeBody>
        <Row gap={space[2]}>
          <LargeBody>{t('eApp:insuranceCoverage.a')}</LargeBody>
          <Paragraph>{t('eApp:insuranceCoverage.2.a')}</Paragraph>
        </Row>
        <Row gap={space[2]}>
          <LargeBody>{t('eApp:insuranceCoverage.b')}</LargeBody>
          <Paragraph>{t('eApp:insuranceCoverage.2.b')}</Paragraph>
        </Row>
        <Row gap={space[2]}>
          <LargeBody>{t('eApp:insuranceCoverage.c')}</LargeBody>
          <Paragraph>{t('eApp:insuranceCoverage.2.c')}</Paragraph>
        </Row>
        <Row gap={space[2]}>
          <LargeBody>{t('eApp:insuranceCoverage.d')}</LargeBody>
          <Paragraph>{t('eApp:insuranceCoverage.2.d')}</Paragraph>
        </Row>
        <LargeBody>{t('eApp:insuranceCoverage.3')}</LargeBody>
        <LargeBody>{t('eApp:insuranceCoverage.4')}</LargeBody>
      </Box>
    </Box>
  );
}

const Paragraph = styled(LargeBody)({
  flex: 1,
});
