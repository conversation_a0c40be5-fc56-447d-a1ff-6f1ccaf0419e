import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import TextMore from 'components/TextMore';
import { Box, Checkbox, H6, H7, LargeBody, Row } from 'cube-ui-components';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { PdpaNotice } from 'features/eAppV2/common/components/pdpaNotice/PdpaNotice';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { IB_PDPA_LINK } from 'features/eAppV2/ib/constants/consent';
import { useAlert } from 'hooks/useAlert';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import useToggle from 'hooks/useToggle';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, StyleSheet } from 'react-native';

const listOfIndexes = [1, 2, 3, 4, 5] as const;
export default function PdpPhone() {
  const { t } = useTranslation(['eApp', 'common']);
  const { disabledForm } = useDisabledEAppForm();
  const { space, colors, borderRadius } = useTheme();
  const completedMap = useEAppProgressBarStore(state => state.completedMap);
  const next = useEAppProgressBarStore(state => state.next);
  const [showMore, setShowMore] = useState(false);
  const { caseObj } = useGetActiveCase();
  const [clippedText, setClippedText] = useState('');
  const [checkedMarketingTerms, setCheckedMarketingTerms] = useState(() =>
    Boolean(caseObj?.application?.proposerConsent?.[0]?.pdpCheck),
  );
  const [reviewedPdpa, setReviewedPdpa] = useState(() =>
    Object.keys(completedMap).includes('consents'),
  );
  const [pdpaNoticeVisible, showPdpaNotice, hidePdpaNotice] = useToggle();

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { alertError } = useAlert();

  const onAgree = useCallback(async () => {
    hidePdpaNotice();
    setReviewedPdpa(true);
    if (!caseObj) return;

    saveApplication({
      caseId: caseObj.id,
      data: {
        ...caseObj.application,
        proposerConsent: (caseObj.application?.proposerConsent || []).map(
          consent => ({
            ...consent,
            pdpCheck: checkedMarketingTerms,
          }),
        ),
      },
    })
      .then(() => next())
      .catch(() => alertError(t('eApp:failedToSaveData')));
  }, [checkedMarketingTerms, hidePdpaNotice]);

  return (
    <Box flex={1}>
      <ScrollView contentContainerStyle={{ paddingBottom: space[4] }}>
        <Box mx={space[4]} mt={space[4]} mb={space[3]}>
          <H6 fontWeight="bold">{t('eApp:pdp')}</H6>
        </Box>
        <Container>
          {showMore ? (
            listOfIndexes.map(index => {
              const indexWithFormat = `${index}.`;
              return (
                <Row mb={space[5]} gap={space[2]}>
                  <LargeBody>{indexWithFormat}</LargeBody>
                  <Box flex={1}>
                    <LargeBody>
                      {t(`eApp:pdp.${index}`)}{' '}
                      {index === listOfIndexes.length && (
                        <LargeBody
                          suppressHighlighting
                          onPress={() => {
                            setShowMore(false);
                          }}
                          fontWeight="bold"
                          color={colors.palette.fwdAlternativeOrange[100]}>
                          {t('common:close')}
                        </LargeBody>
                      )}
                    </LargeBody>
                  </Box>
                </Row>
              );
            })
          ) : (
            <Row mb={space[5]} gap={space[2]}>
              <LargeBody>{`1.`}</LargeBody>
              <Box flex={1}>
                <LargeBody
                  onTextLayout={
                    clippedText
                      ? undefined
                      : e => {
                          const { lines } = e.nativeEvent;
                          const joinedText = lines
                            .splice(0, 10)
                            .map(line => line.text)
                            .join('');
                          if (joinedText.length < t(`eApp:pdp.1`).length) {
                            const clippedText =
                              joinedText.substring(0, joinedText.length - 10) +
                              '... ';
                            setClippedText(clippedText);
                          }
                        }
                  }>
                  {clippedText ? clippedText : t(`eApp:pdp.1`)}
                  <LargeBody
                    suppressHighlighting
                    onPress={() => {
                      setShowMore(true);
                    }}
                    fontWeight="bold"
                    color={colors.palette.fwdAlternativeOrange[100]}>
                    {t('common:more')}
                  </LargeBody>
                </LargeBody>
              </Box>
            </Row>
          )}

          <LargeBody>
            {t('eApp:pdp.agreement.1')}
            <HyperLink
              fontWeight="bold"
              suppressHighlighting
              onPress={showPdpaNotice}
              color={colors.palette.fwdAlternativeOrange[100]}>
              {t('eApp:pdp.notice')}
            </HyperLink>
            {t('eApp:pdp.agreement.2')}
          </LargeBody>
          <Box gap={space[3]} mt={space[5]}>
            <H7 fontWeight="bold">{t('eApp:pdp.marketing')}</H7>
            <LargeBody>{t('eApp:pdp.marketing.description')}</LargeBody>
            <Box
              mt={space[3]}
              padding={space[3]}
              border={1}
              borderColor={
                checkedMarketingTerms
                  ? colors.primary
                  : colors.palette.fwdGrey[100]
              }
              backgroundColor={
                checkedMarketingTerms
                  ? colors.primaryVariant3
                  : colors.background
              }
              borderRadius={borderRadius.medium}>
              <Checkbox
                checked={checkedMarketingTerms}
                label={t('eApp:pdp.marketing.checkbox')}
                labelStyle={styles.checkboxLabel}
                style={styles.checkbox}
                onChange={setCheckedMarketingTerms}
                disabled={disabledForm}
              />
            </Box>
          </Box>
          <PdpaNotice
            visible={pdpaNoticeVisible}
            url={IB_PDPA_LINK}
            nextScreenText={t('eApp:bar.pre-contractualDutyOfDisclosure')}
            onDismiss={hidePdpaNotice}
            onAgree={onAgree}
            reviewed={reviewedPdpa}
          />
        </Container>
      </ScrollView>
      <EAppFooterPhone
        onPrimaryPress={showPdpaNotice}
        primarySubLabel={t('eApp:bar.pre-contractualDutyOfDisclosure')}
        primaryLoading={isSavingApplication} 
        primaryDisabled={disabledForm}
      />
    </Box>
  );
}

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  return {
    borderRadius: borderRadius.large,
    padding: space[4],
    backgroundColor: colors.background,
    marginHorizontal: space[4],
    overflow: 'hidden',
  };
});

const HyperLink = styled(LargeBody)({
  textDecorationLine: 'underline',
});

const styles = StyleSheet.create({
  checkbox: {
    alignItems: 'flex-start',
    marginTop: 4,
  },
  checkboxLabel: {
    flex: 1,
  },
});
