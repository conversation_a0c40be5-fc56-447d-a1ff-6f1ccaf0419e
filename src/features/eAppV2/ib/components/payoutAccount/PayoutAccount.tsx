import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useToggle from 'hooks/useToggle';
import { TFunction } from 'i18next';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import {
  PayoutAccountForm,
  fromDirectCredit,
  payoutAccountSchema,
  toDirectCredit,
} from '../../validations/payoutAccountValidation';
import PayoutAccountPhone from './PayoutAccount.phone';
import PayoutAccountTablet from './PayoutAccount.tablet';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';

export default function PayoutAccount() {
  const { t } = useTranslation(['eApp']);
  const [termConditionVisible, showTermCondition, hideTermCondition] =
    useToggle();
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { alertError } = useAlert();
  const { caseObj } = useGetActiveCase();
  const policyOwnerParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const { data: optionList } = useGetOptionList();
  const { mutateAsync: saveApplication } = useCreateApplication();

  const isEntity = useCheckEntity();
  const accountHolderName = isEntity
    ? policyOwnerParty?.entity?.name
    : policyOwnerParty?.person?.name.fullName;

  const {
    control,
    setValue,
    watch,
    formState: { isValid, isSubmitting },
    handleSubmit,
    trigger,
  } = useEAppForm<PayoutAccountForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...fromDirectCredit(caseObj?.application?.directCredit),
        accountHolderName: accountHolderName || '',
      }),
      [], // eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver: useYupResolver(payoutAccountSchema),
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control,
      schema: payoutAccountSchema,
      watch: watch,
      scrollRef,
      scrollTo: ({ x, y }) =>
        scrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  useEffect(() => {
    if (accountHolderName) {
      setValue('accountHolderName', accountHolderName);
    }
  }, [accountHolderName, setValue]);

  const selectedBank = watch('bankName');

  useEffect(() => {
    const length = optionList?.DC_BANKLIST.options.find(
      o => o.value === selectedBank,
    )?.messageParam;
    if (length) {
      setValue('accountLengths', Array.isArray(length) ? length : [length]);
      trigger('accountNumber');
    } else {
      setValue('accountLengths', []);
    }
  }, [optionList?.DC_BANKLIST.options, selectedBank, setValue, trigger]);

  const onNext = useCallback(() => {
    if (!agentId || !caseObj) return;
    handleSubmit(async value => {
      await saveApplication({
        caseId: caseObj.id,
        data: {
          ...caseObj.application,
          directCredit: toDirectCredit(value, agentId),
        },
      });
      nextGroup();
    })().catch(() => alertError(t('eApp:failedToSaveData')));
  }, [
    agentId,
    alertError,
    caseObj,
    handleSubmit,
    nextGroup,
    saveApplication,
    t,
  ]);

  const accountNumber = watch('accountNumber');
  const accountLengths = watch('accountLengths');
  const accountNumberHint = useMemo(
    () => getAccountLengthHint(accountNumber, accountLengths as number[], t),
    [accountLengths, accountNumber, t],
  );
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <PayoutAccountTablet
      control={control}
      scrollRef={scrollRef}
      accountNumberHint={accountNumberHint}
      showTermCondition={showTermCondition}
      termConditionVisible={termConditionVisible}
      hideTermCondition={hideTermCondition}
      onPress={onNext}
      isValid={isValid}
      isSubmitting={isSubmitting}
      focusOnNextIncompleteField={focusOnNextIncompleteField}
      totalIncompleteRequiredFields={totalIncompleteRequiredFields}
    />
  ) : (
    <PayoutAccountPhone
      control={control}
      scrollRef={scrollRef}
      accountNumberHint={accountNumberHint}
      showTermCondition={showTermCondition}
      termConditionVisible={termConditionVisible}
      hideTermCondition={hideTermCondition}
      onPress={onNext}
      isValid={isValid}
      isSubmitting={isSubmitting}
      focusOnNextIncompleteField={focusOnNextIncompleteField}
      totalIncompleteRequiredFields={totalIncompleteRequiredFields}
    />
  );
}

const getAccountLengthHint = (
  accountNumber: string,
  accountLengths: number[] = [],
  t: TFunction<['eApp']>,
) => {
  switch (accountLengths.length) {
    case 0:
      return '';
    case 1:
      return t('eApp:invalidAccountNumberLengthMin', {
        minLength: accountLengths[0],
      });
      break;
    case 2:
      return t('eApp:invalidAccountNumberLengthRange', {
        minLength: accountLengths[0],
        maxLength: accountLengths[1],
      });
      break;
    default:
      return t('eApp:invalidAccountNumberLengthStops', {
        stops: accountLengths.join(', '),
      });
  }
};
