import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { Party, PartyRole } from 'types/party';
import { DocumentReview } from 'features/eAppV2/common/types/reviewTypes';
import DocumentsSummaryBase from 'features/eAppV2/common/components/review/documentSummary/DocumentsSummaryBase';

export default function DocumentsSummary() {
  const { t } = useTranslation(['eApp']);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const data = useMemo<DocumentReview[]>(() => {
    const owner = caseObj?.parties?.find(p =>
      p.roles.includes(PartyRole.PROPOSER),
    );
    const insured = caseObj?.parties?.find(
      p =>
        p.roles.includes(PartyRole.INSURED) &&
        !p.roles.includes(PartyRole.PROPOSER),
    );

    const payor = caseObj?.parties?.find(
      p => p.roles.includes(PartyRole.PAYER) && Boolean(caseObj.havePayer),
    );

    return (
      [owner, insured, payor]
        .filter((p): p is Party => Boolean(p))
        .map(party => {
          const files =
            caseObj?.files?.filter(file => file.partyId === party.id) || [];
          return {
            name: party.person?.name?.fullName ?? (party.entity?.name || ''),
            documents: Object.values(
              files.reduce<Record<string, DocumentReview['documents'][0]>>(
                (documents, file) => {
                  if (!documents[file.docType]) {
                    documents[file.docType] = {
                      // @ts-expect-error missing translation key
                      type: t(`eApp:documentUpload.${file.docType}`),
                      images: [],
                    };
                  }
                  documents[file.docType].images.push(file.fileName);
                  return documents;
                },
                {},
              ),
            ),
          };
        }) || []
    );
  }, [caseObj?.files, caseObj?.parties, t]);

  return <DocumentsSummaryBase data={data} displayStyle="doc-type" />;
}
