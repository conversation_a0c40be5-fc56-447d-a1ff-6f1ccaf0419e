import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, H6, LargeBody } from 'cube-ui-components';
import ScreenHeaderTablet from 'navigation/components/ScreenHeader/tablet';
import <PERSON>HeaderPhone from 'navigation/components/ScreenHeader/phone';
import React, { Fragment, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { MY_COUNTRY } from 'constants/optionList';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { PartyRole } from 'types/party';
import { useReviewFatcaMapper } from 'features/eAppV2/ib/hooks/useReviewFatcaMapper';
import {
  DeclarationSummaryQuestion,
  DeclarationSummarySeparator,
} from './ConsentAndDeclarationSummary';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';

export default function FatcaReview() {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const { isTabletMode } = useLayoutAdoptionCheck();
  const ScreenHeader = isTabletMode ? ScreenHeaderTablet : ScreenHeaderPhone;
  const displayedData = useReviewFatcaMapper(caseObj?.application);
  const isEntity = useCheckEntity();

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.fatca')}
        route={'FatcaReview'}
        isLeftCrossBackShown={isTabletMode}
        isLeftArrowBackShown={!isTabletMode}
      />
      <ScrollViewContainer>
        <Content>
          <H6 fontWeight="bold">{t('eApp:declaration.fatca.title')}</H6>
          <Box flex={1} height={sizes[4]} />
          <LargeBody fontWeight="medium">
            {t('eApp:declaration.fatca.message')}
          </LargeBody>
          <Box flex={1} height={sizes[6]} />
          {policyOwner?.person?.nationality !== MY_COUNTRY && !isEntity && (
            <>
              <HintContainer flex={1}>
                <LargeBody color={colors.primary}>
                  {t('eApp:declaration.fatca.hint')}
                </LargeBody>
              </HintContainer>
              <Box flex={1} height={sizes[4]} />
            </>
          )} 
          {displayedData?.map((item, index) => {
            return (
              <Fragment key={index}>
                <DeclarationSummaryQuestion
                  index={item.index}
                  question={item.question}
                  answer={item.answer}
                  subQuestion={item.subQuestion}
                  subAnswer={item.subAnswer}
                  info={item.info}
                  additional={item.additional}
                />
                {index !== displayedData.length - 1 && (
                  <DeclarationSummarySeparator />
                )}
              </Fragment>
            );
          })}
        </Content>
      </ScrollViewContainer>
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  marginBottom: space[6],
}));

const HintContainer = styled(Box)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    paddingHorizontal: space[4],
    paddingVertical: space[4],
    borderRadius: borderRadius['small'],
  }),
);
