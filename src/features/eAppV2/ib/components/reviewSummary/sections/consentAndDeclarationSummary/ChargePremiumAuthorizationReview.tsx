import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, H6, LargeBody } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import ScreenHeaderTablet from 'navigation/components/ScreenHeader/tablet';
import ScreenHeaderPhone from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

export default function ChargePremiumAuthorizationReview() {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { caseObj } = useGetActiveCase();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const ScreenHeader = isTabletMode ? ScreenHeaderTablet : ScreenHeaderPhone;

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.chargePremiumAuthorization')}
        route={'ChargePremiumAuthorizationReview'}
        isLeftCrossBackShown={isTabletMode}
        isLeftArrowBackShown={!isTabletMode}
      />
      <ScrollViewContainer>
        <Box gap={space[2]}>
          <LargeBody color={colors.placeholder}>
            {t(
              'eApp:changePremiumAuthorization.financialPlanningAndKnowledge.question',
            )}
          </LargeBody>
          <LargeBody>
            {caseObj?.application?.proposerConsent?.[0].authLevel}
          </LargeBody>
        </Box>
        <Box mt={space[4]} gap={space[2]}>
          <H6 fontWeight="bold">
            {t('eApp:changePremiumAuthorization.declaration')}
          </H6>
          <LargeBody>
            {t('eApp:changePremiumAuthorization.declaration.content')}
          </LargeBody>
        </Box>
      </ScrollViewContainer>
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);
