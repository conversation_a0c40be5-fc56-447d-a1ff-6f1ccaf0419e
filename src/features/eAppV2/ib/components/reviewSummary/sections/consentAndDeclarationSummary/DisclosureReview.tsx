import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, LargeBody, Row } from 'cube-ui-components';
import ScreenHeaderTablet from 'navigation/components/ScreenHeader/tablet';
import ScreenHeaderPhone from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const paragraphs = [1, 2, 3, 4] as const;

export default function DisclosureReview() {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const ScreenHeader = isTabletMode ? ScreenHeaderTablet : ScreenHeaderPhone;

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.disclosure')}
        route={'DisclosureReview'}
        isLeftCrossBackShown={isTabletMode}
        isLeftArrowBackShown={!isTabletMode}
      />
      <ScrollViewContainer>
        <Box mb={space[10]}>
          <Row gap={space[2]}>
            <LargeBody>{t('eApp:disclosure.i')}</LargeBody>
            <Box>
              <Paragraph>{t('eApp:disclosure.consumerContract')}</Paragraph>
              <Paragraph>
                {t('eApp:disclosure.consumerContract.definition')}
              </Paragraph>
              <Box mt={space[6]}>
                <Paragraph>
                  {t('eApp:disclosure.consumerContract.statement')}
                </Paragraph>
                <Row gap={space[2]}>
                  <LargeBody>{t('eApp:disclosure.a')}</LargeBody>
                  <Paragraph>
                    {t('eApp:disclosure.consumerContract.statement.a')}
                  </Paragraph>
                </Row>
                <Row gap={space[2]}>
                  <LargeBody>{t('eApp:disclosure.b')}</LargeBody>
                  <Paragraph>
                    {t('eApp:disclosure.consumerContract.statement.b')}
                  </Paragraph>
                </Row>
              </Box>
            </Box>
          </Row>
          <Row gap={space[2]} mt={space[6]}>
            <LargeBody>{t('eApp:disclosure.ii')}</LargeBody>
            <Box>
              <Paragraph>{t('eApp:disclosure.nonConsumerContract')}</Paragraph>
              <Paragraph>
                {t('eApp:disclosure.nonConsumerContract.definition')}
              </Paragraph>
              <Box mt={space[6]}>
                <Paragraph>
                  {t('eApp:disclosure.nonConsumerContract.statement')}
                </Paragraph>
                <Row gap={space[2]}>
                  <LargeBody>{t('eApp:disclosure.a')}</LargeBody>
                  <Paragraph>
                    {t('eApp:disclosure.nonConsumerContract.statement.a')}
                  </Paragraph>
                </Row>
                <Row gap={space[2]}>
                  <LargeBody>{t('eApp:disclosure.b')}</LargeBody>
                  <Paragraph>
                    {t('eApp:disclosure.nonConsumerContract.statement.b')}
                  </Paragraph>
                </Row>
              </Box>
            </Box>
          </Row>
          <Box gap={space[6]} mt={space[6]}>
            {paragraphs.map(index => (
              <LargeBody>{t(`eApp:disclosure.${index}`)}</LargeBody>
            ))}
          </Box>
        </Box>
      </ScrollViewContainer>
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);

const Paragraph = styled(LargeBody)({
  flex: 1,
});
