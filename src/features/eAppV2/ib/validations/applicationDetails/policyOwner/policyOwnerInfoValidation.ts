import { InferType } from 'yup';
import {
  mainPartyAddressInformationDefaultValue,
  mainPartyAddressInformationSchema,
  mainPartyAddressInformationToParty,
  partyToMainPartyAddressInformation,
} from '../sections/addressInformation';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
  contactDetailsToParty,
  partyToContactDetails,
} from '../sections/contactDetails';
import {
  nationalityDetailsDefaultValue,
  nationalityDetailsSchema,
  nationalityDetailsToParty,
  partyToNationalityDetails,
} from '../sections/nationalityDetails';
import {
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
  occupationDetailsToParty,
  partyToOccupationDetails,
} from '../sections/occupationDetails';
import {
  mainPartyPersonalDetailsDefaultValue,
  mainPartyPersonalDetailsSchema,
  mainPartyPersonalDetailsToParty,
  partyToMainPartyPersonalDetails,
} from '../sections/mainPartyPersonalDetails';
import { Party, PartyRole, PartyType } from 'types/party';
import { OptionList } from 'types/optionList';
import { Application } from 'types/case';

export const policyOwnerInfoSchema = mainPartyPersonalDetailsSchema
  .concat(nationalityDetailsSchema)
  .concat(occupationDetailsSchema)
  .concat(contactDetailsSchema)
  .concat(mainPartyAddressInformationSchema);

export type PolicyOwnerInfoForm = InferType<typeof policyOwnerInfoSchema>;

export const policyOwnerInfoDefaultValue = {
  ...mainPartyPersonalDetailsDefaultValue,
  ...nationalityDetailsDefaultValue,
  ...occupationDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...mainPartyAddressInformationDefaultValue,
} as PolicyOwnerInfoForm;

export const partyToPolicyOwnerInfo = (
  party?: Party,
  application?: Application,
  optionList?: OptionList,
): PolicyOwnerInfoForm => {
  if (!party) return policyOwnerInfoDefaultValue;
  return {
    ...partyToMainPartyPersonalDetails(party, application),
    ...partyToNationalityDetails(party),
    ...partyToOccupationDetails(party, optionList),
    ...partyToContactDetails(party, optionList),
    ...partyToMainPartyAddressInformation(party),
    personalDetailsRole: 'owner',
    contactRole: 'owner',
    nationalityRole: 'mainParty',
    occupationRole: 'mainParty',
  };
};

export const policyOwnerInfoToParty = (
  form: PolicyOwnerInfoForm,
  roles: PartyRole[],
  optionList: OptionList<string, 'ib'>,
): Party => {
  const { person: personForPersonalDetails, ...restOfPersonalDetails } =
    mainPartyPersonalDetailsToParty(form);
  const { person: personForNationalityDetails, ...restOfNationalityDetails } =
    nationalityDetailsToParty(form);
  const { person: personForOccupationDetails, ...restOfOccupationDetails } =
    occupationDetailsToParty(form, optionList);
  const contactDetails = contactDetailsToParty(form);
  const addressInformation = mainPartyAddressInformationToParty(form);
  return {
    clientType: PartyType.INDIVIDUAL,
    roles,
    ...restOfPersonalDetails,
    ...restOfNationalityDetails,
    ...restOfOccupationDetails,
    ...contactDetails,
    ...addressInformation,
    person: {
      ...personForPersonalDetails,
      ...personForNationalityDetails,
      ...personForOccupationDetails,
    },
  };
};
