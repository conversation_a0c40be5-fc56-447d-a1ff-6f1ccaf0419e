import {
  CORRESPONDENCE_ADDRESS_OPTION,
  getOriginalCountryCode,
  MY_COUNTRY,
  MY_MOBILE_CODE,
  NEW_ADDRESS_OPTION,
  OWNER_ADDRESS_OPTION,
} from 'constants/optionList';
import {
  invalidNomineeSameAsAssured,
  invalidNomineeSameAsWitness,
  minimumAllocation,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { nullableDate } from 'features/eAppV2/common/validations/eAppCommonSchema';
import {
  mysAddressSchema,
  mysEmail,
  mysIdNumber,
} from 'features/eAppV2/my/validations/commonSchema';
import { Party, PartyRole, PartyType } from 'types/party';
import { Gender } from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';
import { parsePartyDob, toPartyDob } from 'utils/helper/dateUtil';
import { formPhoneNumber } from 'utils/validation/customValidation';
import validateSanctionCountry from 'utils/validation/ib/sanctionCountryValidation';
import { InferType, array, object, ref, string } from 'yup';
import { ibName } from '../../commonSchema';
import { commonCountrySchema } from '../../../../common/validations/countryValidation';
import { OptionList } from 'types/optionList';
import { cloneDeep } from 'utils/helper/objectUtil';

export type NominationInfoForm = InferType<typeof nominationInfoSchema>;

export type NominationPersonalDetailsForm = InferType<
  typeof nominationPersonalDetailsSchema
>;

export type NominationContactDetailsForm = InferType<
  typeof nominationContactDetailsSchema
>;

export type NominationAddressInfoForm = InferType<
  typeof nominationAddressInformationSchema
>;

type Nominee = NominationInfoForm['nominees'][0];

export const nominationInfoDefaultValue: NominationInfoForm = {
  nominees: [
    {
      id: '',
      title: '',
      dob: null,
      fullName: '',
      gender: '',
      primaryIdType: '',
      primaryId: '',
      relationship: '',
      nationality: '',
      ///
      mobileCountryCode: MY_MOBILE_CODE,
      mobileNumber: '',
      email: '',
      homeCountryCode: MY_MOBILE_CODE,
      homeNumber: '',
      officeCountryCode: MY_MOBILE_CODE,
      officeNumber: '',
      //
      correspondenceAddress: NEW_ADDRESS_OPTION,
      correspondenceAddressLine1: '',
      correspondenceAddressLine2: '',
      correspondenceAddressLine3: '',
      correspondencePostCode: '',
      correspondenceCity: '',
      correspondenceState: '',
      correspondenceCountry: MY_COUNTRY,
      residentialAddress: CORRESPONDENCE_ADDRESS_OPTION,
      residentialAddressLine1: '',
      residentialAddressLine2: '',
      residentialAddressLine3: '',
      residentialPostCode: '',
      residentialCity: '',
      residentialState: '',
      residentialCountry: MY_COUNTRY,
      allocation: '100',
    },
  ],
};

export const nominationPersonalDetailsSchema = object({
  id: string(),
  title: string().required(requiredMessage),
  fullName: ibName().required(requiredMessage),
  primaryIdType: string().required(requiredMessage),
  primaryId: mysIdNumber('primaryIdType', 'dob', 'gender')
    .required(requiredMessage)
    .test(
      'same-person-with-insured',
      invalidNomineeSameAsAssured,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        const insuredIdNumber = ctx.options?.context?.insuredIdNumber;
        const insuredIdType = ctx.options?.context?.insuredIdType;
        const isSamePersonWithInsured =
          insuredIdNumber === primaryId && insuredIdType === primaryIdType;
        return !isSamePersonWithInsured;
      },
    )
    .test(
      'same-person-with-witness',
      invalidNomineeSameAsWitness,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        const witnessIdNumber = ctx.options?.context?.witnessIdNumber;
        const witnessIdType = ctx.options?.context?.witnessIdType;
        const isSamePersonWithWitness =
          witnessIdNumber === primaryId && witnessIdType === primaryIdType;
        return !isSamePersonWithWitness;
      },
    ),
  gender: string().required(requiredMessage),
  dob: nullableDate(),
  relationship: string().required(requiredMessage),
  nationality: string()
    .required(requiredMessage)
    .test({
      name: 'sanction-country-validation',
      test: (value, ctx) => validateSanctionCountry(value, ctx, 'NATIONALITY'),
    }),
});

export const nominationContactDetailsSchema = object({
  mobileCountryCode: string().required(requiredMessage),
  mobileNumber: formPhoneNumber('mobileCountryCode').required(requiredMessage),
  email: mysEmail().required(requiredMessage),
  homeCountryCode: string(),
  homeNumber: formPhoneNumber('homeCountryCode'),
  officeCountryCode: string(),
  officeNumber: formPhoneNumber('officeCountryCode'),
});

export const nominationAddressInformationSchema = object({
  ...mysAddressSchema(['correspondence', 'residential']),
}).concat(commonCountrySchema);

export const nominationAllocation = object({
  allocation: string()
    .required(requiredMessage)
    .test('min-allocation', minimumAllocation, function (value) {
      return Number(value) >= 10;
    }),
});

export const nominationInfoSchema = object({
  nominees: array()
    .of(
      nominationPersonalDetailsSchema
        .concat(nominationContactDetailsSchema)
        .concat(nominationAddressInformationSchema)
        .concat(nominationAllocation),
    )
    .required(requiredMessage)
    .test({
      name: 'total-allocation',
      test: value => {
        return (
          (value ?? []).every(
            i =>
              i.allocation !== '' && !isNaN(Number(i.allocation ?? undefined)),
          ) &&
          (value ?? []).reduce(
            (total, i) => total + Number(i.allocation || 0),
            0,
          ) === 100
        );
      },
    }),
});

export const partiesToNominationInfo = (
  parties?: Party[],
  optionList?: OptionList,
): NominationInfoForm => {
  const cloneNominationInfoDefaultValue = cloneDeep(nominationInfoDefaultValue);
  cloneNominationInfoDefaultValue.nominees[0].correspondenceAddress =
    OWNER_ADDRESS_OPTION;
  if (!parties || parties.length === 0)
    return {
      ...cloneNominationInfoDefaultValue,
    };
  return {
    nominees: parties.map(party => {
      const primaryId = party.person?.registrations?.find(
        r => r.type === 'DEFAULT',
      );
      const mobileContact = party.contacts.phones.find(
        i => i.type === 'MOBILE',
      );
      const homeContact = party.contacts.phones.find(i => i.type === 'HOME');
      const officeContact = party.contacts.phones.find(i => i.type === 'WORK');
      const correspondenceAddress = party.addresses?.find(
        a => a.addressType === 'MAIN',
      );
      const residentialAddress = party.addresses?.find(
        a => a.addressType === 'HOME',
      );
      return {
        id: party.id || '',
        title: party.person?.name.title || '',
        fullName: party.person?.name.firstName || '',
        primaryIdType: primaryId?.idType || '',
        primaryId: primaryId?.id || '',
        gender: party.person?.gender || '',
        dob: party.person?.dateOfBirth
          ? parsePartyDob(party.person?.dateOfBirth)
          : null,
        relationship: party.relationship || '',
        nationality: party.person?.nationality || '',
        mobileCountryCode:
          getOriginalCountryCode(mobileContact?.countryCode, optionList) ||
          MY_MOBILE_CODE,
        mobileNumber: mobileContact?.number || '',
        email: party.contacts.email || '',
        homeCountryCode:
          getOriginalCountryCode(homeContact?.countryCode, optionList) ||
          MY_MOBILE_CODE,
        homeNumber: homeContact?.number || '',
        officeCountryCode:
          getOriginalCountryCode(officeContact?.countryCode, optionList) ||
          MY_MOBILE_CODE,
        officeNumber: officeContact?.number || '',
        correspondenceAddress:
          (correspondenceAddress?.businessAddressOpt as Nominee['correspondenceAddress']) ||
          NEW_ADDRESS_OPTION,
        correspondenceAddressLine1: correspondenceAddress?.street || '',
        correspondenceAddressLine2: correspondenceAddress?.subDistrict || '',
        correspondenceAddressLine3: correspondenceAddress?.district || '',
        correspondencePostCode: correspondenceAddress?.zipCode || '',
        correspondenceCity: correspondenceAddress?.city || '',
        correspondenceState: correspondenceAddress?.province || '',
        correspondenceCountry: correspondenceAddress?.countryCode || MY_COUNTRY,
        residentialAddress:
          (residentialAddress?.businessAddressOpt as Nominee['residentialAddress']) ||
          CORRESPONDENCE_ADDRESS_OPTION,
        residentialAddressLine1: residentialAddress?.street || '',
        residentialAddressLine2: residentialAddress?.subDistrict || '',
        residentialAddressLine3: residentialAddress?.district || '',
        residentialPostCode: residentialAddress?.zipCode || '',
        residentialCity: residentialAddress?.city || '',
        residentialState: residentialAddress?.province || '',
        residentialCountry: residentialAddress?.countryCode || MY_COUNTRY,
        allocation: String(party.beneficiarySetting?.benefitPercentage || ''),
      };
    }),
  };
};

export const nominationInfoToParties = (form: NominationInfoForm): Party[] => {
  return form.nominees.map((nominee: Nominee) => ({
    id: nominee.id || '',
    clientType: PartyType.INDIVIDUAL,
    roles: [PartyRole.BENEFICIARY],
    relationship: nominee.relationship,
    person: {
      name: {
        title: nominee.title,
        firstName: nominee.fullName,
      },
      dateOfBirth: toPartyDob(nominee.dob),
      age: nominee.dob ? calculateAge(nominee.dob) : 0,
      gender: nominee.gender as Gender,
      nationality: nominee.nationality,
      registrations: [
        {
          type: 'DEFAULT',
          idType: nominee.primaryIdType,
          id: nominee.primaryId,
        },
      ],
    },
    contacts: {
      email: nominee.email || '',
      phones: [
        'mobileCountryCode' in nominee
          ? {
              type: 'MOBILE',
              countryCode: nominee.mobileCountryCode || '',
              number: nominee.mobileNumber || '',
            }
          : null,
        'homeCountryCode' in nominee
          ? {
              type: 'HOME',
              countryCode: nominee.homeCountryCode || '',
              number: nominee.homeNumber || '',
            }
          : null,
        'officeCountryCode' in nominee
          ? {
              type: 'WORK',
              countryCode: nominee.officeCountryCode || '',
              number: nominee.officeNumber || '',
            }
          : null,
      ].filter(Boolean) as Party['contacts']['phones'],
    },
    addresses: [
      {
        addressType: 'MAIN',
        street: nominee.correspondenceAddressLine1 || '',
        subDistrict: nominee.correspondenceAddressLine2 || '',
        district: nominee.correspondenceAddressLine3 || '',
        zipCode: nominee.correspondencePostCode || '',
        city: nominee.correspondenceCity || '',
        province: nominee.correspondenceState || '',
        countryCode: nominee.correspondenceCountry || '',
        businessAddressOpt: nominee.correspondenceAddress || '',
      },
      nominee.residentialAddress === CORRESPONDENCE_ADDRESS_OPTION
        ? {
            addressType: 'HOME',
            street: nominee.correspondenceAddressLine1 || '',
            subDistrict: nominee.correspondenceAddressLine2 || '',
            district: nominee.correspondenceAddressLine3 || '',
            zipCode: nominee.correspondencePostCode || '',
            city: nominee.correspondenceCity || '',
            province: nominee.correspondenceState || '',
            countryCode: nominee.correspondenceCountry || '',
            businessAddressOpt: CORRESPONDENCE_ADDRESS_OPTION,
          }
        : {
            addressType: 'HOME',
            street: nominee.residentialAddressLine1 || '',
            subDistrict: nominee.residentialAddressLine2 || '',
            district: nominee.residentialAddressLine3 || '',
            zipCode: nominee.residentialPostCode || '',
            city: nominee.residentialCity || '',
            province: nominee.residentialState || '',
            countryCode: nominee.residentialCountry || '',
            businessAddressOpt: NEW_ADDRESS_OPTION,
          },
    ],
    beneficiarySetting: {
      benefitPercentage: Number(nominee.allocation || 0),
      beneficiaryType: 'NO',
    },
  }));
};
