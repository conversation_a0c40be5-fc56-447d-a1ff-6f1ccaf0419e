import logoPicture from './assets/logo';
import contactPicture from './assets/contact';
import celebratePicture from './assets/celebrate';
import { AgentProfile } from 'types';
import { SendEmailBody } from 'api/emailApi';

export const generateEMandatePaymentEmailTemplate = ({
  recipientEmail,
  recipientName,
  paymentUrl,
  agentProfile,
}: {
  recipientEmail: string;
  recipientName: string;
  paymentUrl: string;
  agentProfile: AgentProfile;
}) => {
  const message =
    "<html xmlns:v='urn:schemas-microsoft-com:vml' " +
    "xmlns:o='urn:schemas-microsoft-com:office:office' " +
    "xmlns:w='urn:schemas-microsoft-com:office:word' " +
    "xmlns:m='http://schemas.microsoft.com/office/2004/12/omml' " +
    "xmlns='http://www.w3.org/TR/REC-html40'> " +
    ' ' +
    '<head> ' +
    "<meta http-equiv=Content-Type content='text/html; charset=unicode'> " +
    '<meta name=ProgId content=Word.Document> ' +
    "<meta name=Generator content='Microsoft Word 15'> " +
    "<meta name=Originator content='Microsoft Word 15'> " +
    '<link rel=File-List ' +
    "href='EXTERNAL%20Payment%20Gateway%20Link_files/filelist.xml'> " +
    '<link rel=Edit-Time-Data ' +
    "href='EXTERNAL%20Payment%20Gateway%20Link_files/editdata.mso'> " +
    '<link rel=themeData ' +
    "href='EXTERNAL%20Payment%20Gateway%20Link_files/themedata.thmx'> " +
    '<link rel=colorSchemeMapping ' +
    "href='EXTERNAL%20Payment%20Gateway%20Link_files/colorschememapping.xml'> " +
    '<style> ' +
    '</style> ' +
    '</head> ' +
    ' ' +
    "<body lang=EN-HK link='#0563C1' vlink='#954F72' style='tab-interval:36.0pt'> " +
    ' ' +
    '<div class=WordSection1> ' +
    ' ' +
    // '<p class=MsoNormal><o:p>&nbsp;</o:p></p> ' +
    ' ' +
    "<table class=MsoNormalTable border=0 cellspacing=5 cellpadding=0 width='80%' " +
    "style='width:80.0%;mso-cellspacing:2.5pt;mso-yfti-tbllook:1184;mso-padding-alt: " +
    "0cm 5.4pt 0cm 5.4pt'> " +
    "<tr style='mso-yfti-irow:1'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal align=right style='text-align:right'><span " +
    "style='mso-fareast-language:EN-US;mso-no-proof:yes'> " +
    '   ' +
    ' ' +
    '<img width=100 height=81 ' +
    "src='" +
    logoPicture +
    "' v:shapes='Picture_x0020_1'></span><span " +
    "style='mso-fareast-font-family:'Times New Roman''><o:p></o:p></span></p> " +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:2'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>Dear " +
    recipientName +
    ', <o:p></o:p></span></p> ' +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:3'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>&nbsp; " +
    '<o:p></o:p></span></p> ' +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:4'> " +
    "<td colspan=3 style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>FWD Insurance" +
    ' would like to thank you for your trust and choice. <o:p></o:p></span></p> ' +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:5'> " +
    "<td colspan=3 style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>Kindly " +
    'be informed that your E-Mandate registration will only become effective upon ' +
    'successful registration.<o:p></o:p></span></p> ' +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:6'> " +
    "<td colspan=3 style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>Please " +
    'click on the link <a ' +
    "href='" +
    paymentUrl +
    "'>" +
    paymentUrl +
    '</a> ' +
    'to register E-Mandate.<o:p></o:p></span></p> ' +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:7'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>If " +
    'you have any questions, connect with us via the following: <o:p></o:p></span></p> ' +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:8'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:9'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal align=center style='text-align:center'><span " +
    "style='mso-fareast-language:EN-US;mso-no-proof:yes'> " +
    '<img border=0 width=645 height=123 ' +
    "src='" +
    contactPicture +
    "' v:shapes='Picture_x0020_2'></span><span " +
    "style='mso-fareast-font-family:'Times New Roman''><o:p></o:p></span></p> " +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:10'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<hr size=4 width='100%' noshade style='background-color:coral; color: coral;border:none;' align=left> " +
    "<span style='mso-fareast-font-family:'Times New Roman''>Or " +
    'contact our Agent for professional advice. <br> ' +
    '<br> ' +
    agentProfile.person?.fullName +
    '&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ' +
    '&nbsp; &nbsp; &nbsp;' +
    agentProfile.agentId +
    '&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ' +
    '&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;' +
    agentProfile.contact.email +
    '<o:p></o:p></span>' +
    "<hr size=4 width='100%' noshade style='background-color:coral; color: coral;border:none;' align=left>" +
    '</td> ' +
    '</tr> ' +
    "<tr style='mso-yfti-irow:11'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:12'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:13'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>Best " +
    'regards, <o:p></o:p></span></p> ' +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:14'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><span style='mso-fareast-font-family:'Times New Roman''>FWD Insurance" +
    ' Berhad <o:p></o:p></span></p> ' +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:15'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:16'> " +
    '<td > ' +
    "<hr size=1 width='100%' align=left> " +
    "<div style='text-align:center'><span align=center style='text-align:center'>" +
    'This is a system generated message. Please do not reply to this email</span></div> ' +
    "<hr size=1 width='100%' align=left> " +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'></td> " +
    '</tr> ' +
    "<tr style='mso-yfti-irow:17;mso-yfti-lastrow:yes'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<table class=MsoNormalTable width='100%' border=0 cellspacing=3 cellpadding=0 " +
    "style='mso-cellspacing:1.5pt;mso-yfti-tbllook:1184;mso-padding-alt:0cm 5.4pt 0cm 5.4pt'> " +
    "<tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'> " +
    "<td style='padding:.75pt .75pt .75pt .75pt'> " +
    "<p class=MsoNormal><sptan syle='mso-fareast-font-family:'Times New Roman''>FWD Insurance" +
    ' Berhad, Registration No: 200601011780 (731530-M) <br> ' +
    'SNB009E1810 <o:p></o:p></span></p> ' +
    '</td> ' +
    "<td style='padding:.75pt .75pt .75pt .75pt;text-align: right;'> " +
    "<p class=MsoNormal ><span style='mso-fareast-language:EN-US;mso-no-proof: " +
    "yes'><img border=0 width=200 height=69 " +
    "src='" +
    celebratePicture +
    "' v:shapes='Picture_x0020_7'></span><span " +
    "style='mso-fareast-font-family:'Times New Roman''><o:p></o:p></span></p> " +
    '</td> ' +
    '</tr> ' +
    '</table> ' +
    '</td> ' +
    "<td style='padding:0.75pt 0.75pt 0.75pt .75pt'></td> " +
    "<td style='padding:0.75pt 0.75pt 0.75pt .75pt'></td> " +
    '</tr> ' +
    '</table> ' +
    ' ' +
    '</div> ' +
    ' ' +
    '</body> ' +
    ' ' +
    '</html> ';
  const body: SendEmailBody = {
    emailBody: message,
    emailTitle: 'E-Mandate Registration Link',
    emailToRecipients: [recipientEmail],
    isHtmlText: true,
    attachments: {},
    emailBccRecipients: [],
    emailCcRecipients: [],
    isFIB: 'Y',
  };
  return body;
};
