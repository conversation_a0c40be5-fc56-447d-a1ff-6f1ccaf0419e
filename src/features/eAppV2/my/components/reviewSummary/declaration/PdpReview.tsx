import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Checkbox,
  H7,
  Icon,
  LargeBody,
  Row,
} from 'cube-ui-components';
import { EAPP_CONSENT_DATA_PDFVIEW, PDPA_LINK } from 'features/eAppV2/my/constants/consent';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PdpaNotice } from '../../../../common/components/pdpaNotice/PdpaNotice';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';

export default function PdpReview() {
  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const isAgreePersonalInfoCollected = useEAppStore(
    state => state.isAgreePersonalInfoCollected,
  );

  const [consentLang, setConsentLang] = useState<'en' | 'my'>('en');
  const isEn = consentLang === 'en';

  const [webVisible, setWebVisible] = useState(false);
  const consentLength = EAPP_CONSENT_DATA_PDFVIEW.LANGUAGES[consentLang].content.length;

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={t('eApp:review.declaration.personalDataProtection')}
        route={'PdpReview'}
        isLeftCrossBackShown
      />
      <ScrollViewContainer
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: sizes[6],
        }}>
        <Row mb={space[4]} gap={space[2]}>
          <Box flex={1} />
          <TouchableOpacity onPress={() => setConsentLang('en')}>
            <BulletButton
              borderColor={isEn ? colors.primary : colors.palette.fwdGrey[100]}
              backgroundColor={isEn ? colors.primaryVariant3 : 'transparent'}>
              <Body
                fontWeight={isEn ? 'bold' : 'normal'}
                color={isEn ? colors.primary : colors.secondary}>
                {t('eApp:english')}
              </Body>
            </BulletButton>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setConsentLang('my')}>
            <BulletButton
              borderColor={!isEn ? colors.primary : colors.palette.fwdGrey[100]}
              backgroundColor={!isEn ? colors.primaryVariant3 : 'transparent'}>
              <Body
                fontWeight={!isEn ? 'bold' : 'normal'}
                color={!isEn ? colors.primary : colors.secondary}>
                {t('eApp:bahasa')}
              </Body>
            </BulletButton>
          </TouchableOpacity>
        </Row>
        {EAPP_CONSENT_DATA_PDFVIEW.LANGUAGES[consentLang].content.map((item, index) => {
          const isCkbConsent = item.additional?.isCheckbox;
          const isLink = item.additional?.isLink;
          return (
            <Row
              key={index}
              marginBottom={index === consentLength - 1 ? 0 : sizes[5]}>
              <LargeBody>{`${index + 1}. `}</LargeBody>
              <Box flex={1}>
                <Box flex={1}>
                  <LargeBody> {item.content}</LargeBody>
                </Box>
                {isCkbConsent && (
                  <Row
                    borderRadius={sizes[2]}
                    borderWidth={1}
                    borderColor={colors.palette.fwdGrey[100]}
                    backgroundColor={colors.palette.fwdGrey[20]}
                    padding={sizes[3]}
                    marginTop={sizes[2]}
                    key={item.toString()}>
                    <Box marginRight={sizes[2]}>
                      <Checkbox
                        value={isAgreePersonalInfoCollected}
                        label={item.additional?.content ?? ''}
                        style={{
                          alignItems: 'flex-start',
                        }}
                        labelStyle={{
                          marginTop: -2,
                        }}
                        disabled={true}
                      />
                    </Box>
                  </Row>
                )}
                {isLink && (
                  <Box marginTop={sizes[2]}>
                    <TouchableOpacity
                      onPress={() => {
                        setWebVisible(true);
                      }}>
                      <Row alignItems="center">
                        <H7 fontWeight="bold" color={colors.primary}>
                          {item.additional?.title}
                        </H7>
                        <Icon.ChevronRight />
                      </Row>
                    </TouchableOpacity>
                  </Box>
                )}
              </Box>
            </Row>
          );
        })}
      </ScrollViewContainer>
      <PdpaNotice
        url={PDPA_LINK}
        visible={webVisible}
        reviewed
        onDismiss={() => setWebVisible(false)}
        onAgree={() => {
          setWebVisible(false);
        }}
      />
    </Box>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space, colors } }) => ({
    flex: 1,
    padding: space[6],
    marginBottom: space[6],
  }),
);

const BulletButton = styled(Box)(({ theme: { space, borderRadius } }) => ({
  borderWidth: 1,
  borderRadius: borderRadius['full'],
  paddingVertical: space[2],
  paddingHorizontal: space[3],
}));
