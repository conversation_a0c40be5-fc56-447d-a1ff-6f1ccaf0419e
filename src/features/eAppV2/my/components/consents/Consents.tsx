import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { DocumentSign2 } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { EAPP_CONSENT_DATA, PDPA_LINK } from 'features/eAppV2/my/constants/consent';
import { toFATCA } from 'features/eAppV2/my/utils/caseUtils';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCase } from 'hooks/useGetCase';
import React, { memo, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import TextMore from '../../../../../components/TextMore';
import EAppFooterTablet from '../../../common/components/footer/EAppFooter.tablet';
import { PdpaNotice } from '../../../common/components/pdpaNotice/PdpaNotice';
import TabletSections from '../../../common/components/TabletSections';

const Consents = memo(() => {
  const { alertError } = useAlert();
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const completedStepsMap = useEAppProgressBarStore(
    state => state.completedMap,
  );
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const [hasReadFullConsent, setHasReadFullConsent] = useState(
    Boolean(completedStepsMap['consents--']),
  );
  const [consentLang, setConsentLang] = useState<'en' | 'my'>('en');
  const isEn = consentLang === 'en';
  const { colors, sizes, space } = useTheme();
  const [webVisible, setWebVisible] = useState(false);
  const { isAgreePersonalInfoCollected, updateAgreePersonalInfoCollected } =
    useEAppStore(
      state => ({
        updateAgreePersonalInfoCollected:
          state.updateAgreePersonalInfoCollected,
        isAgreePersonalInfoCollected: state.isAgreePersonalInfoCollected,
      }),
      shallow,
    );
  const [pdpChecked, setConsentsChecked] = useState(
    isAgreePersonalInfoCollected,
  );
  const { t } = useTranslation(['eApp']);
  const consentLength = EAPP_CONSENT_DATA.LANGUAGES[consentLang].content.length;

  const fatca = useEAppStore(state => state.my_declaration.fatca);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const onSubmit = async () => {
    setWebVisible(true);

    GATracking.logCustomEvent('application', {
      action_type: 'eapp_submit_consents',
      application_type: 'F2F',
    });
  };

  const sections = useMemo(() => {
    return [
      {
        name: 'PDP',
        title: t('eApp:pdp'),
        content: (
          <Container>
            <Content>
              <Row justifyContent="space-between" alignItems="center">
                <Row justifyContent="center" alignItems="center">
                  <DocumentSign2 size={40} />
                  <H6 fontWeight="bold">
                    {EAPP_CONSENT_DATA.LANGUAGES[consentLang].title}
                  </H6>
                </Row>
                <Row>
                  <TouchContainer
                    onPress={() => {
                      setConsentLang('en');
                    }}
                    style={
                      isEn
                        ? {
                            borderColor: colors.primary,
                            backgroundColor: colors.primaryVariant3,
                          }
                        : {}
                    }>
                    <Body
                      color={isEn ? colors.primary : colors.secondary}
                      fontWeight={isEn ? 'medium' : 'normal'}>
                      {t('eApp:english')}
                    </Body>
                  </TouchContainer>
                  <TouchContainer
                    onPress={() => {
                      setConsentLang('my');
                    }}
                    style={
                      !isEn
                        ? {
                            borderColor: colors.primary,
                            backgroundColor: colors.primaryVariant3,
                          }
                        : {}
                    }>
                    <Body
                      color={!isEn ? colors.primary : colors.secondary}
                      fontWeight={!isEn ? 'medium' : 'normal'}>
                      {t('eApp:bahasa')}
                    </Body>
                  </TouchContainer>
                </Row>
              </Row>
              <Box marginTop={sizes[5]}>
                <ScrollView>
                  {EAPP_CONSENT_DATA.LANGUAGES[consentLang].content.map(
                    (item, index) => {
                      const isCkbConsent = item.additional?.isCheckbox;
                      return (
                        <Box key={`${index}`}>
                          {item.title && (
                            <Box marginBottom={sizes[3]}>
                              <LargeLabel fontWeight="bold">
                                {item.title}
                              </LargeLabel>
                            </Box>
                          )}
                          <Row
                            key={index}
                            marginBottom={
                              index === consentLength - 1 ? 0 : sizes[5]
                            }>
                            {!item.title && (
                              <LargeBody>{`${index + 1}. `}</LargeBody>
                            )}
                            <Box flex={1}>
                              <Box flex={1}>
                                {typeof item.content === 'string' ? (
                                  <TextMore text={item.content} numLines={3} />
                                ) : (
                                  <LargeBody>
                                    {' '}
                                    {item?.content?.map(item => {
                                      if (item.isLink) {
                                        return (
                                          <TouchableOpacity
                                            onPress={() => {
                                              setWebVisible(true);
                                            }}
                                            key={item.content}
                                            style={{ marginBottom: -2 }}>
                                            <LargeBody
                                              fontWeight={'bold'}
                                              style={{
                                                color: colors.primary,
                                                textDecorationLine: 'underline',
                                              }}>
                                              {item.content}
                                            </LargeBody>
                                          </TouchableOpacity>
                                        );
                                      }
                                      return (
                                        <LargeBody
                                          key={item.content}
                                          fontWeight={'normal'}>
                                          {item.content}
                                        </LargeBody>
                                      );
                                    })}
                                  </LargeBody>
                                )}
                              </Box>
                              {isCkbConsent && (
                                <Row
                                  borderRadius={sizes[2]}
                                  borderWidth={0}
                                  borderColor={
                                    pdpChecked
                                      ? colors.primary
                                      : colors.palette.fwdGrey[100]
                                  }
                                  backgroundColor={colors.background}
                                  padding={sizes[3]}
                                  paddingLeft={0}
                                  marginTop={sizes[2]}
                                  key={item.toString()}>
                                  <Checkbox
                                    value={pdpChecked}
                                    onChange={(isChecked: boolean) => {
                                      updateAgreePersonalInfoCollected(
                                        isChecked,
                                      );
                                      setConsentsChecked(isChecked);
                                    }}
                                    label={item.additional?.content ?? ''}
                                    style={{
                                      alignItems: 'flex-start',
                                      flex: 1,
                                      marginRight: space[2],
                                    }}
                                    labelStyle={{
                                      marginTop: -2,
                                    }}
                                  />
                                </Row>
                              )}
                            </Box>
                          </Row>
                        </Box>
                      );
                    },
                  )}
                </ScrollView>
              </Box>
              <PdpaNotice
                visible={webVisible}
                url={PDPA_LINK}
                nextScreenText={t('eApp:renewalPaymentSetup.title')}
                onDismiss={() => setWebVisible(false)}
                onAgree={async () => {
                  try {
                    setWebVisible(false);
                    setHasReadFullConsent(true);
                    if (!caseId || !caseObj || !agentId) return;
                    await saveApplication({
                      caseId,
                      data: {
                        ...caseObj.application,
                        proposerConsent: toFATCA(fatca, agentId, pdpChecked),
                      },
                    });
                    nextGroup(true);
                  } catch {
                    alertError(t('eApp:failedToSaveData'));
                  }
                }}
              />
            </Content>
          </Container>
        ),
      },
    ];
  }, [
    t,
    consentLang,
    isEn,
    colors.primary,
    colors.primaryVariant3,
    colors.secondary,
    colors.palette.fwdGrey,
    colors.background,
    sizes,
    webVisible,
    consentLength,
    pdpChecked,
    space,
    updateAgreePersonalInfoCollected,
    caseId,
    caseObj,
    agentId,
    saveApplication,
    fatca,
    nextGroup,
    alertError,
  ]);
  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock="consents-"
        onPrimaryPress={onSubmit}
        primaryLoading={isSavingApplication}
        primarySubLabel={t('eApp:personal.data.protection.notice')}
      />
    </>
  );
});
export default Consents;

const Container = styled(ScrollView)(({ theme: { space } }) => ({
  flex: 1,
  paddingTop: space[6],
  paddingRight: space[8],
  paddingBottom: space[6],
}));
const Content = styled(View)(({ theme: { colors, space, borderRadius } }) => ({
  backgroundColor: colors.background,
  borderRadius: borderRadius.large,
  padding: space[6],
  alignSelf: 'stretch',
  marginBottom: space[12],
}));
const TouchContainer = styled(TouchableOpacity)(
  ({ theme: { colors, sizes } }) => ({
    borderRadius: sizes[8] - 2,
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[50],
    padding: sizes[3],
    paddingTop: sizes[1],
    paddingBottom: sizes[1],
    marginEnd: sizes[1],
  }),
);
