import { useTheme } from '@emotion/react';
import { IPay88PaymentStatus, PaymentGatewayResponse } from 'api/ipay88Api';
import { AxiosError } from 'axios';
import { Box, Icon, Row, Toast } from 'cube-ui-components';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useEAppAlert } from 'features/eAppV2/common/hooks/useEAppAlert';
import { useEAppSubmitApplication } from 'features/eAppV2/common/hooks/useEAppSubmitApplication';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import generatePaymentEmailTemplate from 'features/eAppV2/my/constants/emailTemplate/paymentEmailTemplates';
import { generateSMSTemplate } from 'features/eAppV2/my/utils/generateSMSTemplate';
import { getPremByPaymentMode } from 'features/eAppV2/my/utils/planUtils';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import {
  usePaymentGateway,
  usePaymentLink,
  useQueryPaymentGatewayStatus,
} from 'hooks/useIpay88Gateway';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useSendEmail } from 'hooks/useSendEmail';
import { useSendSms } from 'hooks/useSendSms';
import useToggle from 'hooks/useToggle';
import { isEmpty } from 'lodash';
import round from 'lodash/round';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { CHANNELS } from 'types/channel';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import EAppFooterTablet from '../../../common/components/footer/EAppFooter.tablet';
import AdvanceContributionForm from './AdvanceContributionForm';
import EMandateConfirmationModal from './EMandateConfirmationModal';
import PaymentMethods, {
  PaymentMethod,
  SubPaymentMethod,
} from './PaymentMethods';
import Plan from './Plan';
import TermAndConditionModal from './TermAndConditionModal';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { getConfirmationActions } from 'components/prompt/PromptDialog';
import useLatest from 'hooks/useLatest';
import { usePromptContext } from 'components/prompt/PromptContext';
interface PaymentSubmissionProps {
  onPaymentSubmitted: (paymentId: string) => void;
  onShowInAppPayment: () => void;
}
const DELAY_TIME_IN_MS = 2 * 1000;

const PaymentSubmission = memo(function Payment({
  onPaymentSubmitted,
  onShowInAppPayment,
}: PaymentSubmissionProps) {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const { data: agentProfile } = useGetAgentProfile();
  const { alertError, alert } = useAlert();
  const navigation = useRootStackNavigation();

  const { mutateAsync: submitApplication, isLoading: isSubmittingApplication } =
    useEAppSubmitApplication();

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  useEffect(() => {
    if (isSubmittingApplication) {
      setAppLoading();
    } else {
      setAppIdle();
    }
    return () => {
      setAppIdle();
    };
  }, [isSubmittingApplication, setAppIdle, setAppLoading]);

  const {
    mutateAsync: requestPaymentGateway,
    isLoading: isGeneratingPaymentLink,
  } = usePaymentGateway();

  const { mutateAsync: requestPaymentLink, isLoading: isRequestPaymentLink } =
    usePaymentLink();
  const [isTermConditionVisible, setTermConditionVisible] = useState(false);
  const showTermAndCondition = () => setTermConditionVisible(true);
  const hideTermAndCondition = () => setTermConditionVisible(false);
  const [
    eMandateConfirmationVisible,
    showEMandateConfirmationVisible,
    hideEMandateConfirmationVisible,
  ] = useToggle();

  const {
    my_policyOwnerPersonalInfo,
    my_authorizedSignatoryInfo,
    my_mockPaymentResult,
    setMY_PaymentLinkResponse,
    setMY_PaymentStatusResponse,
    applicationNum,
    my_paymentMethod,
    setMY_PaymentMethod,
    my_subPaymentMethod,
    setMY_SubPaymentMethod,
    my_advanceContributionDuration,
    updateMY_ChequePayment,
    updateMY_DirectTransferPayment,
    my_companyInfo,
    setMY_AdvanceContributionDuration,
    setMY_HasAdvanceContribution,
  } = useEAppStore(state => {
    return {
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_authorizedSignatoryInfo: state.my_authorizedSignatoryInfo,
      my_payorDetails: state.my_payorDetails,
      my_mockPaymentResult: state.my_mockPaymentResult,
      setMY_PaymentLinkResponse: state.setMY_PaymentLinkResponse,
      setMY_PaymentStatusResponse: state.setMY_PaymentStatusResponse,
      policyNum: state.policyNum,
      applicationNum: state.applicationNum,
      my_paymentMethod: state.my_paymentMethod,
      setMY_PaymentMethod: state.setMY_PaymentMethod,
      my_subPaymentMethod: state.my_subPaymentMethod,
      setMY_SubPaymentMethod: state.setMY_SubPaymentMethod,
      my_advanceContributionDuration: state.my_advanceContributionDuration,
      updateMY_ChequePayment: state.updateMY_ChequePayment,
      updateMY_DirectTransferPayment: state.updateMY_DirectTransferPayment,
      my_companyInfo: state.my_companyInfo,
      setMY_AdvanceContributionDuration:
        state.setMY_AdvanceContributionDuration,
      setMY_HasAdvanceContribution: state.setMY_HasAdvanceContribution,
    };
  }, shallow);

  const setProgressBarState = useEAppProgressBarStore(
    state => state.setProgressBarState,
  );

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    my_paymentMethod,
  );
  const [subPaymentMethod, setSubPaymentMethod] =
    useState<SubPaymentMethod | null>(my_subPaymentMethod);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const {
    mutateAsync: checkPaymentStatus,
    isLoading: isCheckingPaymentStatus,
  } = useQueryPaymentGatewayStatus();
  const { mutateAsync: sendEmail, isLoading: isSendingEmail } = useSendEmail();
  const { mutateAsync: sendSms, isLoading: isSendingSms } = useSendSms();
  const quotation = useSelectedQuotation();
  const selectedQuotation = useSelectedQuotation();
  const paymentMode = selectedQuotation?.basicInfo?.paymentMode;
  const premiumByPaymentMode = getPremByPaymentMode(selectedQuotation) ?? 0;
  const isEntity = useCheckEntity();
  const channel = useGetCubeChannel();
  const isAgency = channel === CHANNELS.AGENCY;

  const { alertError: alertRetryError } = useEAppAlert();

  const scrollViewRef = useRef<ScrollView>(null);
  const paymentMethodViewRef = useRef<View>(null);

  useEffect(() => {
    setPaymentMethod(my_paymentMethod);
  }, [my_paymentMethod]);

  const handleSubmitApplication = async () => {
    await submitApplication({
      onSuccess: () => navigation.navigate('Submission'),
      onFailure: () => navigation.navigate('SubmissionFailed'),
    });
  };

  const handleSetPaymentMethod = useCallback(
    (paymentMethod: PaymentMethod | null) => {
      setPaymentMethod(paymentMethod);
      setSubPaymentMethod(
        (paymentMethod === PaymentMethod.CREDIT ||
          paymentMethod === PaymentMethod.FPX) &&
          caseObj?.isRemoteSelling
          ? SubPaymentMethod.VIA_LINK
          : null,
      );
    },
    [caseObj?.isRemoteSelling],
  );

  const handleSubmit = async (
    inputPaymentMethod = paymentMethod,
    inputSubPaymentMethod = subPaymentMethod,
  ) => {
    if (!inputPaymentMethod) return;

    if (!caseId || !caseObj) return;

    const initialPayment = caseObj.application
      ?.initialPayment as PaymentGatewayResponse | null;
    if (initialPayment && initialPayment.paymentTransactionId) {
      // Check existing payment status
      try {
        const paymentResponse = await checkPaymentStatus({
          paymentTransactionId: initialPayment.paymentTransactionId,
        });
        if (
          paymentResponse.paymentStatus === IPay88PaymentStatus.RECEIVE_SUCCEED
        ) {
          // If existing payment is successful, update payment response and stop
          await saveApplication({
            caseId,
            data: {
              ...caseObj.application,
              initialPayment: {
                ...initialPayment,
                paymentResponse,
                paymentDate: new Date().toISOString(),
              },
            },
          });
          await alertError(t('eApp:payment.paymentMethodSelectionWarning'));
          setMY_PaymentMethod(
            caseObj?.application?.paymentMethod as PaymentMethod,
          );
          setMY_SubPaymentMethod(
            caseObj?.application?.isInAppPayment
              ? SubPaymentMethod.IN_APP
              : SubPaymentMethod.VIA_LINK,
          );
          setMY_PaymentLinkResponse(initialPayment);
          setMY_PaymentStatusResponse(paymentResponse);
          onPaymentSubmitted(initialPayment.paymentTransactionId);
          return;
        }
      } catch {
        alertError(t('eApp:payment.failedToGenerate'));
        return;
      }
    }

    // When the payment link is generated successful and sent to user, there will be a toast bar coming out from the bottom
    // After the toast bar displays for 2s, it will direct to next page
    // Do sending link

    const advanceContributionAmount = round(
      (quotation?.summary.initialPrem ?? 0) *
        my_advanceContributionDuration *
        (isEntity ? 1.08 : 1),
      2,
    );
    try {
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          paymentMethod: inputPaymentMethod,
          advancePayment: {
            amount: advanceContributionAmount,
            duration: my_advanceContributionDuration,
          },
        },
      });
    } catch {
      alertError(t('eApp:failedToSaveData'));
      return;
    }

    let paymentLinkResponse: PaymentGatewayResponse;
    const params = {
      caseId: caseId,
      paymentMethod: inputPaymentMethod,
      advancePaymentDuration: my_advanceContributionDuration,
      isInAppPayment:
        inputSubPaymentMethod === null
          ? false
          : inputSubPaymentMethod === SubPaymentMethod.IN_APP,
    };
    try {
      paymentLinkResponse = await requestPaymentLink(params);
    } catch (e) {
      if (e instanceof AxiosError && e.response?.status === 410) {
        alert(t('eApp:payment.expried.title'), t('eApp:payment.expried.des'));
      } else if (e instanceof AxiosError && e.response?.status === 500) {
        alertRetryError(handleSubmit);
      } else alertError(t('eApp:payment.failedToGenerate'));
      return;
    }

    setMY_PaymentMethod(inputPaymentMethod);
    setMY_SubPaymentMethod(inputSubPaymentMethod);

    // CHECK FOR CC & ONLINE PAYMENT
    if (/CC|FPX/i.test(inputPaymentMethod)) {
      //TODO all data will get from application & quotation data
      const { email, fullName } = isEntity
        ? {
            email: my_companyInfo?.email,
            fullName: my_companyInfo?.companyName,
          }
        : my_policyOwnerPersonalInfo;
      const mobileNum = isEntity
        ? `${my_companyInfo.businessCountryCode.split(' - ')[0]}${
            my_companyInfo.businessPhoneNumber
          }`
        : `${my_policyOwnerPersonalInfo.mobileCountryCode.split(' - ')[0]}${
            my_policyOwnerPersonalInfo.mobileNumber
          }`;
      // const prem = getPremByPaymentMode(selectedQuotation) || 0;
      try {
        await saveApplication({
          caseId,
          data: {
            ...caseObj.application,
            isInAppPayment:
              inputSubPaymentMethod === null
                ? null
                : inputSubPaymentMethod === SubPaymentMethod.IN_APP,
            paymentMethod: inputPaymentMethod,
            initialPayment: {
              ...paymentLinkResponse,
              paymentMethod: inputPaymentMethod,
              eMandate: inputPaymentMethod === PaymentMethod.FPX,
            },
            advancePayment: {
              amount: advanceContributionAmount,
              duration: my_advanceContributionDuration,
            },
          },
        });
        setMY_PaymentLinkResponse(paymentLinkResponse);
      } catch {
        alertError(t('eApp:failedToSaveData'));
        return;
      }

      if (paymentLinkResponse.status === 'Failed') {
        alertError(t('eApp:payment.failedToGenerate'));
        return;
      }

      try {
        const { paymentUrl } = paymentLinkResponse;

        if (inputSubPaymentMethod === SubPaymentMethod.VIA_LINK) {
          if (email && agentProfile) {
            const paymentLinkRequest = generatePaymentEmailTemplate({
              recipientEmail: email,
              recipientName: fullName,
              paymentUrl,
              agentProfile,
            });
            await sendEmail(paymentLinkRequest);
          }

          if (mobileNum) {
            const smsBody = generateSMSTemplate({
              mobileNum,
              paymentUrl,
            });
            await sendSms(smsBody);
          }
        }
      } catch {
        alertError(t('eApp:payment.failedToSend'));
        return;
      }

      if (paymentLinkResponse.paymentTransactionId) {
        if (inputSubPaymentMethod === SubPaymentMethod.VIA_LINK) {
          Toast.show(
            [
              {
                message: t('eApp:payment.link.sent'),
                IconLeft: <Icon.Tick />,
              },
            ],
            {
              duration: DELAY_TIME_IN_MS,
              type: 'success',
            },
          );
        }

        setTimeout(() => {
          if (my_mockPaymentResult === 'success') {
            onPaymentSubmitted('09a2e996-6a86-4066-8f85-cf857166ace6');
          } else if (my_mockPaymentResult === 'failed') {
            onPaymentSubmitted('54d16881-0a20-4a5c-b497-d39236c2442d');
          } else {
            onPaymentSubmitted(paymentLinkResponse.paymentTransactionId);
          }
          if (inputSubPaymentMethod === SubPaymentMethod.IN_APP) {
            onShowInAppPayment();
          }
        }, DELAY_TIME_IN_MS);
      }
    } else {
      const initialContributionAmount = String(
        round(
          advanceContributionAmount +
            (quotation?.summary?.initialPrem ?? 0) +
            (quotation?.summary?.adhocPrem || 0),
          2,
        ),
      );
      switch (inputPaymentMethod) {
        case PaymentMethod.BIRO:
          handleSubmitApplication();
          break;
        case PaymentMethod.CHEQUE:
          updateMY_ChequePayment({
            initialContributionAmount,
            chequeAmount: initialContributionAmount,
          });
          break;
        case PaymentMethod.DIRECT_TRANSFER:
          updateMY_DirectTransferPayment({
            initialContributionAmount,
            transactionAmount: initialContributionAmount,
          });
          break;
      }
      onPaymentSubmitted('');
    }
  };

  const handleSubmitRef = useLatest(handleSubmit);
  const setOnLeave = useEAppStore(state => state.setOnLeave);
  const { prompt } = usePromptContext();
  useEffect(() => {
    setOnLeave(async () => {
      if (
        (caseObj?.application?.advancePayment?.duration || 0) !==
        (my_advanceContributionDuration || 0)
      ) {
        const accept = await prompt({
          title: t('eApp:advanceContribution.changeTitle'),
          description: t('eApp:advanceContribution.changeDescription'),
          actions: getConfirmationActions,
          config: {
            dismiss: t('eApp:no'),
            accept: t('eApp:yes'),
          },
        });
        if (accept) {
          const paymentMethod = caseObj?.application
            ?.paymentMethod as PaymentMethod;
          const subPaymentMethod = caseObj?.application?.isInAppPayment
            ? SubPaymentMethod.IN_APP
            : SubPaymentMethod.VIA_LINK;
          setPaymentMethod(paymentMethod);
          setSubPaymentMethod(subPaymentMethod);
          setProgressBarState({
            groupKey: 'payment',
            subgroupKey: undefined,
            itemKey: undefined,
          });
          handleSubmitRef.current(paymentMethod, subPaymentMethod);
          return true;
        } else {
          const duration = caseObj?.application?.advancePayment?.duration || 0;
          setMY_AdvanceContributionDuration(duration);
          setMY_HasAdvanceContribution(duration > 0);
          return false;
        }
      }
      return false;
    });
  }, [
    setOnLeave,
    my_advanceContributionDuration,
    caseObj?.application?.advancePayment?.duration,
    prompt,
    t,
    setMY_AdvanceContributionDuration,
    setMY_HasAdvanceContribution,
    handleSubmitRef,
    setMY_PaymentMethod,
    paymentMethod,
    setMY_SubPaymentMethod,
    subPaymentMethod,
    caseObj?.application?.paymentMethod,
    caseObj?.application?.isInAppPayment,
    setProgressBarState,
  ]);

  return (
    <Box flex={1}>
      <Row
        flex={1}
        bgColor={colors.surface}
        gap={space[10]}
        paddingRight={space[10]}>
        <Box
          width={'37%'}
          bgColor={colors.background}
          paddingLeft={space[10]}
          paddingRight={space[8]}>
          <Plan isBiro={paymentMethod === PaymentMethod.BIRO} />
        </Box>
        <Box flex={1} alignContent="center">
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            ref={scrollViewRef}>
            <Box h={space[6]} />
            {isAgency && (
              <AdvanceContributionForm
                premiumByPaymentMode={premiumByPaymentMode}
                paymentMode={paymentMode}
                paymentMethod={paymentMethod}
                onAdvanceContributionSelected={() => {
                  paymentMethodViewRef.current?.measure(
                    (x, y, w, h, px, py) => {
                      scrollViewRef.current?.scrollTo({
                        y,
                      });
                    },
                  );
                }}
              />
            )}
            <View ref={paymentMethodViewRef}>
              <PaymentMethods
                onPressTC={showTermAndCondition}
                paymentMethod={paymentMethod}
                setPaymentMethod={handleSetPaymentMethod}
                setSubPaymentMethod={setSubPaymentMethod}
                subPaymentMethod={subPaymentMethod}
              />
            </View>
            <Box h={space[6]} />
          </ScrollView>
        </Box>
      </Row>
      <TermAndConditionModal
        visible={isTermConditionVisible}
        onAgree={hideTermAndCondition}
      />
      <EMandateConfirmationModal
        visible={eMandateConfirmationVisible}
        onDismiss={hideEMandateConfirmationVisible}
        onConfirm={() => {
          hideEMandateConfirmationVisible();
          handleSubmit();
        }}
      />

      <EAppFooterTablet
        progressLock="payment-"
        onPrimaryPress={async () => {
          GATracking.logCustomEvent('application', {
            action_type: 'eapp_submit_payment',
            application_type: 'F2F',
          });
          switch (paymentMethod) {
            case PaymentMethod.CHEQUE:
            case PaymentMethod.BIRO:
            case PaymentMethod.DIRECT_TRANSFER:
              await handleSubmit();
              break;
            case PaymentMethod.FPX:
              showEMandateConfirmationVisible();
              break;
            default:
              await handleSubmit();
              break;
          }
        }}
        primaryDisabled={
          isEmpty(paymentMethod) ||
          ((paymentMethod === PaymentMethod.CREDIT ||
            paymentMethod === PaymentMethod.FPX) &&
            isEmpty(subPaymentMethod))
        }
        primaryLoading={
          isCheckingPaymentStatus ||
          isSavingApplication ||
          isGeneratingPaymentLink ||
          isRequestPaymentLink ||
          isSendingEmail ||
          isSendingSms
        }
        primaryLabel={
          paymentMethod === PaymentMethod.BIRO
            ? t('eApp:payment.submit')
            : t('eApp:payment.proceed')
        }
      />
    </Box>
  );
});

export default PaymentSubmission;

export const getPaymentGatewayCode = (
  paymentMethod: PaymentMethod | null | undefined,
  channel: string,
) => {
  switch (paymentMethod) {
    case PaymentMethod.CREDIT:
      if (channel === CHANNELS.AGENCY) {
        return 'RMS-AG-CC-FULL';
      } else if (channel === CHANNELS.BANCA) {
        return 'RMS-BC-CC-FULL';
      }
      return '';
    case PaymentMethod.FPX:
      if (channel === CHANNELS.AGENCY) {
        return 'RMS-AG-FPX';
      } else if (channel === CHANNELS.BANCA) {
        return 'RMS-BC-FPX';
      }
      return '';
    default:
      return '';
  }
};
