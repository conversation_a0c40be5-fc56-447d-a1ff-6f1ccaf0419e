import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Column,
  Currency<PERSON>extField,
  Picker,
  PictogramIcon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import {
  Control,
  UseFormSetValue,
  useController,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ApplicationDetailsTabletSectionContainer from '../../../../../../common/components/ApplicationDetailsTabletSectionContainer';
import { PolicyOwnerFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/policyOwnerInfoValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import { TaxType } from 'types/person';
import {
  getOptionListLabel,
  getOptionListValue,
  INCOME_GREATER_THAN_200K,
  NON_INCOME_OCC_GROUP,
} from 'constants/optionList';
import { InsuredFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import AutocompletePopup from 'components/AutocompletePopup';
import { IncomeRange, Nationality } from 'types/optionList';
import Autocomplete from 'components/Autocomplete';
import AnnualIncome from '../../../AnnualIncome';

interface Props {
  control: Control<PolicyOwnerFormSchemaType & InsuredFormSchemaType>;
  setValue: UseFormSetValue<PolicyOwnerFormSchemaType & InsuredFormSchemaType>;
  isOwner?: boolean;
}

const OccupationDetails = (props: Props) => {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const { data: optionList } = useGetOptionList<'my'>();
  const [isDone, setDone] = useState<boolean>(false);

  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({
    name: 'occupationGroup',
    control,
  });

  const occupation = useWatch({
    name: 'occupation',
    control: control,
  });
  const nameOfBusiness = useWatch({
    name: 'nameOfBusiness',
    control: control,
  });
  const natureOfWork = useWatch({
    name: 'natureOfWork',
    control: control,
  });
  const exactDuties = useWatch({
    name: 'exactDuties',
    control: control,
  });
  const annualIncome = useWatch({
    name: 'annualIncome',
    control: control,
  });
  const annualIncomeAmount = useWatch({
    name: 'annualIncomeAmount',
    control: control,
  });
  const taxPurpose = useWatch({
    name: 'taxPurpose',
    control: control,
  });

  const { occupationClass, occupationGroup, occupationDescription } =
    useOccupationClass(occupation);

  useEffect(() => {
    if (
      occupation &&
      (occupationGroup === NON_INCOME_OCC_GROUP ||
        (nameOfBusiness && natureOfWork && exactDuties && annualIncome)) &&
      (!props.isOwner || taxPurpose)
    ) {
      setDone(true);
    } else {
      setDone(false);
    }
  }, [
    occupation,
    nameOfBusiness,
    natureOfWork,
    exactDuties,
    annualIncome,
    taxPurpose,
    props.isOwner,
    occupationGroup,
  ]);

  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: 'occupationDescription', control });

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
    onChangeOccupationGroup(occupationGroup);
  }, [
    occupationDescription,
    occupationGroup,
    onChangeOccupationDescription,
    onChangeOccupationGroup,
  ]);

  const annualIncomeValue = useWatch({
    name: 'annualIncome',
    control: control,
  });

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: 'annualIncomeAmount', control });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:certificate.occupationTitle')}
      icon={<PictogramIcon.Work3 size={40} />}
      isDone={isDone}>
      <Content>
        <RowInfo>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.form.occupation')}
            </Typography.SmallLabel>

            <Typography.Body>
              {
                optionList?.OCCUPATION.options.find(e => occupation == e.value)
                  ?.label
              }
            </Typography.Body>
          </Column>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationClass')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationClass?.label.en}</Typography.Body>
          </Column>

          <Column flex={4} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationDescription')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationDescription}</Typography.Body>
          </Column>
        </RowInfo>

        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="nameOfBusiness"
            label={t('eApp:certificate.form.nameOfBusiness')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value =>
              occupationGroup !== NON_INCOME_OCC_GROUP && !value
            }
          />
          <Input
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name="natureOfWork"
            label={t('eApp:certificate.form.natureOfWork')}
            data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            searchable
            shouldHighlightOnUntouched={value =>
              occupationGroup !== NON_INCOME_OCC_GROUP && !value
            }
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="exactDuties"
            label={t('eApp:certificate.form.exactDuties')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value =>
              occupationGroup !== NON_INCOME_OCC_GROUP && !value
            }
          />
          <Input
            control={control}
            as={AnnualIncome}
            name="annualIncome"
            shouldHighlightOnUntouched={value =>
              occupationGroup !== NON_INCOME_OCC_GROUP && !value
            }
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={CurrencyTextField}
            name="annualIncomeAmount"
            label={t('eApp:certificate.form.annualIncomeAmount')}
            style={eAppCommonStyles.tabletTextField}
            disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
            shouldHighlightOnUntouched={value =>
              occupationGroup !== NON_INCOME_OCC_GROUP && !value
            }
          />
          {props.isOwner ? (
            <Input
              control={control}
              as={Picker}
              name="taxPurpose"
              label={t('eApp:taxTitle')}
              highlight={taxPurpose === undefined || taxPurpose === ''}
              items={[
                {
                  value: TaxType.BUSINESS,
                  text: t('eApp:business'),
                },
                {
                  value: TaxType.PRIVATE,
                  text: t('eApp:private'),
                },
              ]}
              style={{ flex: 1, marginTop: 7 }}
            />
          ) : (
            <Box flex={1} />
          )}
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};

export default OccupationDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));

const RowInfo = styled(Row)(({ theme: { space, colors } }) => ({
  gap: space[8],
  flex: 1,
  borderRadius: space[2],
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[5],
  marginTop: space[5],
  marginBottom: space[3],
}));
