import styled from '@emotion/native';
import Input from 'components/Input';
import {
  <PERSON>um<PERSON>,
  Picker,
  Pictogram<PERSON><PERSON>,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CountryCode, PrefContactMode } from 'types/optionList';
import ApplicationDetailsTabletSectionContainer from '../../../../../../common/components/ApplicationDetailsTabletSectionContainer';
import Autocomplete from 'components/Autocomplete';
import { PolicyOwnerFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/policyOwnerInfoValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import ReadOnlyField from '../../../../../../common/components/ReadOnlyField';
import { formatPhoneNumber } from 'utils';
import { InsuredFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import PhoneField from 'components/PhoneField';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { getCountryCodeDisplayedLabel } from 'constants/optionList';
import { getOptionListLabel } from 'constants/optionList';
import { getCountryCodeValue } from 'constants/optionList';
import AutocompletePopup from 'components/AutocompletePopup';

interface Props {
  control: Control<PolicyOwnerFormSchemaType & InsuredFormSchemaType>;
  setValue: UseFormSetValue<PolicyOwnerFormSchemaType & InsuredFormSchemaType>;
  getValues: UseFormGetValues<
    PolicyOwnerFormSchemaType & InsuredFormSchemaType
  >;
}

const ContactDetails = (props: Props) => {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const [isDone, setDone] = useState<boolean>(false);
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'my'>();

  const mobileCountryCode = useWatch({
    name: 'mobileCountryCode',
    control: control,
  });
  const mobileNumber = useWatch({
    name: 'mobileNumber',
    control: control,
  });

  const homeNumber = useWatch({
    name: 'homeNumber',
    control: control,
  });

  const officeNumber = useWatch({
    name: 'officeNumber',
    control: control,
  });
  const email = useWatch({
    name: 'email',
    control: control,
  });
  const preferredCertificateCopy = useWatch({
    name: 'preferredCertificateCopy',
    control: control,
  });
  const preferredContactMode = useWatch({
    name: 'preferredContactMode',
    control: control,
  });
  const preferredLanguage = useWatch({
    name: 'preferredLanguage',
    control: control,
  });

  useEffect(() => {
    if (
      mobileCountryCode &&
      mobileNumber &&
      email &&
      preferredCertificateCopy &&
      preferredContactMode &&
      preferredLanguage
    ) {
      setDone(true);
    } else {
      setDone(false);
    }
  }, [
    mobileCountryCode,
    mobileNumber,
    email,
    preferredCertificateCopy,
    preferredContactMode,
    preferredLanguage,
  ]);

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:certificate.contactTitle')}
      icon={<PictogramIcon.Phone size={40} />}
      isDone={isDone}>
      <Content>
        <RowBox>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="mobileCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="mobileNumber"
              label={t('eApp:certificate.form.mobileNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            />
          </Row>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('eApp:email')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
          />
        </RowBox>

        <RowBox>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="homeCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="homeNumber"
              label={t('eApp:certificate.form.homePhone')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
          </Row>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="officeCountryCode"
              label={t('eApp:certificate.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={eAppCommonStyles.tabletCountryCode}
              searchable
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
            <Input
              control={control}
              as={PhoneField}
              name="officeNumber"
              label={t('eApp:certificate.form.officePhone')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={eAppCommonStyles.tabletTextField}
              inputStyle={eAppCommonStyles.tabletPhoneNumberInput}
            />
          </Row>
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<PrefContactMode, string>}
            name="preferredContactMode"
            label={t('eApp:certificate.form.preferredContact')}
            data={optionList?.PREF_CONTACT_MODE.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1, marginTop: 7 }}
          />
          <Input
            control={control}
            as={Picker}
            name="preferredLanguage"
            label={t('eApp:certificate.form.preferredDocument')}
            items={optionList?.PREF_DOC_LANG.options.map(o => ({
              value: o.value,
              text: o.label,
            }))}
            style={{ flex: 1, marginTop: 7 }}
          />
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};

export default ContactDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));
