import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import {
  INCOME_GREATER_THAN_200K,
  NON_INCOME_OCC_GROUP,
} from 'constants/optionList';
import {
  Column,
  CurrencyTextField,
  LargeBody,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import WorkSVG from 'features/eAppV2/common/components/icons/WorkSVG';
import { InsuredFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Control,
  UseFormResetField,
  UseFormSetValue,
  useController,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CHANNELS } from 'types/channel';
import { IncomeRange, Nationality } from 'types/optionList';
import AnnualIncome from '../../../AnnualIncome';

interface Props {
  control: Control<{ data: InsuredFormSchemaType[] }>;
  index: number;
  isLatest?: boolean;
  resetField: UseFormResetField<{ data: InsuredFormSchemaType[] }>;
  forEntity?: boolean;
  setValue: UseFormSetValue<{ data: InsuredFormSchemaType[] }>;
}

const OccupationDetails = (props: Props) => {
  const { index, control } = props;
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;

  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'my'>();
  const [isDone, setDone] = useState<boolean>(false);

  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({
    name: `data.${index}.occupationGroup`,
    control,
  });

  const occupation = useWatch({
    name: `data.${index}.occupation`,
    control: control,
  });
  const nameOfBusiness = useWatch({
    name: `data.${index}.nameOfBusiness`,
    control: control,
  });
  const natureOfWork = useWatch({
    name: `data.${index}.natureOfWork`,
    control: control,
  });
  const exactDuties = useWatch({
    name: `data.${index}.exactDuties`,
    control: control,
  });
  const annualIncome = useWatch({
    name: `data.${index}.annualIncome`,
    control: control,
  });

  const { occupationClass, occupationGroup, occupationDescription } =
    useOccupationClass(occupation);

  const isDetailRequired = occupationGroup !== NON_INCOME_OCC_GROUP;

  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: `data.${index}.occupationDescription`, control });

  useEffect(() => {
    if (
      occupation &&
      (!isDetailRequired || (nameOfBusiness && natureOfWork && exactDuties))
    ) {
      setDone(true);
    } else {
      setDone(false);
    }
  }, [
    occupation,
    nameOfBusiness,
    natureOfWork,
    exactDuties,
    annualIncome,
    isDetailRequired,
  ]);

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
    onChangeOccupationGroup(occupationGroup);
  }, [
    occupationDescription,
    occupationGroup,
    onChangeOccupationDescription,
    onChangeOccupationGroup,
  ]);

  const annualIncomeValue = useWatch({
    name: `data.${index}.annualIncome`,
    control: control,
  });

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: `data.${index}.annualIncomeAmount`, control });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  const shouldHighlight = useMemo(() => {
    return occupationGroup !== NON_INCOME_OCC_GROUP;
  }, [occupationGroup]);

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:certificate.occupationTitle')}
      icon={<WorkSVG />}
      isDone={isDone}>
      <Content>
        <RowInfo>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.form.occupation')}
            </Typography.SmallLabel>

            <Typography.Body>
              {
                optionList?.OCCUPATION.options.find(e => occupation == e.value)
                  ?.label
              }
            </Typography.Body>
          </Column>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationClass')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationClass?.label.en}</Typography.Body>
          </Column>

          <Column flex={4} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationDescription')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationDescription}</Typography.Body>
          </Column>
        </RowInfo>

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name={`data.${index}.nameOfBusiness`}
            label={t('eApp:certificate.form.nameOfBusiness')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
          <Input
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name={`data.${index}.natureOfWork`}
            label={t('eApp:certificate.form.natureOfWork')}
            data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1, marginTop: 7 }}
            searchable
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
        </RowBox>

        {/* 3 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name={`data.${index}.exactDuties`}
            label={t('eApp:certificate.form.exactDuties')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
          <Input
            control={control}
            as={AnnualIncome}
            name={`data.${index}.annualIncome`}
            label={t('eApp:certificate.form.annualIncomeOptional')}
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={CurrencyTextField}
            name={`data.${index}.annualIncomeAmount`}
            label={t('eApp:certificate.form.annualIncomeAmount')}
            style={{ flex: 1, marginTop: 7 }}
            disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
          />
          <Column flex={1} />
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};

export default OccupationDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));

const RowInfo = styled(Row)(({ theme: { space, colors } }) => ({
  gap: space[8],
  flex: 1,
  borderRadius: space[2],
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[5],
  marginTop: space[5],
}));

const Reminder = styled(LargeBody)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    borderRadius: borderRadius.small,
    padding: space[4],
    flex: 1,
    color: colors.primary,
  }),
);
