import { useTheme } from '@emotion/react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import {
  Box,
  ExtraLargeBody,
  Icon,
  PictogramIcon,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import AddressInformationForm, {
  AddressInfo,
} from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import {
  payorDetailsAddressInformationDefaultValue,
  PayorDetailsSchemaType,
} from 'features/eAppV2/my/validations/applicationDetails/payorInfoValidation';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import { payorDetailsAddressInformationValidationSchema } from 'features/eAppV2/my/validations/applicationDetails/payorInfoValidation';
import { InferType } from 'yup';
interface Props {
  control: Control<PayorDetailsSchemaType>;
  setValue: UseFormSetValue<PayorDetailsSchemaType>;
  getValues: UseFormGetValues<PayorDetailsSchemaType>;
  trigger: UseFormTrigger<PayorDetailsSchemaType>;
}

export default function AddressInformation(props: Props) {
  const { control, setValue, getValues, trigger } = props;
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const isEntity = useCheckEntity();

  const poInfo = useEAppStore(state => state.my_policyOwnerPersonalInfo);
  const isValid = useSchemaValid(
    control as unknown as Control<
      InferType<typeof payorDetailsAddressInformationValidationSchema>
    >,
    payorDetailsAddressInformationDefaultValue,
    payorDetailsAddressInformationValidationSchema,
  );

  return (
    <Box
      bgColor={colors.background}
      py={space[6]}
      borderRadius={space[4]}
      mt={space[4]}>
      <Row mb={space[5]} px={space[6]} alignItems="center">
        {isValid ? (
          <Icon.TickCircle size={space[8]} fill={colors.palette.alertGreen} />
        ) : (
          <Icon.TickCircle
            size={space[8]}
            fill={colors.palette.fwdDarkGreen[20]}
          />
        )}

        <Box w={space[2]} />
        <PictogramIcon.Home size={space[10]} />
        <Box w={space[2]} />
        <ExtraLargeBody fontWeight="bold">
          {t('eApp:certificate.addressInfoTitle')}
        </ExtraLargeBody>
      </Row>
      <AddressInformationForm
        addressTypes={['correspondence', 'residential']}
        ownerAddress={poInfo as unknown as AddressInfo}
        control={control as unknown as Control<AddressInfo>}
        setValue={setValue as unknown as UseFormSetValue<AddressInfo>}
        getValues={getValues as unknown as UseFormGetValues<AddressInfo>}
        trigger={trigger as unknown as UseFormTrigger<AddressInfo>}
        isEntityPayor={isEntity}
        isPayor={true}
      />
    </Box>
  );
}
