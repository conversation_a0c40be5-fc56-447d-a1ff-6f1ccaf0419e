import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Control, useWatch } from 'react-hook-form';
import {
  Box,
  Column,
  ExtraLargeBody,
  Icon,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { CountryCode } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import AutocompletePopup from 'components/AutocompletePopup';
import { phoneNumberStyle } from 'features/customerFactFind/styles/phoneNumberStyle';
import PhoneField from 'components/PhoneField';
import { PayorDetailsSchemaType } from 'features/eAppV2/my/validations/applicationDetails/payorInfoValidation';
import { getCountryCodeDisplayedLabel, getCountryCodeValue, getOptionListLabel } from 'constants/optionList';

interface Props {
  control: Control<PayorDetailsSchemaType>;
}

export default function ContactDetails(props: Props) {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();

  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'my'>();

  const mobileCountryCode = useWatch({
    name: 'mobileCountryCode',
    control: control,
  });

  const mobileNumber = useWatch({
    name: 'mobileNumber',
    control: control,
  });

  const isValid = useMemo(
    () => mobileCountryCode && mobileNumber,
    [mobileCountryCode, mobileNumber],
  );

  return (
    <Box
      bgColor={colors.background}
      p={space[6]}
      borderRadius={space[4]}
      mt={space[4]}>
      <Row alignItems="center">
        {isValid ? (
          <Icon.TickCircle size={space[8]} fill={colors.palette.alertGreen} />
        ) : (
          <Icon.TickCircle
            size={space[8]}
            fill={colors.palette.fwdDarkGreen[20]}
          />
        )}

        <Box w={space[2]} />
        <PictogramIcon.Phone size={40} />
        <Box w={space[2]} />
        <ExtraLargeBody fontWeight="bold">
          {t('eApp:certificate.contactTitle')}
        </ExtraLargeBody>
      </Row>

      <Content>
        {/* 1 */}
        <Box flex={1} flexDirection="row">
          <Box flex={1}>
            <Row flex={1}>
              <Input
                control={control}
                as={AutocompletePopup<CountryCode, string>}
                name="mobileCountryCode"
                label={t('eApp:certificate.form.countryCode')}
                data={optionList?.COUNTRY_CODE.options ?? []}
                disabled={isLoadingOptionList}
                getItemValue={getCountryCodeValue}
                getItemLabel={getOptionListLabel}
                getDisplayedLabel={getCountryCodeDisplayedLabel}
                style={{ width: 120, marginRight: space[3] }}
                searchable
                inputStyle={phoneNumberStyle.input}
              />
              <Box flex={4}>
                <Input
                  control={control}
                  as={PhoneField}
                  name="mobileNumber"
                  label={t('eApp:certificate.form.mobileNumber')}
                  keyboardType="number-pad"
                  returnKeyType="done"
                  inputStyle={phoneNumberStyle.input}
                />
              </Box>
            </Row>
          </Box>
          <Box w={space[6]} />
          <Box flex={1} />
        </Box>
      </Content>
    </Box>
  );
}

const Content = styled(Column)(({ theme: { colors } }) => ({
  backgroundColor: colors.background,
  marginTop: 27,
}));
