import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  CurrencyTextField,
  H7,
  Label,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useCallback, useEffect } from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { RocForm } from '../../../../validations/applicationDetails/declarationValidation';
import SectionWithTitle from 'features/eAppV2/common/components/SectionWithTitle';
import Input from 'components/Input';

interface RocProps {
  control: Control<RocForm>;
}

const Roc = (props: RocProps) => {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const { space, sizes } = useTheme();

  const hasReplacePlan = useWatch({
    name: 'hasReplacePlan',
    control: control,
  });

  const terminateInfluence = useWatch({
    name: 'terminateInfluence',
    control: control,
  });

  const {
    field: { onChange: setReplacementReason },
  } = useController({
    control,
    name: 'replacementReason',
  });

  const {
    field: { onChange: setTakafulOperator },
  } = useController({
    control,
    name: 'takafulOperator',
  });

  const {
    field: { onChange: setPlanName },
  } = useController({
    control,
    name: 'planName',
  });

  const {
    field: { onChange: setSumCovered },
  } = useController({
    control,
    name: 'sumCovered',
  });

  const {
    field: { onChange: setSatisfiedWithExplanation },
  } = useController({
    control,
    name: 'satisfiedWithExplanation',
  });

  const {
    field: { onChange: setComment },
  } = useController({
    control,
    name: 'comment',
  });

  useEffect(() => {
    if (hasReplacePlan === 'no') {
      setReplacementReason('');
      setTakafulOperator('');
      setPlanName('');
      setSumCovered('');
    }
  }, [hasReplacePlan]);

  useEffect(() => {
    if (terminateInfluence === 'no') {
      setSatisfiedWithExplanation('');
      setComment('');
    }
  }, [terminateInfluence]);

  const renderReplacePlan = useCallback(() => {
    return (
      <>
        <Row mt={space[4]} gap={space[6]}>
          <Box flex={1}>
            <Input
              control={control}
              as={TextField}
              name={'replacementReason'}
              label={t('eApp:declaration.roc.replacementReason')}
            />
          </Box>
          <Box flex={1}>
            <Input
              control={control}
              as={TextField}
              name={'takafulOperator'}
              label={t('eApp:declaration.roc.takafulOperator')}
            />
          </Box>
        </Row>
        <Row mt={space[5]} gap={space[6]}>
          <Box flex={1}>
            <Input
              control={control}
              as={TextField}
              name={'planName'}
              label={t('eApp:declaration.roc.planName')}
            />
          </Box>
          <Box flex={1}>
            <Input
              control={control}
              as={CurrencyTextField}
              name={'sumCovered'}
              label={t('eApp:declaration.roc.sumCovered')}
            />
          </Box>
        </Row>
      </>
    );
  }, [control, space, t]);

  const renderOtherParty = useCallback(() => {
    return (
      <>
        <Row mt={space[6]}>
          <H7 fontWeight="bold">
            {t('eApp:declaration.roc.question.number.2a')}
          </H7>
          <H7 fontWeight="bold">{t('eApp:declaration.roc.question.2a')}</H7>
        </Row>

        <Row mt={space[4]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'satisfiedWithExplanation'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>

        <Box flex={1} mt={space[4]}>
          <Label fontWeight="bold">{t('eApp:declaration.roc.comment')}</Label>
          <Box mt={space[4]} />
          <Input
            control={control}
            as={TextField}
            name={'comment'}
            label={t('eApp:declaration.roc.comment')}
          />
        </Box>
      </>
    );
  }, [control, space, t]);

  return (
    <SectionWithTitle title={t('eApp:declaration.roc.title')}>
      <Content>
        <Row>
          <H7 fontWeight="bold">
            {t('eApp:declaration.roc.question.number.1')}
          </H7>
          <H7 fontWeight="bold">{t('eApp:declaration.roc.question.1')}</H7>
        </Row>

        <Row mt={space[4]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'hasReplacePlan'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        {hasReplacePlan === 'yes' && renderReplacePlan()}

        <Box flex={1} height={sizes[6]} />
        <Divider />
        <Row mt={space[6]}>
          <H7 fontWeight="bold">
            {t('eApp:declaration.roc.question.number.2')}
          </H7>
          <H7 fontWeight="bold">{t('eApp:declaration.roc.question.2')}</H7>
        </Row>

        <Row mt={space[4]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'terminateInfluence'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        {terminateInfluence === 'yes' && renderOtherParty()}

        <Box flex={1} height={sizes[6]} />
        <Divider />

        <Row mt={space[6]}>
          <H7 fontWeight="bold">
            {t('eApp:declaration.roc.question.number.3')}
          </H7>
          <H7 fontWeight="bold">{t('eApp:declaration.roc.question.3')}</H7>
        </Row>

        <Row mt={space[4]} mb={space[6]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'isCoverageExtension'}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButtonEnd value="no" label={t('eApp:no')} />
          </Input>
        </Row>
      </Content>
    </SectionWithTitle>
  );
};

export default Roc;

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
  marginTop: space[6],
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export const RadioButtonTopAlign = styled(RadioButton)(() => {
  return {
    alignItems: 'flex-start',
  };
});

export const RadioButtonEnd = styled(RadioButton)(({ theme: { space } }) => {
  return {
    marginLeft: space[8],
  };
});
