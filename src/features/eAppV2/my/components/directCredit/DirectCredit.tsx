import styled from '@emotion/native';
import { Row } from 'cube-ui-components';
import { Fragment, memo, useCallback, useMemo, useRef, useState } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { DirectCreditDetails } from './directCreditForm';
import EAppFooterTablet from '../../../common/components/footer/EAppFooter.tablet';
import TabletSections from '../../../common/components/TabletSections';
import { useTranslation } from 'react-i18next';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { DirectCreditDetailsRef } from './directCreditForm/DirectCreditForm';
import { useEAppAlert } from 'features/eAppV2/common/hooks/useEAppAlert';

export const DirectCredit = memo(function () {
  const { t } = useTranslation(['eApp']);
  const { alertError } = useEAppAlert();
  const [isDirectCreditValid, setDirectCreditValid] = useState(false);
  const directCreditHandleSubmitRef = useRef<() => Promise<void> | undefined>();
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);

  const updateHandleSubmit = useCallback((cb: () => Promise<void>) => {
    directCreditHandleSubmitRef.current = cb;
  }, []);

  const isValid = isDirectCreditValid;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSubmit = async () => {
    if (directCreditHandleSubmitRef.current) {
      setIsSubmitting(true);
      try {
        await directCreditHandleSubmitRef.current();
        nextGroup(true);
      } catch {
        alertError(handleSubmit);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const formRef = useRef<DirectCreditDetailsRef>(null);
  const [totalIncompleteFields, setTotalIncompleteFields] = useState<number>(0);

  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const sections = useMemo(() => {
    return [
      {
        name: 'directCredit',
        title: t('eApp:directCredit.menu'),
        content: (
          <ScrollViewContainer ref={scrollRef} key={'DirectCredit'}>
            <DirectCreditDetails
              updateValid={setDirectCreditValid}
              updateHandleSubmit={updateHandleSubmit}
              updateTotalIncompleteFields={total =>
                setTotalIncompleteFields(total)
              }
              ref={formRef}
              scrollRef={scrollRef}
            />
            <BoxSpace />
          </ScrollViewContainer>
        ),
      },
    ];
  }, [t, updateHandleSubmit]);

  return (
    <Fragment>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock="directCredit-"
        onPrimaryPress={handleSubmit}
        totalIncompleteRequiredFields={totalIncompleteFields}
        focusOnIncompleteField={() => {
          formRef?.current?.focusIncompleteField?.();
        }}
        primaryDisabled={!isValid}
        primarySubLabel={t('eApp:bar.uploadDocument')}
        primaryLoading={isSubmitting}
      />
    </Fragment>
  );
});
export default DirectCredit;

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
