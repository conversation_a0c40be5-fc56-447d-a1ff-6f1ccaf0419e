import { MY_COUNTRY, NEW_NRIC } from 'constants/optionList';
import { PictogramIcon } from 'cube-ui-components';
import { parse } from 'date-fns';
import { RelationshipValue } from 'features/proposal/types';
import { TFunction } from 'i18next';
import React from 'react';
import { Occupation, OptionList } from 'types/optionList';
import { Party, PartyRole, PartyType } from 'types/party';
import { TaxType } from 'types/person';
import { formatCurrency, formatPhoneNumber } from 'utils';
import { calculateAge } from 'utils/helper/calculateAge';
import { capitalFirst, dateFormatWithSlashUtil } from 'utils/helper/formatUtil';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import { formatNewNricNumber } from 'utils/helper/idNumberUtils';
import { getRelationshipGroup } from 'utils/helper/optionListUtils';
import { formatAddress } from '../utils/formatAddress';
import { PersonalInformationReviewParams } from 'features/eAppV2/common/types/reviewTypes';
import IconWork from 'features/eAppV2/ph/components/applicationDetails/sections/icons/IconWork';
import { parsePartyDob } from 'utils/helper/dateUtil';

export function useGetReviewSectionsData() {
  const onGetSectionData = (
    party: Party,
    t: TFunction<['eApp']>,
    optionList?: OptionList<string, 'my'>,
    isBancaSourceOfFundWealthRequired?: boolean,
    isBanca?: boolean,
  ) => {
    const relationshipGroup = getRelationshipGroup(
      party.relationship,
      optionList,
    );
    const occupationPicked = (
      optionList?.OCCUPATION.options as Occupation<string, 'my'>[]
    ).find(e => e.value === party.person?.occupation?.natureOfSubWork);
    const defaultId = party.person?.registrations?.find(
      r => r.type === 'DEFAULT',
    );
    const additionalId = party.person?.registrations?.find(
      r => r.type === 'ADDITIONAL',
    );
    const occupationClass = occupationPicked?.occupationClass.label.en || '--';
    const occupationDescription = occupationPicked?.occupationDesc || '--';
    const mobileContact = party.contacts.phones.find(i => i.type === 'MOBILE');
    const homeContact = party.contacts.phones.find(i => i.type === 'HOME');
    const officeContact = party.contacts.phones.find(i => i.type === 'WORK');
    const correspondenceAddress = party.addresses?.find(
      a => a.addressType === 'MAIN',
    );
    const residentialAddress = party.addresses?.find(
      a => a.addressType === 'HOME',
    );
    const businessAddress = party.addresses?.find(
      a => a.addressType === 'WORK',
    );
    const identificationNumber =
      defaultId?.idType === NEW_NRIC
        ? formatNewNricNumber(defaultId.id)
        : defaultId?.id || '--';
    const formattedCorrespondenceAddress = formatAddress(
      'correspondence',
      {
        correspondenceAddressLine1: correspondenceAddress?.street || '',
        correspondenceAddressLine2: correspondenceAddress?.subDistrict || '',
        correspondenceAddressLine3: correspondenceAddress?.district || '',
        correspondencePostCode: correspondenceAddress?.zipCode || '',
        correspondenceCity: correspondenceAddress?.city || '',
        correspondenceState: correspondenceAddress?.province || '',
        correspondenceCountry: correspondenceAddress?.countryCode || MY_COUNTRY,
      },
      optionList,
    );
    const formattedResidentialAddress =
      residentialAddress?.businessAddressOpt === 'correspondence'
        ? formattedCorrespondenceAddress
        : formatAddress(
            'residential',
            {
              residentialAddressLine1: residentialAddress?.street || '',
              residentialAddressLine2: residentialAddress?.subDistrict || '',
              residentialAddressLine3: residentialAddress?.district || '',
              residentialPostCode: residentialAddress?.zipCode || '',
              residentialCity: residentialAddress?.city || '',
              residentialState: residentialAddress?.province || '',
              residentialCountry: residentialAddress?.countryCode || MY_COUNTRY,
            },
            optionList,
          );
    const formattedBusinessAddress = formatAddress(
      'business',
      {
        businessAddressLine1: businessAddress?.street || '',
        businessAddressLine2: businessAddress?.subDistrict || '',
        businessAddressLine3: businessAddress?.district || '',
        businessPostCode: businessAddress?.zipCode || '',
        businessCity: businessAddress?.city || '',
        businessState: businessAddress?.province || '',
        businessCountry: businessAddress?.countryCode || MY_COUNTRY,
      },
      optionList,
    );
    if (
      party.roles.includes(PartyRole.PROPOSER) &&
      party.clientType === PartyType.ENTITY
    ) {
      return {
        title: t('eApp:companyInfo.companyDetails'),
        sections: [
          {
            title: t('eApp:companyInfo.companyDetails'),
            icon: <PictogramIcon.Building2 size={36} />,
            data: [
              {
                label: t('eApp:companyInfo.companyName'),
                value: party.entity?.name,
              },
              {
                label: t('eApp:companyInfo.regNumber'),
                value: party.entity?.registrationNumber || '--',
              },
              {
                label: t('eApp:companyInfo.registrationDate'),
                value: party.entity?.dateOfRegistration
                  ? dateFormatWithSlashUtil(party.entity?.dateOfRegistration)
                  : '--',
              },
              {
                label: t('eApp:companyInfo.regNumberOld'),
                value: party.entity?.registrationOldNo || '--',
              },
              {
                label: t('eApp:companyInfo.natureOfBusiness'),
                value:
                  optionList?.NATURE_OF_WORK_BUSINESS.options.find(
                    natureOfWork =>
                      natureOfWork.value === party.entity?.natureOfBusiness,
                  )?.label || '--',
              },
            ].filter(d =>
              Boolean(d),
            ) as PersonalInformationReviewParams['sections'][0]['data'],
          },
          {
            title: t('eApp:certificate.contactTitle'),
            icon: <PictogramIcon.Call2 size={36} />,
            data: [
              {
                label: t('eApp:companyInfo.businessPhoneNumber'),
                value: mobileContact
                  ? mobileContact?.number
                    ? formatPhoneNumber(mobileContact?.number)
                    : '--'
                  : officeContact
                  ? officeContact?.number
                    ? formatPhoneNumber(officeContact?.number)
                    : '--'
                  : '--',
              },
              {
                label: t('eApp:review.email'),
                value: party.contacts.email,
              },
              {
                label: t('eApp:companyInfo.preferredContact'),
                value:
                  optionList?.PREF_CONTACT_MODE.options.find(
                    contactMode =>
                      contactMode.value === party.contacts.preferredContactMode,
                  )?.label || '--',
              },
              {
                label: t('eApp:companyInfo.preferredDocument'),
                value:
                  optionList?.PREF_DOC_LANG.options.find(
                    language =>
                      language.value === party.contacts.preferredLanguage,
                  )?.label || '--',
              },
            ],
          },
          {
            title: t('eApp:companyInfo.businessRegistration'),
            icon: <PictogramIcon.Work3 size={36} />,
            perRow: true,
            data: [
              {
                label: t('eApp:certificate.title.businessAddress'),
                value: formattedBusinessAddress,
              },
            ],
          },
          {
            title: t('eApp:companyInfo.taxDetails'),
            icon: <PictogramIcon.TaxWithTax size={36} />,
            data: [
              {
                label: t('eApp:companyInfo.taxPurpose'),
                value:
                  party.entity?.purposeOfInsurance === TaxType.BUSINESS
                    ? t('eApp:business')
                    : t('eApp:private'),
              },
              {
                label: t('eApp:companyInfo.taxNumber'),
                value: party.entity?.taxIdNumber || '--',
              },
              {
                label: t('eApp:companyInfo.taxIdentificationNumber'),
                value: party.entity?.taxIdentificationNumber || '--',
              },
            ],
          },
        ],
      };
    } else if (
      party.roles.includes(PartyRole.PROPOSER) ||
      party.roles.includes(PartyRole.INSURED)
    ) {
      return {
        title:
          relationshipGroup === RelationshipValue.OWNER
            ? t('eApp:policyOwner')
            : t('eApp:insured'),
        sections: [
          {
            title: t('eApp:certificate.participantTitle'),
            icon: <PictogramIcon.Lanyard size={36} />,
            data: [
              party.person?.name.title && {
                label: t('eApp:review.salutationTitle'),
                value:
                  optionList?.CUBE_TITLE.options.find(
                    title => title.value === party.person?.name.title,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.fullName'),
                value: party?.person?.name?.fullName || '--',
              },
              {
                label: t('eApp:review.gender'),
                value:
                  getLabelFromValue(
                    optionList?.GENDER?.options,
                    party.person?.gender,
                  ) || '--',
              },
              {
                label: t('eApp:certificate.form.dateOfBirth'),
                value: party.person?.dateOfBirth?.date
                  ? dateFormatWithSlashUtil(party.person?.dateOfBirth?.date)
                  : '--',
              },
              relationshipGroup === RelationshipValue.OWNER
                ? {
                    label: t('eApp:personalDetails.customerType'),
                    value: 'Individual',
                  }
                : undefined,
              {
                label: t('eApp:certificate.form.age'),
                value: String(
                  party.person?.dateOfBirth?.date
                    ? calculateAge(
                        parsePartyDob(party.person?.dateOfBirth),
                      )
                    : '--',
                ),
              },
              {
                label: t('eApp:certificate.form.race'),
                value:
                  optionList?.ETHNICITY.options.find(
                    race => race.value === party.person?.ethnicityCode,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.religion'),
                value:
                  optionList?.RELIGION.options.find(
                    religion => religion.value === party.person?.religion,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.maritalStatus'),
                value:
                  optionList?.MARITAL.options.find(
                    maritalStatus =>
                      maritalStatus.value === party.person?.maritalStatus,
                  )?.label || '--',
              },
              {
                label: t('eApp:smokingHabit'),
                value: party.person?.isSmoker
                  ? t('eApp:smoker')
                  : t('eApp:nonSmoker'),
              },
              {
                label: t('eApp:personalDetails.primaryIdType'),
                value:
                  optionList?.ID_TYPE.options.find(
                    idType => idType.value === defaultId?.idType,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.identificationNumber'),
                value: identificationNumber,
              },
              {
                label: t('eApp:certificate.form.additionalIdType'),
                value:
                  optionList?.ADD_ID_TYPE.options.find(
                    idType => idType.value === additionalId?.idType,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.additional.identification'),
                value: additionalId?.id || '--',
              },
              relationshipGroup === RelationshipValue.CHILD
                ? {
                    label: t('eApp:review.cert.relationship'),
                    value: capitalFirst(
                      optionList?.RELATIONSHIP.options.find(
                        idType => idType.value === party.relationship,
                      )?.label || '--',
                    ),
                  }
                : undefined,
              {
                label: t('eApp:certificate.form.source'),
                value:
                  optionList?.LEAD_SOURCE.options.find(
                    source => source.value === party.sourceLeadId,
                  )?.label || '--',
              },
            ].filter(d =>
              Boolean(d),
            ) as PersonalInformationReviewParams['sections'][0]['data'],
          },
          {
            title: t('eApp:certificate.nationalityTitle'),
            icon: <PictogramIcon.Globe2 size={36} />,
            data: [
              {
                label: t('eApp:nationalityDetails.nationality'),
                value:
                  optionList?.NATIONALITY.options.find(
                    nationality =>
                      nationality.value === party.person?.nationality,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.countryOfBirth'),
                value:
                  optionList?.COUNTRY.options.find(
                    country => country.value === party.person?.countryOfBirth,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.stateOfBirth'),
                value:
                  optionList?.STATE.options.find(
                    state => state.value === party.person?.stateOfBirth,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.cityOfBirth'),
                value:
                  optionList?.CITY.options.find(
                    city => city.value === party.person?.cityOfBirth,
                  )?.label ??
                  party.person?.cityOfBirth ??
                  '--',
              },
              {
                label: t('eApp:certificate.form.cityName'),
                value: party.person?.cityNameOfBirth || '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.occupationTitle'),
            icon: <IconWork size={36} />,
            data: [
              {
                label: t('eApp:certificate.form.occupation'),
                value: occupationPicked?.label || '--',
              },
              {
                label: t('eApp:certificate.occupationClass'),
                value: occupationClass,
              },
              {
                label: t('eApp:certificate.occupationDescription'),
                value: occupationDescription,
              },
              {
                label: t('eApp:certificate.form.nameOfBusiness'),
                value: party.person?.occupation?.nameOfEmployer || '--',
              },
              {
                label: t('eApp:certificate.form.natureOfWork'),
                value:
                  optionList?.NATURE_OF_WORK_BUSINESS.options.find(
                    natureOfWork =>
                      natureOfWork.value ===
                      party.person?.occupation?.natureOfBusiness,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.exactDuties'),
                value: party.person?.occupation?.duties || '--',
              },
              {
                label: t('eApp:certificate.form.annualIncome'),
                value:
                  optionList?.INCOME_RANGE.options.find(
                    incomeRange =>
                      incomeRange.value ===
                      party.person?.occupation?.incomeRange,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.annualIncomeAmount'),
                value: party.person?.occupation?.income
                  ? formatCurrency(party.person?.occupation?.income)
                  : '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.contactTitle'),
            icon: <PictogramIcon.Call2 size={36} />,
            data: [
              {
                label: t('eApp:review.email'),
                value: party.contacts.email,
              },
              {
                label: t('eApp:certificate.form.mobilePhone'),
                value: mobileContact?.number
                  ? formatPhoneNumber(mobileContact?.number)
                  : '--',
              },
              {
                label: t('eApp:certificate.form.homeNumber'),
                value: homeContact?.number
                  ? formatPhoneNumber(homeContact?.number)
                  : '--',
              },
              {
                label: t('eApp:certificate.form.officeNumber'),
                value: officeContact?.number
                  ? formatPhoneNumber(officeContact.number)
                  : '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.addressInfoTitle'),
            icon: <PictogramIcon.Home2 size={36} />,
            perRow: true,
            data: [
              {
                label: t('eApp:certificate.title.correspondenceAddress'),
                value: formattedCorrespondenceAddress,
              },
              {
                label: t('eApp:certificate.title.residentialAddress'),
                value: formattedResidentialAddress,
              },
              businessAddress?.businessAddressOpt !== undefined &&
                businessAddress?.businessAddressOpt !== '' && {
                  label: t('eApp:certificate.title.businessAddress'),
                  value: formattedBusinessAddress,
                },
            ].filter(Boolean),
          },
          party.roles.includes(PartyRole.PROPOSER) &&
            isBancaSourceOfFundWealthRequired && {
              title: t('eApp:sourceOfFund.title'),
              icon: <PictogramIcon.MoneyCapital size={36} />,
              perRow: true,
              data: [
                {
                  label: t('eApp:sourceOfFund.fundDetails.placeholder'),
                  value: party.person?.sourceOfFund || '',
                },
                {
                  label: t('eApp:sourceOfFund.wealthDetails.placeholder'),
                  value: party.person?.sourceOfPremium || '',
                },
                {
                  label: t('eApp:sourceOfFund.countryOfSourceOfWealth'),
                  value:
                    optionList?.COUNTRY.options.find(
                      o => o.value === party.person?.sourceOfPremiumCountry,
                    )?.label || '',
                },
              ],
            },
        ].filter(Boolean) as PersonalInformationReviewParams['sections'],
      };
    } else if (party.roles.includes(PartyRole.PAYER)) {
      return {
        title: t('eApp:payor'),
        sections: [
          {
            title: t('eApp:certificate.participantTitle'),
            icon: <PictogramIcon.Lanyard size={36} />,
            data: [
              !isBanca || party.person?.name.title
                ? {
                    label: t('eApp:review.salutationTitle'),
                    value:
                      optionList?.CUBE_TITLE.options.find(
                        title => title.value === party.person?.name.title,
                      )?.label || '--',
                  }
                : undefined,
              {
                label: t('eApp:certificate.form.fullName'),
                value: party?.person?.name?.fullName || '--',
              },
              {
                label: t('eApp:review.gender'),
                value:
                  getLabelFromValue(
                    optionList?.GENDER?.options,
                    party.person?.gender,
                  ) || '--',
              },
              {
                label: t('eApp:certificate.form.dateOfBirth'),
                value: party.person?.dateOfBirth?.date
                  ? dateFormatWithSlashUtil(
                      parsePartyDob(party.person?.dateOfBirth),
                    )
                  : '--',
              },
              {
                label: t('eApp:certificate.form.age'),
                value: String(
                  party.person?.dateOfBirth?.date
                    ? calculateAge(
                        parsePartyDob(party.person?.dateOfBirth),
                      )
                    : '--',
                ),
              },
              {
                label: t('eApp:certificate.form.relationshipWithCertificate'),
                value: capitalFirst(
                  optionList?.RELATIONSHIP.options.find(
                    idType => idType.value === party.relationship,
                  )?.label || '--',
                ),
              },
              {
                label: t('eApp:personalDetails.primaryIdType'),
                value:
                  optionList?.ID_TYPE.options.find(
                    idType => idType.value === defaultId?.idType,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.identificationNumber'),
                value: identificationNumber,
              },
              {
                label: t('eApp:certificate.form.additionalIdType'),
                value:
                  optionList?.ADD_ID_TYPE.options.find(
                    idType => idType.value === additionalId?.idType,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.additional.identification'),
                value: additionalId?.id || '--',
              },
            ].filter(d =>
              Boolean(d),
            ) as PersonalInformationReviewParams['sections'][0]['data'],
          },
          {
            title: t('eApp:certificate.nationalityTitle'),
            icon: <PictogramIcon.Globe2 size={36} />,
            data: [
              {
                label: t('eApp:nationalityDetails.nationality'),
                value:
                  optionList?.NATIONALITY.options.find(
                    nationality =>
                      nationality.value === party.person?.nationality,
                  )?.label || '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.occupationTitle'),
            icon: <IconWork size={36} />,
            data: [
              {
                label: t('eApp:certificate.form.occupation'),
                value: occupationPicked?.label || '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.contactTitle'),
            icon: <PictogramIcon.Call2 size={36} />,
            data: [
              {
                label: t('eApp:certificate.form.mobilePhone'),
                value: mobileContact?.number
                  ? formatPhoneNumber(mobileContact?.number)
                  : '--',
              },
            ],
          },
          {
            title: t('eApp:certificate.addressInfoTitle'),
            icon: <PictogramIcon.Home2 size={36} />,
            perRow: true,
            data: [
              {
                label: t('eApp:certificate.title.correspondenceAddress'),
                value: formattedCorrespondenceAddress,
              },
              {
                label: t('eApp:certificate.title.residentialAddress'),
                value: formattedResidentialAddress,
              },
            ],
          },
        ],
      };
    } else if (party.roles.includes(PartyRole.BENEFICIARY)) {
      return {
        title: t('eApp:beneficiary'),
        sections: [
          {
            title: t('eApp:certificate.participantTitle'),
            icon: <PictogramIcon.Lanyard size={36} />,
            data: [
              {
                label: t('eApp:review.salutationTitle'),
                value:
                  optionList?.CUBE_TITLE.options.find(
                    title => title.value === party.person?.name.title,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.fullName'),
                value: party?.person?.name?.fullName || '--',
              },
              {
                label: t('eApp:review.gender'),
                value:
                  getLabelFromValue(
                    optionList?.GENDER?.options,
                    party.person?.gender,
                  ) || '--',
              },
              {
                label: t('eApp:certificate.form.dateOfBirth'),
                value: party.person?.dateOfBirth?.date
                  ? dateFormatWithSlashUtil(
                      parsePartyDob(party.person?.dateOfBirth),
                    )
                  : '--',
              },
              {
                label: t('eApp:certificate.form.age'),
                value: String(
                  party.person?.dateOfBirth?.date
                    ? calculateAge(
                        parsePartyDob(party.person?.dateOfBirth),
                      )
                    : '--',
                ),
              },
              {
                label: t('eApp:certificate.form.relationshipWithCertificate'),
                value: capitalFirst(
                  optionList?.RELATIONSHIP.options.find(
                    idType => idType.value === party.relationship,
                  )?.label || '--',
                ),
              },
              {
                label: t('eApp:personalDetails.primaryIdType'),
                value:
                  optionList?.ID_TYPE.options.find(
                    idType => idType.value === defaultId?.idType,
                  )?.label || '--',
              },
              {
                label: t('eApp:certificate.form.identificationNumber'),
                value: identificationNumber,
              },
            ],
          },
          {
            title: t('eApp:certificate.addressInfoTitle'),
            icon: <PictogramIcon.Home2 size={36} />,
            perRow: true,
            data: [
              {
                label: t('eApp:certificate.title.correspondenceAddress'),
                value: formattedCorrespondenceAddress,
              },
            ],
          },
        ],
      };
    }
  };

  return { onGetSectionData };
}
