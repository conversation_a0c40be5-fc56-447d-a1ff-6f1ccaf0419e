import { Icon } from 'cube-ui-components';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import {
  ProgressGroup,
  ProgressSubgroup,
  RouteItemKey,
} from '../../common/types/progressBarTypes';
import {
  generateRouteKey,
  updateCompletedStatus,
} from 'features/eAppV2/common/hooks/useGenerateEAppRoutes';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { DecisionsType } from 'features/eAppV2/common/types/uwmeTypes';
import { PartyRole } from 'types/party';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import useBoundStore from 'hooks/useBoundStore';
import { checkFatcaRequired } from '../utils/planUtils';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';

export const useGenerateMYRoutes = () => {
  const { t } = useTranslation(['eApp']);
  const isEntity = useCheckEntity();

  const { caseObj } = useGetActiveCase();
  const quotation = useSelectedQuotation();
  const basicPlan = quotation?.plans?.[0];
  const { decision, hasDecisionResponse } = useMemo(() => {
    const policyOwner = caseObj?.parties?.find(p =>
      p.roles.includes(PartyRole.PROPOSER),
    );
    return {
      decision: policyOwner?.uw?.result?.scenario as DecisionsType,
      hasDecisionResponse: Boolean(policyOwner?.uw?.decisionResponse),
    };
  }, [caseObj?.parties]);
  const { my_hasEmployeeInsured, my_hasSpouseInsured, my_hasChildInsured } =
    useEAppStore(
      state => ({
        my_hasEmployeeInsured: state.my_hasEmployeeInsured,
        my_hasSpouseInsured: state.my_hasSpouseInsured,
        my_hasChildInsured: state.my_hasChildInsured,
      }),
      shallow,
    );
  const { completedMap, setProgressBarState } = useEAppProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
    }),
    shallow,
  );

  const numOfInsured = useMemo(() => {
    return (
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER),
      ).length ?? 0
    );
  }, [caseObj?.parties]);

  useEffect(() => {
    const groups: Array<ProgressGroup> = [
      {
        routeKey: 'appDetail',
        title: t('eApp:bar.appDetail'),
        items: [
          !isEntity && {
            routeKey: 'policyOwner',
            title: t('eApp:bar.policyOwner'),
            icon: Icon.Account,
            items: [
              {
                routeKey: 'policyOwner',
                title: '  •  ' + t('eApp:bar.policyOwner'),
                icon: Icon.Account,
              },
            ],
          },
          isEntity && {
            routeKey: 'company',
            title: t('eApp:bar.company'),
            icon: Icon.Business2,
            items: [
              {
                routeKey: 'company',
                title: '  •  ' + t('eApp:bar.company'),
                icon: Icon.Business2,
              },
            ],
          },
          isEntity && {
            routeKey: 'authorizedSignatory',
            title: t('eApp:bar.authorizedSignatory'),
            icon: Icon.Account,
            items: [
              {
                routeKey: 'authorizedSignatory',
                title: '  •  ' + t('eApp:bar.authorizedSignatory'),
                icon: Icon.Account,
              },
            ],
          },
          my_hasEmployeeInsured || my_hasSpouseInsured || my_hasChildInsured
            ? {
                routeKey: 'insured',
                title: t('eApp:bar.insured'),
                icon: Icon.Couple,
                items: [
                  {
                    routeKey: 'insured',
                    title: '  •  ' + t('eApp:bar.insured'),
                    icon: Icon.Couple,
                  },
                ],
              }
            : null,
          {
            routeKey: 'other',
            title: t('eApp:bar.otherStakeholder'),
            icon: Icon.Team,
            items: [
              {
                routeKey: 'payor',
                title: '  •  ' + t('eApp:payor'),
                icon: Icon.Team,
              },
              {
                routeKey: 'beneficiary',
                title: '  •  ' + t('eApp:beneficiary'),
                icon: Icon.Team,
              },
            ],
          },
          {
            routeKey: 'declaration',
            title: t('eApp:bar.declaration'),
            icon: Icon.DocumentCopy,
            items: [
              {
                routeKey: 'roc',
                title: '  •  ' + t('eApp:declaration.roc'),
                icon: Icon.DocumentCopy,
              },
              checkFatcaRequired(basicPlan?.pid) && {
                routeKey: 'fatca',
                title: '  •  ' + t('eApp:declaration.fatca'),
                icon: Icon.DocumentCopy,
              },
            ].filter(Boolean) as ProgressGroup['items'],
          },
          {
            routeKey: 'healthQuestion',
            title: t('eApp:bar.healthQuestion'),
            icon: Icon.Health,
            items: [
              {
                routeKey: 'policyOwner',
                title: t('eApp:bar.policyOwner'),
                icon: Icon.Health,
              },
              ...Array.from({ length: numOfInsured }, (_, i) => ({
                routeKey: `insured${i + 1}`,
                title: t('eApp:bar.insured'),
                icon: Icon.Health,
              })),
            ],
          },
        ].filter(Boolean) as ProgressGroup['items'],
      },
      {
        routeKey: 'consents',
        title: t('eApp:bar.consents'),
        items: [],
      },
      {
        routeKey: 'directCredit',
        title: t('eApp:bar.directCredit'),
        items: [],
      },
      {
        routeKey: 'documentUpload',
        title: t('eApp:bar.uploadDocument'),
        items: [],
      },
      {
        routeKey: 'review',
        title: t('eApp:bar.reviewSignature'),
        items: [],
      },
      {
        routeKey: 'payment',
        title: t('eApp:bar.payment'),
        items: [],
      },
    ];

    updateCompletedStatus(groups, completedMap);

    //Update disabled
    const step1 = groups[0];
    const step1_1 = groups[0].items[0];
    const step1_2 = groups[0].items[1];
    const step1_3 = groups[0].items[2];
    const step1_4 = groups[0].items[3];
    const step1_5 = groups[0].items[4];
    const step1_6 = groups[0].items[5];
    step1_2.disabled = !step1_1.completed;
    step1_3.disabled = !step1_2.completed;
    step1_4.disabled = !step1_3.completed;
    if (step1_5) {
      step1_5.disabled = !step1_4.completed;
    }
    if (step1_6) {
      step1_6.disabled = !step1_5.completed;
    }
    step1.completed =
      step1_1.completed &&
      step1_2.completed &&
      step1_3.completed &&
      step1_4.completed;
    if (step1_5) {
      step1.completed &&= step1_5.completed;
    }
    if (step1_6) {
      step1.completed &&= step1_6.completed;
    }
    // if (my_hasEmployeeInsured || my_hasSpouseInsured || my_hasChildInsured) {
    //   const step1_5 = groups[0].items[4];
    //   step1_5.disabled = !step1_4.completed;
    //   step1.completed = step1.completed && step1_5.completed;
    // }

    const step2 = groups[1];
    const step3 = groups[2];
    const step4 = groups[3];
    const step5 = groups[4];
    const step6 = groups[5];

    step1.disabled = step5.completed;
    step2.disabled = step5.completed;
    step3.disabled = step5.completed;
    step4.disabled = step5.completed;

    step5.disabled = !(
      step1.completed &&
      step2.completed &&
      step3.completed &&
      step4.completed
    );
    step6.disabled = !step5.completed;

    if (decision === 'Decline') {
      step1.disabled = true;
      step2.disabled = true;
      step3.disabled = true;
      step4.disabled = true;
      step6.disabled = true;
    }

    const lastStep1 = step1.items[step1.items.length - 1];
    if (hasDecisionResponse) {
      lastStep1.disabled = true;
    }

    setProgressBarState({
      groups,
    });
  }, [
    basicPlan?.pid,
    completedMap,
    decision,
    hasDecisionResponse,
    isEntity,
    my_hasChildInsured,
    my_hasEmployeeInsured,
    my_hasSpouseInsured,
    numOfInsured,
    setProgressBarState,
    t,
  ]);
};
