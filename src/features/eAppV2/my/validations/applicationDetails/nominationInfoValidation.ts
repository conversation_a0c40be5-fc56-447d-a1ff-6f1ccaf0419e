import {
  requiredMessage,
  nominationNotApplicable,
  minimumAllocation,
  nominationDuplicated,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { InferType, array, object, string } from 'yup';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { NOMINATION_TYPE } from 'features/eAppV2/my/constants/nominationType';
import { nullableDate } from 'features/eAppV2/common/validations/eAppCommonSchema';
import { MY_COUNTRY, OWNER_ADDRESS_OPTION } from 'constants/optionList';
import { mysAddressSchema, mysIdNumber, mysName } from '../commonSchema';
export const maxNameLength = 60;

export type NominationFormSchemaType = InferType<
  typeof nominationFormValidationSchema
>;
export type NominationFormSchemaKey = keyof NominationFormSchemaType;

type Nominee = NominationFormSchemaType['information'][0];

export const { defaultDate: dateOfBirthDefaultDate } =
  getDateOfBirthDropdownProps();

export const initialNominationFormData: NominationFormSchemaType = {
  nominationType: NOMINATION_TYPE.HIBAH,
  information: [
    {
      id: '',
      title: '',
      beneficiaryType: NOMINATION_TYPE.HIBAH,
      dob: null,
      fullName: '',
      gender: '',
      identificationNumber: '',
      primaryIdType: '',
      relationship: '',
      correspondenceAddress: OWNER_ADDRESS_OPTION,
      correspondenceAddressLine1: '',
      correspondenceAddressLine2: '',
      correspondenceAddressLine3: '',
      correspondencePostCode: '',
      correspondenceCity: '',
      correspondenceState: '',
      correspondenceCountry: MY_COUNTRY,
      allocation: '100',
    },
  ],
  poIdentificationNumber: '',
  piIdentificationNumbers: [],
  poFullName: '',
  piFullNames: [],
  agentFullName: '',
};

export const nominationFormRequiredSchema = {
  nominationType: string()
    .oneOf(['Conditional Hibah', 'Wasi'])
    .required(requiredMessage),
  information: array()
    .of(
      object({
        title: string().required(requiredMessage),
        fullName: mysName()
          .required(requiredMessage)
          .test(
            'duplicatedNomineeName',
            nominationDuplicated,
            function (value, ctx) {
              const ctxFrom = ctx.from;
              const count =
                ctxFrom?.[1].value.information.filter(
                  (e: Nominee) =>
                    e?.fullName?.trim().toLowerCase() ===
                    value?.trim().toLowerCase(),
                )?.length ?? 0;
              if (value && count >= 2) return false;
              return true;
            },
          )
          .test('fullName', nominationNotApplicable, function (value, ctx) {
            const ctxFrom = ctx.from;
            value = value?.trim().toLowerCase();
            const poFullName = ctxFrom?.[1]?.value?.poFullName
              ?.trim()
              .toLowerCase();
            const piFullNames = ctxFrom?.[1].value.piFullNames?.map(
              (name: string) => name?.trim().toLowerCase(),
            );
            const agentFullName = ctxFrom?.[1]?.value?.agentFullName
              ?.trim()
              .toLowerCase();
            if (
              value &&
              (value === poFullName ||
                piFullNames.includes(value) ||
                value === agentFullName)
            ) {
              return false;
            }
            return true;
          }),
        primaryIdType: string().required(requiredMessage),
        id: string(),
        identificationNumber: mysIdNumber('primaryIdType', 'dob', 'gender')
          .required(requiredMessage)
          .test(
            'identificationNumber',
            nominationNotApplicable,
            function (value, ctx) {
              const ctxFrom = ctx.from;
              value = value?.trim().toLowerCase();
              const poIdentificationNumber =
                ctxFrom?.[1]?.value?.poIdentificationNumber
                  ?.trim()
                  .toLowerCase();
              const piIdentificationNumbers =
                ctxFrom?.[1].value.piIdentificationNumbers?.map((id: string) =>
                  id?.trim().toLowerCase(),
                );
              if (
                value &&
                (value === poIdentificationNumber ||
                  piIdentificationNumbers.includes(value))
              ) {
                return false;
              }
              return true;
            },
          )
          .test(
            'duplicatedNomineeIdNumber',
            nominationDuplicated,
            function (value, ctx) {
              const ctxFrom = ctx.from;
              const count =
                ctxFrom?.[1].value.information.filter(
                  (e: Nominee) =>
                    e?.identificationNumber?.trim().toLowerCase() ===
                    value?.trim().toLowerCase(),
                )?.length ?? 0;
              if (value && count >= 2) return false;
              return true;
            },
          ),
        beneficiaryType: string().oneOf(['Conditional Hibah', 'Wasi']),
        gender: string().required(requiredMessage),
        dob: nullableDate(),
        relationship: string().required(requiredMessage),
        ...mysAddressSchema(['correspondence']),
        allocation: string()
          .required(requiredMessage)
          .test('min-allocation', minimumAllocation, function (value) {
            return Number(value) >= 10;
          }),
      }),
    )
    .required(requiredMessage)
    .test({
      name: 'total-allocation',
      test: value => {
        return (
          (value ?? []).every(
            i =>
              i.allocation !== '' && !isNaN(Number(i.allocation ?? undefined)),
          ) &&
          (value ?? []).reduce(
            (total, i) => total + Number(i.allocation || 0),
            0,
          ) === 100
        );
      },
    }),
  poIdentificationNumber: string(),
  piIdentificationNumbers: array().of(string()),
  poFullName: string(),
  piFullNames: array().of(string()),
  agentFullName: string(),
};

export type NominationRequiredSchemaKey =
  keyof typeof nominationFormRequiredSchema;

export const requiredPersonalDetailFields = Object.keys(
  nominationFormRequiredSchema,
) as NominationRequiredSchemaKey[];

export const nominationFormValidationSchema = object({
  ...nominationFormRequiredSchema,
});
