import * as yup from 'yup';
import { invalidFormatMessage, maxLength20Message, minLength5Message, requiredMessage } from '../../common/constants/eAppErrorMessages';

const numericRegex = /^[0-9]*$/;

export const bankDirectSchema = yup.object({
  paymentMethod: yup.string().required(requiredMessage),
  bankName: yup.string().required(requiredMessage),
  accountHolderName: yup.string().required(requiredMessage),
  accountNumber: yup
    .string()
    .min(5, minLength5Message)
    .max(20, maxLength20Message)
    .matches(numericRegex, {
      excludeEmptyString: true,
      message: invalidFormatMessage,
    })
    .required(requiredMessage),
});
export type BankDirectForm = yup.InferType<typeof bankDirectSchema>;

export const bankDirectDefaultValue: BankDirectForm = {
  paymentMethod: 'D',
  bankName: '',
  accountHolderName: '',
  accountNumber: '',
};