import { useTheme } from '@emotion/react';
import { Box, Button, H6, LargeLabel } from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import IconPaymentRequest from '../icons/IconPaymentRequest';
import PaymentFailedModal from '../modals/paymentFailed/PaymentFailedModal.tablet';
import { usePaymentProcessingLogic } from '../../../hooks/usePaymentProcessingLogic';

const PaymentProcessingTablet = memo(function PaymentProcessing() {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const {
    retryPaymentGetStatus,
    visiblePaymentFailed,
    setVisiblePaymentFailed,
    navigation,
  } = usePaymentProcessingLogic();

  return (
    <Box
      flex={1}
      padding={space[4]}
      backgroundColor={colors.background}
      alignItems="center"
      justifyContent="center">
      <IconPaymentRequest />
      <H6
        style={{
          marginTop: space[4],
        }}
        fontWeight="bold">
        {t('eApp:payment.paymentProcessing')}
      </H6>
      <LargeLabel
        style={{
          marginTop: space[3],
          textAlign: 'center',
        }}>
        {t('eApp:payment.paymentProcessing1')}
      </LargeLabel>
      <Button
        style={{
          marginTop: space[6],
          width: 200,
        }}
        text={t('eApp:payment.tryAgain')}
        onPress={retryPaymentGetStatus}
      />
      <PaymentFailedModal
        visible={visiblePaymentFailed}
        onDismiss={() => {
          setVisiblePaymentFailed(false);
          navigation.goBack();
        }}
      />
    </Box>
  );
});

export default PaymentProcessingTablet;
