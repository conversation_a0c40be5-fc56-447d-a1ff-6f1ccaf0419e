import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { sbcProductList } from 'constants/eAppPayment';
import {
  Body,
  Box,
  Center,
  H4,
  H7,
  Icon,
  LargeBody,
  LargeBody2,
  LargeLabel,
  Row,
  Typography,
} from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import { getFullName } from 'features/eAppV2/common/utils/getFullName';
import SubmissionCoverageText from 'features/eAppV2/my/components/submission/SubmissionCoverageText';
import { TFuncKey } from 'i18next';
import React, { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, ScrollView, View } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { CHANNELS } from 'types/channel';
import { PartyType } from 'types/party';
import { formatCurrency, formatCurrencyWithMask, i18n } from 'utils';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { usePaymentSuccessfulResultLogic } from '../../../hooks/usePaymentSuccessfulResultLogic';
import CheckBoxList from 'components/CheckBoxList';
import { Plan } from 'types/quotation';
import FWDInsuranceLogoSVG from 'features/eAppV2/common/components/icons/FWDInsuranceLogo';
import { enhancedRemoteSellingEnabled } from 'utils/context';

interface RowInfoFieldProps {
  isHighlight?: boolean;
  isMini?: boolean;
  isVertical?: boolean;
  fieldName?: string | null;
  fieldValue?: string | number | null;
}

const RowInfoField = ({
  fieldName,
  fieldValue,
  isHighlight,
  isMini,
  isVertical,
}: RowInfoFieldProps) => {
  const { colors, space } = useTheme();
  return (
    <Box flex={isVertical ? 0 : 1}>
      <Body color={colors.placeholder} fontWeight="bold">
        {fieldName}
      </Body>
      <Box height={space[isMini ? 1 : 2]} />
      {isMini ? (
        <LargeBody color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeBody>
      ) : (
        <LargeLabel color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeLabel>
      )}
    </Box>
  );
};

const PaymentSuccessfulResultTablet = memo(function PaymentSuccessfulResult() {
  const { t } = useTranslation(['eApp', 'common']);
  const { sizes, colors, space, borderRadius } = useTheme();
  const { top: topInset } = useSafeAreaInsets();

  const {
    sbcProducts,
    setSbcProducts,
    cubeChannel,
    isLoading,
    isBackToHome,
    isOffline,
    isPending,
    isOfflineButPendingRS,
    isOnlineButPendingRS,
    remoteSellingStatus,
    pendingRemoteSellingTitle,
    policyOwnerPersonalInfo,
    insuredPersonalInfo,
    quotation,
    basePlan,
    riders,
    applicationNum,
    goToACR,
    goToHome,
    isPIEqualPO,
    statusElements,
    isInRemoteSellingFlow,
    isPendingByRemoteSelling,
    isPrimaryLoading,
    isSecondaryLoading,
    handleResendEmail,
    handleRefreshPage,
  } = usePaymentSuccessfulResultLogic();

  const riderPairs = useMemo(() => {
    return riders.reduce<Array<Plan[]>>(function (result, value, index, array) {
      if (index % 2 === 0) result.push(array.slice(index, index + 2));
      return result;
    }, []);
  }, [riders]);

  const poName = useMemo(
    () =>
      policyOwnerPersonalInfo.personalDetails.customerType === PartyType.ENTITY
        ? policyOwnerPersonalInfo.entityDetails.entityName
        : getFullName(policyOwnerPersonalInfo.personalDetails),
    [
      policyOwnerPersonalInfo.entityDetails.entityName,
      policyOwnerPersonalInfo.personalDetails,
    ],
  );
  console.log('=>(PaymentSuccessfulResult.tablet.tsx:122) poName', poName);

  return (
    <Box flex={1} bgColor={colors.surface}>
      <Pressable onPress={goToHome}>
        <Row
          pt={topInset + space[4]}
          px={space[6]}
          alignSelf={'flex-end'}
          alignItems={'center'}
          columnGap={space[1]}>
          <Icon.Home size={sizes[6]} fill={colors.onBackground} />
          <Typography.LargeLabel fontWeight="bold">Home</Typography.LargeLabel>
        </Row>
      </Pressable>
      <Wrapper edges={['top']}>
        <Row alignItems="center" gap={space[3]}>
          <Center>
            <Box
              pos="absolute"
              width={sizes[16]}
              height={sizes[16]}
              bgColor={
                isOfflineButPendingRS || isOnlineButPendingRS
                  ? undefined
                  : colors.background
              }
              borderRadius={borderRadius.full}
            />
            {statusElements.icon}
          </Center>
          <H4 fontWeight="bold">{statusElements.title}</H4>
        </Row>
        <Box height={space[3]} />
        {isOffline && (
          <H7 fontWeight="normal">{t('eApp:payment.resultDes')}</H7>
        )}
        {isPending && (
          <H7 fontWeight="normal">{t('eApp:payment.resultDesPending')}</H7>
        )}
        {isInRemoteSellingFlow && (
          <H7>
            {remoteSellingStatus
              ? t('eApp:payment.resultRemoteSellingComplete.desc')
              : t('eApp:payment.resultRemoteSellingPending')}
          </H7>
        )}
        <Box height={space[6]} />
        <Content height={space[6]}>
          <Row flexGrow={1}>
            <Box padding={space[6]} width={210}>
              <ScrollView showsVerticalScrollIndicator={false}>
                <Box gap={space[6]}>
                  <RowInfoField
                    fieldName={t('eApp:review.applicationNumber')}
                    fieldValue={applicationNum}
                    isHighlight={true}
                    isVertical={true}
                  />
                  <RowInfoField
                    fieldName={t('eApp:review.policyOwner')}
                    fieldValue={poName}
                    isVertical={true}
                  />
                  <RowInfoField
                    fieldName={t('eApp:review.insured')}
                    fieldValue={
                      isPIEqualPO
                        ? poName
                        : getFullName(insuredPersonalInfo.personalDetails)
                    }
                    isVertical={true}
                  />
                  <RowInfoField
                    fieldName={t('eApp:review.email')}
                    fieldValue={policyOwnerPersonalInfo.contactDetails.email}
                    isVertical={true}
                  />
                  <RowInfoField
                    fieldName={t('eApp:payment.dateSubmitted')}
                    fieldValue={dateFormatUtil(new Date())}
                    isVertical={true}
                  />
                </Box>
              </ScrollView>
            </Box>
            <ApplicationContent>
              <Row mb={space[6]} alignItems="center">
                <Box flex={1}>
                  <H4 fontWeight="bold">
                    {getProductName(basePlan?.productName)}
                  </H4>
                </Box>
                <FWDInsuranceLogoSVG width={86} height={44} />
              </Row>
              <ScrollView showsVerticalScrollIndicator={false}>
                <Row gap={space[4]}>
                  <SubmissionCoverageText
                    label={t('eApp:payment.sumAssured')}
                    currency={t('common:php')}
                    value={formatCurrency(basePlan?.sumAssured || 0)}
                    isNormal={true}
                  />

                  <SubmissionCoverageText
                    label={
                      t(
                        `eApp:paymentModeName.${
                          (basePlan?.paymentMode
                            ? basePlan?.paymentMode
                            : quotation?.basicInfo.paymentMode) as
                            | 'A'
                            | 'M'
                            | 'L'
                            | 'Q'
                            | 'S'
                        }`,
                      ) + ' premium'
                    }
                    currency={t('common:php')}
                    value={formatCurrency(quotation?.summary.totalPrem || 0)}
                    isNormal={true}
                  />
                </Row>
                <Divider />

                <Row gap={space[4]}>
                  <RowInfoField
                    fieldName={t('eApp:payment.policyTerm')}
                    fieldValue={t('common:withYears', {
                      year: basePlan?.policyTerm ?? 0,
                    })}
                  />
                  <RowInfoField
                    fieldName={t('eApp:payment.planType')}
                    fieldValue={t('common:withYears', {
                      year: basePlan?.premiumTerm ?? 0,
                    })}
                  />
                  <RowInfoField
                    fieldName={t('eApp:payment.paymentFrequency')}
                    fieldValue={t(
                      `eApp:paymentMode.${
                        (basePlan?.paymentMode
                          ? basePlan?.paymentMode
                          : quotation?.basicInfo.paymentMode) as
                          | 'A'
                          | 'S'
                          | 'Q'
                          | 'M'
                          | 'L'
                      }`,
                    )}
                  />
                </Row>
                {riderPairs.length > 0 && <Divider />}
                {riderPairs.length > 0 && (
                  <Box>
                    <Row flex={1} justifyContent="space-between">
                      <Box flex={1}>
                        <Body color={colors.placeholder} fontWeight="bold">
                          {t('eApp:payment.coverage')}
                        </Body>
                      </Box>
                      <Row
                        gap={space[6]}
                        flex={1}
                        alignContent="flex-start"
                        justifyContent="space-between"
                        alignItems="flex-start">
                        <Box flex={1} alignContent="flex-start">
                          <Body color={colors.placeholder} fontWeight="bold">
                            {t('eApp:payment.sumAssured')}
                          </Body>
                        </Box>
                      </Row>
                    </Row>
                    <Box marginTop={space[2]} gap={space[2]}>
                      {riders.map(rider => {
                        return (
                          <Row
                            flex={1}
                            justifyContent="space-between"
                            key={`${rider.pid}`}>
                            <Box flex={1}>
                              <Body fontWeight="normal">
                                {getProductName(
                                  rider.productName,
                                  i18n.language,
                                )}
                              </Body>
                            </Box>
                            <Row
                              flex={1}
                              gap={space[6]}
                              alignContent="flex-start"
                              justifyContent="space-between"
                              alignItems="flex-start">
                              <Box flex={1} alignContent="flex-start">
                                <Body fontWeight="normal">
                                  {typeof rider.sumAssured === 'number'
                                    ? t('common:withCurrency', {
                                        amount: formatCurrencyWithMask(
                                          rider.sumAssured,
                                          2,
                                        ),
                                      })
                                    : '--'}
                                </Body>
                              </Box>
                            </Row>
                          </Row>
                        );
                      })}
                    </Box>
                  </Box>
                )}
                {cubeChannel === CHANNELS.BANCA && (
                  <Box flex={1} my={space[5]}>
                    <Typography.H8 color={colors.primary} fontWeight="bold">
                      {t('eApp:payment.sbcProduct')}
                    </Typography.H8>
                    <CheckBoxList<{ label: string; value: string }, string>
                      style={{
                        flex: 1,
                      }}
                      label={t('eApp:payment.sbcProduct')}
                      modalTitle={t('eApp:payment.sbcProduct')}
                      data={sbcProductList}
                      value={sbcProducts}
                      getItemLabel={item => t(item.label as TFuncKey)}
                      getItemValue={item => item.value}
                      onChange={(item: Array<string>) => {
                        setSbcProducts(item);
                      }}
                    />
                  </Box>
                )}
              </ScrollView>
            </ApplicationContent>
          </Row>
        </Content>
      </Wrapper>
      <EAppFooterTablet
        leftComponent={
          isPendingByRemoteSelling ? (
            <Box
              flexDirection={'row'}
              alignItems={'center'}
              px={space[6]}
              py={space[4]}>
              <Box flexDirection={'row'} columnGap={space[2]}>
                <Icon.Warning size={sizes[6]} fill={colors.primary} />
                <Box flex={1}>
                  <LargeBody2 color={colors.primary} fontWeight={'bold'}>
                    {pendingRemoteSellingTitle}
                  </LargeBody2>
                  <LargeBody>
                    {t('eApp:payment.resultRemoteSellingPending.warningBlock')}
                  </LargeBody>
                </Box>
              </Box>
            </Box>
          ) : undefined
        }
        primaryLabel={t('eApp:payment.gotIt')}
        secondaryLabel={
          enhancedRemoteSellingEnabled
            ? isPendingByRemoteSelling
              ? t('eApp:payment.refreshPage')
              : undefined
            : t('eApp:payment.backToHome')
        }
        primaryLoading={!isBackToHome && isLoading}
        secondaryDisabled={!isBackToHome && isLoading}
        primaryDisabled={
          (isBackToHome && isLoading) || isPendingByRemoteSelling
        }
        secondaryLoading={isBackToHome && isLoading}
        onSecondaryPress={
          isPendingByRemoteSelling ? handleRefreshPage : goToHome
        }
        onPrimaryPress={goToACR}
      />
    </Box>
  );
});

export default PaymentSuccessfulResultTablet;

const Wrapper = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  alignContent: 'center',
  justifyContent: 'center',
  alignItems: 'center',
  marginTop: space[12],
  marginBottom: space[10],
  marginHorizontal: space[30],
}));

const Content = styled(Box)(({ theme: { colors, borderRadius } }) => ({
  backgroundColor: colors.palette.fwdGrey[20],
  borderRadius: borderRadius['large'],
  flexGrow: 1,
  flexDirection: 'row',
}));

const ApplicationContent = styled(View)(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.background,
    borderTopRightRadius: borderRadius['large'],
    borderBottomRightRadius: borderRadius['large'],
    flex: 1,
    padding: space[6],
  }),
);

export const Divider = styled(View)(({ theme: { space, colors } }) => {
  return {
    height: 1,
    backgroundColor: colors.palette.fwdGrey[100],
    marginVertical: space[4],
  };
});
