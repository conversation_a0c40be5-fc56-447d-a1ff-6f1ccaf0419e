import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  H6,
  H7,
  H8,
  Icon,
  PictogramIcon,
  RadioButton,
  RadioButtonGroup,
  Row,
  SmallBody,
} from 'cube-ui-components';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import {
  PaymentForm,
  paymentSchema,
} from 'features/eAppV2/ph/validations/paymentValidation';
import { Fragment, memo, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Image, TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import EAppFooterPhone from '../../../common/components/footer/EAppFooter.phone';
import { usePaymentLogic } from '../../hooks/usePaymentLogic';
import IconVisa from './icons/IconVisa';
import PaymentFailedModal from './modals/paymentFailed/PaymentFailedModal.phone';
import { PaymentStatus } from '../../types/paymentTypes';
import { formatCurrency } from 'utils';
import styled from '@emotion/native';

const PaymentPhone = memo(function Payment() {
  const payment = useEAppStore(state => state.initialPayment);
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const [showPlan, setShowPlan] = useState(false);

  const resolver = useEAppValidationResolver(paymentSchema);
  const form = useForm<PaymentForm>({
    mode: 'onBlur',
    defaultValues: payment,
    resolver,
  });
  const { watch, control } = form;
  const {
    paymentMethods,
    isRequestingOnlinePayment,
    visiblePaymentFailed,
    setVisiblePaymentFailed,
    quotation,
    next,
    paymentDisabled,
    paymentPlan,
    isNarrowScreen,
    paymentSubmit,
    initialPremiumData,
  } = usePaymentLogic(form);

  const scrollViewRef = useRef<KeyboardAwareScrollView>(null);

  useEffect(() => {
    switch (payment.paymentStatus) {
      case PaymentStatus.RECEIVE_PENDING:
      case PaymentStatus.RECEIVE_UNKNOWN:
        next();
        return;
      case PaymentStatus.RECEIVE_FAILED:
        setVisiblePaymentFailed(true);
        return;
    }
  }, [next, payment, setVisiblePaymentFailed]);

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        keyboardDismissMode="on-drag">
        <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">{t('eApp:payment.title')}</H6>
          <Box
            bgColor={colors.background}
            paddingX={16}
            borderRadius={12}
            mt={20}>
            <TouchableOpacity
              style={{ paddingVertical: space[4] }}
              onPress={() => setShowPlan(!showPlan)}>
              <Row alignItems={'center'} justifyContent="space-between">
                <H7 fontWeight="bold">{t('eApp:payment.yourPlan')}</H7>
                {showPlan ? <Icon.ChevronUp /> : <Icon.ChevronDown />}
              </Row>
              <Row
                alignItems={'flex-start'}
                justifyContent="space-between"
                mt={10}
                columnGap={space[4]}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.product')}
                </H8>
                <ProductNameText fontWeight={'bold'}>
                  {getProductName(quotation?.plans?.[0].productName)}
                </ProductNameText>
              </Row>
              <Box
                backgroundColor={colors.palette.fwdOrange[5]}
                padding={space[2]}
                mt={space[2]}>
                <Row
                  alignItems={'center'}
                  justifyContent="space-between"
                  columnGap={space[4]}>
                  <Box flex={1}>
                    <H8 fontWeight="bold">
                      {quotation?.plans[0]?.paymentMode === 'M' ||
                      quotation?.basicInfo?.paymentMode === 'M'
                        ? t('eApp:payment.twoMonthlyPremiumDue')
                        : t('eApp:payment.initialPaymentTotal')}
                    </H8>
                  </Box>
                  <Row>
                    <SmallBody
                      style={{
                        marginRight: space[1],
                      }}
                      color={colors.palette.fwdOrange[100]}>
                      {initialPremiumData?.currency}
                    </SmallBody>
                    <H6 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                      {formatCurrency(initialPremiumData?.initialPremium, 2)}
                    </H6>
                  </Row>
                </Row>
              </Box>
            </TouchableOpacity>
            {showPlan ? (
              <Box mt={space[2]}>
                {paymentPlan.map((item, index) => (
                  <Box key={item.title}>
                    <H7 fontWeight="bold">{item.title}</H7>
                    {item.listRowDetails.map(itemRow => (
                      <Row mt={space[2]} key={itemRow.title}>
                        <H8
                          color={colors.palette.fwdGreyDarkest}
                          style={{ flex: 1 }}>
                          {itemRow.title}
                        </H8>
                        <H8 style={{ flex: 1, marginLeft: space[4] }}>
                          {itemRow.value}
                        </H8>
                      </Row>
                    ))}
                    {index === paymentPlan.length - 1 ? (
                      <Box marginY={space[2]} />
                    ) : (
                      <Box
                        marginY={space[3]}
                        height={1}
                        backgroundColor={colors.palette.fwdGrey[100]}
                        style={{
                          width: '100%',
                        }}
                      />
                    )}
                  </Box>
                ))}
              </Box>
            ) : (
              <Fragment />
            )}
          </Box>
          <Input control={control} as={RadioButtonGroup} name="paymentMethod">
            <Box
              bgColor={colors.background}
              padding={16}
              borderRadius={12}
              mt={20}>
              <Row alignItems="center">
                <PictogramIcon.CreditCard size={40} />
                <H7 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                  {t('eApp:payment.paymentMethod')}
                </H7>
              </Row>
              {paymentMethods.map((item, index, arr) => {
                return (
                  <Box key={item.value}>
                    <Row alignItems="center" mt={20}>
                      <RadioButton
                        value={item.value}
                        label={t(item.label)}
                        style={{ flex: 1 }}
                        labelStyle={{ flex: 1 }}
                        key={index}
                      />
                      {index === 0 && arr.length > 1 ? (
                        <Row>
                          <Image
                            source={require('../../../common/assets/mastercard.png')}
                            style={{
                              marginLeft: 20,
                              width: 30,
                              height: 20,
                            }}
                            resizeMode="contain"
                          />
                          <IconVisa />
                        </Row>
                      ) : null}
                    </Row>
                    {arr.length - 1 === index ? null : (
                      <Box
                        marginTop={20}
                        height={1}
                        backgroundColor={colors.palette.fwdGrey[100]}
                      />
                    )}
                  </Box>
                );
              })}
            </Box>
          </Input>
        </Box>
      </KeyboardAwareScrollView>
      <PaymentFailedModal
        visible={visiblePaymentFailed}
        onDismiss={() => {
          setVisiblePaymentFailed(false);
        }}
      />
      <EAppFooterPhone
        primaryLabel={t('eApp:payment.proceedToPayment')}
        onPrimaryPress={paymentSubmit}
        primaryLoading={isRequestingOnlinePayment}
        primaryDisabled={watch('paymentMethod') === '' || paymentDisabled}
      />
    </Box>
  );
});

export default PaymentPhone;

const ProductNameText = styled(H8)(() => ({
  flex: 1,
  textAlign: 'right',
}));
