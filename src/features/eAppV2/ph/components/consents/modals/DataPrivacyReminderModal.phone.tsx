import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { Box, Column, H7 } from 'cube-ui-components';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import useDataPrivacyReminderModal from 'features/eAppV2/ph/hooks/useDataPrivacyReminderModal';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { SharedValue } from 'react-native-reanimated';

interface Props {
  handleClose?: () => void;
}

export default function DataPrivacyReminderModalPhone({ handleClose }: Props) {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const [seeMorePrivacy, setSeeMorePrivacy] = useState(false);

  const bottomSheetProps = useBottomSheet();
  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const { isGettingCase, isSavingApplication, isNarrowScreen, onSubmit } =
    useDataPrivacyReminderModal({ handleClose });

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter {...props}>
          <EAppFooterPhone
            primaryDisabled={isGettingCase}
            primaryLoading={isSavingApplication}
            onPrimaryPress={onSubmit}
            primaryLabel={t('eApp:consents.dataPrivacy.btn.confirm')}
          />
        </BottomSheetFooter>
      );
    },
    [isGettingCase, isSavingApplication, onSubmit, t],
  );

  return (
    <BottomSheetModal
      {...bottomSheetProps}
      onDismiss={handleClose}
      snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
      handleHeight={animatedHandleHeight}
      contentHeight={animatedContentHeight}
      footerComponent={renderFooter}
      style={{ padding: 0 }}>
      <BottomSheetView
        enableFooterMarginAdjustment
        style={{ paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4] }}
        onLayout={e => {
          handleContentLayout(e);
        }}>
        <Column marginBottom={theme.space[8]}>
          <TextInfo numberOfLines={seeMorePrivacy ? undefined : 4}>
            {t('eApp:consents.privacyPolicy.content1')}
            <TextHyperlink
              suppressHighlighting
              onPress={() => {
                Linking.openURL('https://www.fwd.com.ph/privacypolicy/');
              }}
              fontWeight="bold"
              color={theme.colors.primary}>
              https://www.fwd.com.ph/privacypolicy/
            </TextHyperlink>
            . {t('eApp:consents.privacyPolicy.content2')}
            <TextHyperlink
              suppressHighlighting
              onPress={() => {
                Linking.openURL('mailto:<EMAIL>');
              }}
              fontWeight="bold"
              color={theme.colors.primary}>
              <EMAIL>
            </TextHyperlink>{' '}
            {t('eApp:consents.privacyPolicy.content3')}
            <ButtonClose
              onPress={() => setSeeMorePrivacy(false)}
              activeOpacity={1}>
              <TextAction fontWeight="bold">
                {' '}
                {t('eApp:consents.privacyPolicy.close')}
              </TextAction>
            </ButtonClose>
          </TextInfo>
          {!seeMorePrivacy && (
            <ButtonMore
              activeOpacity={1}
              onPress={() => setSeeMorePrivacy(true)}>
              <TextAction fontWeight="bold">
                {t('eApp:consents.privacyPolicy.more')}
              </TextAction>
            </ButtonMore>
          )}
        </Column>
        <H7 color={theme.colors.secondary}>
          {t('eApp:consents.privacyPolicy.note')}
        </H7>
        <Box h={theme.space[4]} />
      </BottomSheetView>
    </BottomSheetModal>
  );
}

const TextInfo = styled(H7)(({ theme: { colors } }) => ({
  color: colors.secondary,
}));

const TextAction = styled(H7)(({ theme: { colors } }) => ({
  color: colors.primary,
}));

const TextHyperlink = styled(H7)(({ theme: { colors } }) => ({
  color: colors.primary,
  textDecorationLine: 'underline',
}));

const ButtonClose = styled.TouchableOpacity(() => ({
  justifyContent: 'flex-end',
  alignSelf: 'flex-end',
  height: 16,
}));

const ButtonMore = styled.TouchableOpacity(({ theme: { space, colors } }) => ({
  position: 'absolute',
  right: 0,
  bottom: 0,
  backgroundColor: colors.background,
  paddingLeft: space[2],
}));

const Separator = styled.View(({ theme: { space, colors } }) => ({
  marginTop: space[4],
  width: '100%',
  height: 1,
  backgroundColor: colors.palette.fwdGrey[100],
}));
