import styled from '@emotion/native';
import { Body, H7, Icon, LargeBody, Row } from 'cube-ui-components';
import { memo, useCallback } from 'react';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { SectionItemProps } from './SectionItem';

const Container = styled.TouchableOpacity(({ theme }) => ({
  paddingVertical: theme.space[4],
}));
const Border = styled.View(() => ({
  borderTopColor: '#D9D9D9',
  borderTopWidth: 1,
}));

const ArrowContainer = styled.View(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  flex: 1,
}));

const TextContainer = styled.View(({ theme }) => ({
  flex: 1,
  paddingLeft: theme.space[2],
  paddingRight: theme.space[3],
}));

const Title = styled(H7)(({ theme }) => ({
  color: theme.colors.onBackground,
}));

const Text = styled(LargeBody)(({ theme }) => ({
  color: theme.colors.onBackground,
  marginRight: theme.space[3],
}));

const ViewText = styled(H7)(({ theme }) => ({
  color: theme.colors.onBackground,
}));

const ActionPendingContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginTop: theme.space[1],
  marginLeft: theme.space[7],
}));

const ActionPendingText = styled(Body)(({ theme }) => ({
  color: theme.colors.palette.alertRed,
  marginLeft: theme.space[1],
  lineHeight: theme.space[9] / 2,
}));

const SectionItemTablet = <T,>({
  icon,
  title,
  text,
  metaData,
  disabled,
  onPress,
  index,
  view,
}: SectionItemProps<T>) => {
  const { t } = useTranslation(['eApp']);
  const { sizes, colors } = useTheme();
  const onPressFn = useCallback(() => {
    onPress(metaData);
  }, [onPress, metaData]);

  const eAppActionPending =
    metaData && Object.hasOwn(metaData, 'actionPending')
      ? (metaData.actionPending as boolean)
      : false;

  return (
    <>
      {index > 0 && <Border />}
      <Container disabled={disabled} onPress={onPressFn}>
        <Row alignItems="center">
          {icon}
          <ArrowContainer>
            <TextContainer>
              <Title fontWeight="bold">{title}</Title>
            </TextContainer>
            {!!text && <Text>{text}</Text>}
            {view && (
              <ViewText fontWeight="bold">{t('eApp:review.view')}</ViewText>
            )}
            {!disabled && <Icon.ChevronRight />}
          </ArrowContainer>
        </Row>
        {eAppActionPending && (
          <ActionPendingContainer>
            <Icon.Warning size={sizes[4]} fill={colors.palette?.alertRed} />
            <ActionPendingText>
              {t('eApp:review.applicationForm.reviewToProceed')}
            </ActionPendingText>
          </ActionPendingContainer>
        )}
      </Container>
    </>
  );
};

export default memo(SectionItemTablet) as typeof SectionItemTablet;
