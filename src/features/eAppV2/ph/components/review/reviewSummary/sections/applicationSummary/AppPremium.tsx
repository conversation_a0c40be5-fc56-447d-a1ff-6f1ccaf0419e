import { memo, useCallback, useMemo } from 'react';
import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { formatCurrency } from 'utils';
import { useTranslation } from 'react-i18next';
import { LayoutChangeEvent, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { IReviewAppInfo } from 'features/eAppV2/common/types/reviewTypes';

const Container = styled.View(({ theme }) => ({
  marginTop: theme.space[5],
  marginBottom: theme.space[1],
  flexDirection: 'row',
  flexWrap: 'wrap',
  justifyContent: 'space-between',
}));

const PartContainer = styled.View(({ theme }) => ({
  marginBottom: theme.space[2],
}));

const Label = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  paddingRight: theme.space[4],
}));

const ValueContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  paddingTop: theme.space[1],
}));

const Currency = styled(Typography.Text)(({ theme }) => ({
  lineHeight: 20,
  fontSize: 12,
  color: theme.colors.primary,
  marginTop: theme.space[1] / 2,
}));

const Value = styled(Typography.H5)(({ theme }) => ({
  color: theme.colors.primary,
  paddingLeft: theme.space[1],
}));
const Divider = styled(Animated.View)(({ theme }) => ({
  width: 1,
  marginHorizontal: theme.space[2],
  backgroundColor: '#D9D9D9',
}));

interface Props {
  data: IReviewAppInfo;
}

export const AppPremium = memo(function AppPremium({ data }: Props) {
  const { t } = useTranslation(['eApp']);
  const [sumAssuredText, premiumText] = useMemo(() => {
    return [formatCurrency(data.sumAssured), formatCurrency(data.totalPremium)];
  }, [data.sumAssured, data.totalPremium]);

  const height = useSharedValue(61);

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    height.value = e.nativeEvent.layout.height;
  }, []);

  const style = useAnimatedStyle(() => ({
    opacity: height.value >= 100 ? 0 : 1,
  }));

  return (
    <Container onLayout={onLayout}>
      <PartContainer>
        <Label>{t('eApp:review.sumAssured')}</Label>
        <ValueContainer>
          <Currency>{data.currency}</Currency>
          <Value fontWeight="bold">{sumAssuredText}</Value>
        </ValueContainer>
      </PartContainer>
      <Divider style={style} />
      <PartContainer>
        <Label>
          {t('eApp:review.premiumYear')}
          {data.perPeriod && data.perPeriod !== 'Annual'
            ? `/${data.perPeriod}`
            : ''}
        </Label>
        <ValueContainer>
          <Currency>{data.currency}</Currency>
          <Value fontWeight="bold">{premiumText}</Value>
        </ValueContainer>
      </PartContainer>
    </Container>
  );
});
export default AppPremium;
