import {
  Icon,
  Button,
  Row,
  Toast,
  addToast,
  LargeLabel,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useEffect, useRef } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import RootSiblings from 'react-native-root-siblings';
import useLatest from 'hooks/useLatest';

interface IProps {
  onAccept?: () => void;
  onClose?: () => void;
}

export default function ReviewAppFormCTA({ onAccept, onClose }: IProps) {
  const { colors, space, animation } = useTheme();
  const { t } = useTranslation(['eApp']);
  const onAcceptRef = useLatest(onAccept);

  const { isTabletMode } = useLayoutAdoptionCheck();
  const toastRef = useRef<RootSiblings>();
  useEffect(() => {
    if (toastRef.current) {
      Toast.hide(toastRef.current);
    }
    toastRef.current = addToast(
      [
        {
          message: '',
          IconLeft: ToastLeftContent,
          buttons: [
            () => (
              <Button
                style={isTabletMode && { width: 160 }}
                size={isTabletMode ? 'small' : 'medium'}
                variant="secondary"
                text={t('eApp:bar.review')}
                onPress={() => {
                  // Toast.hide(toastRef.current);
                  onAcceptRef.current?.();
                }}
              />
            ),
          ],
        },
      ],
      true,
    );
  }, [isTabletMode, onAcceptRef, space, t]);

  useEffect(() => {
    return () => {
      if (toastRef.current) {
        Toast.hide(toastRef.current);
      }
    };
  }, []);

  return null;
}

const ToastLeftContent = () => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <Row gap={space[2]}>
      {isTabletMode && <Icon.Warning fill={colors.background} />}
      <Row gap={space[3]} alignItems="center">
        <LargeLabel color={colors.background}>
          {t('eApp:review.reviewToProceed')}
        </LargeLabel>
      </Row>
    </Row>
  );
};
