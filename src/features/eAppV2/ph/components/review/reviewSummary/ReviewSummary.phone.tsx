import styled from '@emotion/native';
import { Box, H6, LargeBody } from 'cube-ui-components';
import { memo, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import PersonalInformationSummary from './sections/personalInformationSummary/PersonalInformationSummary.phone';
import HealthQuestionsSummary from './sections/healthQuestionsSummary/HealthQuestionsSummary';
import PdfSummary from './sections/pdfSummary/PdfSummary';
import ApplicationSummary from './sections/applicationSummary/ApplicationSummary';
import RenewalPaymentSummary from './sections/renewalPaymentSummary/RenewalPaymentSummary';
import DocumentsSummary from './sections/documentsSummary/DocumentsSummary';
import { useTheme } from '@emotion/react';
import useToggle from 'hooks/useToggle';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';

import UwmeDecisionModal from 'features/eAppV2/common/components/uwmeDecision/UwmeDecisionModal';
import useUWMEDecision from 'features/eAppV2/common/hooks/useUWMEDecision';
import * as ScreenOrientation from 'expo-screen-orientation';
import { DecisionsType } from 'features/eAppV2/common/types/uwmeTypes';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Signature from '../../signature/Signature';
import { checkJuvenile } from 'features/eAppV2/ph/constants/partyUtils';
import { PartyRole } from 'types/party';
import { Party } from 'types/party';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';

const Container = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    paddingVertical: theme.space[4],
  };
});

const Title = styled(H6)(({ theme }) => ({
  color: theme.colors.onSurface,
}));

const OrangeView = styled.View(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'center',
  backgroundColor: theme.colors.palette.white,
  paddingVertical: theme.space[2],
  borderTopColor: theme.colors.palette.fwdGrey[50],
  borderTopWidth: 1,
}));

const OrangeTitle = styled(LargeBody)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    color: theme.colors.palette.fwdOrange['100'],
    marginHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    flex: 1,
  };
});

export const ReviewSummaryPhone = memo(function ReviewSummaryPhone() {
  const { t } = useTranslation(['eApp']);

  const isApplicationFormViewed = useEAppStore(
    state => state.isApplicationFormViewed,
  );
  const { colors } = useTheme();
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const [decisionVisible, showDecisionModal, hideDecisionModal] = useToggle();
  const [decisions, setDecisions] = useState<DecisionsType>('Standard');
  const { initialize, decisionState, isLoading } =
    useUWMEDecision(showDecisionModal);

  useEffect(() => {
    if (decisionState?.scenario) {
      setDecisions(decisionState.scenario);
    }
  }, [decisionState]);

  const [signatureVisible, showSignature, hideSignature] = useToggle();
  const onDecisionPress = useCallback(async () => {
    hideDecisionModal();
    await ScreenOrientation.lockAsync(
      ScreenOrientation.OrientationLock.LANDSCAPE_LEFT,
    );
    showSignature();
  }, [hideDecisionModal, showSignature]);

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <ScrollView keyboardDismissMode="on-drag" nestedScrollEnabled>
        <Container>
          <Title fontWeight="bold">{t('eApp:review.reviewSummary')}</Title>
          <ApplicationSummary />
          <PersonalInformationSummary />
          <HealthQuestionsSummary />
          <PdfSummary />
          <RenewalPaymentSummary />
          <DocumentsSummary />
        </Container>
      </ScrollView>
      <OrangeView>
        <OrangeTitle fontWeight="bold">
          {t('eApp:review.reviewSummary.informationConfirm')}
        </OrangeTitle>
      </OrangeView>
      <EAppFooterPhone
        primaryDisabled={!isApplicationFormViewed}
        primaryLoading={isLoading}
        onPrimaryPress={initialize}
      />

      {decisions && decisionVisible && (
        <UwmeDecisionModal
          decisionState={decisionState}
          decisions={decisions}
          onClose={hideDecisionModal}
          onPress={onDecisionPress}
        />
      )}
      <Signature
        visible={signatureVisible}
        decisionState={decisionState}
        startingIndex={0}
        onDismiss={async () => {
          await ScreenOrientation.lockAsync(
            ScreenOrientation.OrientationLock.PORTRAIT_UP,
          );
          hideSignature();
        }}
        onFinish={async () => {
          await ScreenOrientation.lockAsync(
            ScreenOrientation.OrientationLock.PORTRAIT_UP,
          );
          hideSignature();
          nextGroup(true);
        }}
      />
    </Box>
  );
});

export default ReviewSummaryPhone;

const filterPartyForSignature = (party?: Party[]) => {
  return party
    ?.filter(
      p =>
        p.roles.includes(PartyRole.PROPOSER) ||
        p.roles.includes(PartyRole.INSURED),
    )
    .filter(p => !checkJuvenile(p));
};
