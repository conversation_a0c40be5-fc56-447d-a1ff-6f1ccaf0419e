import { Box, Row, TextField } from 'cube-ui-components';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import IconGlobe from '../icons/IconGlobe';
import TitleApplicationModal from '../modals/TitleApplicationModal';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eAppV2/common/hooks/useEAppSnapPoint';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import SearchableDropdown from 'components/SearchableDropdown';
import BottomSheetFooter from '../../../../../common/components/bottomSheetFooter/BottomSheetFooter';
import {
  NationalityDetailsForm,
  nationalityDetailsSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { CityTown, Country, Nationality } from 'types/optionList';
import { PH_COUNTRY } from 'constants/optionList';
import BottomSheetFooterSpace from 'features/eAppV2/common/components/BottomSheetFooterSpace';
import { View } from 'react-native';
import useNationalityDetailsLogic from '../../../../hooks/useNationalityDetailsLogic';
import { SharedValue } from 'react-native-reanimated';

interface Props {
  onDismiss: () => void;
  value: NationalityDetailsForm;
  onDone: (values: NationalityDetailsForm) => void;
}

export const NationalityDetails = ({ onDismiss, value, onDone }: Props) => {
  const resolver = useEAppValidationResolver(nationalityDetailsSchema);
  const form = useForm<NationalityDetailsForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });

  const {
    control,
    watch,
    handleSubmit,
    getValues,
    setValue,
    formState: { isValid },
  } = form;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const onDoneCb = useCallback(
    (values: NationalityDetailsForm) => {
      onDone(values);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [onDone],
  );

  const {
    theme,
    t,
    optionList,
    isFetchingOptionList,
    uniqueCityList,
    submit,
    isNarrowScreen,
  } = useNationalityDetailsLogic({ onDone: onDoneCb, form });

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <View onLayout={handleContentLayout}>
            <Row px={theme.space[isNarrowScreen ? 3 : 4]}>
              <TitleApplicationModal
                icon={<IconGlobe />}
                content={t('eApp:nationalityDetails.title')}
              />
            </Row>
            <BottomSheetScrollView
              keyboardDismissMode="on-drag"
              style={{
                paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
              }}>
              {/* Form */}
              <Box mt={theme.space[1]}>
                <Input
                  control={control}
                  as={SearchableDropdown<Nationality, string>}
                  modalTitle={t('eApp:nationalityDetails.nationality')}
                  name="nationality"
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:nationalityDetails.nationality')}
                  disabled={isFetchingOptionList}
                  data={optionList?.NATIONALITY.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  searchable
                />
                <Input
                  control={control}
                  as={SearchableDropdown<Country, string>}
                  name="countryOfBirth"
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:nationalityDetails.dateOfBirth')}
                  modalTitle={t('eApp:nationalityDetails.dateOfBirth')}
                  disabled={isFetchingOptionList}
                  data={optionList?.COUNTRY.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  searchable
                  onChange={(value: string) => {
                    if (
                      (value === PH_COUNTRY &&
                        getValues('countryOfBirth') !== PH_COUNTRY) ||
                      (value !== PH_COUNTRY &&
                        getValues('countryOfBirth') === PH_COUNTRY)
                    ) {
                      setValue('placeOfBirth', '');
                    }
                  }}
                />
                {watch('countryOfBirth') === PH_COUNTRY ? (
                  <Input
                    control={control}
                    as={SearchableDropdown<CityTown, string>}
                    name="placeOfBirth"
                    style={{
                      marginTop: theme.space[5],
                      marginBottom: theme.space[4],
                    }}
                    label={t('eApp:nationalityDetails.placeOfBirth')}
                    modalTitle={t('eApp:nationalityDetails.placeOfBirth')}
                    disabled={isFetchingOptionList}
                    data={uniqueCityList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                    searchable
                  />
                ) : (
                  <Input
                    control={control}
                    as={TextField}
                    name="placeOfBirth"
                    label={t('eApp:nationalityDetails.placeOfBirth')}
                    style={{
                      marginTop: theme.space[5],
                      marginBottom: theme.space[4],
                    }}
                  />
                )}
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
