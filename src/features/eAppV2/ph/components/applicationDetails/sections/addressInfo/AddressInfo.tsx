import { memo, useCallback, useEffect } from 'react';
import {
  H7,
  H8,
  PictogramIcon,
  Row,
  TextField,
  Box,
  SmallBody,
  Checkbox,
  Column,
} from 'cube-ui-components';
import { StyleSheet } from 'react-native';
import Input from 'components/Input';
import { useForm } from 'react-hook-form';

import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { useEAppSnapPoints } from 'features/eAppV2/common/hooks/useEAppSnapPoint';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import SearchableDropdown from 'components/SearchableDropdown';
import BottomSheetFooter from '../../../../../common/components/bottomSheetFooter/BottomSheetFooter';
import {
  AddressInfoForm,
  addressInfoSchema,
  maxAddressLength,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { CityTown, Country, Postcode, Province } from 'types/optionList';
import {
  NEW_ADDRESS_OPTION,
  PH_COUNTRY,
  PH_OPTION_LIST,
} from 'constants/optionList';
import styled from '@emotion/native';
import BottomSheetFooterSpace from 'features/eAppV2/common/components/BottomSheetFooterSpace';
import useAddressInfoLogic from 'features/eAppV2/ph/hooks/useAddressInfoLogic';

export const AddressInfo = memo(function ({
  onDismiss,
  value,
  onDone,
  autoAssignPB,
}: {
  onDismiss: () => void;
  value: AddressInfoForm;
  onDone: (values: AddressInfoForm) => void;
  autoAssignPB?: boolean;
}) {
  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints();

  const onDoneCb = useCallback(
    (values: AddressInfoForm) => {
      onDone(values);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [bottomSheetProps.bottomSheetRef, onDone],
  );

  const resolver = useEAppValidationResolver(addressInfoSchema);
  const form = useForm<AddressInfoForm>({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
  });
  const {
    watch,
    reset,
    getValues,
    setValue,
    handleSubmit,
    control,
    formState: { isValid },
  } = form;

  const {
    t,
    colors,
    space,
    typography,
    optionList,
    isFetchingOptionList,
    itemKey,
    country,
    province,
    city,
    businessAddress,
    businessCountry,
    businessProvince,
    businessCity,
    provinceOption,
    cityList,
    cityOption,
    postcodeList,
    businessProvinceOption,
    businessCityList,
    businessCityOption,
    businessPostcodeList,
    isReusedFromPolicyOwner,
    setIsReusedFromPolicyOwner,
    policyOwnerAddressInfo,
    customerType,
    isEntity,
    submit,
    showCheckBox,
    isNarrowScreen,
  } = useAddressInfoLogic({ value, onDone: onDoneCb, form });

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  useEffect(() => {
    if (autoAssignPB) {
      setIsReusedFromPolicyOwner(true);
    }
  }, [autoAssignPB]);

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <Row alignItems="center" px={space[isNarrowScreen ? 3 : 4]}>
            <PictogramIcon.Home size={40} />
            <H7 fontWeight="bold" color={colors.primary}>
              {t('eApp:addressInfo.title')}
            </H7>
          </Row>
          <BottomSheetScrollView
            keyboardDismissMode="on-drag"
            style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
            <Box mt={15}>
              <Box mb={20}>
                <H8 fontWeight="bold" color={colors.primary}>
                  {isEntity && itemKey === 'policyOwner'
                    ? t('eApp:addressInfo.currentAddress.entity')
                    : t('eApp:addressInfo.currentAddress')}
                </H8>
              </Box>
              {showCheckBox && (
                <Input
                  control={control}
                  as={Checkbox}
                  name="sameWithPo"
                  label="Information is same as the policy owner"
                  style={{ marginBottom: 22, opacity: autoAssignPB ? 0.5 : 1 }}
                  onChange={checked => {
                    setIsReusedFromPolicyOwner(checked);
                  }}
                  disabled={autoAssignPB}
                />
              )}
              <Input
                disabled={isReusedFromPolicyOwner || isFetchingOptionList}
                control={control}
                as={SearchableDropdown<Country, string>}
                name="country"
                label={t('eApp:addressInfo.country')}
                data={optionList?.COUNTRY.options ?? []}
                getItemLabel={item => item.label}
                getItemValue={item => item.value}
                modalTitle={t('eApp:addressInfo.country')}
                searchable
                onChange={(el: string) => {
                  if (el === PH_COUNTRY) {
                    setValue('addressLine3', '');
                  }
                }}
              />
            </Box>

            <Input
              disabled={isReusedFromPolicyOwner}
              control={control}
              as={TextField}
              name="addressLine1"
              label={t('eApp:addressInfo.addressLine1')}
              style={[
                styles.mt20,
                {
                  flex: 1,
                  maxHeight: typography.largeLabel.lineHeight * 4,
                },
              ]}
              inputContainerStyle={{
                maxHeight: typography.largeLabel.lineHeight * 4,
              }}
              multiline
              autoExpand
              maxLength={maxAddressLength}
            />
            <Box mr={40} ml={20} mt={4}>
              <SmallBody color={'#636566'}>
                {t('eApp:addressInfo.addressLine1.note')}
              </SmallBody>
            </Box>

            <Input
              disabled={isReusedFromPolicyOwner}
              control={control}
              as={TextField}
              name="addressLine2"
              label={t('eApp:addressInfo.addressLine2')}
              style={[
                styles.mt20,
                {
                  flex: 1,
                  maxHeight: typography.largeLabel.lineHeight * 4,
                },
              ]}
              inputContainerStyle={{
                maxHeight: typography.largeLabel.lineHeight * 4,
              }}
              multiline
              autoExpand
              maxLength={maxAddressLength}
            />
            <Box mr={40} ml={20} mt={4}>
              <SmallBody color={'#636566'}>
                {t('eApp:addressInfo.addressLine2.note')}
              </SmallBody>
            </Box>

            {country === PH_COUNTRY ? (
              <>
                <Input
                  disabled={isReusedFromPolicyOwner || isFetchingOptionList}
                  control={control}
                  as={SearchableDropdown<Province, string>}
                  name="province"
                  label={t('eApp:addressInfo.province')}
                  data={optionList?.PROVINCE.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  style={styles.mt20}
                  modalTitle={t('eApp:addressInfo.province')}
                  searchable
                  onChange={(value: string) => {
                    if (value !== getValues('province')) {
                      setValue('city', '');
                      setValue('postalCode', '');
                    }
                  }}
                />

                <Input
                  disabled={
                    isReusedFromPolicyOwner ||
                    isFetchingOptionList ||
                    province === ''
                  }
                  control={control}
                  as={SearchableDropdown<CityTown, string>}
                  name="city"
                  label={t('eApp:addressInfo.city')}
                  data={cityList}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  style={styles.mt20}
                  modalTitle={t('eApp:addressInfo.city')}
                  searchable
                  onChange={(value: string) => {
                    if (value !== getValues('city')) {
                      setValue('postalCode', '');
                    }
                  }}
                />

                <Input
                  disabled={
                    isReusedFromPolicyOwner ||
                    isFetchingOptionList ||
                    city === ''
                  }
                  control={control}
                  as={SearchableDropdown<Postcode, string>}
                  name="postalCode"
                  label={t('eApp:addressInfo.postalCode')}
                  data={postcodeList}
                  getItemLabel={item => item.label}
                  getItemValue={item => String(item.value)}
                  style={styles.mt20}
                  modalTitle={t('eApp:addressInfo.postalCode')}
                />
              </>
            ) : (
              <Input
                control={control}
                as={TextFieldContainer}
                name="addressLine3"
                label={t('eApp:addressInfo.townCityProvinceState')}
                inputContainerStyle={{
                  maxHeight: typography.largeLabel.lineHeight * 4,
                }}
                multiline
                autoExpand
                maxLength={maxAddressLength}
              />
            )}

            {isEntity &&
            (itemKey === 'policyOwner' ||
              itemKey === 'primaryBeneficiary' ||
              itemKey === 'secondaryBeneficiary') ? null : (
              <>
                <Box mt={20}>
                  <H8 fontWeight="bold" color={colors.primary}>
                    {t('eApp:addressInfo.businessAddress')}
                  </H8>
                </Box>
                <Input
                  disabled={isReusedFromPolicyOwner || isFetchingOptionList}
                  control={control}
                  as={
                    SearchableDropdown<
                      (typeof PH_OPTION_LIST.BUSINESS_ADDRESSES)[0],
                      string
                    >
                  }
                  name="businessAddress"
                  label={t('eApp:addressInfo.address')}
                  data={PH_OPTION_LIST.BUSINESS_ADDRESSES}
                  getItemValue={item => item.value}
                  getItemLabel={item => item.label}
                  style={styles.mt20}
                  modalTitle={t('eApp:addressInfo.address')}
                />

                <Column mb={space[4]}>
                  {businessAddress === NEW_ADDRESS_OPTION ? (
                    <>
                      <Input
                        disabled={
                          isReusedFromPolicyOwner || isFetchingOptionList
                        }
                        control={control}
                        as={SearchableDropdown<Country, string>}
                        name="businessCountry"
                        label={t('eApp:addressInfo.country')}
                        data={optionList?.COUNTRY.options ?? []}
                        getItemValue={item => item.value}
                        getItemLabel={item => item.label}
                        modalTitle={t('eApp:addressInfo.country')}
                        style={styles.mt20}
                        searchable
                        onChange={(el: string) => {
                          if (el === PH_COUNTRY) {
                            setValue('businessAddressLine3', '');
                          }
                        }}
                      />
                      <Input
                        disabled={isReusedFromPolicyOwner}
                        control={control}
                        as={TextField}
                        name="businessAddressLine1"
                        label={t('eApp:addressInfo.addressLine1')}
                        style={[
                          styles.mt20,
                          {
                            maxHeight: typography.largeLabel.lineHeight * 4,
                          },
                        ]}
                        inputContainerStyle={{
                          maxHeight: typography.largeLabel.lineHeight * 4,
                        }}
                        multiline
                        autoExpand
                        maxLength={maxAddressLength}
                      />
                      <Box mr={40} ml={20} mt={4}>
                        <SmallBody color={'#636566'}>
                          {t('eApp:addressInfo.addressLine1.note')}
                        </SmallBody>
                      </Box>

                      <Input
                        disabled={isReusedFromPolicyOwner}
                        control={control}
                        as={TextField}
                        name="businessAddressLine2"
                        label={t('eApp:addressInfo.addressLine2')}
                        style={[
                          styles.mt20,
                          {
                            maxHeight: typography.largeLabel.lineHeight * 4,
                          },
                        ]}
                        inputContainerStyle={{
                          maxHeight: typography.largeLabel.lineHeight * 4,
                        }}
                        multiline
                        autoExpand
                        maxLength={maxAddressLength}
                      />
                      <Box mr={40} ml={20} mt={4}>
                        <SmallBody color={'#636566'}>
                          {t('eApp:addressInfo.addressLine2.note')}
                        </SmallBody>
                      </Box>

                      {businessCountry === PH_COUNTRY ? (
                        <>
                          <Input
                            disabled={
                              isReusedFromPolicyOwner || isFetchingOptionList
                            }
                            control={control}
                            as={SearchableDropdown<Province, string>}
                            name="businessProvince"
                            label={t('eApp:addressInfo.province')}
                            data={optionList?.PROVINCE.options ?? []}
                            getItemValue={item => item.value}
                            getItemLabel={item => item.label}
                            style={styles.mt20}
                            modalTitle={t('eApp:addressInfo.province')}
                            searchable
                            onChange={(value: string) => {
                              if (value !== getValues('businessProvince')) {
                                setValue('businessCity', '');
                                setValue('businessPostalCode', '');
                              }
                            }}
                          />
                          <Input
                            disabled={
                              isReusedFromPolicyOwner ||
                              isFetchingOptionList ||
                              businessProvince === ''
                            }
                            control={control}
                            as={SearchableDropdown<CityTown, string>}
                            name="businessCity"
                            label={t('eApp:addressInfo.city')}
                            data={businessCityList}
                            getItemValue={item => item.value}
                            getItemLabel={item => item.label}
                            style={styles.mt20}
                            modalTitle={t('eApp:addressInfo.city')}
                            searchable
                          />
                          <Input
                            disabled={
                              isReusedFromPolicyOwner ||
                              isFetchingOptionList ||
                              businessCity === ''
                            }
                            control={control}
                            as={SearchableDropdown<Postcode, string>}
                            name="businessPostalCode"
                            label={t('eApp:addressInfo.postalCode')}
                            data={businessPostcodeList}
                            getItemLabel={item => item.label}
                            getItemValue={item => String(item.value)}
                            style={styles.mt20}
                            modalTitle={t('eApp:addressInfo.postalCode')}
                          />
                        </>
                      ) : (
                        <Input
                          control={control}
                          as={TextFieldContainer}
                          name="businessAddressLine3"
                          label={t('eApp:addressInfo.townCityProvinceState')}
                          inputContainerStyle={{
                            maxHeight: typography.largeLabel.lineHeight * 4,
                          }}
                          multiline
                          autoExpand
                          maxLength={maxAddressLength}
                        />
                      )}
                    </>
                  ) : (
                    <></>
                  )}
                </Column>
              </>
            )}
            <BottomSheetFooterSpace />
          </BottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const styles = StyleSheet.create({
  mt20: {
    marginTop: 20,
  },
});

const TextFieldContainer = styled(TextField)(
  ({ theme: { space, typography } }) => ({
    marginTop: space[5],
    flex: 1,
    maxHeight: typography.largeLabel.lineHeight * 4,
  }),
);
