import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box, H7, LargeBody, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import OccupationRow from './OccupationRow';
import { ScrollView, TouchableOpacity } from 'react-native';
import { OccupationGroup, OccupationSubgroup } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';

interface Props {
  paragraphTitle: string;
  paragraphNumber: OccupationGroup['value'];
  occupationType: OccupationGroup['value'] | '';
  setOccupationType: (value: string) => void;
  occupationTypeDetail: OccupationSubgroup['value'] | '';
  setOccupationTypeDetail: (value: string) => void;
}

export default function OccupationList({
  paragraphTitle,
  paragraphNumber,
  occupationType,
  setOccupationType,
  occupationTypeDetail,
  setOccupationTypeDetail,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();

  switch (paragraphNumber) {
    case '001B':
      return (
        <OccupationRow
          paragraphTitle={paragraphTitle}
          expand={occupationType === paragraphNumber}
          onSelect={() => setOccupationType(paragraphNumber)}>
          <LargeBody>
            {t('eApp:occupationDetails.detailRow1.paragraph1')}
          </LargeBody>
          <LabelBold text={t('eApp:occupationDetails.detailRow1.title1')} />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow1.paragraph2')}
          />
        </OccupationRow>
      );
    case '001A':
      return (
        <OccupationRow
          paragraphTitle={paragraphTitle}
          expand={occupationType === paragraphNumber}
          onSelect={() => setOccupationType(paragraphNumber)}>
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow2.paragraph1')}
            mt={space[0]}
          />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow2.paragraph2')}
            mt={space[0]}
          />
          <IndexList
            text={t('eApp:occupationDetails.detailRow2.paragraph3.1')}
          />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow2.paragraph4')}
            mt={space[0]}
            mtItem={space[4]}
          />
          <LabelBold text={t('eApp:occupationDetails.detailRow1.title1')} />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow2.paragraph8')}
          />
        </OccupationRow>
      );
    case '0002':
      return (
        <OccupationRow
          paragraphTitle={paragraphTitle}
          expand={occupationType === paragraphNumber}
          onSelect={() => setOccupationType(paragraphNumber)}>
          <LargeBody>
            {t('eApp:occupationDetails.detailRow3.paragraph1')}
          </LargeBody>
          <LabelBold text={t('eApp:occupationDetails.detailRow1.title1')} />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow3.paragraph2')}
          />
        </OccupationRow>
      );
    case '0003':
      return (
        <OccupationRow
          paragraphTitle={paragraphTitle}
          expand={occupationType === paragraphNumber}
          onSelect={() => setOccupationType(paragraphNumber)}>
          <LargeBody>
            {t('eApp:occupationDetails.detailRow4.paragraph1')}
          </LargeBody>
          <LabelBold text={t('eApp:occupationDetails.detailRow1.title1')} />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow4.paragraph2')}
          />
        </OccupationRow>
      );
    case '0004':
      return (
        <GovernmentOfficial
          occupationType={occupationType}
          setOccupationType={setOccupationType}
          occupationTypeDetail={occupationTypeDetail}
          setOccupationTypeDetail={setOccupationTypeDetail}
          paragraphTitle={paragraphTitle}
          paragraphNumber={paragraphNumber}
        />
      );
    case '0005':
      return (
        <OccupationRow
          paragraphTitle={paragraphTitle}
          expand={occupationType === paragraphNumber}
          onSelect={() => setOccupationType(paragraphNumber)}>
          <LargeBody>
            {t('eApp:occupationDetails.detailRow6.paragraph1')}
          </LargeBody>
          <LabelBold text={t('eApp:occupationDetails.detailRow1.title1')} />
          <CirleBlackList
            text={t('eApp:occupationDetails.detailRow6.paragraph2')}
          />
        </OccupationRow>
      );
    default:
      return null;
  }
}

const GovernmentOfficial = ({
  paragraphTitle,
  paragraphNumber,
  occupationType,
  setOccupationType,
  occupationTypeDetail,
  setOccupationTypeDetail,
}: Props) => {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { data: optionList } = useGetOptionList();

  return (
    <OccupationRow
      paragraphTitle={paragraphTitle}
      expand={occupationType === paragraphNumber}
      onSelect={() => setOccupationType(paragraphNumber)}>
      <LargeBody>{t('eApp:occupationDetails.detailRow5.paragraph1')}</LargeBody>
      <LabelBold text={t('eApp:occupationDetails.detailRow5.paragraph2')} />
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={{
          marginTop: space[4],
          marginLeft: -space[4],
          marginRight: -space[4],
        }}>
        <>
          {optionList?.OCCUPATION_SUBGROUP?.options.map((subgroup, index) => {
            const isSelected = occupationTypeDetail === subgroup.value;
            const colorBorder = isSelected
              ? colors.primary
              : colors.palette.fwdGrey[100];
            const color = isSelected
              ? colors.primary
              : colors.palette.fwdDarkGreen[100];
            const backgroundColor = isSelected
              ? colors.palette.fwdOrange[20]
              : colors.palette.white;
            const borderWidth = isSelected ? 2 : 1;
            const isFirstItem = index === 0;
            const isLatestItem =
              index === optionList?.OCCUPATION_SUBGROUP?.options?.length - 1;
            return (
              <TouchableOpacity
                key={subgroup.value}
                onPress={() => setOccupationTypeDetail(subgroup.value)}
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingHorizontal: 14,
                  borderRadius: 4,
                  borderWidth: borderWidth,
                  borderColor: colorBorder,
                  maxWidth: 254,
                  marginLeft: isFirstItem ? space[4] : space[2],
                  marginRight: isLatestItem ? space[4] : space[2],
                  backgroundColor: backgroundColor,
                  padding: space[3],
                }}>
                <LargeBody fontWeight="medium" color={color}>
                  {subgroup.label}
                </LargeBody>
              </TouchableOpacity>
            );
          })}
        </>
      </ScrollView>
      {occupationTypeDetail !== '' && (
        <CirleBlackList
          text={t(
            `eApp:occupationDetails.detailRow5.row${occupationTypeDetail}.paragraph1`,
          )}
        />
      )}
    </OccupationRow>
  );
};

interface ListProps {
  text: string;
  mt?: number;
  mtItem?: number;
}

const LabelBold = (props: ListProps) => {
  const { space } = useTheme();
  const { text } = props;
  return (
    <H7 style={{ marginTop: space[4] }} fontWeight="bold">
      {text}
    </H7>
  );
};

const CirleBlackList = (props: ListProps) => {
  const { space } = useTheme();
  const { text, mt = space[2], mtItem = space[0] } = props;
  return (
    <Box mt={mt} ml={space[1]}>
      {text.split('\n').map(item => {
        return (
          <Row key={item} mt={mtItem}>
            <LargeBody>{'\u2022 '}</LargeBody>
            <LargeBody style={{ flex: 1 }}>{item}</LargeBody>
          </Row>
        );
      })}
    </Box>
  );
};

const IndexList = ({ text }: ListProps) => {
  const { space } = useTheme();
  return (
    <Box mt={space[2]}>
      {text.split('\n').map((item, index) => {
        return (
          <Row
            key={item}
            style={{
              marginLeft: space[4],
            }}>
            <LargeBody>{`${index + 1}. `}</LargeBody>
            <LargeBody style={{ flex: 1 }}>{item}</LargeBody>
          </Row>
        );
      })}
    </Box>
  );
};
