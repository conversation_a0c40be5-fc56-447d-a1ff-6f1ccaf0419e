import {
  Box,
  Dropdown,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import Input from 'components/Input/Input';
import TitleApplicationModal from '../../modals/TitleApplicationModal';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import { useEAppSnapPoints } from 'features/eAppV2/common/hooks/useEAppSnapPoint';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import SearchableDropdown from 'components/SearchableDropdown';
import BottomSheetFooter from '../../../../../../common/components/bottomSheetFooter/BottomSheetFooter';
import {
  EntityDetailsForm,
  entityDetailsSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/entityDetailsValidation';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { BusinessNatureEntity, LeadSource } from 'types/optionList';
import BottomSheetFooterSpace from 'features/eAppV2/common/components/BottomSheetFooterSpace';
import { View } from 'react-native';
import useEntityDetailsLogic from 'features/eAppV2/ph/hooks/useEntityDetailsLogic';
import { SharedValue } from 'react-native-reanimated';

interface Props {
  onDismiss: () => void;
  value: EntityDetailsForm;
  onDone: (values: EntityDetailsForm) => void;
}

export const EntityDetails = ({ onDismiss, value, onDone }: Props) => {
  const resolver = useEAppValidationResolver(entityDetailsSchema);
  const form = useForm<EntityDetailsForm>({
    mode: 'onBlur',
    defaultValues: {
      ...value,
      leadSource: value.leadSource ?? 'Self Generated',
    },
    resolver,
  });

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = form;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useEAppSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const onDoneCb = useCallback(
    (data: EntityDetailsForm) => {
      onDone(data);
      bottomSheetProps.bottomSheetRef.current?.close();
    },
    [bottomSheetProps.bottomSheetRef, onDone],
  );

  const { t, optionList, isFetchingOptionList, isNarrowScreen, theme, submit } =
    useEntityDetailsLogic({ onDone: onDoneCb, form, value });

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter
          {...props}
          disabled={!isValid}
          onPress={handleSubmit(submit)}
        />
      );
    },
    [handleSubmit, isValid, submit],
  );

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          style={{ padding: 0 }}
          footerComponent={renderFooter}>
          <View onLayout={handleContentLayout}>
            <Row px={theme.space[isNarrowScreen ? 3 : 4]}>
              <TitleApplicationModal
                icon={<PictogramIcon.Building2 size={40} />}
                content={t('eApp:entityDetails.title')}
              />
            </Row>
            <BottomSheetScrollView
              keyboardDismissMode="on-drag"
              style={{
                paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
              }}>
              {/* Form */}
              <Box mt={theme.space[1]}>
                <SearchableDropdown
                  modalTitle={t('eApp:entityDetails.clientType')}
                  style={{
                    marginTop: theme.space[5],
                  }}
                  value={'ENTITY'}
                  label={'Client Type'}
                  disabled
                  data={[{ label: 'Entity', value: 'ENTITY' }]}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                />
                <Input
                  control={control}
                  as={TextField}
                  name="entityName"
                  label={t('eApp:entityDetails.entityName')}
                  style={{
                    marginTop: theme.space[5],
                  }}
                  disabled={!!value.entityName}
                />
                <Input
                  control={control}
                  as={SearchableDropdown<BusinessNatureEntity, string>}
                  modalTitle={t('eApp:entityDetails.businessNature')}
                  name="businessNature"
                  style={{
                    marginTop: theme.space[5],
                  }}
                  label={t('eApp:entityDetails.businessNature')}
                  disabled={isFetchingOptionList || !!value.businessNature}
                  data={optionList?.BUSINESS_NATURE_ENTITY.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  searchable
                />
                <Input
                  control={control}
                  as={Dropdown<LeadSource, string>}
                  name="leadSource"
                  style={{
                    marginTop: theme.space[5],
                    marginBottom: theme.space[4],
                  }}
                  label={t('eApp:entityDetails.leadSource')}
                  modalTitle={t('eApp:entityDetails.leadSource')}
                  disabled={isFetchingOptionList || !!value.leadSource}
                  data={optionList?.LEAD_SOURCE.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                />
              </Box>
              <BottomSheetFooterSpace />
            </BottomSheetScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
};
