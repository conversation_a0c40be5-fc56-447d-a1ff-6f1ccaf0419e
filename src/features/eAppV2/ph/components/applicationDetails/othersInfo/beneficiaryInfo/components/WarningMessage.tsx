import styled from '@emotion/native';
import { memo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';

const ErrorMessageContainer = styled.View(({ theme: { space, colors } }) => ({
  flexDirection: 'row',
  gap: 4,
}));

const ErrorText = styled(Typography.Body)(({ theme: { colors, space } }) => ({
  color: colors.palette.alertRed,
}));

interface Props {
  message: string;
  style?: StyleProp<ViewStyle>;
}

export const WarningMessage = memo(function WarningMessage({
  message,
  style,
}: Props) {
  const theme = useTheme();
  return (
    <ErrorMessageContainer style={style}>
      <Icon.Warning
        height={24}
        width={24}
        fill={theme.colors.palette.alertRed}
      />
      <ErrorText fontWeight="normal">{message}</ErrorText>
    </ErrorMessageContainer>
  );
});
