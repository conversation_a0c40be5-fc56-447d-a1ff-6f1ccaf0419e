import styled from '@emotion/native';
import DialogPhone from 'components/Dialog.phone';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  PictogramIcon,
  Row,
  Switch,
  TextField,
} from 'cube-ui-components';
import React, { useEffect } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import { AddressInfo } from '../../../sections/addressInfo/AddressInfo';
import ApplicationDetailsPhoneSection from '../../../../../../common/components/ApplicationDetailsPhoneSection';
import { ContactDetails } from '../../../sections/contactDetails/ContactDetails';
import IconGlobe from '../../../sections/icons/IconGlobe';
import IconWork from '../../../sections/icons/IconWork';
import { NationalityDetails } from '../../../sections/nationalityDetails/NationalityDetails';
import { OccupationDetails } from '../../../sections/occupationDetails/OccupationDetails';
import { PayorPersonalDetails } from '../../../sections/personalDetails/PayorPersonalDetails';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import usePayorInfoLogic from '../../../../../hooks/usePayorInfoLogic';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { payorPersonalDetailsSchema } from 'features/eAppV2/ph/validations/applicationDetails/sections/personalDetailsValidation';
import { contactDetailsSchema } from 'features/eAppV2/ph/validations/applicationDetails/sections/contactDetailsValidation';
import { addressInfoSchema } from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import { nationalityDetailsSchema } from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import { occupationDetailsSchema } from 'features/eAppV2/ph/validations/applicationDetails/sections/occupationDetailsValidation';
import OnSaveHandler from '../../../OnSaveHandler';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import useToggle from 'hooks/useToggle';
import { AppEvent, OcrRole } from 'types/event';
import { EventRegister } from 'utils/helper/eventRegister';

const emptyValues = {};

export default function PayorInfoPhone() {
  const {
    space,
    colors,
    isNarrowScreen,
    setDialogVisible,
    hasPayor,
    setHasPayor,
    data,
    setThirdPartyPaymentReason,
    setData,
    isSavingParty,
    isDeletingParty,
    isUpdatingCase,
    isSavingOcr,
    isActionDisabled,
    onSubmit,
    dialogVisible,
    onSave,
  } = usePayorInfoLogic();

  const [isPersonalDetailsVisible, showPersonalDetails, hidePersonalDetails] =
    useToggle();

  const incomplete = isActionDisabled;
  useIncompleteSync(incomplete, 'appDetail', 'other', 'payor');

  useEffect(() => {
    const sub = EventRegister.addEventListener(AppEvent.OcrCaptured, args => {
      if (args.role !== OcrRole.Payor) {
        return;
      }
      console.log(
        '[PayorInfo] received',
        AppEvent.OcrCaptured,
        'invoking showPersonalDetails ...',
      );
      showPersonalDetails();
    });
    return () => {
      EventRegister.removeEventListener(sub);
    };
  }, [showPersonalDetails]);

  useEffect(() => {
    const sub = EventRegister.addEventListener(AppEvent.OcrCancelled, args => {
      if (args.role !== OcrRole.Payor) {
        return;
      }
      console.log(
        '[PayorInfo] received',
        AppEvent.OcrCancelled,
        'invoking showPersonalDetails ...',
      );
      showPersonalDetails();
    });
    return () => {
      EventRegister.removeEventListener(sub);
    };
  }, [showPersonalDetails]);

  return (
    <Box flex={1}>
      <ScrollView keyboardDismissMode="on-drag">
        <Row my={space[4]} mx={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">Payor's information</H6>
          <Box w={space[2]} />
          <TouchableOpacity
            hitSlop={ICON_HIT_SLOP}
            onPress={() => setDialogVisible(true)}>
            <Icon.InfoCircle fill={colors.palette.black} />
          </TouchableOpacity>
        </Row>
        <ToggleContainer>
          <Row mb={space[4]}>
            <LargeLabel fontWeight="bold">
              Will the premiums for this policy be paid for by a person/entity
              other than the policy owner?
            </LargeLabel>
          </Row>
          <Switch
            label={hasPayor ? 'Yes' : 'No'}
            checked={hasPayor}
            onChange={setHasPayor}
          />
          {hasPayor && (
            <TextField
              style={{ marginTop: space[6] }}
              label="Reason for third party payment"
              value={data.reasonForThirdPartyPayment}
              onChange={setThirdPartyPaymentReason}
              hint={`${data.reasonForThirdPartyPayment.length}/30`}
              maxLength={30}
            />
          )}
        </ToggleContainer>
        {hasPayor && (
          <FormContainer>
            <ApplicationDetailsPhoneSection
              icon={PictogramIcon.Lanyard}
              name="Personal details"
              done={data.personalDetails.done}
              form={PayorPersonalDetails}
              value={data.personalDetails || emptyValues}
              visible={isPersonalDetailsVisible}
              show={showPersonalDetails}
              hide={hidePersonalDetails}
              onDone={values => {
                if (values) {
                  setData({
                    personalDetails: {
                      ...values,
                      done: true,
                    },
                  });
                }
              }}
              schema={payorPersonalDetailsSchema}
              partyId={data.id}
              ocrResult={data.ocrResult}
            />
            <ApplicationDetailsPhoneSection
              icon={PictogramIcon.Call2}
              name="Contact details"
              done={data.contactDetails.done}
              form={ContactDetails}
              value={data.contactDetails || emptyValues}
              onDone={values => {
                if (values) {
                  setData({
                    contactDetails: {
                      ...values,
                      done: true,
                    },
                  });
                }
              }}
              schema={contactDetailsSchema}
            />
            <ApplicationDetailsPhoneSection
              icon={PictogramIcon.Home}
              name="Address information"
              done={data.addressInfo.done}
              form={AddressInfo}
              value={data.addressInfo || emptyValues}
              onDone={values => {
                if (values) {
                  setData({
                    addressInfo: {
                      ...values,
                      done: true,
                    },
                  });
                }
              }}
              schema={addressInfoSchema}
            />
            <ApplicationDetailsPhoneSection
              icon={IconGlobe}
              name="Nationality details"
              done={data.nationalityDetails.done}
              form={NationalityDetails}
              value={data.nationalityDetails || emptyValues}
              onDone={values => {
                if (values) {
                  setData({
                    nationalityDetails: {
                      ...values,
                      done: true,
                    },
                  });
                }
              }}
              schema={nationalityDetailsSchema}
            />
            <ApplicationDetailsPhoneSection
              icon={IconWork}
              name="Occupation details"
              done={data.occupationDetails.done}
              form={OccupationDetails}
              value={data.occupationDetails || emptyValues}
              onDone={values => {
                if (values) {
                  setData({
                    occupationDetails: {
                      ...values,
                      done: true,
                    },
                  });
                }
              }}
              schema={occupationDetailsSchema}
            />
          </FormContainer>
        )}
        <Box h={space[4]} />
      </ScrollView>
      <EAppFooterPhone
        primaryLoading={
          isSavingParty || isDeletingParty || isUpdatingCase || isSavingOcr
        }
        primaryDisabled={isActionDisabled}
        onPrimaryPress={onSubmit}
      />
      <OnSaveHandler
        onSave={onSave}
        groupKey="appDetail"
        subGroupKey="other"
        itemKey="payor"
      />
      <DialogPhone visible={dialogVisible}>
        <TouchableOpacity
          onPress={() => setDialogVisible(false)}
          hitSlop={ICON_HIT_SLOP}
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'flex-end',
          }}>
          <Icon.Close fill={colors.palette.black} />
        </TouchableOpacity>
        <H6 fontWeight="bold">Payor:</H6>
        <Box h={16} />
        <LargeBody>
          Is any natural or legal person who pay or will be paying for the
          policy premiums other than the policy owner that is acceptable
          pursuant to the Company guidelines.
        </LargeBody>
      </DialogPhone>
    </Box>
  );
}

const ToggleContainer = styled.View(
  ({ theme: { colors, space, borderRadius } }) => {
    const { isNarrowScreen } = useWindowAdaptationHelpers();
    return {
      borderRadius: borderRadius.large,
      padding: space[4],
      backgroundColor: colors.background,
      marginHorizontal: space[isNarrowScreen ? 3 : 4],
      overflow: 'hidden',
    };
  },
);
const FormContainer = styled.View(
  ({ theme: { colors, space, borderRadius } }) => {
    const { isNarrowScreen } = useWindowAdaptationHelpers();
    return {
      borderRadius: borderRadius.large,
      paddingVertical: space[2],
      backgroundColor: colors.background,
      marginHorizontal: space[isNarrowScreen ? 3 : 4],
      marginTop: space[3],
      overflow: 'hidden',
    };
  },
);
