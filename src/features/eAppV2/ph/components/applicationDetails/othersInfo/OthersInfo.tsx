import TabletSections, {
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSections';
import {
  EAppState,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import {
  PayorForm,
  payorFormDefaultValue,
  payorFormValidationSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/payorInfoValidation';
import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Control, useForm, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { shallow } from 'zustand/shallow';
import BeneficialOwnerInfoTablet from './beneficialOwnerInfo/tablet/BeneficialOwnerInfo.tablet';
import PayorInfoTablet from './payorInfo/tablet/PayorInfo.tablet';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import usePayorInfoLogic from '../../../hooks/usePayorInfoLogic';
import useContactDetailsLogic from '../../../hooks/useContactDetailsLogic';
import {
  contactDetailsDefaultValue,
  ContactDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/contactDetailsValidation';
import useNationalityDetailsLogic from 'features/eAppV2/ph/hooks/useNationalityDetailsLogic';
import {
  nationalityDetailsDefaultValue,
  NationalityDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import useAddressInfoLogic from 'features/eAppV2/ph/hooks/useAddressInfoLogic';
import {
  addressInfoDefaultValue,
  AddressInfoForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import useOccupationDetailsLogic from 'features/eAppV2/ph/hooks/useOccupationDetailsLogic';
import {
  OccupationDetailsForm,
  payorOccupationDetailsDefaultValue,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/occupationDetailsValidation';
import {
  beneficialOwnerPersonalDetailsDefaultValue,
  BeneficialOwnerPersonalDetailsForm,
  payorPersonalDetailsDefaultValue,
  PayorPersonalDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/personalDetailsValidation';
import usePayorPersonalDetailsLogic from 'features/eAppV2/ph/hooks/usePayorPersonalDetailsLogic';
import {
  BeneficialOwnerForm,
  beneficialOwnerFormDefaultValue,
  beneficialOwnerFormValidationSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/beneficialOwnerInfoValidation';
import useBeneficialOwnerInfoLogic from 'features/eAppV2/ph/hooks/useBeneficialOwnerInfoLogic';
import useBeneficialOwnerPersonalDetailsLogic from 'features/eAppV2/ph/hooks/useBeneficialOwnerPersonalDetailsLogic';
import useFullName from 'features/eAppV2/ph/hooks/useFullname';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import useBeneficiaryInfoLogic from 'features/eAppV2/ph/hooks/useBeneficiaryInfoLogic';
import BeneficiaryInfo from './beneficiaryInfo/tablet/BeneficiaryInfo.tablet';
import pickAll from 'ramda/src/pickAll';
import useSyncActivePath from 'features/eAppV2/ph/hooks/useSyncActivePath';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { ProgressSubgroup } from 'features/eAppV2/common/types/progressBarTypes';
import OnSaveHandler from '../OnSaveHandler';
import useLatest from 'hooks/useLatest';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import { useProgressBarContext } from 'features/eAppV2/common/components/progressBar/ProgressBarContext';
import { PartyType } from 'types/party';
import { EntityRelationshipType } from 'constants/optionList';
import {
  applyRegionalLogicToOcrValidationResult,
  OcrValidationResult,
  validateOcr,
} from 'features/eAppV2/common/utils/validateOcr';
import { Gender } from 'types/person';

const emptyValues = {};

export default function OthersInfo() {
  const { t } = useTranslation(['eApp']);

  //Start beneficial owner

  const beneficialOwnerScrollRef = useRef<KeyboardAwareScrollView>(null);

  const beneficialOwnerValidationResolver = useEAppValidationResolver(
    beneficialOwnerFormValidationSchema,
  );

  const beneficialOwnerLogicProps = useBeneficialOwnerInfoLogic();
  const beneficialOwnerForm = useForm<BeneficialOwnerForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...beneficialOwnerFormDefaultValue,
        ...pickAll(
          Object.keys(beneficialOwnerPersonalDetailsDefaultValue),
          beneficialOwnerLogicProps.data.personalDetails,
        ),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          beneficialOwnerLogicProps.data.contactDetails,
        ),
        ...pickAll(
          Object.keys(addressInfoDefaultValue),
          beneficialOwnerLogicProps.data.addressInfo,
        ),
        ...pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          beneficialOwnerLogicProps.data.nationalityDetails,
        ),
      }),
      [
        beneficialOwnerLogicProps.data.addressInfo,
        beneficialOwnerLogicProps.data.contactDetails,
        beneficialOwnerLogicProps.data.nationalityDetails,
        beneficialOwnerLogicProps.data.personalDetails,
      ],
    ),
    resolver: beneficialOwnerValidationResolver,
  });

  const {
    control: beneficialOwnerFormControl,
    getValues: getValueBeneficialOwner,
    formState: {
      isValid: isBeneficialOwnerValid,
      errors: beneficialOwnerErrors,
    },
  } = beneficialOwnerForm;

  const beneficialOwnerFullName = useFullName(
    beneficialOwnerFormControl as unknown as Control<{
      firstName: string;
      lastName: string;
      middleName: string | undefined;
    }>,
  );

  const beneficialOwnerPersonalDetailsOnDone = useCallback(
    (values: BeneficialOwnerPersonalDetailsForm) => {
      if (values) {
        beneficialOwnerLogicProps.setData({
          personalDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [beneficialOwnerLogicProps.setData],
  );

  const beneficialOwnerPersonalDetailsProps =
    useBeneficialOwnerPersonalDetailsLogic({
      value: beneficialOwnerLogicProps.data.personalDetails,
      onDone: beneficialOwnerPersonalDetailsOnDone,
      form: beneficialOwnerForm as unknown as UseFormReturn<BeneficialOwnerPersonalDetailsForm>,
      partyId: beneficialOwnerLogicProps.data.id,
    });

  const beneficialOwnerContactOnDone = useCallback(
    (values: ContactDetailsForm) => {
      if (values) {
        beneficialOwnerLogicProps.setData({
          contactDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [beneficialOwnerLogicProps.setData],
  );
  const beneficialOwnerContactDetailsProps = useContactDetailsLogic({
    value: beneficialOwnerLogicProps.data.contactDetails || emptyValues,
    onDone: beneficialOwnerContactOnDone,
    form: beneficialOwnerForm as unknown as UseFormReturn<ContactDetailsForm>,
  });

  const beneficialOwnerNationalityOnDone = useCallback(
    (values: NationalityDetailsForm) => {
      if (values) {
        beneficialOwnerLogicProps.setData({
          nationalityDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [beneficialOwnerLogicProps.setData],
  );
  const beneficialOwnerNationalityDetailsProps = useNationalityDetailsLogic({
    onDone: beneficialOwnerNationalityOnDone,
    form: beneficialOwnerForm as unknown as UseFormReturn<NationalityDetailsForm>,
  });

  const beneficialOwnerAddressOnDone = useCallback(
    (values: AddressInfoForm) => {
      if (values) {
        beneficialOwnerLogicProps.setData({
          addressInfo: {
            ...values,
            done: true,
          },
        });
      }
    },
    [beneficialOwnerLogicProps.setData],
  );
  const beneficialOwnerAddressInfoProps = useAddressInfoLogic({
    value: beneficialOwnerLogicProps.data.addressInfo,
    onDone: beneficialOwnerAddressOnDone,
    form: beneficialOwnerForm as unknown as UseFormReturn<AddressInfoForm>,
  });

  const onBeneficialOwnerContinue = useCallback(
    async (isSaved?: boolean) => {
      const values = getValueBeneficialOwner();
      if (
        beneficialOwnerLogicProps.hasBeneficialOwner &&
        !beneficialOwnerLogicProps.isEntity
      ) {
        try {
          await beneficialOwnerPersonalDetailsProps.handleOcrImageDeletion(
            values.document.frontImage,
          );
        } catch (ocrError) {
          if (!isSaved) {
            throw ocrError;
          }
        }
        try {
          const contactValid =
            await beneficialOwnerContactDetailsProps.validateContact(
              pickAll(Object.keys(contactDetailsDefaultValue), values),
            );
          if (!contactValid && !isSaved) {
            return;
          }
        } catch (error) {
          if (!isSaved) {
            throw error;
          }
        }
      }
      let isOcrSuccess;
      if (beneficialOwnerLogicProps.data.ocrResult) {
        const [result, mismatchFields] = validateOcr(
          {
            ...beneficialOwnerLogicProps.data.ocrResult.data,
            fullName: '',
          },
          {
            firstName: values.firstName,
            lastName: values.lastName,
            fullName: '',
            dateOfBirth: values.dateOfBirth ?? null,
            gender: values.gender as Gender,
          },
        );

        const [newResult] = applyRegionalLogicToOcrValidationResult(
          beneficialOwnerLogicProps.data.ocrResult.type,
          result,
          mismatchFields,
        );

        isOcrSuccess = newResult === OcrValidationResult.Match;
      }
      const data: EAppState['beneficialOwnerPersonalInfo'] = {
        ...beneficialOwnerLogicProps.data,
        personalDetails: pickAll(
          Object.keys(beneficialOwnerPersonalDetailsDefaultValue),
          values,
        ),
        contactDetails: pickAll(
          Object.keys(contactDetailsDefaultValue),
          values,
        ),
        addressInfo: pickAll(Object.keys(addressInfoDefaultValue), values),
        nationalityDetails: pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          values,
        ),
        isOcrSuccess,
      };
      beneficialOwnerLogicProps.setData(data);
      if (isSaved) {
        await beneficialOwnerLogicProps.onSave(data);
      } else {
        await beneficialOwnerLogicProps.onSubmit(data);
      }
    },
    [
      beneficialOwnerContactDetailsProps,
      beneficialOwnerLogicProps,
      beneficialOwnerPersonalDetailsProps,
      getValueBeneficialOwner,
    ],
  );

  const isCompletedBeneficialOwnerForm =
    beneficialOwnerLogicProps.hasBeneficialOwner &&
    !beneficialOwnerLogicProps.isEntity
      ? isBeneficialOwnerValid &&
        Object.keys(beneficialOwnerErrors).length === 0
      : true;

  const isBeneficialOwnerDisabled = !isCompletedBeneficialOwnerForm;
  const isBeneficialOwnerIncomplete = !isCompletedBeneficialOwnerForm;
  useIncompleteSync(
    isBeneficialOwnerIncomplete,
    'appDetail',
    'other',
    'beneficialOwner',
  );

  //end beneficial owner

  //Start payor
  const { hasPayor, isEntityKeymanEmployee } = useEAppStore(
    state => ({
      hasPayor: state.hasPayor,
      setHasPayor: state.setHasPayor,
      isEntityKeymanEmployee:
        state.policyOwnerPersonalInfo.personalDetails.customerType ===
          PartyType.ENTITY &&
        state.insuredPersonalInfo.personalDetails.relationship ===
          EntityRelationshipType.EMPLOYEE_KEYMAN,
    }),
    shallow,
  );

  const payorScrollRef = useRef<KeyboardAwareScrollView>(null);

  const payorValidationResolver = useEAppValidationResolver(
    payorFormValidationSchema,
  );

  const payorLogicProps = usePayorInfoLogic();

  const payorForm = useForm<PayorForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...payorFormDefaultValue,
        ...pickAll(
          Object.keys(payorPersonalDetailsDefaultValue),
          payorLogicProps.data.personalDetails,
        ),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          payorLogicProps.data.contactDetails,
        ),
        ...pickAll(
          Object.keys(addressInfoDefaultValue),
          payorLogicProps.data.addressInfo,
        ),
        ...pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          payorLogicProps.data.nationalityDetails,
        ),
        ...pickAll(
          Object.keys(payorOccupationDetailsDefaultValue),
          payorLogicProps.data.occupationDetails,
        ),
      }),
      [
        payorLogicProps.data.addressInfo,
        payorLogicProps.data.contactDetails,
        payorLogicProps.data.nationalityDetails,
        payorLogicProps.data.occupationDetails,
        payorLogicProps.data.personalDetails,
      ],
    ),
    resolver: payorValidationResolver,
  });

  const {
    control: payorFormControl,
    getValues: getValuePayor,
    formState: { isValid: isPayorValid, errors: payorErrors },
  } = payorForm;

  const payorFullName = useFullName(
    payorFormControl as unknown as Control<{
      firstName: string;
      lastName: string;
      middleName: string | undefined;
    }>,
  );

  const payorPersonalDetailsOnDone = useCallback(
    (values: PayorPersonalDetailsForm) => {
      if (values) {
        payorLogicProps.setData({
          personalDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [payorLogicProps.setData],
  );

  const payorPersonalDetailsProps = usePayorPersonalDetailsLogic({
    value: payorLogicProps.data.personalDetails,
    onDone: payorPersonalDetailsOnDone,
    form: payorForm as unknown as UseFormReturn<PayorPersonalDetailsForm>,
    partyId: payorLogicProps.data.id,
  });

  const payorContactOnDone = useCallback(
    (values: ContactDetailsForm) => {
      if (values) {
        payorLogicProps.setData({
          contactDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [payorLogicProps.setData],
  );
  const payorContactDetailsProps = useContactDetailsLogic({
    value: payorLogicProps.data.contactDetails || emptyValues,
    onDone: payorContactOnDone,
    form: payorForm as unknown as UseFormReturn<ContactDetailsForm>,
  });

  const payorNationalityOnDone = useCallback(
    (values: NationalityDetailsForm) => {
      if (values) {
        payorLogicProps.setData({
          nationalityDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [payorLogicProps.setData],
  );
  const payorNationalityDetailsProps = useNationalityDetailsLogic({
    onDone: payorNationalityOnDone,
    form: payorForm as unknown as UseFormReturn<NationalityDetailsForm>,
  });

  const payorAddressOnDone = useCallback(
    (values: AddressInfoForm) => {
      if (values) {
        payorLogicProps.setData({
          addressInfo: {
            ...values,
            done: true,
          },
        });
      }
    },
    [payorLogicProps.setData],
  );
  const payorAddressInfoProps = useAddressInfoLogic({
    value: payorLogicProps.data.addressInfo,
    onDone: payorAddressOnDone,
    form: payorForm as unknown as UseFormReturn<AddressInfoForm>,
  });

  const payorOccupationDetailsOnDone = useCallback(
    (values: OccupationDetailsForm) => {
      if (values) {
        payorLogicProps.setData({
          occupationDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [payorLogicProps.setData],
  );
  const payorOccupationDetailsProps = useOccupationDetailsLogic({
    value: payorLogicProps.data.occupationDetails,
    onDone: payorOccupationDetailsOnDone,
    form: payorForm as unknown as UseFormReturn<OccupationDetailsForm>,
  });

  const onPayorContinue = useCallback(
    async (isSaved?: boolean) => {
      const values = getValuePayor();
      if (payorLogicProps.hasPayor) {
        try {
          await payorPersonalDetailsProps.handleOcrImageDeletion(
            values.document.frontImage,
          );
        } catch (ocrError) {
          if (!isSaved) {
            throw ocrError;
          }
        }
        try {
          const contactValid = await payorContactDetailsProps.validateContact(
            pickAll(Object.keys(contactDetailsDefaultValue), values),
          );
          if (!contactValid && !isSaved) {
            return;
          }
        } catch (error) {
          if (!isSaved) {
            throw error;
          }
        }
      }
      let isOcrSuccess;
      if (payorLogicProps.data.ocrResult) {
        const [result, mismatchFields] = validateOcr(
          {
            ...payorLogicProps.data.ocrResult.data,
            fullName: '',
          },
          {
            firstName: values.firstName,
            lastName: values.lastName,
            fullName: '',
            dateOfBirth: values.dateOfBirth ?? null,
            gender: values.gender as Gender,
          },
        );

        const [newResult] = applyRegionalLogicToOcrValidationResult(
          payorLogicProps.data.ocrResult.type,
          result,
          mismatchFields,
        );

        isOcrSuccess = newResult === OcrValidationResult.Match;
      }

      const data: EAppState['payorPersonalInfo'] = {
        ...payorLogicProps.data,
        personalDetails: pickAll(
          Object.keys(payorPersonalDetailsDefaultValue),
          values,
        ),
        contactDetails: pickAll(
          Object.keys(contactDetailsDefaultValue),
          values,
        ),
        addressInfo: pickAll(Object.keys(addressInfoDefaultValue), values),
        nationalityDetails: pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          values,
        ),
        occupationDetails: pickAll(
          Object.keys(payorOccupationDetailsDefaultValue),
          values,
        ),
        isOcrSuccess,
      };
      payorLogicProps.setData(data);
      if (isSaved) {
        await payorLogicProps.onSave(data);
      } else {
        await payorLogicProps.onSubmit(data);
      }
    },
    [
      getValuePayor,
      payorContactDetailsProps,
      payorLogicProps,
      payorPersonalDetailsProps,
    ],
  );

  const isCompletedPayorForm = hasPayor
    ? isPayorValid && Object.keys(payorErrors).length === 0
    : true;

  const isPayorDisabled = !isCompletedPayorForm;
  const isPayorIncomplete = !isCompletedPayorForm;
  useIncompleteSync(isPayorIncomplete, 'appDetail', 'other', 'payor');
  //end payor

  //start primary beneficiary
  const beneficiaryScrollRef = useRef<KeyboardAwareScrollView>(null);
  const beneficiarySubmitRef = useRef<{
    submit: (
      isSaved?: boolean,
    ) => Promise<EAppState['beneficiariesPersonalInfo']>;
    isReadyToSave: () => boolean;
  }>(null);

  const beneficiaryLogicProps = useBeneficiaryInfoLogic('PB');

  const onBeneficiaryContinue = useCallback(
    async (isSaved?: boolean) => {
      const allBeneficiaries = await beneficiarySubmitRef.current?.submit(
        isSaved,
      );
      if (isSaved) {
        await beneficiaryLogicProps.onSave(allBeneficiaries);
      } else {
        await beneficiaryLogicProps.onSubmit(allBeneficiaries);
      }
    },
    [beneficiaryLogicProps],
  );

  useIncompleteSync(
    beneficiaryLogicProps.isCTAPrimaryTypeBtnDisabled,
    'appDetail',
    'other',
    'primaryBeneficiary',
  );

  //end primary beneficiary

  //start secondary beneficiary
  const secondaryBeneficiaryScrollRef = useRef<KeyboardAwareScrollView>(null);
  const secondaryBeneficiarySubmitRef = useRef<{
    submit: (
      isSaved?: boolean,
    ) => Promise<EAppState['beneficiariesPersonalInfo']>;
    isReadyToSave: () => boolean;
  }>(null);

  const secondaryBeneficiaryLogicProps = useBeneficiaryInfoLogic('SB');

  const onSecondaryBeneficiaryContinue = useCallback(
    async (isSaved?: boolean) => {
      const allBeneficiaries =
        await secondaryBeneficiarySubmitRef.current?.submit(isSaved);
      if (isSaved) {
        await secondaryBeneficiaryLogicProps.onSave(allBeneficiaries);
      } else {
        await secondaryBeneficiaryLogicProps.onSubmit(allBeneficiaries);
      }
    },
    [secondaryBeneficiaryLogicProps],
  );

  useIncompleteSync(
    secondaryBeneficiaryLogicProps.isCTASecondaryTypeBtnDisabled,
    'appDetail',
    'other',
    'secondaryBeneficiary',
  );

  //end secondary beneficiary

  const { groups, setActiveItemKey, itemKey } = useEAppProgressBarStore(
    state => ({
      groups: state.groups,
      setActiveItemKey: state.setActiveItemKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  const [loading, setLoading] = useState(false);
  const onContinue = useCallback(async () => {
    try {
      setLoading(true);
      if (itemKey === 'payor') {
        await onPayorContinue();
      } else if (itemKey === 'beneficialOwner') {
        await onBeneficialOwnerContinue();
      } else if (itemKey === 'primaryBeneficiary') {
        await onBeneficiaryContinue();
      } else if (itemKey === 'secondaryBeneficiary') {
        await onSecondaryBeneficiaryContinue();
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  }, [
    itemKey,
    onBeneficialOwnerContinue,
    onBeneficiaryContinue,
    onSecondaryBeneficiaryContinue,
    onPayorContinue,
  ]);

  const latestItemKey = useLatest(itemKey);
  const latestOnPayorContinue = useLatest(onPayorContinue);
  const latestOnBeneficialOwnerContinue = useLatest(onBeneficialOwnerContinue);
  const latestOnBeneficiaryContinue = useLatest(onBeneficiaryContinue);
  const latestOnSecondaryBeneficiaryContinue = useLatest(
    onSecondaryBeneficiaryContinue,
  );

  const onSave = useCallback(async () => {
    try {
      const itemKey = latestItemKey.current;
      setLoading(true);
      if (itemKey === 'payor') {
        await latestOnPayorContinue.current(true);
      } else if (itemKey === 'beneficialOwner') {
        await latestOnBeneficialOwnerContinue.current(true);
      } else if (itemKey === 'primaryBeneficiary') {
        await latestOnBeneficiaryContinue.current(true);
      } else if (itemKey === 'secondaryBeneficiary') {
        await latestOnSecondaryBeneficiaryContinue.current(true);
      }
    } catch (e) {
      console.log(e);
      throw e;
    } finally {
      setLoading(false);
    }
  }, [latestItemKey]);

  const disabled = useMemo(() => {
    if (itemKey === 'payor') return isPayorDisabled;
    if (itemKey === 'beneficialOwner') return isBeneficialOwnerDisabled;
    if (itemKey === 'primaryBeneficiary')
      return beneficiaryLogicProps.isCTAPrimaryTypeBtnDisabled;
    if (itemKey === 'secondaryBeneficiary') {
      return secondaryBeneficiaryLogicProps.isCTASecondaryTypeBtnDisabled;
    }
    return true;
  }, [
    itemKey,
    isBeneficialOwnerDisabled,
    isPayorDisabled,
    beneficiaryLogicProps,
    secondaryBeneficiaryLogicProps,
  ]);

  const step1_2 = groups[0]?.items[1] as ProgressSubgroup;
  const step1_2_3 = step1_2?.items[2];

  const sections = useMemo(() => {
    const sectionData: TabletSectionsProps['items'] = [
      {
        name: 'beneficialOwner',
        title: t('eApp:bar.beneficialOwner'),
        subtitle: beneficialOwnerFullName,
        content: (
          <BeneficialOwnerInfoTablet
            scrollRef={beneficialOwnerScrollRef}
            control={beneficialOwnerFormControl}
            beneficialOwnerLogicProps={beneficialOwnerLogicProps}
            personalDetailsProps={beneficialOwnerPersonalDetailsProps}
            contactDetailsProps={beneficialOwnerContactDetailsProps}
            nationalityDetailsProps={beneficialOwnerNationalityDetailsProps}
            addressInfoProps={beneficialOwnerAddressInfoProps}
          />
        ),
      },
      {
        name: 'payor',
        title: t('eApp:bar.payor'),
        subtitle: payorFullName,
        content: (
          <PayorInfoTablet
            scrollRef={payorScrollRef}
            control={payorFormControl}
            payorLogicProps={payorLogicProps}
            personalDetailsProps={payorPersonalDetailsProps}
            contactDetailsProps={payorContactDetailsProps}
            nationalityDetailsProps={payorNationalityDetailsProps}
            addressInfoProps={payorAddressInfoProps}
            occupationDetailsProps={payorOccupationDetailsProps}
          />
        ),
      },
      {
        name: 'primaryBeneficiary',
        title: t('eApp:bar.beneficiary.primary'),
        content: (
          <BeneficiaryInfo
            scrollRef={beneficiaryScrollRef}
            beneficiaryLogicProps={beneficiaryLogicProps}
            ref={beneficiarySubmitRef}
          />
        ),
      },
    ].filter(Boolean);
    if (!isEntityKeymanEmployee) {
      sectionData.push({
        name: 'secondaryBeneficiary',
        title: t('eApp:bar.beneficiary.secondary'),
        subtitle: 'Optional',
        content: (
          <BeneficiaryInfo
            scrollRef={secondaryBeneficiaryScrollRef}
            beneficiaryLogicProps={secondaryBeneficiaryLogicProps}
            ref={secondaryBeneficiarySubmitRef}
          />
        ),
        disabled: !step1_2_3?.completed,
      });
    }
    return sectionData;
  }, [
    t,
    beneficialOwnerFullName,
    beneficialOwnerFormControl,
    beneficialOwnerLogicProps,
    beneficialOwnerPersonalDetailsProps,
    beneficialOwnerContactDetailsProps,
    beneficialOwnerNationalityDetailsProps,
    beneficialOwnerAddressInfoProps,
    payorFullName,
    payorFormControl,
    payorLogicProps,
    payorPersonalDetailsProps,
    payorContactDetailsProps,
    payorNationalityDetailsProps,
    payorAddressInfoProps,
    payorOccupationDetailsProps,
    beneficiaryLogicProps,
    isEntityKeymanEmployee,
    secondaryBeneficiaryLogicProps,
    step1_2_3?.completed,
  ]);

  const subText = useMemo(() => {
    if (itemKey === 'beneficialOwner') {
      return "Payor's information";
    }
    if (itemKey === 'payor') {
      return 'Primary beneficiary';
    }
    if (itemKey === 'primaryBeneficiary') {
      if (isEntityKeymanEmployee) {
        return 'Health questions';
      }
      return 'Secondary beneficiary';
    }
    if (itemKey === 'secondaryBeneficiary') {
      return 'Health questions';
    }
  }, [isEntityKeymanEmployee, itemKey]);

  useSyncActivePath('appDetail', 'other', 'beneficialOwner');

  const isReadyToSave = useCallback(() => {
    if (itemKey === 'payor') {
      const hasPayorErrors = hasPayor && Object.keys(payorErrors).length > 0;
      if (hasPayorErrors) {
        return false;
      }
      onPayorContinue(true).then();
    } else if (itemKey === 'beneficialOwner') {
      const hasBeneficialOwnerErrors =
        beneficialOwnerLogicProps.hasBeneficialOwner &&
        !beneficialOwnerLogicProps.isEntity
          ? Object.keys(beneficialOwnerErrors).length > 0
          : false;
      if (hasBeneficialOwnerErrors) {
        return false;
      }
      onBeneficialOwnerContinue(true).then();
    } else if (itemKey === 'primaryBeneficiary') {
      const ready = beneficiarySubmitRef.current?.isReadyToSave();
      if (!ready) {
        return false;
      }
      onBeneficiaryContinue(true).then();
    } else if (itemKey === 'secondaryBeneficiary') {
      const ready = secondaryBeneficiarySubmitRef.current?.isReadyToSave();
      if (!ready) {
        return false;
      }
      onSecondaryBeneficiaryContinue(true).then();
    }
    return true;
  }, [
    itemKey,
    hasPayor,
    payorErrors,
    onPayorContinue,
    beneficialOwnerLogicProps.hasBeneficialOwner,
    beneficialOwnerLogicProps.isEntity,
    beneficialOwnerErrors,
    onBeneficialOwnerContinue,
    onBeneficiaryContinue,
    onSecondaryBeneficiaryContinue,
  ]);

  const { set } = useProgressBarContext();

  useEffect(() => {
    set(isReadyToSave, 'appDetail', 'other');
  }, [isReadyToSave, set]);

  return (
    <Fragment>
      <TabletSections
        items={sections}
        activePath={itemKey}
        setActivePath={setActiveItemKey}
        isReadyToSave={isReadyToSave}
      />
      <EAppFooterTablet
        onPrimaryPress={onContinue}
        primaryDisabled={disabled}
        primaryLoading={loading}
        primarySubLabel={subText}
      />
      <OnSaveHandler
        onSave={onSave}
        groupKey="appDetail"
        subGroupKey="other"
        itemKey="*"
      />
    </Fragment>
  );
}

const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));
