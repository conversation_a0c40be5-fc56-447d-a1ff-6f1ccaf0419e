import styled from '@emotion/native';
import { Control, useWatch } from 'react-hook-form';
import { Box, Column, PictogramIcon, Row, TextField } from 'cube-ui-components';
import React, { useEffect } from 'react';
import Input from 'components/Input';
import {
  CityTown,
  Country,
  FundSource,
  InsurancePurpose,
  Nationality,
  PremiumSource,
} from 'types/optionList';
import AutocompletePopup from 'components/AutocompletePopup';
import { PH_COUNTRY } from 'constants/optionList';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import {
  AdditionalDetailsForm,
  additionalDetailsDefaultValue,
  additionalDetailsSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/additionalDetailsValidation';
import { AdditionalDetailsLogic } from 'features/eAppV2/ph/hooks/useAdditionalDetailsLogic';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';

import { StyleSheet } from 'react-native';
import pickAll from 'ramda/src/pickAll';
import SearchableDropdown from 'components/SearchableDropdown';
import { PartyType } from 'types/party';
import useUpdateEffect from 'hooks/useUpdateEffect';
import ReadOnlyField from 'features/eAppV2/common/components/ReadOnlyField';

interface Props {
  control: Control<AdditionalDetailsForm>;
  additionalDetailsProps: AdditionalDetailsLogic;
  shouldHighlight?: boolean;
}

export default function AdditionalDetails(props: Props) {
  const { control, additionalDetailsProps, shouldHighlight } = props;
  const {
    theme,
    t,
    optionList,
    isFetchingOptionList,
    isEntity,
    showPremium,
    sourceOfFundList,
    purposeOfInsuranceList,
    form,
    onDone,
  } = additionalDetailsProps;

  const { space } = theme;

  const isValid = useSchemaValid(
    control,
    additionalDetailsDefaultValue,
    additionalDetailsSchema,
  );

  const { watch, getValues, setValue } = form;

  const purposeOfInsurance = useWatch({ name: 'purposeOfInsurance', control });

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:additionalDetail.title')}
      icon={<PictogramIcon.DocumentReview size={40} />}
      isDone={isValid}>
      <Content>
        <Row gap={space[6]} mt={space[5]}>
          {isEntity ? (
            <ReadOnlyField
              value={
                optionList?.INSURANCE_PURPOSE.options.find(
                  item => item.value === purposeOfInsurance,
                )?.label
              }
              label={t('eApp:additionalDetail.purposeOfInsurance')}
              withRowHasInput
            />
          ) : (
            <Input
              control={control}
              as={AutocompletePopup<InsurancePurpose, string>}
              name="purposeOfInsurance"
              style={styles.flex1}
              label={t('eApp:additionalDetail.purposeOfInsurance')}
              modalTitle={t('eApp:additionalDetail.purposeOfInsurance')}
              data={purposeOfInsuranceList ?? []}
              disabled={isFetchingOptionList}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight) && !value
              }
            />
          )}
          {!isEntity ? (
            <Input
              control={control}
              as={TextField}
              name="otherPurposeOfInsurance"
              label={t('eApp:additionalDetail.otherPurpose')}
              style={styles.flex1}
            />
          ) : (
            <Input
              control={control}
              name="sourceOfFund"
              as={AutocompletePopup<FundSource, string>}
              style={styles.flex1}
              label={t('eApp:additionalDetail.sourceOfFund')}
              modalTitle={t('eApp:additionalDetail.sourceOfFund')}
              data={sourceOfFundList}
              disabled={isFetchingOptionList}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight) && !value
              }
            />
          )}
        </Row>
        <Row gap={space[6]} mt={space[5]}>
          {!isEntity && (
            <Input
              control={control}
              name="sourceOfFund"
              as={AutocompletePopup<FundSource, string>}
              style={styles.flex1}
              label={t('eApp:additionalDetail.sourceOfFund')}
              modalTitle={t('eApp:additionalDetail.sourceOfFund')}
              data={sourceOfFundList}
              disabled={isFetchingOptionList}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight) && !value
              }
            />
          )}
          <Input
            control={control}
            as={TextField}
            label={t('eApp:additionalDetail.otherSource')}
            name="otherSourceOfFund"
            style={styles.flex1}
          />
          {watch('showPremium') && (
            <Input
              control={control}
              name="sourceOfPremium"
              as={AutocompletePopup<PremiumSource, string>}
              style={styles.flex1}
              label={t('eApp:additionalDetail.sourceOfPremium')}
              modalTitle={t('eApp:additionalDetail.sourceOfPremium')}
              data={optionList?.PREMIUM_SOURCE.options ?? []}
              disabled={isFetchingOptionList}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight) && !value
              }
            />
          )}
          {isEntity && !watch('showPremium') && <Box flex={1} />}
        </Row>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
}

const Content = styled(Column)<{ noSectionHeader?: boolean }>(
  ({ theme: { space, colors }, noSectionHeader }) => ({
    backgroundColor: colors.background,
    paddingHorizontal: noSectionHeader ? 0 : space[6],
  }),
);

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
});
