import {
  PolicyOwnerForm,
  policyOwnerFormDefaultValue,
  policyOwnerFormValidationSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/policyOwnerInfoValidation';
import {
  Control,
  useForm,
  UseFormReturn,
  UseFormWatch,
  useWatch,
} from 'react-hook-form';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import TabletSections, {
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSections';
import { useTranslation } from 'react-i18next';
import PolicyOwnerInfoTablet from 'features/eAppV2/ph/components/applicationDetails/ownerAndInsuredInfo/policyOwnerInfo/tablet/PolicyOwnerInfo.tablet';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import usePolicyOwnerInfoLogic from 'features/eAppV2/ph/hooks/usePolicyOwnerInfoLogic';
import pickAll from 'ramda/src/pickAll';
import {
  insuredPersonalDetailsDefaultValue,
  InsuredPersonalDetailsForm,
  policyOwnerPersonalDetailsDefaultValue,
  PolicyOwnerPersonalDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/personalDetailsValidation';
import {
  contactDetailsDefaultValue,
  ContactDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/contactDetailsValidation';
import {
  addressInfoDefaultValue,
  AddressInfoForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import {
  nationalityDetailsDefaultValue,
  NationalityDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import useFullName from 'features/eAppV2/ph/hooks/useFullname';
import usePolicyOwnerPersonalDetailsLogic from 'features/eAppV2/ph/hooks/usePolicyOwnerPersonalDetailsLogic';
import useContactDetailsLogic from 'features/eAppV2/ph/hooks/useContactDetailsLogic';
import useNationalityDetailsLogic from 'features/eAppV2/ph/hooks/useNationalityDetailsLogic';
import useAddressInfoLogic from 'features/eAppV2/ph/hooks/useAddressInfoLogic';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { shallow } from 'zustand/shallow';
import { RouteItemKey } from 'features/eAppV2/common/types/progressBarTypes';
import useSyncActivePath from 'features/eAppV2/ph/hooks/useSyncActivePath';
import {
  industryAffiliationDefaultValue,
  IndustryAffiliationForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/industryAffiliationValidation';
import useIndustryAffiliationLogic from 'features/eAppV2/ph/hooks/useIndustryAffiliationLogic';
import {
  occupationDetailsDefaultValue,
  OccupationDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/occupationDetailsValidation';
import useOccupationDetailsLogic from 'features/eAppV2/ph/hooks/useOccupationDetailsLogic';
import {
  additionalDetailsDefaultValue,
  AdditionalDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/additionalDetailsValidation';
import useAdditionalDetailsLogic from 'features/eAppV2/ph/hooks/useAdditionalDetailsLogic';
import useUSTaxDeclarationLogic from 'features/eAppV2/ph/hooks/useUSTaxDeclarationLogic';
import {
  usTaxDeclarationDefaultValue,
  USTaxDeclarationForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/usTaxDeclarationValidation';
import InsuredInfoTablet from 'features/eAppV2/ph/components/applicationDetails/ownerAndInsuredInfo/insuredInfo/tablet/InsuredInfo.tablet';
import {
  InsuredForm,
  insuredFormDefaultValue,
  insuredFormValidationSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/insuredInfoValidation';
import useInsuredInfoLogic from 'features/eAppV2/ph/hooks/useInsuredInfoLogic';
import useInsuredPersonalDetailsLogic from 'features/eAppV2/ph/hooks/useInsuredPersonalDetailsLogic';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { EAppState } from 'features/eAppV2/common/utils/store/eAppStore';
import {
  EntityForm,
  entityFormDefaultValue,
  entityFormValidationSchema,
} from 'features/eAppV2/ph/validations/applicationDetails/entityInfoValidation';
import {
  EntityDetailsForm,
  entityDetailsDefaultValue,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/entityDetailsValidation';
import {
  AuthorizedRepresentativeDetailsForm,
  authorizedRepresentativeDetailsDefaultValue,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/authorizedRepresentativeDetailsValidation';
import useAuthorizedRepresentativeDetailsLogic from 'features/eAppV2/ph/hooks/useAuthorizedRepresentativeDetailsLogic';
import useEntityDetailsLogic from 'features/eAppV2/ph/hooks/useEntityDetailsLogic';
import OnSaveHandler from '../OnSaveHandler';
import useLatest from 'hooks/useLatest';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import { useProgressBarContext } from 'features/eAppV2/common/components/progressBar/ProgressBarContext';

const emptyValues = {};

export default function OwnerAndInsuredInfo() {
  const { t } = useTranslation(['eApp']);

  // Start of Policy Owner Info
  const policyOwnerScrollRef = useRef<KeyboardAwareScrollView>(null);
  const insuredScrollRef = useRef<KeyboardAwareScrollView>(null);

  const policyOwnerValidationResolver = useEAppValidationResolver(
    policyOwnerFormValidationSchema,
  );

  const entityValidationResolver = useEAppValidationResolver(
    entityFormValidationSchema,
  );

  const policyOwnerLogicProps = usePolicyOwnerInfoLogic();
  const isEntity = policyOwnerLogicProps.isEntity;
  const individualForm = useForm<PolicyOwnerForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...policyOwnerFormDefaultValue,
        ...pickAll(
          Object.keys(policyOwnerPersonalDetailsDefaultValue),
          policyOwnerLogicProps.data.personalDetails,
        ),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          policyOwnerLogicProps.data.contactDetails,
        ),
        ...pickAll(
          Object.keys(addressInfoDefaultValue),
          policyOwnerLogicProps.data.addressInfo,
        ),
        ...pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          policyOwnerLogicProps.data.nationalityDetails,
        ),
        ...pickAll(
          Object.keys(occupationDetailsDefaultValue),
          policyOwnerLogicProps.data.occupationDetails,
        ),
        ...pickAll(
          Object.keys(industryAffiliationDefaultValue),
          policyOwnerLogicProps.data.industryAffiliation,
        ),
        ...pickAll(Object.keys(additionalDetailsDefaultValue), {
          ...policyOwnerLogicProps.data.additionalDetails,
          showPremium: false,
        }),
        ...pickAll(
          Object.keys(usTaxDeclarationDefaultValue),
          policyOwnerLogicProps.data.usTaxDeclaration,
        ),
      }),
      [
        policyOwnerLogicProps.data.additionalDetails,
        policyOwnerLogicProps.data.addressInfo,
        policyOwnerLogicProps.data.contactDetails,
        policyOwnerLogicProps.data.industryAffiliation,
        policyOwnerLogicProps.data.nationalityDetails,
        policyOwnerLogicProps.data.occupationDetails,
        policyOwnerLogicProps.data.personalDetails,
        policyOwnerLogicProps.data.usTaxDeclaration,
      ],
    ),
    resolver: policyOwnerValidationResolver,
  });

  const entityForm = useForm<EntityForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...entityFormDefaultValue,
        ...pickAll(
          Object.keys(entityDetailsDefaultValue),
          policyOwnerLogicProps.data.entityDetails,
        ),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          policyOwnerLogicProps.data.contactDetails,
        ),
        ...pickAll(
          Object.keys(addressInfoDefaultValue),
          policyOwnerLogicProps.data.addressInfo,
        ),
        ...pickAll(
          Object.keys(industryAffiliationDefaultValue),
          policyOwnerLogicProps.data.industryAffiliation,
        ),
        ...pickAll(Object.keys(additionalDetailsDefaultValue), {
          ...policyOwnerLogicProps.data.additionalDetails,
          showPremium: false,
        }),
        ...pickAll(
          Object.keys(usTaxDeclarationDefaultValue),
          policyOwnerLogicProps.data.usTaxDeclaration,
        ),
        ...pickAll(
          Object.keys(authorizedRepresentativeDetailsDefaultValue),
          policyOwnerLogicProps.data.authorizedRepresentativeDetails,
        ),
        isEntity: true,
      }),
      [
        policyOwnerLogicProps.data.additionalDetails,
        policyOwnerLogicProps.data.addressInfo,
        policyOwnerLogicProps.data.contactDetails,
        policyOwnerLogicProps.data.entityDetails,
        policyOwnerLogicProps.data.industryAffiliation,
        policyOwnerLogicProps.data.usTaxDeclaration,
        policyOwnerLogicProps.data.authorizedRepresentativeDetails,
      ],
    ),
    resolver: entityValidationResolver,
  });

  const policyOwnerForm = isEntity ? entityForm : individualForm;

  const {
    control: policyOwnerFormControl,
    watch: policyOwnerFormWatch,
    getValues: getValuePolicyOwner,
    formState: { isValid: isPolicyOwnerValid, errors: policyOwnerErrors },
  } = policyOwnerForm;

  const policyOwnerIncompletenessStatus = useIncompleteFields<
    PolicyOwnerForm | EntityForm
  >({
    control: policyOwnerFormControl as unknown as Control<
      PolicyOwnerForm | EntityForm
    >,
    schema: isEntity
      ? entityFormValidationSchema
      : policyOwnerFormValidationSchema,
    watch: policyOwnerFormWatch as unknown as UseFormWatch<
      PolicyOwnerForm | EntityForm
    >,
    scrollRef: policyOwnerScrollRef,
    scrollTo: ({ y }) =>
      policyOwnerScrollRef.current?.scrollToPosition?.(0, y || 0, true),
  });

  const policyOwnerFullName = useFullName(
    policyOwnerFormControl as unknown as Control<{
      firstName: string;
      lastName: string;
      middleName: string | undefined;
    }>,
  );

  const policyOwnerPersonalDetailsOnDone = useCallback(
    (values: PolicyOwnerPersonalDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          personalDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps.setData],
  );

  const policyOwnerPersonalDetailsProps = usePolicyOwnerPersonalDetailsLogic({
    value: policyOwnerLogicProps.data.personalDetails,
    onDone: policyOwnerPersonalDetailsOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<PolicyOwnerPersonalDetailsForm>,
    partyId: policyOwnerLogicProps.data.id,
  });

  const policyOwnerContactOnDone = useCallback(
    (values: ContactDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          contactDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );
  const policyOwnerContactDetailsProps = useContactDetailsLogic({
    value: policyOwnerLogicProps.data.contactDetails || emptyValues,
    onDone: policyOwnerContactOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<ContactDetailsForm>,
  });

  const policyOwnerNationalityOnDone = useCallback(
    (values: NationalityDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          nationalityDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );
  const policyOwnerNationalityDetailsProps = useNationalityDetailsLogic({
    onDone: policyOwnerNationalityOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<NationalityDetailsForm>,
  });

  const policyOwnerOccupationDetailsOnDone = useCallback(
    (values: OccupationDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          occupationDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );
  const policyOwnerOccupationDetailsProps = useOccupationDetailsLogic({
    value: policyOwnerLogicProps.data.occupationDetails || emptyValues,
    onDone: policyOwnerOccupationDetailsOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<OccupationDetailsForm>,
  });

  const policyOwnerIndustryAffiliationOnDone = useCallback(
    (values: IndustryAffiliationForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          industryAffiliation: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );
  const policyOwnerIndustryAffiliationProps = useIndustryAffiliationLogic({
    onDone: policyOwnerIndustryAffiliationOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<IndustryAffiliationForm>,
  });

  const policyOwnerAdditionalDetailsOnDone = useCallback(
    (values: AdditionalDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          additionalDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );
  const policyOwnerAdditionalDetailsProps = useAdditionalDetailsLogic({
    onDone: policyOwnerAdditionalDetailsOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<AdditionalDetailsForm>,
  });

  const policyOwnerUSTaxDeclarationOnDone = useCallback(
    (values: USTaxDeclarationForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          usTaxDeclaration: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps.setData],
  );
  const policyOwnerUSTaxDeclarationProps = useUSTaxDeclarationLogic({
    onDone: policyOwnerUSTaxDeclarationOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<USTaxDeclarationForm>,
  });

  const policyOwnerAddressOnDone = useCallback(
    (values: AddressInfoForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          addressInfo: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps.setData],
  );
  const policyOwnerAddressInfoProps = useAddressInfoLogic({
    value: policyOwnerLogicProps.data.addressInfo,
    onDone: policyOwnerAddressOnDone,
    form: policyOwnerForm as unknown as UseFormReturn<AddressInfoForm>,
  });

  const policyOwnerRepresentativeOnDone = useCallback(
    (values: AuthorizedRepresentativeDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          authorizedRepresentativeDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );

  const policyOwnerRepresentativeDetailsProps =
    useAuthorizedRepresentativeDetailsLogic({
      onDone: policyOwnerRepresentativeOnDone,
      form: policyOwnerForm as unknown as UseFormReturn<AuthorizedRepresentativeDetailsForm>,
    });

  const policyOwnerEntityOnDone = useCallback(
    (values: EntityDetailsForm) => {
      if (values) {
        policyOwnerLogicProps.setData({
          entityDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [policyOwnerLogicProps],
  );

  const policyOwnerEntityDetailsProps = useEntityDetailsLogic({
    onDone: policyOwnerEntityOnDone,
    value: policyOwnerLogicProps.data.entityDetails,
    form: policyOwnerForm as unknown as UseFormReturn<EntityDetailsForm>,
  });

  const entityName = useWatch({
    control: entityForm.control,
    name: 'entityName',
  });

  const onPolicyOwnerContinue = useCallback(
    async (isSaved?: boolean) => {
      const values = getValuePolicyOwner();
      if (!policyOwnerLogicProps.isEntity) {
        try {
          await policyOwnerPersonalDetailsProps.handleOcrImageDeletion(
            values.document.frontImage,
          );
        } catch (ocrError) {
          if (!isSaved) {
            throw ocrError;
          }
        }
      }
      try {
        const contactValid =
          await policyOwnerContactDetailsProps.validateContact(values);
        if (!contactValid && !isSaved) {
          return;
        }
      } catch (error) {
        if (!isSaved) {
          throw error;
        }
      }
      const data: EAppState['policyOwnerPersonalInfo'] = {
        ...policyOwnerLogicProps.data,
        contactDetails: pickAll(
          Object.keys(contactDetailsDefaultValue),
          values,
        ),
        addressInfo: pickAll(Object.keys(addressInfoDefaultValue), values),
        industryAffiliation: pickAll(
          Object.keys(industryAffiliationDefaultValue),
          values,
        ),
        additionalDetails: pickAll(
          Object.keys(additionalDetailsDefaultValue),
          values,
        ),
        usTaxDeclaration: pickAll(
          Object.keys(usTaxDeclarationDefaultValue),
          values,
        ),
      };

      if (policyOwnerLogicProps.isEntity) {
        data.entityDetails = pickAll(
          Object.keys(entityDetailsDefaultValue),
          values,
        );
        data.authorizedRepresentativeDetails = pickAll(
          Object.keys(authorizedRepresentativeDetailsDefaultValue),
          values,
        );
      } else {
        data.personalDetails = pickAll(
          Object.keys(policyOwnerPersonalDetailsDefaultValue),
          values,
        );
        data.nationalityDetails = pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          values,
        );
        data.occupationDetails = pickAll(
          Object.keys(occupationDetailsDefaultValue),
          values,
        );
      }

      policyOwnerLogicProps.setData(data);
      if (isSaved) {
        await policyOwnerLogicProps.onSave(data);
      } else {
        await policyOwnerLogicProps.onSubmit(data);
      }
    },
    [
      getValuePolicyOwner,
      policyOwnerLogicProps,
      policyOwnerPersonalDetailsProps,
      policyOwnerContactDetailsProps,
    ],
  );

  const isPolicyOwnerDisabled =
    !isPolicyOwnerValid || Object.keys(policyOwnerErrors).length > 0;
  const isPolicyOwnerIncomplete =
    !isPolicyOwnerValid || Object.keys(policyOwnerErrors).length > 0;

  useIncompleteSync(
    isPolicyOwnerIncomplete,
    'appDetail',
    'ownerAndInsured',
    'policyOwner',
  );

  // End of Policy Owner Info

  // Start of Insured Info

  const insuredValidationResolver = useEAppValidationResolver(
    insuredFormValidationSchema,
  );
  const insuredLogicProps = useInsuredInfoLogic();

  const insuredForm = useForm<InsuredForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => ({
        ...insuredFormDefaultValue,
        ...pickAll(
          Object.keys(insuredPersonalDetailsDefaultValue),
          insuredLogicProps.data.personalDetails,
        ),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          insuredLogicProps.data.contactDetails,
        ),
        ...pickAll(
          Object.keys(addressInfoDefaultValue),
          insuredLogicProps.data.addressInfo,
        ),
        ...pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          insuredLogicProps.data.nationalityDetails,
        ),
        ...pickAll(
          Object.keys(occupationDetailsDefaultValue),
          insuredLogicProps.data.occupationDetails,
        ),
      }),
      [
        insuredLogicProps.data.addressInfo,
        insuredLogicProps.data.contactDetails,
        insuredLogicProps.data.nationalityDetails,
        insuredLogicProps.data.occupationDetails,
        insuredLogicProps.data.personalDetails,
      ],
    ),
    resolver: insuredValidationResolver,
  });

  const {
    control: insuredFormControl,
    watch: insuredFormWatch,
    getValues: getValueInsured,
    formState: { isValid: isInsuredValid, errors: insuredErrors },
  } = insuredForm;

  const insuredIncompletenessStatus = useIncompleteFields<InsuredForm>({
    control: insuredFormControl,
    schema: insuredFormValidationSchema,
    watch: insuredFormWatch,
    scrollRef: insuredScrollRef,
    scrollTo: ({ y }) =>
      insuredScrollRef.current?.scrollToPosition?.(0, y || 0, true),
  });

  const insuredFullName = useFullName(
    insuredFormControl as unknown as Control<{
      firstName: string;
      lastName: string;
      middleName: string | undefined;
    }>,
  );

  const insuredPersonalDetailsOnDone = useCallback(
    (values: InsuredPersonalDetailsForm) => {
      if (values) {
        insuredLogicProps.setData({
          personalDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [insuredLogicProps.setData],
  );

  const insuredPersonalDetailsProps = useInsuredPersonalDetailsLogic({
    value: insuredLogicProps.data.personalDetails,
    onDone: insuredPersonalDetailsOnDone,
    form: insuredForm as unknown as UseFormReturn<InsuredPersonalDetailsForm>,
    partyId: insuredLogicProps.data.id,
  });

  const insuredContactOnDone = useCallback(
    (values: ContactDetailsForm) => {
      if (values) {
        insuredLogicProps.setData({
          contactDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [insuredLogicProps],
  );
  const insuredContactDetailsProps = useContactDetailsLogic({
    value: insuredLogicProps.data.contactDetails || emptyValues,
    onDone: insuredContactOnDone,
    form: insuredForm as unknown as UseFormReturn<ContactDetailsForm>,
  });

  const insuredNationalityOnDone = useCallback(
    (values: NationalityDetailsForm) => {
      if (values) {
        insuredLogicProps.setData({
          nationalityDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [insuredLogicProps],
  );
  const insuredNationalityDetailsProps = useNationalityDetailsLogic({
    onDone: insuredNationalityOnDone,
    form: insuredForm as unknown as UseFormReturn<NationalityDetailsForm>,
  });

  const insuredOccupationDetailsOnDone = useCallback(
    (values: OccupationDetailsForm) => {
      if (values) {
        insuredLogicProps.setData({
          occupationDetails: {
            ...values,
            done: true,
          },
        });
      }
    },
    [insuredLogicProps],
  );
  const insuredOccupationDetailsProps = useOccupationDetailsLogic({
    value: insuredLogicProps.data.occupationDetails || emptyValues,
    onDone: insuredOccupationDetailsOnDone,
    form: insuredForm as unknown as UseFormReturn<OccupationDetailsForm>,
  });

  const insuredAddressOnDone = useCallback(
    (values: AddressInfoForm) => {
      if (values) {
        insuredLogicProps.setData({
          addressInfo: {
            ...values,
            done: true,
          },
        });
      }
    },
    [insuredLogicProps.setData],
  );
  const insuredAddressInfoProps = useAddressInfoLogic({
    value: insuredLogicProps.data.addressInfo,
    onDone: insuredAddressOnDone,
    form: insuredForm as unknown as UseFormReturn<AddressInfoForm>,
  });

  const onInsuredContinue = useCallback(
    async (isSaved?: boolean) => {
      const values = getValueInsured();
      if (!insuredLogicProps.isEntity) {
        try {
          await insuredPersonalDetailsProps.handleOcrImageDeletion(
            values.document.frontImage,
          );
        } catch (ocrError) {
          if (!isSaved) {
            throw ocrError;
          }
        }
        try {
          const contactValid = await insuredContactDetailsProps.validateContact(
            pickAll(Object.keys(contactDetailsDefaultValue), values),
          );
          if (!contactValid && !isSaved) {
            return;
          }
        } catch (error) {
          if (!isSaved) {
            throw error;
          }
        }
      }
      const data: EAppState['insuredPersonalInfo'] = {
        ...insuredLogicProps.data,
        personalDetails: pickAll(
          Object.keys(insuredPersonalDetailsDefaultValue),
          values,
        ),
        contactDetails: pickAll(
          Object.keys(contactDetailsDefaultValue),
          values,
        ),
        addressInfo: pickAll(Object.keys(addressInfoDefaultValue), values),
        nationalityDetails: pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          values,
        ),
        occupationDetails: pickAll(
          Object.keys(occupationDetailsDefaultValue),
          values,
        ),
      };
      insuredLogicProps.setData(data);
      if (isSaved) {
        await insuredLogicProps.onSave(data);
      } else {
        await insuredLogicProps.onSubmit(data);
      }
    },
    [
      getValueInsured,
      insuredLogicProps,
      insuredPersonalDetailsProps,
      insuredContactDetailsProps,
    ],
  );

  const isInsuredDisabled =
    !isInsuredValid || Object.keys(insuredErrors).length > 0;

  const isInsuredIncomplete =
    !isInsuredValid || Object.keys(insuredErrors).length > 0;

  useIncompleteSync(
    isInsuredIncomplete,
    'appDetail',
    'ownerAndInsured',
    'insured',
  );

  // End of Insured Info

  const { setActiveItemKey, itemKey } = useEAppProgressBarStore(
    state => ({
      setActiveItemKey: state.setActiveItemKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  const [loading, setLoading] = useState(false);
  const onContinue = useCallback(async () => {
    try {
      setLoading(true);
      if (itemKey === 'policyOwner') {
        await onPolicyOwnerContinue();
      } else if (itemKey === 'insured') {
        await onInsuredContinue();
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  }, [itemKey, onPolicyOwnerContinue, onInsuredContinue]);

  const latestItemKey = useLatest(itemKey);
  const latestOnPolicyOwnerContinue = useLatest(onPolicyOwnerContinue);
  const latestOnInsuredContinue = useLatest(onInsuredContinue);

  const onSave = useCallback(async () => {
    try {
      const itemKey = latestItemKey.current;
      setLoading(true);
      if (itemKey === 'policyOwner') {
        await latestOnPolicyOwnerContinue.current(true);
      } else if (itemKey === 'insured') {
        await latestOnInsuredContinue.current(true);
      }
    } catch (e) {
      console.log(e);
      throw e;
    } finally {
      setLoading(false);
    }
  }, [latestItemKey]);

  const disabled = useMemo(() => {
    if (itemKey === 'policyOwner') return isPolicyOwnerDisabled;
    if (itemKey === 'insured') return isInsuredDisabled;
    return false;
  }, [isInsuredDisabled, isPolicyOwnerDisabled, itemKey]);

  const subText = useMemo(() => {
    if (itemKey === 'policyOwner') {
      return policyOwnerLogicProps.isPIEqualPO
        ? 'Beneficial owner'
        : "Insured's information";
    }
    if (itemKey === 'insured') {
      return 'Beneficial owner';
    }
  }, [itemKey, policyOwnerLogicProps.isPIEqualPO]);

  useSyncActivePath('appDetail', 'ownerAndInsured', 'policyOwner');

  const isReadyToSave = useCallback(() => {
    if (itemKey === 'policyOwner') {
      if (Object.keys(policyOwnerErrors).length > 0) {
        return false;
      }
      onPolicyOwnerContinue(true).then();
    } else if (itemKey === 'insured') {
      if (Object.keys(insuredErrors).length > 0) {
        return false;
      }
      onInsuredContinue(true).then();
    }
    return true;
  }, [
    itemKey,
    policyOwnerErrors,
    onPolicyOwnerContinue,
    insuredErrors,
    onInsuredContinue,
  ]);

  const sections = useMemo(
    () =>
      [
        {
          name: 'policyOwner',
          title: t('eApp:bar.policyOwner'),
          subtitle: isEntity ? entityName : policyOwnerFullName,
          content: (
            <PolicyOwnerInfoTablet
              scrollRef={policyOwnerScrollRef}
              control={
                policyOwnerFormControl as unknown as Control<
                  PolicyOwnerForm | EntityForm
                >
              }
              policyOwnerLogicProps={policyOwnerLogicProps}
              personalDetailsProps={policyOwnerPersonalDetailsProps}
              contactDetailsProps={policyOwnerContactDetailsProps}
              nationalityDetailsProps={policyOwnerNationalityDetailsProps}
              addressInfoProps={policyOwnerAddressInfoProps}
              industryAffiliationProps={policyOwnerIndustryAffiliationProps}
              occupationDetailsProps={policyOwnerOccupationDetailsProps}
              additionalDetailsProps={policyOwnerAdditionalDetailsProps}
              usTaxDeclarationProps={policyOwnerUSTaxDeclarationProps}
              representativeDetailsProps={policyOwnerRepresentativeDetailsProps}
              entityDetailsProps={policyOwnerEntityDetailsProps}
            />
          ),
        },
        !policyOwnerLogicProps.isPIEqualPO
          ? {
              name: 'insured',
              title: t('eApp:bar.insured'),
              subtitle: insuredFullName,
              content: (
                <InsuredInfoTablet
                  scrollRef={insuredScrollRef}
                  control={insuredFormControl}
                  insuredLogicProps={insuredLogicProps}
                  personalDetailsProps={insuredPersonalDetailsProps}
                  contactDetailsProps={insuredContactDetailsProps}
                  nationalityDetailsProps={insuredNationalityDetailsProps}
                  addressInfoProps={insuredAddressInfoProps}
                  occupationDetailsProps={insuredOccupationDetailsProps}
                />
              ),
            }
          : undefined,
      ].filter(Boolean) as TabletSectionsProps['items'],
    [
      insuredAddressInfoProps,
      insuredContactDetailsProps,
      insuredFormControl,
      insuredFullName,
      insuredLogicProps,
      insuredNationalityDetailsProps,
      insuredOccupationDetailsProps,
      insuredPersonalDetailsProps,
      policyOwnerAdditionalDetailsProps,
      policyOwnerAddressInfoProps,
      policyOwnerContactDetailsProps,
      policyOwnerEntityDetailsProps,
      policyOwnerFormControl,
      policyOwnerFullName,
      policyOwnerIndustryAffiliationProps,
      policyOwnerLogicProps,
      policyOwnerNationalityDetailsProps,
      policyOwnerOccupationDetailsProps,
      policyOwnerPersonalDetailsProps,
      policyOwnerRepresentativeDetailsProps,
      policyOwnerUSTaxDeclarationProps,
      t,
      isEntity,
    ],
  );

  const { set } = useProgressBarContext();

  useEffect(() => {
    set(isReadyToSave, 'appDetail', 'ownerAndInsured');
  }, [isReadyToSave, set]);

  return (
    <>
      <TabletSections
        items={sections}
        activePath={itemKey}
        setActivePath={setActiveItemKey}
        isReadyToSave={isReadyToSave}
      />
      <EAppFooterTablet
        onPrimaryPress={onContinue}
        primaryDisabled={disabled}
        primaryLoading={loading}
        primarySubLabel={subText}
        totalIncompleteRequiredFields={
          itemKey === 'policyOwner'
            ? policyOwnerIncompletenessStatus.totalIncompleteRequiredFields
            : itemKey === 'insured'
            ? insuredIncompletenessStatus.totalIncompleteRequiredFields
            : undefined
        }
        focusOnIncompleteField={
          itemKey === 'policyOwner'
            ? policyOwnerIncompletenessStatus.focusOnNextIncompleteField
            : itemKey === 'insured'
            ? insuredIncompletenessStatus.focusOnNextIncompleteField
            : undefined
        }
      />
      <OnSaveHandler
        onSave={onSave}
        groupKey="appDetail"
        subGroupKey="ownerAndInsured"
        itemKey="*"
      />
    </>
  );
}
