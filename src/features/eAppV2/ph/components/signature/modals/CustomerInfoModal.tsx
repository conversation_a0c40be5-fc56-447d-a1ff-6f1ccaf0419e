import { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import styled from '@emotion/native';
import {
  Button,
  DatePicker,
  Icon,
  LargeBody,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { Portal } from '@gorhom/portal';
import { useTheme } from '@emotion/react';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import * as yup from 'yup';
import { useForm, useWatch } from 'react-hook-form';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import Input from 'components/Input/Input';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { getCountryCode } from 'features/eAppV2/ph/components/review/personalInfo/PersonalInfoReview';
import { requiredMessage } from 'features/eAppV2/common/constants/eAppErrorMessages';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { getFullName } from 'features/eAppV2/common/utils/getFullName';

const Container = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  overflow: 'hidden',
  width: '100%',
  maxHeight: 340,
  padding: theme.space[6],
  paddingTop: theme.space[12],
}));

const Header = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onBackground,
  marginBottom: theme.space[3],
}));

const ButtonContainer = styled.View(({ theme }) => ({
  marginTop: theme.space[6],
  width: '100%',
  alignItems: 'center',
}));

const CloseButton = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  right: theme.space[5],
  top: theme.space[6],
  backgroundColor: theme.colors.background,
  width: 32,
  height: 32,
  borderRadius: 16,
  justifyContent: 'center',
  alignItems: 'center',
}));

const Spacing = styled.View(({ theme }) => ({
  width: theme.space[4],
}));

interface Props {
  visible: boolean;
  onConfirm: (values: CustomerInfoFormValues) => void;
  onClose: () => void;
}

export const CustomerInfoModal = memo(function CustomerInfoModal({
  visible,
  onConfirm,
  onClose,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { colors } = useTheme();
  const info = useEAppStore(state => state.policyOwnerPersonalInfo);

  const defaultValues = useMemo(() => {
    const name = getFullName(info.personalDetails);
    return {
      ...formDefaultValues,
      fullName: name && name !== 'N/A' ? name : '',
      email: info.contactDetails?.email || '',
      phoneMobile: info.contactDetails?.primaryMobile || '',
      dateOfBirth: info.personalDetails?.dateOfBirth,
    };
  }, [info]);

  const resolver = useEAppValidationResolver(schema);
  const {
    control,
    handleSubmit,
    trigger,
    setValue,
    getValues,
    formState: { isValid, errors },
  } = useForm<CustomerInfoFormValues>({
    mode: 'onBlur',
    defaultValues,
    resolver,
  });

  const { minDate, defaultDate, maxDate } = getDateOfBirthDropdownProps();

  const countryCode =
    getCountryCode(info.contactDetails?.primaryCountryCode) || '+63';

  const dob = useWatch({
    name: 'dateOfBirth',
    control: control,
  });

  const scrollRef = useRef<ScrollView | null>(null);

  const scrollToTop = useCallback(() => {
    scrollRef.current?.scrollTo({ x: 0, y: 0, animated: true });
  }, []);

  const scrollToSecondRow = useCallback(() => {
    scrollRef.current?.scrollToEnd({ animated: true });
  }, []);

  const keyboardShown = useKeyboardShown();

  useEffect(() => {
    if (!keyboardShown) {
      scrollToTop();
    }
  }, [keyboardShown]);

  const disabled = !isValid;

  const onSubmit = useCallback(() => {
    onConfirm(getValues());
  }, [onConfirm, getValues]);

  return (
    <Portal>
      {visible ? (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.modal}>
          <Container>
            <ScrollView
              ref={scrollRef}
              style={{
                width: '100%',
              }}>
              <Header fontWeight="bold">
                {t('eApp:signature.signatureByCustomer')}
              </Header>
              <Row mt={10}>
                <Input
                  control={control}
                  as={TextField}
                  name="fullName"
                  label={t('eApp:signature.fullName')}
                  style={styles.flex1}
                  onFocus={scrollToTop}
                  disableFullscreenUI
                />
                <Spacing />
                <Input
                  control={control}
                  as={DatePicker}
                  name="dateOfBirth"
                  style={styles.flex1}
                  label={`${t('eApp:personalDetails.dateOfBirth')} (${t(
                    'eApp:signature.optional',
                  ).toLowerCase()})`}
                  modalTitle={t('eApp:personalDetails.dateOfBirth')}
                  value={dob ?? defaultDate}
                  formatDate={() => (dob ? dateFormatUtil(dob) : '')}
                  minDate={minDate}
                  maxDate={maxDate}
                  hint="MM/DD/YYYY"
                />
              </Row>
              <Row mt={22}>
                <Input
                  control={control}
                  left={
                    <LargeBody style={{ color: colors.palette.fwdGreyDarker }}>
                      {countryCode}
                    </LargeBody>
                  }
                  as={TextField}
                  name="phoneMobile"
                  label={t('eApp:signature.mobileNumber')}
                  style={styles.flex1}
                  keyboardType="decimal-pad"
                  onFocus={scrollToSecondRow}
                  disableFullscreenUI
                />
                <Spacing />
                <Input
                  control={control}
                  as={TextField}
                  name="email"
                  label={t('eApp:signature.email')}
                  style={styles.flex1}
                  onFocus={scrollToSecondRow}
                  disableFullscreenUI
                />
              </Row>
              <View style={{ height: 64 * 2 }} />
            </ScrollView>
            <ButtonContainer>
              <Button
                disabled={disabled}
                icon={Icon.ArrowRight}
                text={t('eApp:signature.send')}
                variant="primary"
                onPress={onSubmit}
                compact
                style={styles.button}
                contentStyle={styles.buttonContent}
              />
            </ButtonContainer>
            <CloseButton onPress={onClose}>
              <Icon.Close size={24} fill={colors.onBackground} />
            </CloseButton>
          </Container>
        </Animated.View>
      ) : null}
    </Portal>
  );
});
export default CustomerInfoModal;

const styles = StyleSheet.create({
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 48,
    paddingVertical: 16,
  },
  sv: {
    flex: 1,
  },
  button: {
    height: 44,
    width: 200,
  },
  buttonContent: {
    height: '100%',
    paddingVertical: 0,
  },
  flex1: {
    flex: 1,
  },
});

// insured
export const schema = yup.object({
  fullName: yup.string().required(requiredMessage),
  dateOfBirth: yup
    .date()
    .nullable()
    .test({
      name: 'required-date',
      test: value => value instanceof Date,
      message: requiredMessage,
    }),
  phoneMobile: yup.string().required(requiredMessage),
  email: yup.string().required(requiredMessage),
});
export type CustomerInfoFormValues = yup.InferType<typeof schema>;
export const formDefaultValues: CustomerInfoFormValues = {
  fullName: '',
  dateOfBirth: null,
  phoneMobile: '',
  email: '',
};
