import { useTheme } from '@emotion/react';
import Input from 'components/Input/Input';
import {
  Box,
  Dropdown,
  Icon,
  RadioButton,
  RadioButtonGroup,
  Row,
  Typography,
} from 'cube-ui-components';
import SearchableDropdown from 'components/SearchableDropdown';
import { ACRForm } from 'features/eAppV2/ph/validations/acrValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React from 'react';
import { Control, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  AcrIndustryAffiliation1,
  AcrIndustryAffiliation2,
} from 'types/optionList';
import AdditionalInformationContainer from '../common/AdditionalInformationContainer';
import { Divider } from '../common/Divider';
import GovernmentPositionPicker from '../governmentPositions/GovernmentPositionsPicker';
import CheckBoxList from 'components/CheckBoxList';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import AutocompletePopup from 'components/AutocompletePopup';
import GovernmentPositionPickerTablet from 'features/eAppV2/ph/components/acr/governmentPositions/GovernmentPositionsPicker.tablet';

interface Props {
  control: Control<ACRForm>;
}

export default function Part2Tablet({ control }: Props) {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();
  const watch = useWatch<ACRForm>({ control });

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box mt={theme.space[4]} borderRadius={theme.sizes[4]} overflow={'hidden'}>
      <Box
        bgColor={theme.colors.palette.black}
        px={theme.space[6]}
        py={theme.space[3]}>
        <Typography.LargeBody
          fontWeight={'bold'}
          color={theme.colors.palette.white}>
          {t('eApp:acr.part2')}
        </Typography.LargeBody>
      </Box>
      <Box backgroundColor={theme.colors.background} padding={theme.space[6]}>
        <Row alignItems={'center'} mb={theme.space[6]}>
          <Icon.Document
            fill={theme.colors.onBackground}
            size={theme.sizes[5]}
          />
          <Typography.LargeBody
            fontWeight="bold"
            style={{ flex: 1, marginLeft: theme.space[1] }}>
            {t('eApp:acr.part2.politic.title')}
          </Typography.LargeBody>
        </Row>
        <Typography.LargeBody>
          {t('eApp:acr.part2.isPEPorRCA')}
        </Typography.LargeBody>
        <Row mt={theme.space[5]} columnGap={theme.space[8]}>
          <Input control={control} name="isPEPorRCA" as={RadioButtonGroup}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButton value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        <AdditionalInformationContainer active={watch.isPEPorRCA === 'yes'}>
          <Box my={theme.space[5]}>
            <Typography.LargeBody>
              {t('eApp:acr.part2.governmentPosition.question')}
            </Typography.LargeBody>
          </Box>
          <Input
            as={GovernmentPositionPickerTablet}
            control={control}
            name="governmentPosition"
          />
        </AdditionalInformationContainer>
        {/*Divider*/}
        <Box
          alignSelf="stretch"
          h={1}
          backgroundColor={theme.colors.palette.fwdGrey[100]}
          my={theme.space[6]}
        />
        {/*Divider*/}
        <Row alignItems="center" mb={theme.space[6]}>
          <Icon.Business
            fill={theme.colors.onBackground}
            size={theme.sizes[5]}
          />
          <Typography.LargeLabel
            fontWeight="bold"
            style={{ marginLeft: theme.space[1] }}>
            {t('eApp:acr.part2.industryAffiliation.title')}
          </Typography.LargeLabel>
        </Row>
        <Typography.LargeBody>
          {t('eApp:acr.part2.isAffiliatedWithRestrictedEntities')}
        </Typography.LargeBody>
        <Row mt={theme.space[5]} columnGap={theme.space[8]}>
          <Input
            control={control}
            name="isAffiliatedWithRestrictedEntities"
            as={RadioButtonGroup}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButton value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        <AdditionalInformationContainer
          active={watch.isAffiliatedWithRestrictedEntities === 'yes'}>
          <Box mt={theme.space[5]}>
            <Typography.LargeBody>
              {t('eApp:acr.part2.identifyIndustry')}
            </Typography.LargeBody>
            <Input
              as={CheckBoxList<AcrIndustryAffiliation1, string>}
              control={control}
              name={'restrictedEntity'}
              disabled={isFetchingOptionList}
              data={optionList?.ACR_INDUSTRYAFFILIATION_1.options ?? []}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              label={t('eApp:acr.part2.industry')}
              modalTitle={t('eApp:acr.part2.industry')}
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                marginTop: theme.space[1],
              }}
              itemStyle={{ width: '50%', marginTop: theme.space[4] }}
            />
          </Box>
        </AdditionalInformationContainer>
        {/*Divider*/}
        <Box
          alignSelf="stretch"
          h={1}
          backgroundColor={theme.colors.palette.fwdGrey[100]}
          my={theme.space[6]}
        />
        {/*Divider*/}
        <Typography.LargeBody>
          {t('eApp:acr.part2.isAffiliatedWithRestrictedIndustries')}
        </Typography.LargeBody>
        <Row mt={theme.space[5]} columnGap={theme.space[8]}>
          <Input
            control={control}
            name="isAffiliatedWithRestrictedIndustries"
            as={RadioButtonGroup}>
            <RadioButton value="yes" label={t('eApp:yes')} />
            <RadioButton value="no" label={t('eApp:no')} />
          </Input>
        </Row>
        <AdditionalInformationContainer
          active={watch.isAffiliatedWithRestrictedIndustries === 'yes'}>
          <Box mt={theme.space[5]}>
            <Typography.LargeBody>
              {t('eApp:acr.part2.identifyIndustry')}
            </Typography.LargeBody>
            <Input
              as={CheckBoxList<AcrIndustryAffiliation2, string>}
              control={control}
              name={'restrictedIndustry'}
              data={optionList?.ACR_INDUSTRYAFFILIATION_2.options ?? []}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              label={t('eApp:acr.part2.industry')}
              modalTitle={t('eApp:acr.part2.industry')}
              disabled={isFetchingOptionList}
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                marginTop: theme.space[1],
              }}
              itemStyle={{ width: '50%', marginTop: theme.space[4] }}
            />
          </Box>
        </AdditionalInformationContainer>
      </Box>
    </Box>
  );
}
