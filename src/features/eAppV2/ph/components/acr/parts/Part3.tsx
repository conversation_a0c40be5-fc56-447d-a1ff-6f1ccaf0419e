import { useTheme } from '@emotion/react';
import Input from 'components/Input/Input';
import {
  Box,
  Icon,
  RadioButton,
  RadioButtonGroup,
  Row,
  Typography,
} from 'cube-ui-components';
import { ACRForm } from 'features/eAppV2/ph/validations/acrValidation';
import React from 'react';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Divider } from '../common/Divider';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface Props {
  control: Control<ACRForm>;
}

export default function Part3({ control }: Props) {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box px={theme.space[isNarrowScreen ? 3 : 4]} pt={theme.space[7]}>
      <Typography.H6 fontWeight="bold">{t('eApp:acr.part3')}</Typography.H6>
      <Box
        backgroundColor={theme.colors.background}
        padding={theme.space[3]}
        mt={theme.space[4]}
        borderRadius={theme.sizes[2]}>
        <Row>
          <Icon.Document
            fill={theme.colors.onBackground}
            size={theme.sizes[5]}
          />
          <Typography.LargeBody
            fontWeight="bold"
            style={{ flex: 1, marginLeft: theme.space[1] }}>
            {t('eApp:acr.part3.title')}
          </Typography.LargeBody>
        </Row>
        <Typography.LargeBody style={{ marginTop: theme.space[4] }}>
          {t('eApp:acr.part3.existingInsuranceEnforceChange')}
        </Typography.LargeBody>
        <Row mt={theme.space[2]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'existingInsuranceEnforceChange'}>
            <RadioButton
              value="yes"
              label={t('eApp:yes')}
              style={{ flex: 1 }}
            />
            <RadioButton value="no" label={t('eApp:no')} style={{ flex: 1 }} />
          </Input>
        </Row>
        <Divider />
        <Typography.LargeBody>
          {t('eApp:acr.part3.reinstatableLapsedInsuranceChange')}
        </Typography.LargeBody>
        <Row mt={theme.space[2]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'reinstatableLapsedInsuranceChange'}>
            <RadioButton
              value="yes"
              label={t('eApp:yes')}
              style={{ flex: 1 }}
            />
            <RadioButton value="no" label={t('eApp:no')} style={{ flex: 1 }} />
          </Input>
        </Row>
        <Divider />
        <Typography.LargeBody>
          {t('eApp:acr.part3.policyLoanPremiumPayment')}
        </Typography.LargeBody>
        <Row mt={theme.space[2]}>
          <Input
            control={control}
            as={RadioButtonGroup}
            name={'policyLoanPremiumPayment'}>
            <RadioButton
              value="yes"
              label={t('eApp:yes')}
              style={{ flex: 1 }}
            />
            <RadioButton value="no" label={t('eApp:no')} style={{ flex: 1 }} />
          </Input>
        </Row>
      </Box>
    </Box>
  );
}
