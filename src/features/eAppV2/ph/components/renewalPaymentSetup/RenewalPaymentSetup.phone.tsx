import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  H6,
  H7,
  Icon,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import {
  RenewalPaymentSetupForm,
  renewalPaymentSetupSchema,
} from 'features/eAppV2/ph/validations/renewalPaymentSetupValidation';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import { Fragment, memo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { shallow } from 'zustand/shallow';
import useRenewalPaymentSetupLogic from '../../hooks/useRenewalPaymentSetupLogic';
import { AutoCreditArrangementPhone } from './autoCreditArrangement/AutoCreditArrangement.phone';
import { AutoDirectDebitArrangementPhone } from './autoDirectDebitArrangement/AutoDirectDebitArrangement.phone';
import EAppFooterPhone from 'features/eAppV2/common/components/footer/EAppFooter.phone';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import OnSaveHandler from '../applicationDetails/OnSaveHandler';

export const extraFormMethods = ['aca', 'adda'];

const RenewalPaymentSetupPhone = memo(function RenewalPaymentSetupPhone() {
  const { t } = useTranslation(['eApp']);
  const { colors, space, sizes } = useTheme();
  const [showAllPaymentMethod, setShowAllPaymentMethod] = useState(true);
  const { renewalPaymentSetup } = useEAppStore(
    state => ({
      hasBankDetails: state.hasBankDetails,
      renewalPaymentSetup: state.renewalPaymentSetup,
    }),
    shallow,
  );

  const resolver = useEAppValidationResolver(renewalPaymentSetupSchema);
  const {
    watch,
    setValue,
    getValues,
    handleSubmit,
    control,
    trigger,
    formState: { isValid },
  } = useForm<RenewalPaymentSetupForm>({
    mode: 'onBlur',
    defaultValues: renewalPaymentSetup,
    resolver,
  });

  const {
    paymentMethodList,
    isGettingCase,
    isSavingApplication,
    onSubmit,
    isNarrowScreen,
    onSave,
  } = useRenewalPaymentSetupLogic({ watch, handleSubmit, setValue, getValues });

  const scrollViewRef = useRef<KeyboardAwareScrollView>(null);
  const keyboardShown = useKeyboardShown();

  const incomplete = !isValid;
  useIncompleteSync(
    incomplete,
    'renewalPaymentSetup',
    undefined,
    'renewalPayment',
  );

  return (
    <Box flex={1} backgroundColor={colors.surface}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        keyboardDismissMode="on-drag"
        extraScrollHeight={sizes[13]}>
        <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
          <H6 fontWeight="bold">
            {t('eApp:paymentSetup.renewalPremiumPayment')}
          </H6>
          <Box
            bgColor={colors.background}
            paddingX={16}
            borderRadius={12}
            mt={20}>
            <TouchableOpacity
              style={{ paddingVertical: space[4] }}
              onPress={() => setShowAllPaymentMethod(!showAllPaymentMethod)}
              disabled={!extraFormMethods.includes(watch('paymentMethod'))}>
              <Row alignItems={'center'} justifyContent="space-between">
                <H7 fontWeight="bold">
                  {t('eApp:paymentSetup.renewPaymentMethod')}
                </H7>
                {extraFormMethods.includes(watch('paymentMethod')) &&
                  (showAllPaymentMethod ? (
                    <Icon.ChevronUp />
                  ) : (
                    <Icon.ChevronDown />
                  ))}
              </Row>
            </TouchableOpacity>
            <Input control={control} as={RadioButtonGroup} name="paymentMethod">
              {paymentMethodList.map((item, index) => {
                if (
                  watch('paymentMethod') &&
                  watch('paymentMethod') !== item.value &&
                  (watch('paymentMethod') === 'aca' ||
                    watch('paymentMethod') === 'adda') &&
                  !showAllPaymentMethod
                ) {
                  return <Fragment />;
                }
                return (
                  <RadioButton
                    value={item.value}
                    label={item.label}
                    style={{ marginBottom: space[4] }}
                    key={index}
                    onSelect={() => {
                      setShowAllPaymentMethod(false);
                    }}
                  />
                );
              })}
            </Input>
          </Box>
          {watch('paymentMethod') === 'aca' && (
            <AutoCreditArrangementPhone
              watch={watch}
              control={control}
              trigger={trigger}
              scrollViewRef={scrollViewRef}
            />
          )}
          {watch('paymentMethod') === 'adda' && (
            <AutoDirectDebitArrangementPhone
              watch={watch}
              setValue={setValue}
              control={control}
              trigger={trigger}
            />
          )}
        </Box>
      </KeyboardAwareScrollView>
      {!keyboardShown && (
        <EAppFooterPhone
          primaryDisabled={!isValid || isGettingCase}
          primaryLoading={isSavingApplication}
          onPrimaryPress={onSubmit}
        />
      )}
      <OnSaveHandler
        onSave={onSave}
        groupKey="renewalPaymentSetup"
        itemKey="renewalPayment"
      />
    </Box>
  );
});

export default RenewalPaymentSetupPhone;
