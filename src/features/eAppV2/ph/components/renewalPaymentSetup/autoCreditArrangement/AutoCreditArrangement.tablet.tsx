import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import Input from 'components/Input';
import {
  Box,
  Checkbox,
  Column,
  H6,
  H7,
  Label,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  RenewalPaymentSetupForm,
  creditBankACA,
  prefixCreditCardACA,
} from 'features/eAppV2/ph/validations/renewalPaymentSetupValidation';
import { RefObject } from 'react';
import {
  Control,
  UseFormTrigger,
  UseFormWatch,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { CreditCardCompany } from 'types/optionList';
import useAutoCreditArrangementLogic from '../../../hooks/useAutoCreditArrangementLogic';

export const AutoCreditArrangement = ({
  watch,
  control,
  trigger,
  scrollViewRef: scrollRef,
}: {
  watch: UseFormWatch<RenewalPaymentSetupForm>;
  control: Control<RenewalPaymentSetupForm>;
  trigger: UseFormTrigger<RenewalPaymentSetupForm>;
  scrollViewRef: RefObject<KeyboardAwareScrollView>;
}) => {
  const { t } = useTranslation(['eApp']);
  const { colors, space, borderRadius } = useTheme();

  const creditCardCompany = useWatch({
    name: 'creditCardCompany',
    control: control,
  });

  const { optionList, isFetchingOptionList } = useAutoCreditArrangementLogic({
    control,
    trigger,
  });

  return (
    <>
      <Wrapper>
        <TitleContainer>
          <H6 fontWeight="bold" style={{ flex: 1 }}>
            {t('eApp:paymentSetup.autoCreditArrangement.creditCardHolderInfo')}
          </H6>
        </TitleContainer>
        <Column
          backgroundColor={colors.background}
          padding={space[6]}
          borderRadius={borderRadius.large}>
          <Row gap={space[6]} flex={1}>
            <Input
              control={control}
              as={TextField}
              name="creditCardHolderName"
              label={t(
                'eApp:paymentSetup.autoCreditArrangement.cardHolderName',
              )}
              style={styles.input}
            />
            <Input
              control={control}
              as={Autocomplete<CreditCardCompany, string>}
              name="creditCardCompany"
              label={t('eApp:paymentSetup.autoCreditArrangement.cardCompany')}
              disabled={isFetchingOptionList}
              data={optionList?.CREDIT_CARD_COMPANY_LIST?.options ?? []}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              style={styles.input}
            />
          </Row>
          <Box h={space[6]} />
          <Row gap={space[6]}>
            <Input
              control={control}
              as={TextField}
              name="creditCardNumber"
              label={t('eApp:paymentSetup.autoCreditArrangement.cardNumber')}
              style={styles.input}
              keyboardType={'numeric'}
              left={
                creditCardCompany === creditBankACA ? (
                  <LargeBody>{prefixCreditCardACA}</LargeBody>
                ) : undefined
              }
            />
            <Input
              control={control}
              as={TextField}
              name="creditExpiryDate"
              label={t('eApp:paymentSetup.autoCreditArrangement.expiryDate')}
              style={styles.input}
              keyboardType={'numeric'}
              maxLength={5}
            />
          </Row>
          <Box h={space[6]} />
          <H7 fontWeight="bold">
            {t(
              'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.question',
            )}
          </H7>
          <Box h={space[6]} />
          <Input
            control={control}
            as={RadioButtonGroup}
            name="creditIsIssuedBySecBankCorp">
            <Row>
              <RadioButton
                value="yes"
                label={t(
                  'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.yes',
                )}
              />
              <Box width={40} />
              <RadioButton
                value="no"
                label={t(
                  'eApp:paymentSetup.autoCreditArrangement.issuedBySecurityBank.no',
                )}
              />
            </Row>
          </Input>
        </Column>
      </Wrapper>
      <Wrapper>
        <Column
          backgroundColor={colors.background}
          padding={space[6]}
          borderRadius={borderRadius.large}>
          <Input
            control={control}
            as={Checkbox}
            name="creditAcceptDisclaimer"
            label={t(
              'eApp:paymentSetup.autoCreditArrangement.disclaimer.question',
            )}
            style={{
              alignItems: 'flex-start',
            }}
            labelStyle={{
              marginTop: -space[1],
              flex: 1,
            }}
          />
          <Box height={space[5]} />
          <Label>{t('eApp:paymentSetup.autoCreditArrangement.fullNote')}</Label>
        </Column>
      </Wrapper>
    </>
  );
};

const styles = StyleSheet.create({
  input: {
    flex: 1,
  },
});

const Wrapper = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    marginBottom: space[5],
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    overflow: 'hidden',
  }),
);

const TitleContainer = styled(Row)(
  ({ theme: { space, colors, borderRadius } }) => ({
    gap: space[1],
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: space[6],
    paddingTop: space[6],
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
  }),
);
