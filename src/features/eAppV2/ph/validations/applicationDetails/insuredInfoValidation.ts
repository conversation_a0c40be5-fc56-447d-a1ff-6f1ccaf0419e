import * as yup from 'yup';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
} from './sections/contactDetailsValidation';
import {
  addressInfoDefaultValue,
  addressInfoSchema,
} from './sections/addressInfoValidation';
import {
  nationalityDetailsDefaultValue,
  nationalityDetailsSchema,
} from './sections/nationalityDetailsValidation';
import {
  occupationDetailsSchema,
  otherOccupationDetailsDefaultValue,
} from './sections/occupationDetailsValidation';
import {
  insuredPersonalDetailsDefaultValue,
  insuredPersonalDetailsSchema,
} from './sections/personalDetailsValidation';

export const insuredFormValidationSchema = insuredPersonalDetailsSchema
  .concat(contactDetailsSchema)
  .concat(addressInfoSchema)
  .concat(nationalityDetailsSchema)
  .concat(occupationDetailsSchema);

export type InsuredForm = yup.InferType<typeof insuredFormValidationSchema>;

export const insuredFormDefaultValue: InsuredForm = {
  ...insuredPersonalDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...addressInfoDefaultValue,
  ...nationalityDetailsDefaultValue,
  ...otherOccupationDetailsDefaultValue,
};
