import * as yup from 'yup';
import {
  invalidFormatMessage,
  maxLength120Message,
  requiredMessage,
} from '../../../../common/constants/eAppErrorMessages';

export const maxNameLength = 120;

// policy owner
export const entityDetailsSchema = yup.object({
  entityName: yup
    .string()
    .max(maxNameLength, maxLength120Message)
    .required(requiredMessage)
    .validateCompanyName(invalidFormatMessage),
  businessNature: yup.string().required(requiredMessage),
  leadSource: yup.string().required(requiredMessage),
});
export type EntityDetailsForm = yup.InferType<typeof entityDetailsSchema>;
export const entityDetailsDefaultValue: EntityDetailsForm = {
  entityName: '',
  businessNature: '',
  leadSource: '',
};
