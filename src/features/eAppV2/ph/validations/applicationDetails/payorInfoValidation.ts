import * as yup from 'yup';
import {
  payorPersonalDetailsDefaultValue,
  payorPersonalDetailsSchema,
} from './sections/personalDetailsValidation';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
} from './sections/contactDetailsValidation';
import {
  addressInfoDefaultValue,
  addressInfoSchema,
} from './sections/addressInfoValidation';
import {
  nationalityDetailsDefaultValue,
  nationalityDetailsSchema,
} from './sections/nationalityDetailsValidation';
import {
  occupationDetailsSchema,
  payorOccupationDetailsDefaultValue,
} from './sections/occupationDetailsValidation';

export const payorFormValidationSchema = payorPersonalDetailsSchema
  .concat(contactDetailsSchema)
  .concat(addressInfoSchema)
  .concat(nationalityDetailsSchema)
  .concat(occupationDetailsSchema);

export type PayorForm = yup.InferType<typeof payorFormValidationSchema>;

export const payorFormDefaultValue: PayorForm = {
  ...payorPersonalDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...addressInfoDefaultValue,
  ...nationalityDetailsDefaultValue,
  ...payorOccupationDetailsDefaultValue,
};
