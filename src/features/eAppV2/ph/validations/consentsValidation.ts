import * as yup from 'yup';
import { requiredMessage } from '../../common/constants/eAppErrorMessages';

export const consentsSchema = yup.object({
  declaration: yup.object({
    hasExistingInsuranceInForce: yup.boolean().nullable().defined(),
    hasLapsedPolicyToReinstate: yup.boolean().nullable().defined(),
    willReplaceLifeInsurance: yup.boolean().nullable().defined(),
    willPayPremiumsByLoanOrSurrender: yup.boolean().nullable().defined(),
    confirmCheck: yup.boolean().nullable().defined(),
  }),
  dataPrivacyAndConsents: yup.object({
    acceptDataPrivacy: yup.boolean().oneOf([true]),
    acceptInform: yup.boolean().oneOf([true]),
  }),
});

export type ConsentsForm = yup.InferType<typeof consentsSchema>;

export const consentsDefaultValue: ConsentsForm = {
  declaration: {
    hasExistingInsuranceInForce: null,
    hasLapsedPolicyToReinstate: null,
    willReplaceLifeInsurance: null,
    willPayPremiumsByLoanOrSurrender: null,
    confirmCheck: null,
  },
  dataPrivacyAndConsents: {
    acceptDataPrivacy: false,
    acceptInform: false,
  },
};

export type ExistingPolicyForm = {
  companyName: string;
  policyNumber: string;
  amountOfCoverage: string;
};

export const existingPolicySchema = yup.object({
  policies: yup.array(
    yup.object({
      companyName: yup.string().required(requiredMessage),
      policyNumber: yup.string().required(requiredMessage),
      amountOfCoverage: yup.string().required(requiredMessage),
    }),
  ),
});

export type ExistingPoliciesForm = yup.InferType<typeof existingPolicySchema>;

export const existingPolicyDefaultValue: ExistingPoliciesForm = {
  policies: [],
};
