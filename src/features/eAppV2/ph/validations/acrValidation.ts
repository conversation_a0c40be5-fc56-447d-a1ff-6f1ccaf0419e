import * as yup from 'yup';
import { emptySpace, requiredMessage } from '../../common/constants/eAppErrorMessages';

export const acrValidationSchema = yup.object({
  relatedToOwner: yup.string().required(requiredMessage),
  relationship: yup.string().when('relatedToOwner', {
    is: 'yes',
    then: schema => schema.required(requiredMessage),
    otherwise: schema => schema,
  }),
  isRelatedToFWP_FSC: yup.string().required(requiredMessage),
  isPEPorRCA: yup.string().required(requiredMessage),
  governmentPosition: yup
    .object({
      label: yup.string(),
      value: yup.string(),
    })
    .optional()
    .when('isPEPorRCA', {
      is: 'yes',
      then: schema => schema.required(requiredMessage),
      otherwise: schema => schema,
    }),
  isAffiliatedWithRestrictedEntities: yup.string().required(requiredMessage),
  isAffiliatedWithRestrictedIndustries: yup.string().required(requiredMessage),
  restrictedEntity: yup
    .array()
    .of(yup.string())
    .when('isAffiliatedWithRestrictedEntities', {
      is: 'yes',
      then: schema => schema.min(1).required(requiredMessage),
      otherwise: schema => schema,
    }),
  restrictedIndustry: yup
    .array()
    .of(yup.string())
    .when('isAffiliatedWithRestrictedIndustries', {
      is: 'yes',
      then: schema => schema.min(1).required(requiredMessage),
      otherwise: schema => schema,
    }),
  existingInsuranceEnforceChange: yup.string().required(requiredMessage),
  reinstatableLapsedInsuranceChange: yup.string().required(requiredMessage),
  policyLoanPremiumPayment: yup.string().required(requiredMessage),
  policyOwnerBusinessInfo: yup
    .string()
    .required(requiredMessage)
    .test({
      name: 'space-test',
      test: value => {
        if (!value) return true;
        const trimmed = value.trim();
        return trimmed.length !== 0;
      },
      message: emptySpace,
    }),
  policyOwnerFamilyInfo: yup
    .string()
    .required(requiredMessage)
    .test({
      name: 'space-test',
      test: value => {
        if (!value) return true;
        const trimmed = value.trim();
        return trimmed.length !== 0;
      },
      message: emptySpace,
    }),
  otherSpecialRemarks: yup
    .string()
    .required(requiredMessage)
    .test({
      name: 'space-test',
      test: value => {
        if (!value) return true;
        const trimmed = value.trim();
        return trimmed.length !== 0;
      },
      message: emptySpace,
    }),
  secondaryFWPCode: yup.string(),
  secondaryFWPName: yup.string(),
  acceptDeclaration: yup.boolean().oneOf([true]),
});

export type ACRForm = yup.InferType<typeof acrValidationSchema>;

export const acrFormInitialValues: ACRForm = {
  relatedToOwner: '',
  relationship: undefined,
  isRelatedToFWP_FSC: '',
  isPEPorRCA: '',
  governmentPosition: undefined,
  isAffiliatedWithRestrictedEntities: '',
  isAffiliatedWithRestrictedIndustries: '',
  restrictedEntity: undefined,
  restrictedIndustry: undefined,
  existingInsuranceEnforceChange: '',
  reinstatableLapsedInsuranceChange: '',
  policyLoanPremiumPayment: '',
  policyOwnerBusinessInfo: '',
  policyOwnerFamilyInfo: '',
  otherSpecialRemarks: '',
  secondaryFWPCode: '',
  secondaryFWPName: '',
  acceptDeclaration: false,
};
