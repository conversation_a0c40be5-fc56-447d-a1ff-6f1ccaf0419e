import { format } from 'date-fns';
import {
  EAppState,
  EAppStore,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import {
  applyRegionalLogicToOcrValidationResult,
  MismatchFields,
  OcrValidationResult,
  validateOcr,
} from 'features/eAppV2/common/utils/validateOcr';
import { useLACustomerLookup } from 'hooks/useLACustomerLookup';
import { useRef, useState } from 'react';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { LACustomer } from 'types/customer';
import { OcrResult } from 'types/ocr';
import { PartyRole } from 'types/party';
import { Gender } from 'types/person';
import { shallow } from 'zustand/shallow';
import { OcrData } from '../../../../components/Ocr/Ocr';
import useOcrPopulatingLogic, { OCRForm } from './useOcrPopulatingLogic';
import useBoundStore from 'hooks/useBoundStore';
import UpdateSTPFieldConfirmationDialog from '../components/applicationDetails/sections/personalDetails/modals/UpdateSTPFieldConfirmationDialog';
import { Keyboard, TouchableOpacity } from 'react-native';
import OcrValidationErrorDialog from '../../common/components/ocr/OcrValidationErrorDialog';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types/navigation';
import LAVerificationDialog from '../components/applicationDetails/sections/personalDetails/modals/LAVerificationDialog';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { LargeLabel } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import OcrValidationErrorDialogButton from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialogButton';

const useOcrLogic = ({
  role,
  updatePersonalInfo,
  personalInfo,
  ocrImage,
  setValue,
  getValues,
  onClose,
}: (
  | {
      role: PartyRole.PROPOSER;
      updatePersonalInfo: EAppStore['updatePolicyOwnerPersonalInfo'];
      personalInfo: EAppState['policyOwnerPersonalInfo'];
    }
  | {
      role: PartyRole.INSURED;
      updatePersonalInfo: EAppStore['updateInsuredPersonalInfo'];
      personalInfo: EAppState['insuredPersonalInfo'];
    }
) & {
  ocrImage: {
    base64?: string;
    name?: string;
    thumbnail?: string;
  };
  setValue: UseFormSetValue<OCRForm>;
  getValues: UseFormGetValues<OCRForm>;
  onClose?: () => void;
}) => {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const ocrScanned = useRef(Boolean(ocrImage?.name));

  const policyOwnerStore = useEAppStore(
    state => ({
      isPIEqualPO: state.isPIEqualPO,
      setSTP: state.setPolicyOwnerSTP,
      isOcrSuccess: state.policyOwnerPersonalInfo.isOcrSuccess,
      setOCRSuccess: state.setPolicyOwnerOCRSuccess,
    }),
    shallow,
  );

  const insuredStore = useEAppStore(
    state => ({
      isPIEqualPO: state.isPIEqualPO,
      setSTP: state.setInsuredSTP,
      isOcrSuccess: state.insuredPersonalInfo.isOcrSuccess,
      setOCRSuccess: state.setInsuredOCRSuccess,
    }),
    shallow,
  );

  const store = role === PartyRole.PROPOSER ? policyOwnerStore : insuredStore;
  const { setSTP, isOcrSuccess, setOCRSuccess, isPIEqualPO } = store;

  const [ocrValidationResult, setOcrValidationResult] =
    useState<OcrValidationResult>(OcrValidationResult.Match);
  const [ocrExtractionData, setOcrExtractionData] = useState<OcrData>({
    data: {},
    documentType: '',
    image: { base64: '', name: '' },
  });
  const [ocrValidationMismatchFields, setOcrValidationMismatchFields] =
    useState<MismatchFields>({});
  const [ocrValidationErrorDialogVisible, setOcrValidationErrorDialogVisible] =
    useState(false);
  const [
    updateSTPFieldConfirmationDialogVisible,
    setUpdateSTPFieldConfirmationDialogVisible,
  ] = useState(false);
  const [isVerifyingCustomer, setIsVerifyingCustomer] = useState(false);
  const [laVerificationVisible, setLaVerificationVisible] = useState(false);
  const [laCustomer, setLaCustomer] = useState<LACustomer>();

  const { mutateAsync: lookup, isLoading: isSearchingCustomer } =
    useLACustomerLookup();

  const {
    populateOcrData,
    onDelete: onDeleteCb,
    ocrRef,
  } = useOcrPopulatingLogic(role, ocrImage, setValue, getValues);

  const primaryDToptions = optionList?.PRIMARY_ID_TYPE.options ?? [];

  const onFinish = async (
    data: OcrResult['extract'],
    documentType: string,
    image: {
      base64: string;
      name: string;
      thumbnail?: string;
    },
  ) => {
    const isDocumentTypeValid =
      documentType !== 'OTHER' &&
      primaryDToptions.some(option => option.value === documentType);

    const mappedDocumentType = isDocumentTypeValid ? documentType : '';

    if (!isDocumentTypeValid) {
      setOCRSuccess(false);
      populateOcrData({ data, documentType: mappedDocumentType, image });
      return;
    }
    if (role === PartyRole.PROPOSER || role === PartyRole.INSURED) {
      ocrScanned.current = true;
      const [result, mismatchFields] = validateOcr(
        {
          ...data,
          fullName: '',
        },
        {
          firstName: getValues('firstName'),
          lastName: getValues('lastName'),
          fullName: '',
          dateOfBirth: getValues('dateOfBirth') ?? null,
          gender: getValues('gender') as Gender,
        },
      );

      const [newResult, newMismatchFields] =
        applyRegionalLogicToOcrValidationResult(
          documentType,
          result,
          mismatchFields,
        );

      if (newResult === OcrValidationResult.Match) {
        try {
          setOCRSuccess(true);
          setIsVerifyingCustomer(true);
          const customer = await lookup({
            dateOfBirth: data.dateOfBirth
              ? format(data.dateOfBirth, 'yyyy-MM-dd')
              : '',
            firstName: data.firstName || '',
            middleInitial: data.middleName || '',
            lastName: data.lastName || '',
            gender: data.gender === 'FEMALE' ? 'F' : 'M',
          });
          if (!customer.firstName) {
            throw new Error('Customer not found');
          }
          setLaCustomer(customer);
          setLaVerificationVisible(true);
          setSTP(true);
        } finally {
          setIsVerifyingCustomer(false);
          populateOcrData({ data, documentType: mappedDocumentType, image });
        }
      } else {
        setOCRSuccess(false);
        setOcrExtractionData({ data, documentType: mappedDocumentType, image });
        setOcrValidationResult(newResult);
        setOcrValidationMismatchFields(newMismatchFields);
        setOcrValidationErrorDialogVisible(true);
      }
    } else {
      populateOcrData({ data, documentType: mappedDocumentType, image });
    }
  };

  const onDelete = () => {
    if (role === PartyRole.PROPOSER || role === PartyRole.INSURED) {
      setSTP(false);
    }
    onDeleteCb();
  };

  const lookupExistingCustomer = async (
    data: Pick<
      OcrData['data'],
      'firstName' | 'lastName' | 'dateOfBirth' | 'gender' | 'middleName'
    >,
  ) => {
    const customer = await lookup({
      dateOfBirth: data.dateOfBirth
        ? format(data.dateOfBirth, 'yyyy-MM-dd')
        : '',
      firstName: data.firstName || '',
      middleInitial: data.middleName || '',
      lastName: data.lastName || '',
      gender: data.gender === 'FEMALE' ? 'F' : 'M',
    });
    if (!customer.firstName) {
      throw new Error('Customer not found');
    }
    setLaCustomer(customer);
    setLaVerificationVisible(true);
  };

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const updateCustomer = async () => {
    try {
      setAppLoading();
      await lookupExistingCustomer({
        firstName: getValues('firstName'),
        lastName: getValues('lastName'),
        middleName: getValues('middleName'),
        dateOfBirth: getValues('dateOfBirth'),
        gender:
          getValues('gender') === Gender.FEMALE
            ? 'FEMALE'
            : getValues('gender') === Gender.MALE
            ? 'MALE'
            : null,
      });
    } finally {
      setAppIdle();
    }
  };

  const { reset } = useNavigation<NavigationProp<RootStackParamList>>();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );

  const onRetakeForValidationErrorDialog = () => {
    ocrRef?.current?.resetAndOpen();
    setOcrValidationErrorDialogVisible(false);
  };

  const onSkipForValidationErrorDialog = async () => {
    setSTP(false);
    ocrRef?.current?.reset();
    setOcrValidationErrorDialogVisible(false);
    await updateCustomer();
  };

  const isNameMismatch =
    'firstName' in ocrValidationMismatchFields ||
    'lastName' in ocrValidationMismatchFields;
  const isDobOrGenderMismatch =
    'gender' in ocrValidationMismatchFields ||
    'dateOfBirth' in ocrValidationMismatchFields;

  // Requirements from PH BU
  const isShowUpdateButtonForValidationErrorDialog =
    isNameMismatch || (isNameMismatch && isDobOrGenderMismatch);
  const onUpdateForValidationErrorDialog = async () => {
    setSTP(false);
    populateOcrData(ocrExtractionData);
    setOcrValidationErrorDialogVisible(false);
    try {
      setAppLoading();

      let customerData: Pick<
        OcrData['data'],
        'firstName' | 'lastName' | 'dateOfBirth' | 'gender' | 'middleName'
      > = ocrExtractionData.data;

      if (
        (isPIEqualPO && role === PartyRole.PROPOSER) ||
        role === PartyRole.INSURED
      ) {
        customerData = {
          ...customerData,
          dateOfBirth: getValues('dateOfBirth'),
          gender:
            getValues('gender') === Gender.FEMALE
              ? 'FEMALE'
              : getValues('gender') === Gender.MALE
              ? 'MALE'
              : null,
        };
      }
      await lookupExistingCustomer(customerData);
    } finally {
      setAppIdle();
    }
  };

  const isShowCreateNewQuoteButtonForValidationErrorDialog =
    ((isPIEqualPO && role === PartyRole.PROPOSER) ||
      role === PartyRole.INSURED) &&
    ocrValidationResult === OcrValidationResult.DobOrGenderMismatch;
  const onCreateNewQuoteForValidationErrorDialog = () => {
    setOcrValidationErrorDialogVisible(false);
    onClose?.();
    clearActiveCase();
    reset({
      index: 0,
      routes: [{ name: 'Main' }, { name: 'CoverageDetailsScreen' }],
    });
  };

  const renderValidationDialog = () => (
    <OcrValidationErrorDialog
      visible={ocrValidationErrorDialogVisible}
      fields={ocrValidationMismatchFields}>
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.retake')}
        onPress={onRetakeForValidationErrorDialog}
      />
      {isShowUpdateButtonForValidationErrorDialog && (
        <OcrValidationErrorDialogButton
          style={{
            marginTop: space[3],
          }}
          variant="secondary"
          text={t('eApp:ocr.error.update')}
          onPress={onUpdateForValidationErrorDialog}
        />
      )}
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.skip')}
        style={{
          marginTop: space[3],
        }}
        variant="text"
        onPress={onSkipForValidationErrorDialog}
      />
      {isShowCreateNewQuoteButtonForValidationErrorDialog && (
        <TouchableOpacity
          onPress={() => {
            setOcrValidationErrorDialogVisible(false);
            onClose?.();
            clearActiveCase();
            reset({
              index: 0,
              routes: [{ name: 'Main' }, { name: 'CoverageDetailsScreen' }],
            });
          }}>
          <LargeLabel
            style={{ marginTop: space[6] }}
            fontWeight="bold"
            color={colors.primary}>
            {t('eApp:ocr.error.newQuote')}
          </LargeLabel>
        </TouchableOpacity>
      )}
    </OcrValidationErrorDialog>
  );

  const renderConfirmationDialog = () => (
    <UpdateSTPFieldConfirmationDialog
      visible={updateSTPFieldConfirmationDialogVisible}
      onAccept={() => {
        setSTP(false);
        setUpdateSTPFieldConfirmationDialogVisible(false);
      }}
      onCancel={() => {
        Keyboard.dismiss();
        setUpdateSTPFieldConfirmationDialogVisible(false);
      }}
    />
  );

  const renderLADialog = () => (
    <LAVerificationDialog
      visible={laVerificationVisible}
      data={{
        firstName: laCustomer?.firstName,
        lastName: laCustomer?.lastName,
        gender: laCustomer?.gender,
        dob: laCustomer?.birthDate,
      }}
      onAccept={() => {
        setLaVerificationVisible(false);
        if (laCustomer) {
          if (laCustomer.maritalStatus) {
            setValue('maritalStatus', laCustomer.maritalStatus, {
              shouldTouch: true,
              shouldValidate: true,
            });
          }
          const findPhoneCode = (code: string) =>
            optionList?.COUNTRY_CODE.options.find(o => o.value.includes(code))
              ?.value;

          const payload = {
            contactDetails: {
              ...personalInfo.contactDetails,
              email: laCustomer.email || personalInfo.contactDetails.email,
              primaryCountryCode: laCustomer.mobileNo
                ? findPhoneCode(laCustomer.mobileNo.slice(0, 2)) ||
                  personalInfo.contactDetails.primaryCountryCode
                : personalInfo.contactDetails.primaryCountryCode,
              primaryMobile: laCustomer.mobileNo
                ? laCustomer.mobileNo.slice(2)
                : personalInfo.contactDetails.primaryMobile,
              homeCountryCode: laCustomer.phone1
                ? findPhoneCode(laCustomer.phone1.slice(0, 2)) ||
                  personalInfo.contactDetails.homeCountryCode
                : personalInfo.contactDetails.homeCountryCode,
              homeMobile: laCustomer.phone1
                ? laCustomer.phone1.slice(2)
                : personalInfo.contactDetails.homeMobile,
              officeCountryCode: laCustomer.phone2
                ? findPhoneCode(laCustomer.phone2.slice(0, 2)) ||
                  personalInfo.contactDetails.officeCountryCode
                : personalInfo.contactDetails.officeCountryCode,
              officeMobile: laCustomer.phone2
                ? laCustomer.phone2.slice(2)
                : personalInfo.contactDetails.officeMobile,
              faxCountryCode: laCustomer.fax
                ? findPhoneCode(laCustomer.fax.slice(0, 2)) ||
                  personalInfo.contactDetails.faxCountryCode
                : personalInfo.contactDetails.faxCountryCode,
              faxMobile: laCustomer.fax
                ? laCustomer.fax.slice(2)
                : personalInfo.contactDetails.faxMobile,
            },
            addressInfo: {
              ...personalInfo.addressInfo,
              addressLine1:
                laCustomer.address1 || personalInfo.addressInfo.addressLine1,
              addressLine2:
                laCustomer.address2 || personalInfo.addressInfo.addressLine2,
              addressLine3:
                laCustomer.address1 || personalInfo.addressInfo.addressLine3,
              city: laCustomer.address3 || personalInfo.addressInfo.city,
              province:
                laCustomer.address4 || personalInfo.addressInfo.province,
              country:
                optionList?.COUNTRY.options.find(
                  o => o.label === laCustomer.address5,
                )?.value || personalInfo.addressInfo.country,
              postalCode:
                laCustomer.zipCode || personalInfo.addressInfo.postalCode,
            },
            occupationDetails: {
              ...personalInfo.occupationDetails,
              occupationType:
                laCustomer.occupationCode ||
                personalInfo.occupationDetails.occupationType,
            },
            nationalityDetails: {
              ...personalInfo.nationalityDetails,
              nationality:
                laCustomer.nationality ||
                personalInfo.nationalityDetails.nationality,
              countryOfBirth:
                laCustomer.countryCode ||
                personalInfo.nationalityDetails.countryOfBirth,
              placeOfBirth:
                laCustomer.birthPlace ||
                personalInfo.nationalityDetails.placeOfBirth,
            },
          };
          if (role === PartyRole.PROPOSER) {
            (payload as EAppState['policyOwnerPersonalInfo']).usTaxDeclaration =
              {
                ...personalInfo.usTaxDeclaration,
                isUSCitizen: laCustomer.fatca
                  ? laCustomer.fatca === '00'
                    ? 'no'
                    : 'yes'
                  : personalInfo.nationalityDetails.nationality,
              };
          }
          updatePersonalInfo(payload);
        }
      }}
      onDeny={() => {
        setLaVerificationVisible(false);
      }}
    />
  );

  return {
    onFinish,
    onDelete,
    ocrRef,
    ocrScanned,
    ocrValidationResult,
    ocrExtractionData,
    ocrValidationMismatchFields,
    ocrValidationErrorDialogVisible,
    setOcrValidationErrorDialogVisible,
    updateSTPFieldConfirmationDialogVisible,
    setUpdateSTPFieldConfirmationDialogVisible,
    isVerifyingCustomer,
    laVerificationVisible,
    setLaVerificationVisible,
    laCustomer,
    setLaCustomer,
    populateOcrData,
    lookup,
    isSearchingCustomer,
    isOcrSuccess,
    setOCRSuccess,
    lookupExistingCustomer,
    updateCustomer,
    renderValidationDialog,
    onRetakeForValidationErrorDialog,
    onSkipForValidationErrorDialog,
    isShowUpdateButtonForValidationErrorDialog,
    onUpdateForValidationErrorDialog,
    isShowCreateNewQuoteButtonForValidationErrorDialog,
    onCreateNewQuoteForValidationErrorDialog,
    renderConfirmationDialog,
    renderLADialog,
    optionList,
    isFetchingOptionList,
    ocrImage,
  };
};

export default useOcrLogic;
