import { useTheme } from '@emotion/react';
import { PictogramIcon } from 'cube-ui-components';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { RenewalPaymentSetupForm } from 'features/eAppV2/ph/validations/renewalPaymentSetupValidation';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCase } from 'hooks/useGetCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useEffect, useMemo } from 'react';
import {
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { PaymentMode } from 'types/proposal';
import { shallow } from 'zustand/shallow';
import IconMobileBank from '../components/renewalPaymentSetup/icon/IconMobileBank';
import { toApplicationRenewalPaymentSetup } from '../utils/caseUtils';

const useRenewalPaymentSetupLogic = ({
  watch,
  handleSubmit,
  setValue,
  getValues,
}: {
  watch: UseFormWatch<RenewalPaymentSetupForm>;
  handleSubmit: UseFormHandleSubmit<RenewalPaymentSetupForm>;
  setValue: UseFormSetValue<RenewalPaymentSetupForm>;
  getValues: UseFormGetValues<RenewalPaymentSetupForm>;
}) => {
  const { t } = useTranslation(['eApp']);
  const { sizes } = useTheme();
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();
  const { hasBankDetails } = useEAppStore(
    state => ({
      hasBankDetails: state.hasBankDetails,
      renewalPaymentSetup: state.renewalPaymentSetup,
    }),
    shallow,
  );

  const next = useEAppProgressBarStore(state => state.next);
  const setData = useEAppStore(state => state.updateRenewalPaymentSetup);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj, isFetching: isGettingCase } = useGetCase(caseId ?? '');
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const quotation = useSelectedQuotation();
  const paymentMethodList = useMemo((): {
    value: RenewalPaymentSetupForm['paymentMethod'];
    label: string;
    icon?: JSX.Element;
  }[] => {
    const premiumType = quotation?.plans[0].premiumType;
    const paymentMode = quotation?.plans[0].paymentMode;
    if (
      premiumType === 'SP' ||
      paymentMode === PaymentMode.ONE_TIME ||
      paymentMode === PaymentMode.SINGLE
    ) {
      return [
        {
          value: 'NA',
          label: t('eApp:paymentSetup.paymentMethodList.NA'),
        },
      ];
    } else {
      if (paymentMode === PaymentMode.MONTHLY) {
        return [
          {
            value: 'aca',
            label: t('eApp:paymentSetup.paymentMethodList.aca'),
            icon: <PictogramIcon.CreditCard size={sizes[14]} />,
          },
          {
            value: 'adda',
            label: t('eApp:paymentSetup.paymentMethodList.adda'),
            icon: <PictogramIcon.Transaction size={sizes[14]} />,
          },
          {
            value: 'cheque',
            label: t('eApp:paymentSetup.paymentMethodList.cheque'),
            icon: <PictogramIcon.Capital size={sizes[14]} />,
          },
        ];
      }
      return [
        {
          value: 'aca',
          label: t('eApp:paymentSetup.paymentMethodList.aca'),
          icon: <PictogramIcon.CreditCard size={sizes[14]} />,
        },
        {
          value: 'adda',
          label: t('eApp:paymentSetup.paymentMethodList.adda'),
          icon: <PictogramIcon.Transaction size={sizes[14]} />,
        },
        {
          value: 'cash',
          label: t('eApp:paymentSetup.paymentMethodList.cash'),
          icon: <PictogramIcon.Cash size={sizes[14]} />,
        },
        {
          value: 'pos',
          label: t('eApp:paymentSetup.paymentMethodList.pos'),
          icon: <IconMobileBank size={sizes[14]} />,
        },
      ];
    }
  }, [quotation?.plans, t, sizes]);

  const creditExpiryDate = watch('creditExpiryDate');
  useEffect(() => {
    if (creditExpiryDate) {
      setValue(
        'creditExpiryDate',
        creditExpiryDate.replace(/(\d{2})(\d{1,})/g, '$1/$2'),
        {
          shouldTouch: true,
          shouldValidate: true,
        },
      );
    }
  }, [creditExpiryDate, setValue]);

  useEffect(() => {
    if (paymentMethodList.length === 1 && paymentMethodList[0].value === 'NA') {
      setValue('paymentMethod', 'NA', { shouldValidate: true });
    }
  }, [paymentMethodList, setValue]);

  const onSubmit = (autoNext = true) => {
    handleSubmit(async data => {
      setData(data);
      if (agentId && caseId && caseObj) {
        await saveApplication({
          caseId,
          data: {
            ...caseObj.application,
            directCredit: {
              ...toApplicationRenewalPaymentSetup(data, agentId),
            },
          },
        });
      }
      next(false, autoNext);
    })();
  };

  const onSave = async () => {
    const data = getValues();
    setData(data);
    if (agentId && caseId && caseObj) {
      const directCredit = toApplicationRenewalPaymentSetup(data, agentId);
      if (
        'cardNumber' in directCredit &&
        directCredit.cardNumber === 'undefined'
      ) {
        directCredit.cardNumber = '';
      }
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          directCredit: {
            ...directCredit,
          },
        },
      });
    }
  };

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return {
    isNarrowScreen,
    optionList,
    isFetchingOptionList,
    paymentMethodList,
    isGettingCase,
    isSavingApplication,
    onSubmit,
    onSave,
  };
};

export default useRenewalPaymentSetupLogic;

export type DataPrivacyModal = ReturnType<typeof useRenewalPaymentSetupLogic>;
