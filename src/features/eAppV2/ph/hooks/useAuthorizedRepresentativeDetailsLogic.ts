import { useTheme } from '@emotion/react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { UseFormReturn } from 'react-hook-form';
import { AuthorizedRepresentativeDetailsForm } from '../validations/applicationDetails/sections/authorizedRepresentativeDetailsValidation';

const useAuthorizedRepresentativeDetailsLogic = ({
  onDone,
  form,
}: {
  onDone: (values: AuthorizedRepresentativeDetailsForm, done?: boolean) => void;
  form: UseFormReturn<AuthorizedRepresentativeDetailsForm>;
}) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const submit = useCallback(
    (data: AuthorizedRepresentativeDetailsForm) => {
      onDone(data);
    },
    [onDone],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return {
    t,
    optionList,
    isFetchingOptionList,
    submit,
    isNarrowScreen,
    form,
    onDone,
    space,
    colors,
  };
};

export default useAuthorizedRepresentativeDetailsLogic;

export type AuthorizedRepresentativeDetailsLogic = ReturnType<
  typeof useAuthorizedRepresentativeDetailsLogic
>;
