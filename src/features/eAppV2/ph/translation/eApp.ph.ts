import eAppCommonPh from '../../common/translation/eApp.ph';

export default {
  ...eAppCommonPh,
  app: 'Application',
  yourProgress: 'Your progress',
  next: 'Next',
  ok: 'OK',
  yes: 'Yes',
  no: 'No',
  close: ' Close',
  more: ' ...more',
  done: 'Done',
  reminders: 'Reminders: ',
  exitEApp: 'Exit the application',
  exitEAppDesc: 'Do you want to save before exit the application?',
  dontSave: "Don't save",
  save: 'Save',
  add: 'Add',
  saved: 'Saved',
  continue: 'Continue',
  failedToStart: 'Failed to start application',
  policyOwner: 'Policy owner',
  insured: 'Insured',
  appDetail: 'Application detail',
  beneficialOwner: 'Beneficial owner',
  payor: 'Payor',
  beneficiary: 'Beneficiary',
  goToTheField: 'Go to the field',
  back: 'Back',
  confirm: 'Confirm',
  'header.home': 'Home',
  'bar.appDetail': 'Application detail',
  'bar.ownerAndInsured': 'Policy owner & Insured details',
  'bar.policyOwner': 'Policy owner',
  'bar.insured': 'Insured',
  'bar.otherStakeholder': 'Other stakeholder details',
  'bar.beneficialOwner': 'Beneficial owner',
  'bar.payor': 'Payor',
  'bar.beneficiary': 'Beneficiary',
  'bar.beneficiary.primary': 'Primary beneficiary',
  'bar.beneficiary.secondary': 'Secondary beneficiary',
  'bar.healthQuestion': 'Health question',
  'bar.consents': 'Consents',
  'bar.declaration': 'Declaration',
  'bar.dataPrivacy': 'Data privacy & consent declaration',
  'bar.paymentSetup': 'Payment setup',
  'bar.renewalPayment': 'Renewal premium payment',
  'bar.bankDetails': 'Bank details',
  'bar.uploadDocument': 'Upload document',
  'bar.reviewSignature': 'Review & signature',
  'bar.review': 'Review',
  'bar.reviewSummary': 'Review summary',
  'bar.ownerSignature': 'Policy owner’s signature',
  'bar.insuredSignature': 'Insured’s signature',
  'bar.advisorSignature': 'Financial advisor’s signature',
  'bar.payment': 'Payment',
  'bar.paymentOffline': 'Payment Offline',
  'bar.thirdPartyPayor': 'Third party payor',
  'review.reviewSummary': 'Review summary',
  'review.reviewSummary.informationCorrect':
    'The above information is correct.',
  'review.reviewSummary.informationConfirm':
    'I confirm the information on the summary page has been reviewed and verified to be complete and accurate',
  'review.applicationSummary': 'Application summary',
  'review.sumAssured': 'Sum assured',
  'review.premium': 'Premium',
  'review.year': 'year',
  'review.premiumYear': 'Premium',
  'review.premiumPaymentPeriod': 'Premium payment period',
  'review.paymentType': 'Payment type',
  'review.premiumPaymentMode': 'Premium payment mode',
  'review.applicationDetails': 'Application details',
  'review.applicationNumber': 'Application number',
  'review.policyOwner': 'Policy owner',
  'review.policyOwner.healthInformation': "Policy owner's health information",
  'review.email': 'Email',
  'review.dateOfApplication': 'Date of application',
  'review.referralDetails': 'Referral details',
  'review.leadStatus': 'Lead status',
  'review.affiliateCode': 'Affiliate code',
  'review.personalInformation': 'Personal information',
  'review.insured': 'Insured',
  'review.insured.healthInformation': "Insured's health information",
  'review.policyInsured': 'Policy insured',
  'review.beneficialOwner': 'Beneficial owner',
  'review.payor': 'Payor',
  'review.beneficiary': 'Beneficiary',
  'review.primaryBeneficiary': 'Primary beneficiary',
  'review.secondaryBeneficiary': 'Secondary beneficiary',
  'review.healthQuestions': 'Health questions',
  'review.consentsAndAgreements': 'Consents and agreements',
  'review.policyReplacementDeclaration': 'Policy replacement declaration',
  'review.dataPrivacyAndConsentDeclaration':
    'Data privacy & consent declaration',
  'review.fnaPolicyOwner': 'FNA of policy owner',
  'review.rpqPolicyOwner': 'RPQ of policy owner',
  'review.proposalPDF': 'Proposal PDF',
  'review.applicationForm': 'Application form',
  'review.applicationForm.reviewToProceed': 'Please review to proceed',
  'review.renewalPremiumPayment': 'Renewal premium payment',
  'review.renewalPremiumPaymentMethod': 'Renewal premium payment method',
  'review.autoCreditArrangement': 'Auto credit arrangement',
  'review.creditCardHolderInformation': 'Credit card holder information',
  'review.cardHolderName': 'Card holder name',
  'review.cardCompany': 'Card company',
  'review.cardNumber': 'Card number',
  'review.expiryDateMMYY': 'Expiry date (MM/YY)',
  'review.expiryDate': 'Expiry date',
  'review.issuedBySecurityBank':
    'Is your credit card issued by security bank corporation?',
  'review.documentsUploaded': 'Documents uploaded',
  'review.documentOf': 'Document of',
  'review.documentOfAuthorizedRepresentative':
    'Document of {{fullName}} (Authorized representative)',
  'review.cardFront': 'ID card front',
  'review.cardBack': 'ID card back',
  'review.frontID': 'ID card front of {{idName}}',
  'review.backID': 'ID card back of {{idName}}',
  'review.certOnBO': 'Certification on Beneficial Ownership',
  'review.irswForm': 'IRS W-Form',
  'review.proofOfBank': 'Proof of bank account details',
  'review.financialProfile': 'Financial profile',
  'review.rpqYourResult': 'Here is your result',
  'review.rpqResult': 'RPQ result',
  'review.clientWaiver': 'Client waiver and client disclaimer',
  'review.personalInfo': 'Personal info',
  'review.name': 'Name',
  'review.salutationTitle': 'Salutation/Title',
  'review.firstName': 'First name',
  'review.middleName': 'Middle name',
  'review.lastName': 'Last name',
  'review.extensionName': 'Extension name',
  'review.organizationName': 'Organization name',
  'review.identificationDetails': 'Identification details',
  'review.tinSSSGSIS': 'TIN/SSS/GSIS',
  'review.tinSSSGSISNo': 'TIN/SSS/GSIS No. ',
  'review.primaryIdentificationType': 'Primary identification type',
  'review.primaryIdentificationNumber': 'Primary identification number',
  'review.otherDetails': 'Other details',
  'review.gender': 'Gender',
  'review.gender.M': 'Male',
  'review.gender.F': 'Female',
  'review.dateOfBirth': 'Date of birth',
  'review.age': 'Age',
  'review.maritalStatus': 'Marital status',
  'review.leadSource': 'Lead source',
  'review.contactDetails': 'Contact details',
  'review.mobileNumber': 'Mobile number',
  'review.primaryContact': 'Primary contact',
  'review.secondaryContact': 'Secondary contact',
  'review.homeNumber': 'Home number',
  'review.officeNumber': 'Office number',
  'review.faxNumber': 'Fax number',
  'review.addressInfo': 'Address details',
  'review.currentAddress': 'Current address',
  'review.fullAddress': 'Full address',
  'review.countryCode': 'Country code',
  'review.country': 'Country',
  'review.province': 'Province',
  'review.city': 'City',
  'review.postalCode': 'Postal code',
  'review.businessAddress': 'Business address',
  'review.nationalityDetails': 'Nationality details',
  'review.nationality': 'Nationality',
  'review.countryOfBirth': 'Country of birth',
  'review.placeOfBirth': 'Place of birth',
  'review.occupationDetails': 'Occupation details',
  'review.occupation': 'Occupation',
  'review.occupation.natureOfBusiness': 'Nature of business',
  'review.nameOfEmployer': 'Name of employer',
  'review.annualIncome': 'Annual income',
  'review.industryAffiliation': 'Industry affiliation',
  'review.additionalDetails': 'Additional details',
  'review.answer': 'Answer',
  'review.exactAffiliation': 'Exact affiliation',
  'review.usTaxDeclaration': 'US tax declaration',
  'review.passportGreenCard': 'Passport/Green card no.',
  'review.taxIDNumber': 'Tax ID number (TIN)',
  'review.usResidenceAddress': 'US residence address',
  'review.campaignCode': 'Campaign code',

  'review.personal.beneficialOwner.q1':
    'Do you have beneficial owner/s that is/are U.S. citizen, U.S. resident or a U.S. entity?',
  'review.personal.payor.q1':
    'Will the premiums for this policy be paid for by a person/entity other than the policy owner?',
  'review.personal.insured.relationship': 'Relationship',
  'review.personal.payor.relationshipWithOwner': 'Relation with policy owner',
  'review.personal.beneficiary.relationshipWithInsured':
    'Relationship with primary insured',
  'review.personal.payor.reason': 'Reason for third party payment',
  'review.personal.payor.companyAllowance': 'Company allowance',
  'review.healthQuestions.height': 'Height',
  'review.healthQuestions.weight': 'Weight',
  'review.healthQuestions.q1':
    'The person covered’s current height and weight:',
  'review.healthQuestions.q2':
    'Did the Insured have or receive medical advice or treatment for:',
  'review.healthQuestions.q2.a1':
    'Cancer or leukemi, Nephritis, nephrotic syndrome or chronic kidney disease',
  'review.healthQuestions.q3':
    'Did the Insured have or receive medical advice or treatment for:',
  'review.healthQuestions.q3.a1': 'None of the above',
  'review.policy.q1':
    'Do you own any insurance policy with any existing insurance company that is still in inforce',
  'review.policy.q2':
    'Do you own any insurance policy with any insurance company that has lapsed but can be reinstated?',
  'review.policy.q3':
    'Will you change or replace any life insurance policy/ies you own with the one you are applying for?',
  'review.policy.q4':
    'Will premiums for the insurance you are applying for be paid by a policy loan or surrender value from any existing policies?',
  'review.policy.q5':
    'I confirm that the above information is true, complete and accurate.',
  'review.policy.reminder':
    'Reminders:\nREPLACING an existing life insurance policy with a new one is usually disadvantageous as you may lose financial benefits you have accumulated over the years, or you may not even be insurable on standard terms, or you may be required to pay a higher premium in view of higher age. Thus, for your own benefit and interest, please consult your present insurer before making a final decision. Hear from both sides and make a careful comparison. You can then be sure that you are making a decision that is your best interest.',
  'review.privacy.detail':
    "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
  'review.privacy.q1':
    'I expressly consent to the processing of my information as stated above.',
  'review.privacy.q2':
    'I wish to be informed on FWD’s products, and other promotional information that I might be interested in.',
  'review.personalInfo.benefitPercentage': 'Benefit percentage',
  'review.personalInfo.typeOfBeneficiary': 'Type of beneficiary',
  'review.personalInfo.revocableBeneficiary': 'Revocable beneficiary',
  'review.personalInfo.percentage': 'Percentage',
  'review.personalInfo.primary': 'Primary',
  'review.personalInfo.secondary': 'Secondary',
  'review.personalInfo.industryAffiliation.q1':
    'Do you own any interest in or are in any way affiliated, deal with or provide services to any of the following:',
  'review.personalInfo.industryAffiliation.q2':
    'Do you own any interest in or are in any way affiliated with any of the following:',
  'review.personalInfo.additionalDetails.purposeOfInsurance':
    'Purpose of insurance',
  'review.personalInfo.additionalDetails.otherPurposeOfInsurance':
    'Other purpose',
  'review.personalInfo.additionalDetails.sourceOfFund': 'Source of fund',
  'review.personalInfo.additionalDetails.otherSourceOfFund': 'Other source',
  'review.personalInfo.additionalDetails.otherSourceOfFund.entity':
    'Other source (optional)',
  'review.personalInfo.additionalDetails.sourceOfPremium': 'Source of premium',
  'review.personalInfo.usTax.q1':
    'Are you a citizen, passport holder or green card holder of the U.S. or were you born in the U.S.?',
  'review.confirm.title': 'Please confirm',
  'review.confirm.content':
    'The information on the summary page has been reviewed and verified to be complete and accurate.',
  'review.confirm.ok': 'I confirm',
  'review.decision.goodNewsYouAreAlmostThere':
    'Good news, you’re almost there!',
  'review.decision.standard.description': `Your insurance application is approved. Thanks for taking this first step to protect yourself and your family’s future.\n\nNow, let's continue the journey to get you covered!`,
  'review.decision.goodNews': 'Good news!',
  'review.decision.continueToSignature': 'Continue to signature',
  'review.decision.next': 'Next',
  'review.decision.back': 'Back',
  'review.decision.youAreAlmostThere': 'You’re almost there!',
  'review.decision.revisedOffer': 'Revised offer!',
  'review.decision.inTheMeanTime':
    'In the meantime, take a closer look at our new offer, designed especially for you. Based on the information you’ve provided, we adjusted your coverage/premium.',
  'review.decision.basedOnInformation':
    'Based on the information you’ve provided, we made a few changes on the coverage/premium we can offer.',
  'review.decision.preview': 'Preview:',
  'review.decision.totalPremium': 'Total Premium',
  'review.decision.tooltip.title': 'Information of monthly premiums',
  'review.decision.tooltip.increased':
    'We’ve increased your premium because of the following:',
  'review.decision.tooltip.following': 'The following is/are not covered:',
  'review.decision.tooltip.gotIt': 'Ok, got it',
  'review.decision.standardWithAdmin.yourInsuranceIsApproved':
    'Your insurance application is approved. You just need to submit additional requirements for us to issue your policy.\n\nPlease send us:',
  'review.decision.standardWithAdmin.note':
    'Your financial advisor will help you complete and submit these requirements.',
  'review.decision.standardWithExclusionOnly.onlyFewMoreSteps':
    'Only a few more steps left to finally securing your financial future.\nBased on the information you’ve provided, we made a few changes on the coverage/premium we can offer.',
  'review.decision.standardWithExclusionOnly.takeACloser':
    ' Take a closer look at our new offer with exclusion.',
  'review.decision.standardWithExclusionAndAdminRequirements.hereOurNewOffer':
    'Here’s our new offer',
  'review.decision.standardWithExclusionAndAdminRequirements.inTheMeantime':
    'In the meantime, take a closer look at our new offer, designed especially for you. Based on the information you’ve provided, we adjusted your coverage/premium.',
  'review.decision.yourAppIsApproved.yourInsuranceIsApproved':
    'Your insurance application is approved. You just need to submit additional requirements for us to issue your policy.\n\nPlease send us:',
  'review.decision.yourAppIsApproved.weJustNeedAFewDocuments':
    'We just need a few documents from you to help us better assess your application and offer you the best possible plan.\n\nPlease send us:',
  'review.decision.yourAppIsApproved.note':
    'Your financial advisor will help you complete and submit these requirements.',
  'review.decision.yourAppIsApproved.weJustNeedAFewDocs':
    'We just need a few documents from you!',
  'review.decision.yourAppIsApproved.yourAppIsApproved':
    'Your application is approved!',
  'review.decision.yourAppIsApproved.yourInsuranceAppIsApproved': `Your insurance application is approved. Thanks for taking this first step to protect yourself and your family’s future.\n\nNow, let's continue the journey to get you covered!`,
  'review.decision.revisedOffer.newOfferWithExclusion':
    ' Take a closer look at our new offer with exclusion.',
  'review.decision.revisedOffer.newOffer':
    ' Take a closer look at our new offer, designed especially for you.',
  'review.decision.youNeedToKnow.somethingYouNeedToKnow':
    'Something you need to know',
  'review.decision.youNeedToKnow.willBeAttached':
    ' will be attached to and will form part of the policy',
  'review.decision.youNeedToKnow.noBenefit': 'No benefit will be paid for ',
  'review.decision.youNeedToKnow.policy':
    'I may request FWD Insurance, at any time, to terminate the effectivity of this Exclusion document upon presentation of satisfactory proof and justifiable reason, subject to FWD Insurance’s approval',
  'review.decision.others.title': 'Thanks for your application!',
  'review.decision.others.stayTuned': 'Stay tuned! We’ll come back to you.',
  'review.decision.others.content': `We'll need more time to better assess your application so we can offer the best possible plan for you.\n\nWe'll update you thru your financial advisor within 2 working days. Thanks for your patience and understanding.`,
  'review.decision.policy.byClick': 'By clicking',
  'review.decision.policy.continue': 'Continue',
  'review.decision.policy.youUnderstand':
    ' you understand and accept that:\n  • Exclusion document will be attached to and will form part of the policy\n  • No benefit will be paid',
  'review.decision.policy.ifTheInsured':
    ' if the insured’s injury, sickness, loss, or claim is directly or indirectly, wholly or partly, caused by or arising from conditions and/or complications related to the',
  'review.decision.policy.iMayRequestFWD':
    '\n  • I may request FWD Insurance, at any time, to terminate the effectivity of this Exclusion document upon presentation of satisfactory proof and justifiable reason, subject to FWD Insurance’s approval',
  'review.altsReferenceNumber': 'BLTS / ALTS reference number',
  'review.servicingBranch': 'Servicing branch',
  'review.referrerCode': 'Referrer code',
  'review.bankCustomerID': 'Bank customer ID',
  'review.entityDetails': 'Entity details',
  'review.entityName': 'Entity name',
  'review.natureOfBusiness': 'Industry / nature of business',
  'review.entityCurrentOfficeAddress': 'Entity current office address',
  'review.positionJobTitle': 'Position / JobTitle',
  'review.relationshipToPolicyOwner': 'Relationship to policy owner',
  'review.nameOfTrustee': 'Name of trustee',
  'review.relationship': 'Relationship',
  'review.relationshipWithInsured': 'Relationship with insured',
  'review.hasBeneficialOwner': 'Do you have a beneficial owner?',
  'review.view': 'View',
  'review.basePlanSummary': 'Base plan',
  'review.additionalProtection': 'Additional protection',
  'review.fundAllocation': 'Fund allocation',
  'review.coveragePerson': 'Coverage person',
  'review.sumCovered': 'Sum Covered',
  'review.contributionTerm': 'Contribution term',
  'review.certificateTerm': 'Certificate term',
  'review.modalContribution': 'Modal contribution',
  'review.paymentPeriod': 'Payment period',
  'review.policyBenefitPeriod': 'Policy benefit period',
  'review.topUp': 'Top-up',
  'review.contribution': 'Contribution',
  'review.sinceYear': 'Since certificate year',
  'review.certificateYearFrom': 'Certificate year from',
  'review.certificateYearTo': 'Certificate year to',
  'review.reviewToProceed': 'Review application form to proceed.',
  'review.personalDetails': 'Personal details',
  'consents.declaration.title': 'Declaration',
  'consents.healthQuestion.title': 'Health questions for policy owner',
  'consents.dataPrivacy.title': 'Data privacy & consent declaration',
  'consents.dataPrivacy.description.shortNote':
    'By signing and submitting this Application Form to FWD, I expressly consent to the following:\nFWD may collect, use, and store the',
  'consents.dataPrivacy.description.fullNote':
    'By signing and submitting this Application Form to FWD, I expressly consent to the following:\nFWD may collect, use, and store the information provided in this Application Form and from Third Parties (including, but not limited to, affiliates, partners, contracted service providers, medical/financial/insurance institutions, government agencies and medical information sharing facilities) to process this application and to service my policies. The information gathered may be shared with FWD’s third parties for purposes consistent with which it was obtained. These information (including those which will be available during the life of my policies) may further be processed and shared for underwriting, reinsurance, policy issuance and administration, rewards processing, claims adjudication, data analytics, historical and scientific research, profiling, risk management, enhancement of products and services, identity verification, protection against fraud, and to comply with legal, regulatory, or contractual requirements. I acknowledge that in certain instances, my information may be processed through automated means.\nI authorize FWD to disclose my personal and financial information to any government or tax authority (within or outside the Philippines) for the purposes of ensuring FWD’s continual compliance with applicable laws, regulations, guidelines, and good market practices. I also agree that FWD has the right to require any of my beneficiaries, claimants, assignees and/or payees to:a. provide FWD with their respective personal and financial information.b. sign and submit such documents as FWD may reasonably require; and c. authorize FWD to disclose such personal and financial information to relevant Philippine and/or foreign government and/or tax authorities.\nI understand that FWD reports to its parent company located in Hong Kong and Singapore and may engage third-party service providers and partners who, in some instances, may be located outside the Philippines. As necessary, my personal and policy information may be processed, shared, stored, and be subject to the laws of these foreign jurisdictions. FWD and its affiliates, third-party service providers and partners shall protect the confidentiality of my personal information in a manner consistent with data protection principles and applicable laws and regulations.\nFWD may contact me to request or clarify information to process this application, send me policy information, and perform other relevant activities to service my policies.\nTo ensure FWD’s continued service where my servicing intermediary, agent or Financial Solutions Consultant (FSC), is no longer connected with FWD, I authorize FWD to assign and inform me of my new servicing intermediary, who shall have access to my data for purposes of serving my policy/ies.\nI attest that the consent of the Policy Insured, Beneficial Owner (if any), Beneficiary/ies, Payor (if any) and all other data subjects in this Application form were obtained by me for the processing of their information for purposes listed above.',
  'consents.dataPrivacy.acceptPolicy':
    'I expressly consent to the processing of my information as stated above.',
  'consents.dataPrivacy.acceptInform':
    'I wish to be informed on FWD’s products, and other promotional information that I might be interested in.',
  'consents.dataPrivacy.btn.confirm': 'I confirm',
  'consents.question.one':
    'Do you own any insurance policy with any existing insurance company that is still in inforce',
  'consents.question.two':
    'Do you own any insurance policy with any insurance company that has lapsed but can be reinstated?',
  'consents.question.three':
    'Will you change or replace any life insurance policy/ies you own with the one you are applying for?',
  'consents.question.four':
    'Will premiums for the insurance you are applying for be paid by a policy loan or surrender value from any existing policies?',
  'consents.reminder.shortNote':
    'REPLACING an existing life insurance policy with a new one is usually disadvantageous as you may',
  'consents.reminder.fullNote':
    'REPLACING an existing life insurance policy with a new one is usually disadvantageous as you may lose financial benefits you have accumulated over the years, or you may not even be insurable on standard terms, or you may be required to pay a higher premium in view of higher age. Thus, for your own benefit and interest, please consult your present insurer before making a final decision. Hear from both sides and make a careful comparison. You can then be sure that you are making a decision that is your best interest.',
  'consents.checkbox.confirm':
    'I confirm that the above information is true, complete and accurate.',
  'payment.method.select': 'Select your payment method',
  'personalDetails.title': 'Personal Details',
  'personalDetails.scanID': 'Scan ID card',
  'personalDetails.scanID.example': 'e.g. ID card or passport',
  'personalDetails.scanID.info.title': 'Acceptable Philippine ID list',
  'personalDetails.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
  'personalDetails.scanID.info.point.2': 'RP Passport',
  'personalDetails.scanID.info.point.3': "Driver's License",
  'personalDetails.scanID.info.point.4': 'PRC ID',
  'personalDetails.scanID.info.point.5': 'SSS (Social Security System ID)',
  'personalDetails.scanID.info.point.6': 'TIN (Bureau of Internal Revenue ID)',
  'personalDetails.name': 'Name',
  'personalDetails.firstName': 'First name',
  'personalDetails.middleName': 'Middle name (optional)',
  'personalDetails.extensionName': 'Extension name (optional)',
  'personalDetails.lastName': 'Last name',
  'personalDetails.customerType': 'Customer type',
  'personalDetails.salutation': 'Salutation/Title',
  'personalDetails.otherLegalName': 'Other legal name (optional)',
  'personalDetails.otherLegalFirstName': 'Other legal first name',
  'personalDetails.otherLegalLastName': 'Other legal last name',
  'personalDetails.otherLegalMiddleName': 'Other legal middle name',
  'personalDetails.otherLegalExtensionName': 'Other legal extension name',
  'personalDetails.homeNumber': 'Home number',
  'personalDetails.dateOfBirth': 'Date of birth',
  'personalDetails.age': 'Age',
  'personalDetails.maritalStatus': 'Marital status',
  'personalDetails.leadSource': 'Lead source',
  'personalDetails.identificationDetails': 'Identification details',
  'personalDetails.primaryIdType': 'Primary ID type',
  'personalDetails.primaryIdNumber': 'Primary identification number',
  'personalDetails.expiryDate': 'Expiry date',
  'personalDetails.otherDetails': 'Other details',
  'personalDetails.gender': 'Gender',
  'personalDetails.gender.male': 'Male',
  'personalDetails.gender.female': 'Female',
  'personalDetails.organizationName': 'Organization name',
  'personalDetails.entityName': 'Entity name',
  'personalDetails.designation': 'Designation',
  'personalDetails.relationshipToOwner': 'Relationship to owner',
  'contactDetails.title': 'Contact details',
  'contactDetails.countryCode': 'Country Code',
  'contactDetails.code': 'Code',
  'contactDetails.mobileNumber': 'Mobile number',
  'contactDetails.email': 'Email',
  'contactDetails.policy': 'Policy delivery mode',
  'contactDetails.note1':
    '*Hard copies are available for a fee. If you wish to request for a hard copy, contact your FWD financial advisor or reach out to our 24/7 Customer Connect at ',
  'contactDetails.note.phone': '+632 8888 8388',
  'contactDetails.note.email': '<EMAIL>',
  'contactDetails.note2':
    ' or Live Chat, available in fwd.com.ph and our mobile app, FWD app.',
  'contactDetails.secondaryContact': 'Secondary contact (optional)',
  'contactDetails.secondaryContactRequired': 'Secondary contact',
  'contactDetails.primaryContact': 'Primary contact',
  'contactDetails.homePhone': 'Home phone',
  'contactDetails.officePhone': 'Office phone',
  'contactDetails.faxPhone': 'Fax phone',
  'contactDetails.homeNumber': 'Home number',
  'contactDetails.officeNumber': 'Office number',
  'contactDetails.faxNumber': 'Fax number (optional)',
  'contactDetails.entity.note':
    '*FWD will use your contact details to update and notify you in relation to your policy such as payments, billings, annual statement, policy contract, and FWD mobile app credentials.',
  'contactDetails.businessPhoneNumber': 'Business phone number',
  'addressInfo.title': 'Address information',
  'addressInfo.currentAddress': 'Current address',
  'addressInfo.currentAddress.entity': 'Entity current office address',
  'addressInfo.country': 'Country',
  'addressInfo.addressLine1': 'Address line 1',
  'addressInfo.addressLine1.note':
    'House no./Lot & Block no./Phase/Floor/Unit/Floor/Building',
  'addressInfo.addressLine2': 'Address line 2',
  'addressInfo.addressLine2.note': 'Street/Subdivision/Barangay',
  'addressInfo.province': 'Province',
  'addressInfo.city': 'City',
  'addressInfo.postalCode': 'Postal code',
  'addressInfo.townCityProviceState': 'Town/CityProvince/State',
  'addressInfo.businessAddress': 'Business address',
  'addressInfo.address': 'Address',
  'nationalityDetails.title': 'Nationality details',
  'nationalityDetails.nationality': 'Nationality',
  'nationalityDetails.dateOfBirth': 'Country of birth',
  'nationalityDetails.placeOfBirth': 'Place of birth',
  'occupationDetails.title': 'Occupation details',
  'occupationDetails.detailRow1':
    'Office, entrepreneur and sale professional (other than specific service provider)',
  'occupationDetails.detailRow1.paragraph1':
    'You spend a lot of time working in the office, e.g. CEO, officer, clerical work, corporate lawyer, medical practitioners, corporate accountant, actors, entertainers, commercial aviation, insurance agents, business owners, teachers/professors, officer seafarers, priest/nun, government employee (rank and file, doing clerical, admin, or office work)',
  'occupationDetails.detailRow1.title1': 'Sample occupations:',
  'occupationDetails.detailRow1.paragraph2':
    'Actor/Actress without stunt work\n Aerobics/Dance/Gym Instructor\n Architect/Draftsman/Construction\n Artist\n Barker\n Beauty parlor operator/Worker\n Call center manager / Agent\n Car wash worker\n Cashier\n Casino worker\n Caterer\n Chef\n Commercial pilot\n Company executive\n Dressmaker\n Entertainer\n Factory officer/Worker\n Farm owner\n Funeral director\n Insurance agent\n Medical practitioner',
  'occupationDetails.detailRow2': 'Independent service provider',
  'occupationDetails.detailRow2.paragraph1':
    'You are a jewelry or precious stone/metal dealer.',
  'occupationDetails.detailRow2.paragraph2':
    'Persons, including lawyers and accountants, who provide any of the following services:',
  'occupationDetails.detailRow2.paragraph3.1':
    'managing of client money, securities or other assets;\nmanagement of bank, savings, securities or accounts;\norganization of contributions for the creation, operation or management of companies; and\ncreation, operation or management of juridical persons or arrangements, and buying and selling business entities.',
  'occupationDetails.detailRow2.paragraph4':
    'Real estate broker and developers\nOffshore Gaming Operators, as well as their service providers, supervised, accredited or regulated by the Philippine Amusement and Gaming Corporation (PAGCOR) or any Appropriate Government Agency.\nOwner of bank, pawnshop owner and money services (transmitter or changer), dealer\nOwner of a private military firm',
  'occupationDetails.detailRow2.paragraph8':
    'Accountant\n Arms dealer\n Casino Operator\n Jeweler\n Lawyer\n Money changer\n Owner\n Pawnshop owner\n Real estate broker\n Real estate owner',
  'occupationDetails.detailRow3': 'With manual labor',
  'occupationDetails.detailRow3.paragraph1':
    'Your job involves manual labor with some heavy lifting, you may not need specialized training for your work, e.g. mechanic, construction worker, actors with stunt work, mechanics, life guards, bouncers, surface miners, welders, painters, electrician, heavy equipment operators, professional athletes (except equestrian, boxing, diving)',
  'occupationDetails.detailRow3.paragraph2':
    'Actor/Actress with stunt work\nAircraft worker/Mechanic\nBouncer\nConstruction worker\nCircus worker\nDockyard\nElectrician\nElevator installer\nFarm worker\nFireman\nForester\nGlass cutter/Worker\nHeavy equipment operator\nLife guard mechanic/Repairman\nProfessional Athletes (except equestrian, boxing, diving)',
  // 'occupationDetails.detailRow2.paragraph1': 'Your job involves manual labor with some heavy lifting, you may not need specialized training for your work, e.g. mechanic, construction worker, actors with stunt work, mechanics, life guards, bouncers, surface miners, welders, painters, electrician, heavy equipment operators, professional athletes (except equestrian, boxing, diving)',
  'occupationDetails.detailRow4': 'Special class occupations',
  'occupationDetails.detailRow4.paragraph1':
    'Your job involves any of the following: working underground, underwater, shipping, off-shore fishing, mining, oil rigs, armed services, coast guard, chemicals or explosives, non-commercial flying, non-officer seafarers, bodyguard, military (excluding PNP Chief and military officials, General and up), security guard, field reporter, professional athlete/coach (equestrian, boxing, diving)',
  'occupationDetails.detailRow4.paragraph2':
    'Aviation, Non-commercial pilot\nMining/Quarrying\nBodyguard\nExplosive assembler\nField reporter\nFisherman\nMiner - Underground\nPolice/Military/NBI/Navy\nProfessional Athletes (Equestrian, Boxing, Diving)\nScientist\nSeafarer- Non officers\nSecurity guard',
  'occupationDetails.detailRow5': 'Government official (Elected or Appointed)',
  'occupationDetails.detailRow5.paragraph1':
    'You are an elected or appointed high-ranking government official. Please select a sub-group.',
  'occupationDetails.detailRow5.paragraph2': 'Please select sub-group.',
  'occupationDetails.detailRow5.row1':
    'Elected Official/Appointed Official (Ambassador)',
  'occupationDetails.detailRow5.row004A.paragraph1':
    'President\nVice president\nSenator\nCongressman\nProvincial board member\nGovernor\nVice governor\nMayor\nAmbassador',
  'occupationDetails.detailRow5.row2': 'Other Elected Official',
  'occupationDetails.detailRow5.row004B.paragraph1':
    'Vice mayor\nCity councilor\nBarangay chairman\nBarangay kagawad',
  'occupationDetails.detailRow5.row3': 'PNP or Military Officials',
  'occupationDetails.detailRow5.row004C.paragraph1':
    'PNP chief\nGeneral of philippine army\nPhilippine marine corps\nPhilippine navy\nPhilippine coast guard',
  'occupationDetails.detailRow5.row4': 'Executive Branch',
  'occupationDetails.detailRow5.row004D.paragraph1':
    'Executive secretary\nCabinet secretary of department (Ex. department of health, Department of education, Department of foreign affairs)\nCommissioner (Ex. Civil service commission, Commission on elections, Commission on human rights, Commission on audit) Ombudsman\nBureau director/Regional offices director (Ex. Bureau of customs, Bureau of internal revenue)\nPresident/CEO/General manager of government and controlled corporation (Ex. GSIS, PAGIBIG, Landbank of the Philippines, PAGCOR, PHILHEALTH)\nPresident/Heads of state universities and educational institutions (Ex. University of the Philippines (UP), Polytechnic University of the Philippines PUP)',
  'occupationDetails.detailRow5.row5': 'Judicial Branch',
  'occupationDetails.detailRow5.row004E.paragraph1':
    " Judge of the municipal trial court/Metropolitan trial courts/Municipal circuit trial courts\nJudge of the shari'a district & Circuit courts\nJudge of the regional trial court\nJustice of the court of appeals\nJustice of the court of tax appeals\nJustice of the sandiganbayan\nJustice of the Supreme court",
  'occupationDetails.detailRow6': 'Non working',
  'occupationDetails.detailRow6.paragraph1':
    'You are not in paid employment, e.g. housewife/househusband, homemake, student, retiree or not currently employed',
  'occupationDetails.detailRow6.paragraph2':
    'Housewife/househusband\nJuvenile\n Retiree/Pensioner\n Student\n Unemployed',
  'occupationDetails.nameOfEmployer': 'Name of employer',
  'occupationDetails.annualIncome': 'Annual income (PHP)',
  'occupationDetails.describesQuestion': 'What best describes your occupation?',
  'industryAffiliation.title': 'Industry affiliation',
  'industryAffiliation.paragraph1.title':
    '1. Do you own any interest in or are in any way affiliated, deal with or provide services to any of the following:',
  'industryAffiliation.paragraph1.entity.title':
    'Does your entity belong, affiliated, deal with or provide services to any of the following business/industry: ',
  'industryAffiliation.paragraph1.content':
    'shell company,\nlicensed or unlicensed junket,\nunlicensed casino/gambling business,\nunlicensed money lender/changer or remittance business,\nunlicensed virtual currencies issuer or intermediaries,\nshell bank, and/or\nunlicensed bank, and/or\nunregulated or unregistered charities?',
  'industryAffiliation.paragraph1.content.entity':
    'shell company,\nlicensed or unlicensed junket,\nunlicensed casino & gambling business,\nunlicensed money lender/changer or remittance business,\nunlicensed virtual currencies issuer or intermediaries,\nshell bank, \nunlicensed bank, and/or\nunregulated or unregistered charities?',
  'industryAffiliation.paragraph2.title':
    '2. Do you own any interest in or are in any way affiliated with any of the following:',
  'industryAffiliation.paragraph2.content':
    'weaponry and armament industry,\nlicensed casino/gambling business,\nlicensed money service business,\nlicensed virtual asset/currency providers,\nembassy and foreign consulate,\nQuarrying, mining or logging,\ndealers in high value or precious goods/stones/metals,\ncash intensive business,\nregistered charitable and non-profit organization/foundations?',
  'industryAffiliation.label': 'Please give exact affiliation',
  'industryAffiliation.shellCompany': 'Shell company',
  'industryAffiliation.holder': 'Please give exact affiliation',
  'industryAffiliation.yes': 'Yes',
  'industryAffiliation.no': 'No',
  'industryAffiliation.shellcompany': 'Shell Company',
  'industryAffiliation.shellcompany.des':
    'Refers to legal entities which have no business substance in their own right but through which financial transactions may be conducted.',
  'industryAffiliation.shellbank': 'Shell Bank',
  'industryAffiliation.shellbank.des':
    'A bank incorporated, formed or established in a country or jurisdiction where the bank has no physical presence and which is unaffiliated to a regulated financial group. Physical presence means meaningful mind and management located within a country. The existence simply of a local agent or low-level employees does not constitute physical presence.',
  'industryAffiliation.junket': 'Junket',
  'industryAffiliation.junket.des':
    'Individual or business entity other than the casino licensee who is paid a commission or fee for introducing a person/s to gamble.',
  'industryAffiliation.modal.shellBank': 'Shell Bank',
  'industryAffiliation.modal.shellBankDesc':
    'Refers to legal entities which have no business substance in their own right but through which financial transactions may be conducted.',
  'industryAffiliation.modal.shellCompany': 'Shell Company',
  'industryAffiliation.modal.shellCompanyDesc':
    'A bank incorporated, formed or established in a country or jurisdiction where the bank has no physical presence and which is unaffiliated to a regulated financial group. Physical presence means meaningful mind and management located within a country. The existence simply of a local agent or low-level employees does not constitute physical presence.',
  'industryAffiliation.modal.junket': 'Junket',
  'industryAffiliation.modal.junketDesc':
    'Individual or business entity other than the casino licensee who is paid a commission or fee for introducing a person/s to gamble.',
  'additionalDetail.title': 'Additional details',
  'additionalDetail.purposeOfInsurance': 'Purpose of insurance',
  'additionalDetail.otherPurpose': 'Other purpose (optional)',
  'additionalDetail.sourceOfFund': 'Source of fund',
  'additionalDetail.otherSource': 'Other source (optional)',
  'additionalDetail.sourceOfPremium': 'Source of premium',
  'ustaxDeclaration.title': 'US tax declaration',
  'ustaxDeclaration.des':
    'Are you a citizen, passport holder or green card holder of the U.S. or were you born in the U.S.?',
  'ustaxDeclaration.des.entity':
    'Is the corporation or entity a U.S. corporation or entity?',
  'ustaxDeclaration.passport': 'Passport/Green card no.',
  'ustaxDeclaration.tax': 'Tax ID number (TIN)',
  'ustaxDeclaration.us': 'US residence address',
  'ustaxDeclaration.address': 'Address',
  'paymentSetup.renewalPremiumPayment': 'Renewal premium payment',
  'paymentSetup.renewPaymentMethod': 'Renew payment method',
  'paymentSetup.paymentMethodList.aca': 'Auto credit arrangement',
  'paymentSetup.paymentMethodList.NA': 'N/A',
  'paymentSetup.paymentMethodList.adda': 'Auto direct debit arrangement',
  'paymentSetup.paymentMethodList.cash': 'Cash',
  'paymentSetup.paymentMethodList.pos': 'FWD branch POS mobile',
  'paymentSetup.paymentMethodList.cheque': 'Post-dated cheque',
  'paymentSetup.autoCreditArrangement.shortNote':
    'I certify that I am the credit card owner and the Owner or the Insured of this Policy. I hereby authorize FWD to automatically charge',
  'paymentSetup.autoCreditArrangement.fullNote':
    'I certify that I am the credit card owner and the Owner or the Insured of this Policy. I hereby authorize FWD to automatically charge my credit card asthis Policy’s premium becomes due. In the event that the credit card company declines the transaction for reasons attributable to me, such as insufficient balance or credit card expiry, I understand that FWD will consider the premium for my policy as unpaid and I will have to pay the premium through other payment methods to keep my Policy in force, I understand that I may withdraw from this premium payment arrangement effective 30 days after receipt by FWD of a written notice of withdrawal.',
  'paymentSetup.autoCreditArrangement.creditCardHolderInfo':
    'Credit card holder information',
  'paymentSetup.autoCreditArrangement.cardHolderName': 'Card holder name',
  'paymentSetup.autoCreditArrangement.cardCompany': 'Card company',
  'paymentSetup.autoCreditArrangement.cardNumber': 'Card number',
  'paymentSetup.autoCreditArrangement.expiryDate': 'Expiry date (MM/YY)',
  'paymentSetup.autoCreditArrangement.issuedBySecurityBank.question':
    'Is your credit card issued by security bank corporation?',
  'paymentSetup.autoCreditArrangement.issuedBySecurityBank.yes': 'Yes',
  'paymentSetup.autoCreditArrangement.issuedBySecurityBank.no': 'No',
  'paymentSetup.autoCreditArrangement.disclaimer.question':
    'I understand and accept the following disclaimer.',
  'paymentSetup.autoCreditArrangement.disclaimer.close': 'Close',
  'paymentSetup.autoCreditArrangement.disclaimer.more': '...more',
  'paymentSetup.autoDirectDebitArrangement.accountInfo': 'Account information',
  'paymentSetup.autoDirectDebitArrangement.bankName': 'Bank name',
  'paymentSetup.autoDirectDebitArrangement.accountHolderName':
    'Account holder name',
  'paymentSetup.autoDirectDebitArrangement.accountNumber': 'Account number',
  'paymentSetup.bankDetails.title': 'Bank details',
  'paymentSetup.bankDetails.accountInformation': 'Account information',
  'paymentSetup.bankDetails.bankName': 'Bank name',
  'paymentSetup.bankDetails.branchName': 'Branch name',
  'paymentSetup.bankDetails.accountHolderName': 'Account holder name',
  'paymentSetup.bankDetails.accountNumber': 'Account number',
  'paymentSetup.bankDetails.proofOfBank': 'Proof of bank account details',
  'documentUpload.title': 'Upload documents',
  'documentUpload.upload': 'Upload',
  'documentUpload.takePhotoWithCamera': 'Take Photo with Camera',
  'documentUpload.selectFromAlbum': 'Select from Album',
  'documentUpload.imagePicker': 'Upload profile image by',
  'documentUpload.uploading': 'Document uploading...',
  'documentUpload.limit':
    'Accepted formats: jpg, jpeg, png or pdf (up to 5MB each)',
  'documentUpload.policyOwner': 'Policy owner',
  'documentUpload.authorizedRepresentative': 'Authorized Representative',
  'documentUpload.frontOfId': 'Front of {{type}}',
  'documentUpload.backOfId': 'Back of {{type}}',
  'documentUpload.certificateOnBeneficialOwner':
    'Certification on Beneficial Ownership',
  'documentUpload.documentForBeneficialOwner':
    'Document/s for the Beneficial Owner',
  'documentUpload.IRSWForm': 'IRS W-Form',
  'documentUpload.bankAccount': 'Proof of bank account details',
  'documentUpload.ada': 'Auto direct debit arrangement',
  'documentUpload.aca': 'Auto credit arrangement',
  'documentUpload.makeSure': 'Make sure your photo is:',
  'documentUpload.clearly': 'Clearly ',
  'documentUpload.visible': 'visible and in focus',
  'documentUpload.taken': 'Taken',
  'documentUpload.withoutFlash': ' without flash',
  'documentUpload.takeA': 'Take a ',
  'documentUpload.closeUp': 'close up',
  'documentUpload.photo': ' photo of your ID card',
  'documentUpload.insured': 'Insured',
  'documentUpload.beneficiary': 'Beneficiary',
  'documentUpload.trustee': 'Trustee',
  'documentUpload.partyPayor': 'Third party payor',
  'documentUpload.beneficialOwner': 'Beneficial owner',
  existingPolicy: 'Existing policy {{value}}',
  companyName: 'Company name',
  policyNumber: 'Policy number',
  amountOfCoveragePHP: 'Amount of coverage (PHP)',
  amountOfCoverage: 'Amount of coverage',
  addExistingPolicy: 'Add existing policy',
  'consent.existingPolicy.title':
    'Please provide information about the affected insurance policies. ',
  'consents.privacyPolicy.title': 'Privacy policy',
  'consents.privacyPolicy.note':
    'Note: A separate Temporary Life Insurance Certificate form will be forwarded to you as soon as the Initial Modal Premium has been received.',
  'consents.privacyPolicy.more': '...more',
  'consents.privacyPolicy.close': 'Close',
  'consents.privacyPolicy.content1':
    'Your privacy is a priority for FWD.  The Company keeps your personal information about you and the products and services you have with us in confidence. For more information about our Privacy Policy, kindly visit our website at ',
  'consents.privacyPolicy.content2':
    'You may also email our Data Protection Office at ',
  'consents.privacyPolicy.content3':
    'for any privacy concerns related to your information provided to us.',
  'signature.understandAndConfirm': 'I understand and confirm that:',
  'signature.agreeTerm': 'I, {{name}}, agree to the terms and conditions:',
  'signature.placeOfSigning': 'Place of signing',
  'signature.placeOfSigning.format': 'Place of signing: {{place}}',
  'signature.clear': 'Clear',
  'signature.declarationMadeBy': 'Declarations made by',
  'signature.policyOwner': 'Policy owner',
  'signature.proposedOwner': 'Proposed owner',
  'signature.insured': 'Insured',
  'signature.solicitingOfficer': 'Soliciting Officer',
  'signature.solicitingOfficer.placeholder': 'This is deemed approved by:',
  'signature.solicitingOfficer.name': 'Roxanne R. Ramiscal',
  'signature.agent': 'Agent',
  'signature.authorizedRepresentative': 'Authorized representative',
  'signature.financialWealthPlanner': 'Financial wealth planner',
  'signature.financialSolutionsConsultant': 'Financial solutions consultant',
  'signature.brokerRepresentativeOfficer': 'Broker Representative Officer',
  'signature.agreeAndContinue': 'Agree and continue',
  'signature.remoteSignature': 'Remote signature',
  'signature.on': 'On',
  'signature.off': 'Off',
  'signature.sSignature': '’s signature',
  'signature.importantNotice':
    'Please read the important notice and agree to the terms and conditions.',
  'signature.importantNoticeSignature':
    'Please read the important notice and agree to the terms and conditions before signature.',
  'signature.viewDetails': 'View details',
  'signature.save': 'Save',
  'signature.lastFewThings': 'Last few things we need to do.',
  'signature.lastFewThingsDetails':
    "Here are declarations you need to agree to and sign before we can proceed.  If you have questions, don't hesitate to ask.",
  'signature.okGetIt': 'Ok, get it',
  'signature.okGotIt': 'Ok, got it',
  'signature.sAgreement': '’s signature',
  'signature.policySummary': 'Your policy summary',
  'signature.regularPlanSum':
    'Here’s a summary of your insurance plan, including your coverage and benefits. You will be paying {{amount}} {{mode}} for {{term}} years.',
  'signature.monthlyADAPlanSum':
    'Here’s the summary of your insurance plan, including your coverage and benefits. You will be paying an initial amount of {{initialAmount}} and your succeeding monthly premium will be {{amount}} for the next {{term}} years.',
  'signature.singlePayPlanSum':
    'Here’s a summary of your insurance plan, including your coverage and benefits. You will be paying a single premium of {{amount}}.',
  'signature.pendingToConfirm': 'We are pending customer to confirm',
  'signature.pendingToConfirmContent':
    'SMS sent. We are pending for customer’s signature. Application will be resume once it is confirmed.',
  'signature.confirmed': 'Confirmed',
  'signature.confirmedContent':
    'Customer has confirmed the sales illustration. We can continue the application journey.',
  'signature.backToHome': 'Back to home',
  'signature.continue': 'Continue',
  'signature.smsSent': 'SMS has been sent',
  'signature.send': 'Send',
  'signature.signatureByCustomer': 'Signature by customer',
  'signature.fullName': 'Full name',
  'signature.email': 'Email',
  'signature.mobileNumber': 'Mobile number',
  'signature.optional': 'Optional',
  'signature.skip': 'Skip',
  'signature.verifyInformation.title': 'Verify your information',
  'signature.verifyInformation.content1':
    'Please ensure your customer has confirmed the accuracy of all information. Once you select "Confirm",',
  'signature.verifyInformation.content2': ' the information cannot be edited.',
  'signature.verifyInformation.content3':
    'and a remote selling email link will be sent to the remote parties.',
  'po.isRemoteSelling': 'Is it remote selling?',
  'po.yes': 'Yes',
  'po.no': 'No',
  'payment.title': 'Payment',
  'payment.yourPlan': 'Your plan',
  'payment.product': 'Product',
  'payment.setForHealth': 'Set for health',
  'payment.initialPaymentDetails': 'Initial payment details',
  'payment.initialPaymentTotal': 'Initial payment total',
  'payment.twoMonthlyPremiumDue': 'Two monthly premiums due',
  'payment.paymentMethod': 'Payment method',
  'payment.dragonPayOnlineBanking': 'DragonPay Online Banking',
  'payment.dragonPayCards': 'DragonPay Cards',
  'payment.cash': 'Cash/Cheque/FWD POS mobile',
  'payment.proceedToPayment': 'Proceed to payment',
  'payment.policyPayment': 'Policy summary',
  'payment.sumAssured': 'Sum Assured',
  'payment.policyTerm': 'Policy term',
  'payment.planType': 'Plan type',
  'payment.paymentFrequency': 'Payment frequency',
  'payment.coverage': 'Coverage',
  'payment.criticalIllness': 'Critical Illness',
  'payment.totalPermanentDisability': 'Total permanent disability',
  'payment.stakeholderDetails': 'Stakeholder details',
  'payment.policyOwner': 'Policy owner',
  'payment.insured': 'Insured',
  'payment.beneficialOwner': 'Beneficial owner',
  'payment.payor': 'Payor',
  'payment.beneficiary': 'Beneficiary',
  'payment.otherMethod': 'Setup other payment method',
  'payment.otcBillsPayment': 'OTC Bills Payment',
  'payment.securityBank': 'Security Bank',
  'payment.metroBank': 'Metrobank',
  'payment.bdo': 'BDO',
  'payment.bpi': 'BPI',
  'payment.lbc': 'LBC Express Branches',
  'payment.unionBank': 'Union Bank',
  'payment.cebuanaLhuillierBranches': 'Cebuana Lhuillier Branches',
  'payment.gcash': 'Gcash',
  'payment.onlineBanking': 'Online Banking',
  'payment.metroBankDirect': 'Metrobank Direct',
  'payment.bancNetOnline': 'BancNet Online',
  'payment.landBank': 'LandBank',
  'payment.dragonPayCreditCardOption': 'Credit Card',
  'payment.fwdPosMobile': 'FWD POS Mobile',
  'payment.paymaya': 'Paymaya',
  'payment.paymentProcessing': 'Please wait, payment is processing',
  'payment.paymentProcessing1':
    'or choose another payment method and try again.',
  'payment.tryAgain': 'Try again',
  'payment.paymentSuccessful': 'Payment successful',
  'payment.yourPolicySummary': 'Your policy summary',
  'payment.annualPremium': 'Annual premium',
  'payment.email': 'Email',
  'payment.dateSubmitted': 'Date submitted',
  'payment.finalStepACR': 'Final step ACR',
  'payment.paymentFailed': 'Payment failed',
  'payment.paymentFailedDes':
    'Payment cannot be proceed, please try again or choose another payment method.',
  'payment.paymentPending': 'Payment status is still pending/for verification',
  'payment.paymentPendingDes':
    'To proceed with submission, kindly select offline payment mode. We will advice you through your Financial Advisor once status is verified.',
  'payment.save': 'Save',
  'payment.gotIt': 'Got it',
  'payment.backToHome': 'Back to home',
  'payment.resendEmail': 'Resend email',
  'payment.resendEmail.successfully':
    'Email resent successfully. You can resend again in 30 minutes.',
  'payment.resendEmail.limited': 'You can resend only once every 30 minutes',
  'payment.resendEmail.failed': 'The email has failed to resend.',
  'payment.refreshPage': 'Refresh page',
  'payment.ok': 'OK',
  'payment.resultDes':
    'We’ll update you thru your financial advisor within 2 working days. Thanks for your patience and understanding.',
  'payment.resultDesPending':
    'Your payment is in progress. We’ll update you thru your financial advisor within 2 working days.',
  'payment.resultTitle': 'Application details complete',
  'payment.php': 'PHP',
  'payment.sbcProduct': 'SBC product/s offered',
  'payment.sbcProduct.allAccessAccount': 'All Access Account',
  'payment.sbcProduct.homeLoan': 'Home Loan',
  'payment.sbcProduct.businessMortgageLoan': 'Business Mortgage Loan',
  'payment.sbcProduct.businessPlusAccount': 'Business Plus Account',
  'payment.sbcProduct.creditCard': 'Credit Card',
  'payment.sbcProduct.autoCarLoan': 'Auto/Car Loan',
  'payment.sbcProduct.timeDeposit': 'Time Deposit or Build up Savings Account',
  'payment.sbcProduct.businessExpressLoan': 'Business Express Loan',
  'payment.sbcProduct.personal': 'Personal Loan',
  'payment.disabled':
    'Complete the remote selling process in order to proceed to payment',
  'payment.resultRemoteSellingPending':
    'You’re almost there, just one more step to go',
  'payment.resultRemoteSellingComplete.title': 'Application complete',
  'payment.resultRemoteSellingComplete.desc':
    'Your financial advisor will give you an update on your application within 2 working days.',
  'payment.resultRemoteSellingPending.pendingPOPI':
    'Pending PO & PI remote selling confirmation',
  'payment.resultRemoteSellingPending.pendingPO':
    'Pending PO remote selling confirmation',
  'payment.resultRemoteSellingPending.pendingPI':
    'Pending PI remote selling confirmation',
  'payment.resultRemoteSellingPending.warningBlock':
    'To proceed, customer must confirm the remote selling link. Please contact IT if there are issues receiving the email.',
  'acr.header': "Agent's confidential report",
  'acr.part1': 'Part I',
  'acr.part2': 'Part II',
  'acr.part3': 'Part III',
  'acr.part4': 'Part IV',
  'acr.part1.relatedToOwner': 'Are you related to the proposed insured/owner?',
  'acr.part1.relationship': 'Relationship',
  'acr.part1.relationship.question': 'Please give relationship',
  'acr.part1.isRelatedToFWP_FSC':
    'Is the proposed insured/owner related to any FWP/FSC of FWD?',
  'acr.part2.politic.title':
    'Politically Exposed Person (PEP) and Related Customer Account (RCA)',
  'acr.part2.isPEPorRCA':
    'Is the policy owner/beneficial owner a PEP or RCA of a PEP?',
  'acr.part2.governmentPosition.question': 'Please identify the exact position',
  'acr.part2.governmentPosition': 'Government positions',
  'acr.part2.governmentPosition.selectOne': 'Please select one',
  'acr.part2.industryAffiliation.title': 'Industry Affiliation',
  'acr.part2.isAffiliatedWithRestrictedEntities':
    'Is the policy owner/beneficial owner affiliated with, has dealt with or provided any services to any of the following: (a) shell company, (b) junket, (c) unlicensed casino & gambling business, (d) unlicensed money lender/changer or remittance business, (e) unlicensed virtual currencies issuer or intermediaries, (f) shell bank, (g) unlicensed bank, and/or (h) unregulated or unregistered charities?',
  'acr.part2.identifyIndustry': 'Please identify the industry',
  'acr.part2.industry': 'Industry',
  'acr.part2.isAffiliatedWithRestrictedIndustries':
    ' Is the policy owner/ beneficial owner affiliated with any of the following: (a) weaponry and armament industry, (b) licensed casino/gambling business, (c) licensed money service business, (d) licensed virtual asset/currency providers, (e) embassy and foreign consulate, (f) quarrying, mining or logging (g) dealers in high value or precious goods/stones/metals, (h) cash intensive business, (i) registered charitable and non-profit organization/foundations?',
  'acr.part3.title':
    'Declaration on the proposed replacement of existing policy/ies',
  'acr.part3.existingInsuranceEnforceChange':
    'Has there been or will there be any change in any existing currently active insurance plans?',
  'acr.part3.reinstatableLapsedInsuranceChange':
    'Has there been or will there be any change in any lapsed insurance that can still be reinstated?',
  'acr.part3.policyLoanPremiumPayment':
    'Will premiums for the insurance be paid by a policy loan from an existing policy?',
  'acr.part4.financialBackground.title':
    'Financial Background of the Policy Owner',
  'acr.part4.policyOwnerBusinessInfo':
    'Provide information on the employment/business/properties of the policy owner',
  'acr.part4.policyOwnerFamilyInfo':
    'Provide information about the family of the policy owner',
  'acr.part4.otherSpecialRemarks': 'Other special remarks',
  'acr.part4.specifyInformation': 'Specify information',
  'acr.part4.secondAgent.title': 'Secondary Agent details (optional)',
  'acr.part4.secondAgent.verify': 'Verify',
  'acr.part4.secondAgent.verify.success': 'Verified successfully',
  'acr.part4.secondAgent.verify.failed': 'Verification fail. Please try again.',
  'acr.part4.secondAgent.code': 'Secondary FWP’s code',
  'acr.part4.secondAgent.name': 'Secondary FWP’s name',
  'acr.part4.declaration.title': 'Declaration',
  'acr.part4.declaration.confirmation':
    'I/We certify that I/we have answered the questions truthfully to the best of my/our knowledge and that I/we have conducted the required “Know Your Customer” process so I/we can provide answers to the questions in this report.\n\nDate of confirmation: {{date}}',
  'acr.skipForLater': 'Skip for later',
  'acr.secondaryAgentCodeMustNotBeTheSame':
    'Secondary agent code must not be the same as the primary agent code',
  'acr.secondaryAgentLicenseAlreadyExpired':
    'Secondary Agent’s license is already expired',
  'acr.secondaryAgentDoesNotHaveTheLicense':
    'Secondary Agent does not have the license to sell this product',
  'acr.caseIdNotFound': 'Case not found while generating new policy number',
  'acr.complete': 'Complete',
  'acr.skipModal.title': 'Application has not been submitted yet',
  'acr.skipModal.description':
    'Please note that this application is not submitted until you have completed the ACR. You can continue the ACR in **“Saved Proposal”** section',
  'acr.skipModal.stayHere': 'Stay here',
  'acr.submitResultModal.title.success': 'Submitted',
  'acr.submitResultModal.description.success': 'Application number: ',
  'acr.submitResultModal.title.error': 'Submission is unsuccessful ',
  'acr.submitResultModal.description.error':
    'Sorry there was a problem submitting your application. Please try submitting again by clicking Retry button.',
  'acr.submitResultModal.retry': 'Retry',
  'acr.submitResultModal.cancel': 'Cancel',
  'acr.submitResultModal.backToHome': 'Back to home',
  'acr.governmentPosition.agentReport.ExecutiveBranch': 'Executive Branch',
  'acr.governmentPosition.agentReport.LocalGovernment': 'Local Government',
  'acr.governmentPosition.agentReport.PoliceandMilitary': 'Police and Military',
  'acr.governmentPosition.agentReport.LegislativeBranch': 'Legislative Branch',
  'acr.governmentPosition.agentReport.JudicialBranch': 'Judicial Branch',
  'addressInfo.townCityProvinceState': 'Town/CityProvince/State',
  'validation.error.required': 'This field is required',
  'validation.error.emptySpace': 'This field cannot contain only space',
  'validation.error.invalidFormat': 'Invalid format',
  'validation.error.specialCharacterNotAllowed':
    'Special characters are not allowed',
  'validation.error.maxLength60': 'Maximum length is 60',
  'validation.error.maxLength14': 'Maximum length is 14',
  'validation.error.maxLength12': 'Maximum length is 12',
  'validation.error.maxLength10': 'Maximum length is 10',
  'validation.error.maxLength13': 'Maximum length is 13',
  'validation.error.maxLength16': 'Maximum length is 16',
  'validation.error.maxLength20': 'Maximum length is 20',
  'validation.error.maxLength50': 'Maximum length is 50',
  'validation.error.maxLength30': 'Maximum length is 30',
  'validation.error.maxLength120': 'Maximum length is 120',
  'validation.error.minLength5': 'Minimum length is 5',
  'validation.error.invalidGender': 'Invalid gender',
  'validation.error.date': 'Invalid date',
  'validation.error.dateExpired': 'Card is already expired',
  'validation.error.invalidCardFormat': 'Invalid credit card number format',
  'paymentMode.Annual': 'Annually',
  'paymentMode.halfYearly': 'Half-yearly',
  'paymentMode.Semi-Annual': 'Semi-annually',
  'paymentMode.Quarterly': 'Quarterly',
  'paymentMode.Monthly': 'Monthly',
  'paymentMode.A': 'Annually',
  'paymentMode.S': 'Semi-annually',
  'paymentMode.Q': 'Quarterly',
  'paymentMode.M': 'Monthly',
  'paymentMode.L': 'Single Premium',
  'paymentMode.singlePremium': 'Single Premium',
  'paymentMode.ANNUAL': 'Annually',
  'paymentMode.HALF_YEAR': 'half-yearly',
  'paymentMode.SEMI_ANNUAL': 'Semi-annually',
  'paymentMode.QUARTERLY': 'Quarterly',
  'paymentMode.MONTHLY': 'Monthly',
  'paymentMode.ONE_TIME': 'Single Premium',
  'paymentModeName.A': 'Annual',
  'paymentModeName.S': 'Semi-annual',
  'paymentModeName.Q': 'Quarterly',
  'paymentModeName.M': 'Monthly',
  'paymentModeName.L': 'Single',
  'beneficiary.tooltip.title': 'Beneficiary information',
  'beneficiary.tooltip.note': 'Please note: ',
  'beneficiary.tooltip.description1':
    'If the "Allocation of Benefit" is left blank, benefits will be shared equally between Beneficiaries of the same type',
  'beneficiary.tooltip.description2':
    'If the "Type Beneficiary” or "Designation” are left blank, the Beneficiary will be defined as "Primary” or "Revocable” respectively.',
  'beneficiary.tooltip.description3':
    'FWD will require prior approval of all Beneficiaries designated as “Irrevocable” before processing any policy transactions.',
  'beneficiary.tooltip.description4':
    'If there are no changes to the Beneficiary or his designation during the lifetime of the Proposed Insured, the designation will automatically be deemed “Irrevocable”',
  'beneficiary.tooltip.description5':
    'If a designated Beneficiary is a minor, a legally recognized guardian or trustee may be required to enact a policy transaction or file a claim.',
  'beneficiary.tooltip.description6':
    'If primary beneficiary is a creditor, benefits will be limited to any outstanding loan at the time of claim. The remaining beneficiaries, if any, will receive the benefits in excess of the outstanding loan. ',
  'beneficiary.tooltip.gotIt': 'Ok, got it',
  'beneficiary.percentage.zeroError': 'Minimum asset allocation is 1%.',
  'beneficiary.percentage.exceededError':
    'The total value has exceeded 100% in total. Please re-adjust the excessive values.',
  'beneficiary.percentage.notEnoughError':
    'Please make sure your assets add up to 100%.',
  'entityDetails.title': 'Entity details',
  'entityDetails.clientType': 'Client Type',
  'entityDetails.entityName': 'Entity name',
  'entityDetails.businessNature': 'Industry / Nature of business',
  'entityDetails.leadSource': 'Lead source',
  'authorizedRepresentative.title': 'Authorized representative details',
  'authorizedRepresentative.position': 'Position/Job title',
  'decision.failed': 'Failed to get decision',
  healthQuestion: 'Health questions - {{role}}',
  healthQuestionCO: 'Health questions - certificate owner',
  noHealthQuestionCO: 'No health questions for certificate owner',
  healthQuestionPC: 'Health questions - person covered',
  noHealthQuestionPC: 'No health questions for person covered',
  noHealthQuestion: 'No health questions for {{name}}',
  healthQuestionFailed: 'Failed to get health questions. ',
  healthQuestionRetry: 'Retry',
  'healthQuestion.failedToGenerate': 'Failed to generate health question',
  'healthQuestion.failedToFinish': 'Failed to finish health question',
  'healthQuestion.height': 'Height',
  'healthQuestion.cm': 'cm',
  'healthQuestion.weight': 'Weight',
  'healthQuestion.kg': 'kg',
  'healthQuestion.index.a': 'a. {{text}}',
  'healthQuestion.index.b': 'b. {{text}}',
  agent: 'Insured',
  'ocr.error.newQuote': 'Create new quick quote',
  'invalidFields.title': 'Invalid fields detected',
  'invalidFields.desc': 'Please update the invalid fields to\ncontinue.',
  'ocr.verified': 'Verified',
  'healthQuestion.incomplete.title': 'Incomplete field in health questions',
  'healthQuestion.incomplete.description':
    'There’s a missing field in your health questions, please go back to complete the form.',
  'healthQuestion.incomplete.action': 'Go back',
};
