import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { Box, Button, H6, LargeBody, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';

import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
const EMandateConfirmationDialog = ({
  visible,
  paymentMethod,
  onDismiss,
  onConfirm,
}: {
  onDismiss: () => void;
  onConfirm: () => void;
  paymentMethod: string;
  visible: boolean;
}) => {
  const { t } = useTranslation(['eApp']);
  const { sizes, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <DialogContainer visible={visible} isTabletMode={isTabletMode}>
      <Box>
        <H6 fontWeight="bold">
          {t('eApp:payment.eMandateConfirmation.title')}
        </H6>
        <Box h={sizes[4]} />
        <LargeBody color={colors.secondary}>
          {t('eApp:payment.eMandateConfirmation.content', { paymentMethod })}
        </LargeBody>
        <Box h={sizes[12]} />
        <Row justifyContent="center">
          <ActionButton
            variant="secondary"
            text={t('eApp:cancel')}
            onPress={onDismiss}
            size="medium"
          />
          <Box w={sizes[4]} />
          <ActionButton
            text={t('eApp:payment.eMandateConfirmation.continue')}
            onPress={onConfirm}
            size="medium"
          />
        </Row>
      </Box>
    </DialogContainer>
  );
};

export default EMandateConfirmationDialog;

const DialogContainer = styled(DialogPhone)<{isTabletMode?: boolean}>(({ theme: { sizes }, isTabletMode }) => {
  return {
    maxWidth: sizes[175],
    backgroundColor: 'white',
    padding: isTabletMode ? sizes[12] : sizes[5],
    marginEnd: isTabletMode ? sizes[50] : sizes[5],
    marginLeft: isTabletMode ? sizes[50] : sizes[5],
  };
});

const ActionButton = styled(Button)({
  flex: 1,
  maxWidth: 200,
});
