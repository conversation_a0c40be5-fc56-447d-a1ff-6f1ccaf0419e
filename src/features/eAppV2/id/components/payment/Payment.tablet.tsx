import { memo } from 'react';
import PaymentFailed from './gateway/PaymentStatus';
import PaymentFormTablet from './form/PaymentForm.tablet';
import usePayment from './hooks/usePayment';
import usePaymentSuccess from './hooks/usePaymentSuccess';

const PaymentTablet = memo(function Payment() {
  const { paymentMethod, paymentResponse, onBackPress } = usePayment();

  usePaymentSuccess({ paymentMethod, paymentResponse });
  if (paymentResponse && paymentResponse.transaction.status !== 'SUCCESS') {
    return (
      <PaymentFailed
        info={paymentResponse}
        paymentMethod={paymentMethod}
        onBackPress={onBackPress}
      />
    );
  }

  // render default payment form if no payment method + transaction id
  return <PaymentFormTablet paymentMethod={paymentMethod} />;
});

export default PaymentTablet;
