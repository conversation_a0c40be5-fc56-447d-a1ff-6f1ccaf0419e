import { useTheme } from '@emotion/react';
import {
  Box,
  ExtraLargeBody,
  ExtraSmallLabel,
  H5,
  Label,
  LargeBody,
  Row,
} from 'cube-ui-components';
import React from 'react';

const PaymentItemDetail = ({
  title,
  content,
  oldContent,
  isCurrency,
  currency,
  isHighlight,
}: {
  isHighlight?: boolean;
  currency?: string;
  isCurrency?: boolean;
  title: string;
  content: string;
  oldContent?: string;
}) => {
  const { sizes, colors } = useTheme();
  return (
    <Row
      marginBottom={sizes[2]}
      justifyContent="space-between"
      alignItems="center">
      <Box flex={1}>
        <Label fontWeight="normal" color={colors.palette.fwdGreyDarkest}>
          {title}
        </Label>
      </Box>
      <Box flex={1} alignItems="flex-end">
        {isCurrency ? (
          <>
            <Row alignItems="center">
              {isHighlight ? (
                <LargeBody fontWeight="normal" color={colors.primary}>
                  {currency}
                </LargeBody>
              ) : (
                <ExtraSmallLabel>{currency}</ExtraSmallLabel>
              )}
              <Box width={sizes[1] - 2} />
              {isHighlight ? (
                <H5 fontWeight="bold" color={colors.primary}>
                  {content}
                </H5>
              ) : (
                <ExtraLargeBody fontWeight="medium" color={colors.secondary}>
                  {content}
                </ExtraLargeBody>
              )}
            </Row>
            {Boolean(oldContent) && (
              <Label
                style={{ textDecorationLine: 'line-through' }}
                color={colors.palette.fwdGreyDarkest}>
                {currency} {oldContent}
              </Label>
            )}
          </>
        ) : (
          <Label fontWeight="medium" color={colors.secondary}>
            {content}
          </Label>
        )}
      </Box>
    </Row>
  );
};

export default PaymentItemDetail;
