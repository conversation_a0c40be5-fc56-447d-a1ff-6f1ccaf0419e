import { useTheme } from '@emotion/react';
import {
  Box,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import { ClosingAgentQuestion } from 'features/eAppV2/id/constants/consent';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface QuestionProps {
  question: ClosingAgentQuestion;
  index: number;
  isLastQuestion?: boolean;
  answer: 'yes' | 'no' | null;
  details?: string;
  onAnswerChange: (answer: 'yes' | 'no' | null) => void;
  onDetailsChange: (details: string) => void;
}

export const Question = ({
  question,
  index,
  isLastQuestion = false,
  answer,
  details,
  onAnswerChange,
  onDetailsChange,
}: QuestionProps) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);

  return (
    <Box
      py={space[4]}
      borderBottom={isLastQuestion ? 0 : 1}
      borderColor={colors.palette.fwdGrey[100]}>
      <Row alignItems="flex-start" gap={space[2]}>
        <LargeLabel>{`${index + 1}.`}</LargeLabel>
        <LargeLabel style={{ flex: 1 }}>
          {t(question.question as keyof typeof t)}
        </LargeLabel>
      </Row>
      <Box marginTop={space[4]}>
        <RadioButtonGroup value={answer} onChange={onAnswerChange}>
          <Row gap={space[8]}>
            <RadioButton value="yes" label="Yes" />
            <RadioButton value="no" label="No" />
          </Row>
        </RadioButtonGroup>
      </Box>
      {answer === 'no' && question.isNoReasonRequired && (
        <Box marginTop={space[4]}>
          <TextField
            value={details || ''}
            onChangeText={onDetailsChange}
            placeholder="Please provide details"
            style={{ flex: 1 }}
          />
        </Box>
      )}
    </Box>
  );
};
