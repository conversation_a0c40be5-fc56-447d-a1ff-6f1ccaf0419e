import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  PictogramIcon,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
export type ConsentSectionItem =
  | string
  | {
      title: string;
      content?: string[];
    };

interface ConsentSectionTabletProps {
  title: string;
  items: ConsentSectionItem[];
  intro?: string;
  checkboxLabel?: string;
  checked?: boolean;
  contentGap?: number;
  onCheckboxChange?: (checked: boolean) => void;
}

export default function ConsentSectionTablet({
  title,
  items,
  intro,
  checkboxLabel,
  checked,
  onCheckboxChange,
  contentGap,
}: ConsentSectionTabletProps) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  return (
    <Box
      marginRight={space[8]}
      backgroundColor={colors.palette.white}
      marginTop={space[4]}
      borderRadius={16}
      p={space[6]}
      gap={space[5]}
      mb={space[5]}>
      <Row alignItems="center" gap={space[2]}>
        <PictogramIcon.DocumentSign2 size={40} />
        <H6 fontWeight="bold">{title}</H6>
      </Row>
      <Box>
        {intro && (
          <Box mb={space[5]}>
            <LargeBody>{intro}</LargeBody>
          </Box>
        )}
        {checkboxLabel && typeof checked === 'boolean' && onCheckboxChange && (
          <Row alignItems="center" gap={space[3]} pb={space[5]}>
            <Checkbox
              labelStyle={{ fontSize: 16, fontWeight: '700' }}
              label={checkboxLabel}
              checked={checked}
              onChange={onCheckboxChange}
            />
          </Row>
        )}
        {items?.map((item, index) => {
          const indexWithFormat = `${index + 1}.`;
          if (typeof item === 'string') {
            return (
              <Row key={index} mb={contentGap} gap={space[2]}>
                <LargeBody>{indexWithFormat}</LargeBody>
                <LargeBody>{t(item as keyof typeof t)}</LargeBody>
              </Row>
            );
          } else {
            return (
              <Box key={index} mb={contentGap}>
                <Row gap={space[2]}>
                  <LargeBody>{indexWithFormat}</LargeBody>
                  <LargeBody>{t(item.title as keyof typeof t)}</LargeBody>
                </Row>
                {item.content && (
                  <Box ml={space[7]} mt={space[1]} gap={space[1]}>
                    {item?.content?.map((c, i) => (
                      <LargeBody key={i}>
                        {String.fromCharCode(97 + i)}. {t(c as keyof typeof t)}
                      </LargeBody>
                    ))}
                  </Box>
                )}
              </Box>
            );
          }
        })}
      </Box>
    </Box>
  );
}
