import { useTheme } from '@emotion/react';
import { Box, H6, LargeBody, PictogramIcon, Row } from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { statements } from 'features/eAppV2/id/constants/consent';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';

interface StatementPowerOfAttorneyTabletProps {
  onNext: () => void;
}

const StatementPowerOfAttorneyTablet = ({
  onNext,
}: StatementPowerOfAttorneyTabletProps) => {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();

  // This section doesn't have validation requirements
  const isValid = true;
  const totalIncompleteRequiredFields = 0;

  return (
    <>
      <ScrollView
        contentContainerStyle={{
          paddingBottom: space[30],
        }}>
        <Box
          mt={space[6]}
          p={space[6]}
          bgColor={colors.palette.white}
          mr={space[8]}
          borderRadius={16}
          gap={space[5]}>
          <Row alignItems="center" gap={space[2]}>
            <PictogramIcon.DocumentSign2 size={40} />
            <H6 fontWeight="bold">
              {t('eApp:consents.statementAndPowerOfAttorney')}
            </H6>
          </Row>
          <LargeBody>
            {t('eApp:consents.statementAndPowerOfAttorney.subTitle')}
          </LargeBody>
          <Box>
            {statements.map((item, index) => {
              const indexWithFormat = `${index + 1}.`;
              if (typeof item === 'string') {
                return (
                  <Row key={index} gap={space[2]}>
                    <LargeBody>{indexWithFormat}</LargeBody>
                    <LargeBody>{t(item as keyof typeof t)}</LargeBody>
                  </Row>
                );
              } else {
                return (
                  <Box key={index}>
                    <Row gap={space[2]}>
                      <LargeBody>{indexWithFormat}</LargeBody>
                      <LargeBody>{t(item.title as keyof typeof t)}</LargeBody>
                    </Row>
                    {item.content && (
                      <Box ml={space[7]} mt={space[1]} gap={space[1]}>
                        {item?.content?.map((c, i) => (
                          <LargeBody key={i}>
                            {String.fromCharCode(97 + i)}.{' '}
                            {t(c as keyof typeof t)}
                          </LargeBody>
                        ))}
                      </Box>
                    )}
                  </Box>
                );
              }
            })}
          </Box>
        </Box>
      </ScrollView>
      <EAppFooterTablet
        progressLock="consents-"
        onPrimaryPress={onNext}
        primaryDisabled={!isValid}
        primaryLoading={false}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        isAbsolutePositioned
      />
    </>
  );
};

export default StatementPowerOfAttorneyTablet;
