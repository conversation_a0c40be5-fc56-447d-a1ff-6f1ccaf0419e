import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import BasicModal from 'components/BasicModal';
import { Box, Button, H6, LargeBody, Row } from 'cube-ui-components';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React, { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Dimensions } from 'react-native';
import { RootStackParamList } from 'types';
import { DukcapilValidationResponse } from 'types/dukcapil';
import { dateFormatUtil } from 'utils/helper/formatUtil';

type Props = {
  visible: boolean;
  onClose: () => void;
  result?: DukcapilValidationResponse;
};

// if error code is SOME_DATA_MISMATCH, show mismatched fields and not allow to proceed
// else still allow to update and proceed

const DukcapilValidationPopup = ({ visible, onClose, result }: Props) => {
  const { space, colors, borderRadius } = useTheme();
  const { width } = Dimensions.get('window');
  const { t } = useTranslation(['eApp']);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { control } = useFormContext();

  const [primaryId, gender, fullName, dob] = useWatch({
    control,
    name: ['primaryId', 'gender', 'fullName', 'dob'],
  });

  const { data: optionList } = useGetOptionList<'id'>();

  const fieldsMapping: Record<string, { label: string; value: string }> = {
    primaryId: {
      label: t('eApp:idNumber'),
      value: primaryId,
    },
    gender: {
      label: t('eApp:gender'),
      value:
        optionList?.GENDER?.options?.find(option => option?.value === gender)
          ?.label ?? '',
    },
    fullName: {
      label: t('eApp:fullName'),
      value: fullName,
    },
    dob: {
      label: t('eApp:dateOfBirth'),
      value: dateFormatUtil(dob),
    },
  };

  const description = useMemo(() => {
    switch (result?.errorMsgCode) {
      case 'NRIC_MISMATCH':
        return t('eApp:dukcapilValidationPopup.NRIC_MISMATCH');
      case 'RESIDENCY_STATUS_DECEASED':
        return t('eApp:dukcapilValidationPopup.RESIDENCY_STATUS_DECEASED');
      case 'RESIDENCY_STATUS_INACTIVE':
        return t('eApp:dukcapilValidationPopup.RESIDENCY_STATUS_INACTIVE');
      case 'SOME_DATA_MISMATCH':
        return t('eApp:dukcapilValidationPopup.SOME_DATA_MISMATCH');

      default:
        return t('eApp:dukcapilValidationPopup.NRIC_MISMATCH');
    }
  }, [result]);

  const mismatchedFields = useMemo(() => {
    if (!result) return [];
    switch (result?.errorMsgCode) {
      case 'NRIC_MISMATCH':
      case 'RESIDENCY_STATUS_DECEASED':
      case 'RESIDENCY_STATUS_INACTIVE':
        return ['primaryId'].map(key => fieldsMapping[key]);

      case 'SOME_DATA_MISMATCH':
        return result?.mismatches
          ?.map(key => fieldsMapping?.[key])
          .filter(Boolean);

      default:
        return [];
    }
  }, [result]);

  const buttonProps = useMemo(() => {
    switch (result?.errorMsgCode) {
      case 'SOME_DATA_MISMATCH':
        return {
          text: t('eApp:dukcapilValidationPopup.backToHome'),
          onPress: () => {
            navigation.reset({
              index: 0,
              routes: [{ name: 'Main' }],
            });
          },
        };

      case 'NRIC_MISMATCH':
      case 'RESIDENCY_STATUS_DECEASED':
      case 'RESIDENCY_STATUS_INACTIVE':
      default:
        return {
          text: t('eApp:dukcapilValidationPopup.backToEdit'),
          onPress: onClose,
        };
    }
  }, [result]);

  return (
    <BasicModal visible={visible} onRequestClose={onClose}>
      <Box
        w={width - 2 * space[40]}
        my={space[25]}
        mx={space[40]}
        gap={space[4]}
        backgroundColor={colors.background}
        borderRadius={borderRadius['large']}
        p={space[12]}>
        <H6 fontWeight="bold">
          {result?.errorMsgCode === 'SOME_DATA_MISMATCH'
            ? t('eApp:dukcapilValidationPopup.cannotProceed')
            : t('eApp:dukcapilValidationPopup.mismatchDetected')}
        </H6>
        <Row
          alignItems="center"
          flexWrap="wrap"
          gap={space[2]}
          justifyContent="space-between"
          paddingX={space[2]}>
          {mismatchedFields?.map(item => (
            <Row
              flexBasis={'45%'}
              alignItems="center"
              gap={space[2]}
              key={item.label}>
              <Box
                w={12}
                h={12}
                borderRadius={borderRadius['full']}
                backgroundColor={colors.error}
              />
              <LargeBody color={colors.error}>
                {item.label}: {item.value}
              </LargeBody>
            </Row>
          ))}
        </Row>

        <LargeBody>{description}</LargeBody>

        <Button
          {...buttonProps}
          style={{ width: '30%', marginTop: space[2], alignSelf: 'center' }}
        />
      </Box>
    </BasicModal>
  );
};

export default DukcapilValidationPopup;
