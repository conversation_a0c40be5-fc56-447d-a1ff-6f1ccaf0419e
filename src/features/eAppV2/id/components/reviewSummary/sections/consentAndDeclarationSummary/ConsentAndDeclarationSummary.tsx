import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  Box,
  Column,
  H7,
  Icon,
  LargeBody,
  PictogramIcon,
  Row,
} from 'cube-ui-components';
import PressableSection from 'features/eAppV2/common/components/review/pressableSection/PressableSection';
import ReviewSection from 'features/eAppV2/common/components/review/ReviewSection';
import RowInfoField, {
  RowInfoFieldProps,
} from 'features/eAppV2/common/components/review/rowInfoField/RowInfoField';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import useRpqPdf from 'features/proposal/components/RpqQuestionForm/hooks/useRpqPdf';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import useToggle from 'hooks/useToggle';
import React, { Fragment, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { Party, PartyRole } from 'types/party';

export interface DeclarationQuestionSummaryProps {
  index?: number;
  question: string;
  answer?: string;
  subQuestion?: string;
  subAnswer?: string;
  info?: RowInfoFieldProps[];
  additional?: DeclarationSummaryAdditionalInfoProps[];
}

interface DeclarationSummaryAdditionalInfoProps {
  title: string;
  info?: RowInfoFieldProps[];
}

export const DeclarationSummaryAdditionalInfo = ({
  additional,
}: {
  additional?: DeclarationSummaryAdditionalInfoProps;
}) => {
  const { sizes, colors, borderRadius } = useTheme();

  const renderContent = () => {
    return additional?.info?.map((item, index) => {
      return (
        <RowInfoField
          key={index}
          {...item}
          isLastItem={index === (additional?.info?.length ?? 0) - 1}
        />
      );
    });
  };

  return (
    <Column
      flex={1}
      gap={sizes[2]}
      backgroundColor={colors.palette.fwdGrey[20]}
      padding={sizes[4]}
      borderRadius={borderRadius.medium}>
      <H7 fontWeight="bold">{additional?.title}</H7>
      <Box height={sizes[4]} />
      {renderContent()}
    </Column>
  );
};

export const DeclarationSummaryInfo = ({
  info,
}: {
  info?: RowInfoFieldProps[];
}) => {
  const { sizes, colors, borderRadius } = useTheme();

  const renderContent = () => {
    return info?.map((item, index) => {
      return (
        <RowInfoField
          key={index}
          {...item}
          isLastItem={index === info.length - 1}
        />
      );
    });
  };

  return (
    <Column
      flex={1}
      gap={sizes[2]}
      backgroundColor={colors.palette.fwdGrey[20]}
      padding={sizes[4]}
      borderRadius={borderRadius.medium}>
      {renderContent()}
    </Column>
  );
};

export const DeclarationSummaryQuestion = ({
  index,
  question,
  answer,
  subQuestion,
  subAnswer,
  additional,
  info,
}: DeclarationQuestionSummaryProps) => {
  const { sizes, colors } = useTheme();

  return (
    <Column>
      <Row>
        {index && (
          <Box width={sizes[5]}>
            <LargeBody color={colors.placeholder}>{`${index}.`}</LargeBody>
          </Box>
        )}
        <Column>
          <LargeBody color={colors.placeholder}>{question}</LargeBody>
          {answer && (
            <Column>
              <Box height={sizes[2]} />
              <LargeBody>{answer}</LargeBody>
            </Column>
          )}
          {subQuestion && (
            <Column>
              <Box height={sizes[4]} />
              <LargeBody color={colors.placeholder}>{subQuestion}</LargeBody>
              <Box height={sizes[2]} />
              <LargeBody>{subAnswer}</LargeBody>
            </Column>
          )}
        </Column>
      </Row>
      {additional?.map(item => {
        return (
          <Column>
            <Box height={sizes[4]} />
            <DeclarationSummaryAdditionalInfo additional={item} />
          </Column>
        );
      })}
      {info && (
        <Column>
          <Box height={sizes[4]} />
          <DeclarationSummaryInfo info={info} />
        </Column>
      )}
    </Column>
  );
};

export const DeclarationSummarySeparator = () => {
  const { sizes, colors } = useTheme();

  return (
    <Column>
      <Box h={sizes[4]} />
      <Box h={1} backgroundColor={colors.palette.fwdGrey[100]} />
      <Box h={sizes[4]} />
    </Column>
  );
};

export default function ConsentAndDeclarationSummary() {
  const { t } = useTranslation(['eApp', 'pdfViewer']);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors } = useTheme();
  const quotation = useSelectedQuotation();
  const [pdfVisible, showPdf, hidePdf] = useToggle();
  const { caseObj } = useGetActiveCase();

  const { pdfGeneratorRef } = useRpqPdf();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const getPartyRole = (party: Party): string => {
    switch (true) {
      case isRole(party, PartyRole.PROPOSER):
        return t('eApp:bar.policyOwner');
      case isRole(party, PartyRole.PAYER):
        return t('eApp:bar.payor');
      case isRole(party, PartyRole.RENEWAL_PAYER):
        return t('eApp:bar.renewalPayer');
      default:
        return '';
    }
  };

  const consents = useMemo(() => {
    const proposerConsentArray = caseObj?.application?.proposerConsent || [];

    const fatcaEntries = proposerConsentArray.map((item, idx: number) => {
      const party = caseObj?.parties?.find(p => p?.id === item?.partyId);
      if (party) {
        const title = `${t('eApp:review.declaration.fatca')} - ${getPartyRole(
          party,
        )}`;
        return {
          title,
          screen: 'FatcaReview' as keyof RootStackParamList,
          params: { partyId: item.partyId, title },
        };
      }
      return null;
    });

    return (
      [
        ...fatcaEntries,
        {
          title: t('eApp:declaration.passionSurvey.title'),
          screen: 'PassionSurveyReview' as keyof RootStackParamList,
        },
        {
          title: t('eApp:consents.underwritingDecision'),
          screen: 'UnderwritingDecisionReview' as keyof RootStackParamList,
        },
        {
          title: t('eApp:consents.closingAgent'),
          screen: 'ClosingAgentReview' as keyof RootStackParamList,
        },
        {
          title: t('eApp:consents.statementAndPowerOfAttorney'),
          screen:
            'StatementAndPowerOfAttorneyReview' as keyof RootStackParamList,
        },
        {
          title: t('eApp:consents.temporaryCoverage'),
          screen: 'TemporaryCoverageReview' as keyof RootStackParamList,
        },
      ] as const
    ).filter(Boolean) as {
      title: string;
      screen:
        | 'FatcaReview'
        | 'PassionSurveyReview'
        | 'UnderwritingDecisionReview'
        | 'ClosingAgentReview'
        | 'StatementAndPowerOfAttorneyReview'
        | 'TemporaryCoverageReview';
      params?: { index: number };
      callback?: () => void;
    }[];
  }, [
    quotation,
    showPdf,
    t,
    caseObj?.application?.proposerConsent,
    caseObj?.parties,
    caseObj?.havePayer,
  ]);

  return (
    <>
      <ReviewSection title={t('eApp:review.declaration.title')}>
        {consents.map(({ title, screen, callback, params }, idx) => (
          <Fragment key={title}>
            <PressableSection
              onPress={() => {
                if (callback) {
                  callback();
                } else {
                  (navigation.navigate as any)(screen, params);
                }
              }}
              icon={
                isTabletMode ? (
                  <PictogramIcon.MedicalForm2 size={28} />
                ) : (
                  <Icon.DocumentCopy size={24} />
                )
              }
              sectionName={title}
              actionLabel={t('eApp:review.view')}
            />
            {idx !== consents.length - 1 && (
              <Box h={1} bgColor={colors.palette.fwdGrey[100]} />
            )}
          </Fragment>
        ))}
      </ReviewSection>
      <PdfViewer
        title={t('pdfViewer:RPQ')}
        onClose={hidePdf}
        visible={pdfVisible}
        pdfGenerator={pdfGeneratorRef.current}
      />
    </>
  );
}
