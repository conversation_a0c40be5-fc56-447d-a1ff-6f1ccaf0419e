import { useTheme } from '@emotion/react';
import { Box, LargeBody, Row } from 'cube-ui-components';
import { closingAgentQuestions } from 'features/eAppV2/id/constants/consent';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import ScreenHeaderTablet from 'navigation/components/ScreenHeader/tablet';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';

export default function ClosingAgentReview() {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { caseObj } = useGetActiveCase();

  const formatAnswer = (answer: string | undefined): string => {
    if (!answer) return '--';
    return answer.toLowerCase() === 'yes' ? t('eApp:yes') : t('eApp:no');
  };

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeaderTablet
        customTitle={t('eApp:consents.closingAgent')}
        route={'ClosingAgentReview'}
        isLeftCrossBackShown={isTabletMode}
        isLeftArrowBackShown={!isTabletMode}
      />
      <ScrollView>
        <Box
          flex={1}
          backgroundColor={colors.background}
          p={space[6]}
          gap={space[5]}>
          {closingAgentQuestions.map((item, index) => {
            const answer =
              caseObj?.application?.proposerConsent?.[0]?.closingAgentAnswers?.[
                index
              ]?.answer;
            return (
              <Box key={index}>
                <Row mb={space[2]}>
                  <LargeBody color={colors.palette.fwdGreyDarker}>
                    {index + 1}.
                  </LargeBody>
                  <LargeBody color={colors.palette.fwdGreyDarker}>
                    {t(item.question as keyof typeof t)}
                  </LargeBody>
                </Row>
                <LargeBody>{formatAnswer(answer)}</LargeBody>
                {answer === 'no' && item?.isNoReasonRequired && (
                  <Box mt={space[2]} gap={space[2]}>
                    <LargeBody color={colors.palette.fwdGreyDarker}>
                      {t('eApp:review.provideDetails')}
                    </LargeBody>
                    <LargeBody>
                      {
                        caseObj?.application?.proposerConsent?.[0]
                          ?.closingAgentAnswers?.[index]?.details
                      }
                    </LargeBody>
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>
      </ScrollView>
    </Box>
  );
}
