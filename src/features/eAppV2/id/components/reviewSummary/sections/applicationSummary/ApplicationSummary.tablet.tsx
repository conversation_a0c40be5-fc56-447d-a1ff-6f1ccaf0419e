import { useTheme } from '@emotion/react';
import {
  Box,
  Center,
  ExtraLargeBody,
  LargeBody,
  Row,
} from 'cube-ui-components';

import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import ReviewSection from 'features/eAppV2/common/components/review/ReviewSection';
import AdditionalProtectionSection from 'features/proposal/components/SalesIllustrationDetailsModal/AdditionalProtectionSection';
import BasePlanSection from 'features/proposal/components/SalesIllustrationDetailsModal/BasePlanSection';
import FundAllocationSection from 'features/proposal/components/SalesIllustrationDetailsModal/FundAllocationSection';
import TopUpSection from 'features/proposal/components/SalesIllustrationDetailsModal/TopUpSection';
import { useSIFormSummary } from 'features/proposal/components/SalesIllustrationDetailsModal/useSIFormSummary';

interface SectionType {
  step: number;
  title: string;
  component: React.JSX.Element;
}

export default function ApplicationSummaryTablet({
  sectionTitle,
}: {
  sectionTitle: string;
}) {
  const { space, borderRadius, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { topUpRiders, nonTopUpRiders, funds, charityOrgList } =
    useSIFormSummary();

  const sections = useMemo(
    () =>
      [
        {
          title: t('eApp:review.basePlanSummary'),
          value: 'basePlan',
          component: (
            <BasePlanSection productLogoWidth={243} productLogoHeight={80} />
          ),
        },
        // hide when it doesn't have any top-up rider
        topUpRiders.length
          ? {
              title: t('eApp:review.topUp'),
              component: <TopUpSection topUpRiders={topUpRiders} />,
            }
          : null,
        // hide when it doesn't have any common rider
        nonTopUpRiders.length
          ? {
              title: t('eApp:review.additionalProtection'),
              component: (
                <AdditionalProtectionSection
                  charityOrgList={charityOrgList ?? []}
                />
              ),
            }
          : null,
        // hide when it doesn't have any fund allocation
        funds.length
          ? {
              title: t('eApp:review.fundAllocation'),
              component: <FundAllocationSection funds={funds} />,
            }
          : null,
      ]
        .filter((item): item is SectionType => Boolean(item))
        .map((item, index) => ({ ...item, step: index + 1 })),
    [charityOrgList, funds, topUpRiders, nonTopUpRiders.length, t],
  );

  return (
    <ReviewSection title={sectionTitle}>
      <Box mb={space[5]}>
        {sections.map(section => (
          <React.Fragment key={section.title}>
            <Row alignItems="center" mt={space[6]}>
              <Box mr={space[3]}>
                {section.step && (
                  <Center
                    width={38}
                    height={38}
                    borderRadius={borderRadius['x-large']}
                    backgroundColor={colors.palette.fwdOrange[20]}
                    justifyContent={'center'}
                    alignItems={'center'}>
                    <LargeBody>{section.step}</LargeBody>
                  </Center>
                )}
              </Box>
              <ExtraLargeBody fontWeight="bold">{section.title}</ExtraLargeBody>
            </Row>
            {section.component}
          </React.Fragment>
        ))}
      </Box>
    </ReviewSection>
  );
}
