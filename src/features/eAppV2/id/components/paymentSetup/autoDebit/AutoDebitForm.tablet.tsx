import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import Input from 'components/Input';
import { Box, Column, H6, Row, TextField } from 'cube-ui-components';
import {
  BANK_CODE,
  RenewalPaymentSetupForm,
} from 'features/eAppV2/id/validations/renewalPaymentSetupValidation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';
import { BankName } from 'types/optionList';

const AutoDebitForm = () => {
  const { t } = useTranslation(['eApp']);
  const { colors, space, borderRadius } = useTheme();
  const { control, trigger, setValue } =
    useFormContext<RenewalPaymentSetupForm>();
  const { data: optionList } = useGetOptionList<'id'>();

  const handleOnBankChange = (value: string | null) => {
    setValue('bankNameForRenewal', value ?? undefined);
    trigger('accountNumberForRenewal');
  };

  const allowedBanks =
    optionList?.BANK_CODE?.options.filter(bank =>
      Object.values(BANK_CODE).includes(bank.value as BANK_CODE),
    ) ?? [];

  return (
    <>
      <Wrapper>
        <TitleContainer>
          <H6 fontWeight="bold" style={{ flex: 1 }}>
            {t('eApp:paymentSetup.autoDirectDebitArrangement.accountInfo')}
          </H6>
        </TitleContainer>
        <Column
          backgroundColor={colors.background}
          padding={space[6]}
          borderRadius={borderRadius.large}>
          <Row gap={space[6]} flex={1}>
            <Input
              control={control}
              as={TextField}
              name="accountHolderForRenewal"
              label={t(
                'eApp:paymentSetup.autoDirectDebitArrangement.accountHolderName',
              )}
              style={styles.input}
              shouldHighlightOnUntouched={value => !value}
            />
            <Input
              control={control}
              as={Autocomplete<BankName, string>}
              name="bankNameForRenewal"
              label={t('eApp:paymentSetup.autoDirectDebitArrangement.bankName')}
              data={allowedBanks}
              getItemValue={item => item.value}
              getItemLabel={item => item.label}
              style={styles.input}
              shouldHighlightOnUntouched={value => !value}
              onChange={handleOnBankChange}
            />
          </Row>
          <Box h={space[6]} />
          <Row gap={space[6]} flex={1}>
            <Input
              control={control}
              as={TextField}
              name="accountNumberForRenewal"
              label={t(
                'eApp:paymentSetup.autoDirectDebitArrangement.bankAccountNumber',
              )}
              style={styles.input}
              shouldHighlightOnUntouched={value => !value}
            />
            <Box flex={1} />
          </Row>
        </Column>
      </Wrapper>
    </>
  );
};

export default AutoDebitForm;

const styles = StyleSheet.create({
  input: {
    flex: 1,
  },
});

const Wrapper = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    marginBottom: space[5],
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    overflow: 'hidden',
  }),
);

const TitleContainer = styled(Row)(
  ({ theme: { space, colors, borderRadius } }) => ({
    gap: space[1],
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: space[6],
    paddingTop: space[6],
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
  }),
);
