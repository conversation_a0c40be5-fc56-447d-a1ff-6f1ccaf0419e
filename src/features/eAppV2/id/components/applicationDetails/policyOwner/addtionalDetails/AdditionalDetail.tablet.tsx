import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import Input from 'components/Input';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import { Box, PictogramIcon, Row, TextField } from 'cube-ui-components';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import {
  additionalDetailsDefaultValue,
  additionalDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/additionalDetails';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

export default function AdditionalDetails() {
  const { t } = useTranslation('eApp');
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList<'id'>();

  const { control, watch } = useFormContext();

  const isDone = useSchemaValid(
    control,
    additionalDetailsDefaultValue,
    additionalDetailsSchema,
  );

  const sourceOfFund = watch('sourceOfFund');
  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('additionalDetails')}
      icon={<PictogramIcon.InformationDocument size={40} />}
      isDone={isDone}>
      <Box px={space[6]} gap={space[5]} pt={space[5]}>
        <Row gap={space[6]}>
          <Input
            control={control}
            as={Autocomplete<{ label: string; value: string }, string>}
            name="sourceOfFund"
            data={optionList?.SOURCE_OF_FUND?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('application.owner.sourceOfFund')}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            style={eAppCommonStyles.tabletTextField}
          />
          <Input
            control={control}
            as={TextField}
            name="otherSource"
            label={t('otherSourceOfFund')}
            disabled={sourceOfFund !== 'OT'}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            style={eAppCommonStyles.tabletTextField}
          />
        </Row>
        <Row gap={space[6]}>
          <Input
            control={control}
            as={TextField}
            name="processPlace"
            label={t('processPlace')}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            style={eAppCommonStyles.tabletTextField}
          />
          <View style={{ flex: 1 }} />
        </Row>
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
