import { useEffect, useMemo } from 'react';
import { useController, useFormContext, useWatch } from 'react-hook-form';
import { useGetOptionList } from 'hooks/useGetOptionList';
import {
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/payorOccupationDetails';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';

const usePayorOccupationDetails = () => {
  const { control, setValue, trigger, getValues } = useFormContext();
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'id'>();

  const occupation = useWatch({
    name: 'occupation',
    control: control,
  });

  const occupationSector = useWatch({
    name: 'occupationSector',
    control: control,
  });

  const industry = useWatch({
    name: 'industry',
    control: control,
  });

  const occupationOptions = useMemo(() => {
    return optionList?.OCCUPATION?.options.filter(
      item => item.industry === industry,
    );
  }, [industry, optionList?.OCCUPATION?.options]);

  const occupationSectorOptions = useMemo(() => {
    const industryOption = optionList?.INDUSTRY?.options.find(
      item => item.value === industry,
    );
    return optionList?.OCCUPATION_SECTOR?.options.filter(
      item =>
        item.OCCUPATION_SECTOR_HAVE_INCOME ===
        industryOption?.dependencyTrigger,
    );
  }, [industry, optionList?.OCCUPATION_SECTOR?.options]);

  const occupationPositionOptions = useMemo(() => {
    return optionList?.OCCUPATION_POSITION?.options.filter(
      item => item.occupationSector === occupationSector,
    );
  }, [occupationSector, optionList?.OCCUPATION_POSITION?.options]);

  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({ control, name: 'occupationGroup' });

  const { occupationClass, occupationGroup } = useOccupationClass(occupation);

  useEffect(() => {
    onChangeOccupationGroup(occupationGroup);
  }, [occupationGroup, onChangeOccupationGroup]);

  const isValid = useSchemaValid(
    control,
    occupationDetailsDefaultValue,
    occupationDetailsSchema,
  );

  return {
    control,
    setValue,
    getValues,
    trigger,
    isValid,
    optionList,
    isLoadingOptionList,
    occupationClass,
    occupationGroup,
    occupationOptions,
    occupationSectorOptions,
    occupationPositionOptions,
  };
};

export default usePayorOccupationDetails;
