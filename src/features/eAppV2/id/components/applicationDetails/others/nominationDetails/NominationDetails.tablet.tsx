import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, LargeBody, Row } from 'cube-ui-components';
import RemoveFormConfirmationDialog from 'features/customerFactFind/components/RemoveFormConfirmationDialog';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import {
  nominationInfoDefaultValue,
  nominationInfoSchema,
} from 'features/eAppV2/id/validations/applicationDetails/nomination/nominationInfoValidation';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useDeleteParty } from 'hooks/useParty';
import React, {
  ForwardedRef,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { FormProvider, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import NominationForm, {
  NominationFormRef,
} from './nominationForm/NominationForm';
import PayablePercentageArrangement from './payablePercentageArrangement/PayablePercentageArrangement';
import { useNominationDetails } from './useNominationDetails';

const MAX_ALLOWED_NOMINEES = 5;

interface Props {
  onNext: () => void;
}

export type NominationDetailsRef = { expand?: () => void };

const NominationDetailsTablet = forwardRef(function NominationDetails(
  { onNext }: Props,
  ref: ForwardedRef<NominationDetailsRef>,
) {
  const { space, colors } = useTheme();

  const { t } = useTranslation(['eApp']);

  const [removeConfirmationVisible, setRemoveConfirmationVisible] =
    useState(false);
  const indexToRemove = useRef(-1);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: deleteParty, isLoading: isDeletingParty } =
    useDeleteParty();

  const { form, nominees, saveNomination, append, remove, update } =
    useNominationDetails();

  const hasReachedMaximum = nominees.length >= MAX_ALLOWED_NOMINEES;

  const nominationRef = useRef<(NominationFormRef | null)[]>([]);

  const expandAllNominationForm = () => {
    nominationRef?.current?.forEach(element => {
      element?.expand?.();
    });
  };

  useImperativeHandle(ref, () => ({
    expand: expandAllNominationForm,
  }));

  const showRemoveConfirmationDialog = useCallback(
    (index: number) => {
      indexToRemove.current = index;
      setRemoveConfirmationVisible(true);
    },
    [setRemoveConfirmationVisible],
  );

  const addNomination = useCallback(() => {
    if (!hasReachedMaximum) {
      append({
        ...nominationInfoDefaultValue.nominees[0],
        allocation: '0',
      });
    }
  }, [append, hasReachedMaximum]);

  const allocations = useWatch({
    name: nominees.map(
      (_, idx) => `nominees.${idx}.allocation`,
    ) as `nominees.${number}.allocation`[],
    control: form.control,
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      schema: nominationInfoSchema,
      watch: form.watch,
      control: form.control,
      scrollRef,
      scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  const handleOnSubmit = async () => {
    await saveNomination();
    onNext();
  };

  return (
    <FormProvider {...form}>
      <ScrollViewContainer
        ref={scrollRef}
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20}
        keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
        enableAutomaticScroll={true}
        contentContainerStyle={{ paddingBottom: space[30] }}>
        <Box>
          {
            <>
              {nominees.map((nominee, index) => (
                <NominationForm
                  ref={ref => {
                    nominationRef.current[index] = ref;
                  }}
                  key={nominee.nomineeKey}
                  index={index}
                  onDelete={
                    index > 0
                      ? () => showRemoveConfirmationDialog(index)
                      : undefined
                  }
                  isLatestItem={index === nominees.length - 1}
                  update={update}
                />
              ))}
              <Row alignItems="center" mt={space[4]} gap={space[5]}>
                <TouchableOpacity
                  onPress={addNomination}
                  disabled={hasReachedMaximum}>
                  <Box
                    flexDirection="row"
                    alignItems="center"
                    alignSelf="flex-start"
                    borderRadius={space[1]}
                    backgroundColor={colors.background}
                    px={space[3]}
                    py={space[2]}
                    borderWidth={2}
                    borderColor={
                      hasReachedMaximum
                        ? colors.palette.fwdOrange['50']
                        : colors.primary
                    }>
                    <Icon.Plus
                      fill={
                        hasReachedMaximum
                          ? colors.palette.fwdOrange['50']
                          : colors.primary
                      }
                    />
                    <LargeBody
                      fontWeight="bold"
                      color={
                        hasReachedMaximum
                          ? colors.palette.fwdOrange['50']
                          : colors.primary
                      }>
                      {t('eApp:other.nominationDetails.addHibah')}
                    </LargeBody>
                  </Box>
                </TouchableOpacity>
                <LargeBody fontWeight="medium">
                  {t('eApp:other.nominationDetails.maximumHibah')}
                </LargeBody>
              </Row>
              <PayablePercentageArrangement
                onDelete={(index: number) =>
                  showRemoveConfirmationDialog(index)
                }
                update={update}
                allocations={allocations}
              />
            </>
          }
        </Box>
        <RemoveFormConfirmationDialog
          visible={removeConfirmationVisible}
          isLoading={isDeletingParty}
          onDismiss={() => setRemoveConfirmationVisible(false)}
          onConfirm={async () => {
            setRemoveConfirmationVisible(false);
            const partyId = nominees[indexToRemove.current].id;
            if (
              caseId &&
              partyId &&
              caseObj?.parties?.find(p => p.id === partyId)
            ) {
              await deleteParty({ caseId, partyId });
            }
            remove(indexToRemove.current);
          }}
        />
      </ScrollViewContainer>

      <EAppFooterTablet
        progressLock="appDetail-others"
        primaryDisabled={!form.formState.isValid}
        primaryLabel={t('next')}
        onPrimaryPress={handleOnSubmit}
        primaryLoading={form.formState.isSubmitting}
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        isAbsolutePositioned
      />
    </FormProvider>
  );
});
export default NominationDetailsTablet;

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space, colors } }) => ({
    flex: 1,
    paddingRight: space[8],

    backgroundColor: colors.surface,
  }),
);
