import { NominationInfoForm } from 'features/eAppV2/id/validations/applicationDetails/nomination/nominationInfoValidation';
import React from 'react';
import { UseFieldArrayUpdate } from 'react-hook-form';
import PayablePercentageArrangementTablet from './PayablePercentageArrangement.tablet';

interface Props {
  onDelete: (index: number) => void;
  update: UseFieldArrayUpdate<NominationInfoForm>;
  allocations: (string | undefined)[];
}

export default function PayablePercentageArrangement(props: Props) {
  return <PayablePercentageArrangementTablet {...props} />;
}
