import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Checkbox,
  Column,
  H6,
  Icon,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useController,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { City, Country, OptionList, PostalCode } from 'types/optionList';
import {
  CORRESPONDENCE_ADDRESS_OPTION,
  MY_COUNTRY,
  NEW_ADDRESS_OPTION,
  OWNER_ADDRESS_OPTION,
} from 'constants/optionList';
import { useWatch } from 'react-hook-form';
import { useCallback, useEffect, useMemo } from 'react';
import AutocompletePopup from 'components/AutocompletePopup';
import { NominationInfoForm } from 'features/eAppV2/id/validations/applicationDetails/nomination/nominationInfoValidation';
import styled from '@emotion/native';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import uniqBy from 'lodash/uniqBy';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { PartyRole } from 'types/party';

interface Props {
  index: number;
  disabled?: boolean;
  control: Control<NominationInfoForm>;
  setValue: UseFormSetValue<NominationInfoForm>;
  getValues: UseFormGetValues<NominationInfoForm>;
  trigger: UseFormTrigger<NominationInfoForm>;
  addressType: 'correspondence' | 'residential';
}

export default function NominationAddressInformationForm({
  index,
  disabled,
  control,
  setValue,
  getValues,
  trigger,
  addressType,
}: Props) {
  const fieldNameKeyPrefix = `nominees.${index}.${addressType}` as const;

  const { colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);

  const { data: rawOptionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'id'>();
  const optionList = rawOptionList as OptionList<string, 'id'> | undefined;

  const {
    field: { value: addressOption, onChange: setAddressOption },
  } = useController({
    name: `${fieldNameKeyPrefix}Address`,
    control,
  });

  const country = useWatch({
    name: `${fieldNameKeyPrefix}Country`,
    control: control,
  });

  const postCode = useWatch({
    name: `${fieldNameKeyPrefix}PostCode`,
    control: control,
  });

  const isMY = country === MY_COUNTRY || country === '';

  const getCityFromPostCode = useCallback(
    (postCode: string | undefined): City<string> | undefined => {
      const postCodeOption = postCode
        ? (optionList?.POSTAL_CODE?.options ?? []).find(
            i => String(i.value) === postCode,
          )
        : undefined;
      const cityOption = postCodeOption?.CITY
        ? (optionList?.CITY?.options ?? []).find(
            i => i.value === postCodeOption?.CITY,
          )
        : undefined;
      return cityOption;
    },
    [optionList],
  );

  const onCountryChange = (value: string | undefined) => {
    const isMY = value === MY_COUNTRY || value === undefined || value === '';
    if (isMY) {
      const cityOption = getCityFromPostCode(postCode);
      if (cityOption) {
        setValue(`${fieldNameKeyPrefix}City`, cityOption.value);
        trigger(`${fieldNameKeyPrefix}City`);
      }
    } else {
      setValue(`${fieldNameKeyPrefix}PostCode`, '');
      setValue(`${fieldNameKeyPrefix}City`, '');
      setValue(`${fieldNameKeyPrefix}State`, '');
      trigger(`${fieldNameKeyPrefix}PostCode`);
      trigger(`${fieldNameKeyPrefix}City`);
    }
  };

  const onPostCodeChange = (value: string | undefined) => {
    const country = getValues(`${fieldNameKeyPrefix}Country`);
    const isMY =
      country === MY_COUNTRY || country === undefined || country === '';
    if (isMY) {
      const cityOption = getCityFromPostCode(value);
      if (cityOption) {
        setValue(`${fieldNameKeyPrefix}City`, cityOption.value);
        trigger(`${fieldNameKeyPrefix}City`);
      }
    }
  };

  const postCodeList = useMemo(() => {
    return uniqBy(optionList?.POSTAL_CODE?.options, 'value');
  }, [optionList?.POSTAL_CODE?.options]);

  const cityList = useMemo(() => {
    if (isMY) {
      const postCodeOptions = postCode
        ? (optionList?.POSTAL_CODE?.options ?? [])
            .filter(i => String(i.value) === postCode)
            .map(i => i.CITY)
        : [];
      return (optionList?.CITY?.options ?? []).filter(i =>
        postCodeOptions.includes(i.value),
      );
    } else {
      return [];
    }
  }, [
    isMY,
    postCode,
    optionList?.POSTAL_CODE?.options,
    optionList?.CITY?.options,
  ]);

  const { caseObj } = useGetActiveCase();
  const companyParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  useEffect(() => {
    if (
      addressType === 'correspondence' &&
      addressOption === OWNER_ADDRESS_OPTION
    ) {
      const res = (companyParty?.addresses || []).find(
        el => el.addressType === 'MAIN',
      );
      if (res) {
        const {
          street,
          subDistrict,
          district,
          countryCode,
          zipCode,
          city,
          province,
        } = res;

        setValue(`${fieldNameKeyPrefix}AddressLine1`, street);
        setValue(`${fieldNameKeyPrefix}AddressLine2`, subDistrict);
        setValue(`${fieldNameKeyPrefix}AddressLine3`, district);
        setValue(`${fieldNameKeyPrefix}PostCode`, zipCode);
        setValue(`${fieldNameKeyPrefix}City`, city);
        setValue(`${fieldNameKeyPrefix}State`, province);
        setValue(`${fieldNameKeyPrefix}Country`, countryCode);
      }
    }
  }, [addressOption, addressType, companyParty, fieldNameKeyPrefix, setValue]);

  return (
    <Column backgroundColor={colors.background}>
      <Row gap={space[1]}>
        <Icon.Location fill={colors.palette.black} />
        <H6 fontWeight="bold">
          {addressType === 'correspondence' && t(`eApp:correspondenceAddress`)}
          {addressType === 'residential' && t(`eApp:residentialAddress`)}
        </H6>
      </Row>
      {addressType === 'correspondence' ? (
        <AddressOptionCheckbox
          label={t('eApp:correspondenceAddress.same.po')}
          checked={addressOption === OWNER_ADDRESS_OPTION}
          onChange={checked => {
            if (checked) {
              setAddressOption(OWNER_ADDRESS_OPTION);
            } else {
              setAddressOption(NEW_ADDRESS_OPTION);
              setValue(`${fieldNameKeyPrefix}AddressLine1`, '');
              setValue(`${fieldNameKeyPrefix}AddressLine2`, '');
              setValue(`${fieldNameKeyPrefix}AddressLine3`, '');
              setValue(`${fieldNameKeyPrefix}PostCode`, '');
              setValue(`${fieldNameKeyPrefix}City`, '');
              setValue(`${fieldNameKeyPrefix}State`, '');
              setValue(`${fieldNameKeyPrefix}Country`, MY_COUNTRY);
            }
          }}
        />
      ) : (
        <Box h={7} />
      )}
      {addressType === 'residential' ? (
        <AddressOptionCheckbox
          label={t('eApp:certificate.residentialAddress.correspondence')}
          checked={addressOption === CORRESPONDENCE_ADDRESS_OPTION}
          onChange={checked => {
            if (checked) {
              setAddressOption(CORRESPONDENCE_ADDRESS_OPTION);
            } else {
              setAddressOption(NEW_ADDRESS_OPTION);
            }
          }}
        />
      ) : (
        <Box h={7} />
      )}
      {addressOption === NEW_ADDRESS_OPTION && (
        <Box gap={space[5]} mt={space[5]}>
          <Input
            control={control}
            as={TextField}
            name={`${fieldNameKeyPrefix}AddressLine1`}
            label={t('eApp:addressLine1')}
            hint={t('eApp:addressLine1.hint')}
            disabled={disabled}
            shouldUnregister={true}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={TextField}
            name={`${fieldNameKeyPrefix}AddressLine2`}
            label={t('eApp:addressLine2')}
            hint={t('eApp:addressLine2.hint')}
            style={eAppCommonStyles.tabletTextField}
            disabled={disabled}
            shouldUnregister={true}
          />
          <Input
            control={control}
            as={TextField}
            name={`${fieldNameKeyPrefix}AddressLine3`}
            label={t('eApp:addressLine3')}
            style={eAppCommonStyles.tabletTextField}
            disabled={disabled}
            shouldUnregister={true}
          />
          {/* 1 */}
          <Row gap={space[6]}>
            <Input
              control={control}
              as={AutocompletePopup<Country, string>}
              name={`${fieldNameKeyPrefix}Country`}
              label={t('eApp:certificate.form.country')}
              data={optionList?.COUNTRY?.options ?? []}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.tabletTextField}
              onChange={onCountryChange}
              searchable
              shouldUnregister={true}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              initialHighlight={false}
            />
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<PostalCode<string>, string>}
                name={`${fieldNameKeyPrefix}PostCode`}
                label={t('eApp:postcode')}
                data={postCodeList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={eAppCommonStyles.tabletTextField}
                onChange={onPostCodeChange}
                searchable
                shouldUnregister={true}
                shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
                initialHighlight={false}
              />
            ) : (
              <Input
                control={control}
                as={TextField}
                name={`${fieldNameKeyPrefix}PostCode`}
                label={t('eApp:postcode')}
                style={eAppCommonStyles.tabletTextField}
                disabled={disabled}
                maxLength={10}
                shouldUnregister={true}
                shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
                initialHighlight={false}
              />
            )}
          </Row>
          {/* 2 */}
          <Row gap={space[6]}>
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<{ value: string; label: string }, string>}
                name={`${fieldNameKeyPrefix}City`}
                label={t('eApp:city')}
                data={cityList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={eAppCommonStyles.tabletTextField}
                searchable
                shouldUnregister={true}
                shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
                initialHighlight={false}
              />
            ) : (
              <Input
                control={control}
                as={TextField}
                name={`${fieldNameKeyPrefix}City`}
                label={t('eApp:city')}
                style={eAppCommonStyles.tabletTextField}
                disabled={disabled}
                maxLength={60}
                shouldUnregister={true}
                shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
                initialHighlight={false}
              />
            )}
            <Input
              control={control}
              as={AutocompletePopup<{ value: string; label: string }, string>}
              name={`${fieldNameKeyPrefix}State`}
              label={t('eApp:state')}
              data={optionList?.PROVINCE?.options ?? []}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.tabletTextField}
              searchable
              shouldUnregister={true}
              shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
              initialHighlight={false}
            />
          </Row>
        </Box>
      )}
    </Column>
  );
}

const AddressOptionCheckbox = styled(Checkbox)(({ theme }) => ({
  alignSelf: 'stretch',
  marginTop: theme.space[5],
}));
