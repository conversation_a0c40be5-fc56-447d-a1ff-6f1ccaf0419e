import styled from '@emotion/native';
import { Control, useController, useWatch } from 'react-hook-form';
import {
  <PERSON>um<PERSON>,
  Picker,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { Industry, Nationality } from 'types/optionList';
import IdNumberField from 'components/IdNumberField';
import NameField from 'components/NameField';
import AutocompletePopup from 'components/AutocompletePopup';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { TrusteeCompanyInfoForm } from 'features/eAppV2/id/validations/applicationDetails/trustee/trusteeCompanyInfoValidation';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { NEW_NRIC } from 'constants/optionList';

interface Props {
  control: Control<TrusteeCompanyInfoForm>;
}

const TODAY = new Date();

const CompanyDetailsFormTablet = (props: Props) => {
  const { control } = props;
  const { t } = useTranslation(['eApp']);
  const { data: optionList } = useGetOptionList<'id'>();

  const companyName = useWatch({
    name: 'companyName',
    control: control,
  });

  const registrationNumber = useWatch({
    name: 'regNumber',
    control: control,
  });

  const registrationDate = useWatch({
    name: `registrationDate`,
    control: control,
  });

  const natureOfBusiness = useWatch({
    name: 'natureOfBusiness',
    control: control,
  });

  const primaryIdType = useWatch({
    name: `primaryIdType`,
    control: control,
  });

  const {
    field: { value: primaryId, onChange: onChangePrimaryId },
  } = useController({
    name: `primaryId`,
    control: control,
  });

  const authorizedSignatory = useWatch({
    name: `authorizedSignatory`,
    control: control,
  });

  const countryOfIncorporation = useWatch({
    name: 'countryOfIncorporation',
    control,
  });

  const isValid = useMemo(() => {
    return (
      companyName &&
      registrationNumber &&
      registrationDate &&
      natureOfBusiness &&
      authorizedSignatory &&
      countryOfIncorporation &&
      primaryIdType &&
      primaryId
    );
  }, [
    companyName,
    registrationNumber,
    registrationDate,
    natureOfBusiness,
    authorizedSignatory,
    countryOfIncorporation,
    primaryIdType,
    primaryId,
  ]);

  const isNewNric = primaryIdType === NEW_NRIC;

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:other.trusteeDetails.details')}
      icon={<PictogramIcon.Lanyard size={40} />}
      isDone={Boolean(isValid)}>
      <Content>
        {/* 1 */}
        <RowBox>
          <Input
            control={control}
            as={NameField}
            name={`companyName`}
            label={t('eApp:other.trusteeDetails.companyName')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={DatePickerCalendar}
            name={`registrationDate`}
            label={t('eApp:other.trusteeDetails.dateOfRegistration')}
            hint={t('eApp:dateFormat')}
            maxDate={TODAY}
            formatDate={val => (val ? dateFormatUtil(val) : '')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="regNumber"
            label={t('eApp:other.trusteeDetails.registrationNumberNew')}
            style={eAppCommonStyles.tabletTextField}
            keyboardType="number-pad"
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={AutocompletePopup<Industry<string>, string>}
            name="natureOfBusiness"
            label={t('eApp:other.trusteeDetails.natureOfBusiness')}
            style={eAppCommonStyles.tabletTextField}
            data={optionList?.INDUSTRY?.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            searchable
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 3 */}
        <RowBox>
          <Input
            style={eAppCommonStyles.tabletTextField}
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name="countryOfIncorporation"
            label={t('eApp:other.trusteeDetails.countryOfIncorporation')}
            data={optionList?.NATIONALITY?.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            searchable
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={NameField}
            name={`authorizedSignatory`}
            label={t('eApp:other.trusteeDetails.authorizedSignatory')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={Picker}
            name="primaryIdType"
            type="chip"
            label={t('eApp:idType')}
            items={optionList?.IDTYPE?.options || []}
            labelStyle={eAppCommonStyles.pickerLabel}
            style={eAppCommonStyles.tabletNonTextField}
            onChange={value => {
              if (value !== primaryIdType && value === NEW_NRIC) {
                onChangePrimaryId('');
              }
            }}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Input
            control={control}
            as={IdNumberField}
            idType={primaryIdType}
            name={`primaryId`}
            hint={isNewNric ? t('eApp:nricHint') : ''}
            placeholder={isNewNric ? t('eApp:nricPlaceholder') : ''}
            label={t('eApp:certificate.form.identificationNumber')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
        </RowBox>

        {/* 5 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="regNumberOld"
            label={t('eApp:other.trusteeDetails.registrationNumberOld')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheckEApp}
            initialHighlight={false}
          />
          <Column flex={1} />
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};
export default CompanyDetailsFormTablet;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  gap: space[5],
  paddingHorizontal: space[6],
  marginTop: space[5],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  flexDirection: 'row',
  flex: 1,
  gap: space[6],
}));
