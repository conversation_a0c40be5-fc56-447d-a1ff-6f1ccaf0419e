import { useGetOptionList } from 'hooks/useGetOptionList';
import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { Application } from 'types/case';
import { NoTinReason } from 'features/eAppV2/id/validations/applicationDetails/declarationValidation';
import { DeclarationQuestionSummaryProps } from '../components/reviewSummary/sections/consentAndDeclarationSummary/ConsentAndDeclarationSummary';

export const useReviewFatcaMapper = (application?: Application) => {
  const { t } = useTranslation(['eApp']);
  const { data: optionList } = useGetOptionList<'id'>();
  const proposerConsent = application?.proposerConsent?.[0];

  const getNoTinReason = useCallback(
    (reason?: string) => {
      switch (reason) {
        case NoTinReason.NOT_ISSUE:
          return t('eApp:declaration.fatca.taxResidency.noTin.1');
        case NoTinReason.DOES_NOT_GET_TIN:
          return t('eApp:declaration.fatca.taxResidency.noTin.2');
        case NoTinReason.NO_TIN_REQUIRED:
          return t('eApp:declaration.fatca.taxResidency.noTin.3');
      }
      return '';
    },
    [t],
  );

  return [
    {
      question: t('eApp:declaration.fatca.question.1'),
      answer: proposerConsent?.otherCountryTax ? t('eApp:yes') : t('eApp:no'),
      subQuestion: t('eApp:declaration.fatca.question.2'),
      subAnswer: proposerConsent?.usGreenCardHolder
        ? t('eApp:yes')
        : t('eApp:no'),
      additional: proposerConsent?.otherCountryTaxArray?.map((item, index) => {
        return {
          title: t('eApp:declaration.fatca.taxResidency', {
            index: index + 1,
          }),
          info: [
            {
              fieldName: t('eApp:declaration.fatca.taxResidency.country'),
              fieldValue: optionList?.COUNTRY?.options.find(
                e => e.value === item.country,
              )?.label,
              isSingle: true,
            },
            {
              fieldName: t('eApp:declaration.fatca.taxResidency.tin.question'),
              fieldValue: item.haveTIN === 'yes' ? t('eApp:yes') : t('eApp:no'),
              isSingle: true,
            },
            {
              fieldName: t('eApp:declaration.fatca.taxResidency.noTin.title'),
              fieldValue: getNoTinReason(item.noTinReason),
              isSingle: true,
            },
            {
              fieldName: t('eApp:declaration.fatca.taxResidency.tin.number'),
              fieldValue: item.tinNumber,
              isSingle: true,
            },
          ],
        };
      }),
    },
  ] as DeclarationQuestionSummaryProps[];
};
