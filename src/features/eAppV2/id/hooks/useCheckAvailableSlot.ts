import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { RelationshipValue } from 'features/proposal/types';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetUnionPolicyList } from 'hooks/useGetUnionPolicyList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useCallback, useMemo } from 'react';
import { PartyRole } from 'types/party';

export const useCheckAvailableSlot = () => {
  const { data: optionList } = useGetOptionList<'id'>();
  const quotation = useSelectedQuotation();
  const { caseObj } = useGetActiveCase();

  const policyListParams = useMemo(() => {
    const policyOwner = getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0];

    if (policyOwner) {
      return {
        firstName: policyOwner?.person?.name?.firstName || '',
        gender: policyOwner?.person?.gender,
        dob: policyOwner?.person?.dateOfBirth?.date,
        idNumber: policyOwner?.person?.registrations?.find(r => r.type === 'DEFAULT')
            ?.id || '',
      };
    }
  }, [caseObj]);
  const { refetch, isFetching } = useGetUnionPolicyList(policyListParams);

  const checkAvailableSlot = useCallback(async () => {
    if (!quotation?.familyPlanInfo?.primaryPolicyNum) return true;
    const policies = await refetch();
    const primaryPolicy = policies?.find(
      p => p.policyNumber === quotation?.familyPlanInfo?.primaryPolicyNum,
    );
    if (!primaryPolicy || !primaryPolicy.quota) return false;
    const insured = getPartiesByRole(caseObj, PartyRole.INSURED)?.[0];
    const insuredRelationship = optionList?.RELATIONSHIP?.options.find(
      o => o.value === insured?.relationship,
    )?.value;
    if (insuredRelationship === RelationshipValue.SPOUSE) {
      return primaryPolicy.quota.spouse < primaryPolicy.quota.spouseMax;
    } else if (
      insuredRelationship === RelationshipValue.CHILD ||
      insuredRelationship === 'CHLD'
    ) {
      return primaryPolicy.quota.child < primaryPolicy.quota.childMax;
    }
    return true;
  }, [
    caseObj,
    optionList?.RELATIONSHIP?.options,
    quotation?.familyPlanInfo?.primaryPolicyNum,
    refetch,
  ]);

  return {
    checkAvailableSlot,
    isFetching,
  };
};
