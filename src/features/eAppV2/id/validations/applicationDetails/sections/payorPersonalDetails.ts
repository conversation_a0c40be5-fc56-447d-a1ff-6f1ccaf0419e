import {
  invalidBeneficialOwnerNotMatchAsLifeAssured,
  invalidBeneficialOwnerSameAsPolicyOwner,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { nullableDate } from 'features/eAppV2/common/validations/eAppCommonSchema';
import { mysIdNumber } from 'features/eAppV2/my/validations/commonSchema';
import { t } from 'i18next';
import { Party } from 'types/party';
import { Gender, MaritalStatus, Registration, Religion } from 'types/person';
import { parsePartyDob, toPartyDob } from 'utils/helper/dateUtil';
import { InferType, object, ref, string } from 'yup';
import { idName } from '../../commonSchema';

const idNumberLimit = 16;
const idNumberErrorMsg = t('form.icNumberLimitCharacters');
const idNumberOnlyErrorMsg = t('form.inputNumberOnly');
const isDigitRex = /^[0-9]+$/;
export const maxNameLength = 60;
export const payorPersonalDetailsSchema = object({
  id: string(),
  relationship: string().required(requiredMessage),
  title: string().required(requiredMessage),
  fullName: idName()
    .required(requiredMessage)
    .test(
      'same-person-with-policy-owner',
      invalidBeneficialOwnerSameAsPolicyOwner,
      (fullName, ctx) => {
        const policyOwnerFullName = ctx.options?.context?.policyOwnerFullName;
        return !(policyOwnerFullName === fullName);
      },
    )
    .test(
      'not-match-with-insured',
      invalidBeneficialOwnerNotMatchAsLifeAssured,
      (fullName, ctx) => {
        const { context } = ctx.options || {};
        if (!context?.hasInsured) return true;

        const [primaryId, primaryIdType] = ['primaryId', 'primaryIdType'].map(
          key => ctx.resolve(ref(key)),
        );
        const { insuredFullName, insuredIdNumber, insuredIdType } = context;

        const fullNameNotMatch =
          primaryIdType === insuredIdType &&
          primaryId === insuredIdNumber &&
          fullName !== insuredFullName;
        return !fullNameNotMatch;
      },
    ),
  dob: nullableDate(),
  gender: string().required(requiredMessage),
  maritalStatus: string().required(requiredMessage),
  religion: string().required(requiredMessage),
  nationality: string().required(requiredMessage),
  primaryIdType: string().required(requiredMessage),
  primaryId: mysIdNumber('primaryIdType', 'dob', 'gender')
    .required(requiredMessage)
    .test(
      'same-person-with-policy-owner',
      invalidBeneficialOwnerSameAsPolicyOwner,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        const policyOwnerIdNumber = ctx.options?.context?.policyOwnerIdNumber;
        const policyOwnerIdType = ctx.options?.context?.policyOwnerIdType;
        const isSamePersonWithPolicyOwner =
          policyOwnerIdNumber === primaryId &&
          policyOwnerIdType === primaryIdType;
        return !isSamePersonWithPolicyOwner;
      },
    )
    .test(
      'not-match-with-insured',
      invalidBeneficialOwnerNotMatchAsLifeAssured,
      (primaryId, ctx) => {
        const { context } = ctx.options || {};
        if (!context?.hasInsured) return true;

        const [fullName, primaryIdType] = ['fullName', 'primaryIdType'].map(
          key => ctx.resolve(ref(key)),
        );
        const { insuredFullName, insuredIdNumber, insuredIdType } = context;

        const primaryIdNotMatch =
          fullName === insuredFullName &&
          (primaryIdType !== insuredIdType || primaryId !== insuredIdNumber);

        return !primaryIdNotMatch;
      },
    )
    .when('primaryIdType', {
      is: 'ID',
      then: schema =>
        schema
          .min(idNumberLimit, idNumberErrorMsg)
          .max(idNumberLimit, idNumberErrorMsg)
          .matches(isDigitRex, idNumberOnlyErrorMsg),
    }),
  countryOfBirth: string().required(requiredMessage),
  placeOfBirth: string().required(requiredMessage),
  taxIdNumber: string(),
});

export type PayorPersonalDetailsForm = InferType<
  typeof payorPersonalDetailsSchema
>;

export const payorPersonalDetailsDefaultValue: PayorPersonalDetailsForm = {
  id: '',
  relationship: '',
  title: '',
  fullName: '',
  dob: null,
  gender: '',
  maritalStatus: '',
  religion: '',
  nationality: '',
  primaryIdType: '',
  primaryId: '',
  countryOfBirth: '',
  placeOfBirth: '',
  taxIdNumber: '',
};

export const partyToPayorPersonalDetails = (
  party?: Party,
): PayorPersonalDetailsForm => {
  if (!party) return payorPersonalDetailsDefaultValue;
  const primaryId = party.person?.registrations?.find(
    r => r.type === 'DEFAULT',
  );
  return {
    id: party.id || '',
    relationship: party?.relationship || '',
    title: party.person?.name.title || '',
    fullName: party.person?.name.firstName || '',
    dob: party.person?.dateOfBirth
      ? parsePartyDob(party.person?.dateOfBirth)
      : null,
    gender: party.person?.gender || '',
    maritalStatus: party.person?.maritalStatus || '',
    religion: party.person?.religion || '',
    nationality: party.person?.nationality || '',
    primaryIdType: primaryId?.idType || '',
    primaryId: primaryId?.id || '',
    countryOfBirth: party.person?.countryOfBirth || '',
    placeOfBirth: party.person?.placeOfBirth || '',
    taxIdNumber: party.entity?.taxIdNumber || '',
  };
};

export const payorPersonalDetailsToParty = (
  form: PayorPersonalDetailsForm,
): Pick<Party, 'id' | 'relationship' | 'person' | 'entity'> => {
  return {
    id: form.id || '',
    relationship: form.relationship,
    person: {
      name: {
        title: form.title,
        firstName: form.fullName,
        fullName: form.fullName,
      },
      dateOfBirth: toPartyDob(form.dob),
      gender: form.gender as Gender,
      maritalStatus: form.maritalStatus as MaritalStatus,
      religion: form.religion as Religion,
      nationality: form.nationality,
      registrations: [
        {
          type: 'DEFAULT',
          idType: form.primaryIdType,
          id: form.primaryId,
        },
      ].filter((r): r is Registration => Boolean(r)),
      countryOfBirth: form.countryOfBirth,
      placeOfBirth: form.placeOfBirth,
    },
    entity: {
      name: form.fullName,
      taxIdNumber: form.taxIdNumber,
    },
  };
};
