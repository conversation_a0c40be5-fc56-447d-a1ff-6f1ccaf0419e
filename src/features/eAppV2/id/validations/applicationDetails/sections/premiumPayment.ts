import { requiredMessage } from 'features/eAppV2/common/constants/eAppErrorMessages';
import { PaymentMethod } from 'features/eAppV2/id/components/payment/form/methods/paymentMethodTypes';
import { Application } from 'types/case';
import { InferType, object, string } from 'yup';

export const premiumPaymentSchema = object({
  paymentMethod: string().required(requiredMessage),
  issuedCountry: string().when('paymentMethod', {
    is: PaymentMethod.CC,
    then: schema => schema.required(requiredMessage),
  }),
});

export type PremiumPaymentForm = InferType<typeof premiumPaymentSchema>;

export const premiumPaymentDefaultValue: PremiumPaymentForm = {
  paymentMethod: '',
  issuedCountry: '',
};

export const applicationToPremiumPayment = (
  application?: Application,
): PremiumPaymentForm => {
  return {
    paymentMethod: application?.paymentMethod || '',
    issuedCountry: application?.creditCardIssuedCountry || '',
  };
};
