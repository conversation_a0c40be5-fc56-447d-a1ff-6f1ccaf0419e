import { NON_INCOME_OCC_GROUP } from 'constants/optionList';
import {
  invalidFormatMessage,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { normalizedSpaceString } from 'features/eAppV2/common/validations/eAppCommonSchema';
import { mysAnnualIncomeAmount } from 'features/eAppV2/my/validations/commonSchema';
import { Occupation, OptionList } from 'types/optionList';
import { Party } from 'types/party';
import { Person } from 'types/person';
import { InferType, object, string } from 'yup';
export const occupationDetailsSchema = object({
  occupationRole: string().oneOf(['mainParty', 'others']),
  occupation: string().required(requiredMessage),
  occupationGroup: string(),
  occupationDescription: string().required(requiredMessage),
  occupationSector: string().required(requiredMessage),
  companyName: normalizedSpaceString()
    .validateCompanyName(invalidFormatMessage)
    .when('occupationGroup', {
      is: NON_INCOME_OCC_GROUP,
      then: s => s.optional(),
      otherwise: s => s.required(requiredMessage),
    }),

  industry: string().when(['occupationRole', 'occupationGroup'], {
    is: (role: string, occGroup: string) => occGroup !== NON_INCOME_OCC_GROUP,
    then: s => s.required(requiredMessage),
    otherwise: s => s.optional(),
  }),
  monthlyIncome: mysAnnualIncomeAmount('annualIncome', 'annualIncomeAmount'),
  occupationPosition: string().required(requiredMessage),
});
export type OccupationDetailsForm = InferType<typeof occupationDetailsSchema>;
export const occupationDetailsDefaultValue: OccupationDetailsForm = {
  occupation: '',
  occupationGroup: '',
  occupationDescription: '',
  occupationSector: '',
  companyName: '',
  industry: '',
  monthlyIncome: '',
  occupationPosition: '',
};
export const partyToOccupationDetails = (
  party?: Party,
  optionList?: OptionList,
): OccupationDetailsForm => {
  if (!party) return occupationDetailsDefaultValue;
  const occupationOption = optionList?.OCCUPATION.options?.find(
    e => e.value === party.person?.occupation?.natureOfSubWork,
  );
  return {
    occupation: party.person?.occupation?.natureOfSubWork || '',
    companyName: party.person?.occupation?.nameOfEmployer || '',
    industry: (occupationOption as Occupation<string, 'id'>)?.industry || '',
    monthlyIncome:
      typeof party.person?.occupation?.income === 'number'
        ? String(party.person?.occupation.income)
        : '',
    occupationGroup: (occupationOption as Occupation<string, 'my'>)
      ?.occupationGroupCode,
    occupationDescription:
      party?.person?.occupation?.occupationDescription || '',
    occupationSector: party?.person?.occupation?.occupationSector || '',
    occupationPosition: party?.person?.occupation?.duties || '',
  };
};
export const occupationDetailsToParty = (
  form: OccupationDetailsForm,
  optionList: OptionList<string, 'id'>,
): { person: Pick<Person, 'occupation'> } => {
  return {
    person: {
      occupation: {
        nameOfEmployer: form.companyName || '',
        natureOfBusiness: form.industry || '',
        natureOfWork:
          optionList.OCCUPATION.options.find(o => o.value === form.occupation)
            ?.occupationClass.value || '',
        natureOfSubWork: form.occupation || '',
        occupationDescription: form.occupationDescription || '',
        incomeRange: form.monthlyIncome || '',
        income:
          form.monthlyIncome !== undefined &&
          form.monthlyIncome !== null &&
          form.monthlyIncome !== ''
            ? Number(form.monthlyIncome)
            : undefined,
        duties: form.occupationPosition || '',
        occupationSector: form.occupationSector || '',
      },
    },
  };
};
