import { getOriginalCountryCode, ID_MOBILE_CODE } from 'constants/optionList';
import {
  invalidEmailMessage,
  invalidMobileNumberMessage,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import { mysEmail } from 'features/eAppV2/my/validations/commonSchema';
import { OptionList } from 'types/optionList';
import { Party, PartyRole } from 'types/party';
import { formPhoneNumber } from 'utils/validation/customValidation';
import { boolean, InferType, object, ref, string } from 'yup';
const requiredByOwnerRole = () =>
  string().when('contactRole', {
    is: 'owner',
    then: s => s.required(requiredMessage),
    otherwise: s => s.optional(),
  });
export const contactDetailsSchema = object({
  email: mysEmail()
    .required(requiredMessage)
    .test({
      name: 'invalid-email',
      message: invalidEmailMessage,
      test: (email, ctx) => {
        const contactRole: string = ctx.resolve(ref('contactRole'));
        if (!['owner', 'insured'].includes(contactRole)) return true;
        const primaryId =
          ctx.resolve(ref('primaryId')) ?? ctx.options?.context?.primaryId;
        if (
          ctx.options?.context?.agentId?.replace(/-/g, '') ===
          primaryId?.replace(/-/g, '')
        )
          return true;
        return ctx.options?.context?.agentEmail?.toLowerCase() ===
          email?.toLowerCase()
          ? false
          : true;
      },
    }),
  mobileCountryCode: string().required(requiredMessage),
  mobileNumber: formPhoneNumber('mobileCountryCode')
    .required(requiredMessage)
    .test({
      name: 'invalid-mobile-number',
      message: invalidMobileNumberMessage,
      test: (mobileNumber, ctx) => {
        const contactRole: string = ctx.resolve(ref('contactRole'));
        if (!['owner', 'insured'].includes(contactRole)) return true;
        const mobileCountryCode: string = ctx.resolve(ref('mobileCountryCode'));
        const agentMobileNumber: string =
          ctx.options?.context?.agentMobileNumber;
        if (
          agentMobileNumber ===
          `+${mobileCountryCode.split(' - ')[0]}${mobileNumber}`
        ) {
          const primaryId =
            ctx.resolve(ref('primaryId')) ?? ctx.options?.context?.primaryId;
          return (
            ctx.options?.context?.agentId?.replace(/-/g, '') ===
            primaryId?.replace(/-/g, '')
          );
        }
        return true;
      },
    }),
  homeCountryCode: string(),
  homeNumber: formPhoneNumber('homeCountryCode'),
  officeCountryCode: string(),
  officeNumber: formPhoneNumber('officeCountryCode'),
  contactRole: string().oneOf(['owner', 'insured', 'others']),
  preferredCopy: requiredByOwnerRole(),
  preferredContactMode: requiredByOwnerRole(),
  shippingType: requiredByOwnerRole(),
  otherContactMode: string().when('preferredContactMode', {
    is: (value: string) =>
      ['TWITTER', 'FACEBOOK'].some(item => value?.toUpperCase() === item),
    then: s => s.required(requiredMessage),
    otherwise: s => s.optional(),
  }),
  sameAsPO: boolean().optional(),
});
export type ContactDetailsForm = InferType<typeof contactDetailsSchema>;
export const contactDetailsDefaultValue: ContactDetailsForm = {
  email: '',
  mobileCountryCode: ID_MOBILE_CODE,
  mobileNumber: '',
  homeCountryCode: ID_MOBILE_CODE,
  homeNumber: '',
  officeCountryCode: ID_MOBILE_CODE,
  officeNumber: '',
  contactRole: '',
  preferredCopy: '',
  preferredContactMode: '',
  shippingType: '',
  otherContactMode: '',
};
export const partyToContactDetails = (
  party?: Party,
  optionList?: OptionList,
): ContactDetailsForm => {
  if (!party) return contactDetailsDefaultValue;
  const mobileContact = party.contacts.phones.find(i => i.type === 'MOBILE');
  const homeContact = party.contacts.phones.find(i => i.type === 'HOME');
  const officeContact = party.contacts.phones.find(i => i.type === 'WORK');
  return {
    email: party.contacts.email || '',
    mobileCountryCode:
      getOriginalCountryCode(mobileContact?.countryCode, optionList) ||
      ID_MOBILE_CODE,
    mobileNumber: mobileContact?.number || '',
    homeCountryCode:
      getOriginalCountryCode(homeContact?.countryCode, optionList) ||
      ID_MOBILE_CODE,
    homeNumber: homeContact?.number || '',
    officeCountryCode:
      getOriginalCountryCode(officeContact?.countryCode, optionList) ||
      ID_MOBILE_CODE,
    officeNumber: officeContact?.number || '',
    ...(isRole(party, PartyRole.PROPOSER) && {
      preferredCopy: party.preferredCertificateCopy || '',
      preferredLanguage: party.contacts.preferredLanguage || '',
      preferredContactMode: party.contacts.preferredContactMode || '',
    }),
    shippingType: party.contacts.shippingType || '',
    otherContactMode: party.contacts.otherContactMode || '',
    sameAsPO: party.contacts.sameAsPO,
  };
};
export const contactDetailsToParty = (
  form: ContactDetailsForm,
): Pick<Party, 'contacts' | 'preferredCertificateCopy'> => {
  return {
    preferredCertificateCopy: form.preferredCopy || '',
    contacts: {
      email: form.email || '',
      preferredContactMode: form.preferredContactMode || '',
      otherContactMode: form.otherContactMode || '',
      shippingType: form.shippingType || '',
      phones: [
        'mobileCountryCode' in form
          ? {
              type: 'MOBILE',
              countryCode: form.mobileCountryCode || '',
              number: form.mobileNumber || '',
            }
          : null,
        'homeCountryCode' in form
          ? {
              type: 'HOME',
              countryCode: form.homeCountryCode || '',
              number: form.homeNumber || '',
            }
          : null,
        'officeCountryCode' in form
          ? {
              type: 'WORK',
              countryCode: form.officeCountryCode || '',
              number: form.officeNumber || '',
            }
          : null,
      ].filter(Boolean) as Party['contacts']['phones'],
      sameAsPO: form.sameAsPO,
    },
  };
};
