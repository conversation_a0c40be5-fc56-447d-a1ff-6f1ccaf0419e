import {
  invalidWitnessMinAge,
  invalidWitnessSameAsAssured,
  invalidWitnessSameAsNominee,
  invalidWitnessSameAsPolicyOwner,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { nullableDate } from 'features/eAppV2/common/validations/eAppCommonSchema';
import { mysIdNumber } from 'features/eAppV2/my/validations/commonSchema';
import moment from 'moment';
import { Party, PartyRole, PartyType } from 'types/party';
import { Gender } from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';
import { parsePartyDob, toPartyDob } from 'utils/helper/dateUtil';
import { InferType, array, object, ref, string } from 'yup';
import { idName } from '../../commonSchema';

export const witnessInfoSchema = object().shape({
  id: string(),
  title: string().required(requiredMessage),
  fullName: idName().required(requiredMessage),
  primaryIdType: string().required(requiredMessage),
  primaryId: mysIdNumber('primaryIdType', 'dob', 'gender')
    .required(requiredMessage)
    .test(
      'same-person-with-policy-owner',
      invalidWitnessSameAsPolicyOwner,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        const policyOwnerIdNumber = ctx.options?.context?.policyOwnerIdNumber;
        const policyOwnerIdType = ctx.options?.context?.policyOwnerIdType;
        const isSamePersonWithPolicyOwner =
          policyOwnerIdNumber === primaryId &&
          policyOwnerIdType === primaryIdType;
        return !isSamePersonWithPolicyOwner;
      },
    )
    .test(
      'same-person-with-insured',
      invalidWitnessSameAsAssured,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        const insuredIdNumber = ctx.options?.context?.insuredIdNumber;
        const insuredIdType = ctx.options?.context?.insuredIdType;
        const isSamePersonWithInsured =
          insuredIdNumber === primaryId && insuredIdType === primaryIdType;
        return !isSamePersonWithInsured;
      },
    )
    .test(
      'same-person-with-nominee',
      invalidWitnessSameAsNominee,
      (primaryId, ctx) => {
        const primaryIdType = ctx.resolve(ref('primaryIdType'));
        // [nominees.0.primaryId, nominees.1.primaryIdType, nominees.1.primaryId, nominees.1.primaryIdType]
        const nomineesIds: string[] = ctx.options?.context?.nomineesIds;
        let isSamePersonWithNominee = false;
        if (nomineesIds && nomineesIds.length > 0) {
          for (let i = 0; i < nomineesIds.length; i += 2) {
            if (
              nomineesIds[i] === primaryId &&
              nomineesIds[i + 1] === primaryIdType
            ) {
              isSamePersonWithNominee = true;
            }
          }
        }
        return !isSamePersonWithNominee;
      },
    ),
  gender: string().required(requiredMessage),
  dob: nullableDate().test('DOB', invalidWitnessMinAge, value => {
    return moment().diff(moment(value), 'years') >= 18;
  }),
  nominees: array().of(
    object().shape({
      primaryIdType: string(),
      primaryId: string(),
    }),
  ),
});

export type WitnessInfoForm = InferType<typeof witnessInfoSchema>;

export const witnessInfoDefaultValue: WitnessInfoForm = {
  id: '',
  title: '',
  dob: null,
  primaryIdType: '',
  primaryId: '',
  fullName: '',
  gender: '',
};

export const partyToWitnessInfo = (party?: Party): WitnessInfoForm => {
  if (!party) return witnessInfoDefaultValue;
  const primaryId = party.person?.registrations?.find(
    r => r.type === 'DEFAULT',
  );
  return {
    id: party.id || '',
    title: party.person?.name.title || '',
    fullName: party.person?.name.firstName || '',
    dob: party.person?.dateOfBirth
      ? parsePartyDob(party.person?.dateOfBirth)
      : null,
    gender: party.person?.gender || '',
    primaryId: primaryId?.id || '',
    primaryIdType: primaryId?.idType || '',
  };
};

export const witnessInfoToParty = (form: WitnessInfoForm): Party => {
  return {
    id: form.id || '',
    clientType: PartyType.INDIVIDUAL,
    roles: [PartyRole.WITNESS],
    person: {
      name: {
        title: form.title,
        firstName: form.fullName,
      },
      dateOfBirth: toPartyDob(form.dob),
      age: form.dob ? calculateAge(form.dob) : 0,
      gender: form.gender as Gender,
      registrations: [
        {
          type: 'DEFAULT',
          idType: form.primaryIdType,
          id: form.primaryId,
        },
      ],
    },
    contacts: {
      email: '',
      phones: [],
    },
    addresses: [],
  };
};
