export default {
  'documentUpload.deleteTitle': 'Are you sure to delete?',
  'documentUpload.deleteSubtitle': 'Are you sure to delete the uploaded file?',
  cancel: 'Cancel',
  retake: 'Retake',
  usePhoto: 'Use Photo',
  remove: 'Remove',
  'prepopulate.sameCustomer.title': 'Is it the same customer?',
  'prepopulate.title': 'Select a record to import data',
  'prepopulate.fields.lifeAssured': 'Life assured',
  'prepopulate.fields.product': 'Product',
  'prepopulate.fields.policyStatus': 'Policy status',
  'prepopulate.fields.applicationStage': 'Application stage',
  'prepopulate.fields.lastUpdate': 'Last update date',
  'status.FNA': 'FNA',
  'status.CFF': 'CFF',
  'status.QUICK_SI': 'Quick quote',
  'status.FULL_SI': 'SI',
  'status.IN_APP': 'In Application',
  'status.INDIVIDUAL': 'Individual',
  'status.ORGANISATION': 'Organisation',
  'status.APP_SUBMITTED': 'Submitted Policy',
  'status.emptyRecord': 'Empty record',
  'status.COVERAGE': 'Coverage',
  'status.REMOTE_SELLING_COMPLETED': 'Remote selling completed',
  'status.UNKNOWN': 'Unknown',
  'status.REJECTED_BY_LEADER': 'Rejected by leader',
  'status.PENDING_FOR_LEADER': 'Pending for leader',
  'status.APPROVED_BY_LEADER': 'Approved by leader',
  'status.EXPIRED_AFTER_15_DAYS': 'Expired after 15 days',
  'documentUpload.title': 'Upload documents',
  'documentUpload.next': 'Documents - {{role}}',
  'documentUpload.partyDocuments': "{{role}}'s documents",
  'documentUpload.limit':
    'Accepted formats: jpg, jpeg, png or pdf (up to 5MB each)',
  'documentUpload.deleted': 'Deleted the file',
  'documentUpload.failedToUpload': 'Upload document failed',
  'documentUpload.failedToDelete': 'Failed to delete the file',
  'documentUpload.uploading': 'Document uploading...',
  'documentUpload.imagePicker': 'Upload image by',
  'documentUpload.upload': 'Upload',
  'documentUpload.takeA': 'Take a ',
  'documentUpload.closeUp': 'close up',
  'documentUpload.photo': ' photo of your ID card',
  'documentUpload.clearly': 'Clearly ',
  'documentUpload.visible': 'visible and in focus',
  'documentUpload.taken': 'Taken',
  'documentUpload.withoutFlash': ' without flash',
  'documentUpload.documentsOf': 'Documents of {{label}}',
  'documentUpload.f2fQuestion': 'Are you meeting your customer face to face?',
  'documentUpload.f2fQuestion.note':
    'Note: Consent letter is required if your answer is “No”',
  'documentUpload.takePhotoWithCamera': 'Take Photo with Camera',
  'documentUpload.selectFromAlbum': 'Select from Album',
  'documentUpload.attachFile': 'Attach file',
  document: 'Document',
  yes: 'Yes',
  no: 'No',
  goToTheField: 'Go to the field',
  incompleteFields_other: '{{count}} incomplete fields',
  incompleteFields_one: '{{count}} incomplete field',
  next: 'Next',
  'healthQuestion.failedToFinish': 'Failed to finish health question',
  healthQuestionFailed: 'Failed to get health questions. ',
  healthQuestionRetry: 'Retry',
  noHealthQuestion: 'No health questions for {{name}}',
  healthQuestion: 'Health questions - {{role}}',
  'healthQuestion.incomplete.title': 'Incomplete field in health questions',
  'healthQuestion.incomplete.description':
    'There’s a missing field in your health questions, please go back to complete the form.',
  'healthQuestion.incomplete.action': 'Go back',
  consents: 'Consents',
  'healthQuestion.index.a': 'a. {{text}}',
  'healthQuestion.height': 'Height',
  'healthQuestion.cm': 'cm',
  'healthQuestion.ft': 'ft',
  'healthQuestion.in': 'in',
  'healthQuestion.ft&in': 'ft and in',
  'healthQuestion.index.b': 'b. {{text}}',
  'healthQuestion.weight': 'Weight',
  'healthQuestion.kg': 'kg',
  'healthQuestion.lbs': 'lbs',
  ok: 'OK',
  app: 'Application',
  saved: 'Saved',
  exitEApp: 'Are you sure to quit?',
  exitEAppDesc: 'Do you want to save before exit the application?',
  dontSave: "Don't save",
  save: 'Save',
  yourProgress: 'Your progress',
  home: 'Home',
  'signature.title': "{{name}}'s ({{role}}) signature",
  'signature.viewTerms': 'View full version of terms and conditions',
  'signature.moreDetails': 'More details',
  'signature.note':
    'Your signature will be appeared in the document you have reviewed',
  'signature.remoteSellingNote':
    'Note: No signature & place of signature required for Non-face to face option',
  'signature.agreement': 'I, {{name}}, agree to the terms and condition:',
  'signature.declaration': 'Declaration made by {{role}}',
  'signature.clear': 'Clear',
  'signature.placeOfSignature': 'Place of signature',
  'signature.signatureDate':
    '{{role}}: {{name}}  |  Signature date: {{signDate}}',
  'signature.signatureDate.short': '{{name}} - {{signDate}}',
  'signature.cancel': 'Cancel',
  'signature.next': 'Next',
  'signature.agreeAndContinue': 'Agree and continue',
  'signature.gotIt': 'OK, got it',
  'signature.remoteSignature': 'Remote signature',
  'signature.remoteSignature.on': 'ON',
  'signature.remoteSignature.off': 'OFF',
  'language.en': 'English',
  'language.my': 'Bahasa',
  'language.ph': 'Filipino',
  'language.id': 'Indonesian',
  'language.ib': 'English',
  dobAndAge: '{{dob}} ({{age}} y.o.)',
  'personalDetails.scanID': 'Scan ID card',
  'personalDetails.scanID.example': 'e.g. ID card or passport',
  'personalDetails.scanID.info.title': 'Acceptant of ID list',
  'personalDetails.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
  'personalDetails.scanID.info.point.2': 'RP Passport',
  'personalDetails.scanID.info.point.3': "Driver's License",
  'personalDetails.scanID.info.point.4':
    'PRC ID (Professional Regulation Commission ID)',
  'personalDetails.scanID.info.point.5': 'SSS (Social Security System ID)',
  'personalDetails.scanID.info.point.6': 'TIN (Bureau of Internal Revenue ID)',
  'validation.error.emptySpace': 'This field cannot contain only space',
  'validation.error.maxLength60': 'Maximum length is 60',
  'validation.error.maxLength14': 'Maximum length is 14',
  'validation.error.maxLength12': 'Maximum length is 12',
  'validation.error.maxLength10': 'Maximum length is 10',
  'validation.error.maxLength13': 'Maximum length is 13',
  'validation.error.maxLength15': 'Maximum length is 15',
  'validation.error.maxLength16': 'Maximum length is 16',
  'validation.error.maxLength20': 'Maximum length is 20',
  'validation.error.maxLength50': 'Maximum length is 50',
  'validation.error.maxLength30': 'Maximum length is 30',
  'validation.error.maxLength120': 'Maximum length is 120',
  'validation.error.minLength5': 'Minimum length is 5',
  'validation.error.invalidGender': 'Invalid gender',
  'validation.error.date': 'Invalid date',
  'validation.error.dateExpired': 'Card is already expired',
  'validation.error.invalidCardFormat': 'Invalid credit card number format',
  'validation.error.invalidAccountNumber': 'Account number is invalid',
  'validation.error.invalidCardLength':
    'Nomor kartu kredit harus {{length}} digit',
  'validation.error.invalidBankAccountNumberLength':
    'Account number must be {{length}} digits',
  'validation.error.specialCharacterNotAllowed':
    'Special characters are not allowed',
  'other.nominationDetails.nominationNotApplicable':
    'Nomination is not applicable.',
  'other.nominationDetails.nominationDuplicated':
    'The nomination is duplicated.',
  'other.nominationDetails.minimumAllocation': 'Minimum allocation is 10%',
  'other.nominationDetails.requiredTotalAllocation':
    'Payable allocation needs to be 100% in total',
  'validation.error.required': 'Required field',
  'validation.error.duplicate': 'The reason can not be same',
  'validation.error.invalidFormat': 'Invalid format',
  'validation.error.NRICNotMatch':
    'NRIC number doesn’t match with Date of birth or Gender',
  'validation.error.format': 'Error in format',
  'validation.error.invalidAnnualIncomeAmount':
    'Value should be greater than 200,000',
  'validation.error.authorizedSignatoryNotApplicable':
    'The authorised signatory cannot be the same person as the person covered due to potential conflicts of interest',
  'validation.error.insuredNotApplicable':
    'The person covered cannot be the same person as the authorised signatory due to potential conflicts of interest',
  'validation.error.invalidWitnessMinAge':
    'Witness must be at least 18 years old',
  'validation.error.invalidTrusteeMinAge': "Trustee's age <18 years old",
  'validation.error.invalidNomineeSameAsAssured':
    'Nominee cannot be the same as Life Assured',
  'validation.error.invalidNomineeSameAsWitness':
    'Nominee cannot be the same as Witness',
  'validation.error.invalidTrusteeSameAsPolicyOwner':
    'Policy owner cannot be the same as Trustee',
  'validation.error.invalidTrusteeSameAsAssured':
    'Assured cannot be the same as Trustee',
  'validation.error.invalidWitnessSameAsPolicyOwner':
    'Witness cannot be the same as Policy Owner',
  'validation.error.invalidWitnessSameAsAssured':
    'Witness cannot be the same as Life Assured',
  'validation.error.invalidWitnessSameAsNominee':
    'Witness cannot be the same as Nominee',
  'validation.error.invalidParentSameAsPolicyOwner':
    'Parent cannot be the same as Policy Owner',
  'validation.error.invalidPayorSameAsPolicyOwner':
    'Payor cannot be the same as Policy Owner',
  'validation.error.invalidPayorNotMatchAsLifeAssured':
    'Payor details is not matched with Life Assured',
  'validation.error.invalidParentSameAsAssured':
    'Parent cannot be the same as Life Assured',
  'validation.error.invalidAddressLine':
    'Please confirm the address are entered correctly.',
  viewPDPANotice: 'View PDPA Notice',
  'scroll.to.bottom': 'Please scroll to the bottom',
  failedToSaveData: 'Failed to save data',
  failedToSavePaymentData: 'Failed to save payment data',
  failedToStart: 'Failed to start application',
  'review.documentsUploaded': 'Documents uploaded',
  'review.documentOf': 'Document of',
  'summary.health': 'Health questions',
  'validation.error.invalidMobileNumber': 'This mobile number is not allowed',
  'validation.error.invalidEmail': 'This email is not allowed',
  'ocr.upload.title': 'Upload ID card',
  'ocr.upload.title.mobile': 'Scan ID card',
  'ocr.upload.description': 'Scan ID card to pre-populate information below.',
  'ocr.upload.description.mobile': 'e.g. ID card or passport',
  'ocr.upload': 'Upload',
  'ocr.verifiedSuccessfully': 'Successfully verified',
  'ocr.validationMismatch': 'Mismatch data captured',
  'ocr.validationMismatch.firstName': 'First name',
  'ocr.validationMismatch.lastName': 'Last name',
  'ocr.validationMismatch.fullName': 'Full name',
  'ocr.validationMismatch.gender': 'Gender',
  'ocr.validationMismatch.gender.M': 'Male',
  'ocr.validationMismatch.gender.F': 'Female',
  'ocr.validationMismatch.dob': 'Date of birth',
  'ocr.validationMismatch.warning':
    'Data captured from ID card mismatch with the data filled in proposal.',
  'ocr.validationMismatch.noSTP':
    'If you choose to continue, this application will process manual verification.',
  'ocr.error.mismatch': 'Mismatch data captured',
  'ocr.error.missing': 'Missing',
  'ocr.error.firstName': 'First name',
  'ocr.error.lastName': 'Last name',
  'ocr.error.fullName': 'Full name',
  'ocr.error.gender': 'Gender',
  'ocr.error.gender.male': 'Male',
  'ocr.error.gender.female': 'Female',
  'ocr.error.dateOfBirth': 'Date of Birth',
  'ocr.error.nameMismatchWarning':
    'Data captured from ID card mismatch with the data filled in proposal.',
  'ocr.error.dobOrGenderMismatchWarning':
    'If you choose to continue, this application will process manual verification.',
  'ocr.error.retake': 'Retake ID image',
  'ocr.error.update': 'Update and continue',
  'ocr.error.skip': 'Skip and continue',
  'ocr.error.newApplication': 'Create new application',
  'ocr.error.field': ' • {{key}}: {{value}}',
  done: 'Done',
  language: 'Language',
  'ocr.error.blurryPhoto.title': 'Blurry data captured',
  'ocr.error.blurryPhoto.desc':
    'The data captured from your ID is blurry, please retake the photo.',
  'ocr.error.invalidPhoto.title': 'Invalid card',
  'ocr.error.invalidPhoto.desc':
    'Invalid ID card, please upload another one to proceed with the application.',
  'ocr.error.blankPhoto.title': 'Blank data captured',
  'ocr.error.blankPhoto.desc':
    'The data captured from this ID is blank, please retake the image.',
  'ocr.verified': 'Verified',
  details: '{{screen}} details',
  eAppAlert:
    'We’re really sorry to be holding you up today!\nThere seems to be a temporary glitch on our end. Give it another try in a moment, and things should be back to normal',
  tryAgain: 'Try again',
  back: 'Back',
};
