import { useEAppProgressBarStore } from '../utils/store/eAppProgressBarStore';
import {
  RouteGroupKey,
  RouteItemKey,
  RouteSubgroupKey,
} from '../types/progressBarTypes';
import useUpdateEffect from 'hooks/useUpdateEffect';
import useLatest from 'hooks/useLatest';
import { useSaveApplicationProgress } from 'hooks/useApplicationProgress';
import { generateRouteKey } from './useGenerateEAppRoutes';
import { ApplicationProgress } from 'api/caseApi';

export const useIncompleteSync = (
  incomplete: boolean,
  groupKey?: RouteGroupKey,
  subgroupKey?: RouteSubgroupKey,
  itemKey?: RouteItemKey,
  enabled: boolean | undefined = true,
  autoSync: boolean | undefined = false,
) => {
  const markCompletedByKeys = useEAppProgressBarStore(
    state => state.markCompletedByKeys,
  );

  const latestGroupKey = useLatest(groupKey);
  const latestSubgroupKey = useLatest(subgroupKey);
  const latestItemKey = useLatest(itemKey);
  const latestMarkCompletedByKeys = useLatest(markCompletedByKeys);
  const { saveApplicationProgress } = useSaveApplicationProgress();

  useUpdateEffect(() => {
    if (enabled) {
      if (incomplete) {
        latestMarkCompletedByKeys.current(
          [
            latestGroupKey.current,
            latestSubgroupKey.current,
            latestItemKey.current,
          ],
          false,
        );
        if (autoSync) {
          const key = generateRouteKey([
            latestGroupKey.current,
            latestSubgroupKey.current,
            latestItemKey.current,
          ]);
          saveApplicationProgress({
            [key]: ApplicationProgress.IN_PROGRESS,
          }).then();
        }
      }
    }
  }, [incomplete]);
};
