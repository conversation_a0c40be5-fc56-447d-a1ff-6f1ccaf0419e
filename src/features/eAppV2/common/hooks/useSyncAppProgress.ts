import { ApplicationProgress } from 'api/caseApi';
import { useSaveApplicationProgress } from 'hooks/useApplicationProgress';
import { useCallback } from 'react';
import { useEAppProgressBarStore } from '../utils/store/eAppProgressBarStore';

export const useSyncAppProgress = () => {
  const { saveApplicationProgress } = useSaveApplicationProgress();

  const completedMap = useEAppProgressBarStore(state => state.completedMap);

  const sync = useCallback(async () => {
    // Sync completedMap to server
    const progress = Object.keys(completedMap).reduce<{
      [key: string]: ApplicationProgress;
    }>((progress, key: string) => {
      if (typeof completedMap[key] === 'boolean') {
        progress[key] = completedMap[key]
          ? ApplicationProgress.COMPLETED
          : ApplicationProgress.IN_PROGRESS;
      }
      return progress;
    }, {});
    await saveApplicationProgress(progress);
  }, [completedMap, saveApplicationProgress]);
  return sync;
};
