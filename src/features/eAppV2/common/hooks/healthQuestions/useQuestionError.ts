import { HealthQuestion } from 'types/healthQuestion';

type Props = {
  question: HealthQuestion;
};

const useQuestionError = ({ question }: Props) => {
  const { validationErrors } = question;
  const errorList = Object.keys(validationErrors).flatMap(
    errorKey => validationErrors[errorKey],
  );
  let errorMessage = '';

  if (!errorList.length) errorMessage = '';

  errorMessage = errorList[0];

  return errorMessage;
};

export default useQuestionError;
