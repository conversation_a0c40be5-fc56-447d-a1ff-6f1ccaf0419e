import { useMemo } from 'react';
import {
  OptionBackedHealthQuestion,
  OptionGroupHealthQuestion,
  OptionListHealthQuestion,
  QuestionOptionsList,
} from 'types/healthQuestion';

type Props = {
  question:
    | OptionGroupHealthQuestion
    | OptionListHealthQuestion
    | OptionBackedHealthQuestion;
};

const useQuestionOptionList = ({ question }: Props) => {
  const {
    options: embedOptions,
    optionTags: embedOptionTags,
    optionListName,
  } = question.definition;

  const _mappedOptionsList: QuestionOptionsList | undefined = useMemo(() => {
    if (embedOptions?.length) {
      return {
        name: optionListName,
        options: embedOptions?.map(optionText => ({
          text: optionText,
          tags: embedOptionTags?.[optionText] || [],
        })),
      };
    }
    return {
      name: optionListName,
      options: [],
    };
  }, [embedOptions?.length]);

  return {
    optionsList: _mappedOptionsList,
  };
};

export default useQuestionOptionList;
