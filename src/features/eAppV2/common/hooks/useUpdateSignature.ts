import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useUploadDocument } from 'hooks/useUploadDocument';
import { useCallback } from 'react';
import { Application } from 'types/case';
import { DocumentCustomerType, DocumentType } from 'types/document';
import { useGetActiveCase } from 'hooks/useGetActiveCase';

export const useUpdateSignature = () => {
  const { mutateAsync: uploadDocument, isLoading: isUploading } =
    useUploadDocument();
  const caseId = useBoundStore(state => state.case.caseId);
  const { caseObj } = useGetActiveCase();
  const applicationNum = caseObj?.application?.applicationNum || '';
  const { mutateAsync: getCase, isLoading: isGettingCase } =
    useGetCaseManually();
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const updateSignature = useCallback(
    async ({
      imageBase64,
      signedTime,
      party,
      custSeq = '1',
      placeOfSignature,
    }: {
      imageBase64?: string;
      signedTime: number;
      party: keyof NonNullable<Required<Application>['signature']>;
      custSeq?: string;
      placeOfSignature?: string;
    }) => {
      if (!caseId) {
        throw new Error('Case id not found while saving signature');
      }
      const caseObj = await getCase(caseId);
      let customerType: DocumentCustomerType = DocumentCustomerType.NA;
      if (party === 'agent') {
        customerType = DocumentCustomerType.AG;
      } else if (
        ['insureds', 'trustees', 'witnesses', 'parents'].includes(party)
      ) {
        customerType = DocumentCustomerType.PI;
      } else if (party === 'proposers') {
        customerType = DocumentCustomerType.PO;
      }
      let filePath = '';
      if (imageBase64) {
        const uploadResult = await uploadDocument({
          body: {
            custType: customerType,
            custSeq: custSeq,
            docType: DocumentType.Signature,
            fileContent: imageBase64,
            applicationNum: applicationNum,
            fileType: 'png',
          },
        });
        filePath = uploadResult.filePath;
      }
      const application: Application = caseObj.application ?? {};
      if (application.signature) {
        const partySignature = application.signature[party];
        if (partySignature) {
          partySignature[Number(custSeq) - 1 || 0] = {
            fileId: filePath,
            signatureDt: new Date(signedTime).toISOString(),
            placeOfSignature,
          };
        } else {
          application.signature[party] = [
            {
              fileId: filePath,
              signatureDt: new Date(signedTime).toISOString(),
              placeOfSignature,
            },
          ];
        }
      } else {
        application.signature = {
          [party]: [
            {
              fileId: filePath,
              signatureDt: new Date(signedTime).toISOString(),
              placeOfSignature,
            },
          ],
        };
      }
      await saveApplication({
        caseId,
        data: application,
      });
      return { filePath };
    },
    [uploadDocument, saveApplication, caseId, getCase, applicationNum],
  );

  const removeSignature = useCallback(
    async ({
      party,
      custSeq = '1',
    }: {
      party: keyof NonNullable<Required<Application>['signature']>;
      custSeq?: string;
    }) => {
      if (!caseId) {
        throw new Error('Case id not found while saving signature');
      }
      const caseObj = await getCase(caseId);
      const application: Application = caseObj.application ?? {};
      if (application.signature) {
        const partySignature = application.signature[party];
        if (partySignature) {
          partySignature.splice(Number(custSeq) - 1 || 0, 1);
        } else {
          application.signature[party] = [];
        }
      } else {
        application.signature = {
          [party]: [],
        };
      }
      await saveApplication({
        caseId,
        data: application,
      });
    },
    [saveApplication, caseId, getCase],
  );

  return {
    updateSignature,
    removeSignature,
    isLoading: isGettingCase || isUploading || isSavingApplication,
  };
};
