import useBoundStore from 'hooks/useBoundStore';
import usePrevious from 'hooks/usePrevious';
import { useEffect, useRef } from 'react';
import { LOADING_STATUS } from 'types';
import { Schema, ValidationError } from 'yup';

const useAutoDone = ({
  schema,
  done,
  value,
  onDone,
}: {
  schema?: Schema;
  done?: boolean;
  value: any;
  onDone: (value: any) => void;
}) => {
  const loadingStatus = useBoundStore(state => state.loadingStatus);
  const previousLoadingStatus = usePrevious(loadingStatus);

  const case1Ref = useRef(false);
  const case2Ref = useRef(false);

  useEffect(() => {
    if (schema) {
      const loaded =
        loadingStatus === LOADING_STATUS.IDLE &&
        previousLoadingStatus === LOADING_STATUS.LOADING;
      //Wait for data, auto navigation
      const case1 = loaded && !case1Ref.current;
      //Always validate at the first time;
      const case2 = !case2Ref.current;
      if (case1 || case2) {
        if (case1) {
          case1Ref.current = true;
        }
        if (case2) {
          case2Ref.current = true;
        }
        if (!done) {
          schema.validate(value).then(
            () => {
              onDone(value);
            },
            (error: ValidationError) => {
              if (error.errors.length === 1 && error.path === 'expiryDate') {
                onDone(value);
              }
            },
          );
        }
      }
    }
  }, [loadingStatus]);
};

export default useAutoDone;
