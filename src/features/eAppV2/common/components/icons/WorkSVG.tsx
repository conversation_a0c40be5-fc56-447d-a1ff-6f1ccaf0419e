import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function WorkSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 28}
      height={props.height || 24}
      viewBox="0 0 28 24"
      fill="none">
      <Path
        fill="#183028"
        d="M18.56 3.318v.86h.869v-.86c0-1.232-.98-2.252-2.18-2.369l-.02.874c.717.112 1.331.746 1.331 1.495Z"
      />
      <Path
        fill="#E87722"
        d="m2.114 13.586.552 7.88c.085 1.217.965 2.154 2.023 2.154h18.614c1.057 0 1.937-.937 2.02-2.154l.551-7.88c-.083.114-23.674.114-23.76 0ZM11.526 2.318h4.94c.34 0 .632-.203.76-.494.009 0 .02-.895.02-.895a.827.827 0 0 0-.78-.548h-4.94a.83.83 0 0 0-.777.54s.011.906.017.903a.83.83 0 0 0 .76.494Z"
      />
      <Path
        fill="#183028"
        d="M9.432 4.178v-.86c0-.754.609-1.386 1.334-1.494L10.75.955c-1.208.112-2.183 1.126-2.183 2.363v.86h.866Z"
      />
      <Path
        fill="#F3BB90"
        d="M23.906 4.129H4.089A3.103 3.103 0 0 0 .994 7.466l.412 5.411a2.949 2.949 0 0 0 2.94 2.726h19.308a2.95 2.95 0 0 0 2.94-2.726l.412-5.411a3.113 3.113 0 0 0-3.1-3.337Z"
      />
      <Path
        fill="#183028"
        d="M15.743 16.553c.297 0 .537-.297.537-.663v-.971c0-.366-.24-.663-.537-.663H12.25c-.297 0-.537.297-.537.663v.971c0 .366.24.663.537.663h3.494Z"
      />
    </Svg>
  );
}
