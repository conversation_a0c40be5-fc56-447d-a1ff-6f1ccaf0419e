import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import CubeWebView from 'components/CubeWebView';
import FloatingHintButton from 'components/FloatingHintButton';
import LoadingIndicator from 'components/LoadingIndicator';
import { Box, H6 } from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import ApplicationDetailsBottomSheet from 'features/eAppV2/common/components/ApplicationDetailsBottomSheet';
import { useTranslation } from 'react-i18next';
import { NativeSyntheticEvent } from 'react-native';

export const PdpaNoticePhone = ({
  url,
  nextScreenText = '',
  onDismiss,
  onAgree,
  isPrimaryDisabled,
  injectedScript,
  onScroll,
  handleShouldStartLoadWithRequest,
  visible,
}: {
  visible: boolean;
  url: string;
  nextScreenText?: string;
  reviewed?: boolean;
  onDismiss: () => void;
  onAgree?: () => void;
  isPrimaryDisabled?: boolean;
  injectedScript?: string;
  onScroll?: (event: NativeSyntheticEvent<any>) => void | Promise<void>;
  handleShouldStartLoadWithRequest?: (event: { url: string }) => boolean;
}) => {
  const { t } = useTranslation(['eApp']);
  const { colors, borderRadius, space } = useTheme();
  return (
    <>
      {visible && (
        <ApplicationDetailsBottomSheet
          noFooterBorder
          enableContentPanningGesture={false}
          onDismiss={onDismiss}
          disabled={isPrimaryDisabled}
          onPress={onAgree}
          buttonSubTitle={nextScreenText}>
          <Box m={space[4]}>
            <H6 fontWeight="bold">{t('eApp:viewPDPANotice')}</H6>
          </Box>
          <Box
            flex={1}
            mx={space[4]}
            borderRadius={borderRadius.medium}
            borderWidth={1}
            borderColor={colors.palette.fwdGrey[100]}
            overflow="hidden">
            <WebViewContainer
              source={{
                uri: url,
              }}
              onScroll={onScroll}
              onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
              javaScriptEnabled
              domStorageEnabled
              onMessage={() => null}
              injectedJavaScript={injectedScript}
              startInLoadingState
              renderLoading={() => (
                <LoadingIndicatorContainer>
                  <LoadingIndicator size={sizes[20]} />
                </LoadingIndicatorContainer>
              )}
            />
            <FloatingHintButton
              visible={isPrimaryDisabled}
              text={t('eApp:scroll.to.bottom')}
              containerStyle={{ bottom: 12 }}
            />
          </Box>
          <BottomSheetFooterSpace />
        </ApplicationDetailsBottomSheet>
      )}
    </>
  );
};

const WebViewContainer = styled(CubeWebView)(
  ({ theme: { borderRadius, space } }) => ({
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0)',
  }),
);

const LoadingIndicatorContainer = styled.View(
  ({ theme: { borderRadius } }) => ({
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#525558',
    borderRadius: borderRadius.large,
  }),
);
