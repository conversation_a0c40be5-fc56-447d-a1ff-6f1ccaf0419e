import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useAlert } from 'hooks/useAlert';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import {
  ComponentProps,
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { HealthQuestion2, Section as SectionType } from 'types/healthQuestion';
import {
  useAnswerHealthQuestion,
  useCloseHealthQuestion,
  useGetHealthQuestions,
} from 'hooks/useHealthQuestion';
import { FormProvider, useForm } from 'react-hook-form';
import useHealthQuestions from '../../hooks/healthQuestions/useHealthQuestions';
import useHealthQuestionDetail from '../../hooks/healthQuestions/useHealthQuestionDetail';
import { useEAppProgressBarStore } from '../../utils/store/eAppProgressBarStore';
import { useFilterHealthQuestion } from '../../hooks/healthQuestions/useFilterHealthQuestion';
import Section from './section/Section';
import { Box, LargeBody, LargeLabel, Typography } from 'cube-ui-components';
import HealthQuestionEnquiryContext from './context/HealthQuestionEnquiryContext';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';
import IconEmpty from 'features/eAppV2/my/components/applicationDetails/healthQuestions/IconEmpty';
import { RefreshControl } from 'react-native';
import TabletSections, {
  TabletSectionsProps,
  flattenSectionNameList,
} from '../TabletSections';
import { PartyRole } from 'types/party';
import { useGetOwb } from '../../hooks/useGetOwb';
import { country } from 'utils/context';
import EAppFooterTablet from '../footer/EAppFooter.tablet';
import LoadingIndicator from 'components/LoadingIndicator';

interface Props {
  tabs: {
    key: string;
    partyId: string;
    role: string;
    disabled?: boolean;
    customValidation?: (data: HealthQuestion2) => boolean;
    onNext?: () => void;
  }[];
  progressLock?: ComponentProps<typeof EAppFooterTablet>['progressLock'];
}

export type HealthQuestionsBaseTabletProps = Props;

export default function HealthQuestionsBaseTablet({
  progressLock,
  tabs,
}: Props) {
  const [data, setData] = useState<HealthQuestion2>();
  const { space, sizes, colors } = useTheme();
  const { t } = useTranslation(['common', 'eApp']);
  const [activePath, setActivePath] = useState('');
  const { alertError, alertBackendError } = useAlert();
  // const { groups } = useEAppProgressBarStore(
  //   state => ({
  //     groups: state.groups,
  //   }),
  //   shallow,
  // );
  // const healthQuestionSubgroup: ProgressSubgroup = groups?.[0]?.items?.[
  //   groups?.[0]?.items.length - 1
  // ] as ProgressSubgroup;

  const activeTab = useMemo(
    () => {
      if (activePath.includes('.')) {
        const [key, index] = activePath.split('.');
        return tabs.filter(tab => tab.key === key)[Number(index)];
      }
      return tabs.find(tab => tab.key === activePath);
    },
    [activePath, tabs],
  );
  const isCurrentTabSatisfied =
    data?.isSatisfied &&
    data?.isCloseable &&
    (!activeTab?.customValidation || activeTab.customValidation(data));

  const {
    startGeneratingHealthQuestion,
    isLoadingHealthQuestions,
    isError: isErrorGeneratingHealthQuestion,
  } = useHealthQuestions();

  const {
    mutateAsync: closeHealthQuestion,
    isLoading: isClosingHealthQuestion,
  } = useCloseHealthQuestion();

  const hqForm = useForm();
  const reset = hqForm.reset;

  const { getOwb, isLoading: isGettingOwb } = useGetOwb();

  const { caseObj } = useGetActiveCase();
  const partyIdToEnquiryId = useMemo<Record<string, string>>(
    () =>
      caseObj?.parties
        ?.filter(p => p.uw?.enquiryId)
        .reduce<Record<string, string>>((map, p) => {
          map[p.id] = p.uw?.enquiryId as string;
          return map;
        }, {}) || {},
    [caseObj?.parties],
  );
  const activeEnquiryId = partyIdToEnquiryId[activeTab?.partyId || ''];

  const healthQuestionForms = useGetHealthQuestions(
    useMemo(() => Object.values(partyIdToEnquiryId), [partyIdToEnquiryId]),
  );

  const areAllTabsSatisfied = healthQuestionForms.every(form => {
    const tab = tabs.find(
      tab => partyIdToEnquiryId[tab.partyId] === form.data?.enquiryId,
    );
    return (
      form?.data?.isSatisfied &&
      form?.data?.isCloseable &&
      (!tab?.customValidation || tab.customValidation(form.data))
    );
  });

  const {
    initialData,
    isLoadingHealthQuestionDetail,
    refetch: refetchHealthQuestion,
    isError: isErrorHealthQuestionDetail,
  } = useHealthQuestionDetail(activeEnquiryId);

  const { mutateAsync: answerHealthQuestion, isLoading: isAnswering } =
    useAnswerHealthQuestion({
      onSuccess: setData,
    });

  const doneInitForm = useRef('');
  useEffect(() => {
    if (initialData && doneInitForm.current != activeEnquiryId) {
      doneInitForm.current = activeEnquiryId;
      setData(initialData);
      if (initialData?.allAnswers) {
        reset(initialData.allAnswers);
      }
    }
  }, [reset, initialData, activeEnquiryId]);

  useEffect(() => {
    const subscription = hqForm.watch(formValues => {
      if (!activeEnquiryId || !formValues) return;
      answerHealthQuestion({
        id: activeEnquiryId,
        requestData: { answers: formValues },
      }).catch(alertBackendError);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, [activeEnquiryId, alertBackendError, answerHealthQuestion, hqForm]);

  // TODO: move it out
  useEffect(() => {
    if (country === 'my') {
      if (
        data?.sections?.some(e =>
          e?.enquiryLines?.some(
            l =>
              l.path === 'SIO_Application' &&
              l.questions?.some(
                q => q.name === 'Proceed_FUW' && q.answers?.length === 0,
              ),
          ),
        )
      ) {
        hqForm.setValue('SIO_Application/Proceed_FUW', ['Yes']);
      }
    }
  }, [data, hqForm]);

  const renderSection = useCallback(
    ({ item: section }: { item: SectionType }) => {
      return <Section section={section} />;
    },
    [],
  );

  // const next = useEAppProgressBarStore(state => state.next);
  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);

  const filteredSection = useFilterHealthQuestion(data?.sections || []);

  const renderHealthQuestionForm = useCallback(
    ({ role, name }: { role: string; name: string }) => {
      return (
        <Box flex={1}>
          <FormProvider {...hqForm}>
            <HealthQuestionEnquiryContext.Provider
              value={{
                branch: data?.branch || '',
                tag: data?.tag || '',
              }}>
              <KeyboardAwareFlatList
                data={filteredSection}
                renderItem={renderSection}
                keyExtractor={item => item.name}
                contentContainerStyle={{
                  flexGrow: filteredSection.length === 0 ? 1 : 0,
                  marginRight: space[8],
                }}
                enableResetScrollToCoords={false}
                ListEmptyComponent={
                  <Fragment>
                    {!(
                      isLoadingHealthQuestionDetail ||
                      isGettingOwb ||
                      isLoadingHealthQuestions ||
                      isAnswering ||
                      isErrorGeneratingHealthQuestion ||
                      isErrorHealthQuestionDetail
                    ) && (
                      <Box
                        alignItems="center"
                        gap={space[4]}
                        py={17.25}
                        pl={space[8]}
                        flex={1}
                        justifyContent="center">
                        <IconEmpty />
                        <LargeBody color={colors.placeholder}>
                          {t('eApp:noHealthQuestion', { name })}
                        </LargeBody>
                      </Box>
                    )}
                  </Fragment>
                }
                ListHeaderComponent={
                  <Fragment>
                    {filteredSection.length !== 0 && (
                      <Box mt={space[5]} mb={space[4]}>
                        <Typography.H6 fontWeight="bold">
                          {t('eApp:healthQuestion', {
                            role: role.toLowerCase(),
                          })}
                        </Typography.H6>
                      </Box>
                    )}
                  </Fragment>
                }
                ListFooterComponent={<Box h={space[4]} />}
                refreshControl={
                  <RefreshControl
                    refreshing={false}
                    onRefresh={refetchHealthQuestion}
                  />
                }
              />
            </HealthQuestionEnquiryContext.Provider>
          </FormProvider>

          {(isLoadingHealthQuestionDetail ||
            isGettingOwb ||
            isAnswering ||
            isLoadingHealthQuestions) && (
            <LoadingBox>
              <LoadingIndicator size={sizes[12]} color={colors.primary} />
            </LoadingBox>
          )}
          {isErrorGeneratingHealthQuestion && filteredSection.length === 0 && (
            <LoadingBox>
              <LargeLabel style={{ textAlign: 'center' }}>
                {t('eApp:healthQuestionFailed')}
                <LargeLabel
                  suppressHighlighting
                  onPress={startGeneratingHealthQuestion}
                  color={colors.primary}>
                  {t('eApp:healthQuestionRetry')}
                </LargeLabel>
              </LargeLabel>
            </LoadingBox>
          )}
          {isErrorHealthQuestionDetail && filteredSection.length === 0 && (
            <LoadingBox>
              <LargeLabel style={{ textAlign: 'center' }}>
                {t('eApp:healthQuestionFailed')}
                <LargeLabel
                  suppressHighlighting
                  onPress={() => {
                    refetchHealthQuestion();
                  }}
                  color={colors.primary}>
                  {t('eApp:healthQuestionRetry')}
                </LargeLabel>
              </LargeLabel>
            </LoadingBox>
          )}
        </Box>
      );
    },
    [
      colors.placeholder,
      colors.primary,
      data?.branch,
      data?.tag,
      filteredSection,
      hqForm,
      isAnswering,
      isErrorGeneratingHealthQuestion,
      isErrorHealthQuestionDetail,
      isGettingOwb,
      isLoadingHealthQuestionDetail,
      isLoadingHealthQuestions,
      refetchHealthQuestion,
      renderSection,
      sizes,
      space,
      startGeneratingHealthQuestion,
      t,
    ],
  );

  // useSyncActivePath(
  //   groups[0]?.routeKey,
  //   healthQuestionSubgroup?.routeKey,
  //   healthQuestionSubgroup?.items?.[0]?.routeKey,
  // );

  const sections = useMemo(() => {
    const insureds =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER),
      ) || [];
    const isSingleInsured = insureds.length <= 1;
    const multiInsuredSection: TabletSectionsProps['items'][0] = {
      name: '',
      title: '',
      subItems: [],
    };
    const sections = tabs.reduce<TabletSectionsProps['items']>(
      (sections, tab) => {
        const party = caseObj?.parties?.find(p => p.id === tab.partyId);
        const name = party?.person?.name?.fullName?.trim();
        const isInsured =
          party?.roles.includes(PartyRole.INSURED) &&
          !party?.roles.includes(PartyRole.PROPOSER);
        if (isInsured && !isSingleInsured) {
          multiInsuredSection.title = tab.role;
          multiInsuredSection.name = tab.key;
          multiInsuredSection.subItems?.push({
            name: String(multiInsuredSection.subItems?.length || 0),
            title: name ?? '',
            content: renderHealthQuestionForm({
              role: tab.role,
              name: name ?? '',
            }),
          });
        } else {
          sections.push({
            name: tab.key,
            title: tab.role,
            subtitle: name || '',
            content: renderHealthQuestionForm({
              role: tab.role,
              name: name || tab.role || '',
            }),
            disabled: tab.disabled,
          });
        }
        return sections;
      },
      [],
    );
    if (
      multiInsuredSection.subItems?.length &&
      multiInsuredSection.subItems.length > 0
    ) {
      sections.push(multiInsuredSection);
    }
    return sections;
  }, [caseObj?.parties, tabs, renderHealthQuestionForm]);

  const { flattenPaths, activePathIndex, isLastTabActive } = useMemo(() => {
    const flattenPaths = flattenSectionNameList(sections);
    const activePathIndex = flattenPaths.findIndex(path => path === activePath);
    const isLastTabActive = activePathIndex === flattenPaths.length - 1;
    return {
      flattenPaths,
      activePathIndex,
      isLastTabActive,
    };
  }, [activePath, sections]);

  const onSubmit = useCallback(async () => {
    try {
      if (
        isLastTabActive &&
        healthQuestionForms.every(form => form.data?.isCloseable)
      ) {
        const owbModel = await getOwb();
        const res = await closeHealthQuestion(owbModel);
        if (
          res.policy.parties.length === 0 ||
          res.policy.parties.some(p => p.enquiry_status === 'Open')
        ) {
          alertError(t('eApp:healthQuestion.failedToFinish'));
          return;
        }
        activeTab?.onNext?.();
        nextGroup(true);
      } else if (!isLastTabActive) {
        activeTab?.onNext?.();
        setActivePath(
          flattenPaths[Math.min(activePathIndex + 1, flattenPaths.length - 1)],
        );
      }
    } catch {
      alertBackendError();
    }
  }, [
    isLastTabActive,
    healthQuestionForms,
    getOwb,
    closeHealthQuestion,
    activeTab,
    nextGroup,
    alertError,
    t,
    flattenPaths,
    activePathIndex,
    alertBackendError,
  ]);

  const subtext = useMemo(() => {
    const flattenPaths = flattenSectionNameList(sections);
    const activePathIndex = flattenPaths.findIndex(path => path === activePath);
    const isLastTabActive = activePathIndex === flattenPaths.length - 1;
    if (isLastTabActive) {
      return t('eApp:consents');
    } else {
      return tabs[activePathIndex + 1].role;
    }
  }, [activePath, sections, t, tabs]);

  return (
    <Fragment>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock={progressLock}
        primaryDisabled={
          isLoadingHealthQuestions ||
          isLoadingHealthQuestionDetail ||
          isGettingOwb ||
          isErrorGeneratingHealthQuestion ||
          isErrorHealthQuestionDetail ||
          isAnswering ||
          !data ||
          !isCurrentTabSatisfied ||
          (isLastTabActive ? !areAllTabsSatisfied : false)
        }
        onPrimaryPress={onSubmit}
        primaryLoading={isClosingHealthQuestion}
        primarySubLabel={subtext}
      />
    </Fragment>
  );
}

const LoadingBox = styled.View(() => {
  return {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    alignItems: 'center',
    justifyContent: 'center',
  };
});
