import React, { useMemo, useCallback, useEffect, useState, useRef } from 'react';
import {
  Box,
  LargeBody,
  LargeLabel,
  Typography,
  addErrorBottomToast,
} from 'cube-ui-components';
import Section from 'features/eAppV2/common/components/healthQuestions/section/Section';
import { useTheme } from '@emotion/react';
import { FormProvider, useForm } from 'react-hook-form';
import { HealthQuestion2, Section as SectionType } from 'types/healthQuestion';
import styled from '@emotion/native';
import HealthQuestionEnquiryContext from 'features/eAppV2/common/components/healthQuestions/context/HealthQuestionEnquiryContext';
import LoadingIndicator from 'components/LoadingIndicator';
import { KeyboardAwareFlatList } from 'react-native-keyboard-aware-scroll-view';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { shallow } from 'zustand/shallow';
import {
  useAnswerHealthQuestion,
  useCloseHealthQuestion,
  useGetHealthQuestions,
} from 'hooks/useHealthQuestion';
import { useFilterHealthQuestion } from 'features/eAppV2/common/hooks/healthQuestions/useFilterHealthQuestion';
import { RefreshControl } from 'react-native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useHealthQuestions from 'features/eAppV2/common/hooks/healthQuestions/useHealthQuestions';
import useHealthQuestionDetail from 'features/eAppV2/common/hooks/healthQuestions/useHealthQuestionDetail';
import { useGetOwb } from 'features/eAppV2/common/hooks/useGetOwb';
import { ProgressSubgroup } from 'features/eAppV2/common/types/progressBarTypes';
import EAppFooterPhone from '../footer/EAppFooter.phone';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useAlert } from 'hooks/useAlert';
import { useTranslation } from 'react-i18next';
import useLatest from 'hooks/useLatest';
import IconEmpty from 'features/eAppV2/my/components/applicationDetails/healthQuestions/IconEmpty';
import { useGetPartyFromActiveCase } from 'hooks/useParty';
import { EAppFooterProps } from '../footer/eAppFooterTypes';

interface Props {
  activePartyId: string;
  role: string;
  customValidation?: (data: HealthQuestion2) => boolean;
  onNext?: () => void;
  progressLock?: string;
}

export default function HealthQuestionsBasePhone({
  activePartyId,
  role,
  customValidation,
  onNext,
  progressLock,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { space, sizes, colors } = useTheme();
  const [data, setData] = useState<HealthQuestion2>();
  const { alertBackendError } = useAlert();

  const { itemKey, next, groups } = useEAppProgressBarStore(
    state => ({
      next: state.next,
      groups: state.groups,
      itemKey: state.itemKey,
      subgroupKey: state.subgroupKey,
      groupKey: state.groupKey,
    }),
    shallow,
  );

  const { caseObj } = useGetActiveCase();
  const party = useGetPartyFromActiveCase(activePartyId);
  const partyIdToEnquiryId = useMemo<Record<string, string>>(
    () =>
      caseObj?.parties
        ?.filter(p => p.uw?.enquiryId)
        .reduce<Record<string, string>>((map, p) => {
          map[p.id] = p.uw?.enquiryId as string;
          return map;
        }, {}) || {},
    [caseObj?.parties],
  );

  const healthQuestionForms = useGetHealthQuestions(
    Object.values(partyIdToEnquiryId),
  );

  const healthQuestionSubgroup: ProgressSubgroup = groups?.[0]?.items?.[
    groups?.[0]?.items.length - 1
  ] as ProgressSubgroup;

  const isLastItemInHQ =
    healthQuestionSubgroup?.items?.[healthQuestionSubgroup.items.length - 1]
      ?.routeKey === itemKey;

  const isAbleToPassHQStep = useMemo(() => {
    if (isLastItemInHQ) {
      const areAllHQsComplete = healthQuestionForms.every(
        form => form.data?.isCloseable,
      );

      const arePreviousStepsComplete = groups?.[0]?.items
        .slice(0, groups?.[0]?.items.length - 1)
        .every(i => i.completed);
      return arePreviousStepsComplete && areAllHQsComplete;
    } else {
      return true;
    }
  }, [groups, healthQuestionForms, isLastItemInHQ]);

  const areAllQuestionsSatisfied =
    data?.isSatisfied &&
    data?.isCloseable &&
    (!customValidation || customValidation(data));

  const activeEnquiryId = partyIdToEnquiryId[activePartyId];

  const {
    startGeneratingHealthQuestion,
    isLoadingHealthQuestions,
    isError: isErrorGenerationHealthQuestion,
  } = useHealthQuestions();

  const {
    initialData,
    isLoadingHealthQuestionDetail,
    refetch: refetchHealthQuestion,
    isError: isErrorHealthQuestionDetail,
  } = useHealthQuestionDetail(activeEnquiryId);

  const form = useForm();
  const reset = form.reset;
  const { mutateAsync: answerHealthQuestion, isLoading: isAnswering } =
    useAnswerHealthQuestion({
      onSuccess: setData,
    });
  useEffect(() => {
    const subscription = form.watch(formValues => {
      if (!activeEnquiryId || !formValues) return;
      answerHealthQuestion({
        id: activeEnquiryId,
        requestData: { answers: formValues },
      }).catch(alertBackendError);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, [
    form.watch,
    answerHealthQuestion,
    form,
    data,
    alertBackendError,
    activeEnquiryId,
  ]);

  const { getOwb, isLoading: isGettingOwb } = useGetOwb();
  const {
    mutateAsync: closeHealthQuestion,
    isLoading: isClosingHealthQuestion,
  } = useCloseHealthQuestion();

  const renderSection = useCallback(
    ({ item: section }: { item: SectionType }) => {
      return <Section section={section} />;
    },
    [],
  );

  const doneInitForm = useRef('');
  useEffect(() => {
    if (initialData && doneInitForm.current != activeEnquiryId) {
      doneInitForm.current = activeEnquiryId;
      setData(initialData);
      if (initialData?.allAnswers) {
        reset(initialData.allAnswers);
      }
    }
  }, [reset, initialData, activeEnquiryId]);

  const onNextRef = useLatest(onNext);
  const onSubmit = useCallback(async () => {
    try {
      if (
        isLastItemInHQ &&
        healthQuestionForms.every(form => form.data?.isCloseable)
      ) {
        const owbModel = await getOwb();
        const res = await closeHealthQuestion(owbModel);
        if (
          res.policy.parties.length === 0 ||
          res.policy.parties.some(p => p.enquiry_status === 'Open')
        ) {
          addErrorBottomToast([
            {
              message: t('eApp:healthQuestion.failedToFinish'),
            },
          ]);
          return;
        }
        onNextRef.current?.();
        next(true);
      } else if (!isLastItemInHQ) {
        onNextRef.current?.();
        next(true);
      }
    } catch {
      alertBackendError();
    }
  }, [
    isLastItemInHQ,
    healthQuestionForms,
    onNextRef,
    next,
    getOwb,
    closeHealthQuestion,
    t,
    alertBackendError,
  ]);

  const filteredSection = useFilterHealthQuestion(data?.sections || []);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const nextSubLabel = useMemo(() => {
    if (!groups || !groups?.[0]) return '';
    const othersGroupIndex = groups[0].items.findIndex(
      i => i.routeKey === 'healthQuestion',
    );
    if (othersGroupIndex === -1) return '';
    const othersGroup = groups[0].items[othersGroupIndex] as ProgressSubgroup;
    const currentIndex = othersGroup?.items.findIndex(
      i => i.routeKey === itemKey,
    );
    const isLastStep = currentIndex === othersGroup?.items.length - 1;
    if (isLastStep) {
      return groups[1].title;
    }
    return t('eApp:details', {
      screen: othersGroup.items[currentIndex + 1].barTitle,
    });
  }, [groups, itemKey, t]);

  return (
    <Box flex={1}>
      <FormProvider {...form}>
        <HealthQuestionEnquiryContext.Provider
          value={{
            branch: data?.branch || '',
            tag: data?.tag || '',
          }}>
          <KeyboardAwareFlatList
            data={filteredSection}
            renderItem={renderSection}
            keyExtractor={item => item.name}
            contentContainerStyle={{
              paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            }}
            enableResetScrollToCoords={false}
            ListEmptyComponent={
              <>
                {!(
                  isLoadingHealthQuestionDetail ||
                  isGettingOwb ||
                  isLoadingHealthQuestions ||
                  isAnswering ||
                  isErrorGenerationHealthQuestion ||
                  isErrorHealthQuestionDetail
                ) && (
                  <Box
                    alignItems="center"
                    gap={space[4]}
                    py={17.25}
                    flex={1}
                    justifyContent="center">
                    <IconEmpty />
                    <LargeBody color={colors.placeholder}>
                      {t('eApp:noHealthQuestion', {
                        name: party?.person?.name?.fullName?.trim() || role,
                      })}
                    </LargeBody>
                  </Box>
                )}
              </>
            }
            ListHeaderComponent={() => (
              <Box marginY={space[4]}>
                <Typography.H6 fontWeight="bold">
                  Health questions - {role}
                </Typography.H6>
              </Box>
            )}
            ListFooterComponent={() => <Box h={space[4]} />}
            refreshControl={
              <RefreshControl
                refreshing={false}
                onRefresh={refetchHealthQuestion}
              />
            }
          />
        </HealthQuestionEnquiryContext.Provider>
      </FormProvider>
      <EAppFooterPhone
        progressLock={progressLock as EAppFooterProps['progressLock']}
        primaryDisabled={
          isLoadingHealthQuestions ||
          isLoadingHealthQuestionDetail ||
          isGettingOwb ||
          isErrorGenerationHealthQuestion ||
          isErrorHealthQuestionDetail ||
          isAnswering ||
          !isAbleToPassHQStep ||
          !data ||
          !areAllQuestionsSatisfied
        }
        primaryLoading={isGettingOwb || isClosingHealthQuestion}
        primarySubLabel={nextSubLabel}
        onPrimaryPress={onSubmit}
      />
      {(isLoadingHealthQuestionDetail ||
        isAnswering ||
        isLoadingHealthQuestions) && (
        <LoadingBox>
          <LoadingIndicator size={sizes[12]} color={colors.primary} />
        </LoadingBox>
      )}
      {isErrorGenerationHealthQuestion && filteredSection.length === 0 && (
        <LoadingBox>
          <LargeLabel style={{ textAlign: 'center' }}>
            {t('eApp:healthQuestionFailed')}
            <LargeLabel
              suppressHighlighting
              onPress={startGeneratingHealthQuestion}
              color={colors.primary}>
              {t('eApp:healthQuestionRetry')}
            </LargeLabel>
          </LargeLabel>
        </LoadingBox>
      )}
      {isErrorHealthQuestionDetail && filteredSection.length === 0 && (
        <LoadingBox>
          <LargeLabel style={{ textAlign: 'center' }}>
            {t('eApp:healthQuestionFailed')}
            <LargeLabel
              suppressHighlighting
              onPress={() => refetchHealthQuestion()}
              color={colors.primary}>
              {t('eApp:healthQuestionRetry')}
            </LargeLabel>
          </LargeLabel>
        </LoadingBox>
      )}
    </Box>
  );
}

const LoadingBox = styled.View(() => {
  return {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 93,
    left: 0,
    alignItems: 'center',
    justifyContent: 'center',
  };
});
