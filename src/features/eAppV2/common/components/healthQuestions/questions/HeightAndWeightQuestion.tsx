import React, { useCallback, useEffect, useState } from 'react';
import { NumberHealthQuestion } from 'types/healthQuestion';
import {
  Box,
  Column,
  Icon,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import QuestionBox from './QuestionBox';
import useNumberQuestion from 'features/eAppV2/common/hooks/healthQuestions/useNumberQuestion';
import { ClipPath, Defs, G, Path, Rect, Svg } from 'react-native-svg';
import { TouchableOpacity } from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import QuestionTooltipModal from '../tooltip/QuestionTooltipModal';
import DecimalTextField from 'components/DecimalTextField';
import { useTranslation } from 'react-i18next';
import useQuestionError from 'features/eAppV2/common/hooks/healthQuestions/useQuestionError';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

type Props = {
  heightQuestion: NumberHealthQuestion;
  weightQuestion: NumberHealthQuestion;
};

const HeightAndWeightQuestion = ({ heightQuestion, weightQuestion }: Props) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [dialogVisible, setDialogVisible] = useState(false);
  const isPH = country === 'ph';

  const {
    text: heightText,
    onChange: onChangeHeight,
    onBlur: onBlurHeight,
  } = useNumberQuestion({
    question: heightQuestion,
  });
  const heightErrorMessage = useQuestionError({ question: heightQuestion });
  const [heightType, setHeightType] = useState(isPH ? 'ft&in' : 'cm');
  const [cm, setCm] = useState('');
  const [ft, setFt] = useState('');
  const [inch, setInch] = useState('');
  useEffect(() => {
    setCm(heightText ?? '');
    setInch(heightText ? String(cmToFtAndInch(Number(heightText)).inch) : '');
    setFt(heightText ? String(cmToFtAndInch(Number(heightText)).ft) : '');
  }, [heightText]);

  const onInchChange = useCallback(
    (inch: number | null) => {
      const numericInches = Number(inch);
      if (isNaN(numericInches)) return;
      const numericFt = Number(ft);
      const { cm, newFt, newInch } = ftAndInchToCm(numericFt, numericInches);
      setInch(String(newInch));
      setCm(String(cm));
      setFt(String(newFt));
    },
    [ft],
  );
  const onFtChange = useCallback(
    (ft: number | null) => {
      const numericInch = Number(inch);
      const numericFt = Number(ft);
      if (isNaN(numericFt)) return;
      const { cm } = ftAndInchToCm(numericFt, numericInch);
      setFt(String(numericFt));
      setCm(String(cm));
    },
    [inch],
  );
  const onCmChange = useCallback((cm: number | null) => {
    const numericCm = Number(cm);
    if (isNaN(numericCm)) return;
    const { ft, inch } = cmToFtAndInch(numericCm);
    setFt(String(ft));
    setInch(String(inch));
    setCm(String(numericCm));
  }, []);

  const {
    text: weightText,
    onChange: onChangeWeight,
    onBlur: onBlurWeight,
  } = useNumberQuestion({
    question: weightQuestion,
  });
  const weightErrorMessage = useQuestionError({ question: weightQuestion });
  const [weightType, setWeightType] = useState(isPH ? 'lbs' : 'kg');
  const [kg, setKg] = useState('');
  const [lbs, setLbs] = useState('');
  useEffect(() => {
    setKg(weightText ?? '');
    setLbs(weightText ? String(kgToLbs(Number(weightText)).lbs) : '');
  }, [weightText]);

  const onKgChange = useCallback((kg: number | null) => {
    const numericKg = Number(kg);
    if (isNaN(numericKg)) return;
    const { lbs } = kgToLbs(numericKg);
    setLbs(String(lbs));
    setKg(String(numericKg));
  }, []);
  const onLbsChange = useCallback((lbs: number | null) => {
    const numericLbs = Number(lbs);
    if (isNaN(numericLbs)) return;
    const { kg } = lbsToKg(numericLbs);
    setKg(String(kg));
    setLbs(String(numericLbs));
  }, []);

  const [helpText, setHelpText] = useState('');

  return (
    <Column>
      <QuestionBox>
        <Box flexDirection={isTabletMode ? 'row' : 'column'} gap={space[6]}>
          <Box flex={1} gap={space[1]}>
            <Row gap={space[1]} justifyContent="space-between">
              <LargeBody fontWeight={!isTabletMode ? 'bold' : undefined}>
                {isTabletMode
                  ? t('eApp:healthQuestion.index.a', {
                      text: heightQuestion.definition.text,
                    })
                  : heightQuestion.definition.text}
              </LargeBody>
              <TouchableOpacity
                hitSlop={ICON_HIT_SLOP}
                onPress={() => {
                  setHelpText(heightQuestion.definition.helpText);
                  setDialogVisible(true);
                }}>
                <Icon.InfoCircle
                  fill={isTabletMode ? colors.primary : colors.onBackground}
                />
              </TouchableOpacity>
            </Row>
            <Row gap={space[6]} alignSelf="stretch">
              <IconHeight />
              <Column mt={space[2]} flex={1}>
                <Row gap={space[2]}>
                  {heightType === 'cm' && (
                    <DecimalTextField
                      precision={1}
                      style={{ flex: 1 }}
                      value={cm ? Number(cm) : null}
                      onChange={onCmChange}
                      onBlur={() => {
                        onBlurHeight();
                        onChangeHeight(cm);
                      }}
                      placeholder={t('eApp:healthQuestion.height')}
                      right={
                        <LargeBody color={colors.palette.fwdDarkGreen[50]}>
                          {t('eApp:healthQuestion.cm')}
                        </LargeBody>
                      }
                      keyboardType={
                        heightQuestion.definition.type === 'INTEGER'
                          ? 'number-pad'
                          : 'decimal-pad'
                      }
                      error={heightErrorMessage}
                    />
                  )}
                  {isPH && heightType === 'ft&in' && (
                    <>
                      <DecimalTextField
                        precision={1}
                        style={{ flex: 1 }}
                        value={ft ? Number(ft) : null}
                        onChange={onFtChange}
                        onBlur={() => {
                          onBlurHeight();
                          onChangeHeight(cm);
                        }}
                        placeholder={t('eApp:healthQuestion.height')}
                        right={
                          <LargeBody color={colors.palette.fwdDarkGreen[50]}>
                            {t('eApp:healthQuestion.ft')}
                          </LargeBody>
                        }
                        keyboardType={
                          heightQuestion.definition.type === 'INTEGER'
                            ? 'number-pad'
                            : 'decimal-pad'
                        }
                        error={heightErrorMessage}
                      />
                      <DecimalTextField
                        precision={1}
                        style={{ flex: 1 }}
                        value={inch ? Number(inch) : null}
                        onChange={onInchChange}
                        onBlur={() => {
                          onBlurHeight();
                          onChangeHeight(cm);
                        }}
                        placeholder={t('eApp:healthQuestion.height')}
                        right={
                          <LargeBody color={colors.palette.fwdDarkGreen[50]}>
                            {t('eApp:healthQuestion.in')}
                          </LargeBody>
                        }
                        keyboardType={
                          heightQuestion.definition.type === 'INTEGER'
                            ? 'number-pad'
                            : 'decimal-pad'
                        }
                      />
                    </>
                  )}
                </Row>
                {isPH && (
                  <RadioButtonGroup value={heightType} onChange={setHeightType}>
                    <Row gap={space[8]} mt={space[3]}>
                      <RadioButton
                        value="ft&in"
                        label={t('eApp:healthQuestion.ft&in')}
                      />
                      <RadioButton
                        value="cm"
                        label={t('eApp:healthQuestion.cm')}
                      />
                    </Row>
                  </RadioButtonGroup>
                )}
              </Column>
            </Row>
          </Box>
          <Box flex={1} gap={space[1]}>
            <Row gap={space[1]} justifyContent="space-between">
              <LargeBody fontWeight={!isTabletMode ? 'bold' : undefined}>
                {isTabletMode
                  ? t('eApp:healthQuestion.index.b', {
                      text: weightQuestion.definition.text,
                    })
                  : weightQuestion.definition.text}
              </LargeBody>
              <TouchableOpacity
                hitSlop={ICON_HIT_SLOP}
                onPress={() => {
                  setHelpText(weightQuestion.definition.helpText);
                  setDialogVisible(true);
                }}>
                <Icon.InfoCircle
                  fill={isTabletMode ? colors.primary : colors.onBackground}
                />
              </TouchableOpacity>
            </Row>
            <Row gap={space[6]} alignSelf="stretch">
              <IconWeight />
              <Column mt={space[2]} flex={1}>
                <DecimalTextField
                  precision={1}
                  style={{ flex: 1 }}
                  value={
                    weightType === 'kg'
                      ? kg
                        ? Number(kg)
                        : null
                      : lbs
                      ? Number(lbs)
                      : null
                  }
                  onChange={weightType === 'kg' ? onKgChange : onLbsChange}
                  onBlur={() => {
                    onBlurWeight();
                    onChangeWeight(kg);
                  }}
                  placeholder={t('eApp:healthQuestion.weight')}
                  right={
                    <LargeBody color={colors.palette.fwdDarkGreen[50]}>
                      {t(`eApp:healthQuestion.${weightType as 'kg' | 'lbs'}`)}
                    </LargeBody>
                  }
                  keyboardType={
                    weightQuestion.definition.type === 'INTEGER'
                      ? 'number-pad'
                      : 'decimal-pad'
                  }
                  error={weightErrorMessage}
                />
                {isPH && (
                  <RadioButtonGroup value={weightType} onChange={setWeightType}>
                    <Row gap={space[8]} mt={space[3]}>
                      <RadioButton
                        value="lbs"
                        label={t('eApp:healthQuestion.lbs')}
                      />
                      <RadioButton
                        value="kg"
                        label={t('eApp:healthQuestion.kg')}
                      />
                    </Row>
                  </RadioButtonGroup>
                )}
              </Column>
            </Row>
          </Box>
        </Box>
      </QuestionBox>
      <QuestionTooltipModal
        visible={dialogVisible}
        onClose={() => setDialogVisible(false)}
        title="Notice"
        content={helpText}
      />
    </Column>
  );
};

export default HeightAndWeightQuestion;

const IconHeight = () => {
  return (
    <Svg width="64" height="65" viewBox="0 0 64 65" fill="none">
      <Path
        d="M24.0704 23.5795C26.1028 23.5795 27.7504 21.9728 27.7504 19.9909C27.7504 18.009 26.1028 16.4023 24.0704 16.4023C22.038 16.4023 20.3904 18.009 20.3904 19.9909C20.3904 21.9728 22.038 23.5795 24.0704 23.5795Z"
        fill="#E87722"
      />
      <Path
        d="M34.2922 31.9681C33.835 31.2412 33.3779 30.5143 32.9207 29.7921C32.3402 28.8778 31.7916 27.9406 31.1973 27.0401C30.5116 26.0023 30.0544 24.8595 29.003 24.1555C27.3116 23.0218 25.2362 23.2915 23.3299 23.2778C22.379 23.2732 21.3824 23.2686 20.4727 23.5978C19.4122 23.9863 18.7767 24.7726 18.1962 25.7463C17.771 26.4549 17.3002 27.1361 16.8704 27.8446C16.0064 29.2572 15.0967 30.6378 14.2282 32.0412C14.0362 32.3521 13.8442 32.6583 13.6476 32.9646C13.0533 33.9109 13.1493 35.0903 13.8624 35.5932C14.5802 36.0961 15.6407 35.7395 16.2396 34.7886L19.9196 28.9372V50.9395C19.9196 52.0001 20.7333 52.8641 21.7344 52.8641C22.7356 52.8641 23.5493 52.0046 23.5493 50.9395V38.0298H25.2773V50.8983C25.2773 51.9863 26.1093 52.8686 27.1379 52.8686C28.1664 52.8686 28.9984 51.9863 28.9984 50.8983C28.9984 49.6732 28.9984 47.0766 28.9984 45.8515C28.9984 43.7258 29.0122 41.6046 29.003 39.4789C28.9984 38.5052 28.9984 29.4903 28.9984 29.4903L32.5139 35.0812C33.1082 36.0275 34.1733 36.3886 34.891 35.8858C35.3756 35.5429 35.5722 34.8892 35.4624 34.2081C35.4487 34.0161 34.4933 32.2195 34.2922 31.9681Z"
        fill="#E87722"
      />
      <G clip-path="url(#clip0_24054_87381)">
        <Path
          d="M41.401 13.2031H49.1009C50.2569 13.2031 51.1934 14.1333 51.1934 15.2794V50.8974C51.1934 52.0444 50.256 52.9736 49.1009 52.9736H41.401C40.2451 52.9736 39.3086 52.0435 39.3086 50.8974V15.2794C39.3086 14.1333 40.246 13.2031 41.401 13.2031Z"
          fill="#183028"
        />
        <Path
          d="M46.2878 18.172C46.2878 17.8111 45.992 17.5176 45.6283 17.5176H39.3086V18.8263H45.6283C45.992 18.8263 46.2868 18.5356 46.2878 18.1748C46.2878 18.1738 46.2878 18.1729 46.2878 18.172Z"
          fill="white"
        />
        <Path
          d="M43.8044 23.1446C43.8044 22.7838 43.5114 22.4912 43.1477 22.4902C43.1468 22.4902 43.1458 22.4902 43.1449 22.4902H39.3086V23.799H43.1449C43.5085 23.799 43.8034 23.5083 43.8044 23.1474C43.8044 23.1465 43.8044 23.1456 43.8044 23.1446Z"
          fill="white"
        />
        <Path
          d="M46.2878 28.1153C46.2878 27.7545 45.992 27.4609 45.6283 27.4609H39.3086V28.7697H45.6283C45.992 28.7697 46.2868 28.479 46.2878 28.1181C46.2878 28.1172 46.2878 28.1163 46.2878 28.1153Z"
          fill="white"
        />
        <Path
          d="M43.8044 33.088C43.8044 32.7271 43.5114 32.4345 43.1477 32.4336C43.1468 32.4336 43.1458 32.4336 43.1449 32.4336H39.3086V33.7424H43.1449C43.5085 33.7424 43.8034 33.4516 43.8044 33.0908C43.8044 33.0898 43.8044 33.0889 43.8044 33.088Z"
          fill="white"
        />
        <Path
          d="M46.2878 38.0606C46.2878 37.6998 45.9948 37.4072 45.6311 37.4062C45.6302 37.4062 45.6292 37.4062 45.6283 37.4062H39.3086V38.715H45.6283C45.992 38.715 46.2878 38.4215 46.2878 38.0606Z"
          fill="white"
        />
        <Path
          d="M43.8044 43.0313C43.8044 42.6705 43.5114 42.3779 43.1477 42.377C43.1468 42.377 43.1458 42.377 43.1449 42.377H39.3086V43.6857H43.1449C43.5085 43.6857 43.8034 43.395 43.8044 43.0341C43.8044 43.0332 43.8044 43.0323 43.8044 43.0313Z"
          fill="white"
        />
        <Path
          d="M46.2878 48.004C46.2878 47.6431 45.9948 47.3505 45.6311 47.3496C45.6302 47.3496 45.6292 47.3496 45.6283 47.3496H39.3086V48.6584H45.6283C45.992 48.6584 46.2878 48.3648 46.2878 48.004Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_24054_87381">
          <Rect
            width="11.8857"
            height="39.7714"
            fill="white"
            transform="translate(39.3086 13.2031)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

const IconWeight = () => {
  return (
    <Svg width="64" height="65" viewBox="0 0 64 65" fill="none">
      <Path
        d="M38.2491 17.8152V22.3435C38.2491 22.6691 37.989 22.9341 37.6677 22.9341H33.0383L34.7228 20.0578L35.0041 19.5782C35.1707 19.294 35.0795 18.9269 34.7998 18.7577C34.5193 18.5885 34.1579 18.6819 33.9913 18.966L33.7108 19.4441L31.6664 22.9341H26.3353C26.0148 22.9341 25.7539 22.6691 25.7539 22.3435V17.8152C25.7539 17.4896 26.0148 17.2246 26.3353 17.2246H37.6677C37.9883 17.2246 38.2491 17.4896 38.2491 17.8152Z"
        fill="#E87722"
      />
      <Path
        d="M47.4584 13.2031H16.5436C13.972 13.2031 11.8867 15.3212 11.8867 17.9334V47.33C11.8867 49.9422 13.972 52.0603 16.5436 52.0603H47.4584C50.03 52.0603 52.1153 49.9422 52.1153 47.33V17.9334C52.1153 15.3212 50.03 13.2031 47.4584 13.2031ZM24.5744 17.8153C24.5744 16.8296 25.3641 16.0275 26.3344 16.0275H37.6668C38.6379 16.0275 39.4268 16.8296 39.4268 17.8153V22.3436C39.4276 23.3292 38.6379 24.1313 37.6676 24.1313H26.3344C25.3641 24.1313 24.5744 23.3292 24.5744 22.3436V17.8153Z"
        fill="#E87722"
      />
      <Path
        d="M11.8867 35.0645V47.3302C11.8867 49.9423 13.972 52.0605 16.5436 52.0605H47.4584C50.03 52.0605 52.1153 49.9423 52.1153 47.3302V35.0645H11.8867Z"
        fill="#F3BB90"
      />
      <Path
        d="M38.2491 17.8152V22.3435C38.2491 22.6691 37.989 22.9341 37.6677 22.9341H33.0383L34.7228 20.0578L35.0041 19.5782C35.1707 19.294 35.0795 18.9269 34.7998 18.7577C34.5193 18.5885 34.1579 18.6819 33.9913 18.966L33.7108 19.4441L31.6664 22.9341H26.3353C26.0148 22.9341 25.7539 22.6691 25.7539 22.3435V17.8152C25.7539 17.4896 26.0148 17.2246 26.3353 17.2246H37.6677C37.9883 17.2246 38.2491 17.4896 38.2491 17.8152Z"
        fill="white"
      />
      <Path
        d="M33.7085 19.4438L31.6641 22.9338H27.668C27.9548 20.7646 29.7847 19.0918 31.9996 19.0918C32.6054 19.0918 33.1837 19.2171 33.7085 19.4438Z"
        fill="#E87722"
      />
      <Path
        d="M36.3344 22.9349H33.0391L34.7236 20.0586C35.5848 20.7537 36.1811 21.7729 36.3344 22.9349Z"
        fill="#E87722"
      />
      <Path
        d="M37.6666 16.0273H26.3342C25.3639 16.0273 24.5742 16.8294 24.5742 17.8151V22.3434C24.5742 23.329 25.3639 24.1311 26.3342 24.1311H37.6674C38.6377 24.1311 39.4274 23.329 39.4266 22.3434V17.8151C39.4266 16.8294 38.6377 16.0273 37.6666 16.0273ZM38.248 22.3434C38.248 22.669 37.9879 22.934 37.6666 22.934H26.3342C26.0136 22.934 25.7528 22.669 25.7528 22.3434V17.8151C25.7528 17.4894 26.0136 17.2245 26.3342 17.2245H37.6666C37.9871 17.2245 38.248 17.4894 38.248 17.8151V22.3434Z"
        fill="#F3BB90"
      />
      <Path
        d="M35.0018 19.5786L34.7205 20.0583L33.0359 22.9345H31.6641L33.7085 19.4445L33.989 18.9665C34.1556 18.6824 34.517 18.589 34.7975 18.7582C35.0772 18.9274 35.1683 19.2945 35.0018 19.5786Z"
        fill="black"
      />
      <Path
        d="M22.1631 47.1213C19.4736 44.3144 16.7158 39.4062 17.6814 32.0574C18.013 29.5355 19.8201 27.2051 22.4586 27.2051C25.097 27.2051 26.8923 29.1708 27.2357 32.0574C27.5076 34.3439 26.9332 35.8771 26.4688 37.8587C25.8277 40.5953 27.4746 42.4389 27.4746 44.8866C27.4746 46.6321 26.0815 48.0471 24.3631 48.0471C23.5036 48.0471 22.7257 47.6935 22.1631 47.1213Z"
        fill="#183028"
      />
      <Path
        opacity="0.1"
        d="M24.3636 46.8501C23.8498 46.8501 23.3665 46.6474 23.002 46.2802C21.5217 44.7335 20.3965 42.9163 19.658 40.8788C18.7104 38.2643 18.4385 35.3497 18.8503 32.2156C19.0993 30.3209 20.4185 28.4023 22.459 28.4023C24.4995 28.4023 25.7834 29.8221 26.0663 32.2012C26.2635 33.858 25.9618 35.0424 25.5807 36.5404C25.4958 36.874 25.4078 37.2188 25.323 37.5811C24.8735 39.4997 25.3544 41.0368 25.7779 42.3936C26.0443 43.2459 26.2965 44.0512 26.2965 44.886C26.2965 45.9682 25.429 46.8493 24.3636 46.8493V46.8501Z"
        fill="black"
      />
      <Path
        d="M41.8388 47.1213C44.5283 44.3144 47.2861 39.4062 46.3205 32.0574C45.9889 29.5355 44.1818 27.2051 41.5433 27.2051C38.9049 27.2051 37.1096 29.1708 36.7662 32.0574C36.4943 34.3439 37.0687 35.8771 37.5331 37.8587C38.1742 40.5953 36.5273 42.4389 36.5273 44.8866C36.5273 46.6321 37.9204 48.0471 39.6388 48.0471C40.4976 48.0471 41.2754 47.6935 41.8388 47.1213Z"
        fill="#183028"
      />
      <Path
        opacity="0.1"
        d="M39.6399 46.8493C38.5745 46.8493 37.707 45.9682 37.707 44.886C37.707 44.0512 37.9585 43.2459 38.2256 42.3936C38.6499 41.0368 39.1307 39.4997 38.6805 37.5811C38.5957 37.2188 38.5077 36.874 38.4228 36.5404C38.0417 35.0416 37.7408 33.8581 37.9372 32.2012C38.2201 29.8229 39.5684 28.4023 41.5445 28.4023C43.5205 28.4023 44.9042 30.3209 45.1532 32.2156C45.565 35.3489 45.2931 38.2635 44.3455 40.8788C43.607 42.9155 42.4818 44.7327 41.0015 46.2802C40.637 46.6474 40.1537 46.8501 39.6399 46.8501V46.8493Z"
        fill="black"
      />
      <Path
        d="M20.8702 19.2296C21.5384 19.2296 22.0802 18.6793 22.0802 18.0005C22.0802 17.3217 21.5384 16.7715 20.8702 16.7715C20.2019 16.7715 19.6602 17.3217 19.6602 18.0005C19.6602 18.6793 20.2019 19.2296 20.8702 19.2296Z"
        fill="#F3BB90"
      />
      <Path
        d="M16.3545 19.2296C17.0228 19.2296 17.5645 18.6793 17.5645 18.0005C17.5645 17.3217 17.0228 16.7715 16.3545 16.7715C15.6863 16.7715 15.1445 17.3217 15.1445 18.0005C15.1445 18.6793 15.6863 19.2296 16.3545 19.2296Z"
        fill="#F3BB90"
      />
      <Path
        d="M20.8702 23.4308C21.5384 23.4308 22.0802 22.8805 22.0802 22.2017C22.0802 21.5229 21.5384 20.9727 20.8702 20.9727C20.2019 20.9727 19.6602 21.5229 19.6602 22.2017C19.6602 22.8805 20.2019 23.4308 20.8702 23.4308Z"
        fill="#F3BB90"
      />
      <Path
        d="M16.3545 23.4308C17.0228 23.4308 17.5645 22.8805 17.5645 22.2017C17.5645 21.5229 17.0228 20.9727 16.3545 20.9727C15.6863 20.9727 15.1445 21.5229 15.1445 22.2017C15.1445 22.8805 15.6863 23.4308 16.3545 23.4308Z"
        fill="#F3BB90"
      />
      <Path
        d="M43.1358 19.2296C43.804 19.2296 44.3458 18.6793 44.3458 18.0005C44.3458 17.3217 43.804 16.7715 43.1358 16.7715C42.4675 16.7715 41.9258 17.3217 41.9258 18.0005C41.9258 18.6793 42.4675 19.2296 43.1358 19.2296Z"
        fill="#F3BB90"
      />
      <Path
        d="M47.6475 19.2296C48.3158 19.2296 48.8575 18.6793 48.8575 18.0005C48.8575 17.3217 48.3158 16.7715 47.6475 16.7715C46.9792 16.7715 46.4375 17.3217 46.4375 18.0005C46.4375 18.6793 46.9792 19.2296 47.6475 19.2296Z"
        fill="#F3BB90"
      />
      <Path
        d="M43.1358 23.4308C43.804 23.4308 44.3458 22.8805 44.3458 22.2017C44.3458 21.5229 43.804 20.9727 43.1358 20.9727C42.4675 20.9727 41.9258 21.5229 41.9258 22.2017C41.9258 22.8805 42.4675 23.4308 43.1358 23.4308Z"
        fill="#F3BB90"
      />
      <Path
        d="M47.6475 23.4308C48.3158 23.4308 48.8575 22.8805 48.8575 22.2017C48.8575 21.5229 48.3158 20.9727 47.6475 20.9727C46.9792 20.9727 46.4375 21.5229 46.4375 22.2017C46.4375 22.8805 46.9792 23.4308 47.6475 23.4308Z"
        fill="#F3BB90"
      />
    </Svg>
  );
};

export const cmToFtAndInch = (cm: number) => {
  const ft = Math.trunc(cm / 30.48);
  const inch = Math.round(((cm / 30.48) % 1) * 12);
  return { ft, inch };
};

const ftAndInchToCm = (ft: number, inch: number) => {
  const cm = Math.round((ft * 12 + inch) * 2.54 * 100) / 100;
  let numericFt = ft;
  let numericInch = inch;
  if (inch >= 12) {
    numericFt = numericFt + Math.round(inch / 12);
    numericInch = numericInch % 12;
  }
  return { cm, newFt: numericFt, newInch: numericInch };
};

export const kgToLbs = (kg: number) => {
  const lbs = Math.round(kg * 2.2 * 10) / 10;
  return { lbs };
};

const lbsToKg = (lbs: number) => {
  const kg = Math.round((lbs / 2.2) * 10) / 10;
  return { kg };
};
