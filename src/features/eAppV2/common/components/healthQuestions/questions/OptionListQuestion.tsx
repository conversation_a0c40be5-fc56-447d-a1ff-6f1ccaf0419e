import React, { memo, useMemo } from 'react';
import { OptionListHealthQuestion } from 'types/healthQuestion';
import { Box } from 'cube-ui-components';
import QuestionBox from './QuestionBox';
import { useTheme } from '@emotion/react';
import useOptionQuestion from 'features/eAppV2/common/hooks/healthQuestions/useOptionQuestion';
import useQuestionError from 'features/eAppV2/common/hooks/healthQuestions/useQuestionError';
import SearchableDropdown from 'components/SearchableDropdown';
import QuestionTitle from '../QuestionTitle';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AutocompletePopup from 'components/AutocompletePopup';

type Props = {
  question: OptionListHealthQuestion;
};

const OptionListQuestion = ({ question }: Props) => {
  const { space } = useTheme();
  const { text } = question.definition;
  const {
    answers,
    optionsList,
    isOptionSelected,
    onSelect,
    onDeselect,
    onSelectMultiOptions,
  } = useOptionQuestion({
    question,
  });

  const { isTabletMode } = useLayoutAdoptionCheck();

  const errorMessage = useQuestionError({ question });
  const options = useMemo(
    () => optionsList?.options.map(option => option.text),
    [optionsList],
  );
  return (
    <QuestionBox>
      <QuestionTitle title={text} helpText={question.definition?.helpText} />
      <Box h={space[3]} />
      {isTabletMode ? (
        <AutocompletePopup<string, string>
          modalTitle={text}
          error={errorMessage}
          data={options || []}
          getItemLabel={option => option}
          getItemValue={option => option}
          searchable
          getExternalDisplayedLabel={() => answers?.join(', ') || ''}
          {...(question.definition.isMultiValued
            ? {
                type: 'multiple',
                value: answers,
                onChange: (options: string[]) => {
                  onSelectMultiOptions(options);
                },
              }
            : {
                type: 'single',
                value: answers[0],
                onChange: (option: string) => {
                  isOptionSelected(option as string)
                    ? onDeselect(option as string)
                    : onSelect(option as string);
                },
              })}
        />
      ) : (
        <SearchableDropdown<string, string>
          error={errorMessage}
          data={options || []}
          getItemLabel={option => option}
          getItemValue={option => option}
          searchable
          getExternalDisplayedLabel={() => answers?.join(', ') || ''}
          {...(question.definition.isMultiValued
            ? {
                type: 'multiple',
                value: answers,
                onChange: (options: string[]) => {
                  onSelectMultiOptions(options);
                },
              }
            : {
                type: 'single',
                value: answers[0],
                onChange: (option: string) => {
                  isOptionSelected(option as string)
                    ? onDeselect(option as string)
                    : onSelect(option as string);
                },
              })}
        />
      )}
    </QuestionBox>
  );
};

export default memo(OptionListQuestion);
