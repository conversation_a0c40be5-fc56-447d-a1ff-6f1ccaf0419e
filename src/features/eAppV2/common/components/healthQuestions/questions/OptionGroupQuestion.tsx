import React, { memo, useContext, useMemo, useState } from 'react';
import {
  OptionGroupHealthQuestion,
  QuestionOption,
} from 'types/healthQuestion';
import {
  Box,
  Typography,
  Checkbox,
  RadioButton,
  RadioButtonGroup,
  Row,
  Icon,
} from 'cube-ui-components';
import QuestionBox from './QuestionBox';
import { useTheme } from '@emotion/react';
import { FlatList } from 'react-native-gesture-handler';
import useOptionQuestion from 'features/eAppV2/common/hooks/healthQuestions/useOptionQuestion';
import useQuestionError from 'features/eAppV2/common/hooks/healthQuestions/useQuestionError';
import SearchableDropdown from 'components/SearchableDropdown';
import { TouchableOpacity } from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { useGetQuestionToolTipContent } from 'hooks/useHealthQuestion';
import QuestionTooltipModal from '../tooltip/QuestionTooltipModal';
import QuestionTitle from '../QuestionTitle';
import HealthQuestionEnquiryContext from '../context/HealthQuestionEnquiryContext';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AutocompletePopup from 'components/AutocompletePopup';

type Props = {
  question: OptionGroupHealthQuestion;
};

const Wrapper = ({
  children,
  option,
  onClick,
}: {
  children: React.ReactNode;
  option: QuestionOption;
  onClick: () => void;
}) => {
  const { space, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Row justifyContent="space-between" alignItems="center">
      <Box flex={1}>{children}</Box>
      {option.tags.includes('INLINE_HELPTEXT') && (
        <>
          <Box h={space[2]} w={space[8]} />
          <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={onClick}>
            <Icon.InfoCircle
              fill={isTabletMode ? colors.primary : colors.palette.black}
            />
          </TouchableOpacity>
        </>
      )}
    </Row>
  );
};
const OptionGroupQuestion = ({ question }: Props) => {
  const { space, colors } = useTheme();
  const {
    text: questionTitle,
    options,
    isMultiValued,
    helpText,
  } = question.definition;
  const { isTabletMode } = useLayoutAdoptionCheck();
  const shouldUsePicker = useMemo(() => {
    const optionsLength = options?.length || 0;
    if (optionsLength <= 10) return false;
    return true;
  }, [options?.length]);
  const { answers, optionsList, isOptionSelected, onSelect, onDeselect } =
    useOptionQuestion({
      question,
    });
  const errorMessage = useQuestionError({ question });
  const [dialogVisible, setDialogVisible] = useState(false);
  const [optionTag, setOptionTag] = useState('');
  const { branch, tag } = useContext(HealthQuestionEnquiryContext);
  const { isLoading, data } = useGetQuestionToolTipContent(
    {
      branch,
      tag,
      optionListName: question.definition.optionListName,
      optionTag,
    },
    {
      enabled: !!optionTag,
    },
  );

  const onWrapperClicked = (option: QuestionOption) => {
    setOptionTag(option.text);
    setDialogVisible(true);
  };

  return (
    <QuestionBox>
      <QuestionTitle title={questionTitle} helpText={helpText} />
      <Box h={space[3]} />
      {shouldUsePicker && !isMultiValued ? (
        <>
          {isTabletMode ? (
            <AutocompletePopup<string, string>
              modalTitle={questionTitle}
              value={answers[0]}
              error={errorMessage}
              data={optionsList?.options.map(option => option.text) || []}
              getItemLabel={option => option}
              getItemValue={option => option}
              getExternalDisplayedLabel={() => answers[0] || ''}
              onChange={(option: string) => {
                isOptionSelected(option)
                  ? onDeselect(option)
                  : onSelect(option);
              }}
              searchable
            />
          ) : (
            <SearchableDropdown<string, string>
              value={answers[0]}
              error={errorMessage}
              data={optionsList?.options.map(option => option.text) || []}
              getItemLabel={option => option}
              getItemValue={option => option}
              getExternalDisplayedLabel={() => answers[0] || ''}
              onChange={(option: string) => {
                isOptionSelected(option)
                  ? onDeselect(option)
                  : onSelect(option);
              }}
              searchable
            />
          )}
        </>
      ) : isMultiValued ? (
        <>
          <FlatList
            scrollEnabled={false}
            data={optionsList?.options || []}
            renderItem={({ item: option }) => (
              <Wrapper
                onClick={() => {
                  onWrapperClicked(option);
                }}
                option={option}>
                <Checkbox
                  label={option?.text}
                  checked={isOptionSelected(option.text)}
                  onChange={checked =>
                    checked ? onSelect(option.text) : onDeselect(option.text)
                  }
                  labelStyle={{ flex: 1 }}
                />
              </Wrapper>
            )}
            keyExtractor={item => item.text}
            ItemSeparatorComponent={() => <Box h={space[4]} />}
          />
          {Boolean(errorMessage) && (
            <Typography.Body color={colors.error}>
              {errorMessage}
            </Typography.Body>
          )}
        </>
      ) : (
        <>
          <RadioButtonGroup value={answers[0]}>
            <FlatList
              scrollEnabled={false}
              data={optionsList?.options || []}
              horizontal={isTabletMode && optionsList?.options.length === 2}
              renderItem={({ item: option }) => {
                return (
                  <Wrapper
                    onClick={() => {
                      onWrapperClicked(option);
                    }}
                    option={option}>
                    <RadioButton
                      key={option.text}
                      value={option.text}
                      label={option.text}
                      onSelect={() => onSelect(option.text)}
                      labelStyle={{
                        flex: 1,
                      }}
                    />
                  </Wrapper>
                );
              }}
              keyExtractor={item => item.text}
              ItemSeparatorComponent={() => {
                if (isTabletMode && optionsList?.options.length === 2) {
                  return <Box w={space[8]} />;
                }
                return <Box h={space[4]} />;
              }}
            />
          </RadioButtonGroup>
          {Boolean(errorMessage) && (
            <Typography.Body color={colors.error}>
              {errorMessage}
            </Typography.Body>
          )}
        </>
      )}
      <QuestionTooltipModal
        visible={dialogVisible}
        title={optionTag}
        content={data?.aliases ?? []}
        isLoading={isLoading}
        onClose={() => {
          setDialogVisible(false);
          setOptionTag('');
        }}
      />
    </QuestionBox>
  );
};

export default memo(OptionGroupQuestion);
