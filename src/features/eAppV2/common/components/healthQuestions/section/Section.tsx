import SectionPhone from './Section.phone';
import SectionTablet from './Section.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { Section as SectionType } from 'types/healthQuestion';

type Props = {
  section: SectionType;
};

export default function Section({ section }: Props) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <SectionTablet section={section} />
  ) : (
    <SectionPhone section={section} />
  );
}
