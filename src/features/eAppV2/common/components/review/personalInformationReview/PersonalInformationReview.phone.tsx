import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Box, H6, Label, Row } from 'cube-ui-components';
import React from 'react';
import { RootStackParamList } from 'types';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';

export default function PersonalInformationReview() {
  const { colors, space, sizes } = useTheme();
  const { title, sections } =
    useRoute<RouteProp<RootStackParamList, 'PersonalInformationReview'>>()
      .params;
  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={title}
        route={'PersonalInformationReview'}
        isLeftArrowBackShown
      />
      <ScrollContainer>
        <Box gap={space[6]} mb={space[16]}>
          {sections.map((section, idx) => (
            <Box key={idx}>
              <Box>
                {!!section.title && <H6 fontWeight="bold">{section.title}</H6>}
              </Box>
              <Box>
                {section.data.map((field, idx) => {
                  if ('label' in field) {
                    return (
                      <SummaryText
                        key={field.label}
                        label={field.label}
                        value={field.value}
                        perRow={section.perRow}
                      />
                    );
                  }
                  if ('question' in field) {
                    return (
                      <Row key={idx} mt={space[6]}>
                        <Label
                          color={colors.placeholder}
                          style={{ width: sizes[5] }}>{`${idx + 1}.`}</Label>
                        <Box flex={1}>
                          <Label color={colors.palette.fwdGreyDarkest}>
                            {field.question}
                          </Label>
                          <Box flex={1}>
                            {field.answers.map((answer, index) => (
                              <SummaryText
                                key={index}
                                label={answer[0]}
                                value={answer[1]}
                              />
                            ))}
                          </Box>
                        </Box>
                      </Row>
                    );
                  }
                  return (
                    <Box key={`title-${idx}`} mt={space[6]}>
                      <Label fontWeight="bold">{field.title}</Label>
                      {field.data.map(item => (
                        <SummaryText
                          key={item.label}
                          label={item.label}
                          value={item.value}
                        />
                      ))}
                    </Box>
                  );
                })}
              </Box>
            </Box>
          ))}
        </Box>
      </ScrollContainer>
    </Box>
  );
}

const ScrollContainer = styled.ScrollView(({ theme }) => ({
  paddingHorizontal: theme.space[4],
  paddingTop: theme.space[4],
}));

const SummaryText = ({ label, value, perRow }: { label: string; value: string; perRow?: boolean; }) => {
  const { colors, space } = useTheme();

  const Container = perRow ? Box : Row;
  return (
    <Container mt={space[2]} mb={space[perRow ? 2 : 0]}>
      <Box flex={1}>
        <Label color={colors.palette.fwdGreyDarkest}>{label}</Label>
      </Box>
      <Box flex={1} mt={space[perRow ? 1 : 0]}>
        <Label>{value}</Label>
      </Box>
    </Container>
  );
};
