import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { memo } from 'react';
import { LabelViewProps } from './LabelView';

const Container = styled.View<{ topSpacing?: number; ph?: number }>(
  ({ theme, topSpacing, ph }) => ({
    marginTop: topSpacing ? topSpacing + theme.space[1] : theme.space[3],
    paddingRight: theme.space[4],
    paddingHorizontal: ph,
    flexBasis: `${100 / 3}%`,
  }),
);

const Label = styled(Typography.SmallLabel)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
  flex: 1,
}));

const Text = styled(Typography.H7)(({ theme }) => ({
  color: theme.colors.onBackground,
  flex: 1,
  marginTop: theme.space[1],
}));

export const LabelViewTablet = memo(function LabelViewTablet({
  topSpacing,
  label,
  text,
  ph,
}: LabelViewProps) {
  return (
    <Container topSpacing={topSpacing} ph={ph}>
      <Label>{label}</Label>
      <Text>{text}</Text>
    </Container>
  );
});
export default LabelViewTablet;
