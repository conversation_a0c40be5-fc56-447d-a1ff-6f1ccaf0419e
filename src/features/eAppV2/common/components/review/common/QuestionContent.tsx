import styled from '@emotion/native';
import { Box, Typography } from 'cube-ui-components';
import { memo } from 'react';
import LabelView, { LabelViewProps } from './LabelView';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTheme } from '@emotion/react';

const Container = styled.View(({ theme }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  if (isTabletMode) {
    return {
      paddingHorizontal: theme.space[6],
      paddingBottom: theme.space[6],
      flexDirection: 'row',
    };
  }
  return {
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    paddingBottom: theme.space[4],
    flexDirection: 'row',
  };
});

const Divider = styled.View(({ theme: { space, colors } }) => ({
  borderTopWidth: 1,
  marginHorizontal: space[4],
  marginBottom: space[4],
  borderTopColor: colors.palette.fwdGrey[100],
}));

const ContentContainer = styled.View(() => ({
  flex: 1,
}));

const IndexContainer = styled.View(({ theme }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    width: theme.sizes[isTabletMode ? 5 : 4],
    marginRight: theme.space[1],
  };
});
const PhoneTitle = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

const TabletTitle = styled(Typography.LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

export interface QuestionContentProps
  extends Omit<LabelViewProps, 'label' | 'text'> {
  title?: string;
  indexVisible?: boolean;
  index: number;
  items: Array<{
    label: string;
    text: string;
  }>;
  dividerVisible?: boolean;
}

export const QuestionContent = memo(function QuestionContent({
  title,
  items,
  indexVisible,
  index,
  dividerVisible,
  ...rest
}: QuestionContentProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Title = isTabletMode ? TabletTitle : PhoneTitle;
  const { space } = useTheme();
  return (
    <>
      {dividerVisible && <Divider />}
      <Container>
        {indexVisible && (
          <IndexContainer>
            <Title>{index + 1}.</Title>
          </IndexContainer>
        )}
        <ContentContainer>
          {!!title && <Title>{title}</Title>}
          {items.map((item, index) => (
            <>
              {isTabletMode ? (
                <Box mt={space[2]}>
                  <Typography.LargeBody key={index}>
                    {item.text}
                  </Typography.LargeBody>
                </Box>
              ) : (
                <LabelView
                  key={index}
                  {...item}
                  {...rest}
                  topSpacing={index === 0 && !title ? 0 : rest.topSpacing}
                />
              )}
            </>
          ))}
        </ContentContainer>
      </Container>
    </>
  );
});
export default QuestionContent;
