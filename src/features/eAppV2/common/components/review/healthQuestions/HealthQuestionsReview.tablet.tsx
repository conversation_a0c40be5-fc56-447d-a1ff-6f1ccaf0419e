import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Box, LargeBody } from 'cube-ui-components';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, SectionListData, StyleSheet, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { RowInfoFieldProps } from '../rowInfoField/RowInfoField';
import _ from 'lodash';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import useHealthQuestionDetail from 'features/eAppV2/common/hooks/healthQuestions/useHealthQuestionDetail';
import { useFilterHealthQuestion } from 'features/eAppV2/common/hooks/healthQuestions/useFilterHealthQuestion';
import {
  InfoItemType,
  InfoQuestion,
  InfoSectionData,
} from 'features/eAppV2/common/types/reviewTypes';
import Question from '../questions/Question';
import SectionContent from 'features/eAppV2/common/components/review/common/SectionContent';
import ReviewSectionList from 'features/eAppV2/common/components/review/common/ReviewSectionList';
import SectionSubHeader from 'features/eAppV2/common/components/review/common/SectionSubHeader';
import { RootStackParamList } from 'types';
import useBoundStore from 'hooks/useBoundStore';
import { shallow } from 'zustand/shallow';
import IconEmpty from './IconEmpty';
import { useGetPartyFromActiveCase } from 'hooks/useParty';

interface QuestionProps {
  index: number;
  question: string;
  answer?: string;
  subQuestion?: string;
  subAnswer?: string;
  info?: RowInfoFieldProps[];
}

export default function HealthQuestionsReview() {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const { enquiryId, title, partyId } =
    useRoute<RouteProp<RootStackParamList, 'HealthQuestionsReview'>>().params;
  const party = useGetPartyFromActiveCase(partyId);

  const { initialData, isLoadingHealthQuestionDetail } =
    useHealthQuestionDetail(enquiryId);

  const { setAppLoading, setAppIdle } = useBoundStore(
    state => ({
      setAppLoading: state.appActions.setAppLoading,
      setAppIdle: state.appActions.setAppIdle,
    }),
    shallow,
  );

  useEffect(() => {
    if (isLoadingHealthQuestionDetail) {
      setAppLoading();
    } else {
      setAppIdle();
    }
  }, [isLoadingHealthQuestionDetail, setAppLoading, setAppIdle]);

  const sections = useFilterHealthQuestion(initialData?.sections || []);

  const reviewSections = useMemo(() => {
    const reviewSections: InfoSectionData[] = sections.map(section => {
      const questions: InfoQuestion[] = [];
      section.enquiryLines.forEach(line => {
        line.questions.forEach(question => {
          questions.push({
            title: question.definition.text.replace(/(<([^>]+)>)/gi, ''),
            indexVisible: true,
            healthQuestion: question,
            items: question.definition.isMultiValued
              ? [
                  {
                    label: 'Answer',
                    text: question.answers.join(', '),
                  },
                ]
              : question?.definition?.type === 'NUMBER' &&
                question?.definition?.name === 'HEIGHT'
              ? question.answers.map(answer => ({
                  label: 'Answer',
                  text: `${answer} cm`,
                }))
              : question?.definition?.type === 'NUMBER' &&
                question?.definition?.name.includes('WEIGHT')
              ? question.answers.map(answer => ({
                  label: 'Answer',
                  text: `${answer} kg`,
                }))
              : question.answers.map(answer => ({
                  label: 'Answer',
                  text: answer,
                })),
          });
        });
      });
      return {
        title: section.name,
        type: 'question',
        data: questions,
      };
    });
    return reviewSections;
  }, [sections]);

  const { sizes, colors } = useTheme();

  const renderItem = useCallback(
    ({
      item,
      section,
      index,
    }: {
      item: InfoItemType;
      section: { title: string; type: string };
      index: number;
    }) => {
      if (item) {
        if (section.type === 'question') {
          return <Question {...item} index={index} />;
        }
        return <SectionContent {...item} />;
      }
      return null;
    },
    [],
  );

  const renderSectionHeader = useCallback(
    ({
      section: { title },
    }: {
      section: SectionListData<InfoItemType, { title: string; type: string }>;
    }) => {
      if (!title) {
        return <View />;
      }
      return <SectionSubHeader title={title} />;
    },
    [],
  );

  return (
    <Box flex={1} backgroundColor={colors.background}>
      <ScreenHeader
        customTitle={title || t('eApp:summary.health')}
        route={'HealthQuestionsReview'}
        isLeftCrossBackShown
      />
      {!isLoadingHealthQuestionDetail && reviewSections.length === 0 ? (
        <Box
          alignItems="center"
          gap={space[4]}
          py={17.25}
          pl={space[8]}
          flex={1}
          justifyContent="center">
          <IconEmpty />
          <LargeBody color={colors.placeholder}>
            {t('eApp:noHealthQuestion', {
              name: party?.person.name.fullName?.trim() || title,
            })}
          </LargeBody>
        </Box>
      ) : (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <ReviewSectionList
            sections={reviewSections}
            renderSectionHeader={renderSectionHeader}
            renderItem={renderItem}
          />
        </ScrollView>
      )}
    </Box>
  );
}

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    paddingVertical: 24,
  },
});

export type { QuestionProps };
