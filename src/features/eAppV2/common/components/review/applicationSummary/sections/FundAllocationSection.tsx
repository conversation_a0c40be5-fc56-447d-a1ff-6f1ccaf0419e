import { Box, Column, H3, Row, Typography } from 'cube-ui-components';
import React, { useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { useTranslation } from 'react-i18next';
import { Quotation } from 'types/quotation';
import styled from '@emotion/native';
import { getPieChartItemColor } from 'features/eAppV2/common/utils/beneficiaryAllocationUtils';
import { View } from 'react-native';
import { MemoPieChart } from '../../../chart/PieChart';
import { country } from 'utils/context';

export default function FundAllocationSection({
  quotation,
}: {
  quotation?: Quotation;
}) {
  const { sizes, colors, borderRadius, space } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const funds = useMemo(() => {
    return (
      quotation?.funds?.fundAllocation
        .filter(fund => Number(fund.allocation) !== 0)
        .map(fund => ({
          fid: fund.fid,
          underlyingFundName: fund.underlyingFundName,
          allocation: fund.allocation,
          colorCode: fund.colorCode,
        })) ?? []
    );
  }, [quotation?.funds?.fundAllocation]);

  const progresses = useMemo(() => {
    return funds?.map(i => i.allocation);
  }, [funds]);

  const randomColors = useMemo(() => {
    const colors: string[] = [];
    progresses.map((_, index) => {
      colors.push(getPieChartItemColor(index));
    });
    return colors;
  }, [progresses]);

  const size = 274;

  return (
    <Row
      borderRadius={borderRadius.large}
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}
      mt={space[6]}
      p={space[10]}>
      <Column flex={1}>
        <Row mb={space[2]}>
          <Column flex={3}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('proposal:salesIllustrationDetail.fund')}
            </Typography.SmallLabel>
          </Column>

          <Column flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {`${t('proposal:salesIllustrationDetail.allocation')} (%)`}
            </Typography.SmallLabel>
          </Column>
        </Row>

        {funds?.map((fund, index) => (
          <Row
            key={renderLabelByLanguage(fund.underlyingFundName)}
            mb={space[3]}>
            <Row flex={3}>
              <Box
                width={sizes[4]}
                height={sizes[4]}
                bgColor={
                  country === 'ph'
                    ? fund.colorCode ?? randomColors[index]
                    : fund.colorCode
                }
                mr={space[2]}
                borderRadius={2}
              />
              <Typography.Label
                style={{ flex: 1 }}
                color={colors.palette.fwdDarkGreen[100]}>
                {renderLabelByLanguage(fund.underlyingFundName)}
              </Typography.Label>
            </Row>
            <Column flex={1}>
              <Typography.Label color={colors.palette.fwdDarkGreen[100]}>
                {fund.allocation}
              </Typography.Label>
            </Column>
          </Row>
        ))}
      </Column>
      {(quotation?.fundsPieChartImage || country !== 'ph') && (
        <FundImage source={{ uri: quotation?.fundsPieChartImage || '' }} />
      )}
      {country === 'ph' && !quotation?.fundsPieChartImage && (
        <View
          style={{
            width: size,
            height: size,
            marginLeft: space[6],
            marginRight: space[3],
          }}>
          <MemoPieChart
            size={size}
            progresses={progresses}
            fullPercentage={100}
            colors={randomColors}
            strokeWidth={54}>
            <TextContainer>
              <H3 fontWeight="bold">100%</H3>
            </TextContainer>
          </MemoPieChart>
        </View>
      )}
    </Row>
  );
}

const FundImage = styled.Image({
  width: 272,
  height: 272,
});

const TextContainer = styled.View(() => ({
  position: 'absolute',
  justifyContent: 'center',
  alignContent: 'center',
  alignItems: 'center',
}));
