import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { ApplicationProgress } from 'api/caseApi';
import { usePromptContext } from 'components/prompt/PromptContext';
import { getStyledAlertActions } from 'components/prompt/PromptDialog';
import { addErrorBottomToast, addToast, Icon } from 'cube-ui-components';
import {
  ProgressItem,
  ProgressSubgroup,
} from 'features/eAppV2/common/types/progressBarTypes';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  useGetApplicationProgress,
  useSaveApplicationProgress,
} from 'hooks/useApplicationProgress';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useToggle from 'hooks/useToggle';
import isEqual from 'lodash/isEqual';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BackHandler } from 'react-native';
import { RootStackParamList } from 'types';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import { useGenerateEAppRoutes } from '../../hooks/useGenerateEAppRoutes';
import { useSyncAppProgress } from '../../hooks/useSyncAppProgress';
import ProgressBarPhone from './phone/ProgressBar.phone';
import { useProgressBarContext } from './ProgressBarContext';
import {
  UNIVERSAL_ITEM_KEY,
  useSaveHandlerContext,
} from './SaveHandlerContext';
import ProgressBarTablet from './tablet/ProgressBar.tablet';

interface Props {
  onChangeTopOffset?: (top: number) => void;
}

export interface InternalProgressBarProps extends Props {
  saveModalVisible: boolean;
  isSaving: boolean;
  goBack: () => void;
  goHome: () => void;
  onConfirmSaveModal: () => void;
  onDenySaveModal: () => void;
  onCancelSaveModal: () => void;
  isReady: () => boolean;
}

export default function ProgressBar(props: Props) {
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: appProgress } = useGetApplicationProgress(caseId);
  const { saveApplicationProgress } = useSaveApplicationProgress();
  const {
    completedMap,
    setProgressBarState,
    groups,
    groupKey,
    subgroupKey,
    itemKey,
  } = useEAppProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
      groups: state.groups,
      groupKey: state.groupKey,
      subgroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  useEffect(() => {
    const saveApplicationProgressAction = async () => {
      const newAppProgress = { ...appProgress };
      Object.keys(completedMap).forEach((key: string) => {
        if (completedMap[key]) {
          // Append more completed route to progress object
          newAppProgress[key] = ApplicationProgress.COMPLETED;
        }
      });
      if (!isEqual(newAppProgress, appProgress)) {
        // Only save when detect object properties changed
        await saveApplicationProgress(newAppProgress);
      }
    };
    saveApplicationProgressAction();
  }, [completedMap, saveApplicationProgress, appProgress]);

  /**
   * Set current progress group key, expanded group key, item key to first element in groups
   */
  useEffect(() => {
    if (Array.isArray(groups) && groups.length > 0) {
      if (typeof groupKey !== 'undefined') {
        return;
      }
      const group = groups[0];
      const subgroup = group.items[0];
      setProgressBarState({
        groupKey: group.routeKey,
        expandedGroupKey: group.routeKey,
        subgroupKey: 'items' in subgroup ? subgroup.routeKey : undefined,
        itemKey:
          'items' in subgroup
            ? (subgroup as ProgressSubgroup).items?.[0]?.routeKey
            : subgroup.routeKey,
      });
    }
  }, [groups, groupKey, setProgressBarState]);

  /**
   * Update group, item group, item respectively based on their key
   */
  useEffect(() => {
    if (groups.length > 0) {
      const groupIndex = groups.findIndex(i => i.routeKey === groupKey);
      const activeGroup = groups[groupIndex];
      let activeSubgroup: ProgressSubgroup | undefined = undefined;
      let activeItem: ProgressItem | undefined = undefined;
      if (activeGroup && activeGroup.items) {
        const subgroupIndex = activeGroup.items.findIndex(
          i => i.routeKey === subgroupKey,
        );
        const itemIndex = activeGroup.items.findIndex(
          i => i.routeKey === itemKey,
        );
        const subgroup = activeGroup.items[subgroupIndex] as ProgressSubgroup;
        const item = activeGroup.items[itemIndex] as ProgressItem;
        if (subgroup?.items) {
          activeSubgroup = subgroup;
          activeItem = activeSubgroup?.items.find(i => i.routeKey === itemKey);
        } else {
          activeItem = item;
        }
      }
      setProgressBarState({
        group: activeGroup,
        groupIndex,
        subgroup: activeSubgroup,
        item: activeItem,
      });
    }
  }, [groups, groupKey, subgroupKey, itemKey, setProgressBarState]);

  useGenerateEAppRoutes();

  // Handle save event
  const { colors } = useTheme();
  const route = useRoute();
  const { t } = useTranslation(['eApp']);
  const [saveModalVisible, showSaveModal, hideSaveModal] = useToggle();
  const [loading, setLoading] = useState(false);
  const [goingBack, setGoingBack] = useState(false);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();

  useEffect(() => {
    const subscription = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        showSaveModal();
        return true;
      },
    );

    return () => subscription.remove();
  }, [showSaveModal]);

  const backToMain = useCallback(() => {
    navigation.navigate('Main');
  }, [navigation]);

  const backToHome = useCallback(() => {
    navigation.navigate('Main', {
      screen: 'Home',
    });
  }, [navigation]);

  const goBack = useCallback(() => {
    setGoingBack(true);
    if (route?.name === 'EApp') {
      showSaveModal();
      return;
    }
    if (navigation.canGoBack()) {
      GATracking.logButtonPress({
        screenName,
        screenClass: 'Sales flow',
        actionType: 'non_cta_button',
        buttonName: 'Progress bar back button',
      });

      navigation.goBack();
    } else {
      GATracking.logButtonPress({
        screenName,
        screenClass: 'Sales flow',
        actionType: 'non_cta_button',
        buttonName: 'Progress bar back button',
      });

      navigation.navigate('Main');
    }
  }, [route?.name, navigation, showSaveModal]);

  const goHome = useCallback(() => {
    setGoingBack(false);
    if (route?.name === 'EApp') {
      showSaveModal();
      return;
    }

    GATracking.logButtonPress({
      screenName,
      screenClass: 'Sales flow',
      actionType: 'non_cta_button',
      buttonName: 'Progress bar home button',
    });

    navigation.navigate('Main');
  }, [route?.name, navigation, showSaveModal]);

  const { get } = useSaveHandlerContext();
  const syncAppProgress = useSyncAppProgress();

  const onConfirmSaveModal = useCallback(async () => {
    try {
      setLoading(true);
      const onUniversalSave = get(groupKey, subgroupKey, UNIVERSAL_ITEM_KEY);

      const onSave = onUniversalSave
        ? onUniversalSave
        : get(groupKey, subgroupKey, itemKey);
      if (onSave) {
        await onSave();
      }
      await syncAppProgress();

      GATracking.logCustomEvent('application', {
        action_type: 'eapp_save',
      });
    } catch (err) {
      addErrorBottomToast([
        {
          message: 'An error occurred. Please try again later',
        },
      ]);
      return;
    } finally {
      hideSaveModal();
      setLoading(false);
    }
    addToast([
      {
        message: t('eApp:saved'),
        IconLeft: <Icon.Tick fill={colors.palette.white} />,
      },
    ]);
    if (goingBack) {
      backToMain();
    } else {
      backToHome();
    }
  }, [
    t,
    colors.palette.white,
    goingBack,
    get,
    groupKey,
    subgroupKey,
    itemKey,
    syncAppProgress,
    hideSaveModal,
    backToMain,
    backToHome,
  ]);

  const onDenySaveModal = useCallback(() => {
    GATracking.logCustomEvent('application', {
      action_type: 'eapp_exit',
    });

    hideSaveModal();
    if (goingBack) {
      backToMain();
    } else {
      backToHome();
    }
  }, [hideSaveModal, goingBack, backToMain, backToHome]);

  const { isTabletMode } = useLayoutAdoptionCheck();
  const Component = isTabletMode ? ProgressBarTablet : ProgressBarPhone;

  const { prompt } = usePromptContext();

  const { get: getSave } = useProgressBarContext();
  const isReady = useCallback(() => {
    const isReadyToSave = getSave(groupKey, subgroupKey);
    if (isReadyToSave) {
      const ready = isReadyToSave();
      if (!ready) {
        prompt<{ accept: string }>({
          title: t('eApp:invalidFields.title'),
          description: t('eApp:invalidFields.desc'),
          actions: getStyledAlertActions,
          config: {
            accept: t('eApp:ok'),
          },
          closable: false,
        });
        return false;
      }
    }
    syncAppProgress().then();
    return true;
  }, [getSave, groupKey, prompt, subgroupKey, t, syncAppProgress]);

  return (
    <Component
      {...props}
      saveModalVisible={saveModalVisible}
      isSaving={loading}
      goBack={goBack}
      goHome={goHome}
      onCancelSaveModal={hideSaveModal}
      onConfirmSaveModal={onConfirmSaveModal}
      onDenySaveModal={onDenySaveModal}
      isReady={isReady}
    />
  );
}
