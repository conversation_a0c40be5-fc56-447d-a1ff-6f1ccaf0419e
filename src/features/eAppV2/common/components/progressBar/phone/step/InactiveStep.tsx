import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { H8, Icon } from 'cube-ui-components';
import { memo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';

const Container = styled(Animated.View)<{
  color: string;
  completed?: boolean;
  disabled?: boolean;
}>(({ theme, color, completed, disabled }) => ({
  width: theme.sizes[6],
  height: theme.sizes[6],
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: theme.sizes[3],
  borderWidth: completed && !disabled ? 0 : 1,
  borderColor: color,
  backgroundColor: theme.colors.background,
}));

interface Props {
  step: number;
  completed?: boolean;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
}

export const InactiveStep = memo(function InactiveStep({
  step,
  completed,
  disabled,
  style,
}: Props) {
  const { colors } = useTheme();
  const borderColor = colors.palette.fwdGreyDark;
  return (
    <Container
      style={style}
      color={borderColor}
      completed={completed}
      disabled={disabled}>
      {completed ? (
        <Icon.TickCircle fill={colors.palette.alertGreen} />
      ) : (
        <H8 fontWeight="bold" color={borderColor}>
          {step}
        </H8>
      )}
    </Container>
  );
});
export default InactiveStep;
