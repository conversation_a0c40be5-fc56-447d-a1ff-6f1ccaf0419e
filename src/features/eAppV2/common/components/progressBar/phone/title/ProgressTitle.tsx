import styled from '@emotion/native';
import { H7, SvgIconProps } from 'cube-ui-components';
import React, { memo } from 'react';

const TitleContainer = styled.View(({ theme }) => ({
  height: theme.sizes[10],
  marginBottom: theme.space[2],
  flexDirection: 'row',
  alignSelf: 'stretch',
  alignItems: 'center',
}));

const Title = styled(H7)<{ hasIcon: boolean }>(({ theme, hasIcon }) => ({
  color: theme.colors.primary,
  marginLeft: hasIcon ? theme.space[2] : 0,
}));

interface Props {
  title?: string;
  icon?: React.ComponentType<SvgIconProps> | React.ReactElement<SvgIconProps>;
}

export const ProgressTitle = memo(function ProgressTitle({
  title,
  icon,
}: Props) {
  if (!icon && !title) {
    return null;
  }

  return (
    <TitleContainer>
      {icon && (
        <>{React.isValidElement(icon) ? icon : React.createElement(icon)}</>
      )}
      <Title fontWeight="bold" numberOfLines={1} hasIcon={!!icon}>
        {title}
      </Title>
    </TitleContainer>
  );
});
export default ProgressTitle;
