import styled from '@emotion/native';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import Step from '../step/Step';
import { useTheme } from '@emotion/react';
import ProgressTitle from '../title/ProgressTitle';
import Handler from '../modal/Handler';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { shallow } from 'zustand/shallow';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

const Container = styled(Animated.View)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    backgroundColor: theme.colors.background,
    borderBottomLeftRadius: theme.borderRadius.large,
    borderBottomRightRadius: theme.borderRadius.large,
    alignItems: 'center',
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
  };
});

const RowContainer = styled.Pressable(({ theme }) => ({
  flexDirection: 'row',
  height: theme.sizes[12],
  paddingTop: 3,
}));

const Row = styled(Animated.View)(({ theme }) => ({
  flexDirection: 'row',
  height: theme.sizes[6],
  flex: 1,
}));

export const ProgressBreadcrumb = memo(function ProgressBreadcrumb() {
  const { groups, groupIndex, item, group } = useEAppProgressBarStore(
    state => ({
      groups: state.groups,
      groupIndex: state.groupIndex,
      item: state.item,
      group: state.group,
    }),
    shallow,
  );
  const inProgressStep = useMemo(() => {
    return groupIndex + 1;
  }, [groupIndex]);
  const { sizes, animation } = useTheme();

  const isNoChildGroup = !group?.items || group.items.length === 0;

  const icon = isNoChildGroup ? group?.icon : item?.icon;
  const title = isNoChildGroup ? group?.title : item?.barTitle || item?.title;

  const { setProgressBarState, updateProgressBarZIndex } =
    useEAppProgressBarStore(state => ({
      setProgressBarState: state.setProgressBarState,
      updateProgressBarZIndex: state.updateProgressBarZIndex,
    }));
  const translateY = useSharedValue(-sizes[24]);
  const style = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY: translateY.value,
        },
      ],
    }),
    [translateY],
  );

  const showTitleBar = useCallback(() => {
    updateProgressBarZIndex(10);
    const resetZIndex = () => updateProgressBarZIndex(0);
    translateY.value = -sizes[24];
    translateY.value = withSequence(
      withTiming(0, {
        duration: animation.duration,
      }),
      withDelay(
        animation.duration * 4,
        withTiming(-sizes[12], { duration: animation.duration }, finished => {
          if (finished) {
            runOnJS(resetZIndex)();
          }
        }),
      ),
    );
  }, [updateProgressBarZIndex]);

  useEffect(() => {
    setProgressBarState({ showTitleBar });
    showTitleBar();
  }, [setProgressBarState, showTitleBar]);

  const showModal = useEAppProgressBarStore(state => state.showModal);

  return (
    <Container style={style}>
      <ProgressTitle title={title} icon={icon} />
      <RowContainer onPress={showModal}>
        <Row>
          {groups.map((group, index) => (
            <Step
              group={group}
              key={index}
              step={index + 1}
              inProgressStep={inProgressStep}
            />
          ))}
        </Row>
      </RowContainer>
      <Handler />
    </Container>
  );
});
export default ProgressBreadcrumb;
