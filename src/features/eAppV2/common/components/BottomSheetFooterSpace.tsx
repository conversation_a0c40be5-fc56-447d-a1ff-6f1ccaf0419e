import { useBottomSheetInternal } from '@gorhom/bottom-sheet';
import React from 'react';
import { ViewProps } from 'react-native';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';

export default function BottomSheetFooterSpace(props: ViewProps) {
  const { animatedFooterHeight } = useBottomSheetInternal();
  const animatedStyle = useAnimatedStyle(
    () => ({
      height: animatedFooterHeight.value,
    }),
    [animatedFooterHeight.value],
  );

  return <Animated.View {...props} style={animatedStyle} />;
}
