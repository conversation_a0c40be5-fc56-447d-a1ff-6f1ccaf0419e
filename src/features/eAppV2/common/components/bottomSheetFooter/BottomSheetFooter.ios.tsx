import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter as GorhomBottomSheetFooter,
  KEYBOARD_STATE,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import { Button } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { BottomSheetFooterProps } from './BottomSheetFooter';
import IncompleteFields from 'components/IncompleteFields';

export default function BottomSheetFooter({
  disabled,
  onPress,
  buttonTitle,
  buttonSubTitle,
  loading,
  totalIncompleteRequiredFields = 0,
  focusOnIncompleteField,
  noBorder,
  ...props
}: BottomSheetFooterProps) {
  const { space, colors } = useTheme();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { t } = useTranslation(['eApp']);
  const { animatedKeyboardState, animatedFooterHeight } =
    useBottomSheetInternal();
  const [footerHeight, setFooterHeight] = useState(
    Math.max(animatedFooterHeight.value, 0),
  );
  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY:
            animatedKeyboardState.value === KEYBOARD_STATE.SHOWN
              ? footerHeight
              : 0,
        },
      ],
    }),
    [animatedKeyboardState.value, footerHeight],
  );

  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();

  return (
    <GorhomBottomSheetFooter {...props}>
      <Animated.View
        onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
        style={[
          animatedStyle,
          {
            paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            paddingTop: space[4],
            paddingBottom: space[4] + bottomInset,
            backgroundColor: colors.background,
            borderTopWidth: noBorder ? 0 : 1,
            borderColor: colors.palette.fwdGrey[100],
            gap: space[4],
          },
        ]}>
        {totalIncompleteRequiredFields > 0 && (
          <IncompleteFields
            incompleteCount={totalIncompleteRequiredFields}
            focusNext={focusOnIncompleteField}
            message={t('eApp:incompleteFields', {
              count: totalIncompleteRequiredFields,
            })}
          />
        )}
        <Button
          text={buttonTitle ?? t('eApp:done')}
          subtext={buttonSubTitle}
          disabled={disabled}
          onPress={onPress}
          loading={loading}
          style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
        />
      </Animated.View>
    </GorhomBottomSheetFooter>
  );
}
