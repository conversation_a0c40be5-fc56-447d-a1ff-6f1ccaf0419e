import { useTheme } from '@emotion/react';
import { Body, Box, ExtraSmallLabel, Row } from 'cube-ui-components';

import React from 'react';
const InfoField = ({
  fieldName,
  fieldValue,
  fieldDetail,
  isVertical,
  isFlat,
  isImportant,
}: {
  isImportant?: boolean;
  isFlat?: boolean;
  isVertical?: boolean;
  fieldDetail?: string;
  fieldName?: string | null;
  fieldValue?: string | number | null;
}) => {
  const { colors, sizes } = useTheme();
  if (isFlat) {
    return (
      <Row marginBottom={sizes[3]}>
        <Box flex={1}>
          <Body color={colors.palette.fwdGreyDarker}>{fieldName}</Body>
        </Box>
        <Box flex={3}>
          <Body color={colors.palette.fwdDarkGreen[100]}>{fieldValue}</Body>
        </Box>
      </Row>
    );
  }
  if (isVertical) {
    return (
      <Box marginBottom={sizes[3]}>
        <Body color={colors.palette.fwdGreyDarker}>{fieldName}</Body>
        <Body color={colors.palette.fwdDarkGreen[100]}>{fieldValue}</Body>
      </Box>
    );
  }
  return (
    <Row mb={sizes[3]} alignItems="flex-start" flex={1}>
      <Box flex={1}>
        <Body
          color={
            isImportant
              ? colors.palette.fwdDarkGreen[100]
              : colors.palette.fwdGreyDarker
          }>
          {fieldName}
        </Body>
      </Box>
      <Box flex={1}>
        <Body color={colors.palette.fwdDarkGreen[100]}>{fieldValue}</Body>
        {fieldDetail && (
          <ExtraSmallLabel color={colors.palette.fwdGreyDarker}>
            {fieldDetail}
          </ExtraSmallLabel>
        )}
      </Box>
    </Row>
  );
};
export default InfoField;
