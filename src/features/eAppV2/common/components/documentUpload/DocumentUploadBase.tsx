import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DocumentUploadPhone from './phone/DocumentUploadBase.phone';
import DocumentUploadTablet from './tablet/DocumentUploadBase.tablet';
import { ImagePickerFile } from 'components/ImagePicker/utils';
import { DocumentCustomerType, DocumentType } from 'types/document';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { ComponentProps, useMemo } from 'react';
import { F2FLevel } from '../../types/remoteSellingTypes';
import EAppFooterTablet from '../footer/EAppFooter.tablet';
import { useEAppProgressBarStore } from '../../utils/store/eAppProgressBarStore';
import useUpdateEffect from 'hooks/useUpdateEffect';

export interface DocumentUploadType {
  type: DocumentType;
  title: string;
  multiple?: boolean;
  optional?: boolean;
  customerType?: DocumentCustomerType; // override customerType
  silentOCREnabled?: boolean;
}

export interface UploaderProps {
  title?: string;
  highlight?: boolean;
  documents: DocumentUploadType[] | undefined;
  partyId: string;
  customerType: DocumentCustomerType;
  customerSeq: string;
  shouldShowInstruction?: boolean;
  setShouldShowInstruction?: (value: boolean) => void;
  itemKey: string;
}

export type DocumentFile = ImagePickerFile & {
  status: 'uploading' | 'uploaded';
};

export const UPLOAD_COMPRESSION_CONFIG = {
  compression: 0.5,
  maxHeight: 800,
  maxWidth: 800,
};

export interface DocumentUploadTab {
  partyId: string;
  customerSeq: string;
  customerType: DocumentCustomerType;
  role: string;
  key: string;
  name: string;
  documents: DocumentUploadType[];
  title?: string;
  disabledF2F?: boolean;
  disabledForm?: boolean;
  hiddenF2F?: boolean;
}

export interface DocumentUploadProps {
  tabs: DocumentUploadTab[];
  shouldRenderF2F?: boolean;
  highlight?: boolean;
  /**
   * case: remoteSelling flag will be saved at case level and sync between parties
   * party: remoteSelling flag will be saved at party level and are separated between parties
   */
  f2fLevel?: F2FLevel;
  progressLock?: ComponentProps<typeof EAppFooterTablet>['progressLock'];
  disabledNext?: boolean;
  /**
   * Denotes the document upload sections is grouped into 1 screen
   */
  groupedSections?: boolean;
}

export interface InternalDocumentUploadProps extends DocumentUploadProps {
  isValid?: boolean;
  arePreviousStepsComplete?: boolean;
}

export default function DocumentUploadBase(props: DocumentUploadProps) {
  const { caseObj } = useGetActiveCase();
  const isAllDocumentsValid = useMemo(() => {
    return props.tabs.every(tab => {
      const partyFiles =
        caseObj?.files?.filter(f => f.partyId === tab.partyId) || [];
      return tab.documents.every(
        doc =>
          doc.optional ||
          partyFiles.filter(f => f.docType === doc.type).length > 0,
      );
    });
  }, [caseObj?.files, props?.tabs]);

  const groups = useEAppProgressBarStore(state => state.groups);
  const arePreviousStepsComplete = useMemo(() => {
    let allComplete = true;
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i];
      if (group.routeKey === 'documentUpload') {
        break;
      } else {
        allComplete &&= Boolean(group.completed);
      }
    }
    return allComplete;
  }, [groups]);

  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <DocumentUploadTablet
      {...props}
      isValid={isAllDocumentsValid}
      arePreviousStepsComplete={arePreviousStepsComplete}
    />
  ) : (
    <DocumentUploadPhone
      {...props}
      isValid={isAllDocumentsValid}
      arePreviousStepsComplete={arePreviousStepsComplete}
    />
  );
}
