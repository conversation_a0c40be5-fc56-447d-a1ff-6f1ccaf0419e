import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Column,
  HighlightShadow,
  Icon,
  Row,
  Shadow,
  Typography,
} from 'cube-ui-components';
import { ForwardedRef, forwardRef, memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import FileList from './FileList';
import { DocumentFile } from '../DocumentUploadBase';
import { ImagePickerFile } from 'components/ImagePicker/utils';
import { StyleSheet, View } from 'react-native';

export const UploaderItem = memo(
  forwardRef(
    (
      {
        index,
        files,
        title,
        type,
        multiple,
        onStartUploadFile: onStartUploadAsset,
        onRemoveFile,
        last,
        highlight,
        disabled,
      }: {
        index: number;
        title: string;
        type: string;
        files: DocumentFile[];
        multiple?: boolean;
        onStartUploadFile: (type: string) => void;
        onRemoveFile: (asset: ImagePickerFile) => void;
        last?: boolean;
        highlight?: boolean;
        disabled?: boolean;
      },
      ref: ForwardedRef<View>,
    ) => {
      const { space, colors, borderRadius } = useTheme();
      const { t } = useTranslation(['eApp']);

      const onPress = useCallback(() => {
        onStartUploadAsset(type);
      }, [onStartUploadAsset, type]);

      return (
        <Container ref={ref} index={index}>
          <Row alignItems="center">
            <Typography.H7 fontWeight="medium" style={{ flex: 1 }}>
              {title}
            </Typography.H7>
            <Box width={space[3]} />
            {(files.length === 0 || (files.length >= 1 && multiple)) && (
              <HighlightShadow disabled={!highlight} borderRadius="x-small">
                <Button
                  size="small"
                  disabled={disabled}
                  icon={<Icon.Upload size={18} />}
                  text={t('eApp:documentUpload.upload')}
                  variant="secondary"
                  onPress={onPress}
                  contentStyle={styles.buttonContent}
                />
              </HighlightShadow>
            )}
          </Row>
          {files.length > 0 && <Box h={space[5]} />}
          <FileList files={files} onRemoveFile={onRemoveFile} disabled={disabled} />
          {(files.length > 0 || last) && <Box h={2} />}
        </Container>
      );
    },
  ),
);
export default UploaderItem;

const Container = styled(Column)<{ index: number }>(
  ({ theme: { space, colors }, index }) => ({
    paddingVertical: 18,
    borderTopWidth: index > 0 ? 1 : 0,
    borderTopColor: colors.palette.fwdGrey[100],
  }),
);

const styles = StyleSheet.create({
  buttonContent: {
    height: 36,
  },
});
