import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useIncompleteSync } from 'features/eAppV2/common/hooks/useIncompleteSync';
import { RouteItemKey } from 'features/eAppV2/common/types/progressBarTypes';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { Fragment, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { DocumentCustomerType } from 'types/document';
import { PartyRole } from 'types/party';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import EAppFooterTablet from '../../footer/EAppFooter.tablet';
import TabletSections, {
  TabletSectionsProps,
  flattenSectionNameList,
} from '../../TabletSections';
import {
  DocumentUploadType,
  InternalDocumentUploadProps,
} from '../DocumentUploadBase';
import F2FQuestion from '../F2FQuestion';
import Uploader, { UploaderRef } from './Uploader';

export default function DocumentUploadBaseTablet({
  tabs,
  shouldRenderF2F,
  f2fLevel,
  highlight,
  isValid,
  progressLock,
  arePreviousStepsComplete = true,
  disabledNext,
}: InternalDocumentUploadProps) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const [activePath, setActivePath] = useState<string>('');
  const { nextGroup } = useEAppProgressBarStore(
    state => ({
      nextGroup: state.nextGroup,
    }),
    shallow,
  );
  const activeTab = useMemo(() => {
    if (activePath.includes('.')) {
      const [key, index] = activePath.split('.');
      return tabs.filter(tab => tab.key === key)[Number(index)];
    }
    return tabs.find(tab => tab.key === activePath);
  }, [activePath, tabs]);

  const scrollViewRef = useRef<ScrollView[]>([]);
  const uploaderRef = useRef<UploaderRef>(null);

  const { caseObj } = useGetActiveCase();

  const isEntity = useCheckEntity();

  const isCurrentTabValid = useMemo(() => {
    const partyFiles =
      caseObj?.files?.filter(f => f.partyId === activeTab?.partyId) || [];
    return activeTab?.documents.every(
      doc =>
        doc.optional ||
        partyFiles.filter(f => f.docType === doc.type).length > 0,
    );
  }, [activeTab?.documents, activeTab?.partyId, caseObj?.files]);

  const sections = useMemo(() => {
    const insureds =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER),
      ) || [];
    const isSingleInsured = insureds.length <= 1;
    const multiInsuredSection: TabletSectionsProps['items'][0] = {
      name: '',
      title: '',
      subItems: [],
    };
    const sections = tabs.reduce<TabletSectionsProps['items']>(
      (sections, tab, tabIndex) => {
        const renderDocumentUploadForm = ({
          partyId,
          title,
          items,
          customerType,
          customerSeq,
        }: {
          partyId: string;
          title: string;
          items: DocumentUploadType[];
          customerType: DocumentCustomerType;
          customerSeq: string;
        }) => {
          return (
            <ScrollViewContainer
              ref={el => {
                el && (scrollViewRef.current[tabIndex] = el);
              }}>
              <Box mt={space[6]} />
              {shouldRenderF2F && (
                <F2FQuestion
                  f2fLevel={f2fLevel}
                  partyId={partyId}
                  disabledF2F={tab.disabledF2F || tab.disabledForm}
                  hiddenF2F={tab.hiddenF2F}
                />
              )}
              <Uploader
                ref={uploaderRef}
                scrollViewRef={scrollViewRef}
                title={title}
                documents={items}
                partyId={partyId}
                customerType={customerType}
                customerSeq={customerSeq}
                highlight={highlight}
                itemKey={tab.key}
                disabled={tab.disabledForm}
              />
              <Box mb={space[6]} />
            </ScrollViewContainer>
          );
        };
        const party = caseObj?.parties?.find(p => p.id === tab.partyId);
        const isInsured =
          party?.roles.includes(PartyRole.INSURED) &&
          !party?.roles.includes(PartyRole.PROPOSER);

        const formTitle = isEntity
          ? t('eApp:documentUpload.documentsOf', {
              label: `${tab.name} (${tab.role})`,
            })
          : t('eApp:documentUpload.documentsOf', {
              label: `${tab.role}: ${tab.name}`,
            });
        if (isInsured && !isSingleInsured) {
          multiInsuredSection.title = tab.role;
          multiInsuredSection.name = tab.key;
          multiInsuredSection.subItems?.push({
            name: String(multiInsuredSection.subItems?.length || 0),
            title: tab.name ?? '',
            content: renderDocumentUploadForm({
              partyId: tab.partyId,
              title: tab.title ?? formTitle,
              items: tab.documents,
              customerSeq: tab.customerSeq,
              customerType: tab.customerType,
            }),
          });
        } else {
          sections.push({
            name: tab.key,
            title: tab.role,
            subtitle: tab.name ?? '',
            content: renderDocumentUploadForm({
              partyId: tab.partyId,
              title: tab.title ?? formTitle,
              items: tab.documents,
              customerSeq: tab.customerSeq,
              customerType: tab.customerType,
            }),
          });
        }

        return sections;
      },
      [],
    );
    if (
      multiInsuredSection.subItems?.length &&
      multiInsuredSection.subItems.length > 0
    ) {
      sections.push(multiInsuredSection);
    }
    return sections;
  }, [
    caseObj?.parties,
    f2fLevel,
    highlight,
    isEntity,
    shouldRenderF2F,
    space,
    t,
    tabs,
  ]);

  const { flattenPaths, activePathIndex, isLastTabActive } = useMemo(() => {
    const flattenPaths = flattenSectionNameList(sections);
    const activePathIndex = flattenPaths.findIndex(path => path === activePath);
    const isLastTabActive = activePathIndex === flattenPaths.length - 1;
    return {
      flattenPaths,
      activePathIndex,
      isLastTabActive,
    };
  }, [activePath, sections]);

  const onContinue = useCallback(() => {
    GATracking.logCustomEvent('application', {
      action_type: 'eapp_submit_documents',
      application_type: 'F2F',
    });

    if (isLastTabActive && isValid) {
      nextGroup(true);
    } else {
      if (activeTab?.key) {
        setActivePath(
          flattenPaths[Math.min(activePathIndex + 1, flattenPaths.length - 1)],
        );
      }
    }
  }, [
    activePathIndex,
    activeTab?.key,
    flattenPaths,
    isLastTabActive,
    isValid,
    nextGroup,
  ]);

  const invalidIndices = useMemo(() => {
    const indices = [];
    for (let tabIndex = 0; tabIndex < tabs.length; tabIndex++) {
      const tab = tabs[tabIndex];
      const partyFiles =
        caseObj?.files?.filter(f => f.partyId === tab.partyId) || [];
      for (let docIndex = 0; docIndex < tab.documents.length; docIndex++) {
        const doc = tab.documents[docIndex];
        if (
          !doc.optional &&
          partyFiles.filter(f => f.docType === doc.type).length === 0
        ) {
          indices.push({ tabIndex, docIndex });
        }
      }
    }
    return indices;
  }, [caseObj?.files, tabs]);

  const focusOnIncompleteField = useCallback(() => {
    if (uploaderRef.current && invalidIndices.length) {
      uploaderRef.current?.scrollToItem(
        invalidIndices?.[0]?.tabIndex,
        invalidIndices?.[0]?.docIndex,
      );
    }
  }, [invalidIndices]);

  //Only apply for PH first
  useIncompleteSync(
    isLastTabActive ? !isValid || !isCurrentTabValid : !isCurrentTabValid,
    'documentUpload',
    undefined,
    activePath as RouteItemKey | undefined,
    country === 'ph',
    country === 'ph',
  );

  const disabled = isLastTabActive
    ? !isValid || !isCurrentTabValid || !arePreviousStepsComplete
    : !isCurrentTabValid;

  return (
    <Fragment>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock={progressLock}
        onPrimaryPress={onContinue}
        primaryDisabled={disabled || disabledNext}
        focusOnIncompleteField={focusOnIncompleteField}
        focusOnIncompleteFieldLabel={t(
          'eApp:documentUpload.footer.focusOnIncompleteField',
        )}
        totalIncompleteRequiredFields={highlight ? invalidIndices.length : 0}
      />
    </Fragment>
  );
}

const ScrollViewContainer = styled(ScrollView)(({ theme: { space } }) => ({
  flex: 1,
  paddingRight: space[8],
}));
