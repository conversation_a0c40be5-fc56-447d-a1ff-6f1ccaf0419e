import { <PERSON><PERSON><PERSON>, ViewStyle } from 'react-native';
import {
  RouteG<PERSON>Key,
  RouteItemKey,
  RouteSubgroupKey,
} from '../../types/progressBarTypes';
import { ComponentProps } from 'react';
import { Box } from 'cube-ui-components';

type FooterButtonPrefixes = 'primary' | 'secondary' | 'tertiary';

type PostfixToValue = {
  Label?: string;
  SubLabel?: string;
  Disabled?: boolean;
  Loading?: boolean;
  Press?: () => void;
  Style?: StyleProp<ViewStyle>;
  ContentStyle?: StyleProp<ViewStyle>;
};

type ButtonProps = {
  [K in keyof PostfixToValue as K extends 'Press'
    ? `on${Capitalize<FooterButtonPrefixes>}Press`
    : `${FooterButtonPrefixes}${K}`]: PostfixToValue[K];
};

export type EAppFooterProps = {
  variant?: 'primary' | 'secondary';
  direction?: ComponentProps<typeof Box>['flexDirection'];
  hasShadow?: boolean;
  progressLock?:
    | `${RouteGroupKey | ''}-${RouteSubgroupKey | ''}`
    | `${RouteGroupKey | ''}-${RouteSubgroupKey | ''}-${RouteItemKey | ''}`;
  note?: string;
  totalIncompleteRequiredFields?: number;
  focusOnIncompleteField?: () => void;
  focusOnIncompleteFieldLabel?: string;
  leftComponent?: React.ReactNode;
  children?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
} & ButtonProps;
