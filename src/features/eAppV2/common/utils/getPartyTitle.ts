import { TFunction } from 'i18next';
import { OptionList } from 'types/optionList';
import { Party, PartyRole } from 'types/party';
import { country } from 'utils/context';

export const getPartyTitle = (
  party: Party,
  t: TFunction<['eApp']>,
  optionList?: OptionList,
) => {
  const relationshipOptions =
    country === 'my' || country === 'ib'
      ? optionList?.RELATIONSHIP.options
      : optionList?.OWNER_RELATIONSHIP.options;
  const relationship = relationshipOptions?.find(
    o => o.value === party.relationship,
  )?.label;
  if (party?.isMainInsured && country === 'my') {
    return party?.roles.includes(PartyRole.PROPOSER)
      ? `${t('eApp:policyOwner')} / ${t('eApp:mainInsured')}`
      : `${t('eApp:mainInsured')} (${relationship})`;
  }

  if (party?.roles.includes(PartyRole.PROPOSER)) {
    return `${t('eApp:policyOwner')}`;
  }

  if (party.roles.includes(PartyRole.INSURED)) {
    return `${t('eApp:insured')} (${relationship})`;
  }

  if (party.roles.includes(PartyRole.PAYER)) {
    return t('eApp:payor');
  }
};
