import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function RiskArrowSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 24}
      height={props.height || 11}
      viewBox="0 0 24 11"
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path d="M0 .294h24v4l-12 6-12-6v-4z" fill="#E87722" />
    </Svg>
  );
}

export default RiskArrowSVG;
