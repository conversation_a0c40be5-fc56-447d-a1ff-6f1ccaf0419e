import { InferType, object, string } from 'yup';
import { requiredMessage } from 'features/eApp/validations/eAppErrorMessages';

export const rqpQuestionFormSchema = {
  investmentTime: string().required(requiredMessage),
  investmentKnowledge: string().required(requiredMessage),
  financialGoal: string().required(requiredMessage),
  importanceOfInvestedFund: string().required(requiredMessage),
  riskAttitude: string().required(requiredMessage),
};

export const rqpQuestionFormSchemaValidate = object(rqpQuestionFormSchema);
export type rqpQuestionFormType = InferType<
  typeof rqpQuestionFormSchemaValidate
>;
