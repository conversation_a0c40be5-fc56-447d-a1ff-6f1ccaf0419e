import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { MaterialTopTabBarProps } from '@react-navigation/material-top-tabs';
import { Typography, XView } from 'cube-ui-components';

interface CustomTabBarProps extends MaterialTopTabBarProps {
  onTabPress: (index: number) => void;
  focusedSectionIndex: number;
}

const CustomTab = styled.TouchableOpacity<{ isFocused: boolean }>(
  ({ theme: { colors }, isFocused }) => {
    return [
      {
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
      },
      isFocused && {
        borderBottomColor: colors.primary,
        borderBottomWidth: 3,
      },
    ];
  },
);

const CustomTabBar = ({
  state,
  descriptors,
  navigation,
  onTabPress,
  focusedSectionIndex,
}: CustomTabBarProps) => {
  const { colors } = useTheme();
  return (
    <XView style={{ backgroundColor: colors.background, height: 50 }}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options?.title;

        const isFocused = focusedSectionIndex === index;

        const onPress = () => {
          if (typeof onTabPress === 'function') {
            onTabPress(index);
          }
          navigation.navigate({ name: route.name, params: undefined });
        };

        return (
          <CustomTab key={index} onPress={onPress} isFocused={isFocused}>
            <Typography.LargeBody
              fontWeight={isFocused ? 'bold' : 'normal'}
              color={isFocused ? colors.primary : colors.secondary}>
              {label}
            </Typography.LargeBody>
          </CustomTab>
        );
      })}
    </XView>
  );
};

export default CustomTabBar;
