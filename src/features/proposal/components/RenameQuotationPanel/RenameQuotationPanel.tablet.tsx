import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  Checkbox,
  TextField,
  Typography,
  XView,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';

import { View } from 'react-native';

import { useTranslation } from 'react-i18next';
import { RenameQuotationPanelProps } from './types';
import Modal from 'react-native-modal';

function PanelModalContent({ children }: { children: React.ReactNode }) {
  const { space, colors, borderRadius } = useTheme();

  return (
    <View
      style={{
        padding: space[12],
        borderRadius: borderRadius.large,
        backgroundColor: colors.background,
        margin: space[30],
        minHeight: space[80],
      }}>
      {children}
    </View>
  );
}

export default function RenameQuotationPanelTablet({
  visible,
  onClose,
  onSave,
  isFirstVersion,
  quotationName,
  error,
  disableTextField,
}: RenameQuotationPanelProps) {
  const { space } = useTheme();
  const { t } = useTranslation(['proposal', 'common']);

  const [isNewVersion, setIsNewVersion] = useState(false);

  const [proposalName, setProposalName] = useState(quotationName);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const proposalNameError = useMemo(() => {
    return error?.(quotationName, proposalName, isNewVersion);
  }, [error, quotationName, proposalName, isNewVersion]);

  const isSaveDisabled = () => {
    return proposalName.length === 0 || (!isEditing && proposalName.length > 0);
  };

  useEffect(() => {
    if (visible) {
      setProposalName(quotationName);
    }
  }, [quotationName, visible]);

  useEffect(() => {
    if (isNewVersion) {
      setProposalName(prev => {
        if (prev.includes(t('proposal:saveDialog.version2'))) {
          return prev;
        }
        return `${prev} ${t('proposal:saveDialog.version2')}`;
      });
    } else {
      setProposalName(prev =>
        prev.replace(` ${t('proposal:saveDialog.version2')}`, ''),
      );
    }
  }, [isNewVersion, t]);

  const localOnSave = async () => {
    try {
      await onSave(proposalName, isNewVersion);
      setProposalName(proposalName);
      onClose();
    } catch (error) {
      console.error('Error saving quotation:', error);
    } finally {
      onClose();
      setIsEditing(false);
      setIsSaving(false);
    }
  };

  return (
    <Modal isVisible={visible} backdropOpacity={0.5} avoidKeyboard>
      <PanelModalContent>
        <View style={{ marginBottom: space[6] }}>
          <Typography.H6 fontWeight="bold">
            {t('proposal:editDialog.title')}
          </Typography.H6>
        </View>

        <TextField
          disabled={disableTextField}
          placeholder={t('proposal:saveDialog.proposalNamePlaceholder')}
          label={t('proposal:saveDialog.proposalName')}
          value={proposalName}
          defaultValue={quotationName}
          onChange={(newValue: string) => {
            setProposalName(newValue);
            setIsEditing(true);
          }}
          error={proposalNameError}
          style={{ marginBottom: space[12] }}
        />

        <XView style={{ justifyContent: 'center' }}>
          <Button
            text={t('common:cancel')}
            variant="secondary"
            onPress={() => {
              setIsEditing(false);
              onClose();
            }}
            style={{ width: space[50], marginRight: space[4] }}
          />
          <Button
            text={t('common:save')}
            disabled={isSaveDisabled()}
            loading={isSaving}
            onPress={localOnSave}
            style={{ width: space[50] }}
          />
        </XView>
      </PanelModalContent>
    </Modal>
  );
}
