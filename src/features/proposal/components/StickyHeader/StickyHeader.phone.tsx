import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Typography, XView } from 'cube-ui-components';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, Pressable, View, ViewProps } from 'react-native';
import Animated, {
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { getPartyName, Party, PartyType } from 'types/party';
import { DeathBenefitOption, PaymentMode } from 'types/proposal';
import { QuotationSummary } from './QuotationSummary';

import { useProductImageQuery } from 'features/productSelection/hooks/useProducts';
import { country } from 'utils/context';
import { countryModuleSiConfig } from 'utils/config/module';
import QuotationActions from './QuotationActions';
import { SkeletonBlock, SkeletonProductImage } from './QuotationSkeleton';

const InfoContainer = styled.View(({ theme: { space } }) => ({
  marginTop: space[5],
  gap: space[2],
}));

const InfoItemContainer = styled.View(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const InfoItemLabel = styled(Typography.LargeBody)(({ theme: { colors } }) => ({
  flexBasis: '53%',
  color: colors.palette.fwdOrange[20],
}));

const InfoItemValue = styled(Typography.LargeBody)(({ theme: { colors } }) => ({
  flexBasis: '47%',
  color: colors.onPrimary,
}));

export interface StickyHeaderProps extends ViewProps {
  planName: string;
  productLine?: string;
  logo?: string;
  currency?: string;
  sumAssured?: number;
  modalPremium?: number;
  paymentMode?: PaymentMode;
  paymentTerm?: number;
  paymentType?: string;
  policyBenefitPeriod?: number;
  owner?: Party;
  insured?: Party;
  isInitializing: boolean;
  isCollapsed: boolean;
  currentHeight: SharedValue<number>;
  maxHeight: SharedValue<number>;
  minHeight: SharedValue<number>;
  deathBenefitOption?: DeathBenefitOption;
}

const StickyHeader = ({
  planName,
  productLine,
  logo,
  currency,
  sumAssured,
  modalPremium,
  paymentMode,
  paymentTerm,
  paymentType,
  policyBenefitPeriod,
  owner,
  insured,
  currentHeight,
  maxHeight,
  minHeight,
  isInitializing,
  isCollapsed,
  deathBenefitOption,
}: StickyHeaderProps) => {
  const { colors, space, animation, borderRadius } = useTheme();
  const { t } = useTranslation(['common', 'proposal']);

  const { data: imageUrl } = useProductImageQuery(logo);

  const [isShowDetails, setIsShowDetails] = useState<boolean>(false);

  const headerHeightFromPadding = space[8];
  const planHeightSharedValue = useSharedValue(0);
  const quotationSummaryHeightSharedValue = useSharedValue(90);

  const quotationActionsHeightSharedValue = useSharedValue(
    countryModuleSiConfig.summaryModalsEnabled ? 50 : 0,
  );

  const detailsHeightSharedValue = useSharedValue(0);
  const normalHeightSharedValue = useDerivedValue(() => {
    return (
      planHeightSharedValue.value +
      quotationSummaryHeightSharedValue.value +
      quotationActionsHeightSharedValue.value +
      headerHeightFromPadding
    );
  });
  const expandedHeightSharedValue = useDerivedValue(() => {
    return (
      normalHeightSharedValue.value + detailsHeightSharedValue.value + space[4]
    );
  });
  const collapsedHeightSharedValue = useDerivedValue(() => {
    return quotationSummaryHeightSharedValue.value;
  });

  useAnimatedReaction(
    () => {
      return normalHeightSharedValue.value;
    },
    (currentValue, prevValue) => {
      if (prevValue === null || currentValue > prevValue) {
        // initial animation
        currentHeight.value = withTiming(normalHeightSharedValue.value, {
          duration: animation.duration,
        });
        // init maxHeight
        maxHeight.value = normalHeightSharedValue.value;
      }
    },
  );

  useAnimatedReaction(
    () => {
      return collapsedHeightSharedValue.value;
    },
    (currentValue, prevValue) => {
      if (prevValue === null || currentValue > prevValue) {
        // init minHeight
        minHeight.value = collapsedHeightSharedValue.value;
      }
    },
  );

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      height:
        currentHeight.value <= headerHeightFromPadding
          ? 'auto'
          : currentHeight.value,
      padding: interpolate(
        currentHeight.value,
        [
          quotationSummaryHeightSharedValue.value,
          normalHeightSharedValue.value,
        ],
        [0, space[4]],
        Extrapolation.CLAMP,
      ),
      backgroundColor: colors.primary,
      overflow: 'hidden',
    };
  });

  const animatedPlanStyle = useAnimatedStyle(() => {
    return {
      height:
        currentHeight.value <= headerHeightFromPadding ||
        planHeightSharedValue.value === 0
          ? 'auto'
          : interpolate(
              currentHeight.value,
              [collapsedHeightSharedValue.value, normalHeightSharedValue.value],
              [0, planHeightSharedValue.value],
              Extrapolation.CLAMP,
            ),
      opacity: interpolate(
        currentHeight.value,
        [collapsedHeightSharedValue.value, normalHeightSharedValue.value],
        [0, 1],
        Extrapolation.CLAMP,
      ),
    };
  });

  const animatedSummaryStyle = useAnimatedStyle(() => {
    return {
      height: quotationSummaryHeightSharedValue.value,
      backgroundColor: 'transparent',
      overflow: 'hidden',
      borderRadius: interpolate(
        currentHeight.value,
        [
          quotationSummaryHeightSharedValue.value,
          normalHeightSharedValue.value,
        ],
        [0, space[2]],
        Extrapolation.CLAMP,
      ),
    };
  });

  const animatedQuotationActionStyle = useAnimatedStyle(() => {
    return {
      height: interpolate(
        currentHeight.value,
        [
          collapsedHeightSharedValue.value,
          normalHeightSharedValue.value,
          expandedHeightSharedValue.value,
        ],
        [0, quotationActionsHeightSharedValue.value, space[8]],
        Extrapolation.CLAMP,
      ),
      marginTop: space[4],
    };
  });

  const animatedDetailsStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(
        currentHeight.value,
        [normalHeightSharedValue.value, expandedHeightSharedValue.value],
        [0, 1],
        Extrapolation.CLAMP,
      ),
      zIndex: -1,
    };
  });

  const ownerName = useMemo(() => {
    if (!owner) {
      return '--';
    }
    return owner?.clientType?.toUpperCase() === PartyType.ENTITY
      ? owner?.entity?.name
      : getPartyName(owner);
  }, [owner]);

  const insuredName = useMemo(() => {
    if (!insured) {
      return '--';
    }
    return getPartyName(insured);
  }, [insured]);

  const normalizedProductLine = useMemo(
    () =>
      productLine && productLine.split(', ').length === 1
        ? productLine.split(',').join(', ')
        : productLine,
    [productLine],
  );

  const onPressViewBtn = (newIsShow: boolean) => {
    if (isInitializing) {
      return;
    }
    setIsShowDetails(newIsShow);
    const newHeight = newIsShow
      ? expandedHeightSharedValue.value
      : normalHeightSharedValue.value;
    maxHeight.value = newHeight;

    currentHeight.value = withTiming(newHeight, {
      duration: animation.duration,
    });
  };

  return (
    <Animated.View style={animatedContainerStyle}>
      <Animated.View style={animatedPlanStyle}>
        <View
          onLayout={e => {
            if (e.nativeEvent.layout.height >= planHeightSharedValue.value) {
              planHeightSharedValue.value = Math.floor(
                e.nativeEvent.layout.height,
              );
            }
          }}>
          <XView style={{ marginBottom: space[5] }}>
            <Typography.LargeBody
              style={{ flexGrow: 1 }}
              fontWeight="bold"
              color={colors.background}>
              {t('proposal:header.planYouWillPurchase')}
            </Typography.LargeBody>
            {/* hard code temporarily for PH 
              TODO: remove this View button
            */}
            {country === 'ph' && (
              <Pressable onPress={() => onPressViewBtn(!isShowDetails)}>
                <XView style={{ alignItems: 'center' }}>
                  <Typography.LargeBody
                    fontWeight="bold"
                    color={colors.background}
                    style={{ marginRight: space[1], width: space[11] }}>
                    {!isShowDetails ? t('common:view') : t('common:close')}
                  </Typography.LargeBody>
                  {!isShowDetails ? (
                    <Icon.ChevronDown size={space[5]} fill={colors.onPrimary} />
                  ) : (
                    <Icon.Close size={space[5]} fill={colors.onPrimary} />
                  )}
                </XView>
              </Pressable>
            )}
          </XView>

          <XView style={{ marginBottom: space[5] }}>
            {!isInitializing ? (
              <Image
                source={{ uri: imageUrl || logo }}
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: borderRadius.small,
                }}
              />
            ) : (
              <SkeletonProductImage />
            )}

            <View
              style={{
                alignItems: 'flex-start',
                justifyContent: 'space-around',
                paddingHorizontal: space[3],
                flex: 1,
              }}>
              {normalizedProductLine &&
                (!isInitializing ? (
                  <View
                    style={{
                      backgroundColor: colors.palette.fwdOrange[20],
                      justifyContent: 'center',
                      alignItems: 'center',
                      paddingHorizontal: space[1],
                    }}>
                    <Typography.SmallBody
                      color={colors.primary}
                      fontWeight="medium">
                      {normalizedProductLine}
                    </Typography.SmallBody>
                  </View>
                ) : (
                  <SkeletonBlock height={space[4]} width={space[15]} />
                ))}

              {!isInitializing ? (
                <Typography.ExtraLargeBody
                  fontWeight="bold"
                  color={colors.background}>
                  {planName}
                </Typography.ExtraLargeBody>
              ) : (
                <SkeletonBlock
                  height={space[6]}
                  width={space[65]}
                  marginTop={space[2]}
                />
              )}
            </View>
          </XView>
        </View>
      </Animated.View>

      <Animated.View style={animatedSummaryStyle}>
        <QuotationSummary
          currency={currency}
          sumAssured={sumAssured}
          modalPremium={modalPremium}
          paymentMode={paymentMode}
          collapsed={isCollapsed}
          isInitializing={isInitializing}
        />
      </Animated.View>
      {countryModuleSiConfig.summaryModalsEnabled && (
        <Animated.View style={animatedQuotationActionStyle}>
          <QuotationActions />
        </Animated.View>
      )}

      <Animated.View style={animatedDetailsStyle}>
        <InfoContainer
          onLayout={e => {
            detailsHeightSharedValue.value = e.nativeEvent.layout.height;
          }}>
          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.productLine`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">
              {productLine ?? '--'}
            </InfoItemValue>
          </InfoItemContainer>

          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.paymentMode`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">
              {paymentMode ? t(`proposal:paymentMode.${paymentMode}`) : '--'}
            </InfoItemValue>
          </InfoItemContainer>

          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.paymentTerm`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">
              {paymentTerm ?? '--'}
            </InfoItemValue>
          </InfoItemContainer>

          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.paymentType`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">
              {paymentType ?? '--'}
            </InfoItemValue>
          </InfoItemContainer>

          <InfoItemContainer>
            <InfoItemLabel>
              {t(`proposal:header.policyBenefitPeriod`)}
            </InfoItemLabel>
            <InfoItemValue fontWeight="bold">
              {`${policyBenefitPeriod ?? '--'} ${t('years')}`}
            </InfoItemValue>
          </InfoItemContainer>

          {deathBenefitOption && (
            <InfoItemContainer>
              <InfoItemLabel>
                {t(`proposal:header.deathBenefitOption`)}
              </InfoItemLabel>
              <InfoItemValue fontWeight="bold">
                {t(`proposal:deathBenefitOption.${deathBenefitOption}`)}
              </InfoItemValue>
            </InfoItemContainer>
          )}

          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.policyOwner`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">{ownerName}</InfoItemValue>
          </InfoItemContainer>

          <InfoItemContainer>
            <InfoItemLabel>{t(`proposal:header.policyInsured`)}</InfoItemLabel>
            <InfoItemValue fontWeight="bold">{insuredName}</InfoItemValue>
          </InfoItemContainer>
        </InfoContainer>
      </Animated.View>
    </Animated.View>
  );
};

export default StickyHeader;
