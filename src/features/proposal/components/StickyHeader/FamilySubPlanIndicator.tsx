import { useTheme } from '@emotion/react';
import { PictogramIcon, Row, Typography } from 'cube-ui-components';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import React from 'react';
import { useTranslation } from 'react-i18next';

const FamilySubPlanIndicator = () => {
  const primaryPlanPolicyNumber = useFnaStore(
    state => state.familySharePlanPolicyNumber,
  );

  const { space, colors, borderRadius } = useTheme();

  const { t } = useTranslation(['proposal']);

  // no policy number means new primary plan
  if (!primaryPlanPolicyNumber) {
    return null;
  }

  return (
    <Row
      marginTop={space[4]}
      marginRight={space[5]}
      alignItems="center"
      paddingX={space[3]}
      paddingY={space[2]}
      borderRadius={borderRadius.medium}
      backgroundColor={colors.palette.fwdOrange[20]}
      gap={space[1]}>
      <PictogramIcon.DocumentLinked height={space[7]} width={space[7]} />

      <Typography.Body>
        {`${t('proposal:basicPlan.planAssociatedWith')} `}
        <Typography.Body fontWeight="bold">
          {primaryPlanPolicyNumber}
        </Typography.Body>
      </Typography.Body>
    </Row>
  );
};

export default FamilySubPlanIndicator;
