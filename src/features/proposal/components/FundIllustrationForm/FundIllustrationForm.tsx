import React, { useState } from 'react';
import { Pressable, View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Input from 'components/Input';
import FundIllustrationFormHeader from './FundIllustrationFormHeader';
import FundIllustrationFormExitModal from './FundIllustrationFormExitModal';
import {
  Button,
  CurrencyTextField,
  H7,
  Icon,
  Row,
  TextField,
  Typography,
  XView,
} from 'cube-ui-components';
import {
  InitialFundIllustrationFormData,
  checkIsRawAlteration,
  parsingFormData,
  parsingPremiumExtension,
  responseToExtensionFormValue,
  responseToTopUpWithdrawFormValue,
} from './fundIllustrationFormUtils';
import { useTranslation } from 'react-i18next';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFundIllustrationForm } from './useFundIllustrationForm';
import useBoundStore from 'hooks/useBoundStore';
import { Alterations, Currency } from 'types/quotation';
import { shallow } from 'zustand/shallow';
import FundIllustrationYearInput from './FundIllustrationYearInput';

const FORM_RESET_VALUES = {
  withdrawal: [
    {
      policyYearFrom: undefined,
      policyYearTo: undefined,
      amount: undefined,
    },
  ],
  adhocTopUps: [
    {
      policyYearFrom: undefined,
      policyYearTo: undefined,
      amount: undefined,
    },
  ],
  premiumExtension: [
    {
      numberOfYears: undefined,
    },
  ],
};

export default function FundIllustrationForm() {
  const insets = useSafeAreaInsets();
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation(['common', 'proposal']);
  const { canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const [modalVisible, setModalVisible] = useState<boolean>(false);

  // Check show premium extension
  const { isVul, productCode, alterations, currency } = useBoundStore(
    state => ({
      isVul: state.quotation?.isVul,
      productCode: state.quotation?.values?.basePlan?.pid,
      alterations: state.quotation?.alterations,
      currency: state.quotation?.basicInfo?.currency,
    }),
    shallow,
  );
  const { fundIllustrationFormSchema } = useFundIllustrationForm({
    currency,
  });

  const isRawAlteration = checkIsRawAlteration(alterations);
  const isManifestProduct = [
    'UHE1',
    'UHE2',
    'FWDM',
    'ULL1',
    'ULL2',
    'ULI1',
    'ULI2',
    'LPVL',
  ].includes(productCode ?? '');
  const hasPremiumExtension = isVul && isManifestProduct;

  const updateAlterations = useBoundStore(
    state => state.quotationActions.updateAlterations,
  );

  // React hook form
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty, errors },
  } = useForm<InitialFundIllustrationFormData>({
    defaultValues: isRawAlteration
      ? FORM_RESET_VALUES
      : async () => ({
          withdrawal: responseToTopUpWithdrawFormValue(alterations?.withdrawal),
          adhocTopUps: responseToTopUpWithdrawFormValue(alterations?.lumpSum),
          premiumExtension: responseToExtensionFormValue(
            alterations?.premiumExtension,
          ),
        }),
    resolver: yupResolver(fundIllustrationFormSchema),
  });

  const {
    fields: withdrawalFields,
    append: withdrawalAppend,
    remove: withdrawalRemove,
  } = useFieldArray({
    control,
    name: 'withdrawal',
  });

  const {
    fields: adhocTopUpsFields,
    append: adhocTopUpsAppend,
    remove: adhocTopUpsRemove,
  } = useFieldArray({
    control,
    name: 'adhocTopUps',
  });

  const {
    fields: premiumExtensionFields,
    append: premiumExtensionAppend,
    remove: premiumExtensionRemove,
  } = useFieldArray({
    control,
    name: 'premiumExtension',
  });

  const onSubmit = (data: InitialFundIllustrationFormData) => {
    const { withdrawal, adhocTopUps, premiumExtension } = data;

    const submissionObj: Alterations = {
      withdrawal: parsingFormData(withdrawal),
      lumpSum: parsingFormData(adhocTopUps),
    };

    if (hasPremiumExtension) {
      submissionObj.premiumExtension =
        parsingPremiumExtension(premiumExtension);
    }

    updateAlterations(submissionObj);

    canGoBack() && goBack();
  };

  return (
    <>
      <Container
        style={{
          paddingLeft: space[12],
          paddingRight: insets.top > 0 ? insets.top : space[12],
        }}>
        <StatusBar hidden />

        <FundIllustrationFormHeader
          onReset={() => reset(FORM_RESET_VALUES)}
          onSave={handleSubmit(data =>
            onSubmit(data as InitialFundIllustrationFormData),
          )}
          formIsDirty={isDirty}
          showExitModal={() => setModalVisible(true)}
        />

        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          style={{ backgroundColor: colors.background }}>
          {/* // ! Withdrawal (-)  */}
          <>
            <AlignedXView style={{ marginBottom: space[4] }}>
              <View>
                <H7 fontWeight="bold">
                  {t('proposal:fundIllForm.subtitle.withdrawal')}
                </H7>
              </View>
              <Button
                variant="secondary"
                text={t('common:add')}
                contentStyle={{
                  paddingVertical: space[2],
                  paddingHorizontal: space[4],
                }}
                onPress={() =>
                  withdrawalAppend({
                    policyYearFrom: undefined,
                    policyYearTo: undefined,
                    amount: undefined,
                  })
                }
              />
            </AlignedXView>

            <View style={{ gap: space[4] }}>
              {withdrawalFields.map((field, index) => {
                return (
                  <Row key={field.id} style={{ gap: space[2] }}>
                    <RowIndex fontWeight="bold">{index + 1}</RowIndex>
                    <FundIllustrationYearInput
                      name={`withdrawal.${index}.policyYearFrom`}
                      label={t('proposal:fundIllForm.policyYearForm')}
                      control={control}
                      error={
                        errors?.withdrawal?.[index]?.policyYearFrom?.message
                      }
                      style={{ flex: 1 }}
                    />

                    <FundIllustrationYearInput
                      name={`withdrawal.${index}.policyYearTo`}
                      label={t('proposal:fundIllForm.policyYearTo')}
                      control={control}
                      error={errors?.withdrawal?.[index]?.policyYearTo?.message}
                      style={{ flex: 1 }}
                    />

                    <Input
                      name={`withdrawal.${index}.amount`}
                      label={`${t(
                        'proposal:fundIllForm.amount',
                      )} (${currency})`}
                      control={control}
                      as={CurrencyTextField}
                      error={errors?.withdrawal?.[index]?.amount?.message}
                      keyboardType="number-pad"
                      disableFullscreenUI={true}
                      style={{ flex: 2 }}
                    />

                    <DeleteButtonContainer
                      onPress={() => withdrawalRemove(index)}>
                      <Icon.Delete
                        size={sizes[6]}
                        fill={colors.palette.fwdGreyDark}
                      />
                    </DeleteButtonContainer>
                  </Row>
                );
              })}
            </View>
          </>
          <SectionSeparator />
          {/* // ! Adhoc top-ups (+) */}
          <>
            <AlignedXView style={{ marginBottom: space[4] }}>
              <View>
                <H7 fontWeight="bold" style={{ marginBottom: space[1] }}>
                  {t('proposal:fundIllForm.subtitle.adhocTopUps')}
                </H7>
                {/* <Typography.SmallLabel color={colors.palette.fwdGreyDarkest}>
                  {t('proposal:fundIllForm.yearRangeHints', {
                    from: 1,
                    to: maxYear,
                  })}
                </Typography.SmallLabel> */}
              </View>
              <Button
                variant="secondary"
                text={t('common:add')}
                contentStyle={{
                  paddingVertical: space[2],
                  paddingHorizontal: space[4],
                }}
                onPress={() =>
                  adhocTopUpsAppend({
                    policyYearFrom: undefined,
                    policyYearTo: undefined,
                    amount: undefined,
                  })
                }
              />
            </AlignedXView>

            <View style={{ gap: space[4] }}>
              {adhocTopUpsFields.map((field, index) => {
                return (
                  <Row key={field.id} style={{ gap: space[2] }}>
                    <RowIndex fontWeight="bold">{index + 1}</RowIndex>

                    <FundIllustrationYearInput
                      name={`adhocTopUps.${index}.policyYearFrom`}
                      label={t('proposal:fundIllForm.policyYearForm')}
                      control={control}
                      error={
                        errors?.adhocTopUps?.[index]?.policyYearFrom?.message
                      }
                      style={{ flex: 1 }}
                    />

                    <FundIllustrationYearInput
                      name={`adhocTopUps.${index}.policyYearTo`}
                      label={t('proposal:fundIllForm.policyYearTo')}
                      control={control}
                      error={
                        errors?.adhocTopUps?.[index]?.policyYearTo?.message
                      }
                      style={{ flex: 1 }}
                    />
                    <Input
                      name={`adhocTopUps.${index}.amount`}
                      label={`${t(
                        'proposal:fundIllForm.amount',
                      )} (${currency})`}
                      control={control}
                      as={CurrencyTextField}
                      error={errors?.adhocTopUps?.[index]?.amount?.message}
                      keyboardType="number-pad"
                      disableFullscreenUI={true}
                      style={{ flex: 2 }}
                    />

                    <DeleteButtonContainer
                      onPress={() => adhocTopUpsRemove(index)}>
                      <Icon.Delete
                        size={sizes[6]}
                        fill={colors.palette.fwdGreyDark}
                      />
                    </DeleteButtonContainer>
                  </Row>
                );
              })}
            </View>
          </>
          {hasPremiumExtension && <SectionSeparator />}
          {/* // ! Premium extension */}
          {hasPremiumExtension && (
            <>
              <AlignedXView style={{ marginBottom: space[4] }}>
                <H7 fontWeight="bold">
                  {t('proposal:fundIllForm.subtitle.premiumExtension')}
                </H7>
                <Button
                  variant="secondary"
                  text={t('common:add')}
                  contentStyle={{
                    paddingVertical: space[2],
                    paddingHorizontal: space[4],
                  }}
                  onPress={() =>
                    premiumExtensionAppend({
                      numberOfYears: undefined,
                    })
                  }
                />
              </AlignedXView>

              <View style={{ gap: space[4] }}>
                {premiumExtensionFields.map((field, index) => {
                  return (
                    <Row key={field.id} style={{ gap: space[2] }}>
                      <RowIndex fontWeight="bold">{index + 1}</RowIndex>

                      <FundIllustrationYearInput
                        name={`premiumExtension.${index}.numberOfYears`}
                        label={t('proposal:fundIllForm.numberOfYears')}
                        control={control}
                        error={
                          errors?.premiumExtension?.[index]?.numberOfYears
                            ?.message
                        }
                        style={{ flex: 1 }}
                      />

                      <View style={{ flex: 1 }} />
                      <View style={{ flex: 2 }} />

                      <DeleteButtonContainer
                        onPress={() => premiumExtensionRemove(index)}>
                        <Icon.Delete
                          size={sizes[6]}
                          fill={colors.palette.fwdGreyDark}
                        />
                      </DeleteButtonContainer>
                    </Row>
                  );
                })}
              </View>
            </>
          )}

          <EmptyBottomView />
        </KeyboardAwareScrollView>
      </Container>

      <FundIllustrationFormExitModal
        modalVisible={modalVisible}
        onSave={handleSubmit(data =>
          onSubmit(data as InitialFundIllustrationFormData),
        )}
        onDismiss={() => setModalVisible(false)}
      />
    </>
  );
}

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const AlignedXView = styled(XView)(() => ({
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const SectionSeparator = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[100],
  width: '100%',
  height: 1,
  marginVertical: theme.space[4],
}));

const EmptyBottomView = styled.View(({ theme }) => ({
  padding: theme.space[6],
}));

const RowIndex = styled(H7)(({ theme }) => ({
  marginRight: theme.space[2],
  marginTop: theme.space[4], // For adjusting display height
}));

const DeleteButtonContainer = styled(Pressable)(({ theme }) => ({
  marginTop: theme.space[4], // For adjusting display height
}));
