import {
  AlterationExtensionField,
  AlterationTopUpWithdrawalField,
} from 'types/quotation';

// * Check if alteration === {}
export function checkIsRawAlteration(alteration?: object) {
  if (!alteration) return;
  return Object.keys(alteration).length === 0;
}

// * For withdrawal && adhoc top-up
export interface TopUpAndWithdrawalInputItem {
  amount?: number;
  policyYearFrom?: number;
  policyYearTo?: number;
}

export function parsingFormData(input: TopUpAndWithdrawalInputItem[]) {
  const output: AlterationTopUpWithdrawalField = {};

  input.forEach(({ amount, policyYearFrom, policyYearTo }) => {
    if (!amount || !policyYearFrom || !policyYearTo) return;

    for (let year = policyYearFrom; year <= policyYearTo; year++) {
      if (output[year]) {
        output[year] += amount;
      } else {
        output[year] = amount;
      }
    }
  });

  return output;
}

export function responseToTopUpWithdrawFormValue(
  response?: AlterationTopUpWithdrawalField,
) {
  const formValue: TopUpAndWithdrawalInputItem[] = [];

  let current: TopUpAndWithdrawalInputItem | undefined;
  for (const [key, value] of Object.entries(response ?? {})) {
    if (!current) {
      current = {
        amount: value,
        policyYearFrom: parseInt(key),
        policyYearTo: parseInt(key),
      };
      continue;
    }

    // extend the year or push the current to the formValue
    if (current.amount === value) {
      current.policyYearTo = parseInt(key);
    } else {
      formValue.push(current);
      current = {
        amount: value,
        policyYearFrom: parseInt(key),
        policyYearTo: parseInt(key),
      };
    }
  }
  if (current) {
    formValue.push(current);
  }

  return formValue;
}

// * For premium extension
export interface PremiumExtensionInputItem {
  numberOfYears?: number;
}

export function parsingPremiumExtension(input: PremiumExtensionInputItem[]) {
  const output: AlterationExtensionField = {};

  input.forEach(({ numberOfYears }) => {
    if (!numberOfYears) return;
    output[numberOfYears] = true;
  });

  return output;
}

export function responseToExtensionFormValue(
  response?: AlterationExtensionField,
) {
  const formValue: PremiumExtensionInputItem[] = [];
  // the key is the year, add the key if the value(boolean type) is true
  for (const [key, value] of Object.entries(response ?? {})) {
    if (value) {
      formValue.push({
        numberOfYears: parseInt(key),
      });
    }
  }
  return formValue;
}

// * Initial fund illustration form data
export interface InitialFundIllustrationFormData {
  withdrawal: TopUpAndWithdrawalInputItem[];
  adhocTopUps: TopUpAndWithdrawalInputItem[];
  premiumExtension: PremiumExtensionInputItem[];
}
