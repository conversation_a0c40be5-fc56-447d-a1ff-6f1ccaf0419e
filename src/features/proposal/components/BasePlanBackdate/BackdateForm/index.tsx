import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { SiFormValues } from 'features/proposal/types';
import React from 'react';
import { Control } from 'react-hook-form';
import BackdateFromPhone from './BackdateForm.phone';
import BackdateFormTablet from './BackdateForm.tablet';

export type BasePlanFormProps = {
  control: Control<SiFormValues>;
  minDate: string | undefined;
  maxDate: string | undefined;
};

const BackdateForm = (props: BasePlanFormProps) => {
  return (
    <DeviceBasedRendering
      phone={<BackdateFromPhone {...props} />}
      tablet={<BackdateFormTablet {...props} />}
    />
  );
};

export default BackdateForm;
