import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H6, H8, Icon, XView } from 'cube-ui-components';
import { t } from 'i18next';
import { Pressable, StatusBar } from 'react-native';
import { RowData } from './Table/Table';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import WithdrawalAndTopUpsButton from './WithdrawalAndTopUpsButton';

interface SimulationContainerProps {
  hasWithdrawalAndTopup?: boolean;
  children?: React.ReactNode | React.ReactNode[];
}

const SimulationContainer = ({
  children,
  hasWithdrawalAndTopup,
}: SimulationContainerProps) => {
  const { sizes, colors, space } = useTheme();
  const { navigate, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <>
      <Container>
        <StatusBar hidden />

        <TableContainer>
          <Column flex={1}>
            <HeaderContainer>
              <Pressable
                hitSlop={{
                  top: space[3],
                  bottom: space[3],
                  left: space[3],
                  right: space[3],
                }}
                onPress={goBack}>
                <Icon.Close size={sizes[5]} fill={colors.secondary} />
              </Pressable>
              <H6 fontWeight="bold">{t('proposal:simulation.table')}</H6>
            </HeaderContainer>

            <HelperText>{t('proposal:illustration.allValue')}</HelperText>

            {children}
          </Column>
        </TableContainer>
      </Container>
      {hasWithdrawalAndTopup && (
        <WithdrawalAndTopUpsButton
          onPress={() => navigate('FundIllustrationForm')}
        />
      )}
    </>
  );
};

export default SimulationContainer;

export const mappingDataTables = (
  convertDataTable: number[][],
  freezedIndex: number,
): RowData[] => {
  const keys = Object.keys(convertDataTable);

  if (keys.length === 0) {
    return [];
  }

  const maxLength = Math.max(
    ...keys.map((_, idx) =>
      convertDataTable[idx] ? convertDataTable[idx].length : 0,
    ),
  );

  if (!Number.isFinite(maxLength) || maxLength <= 0) {
    return [];
  }

  const newArray = Array.from({ length: maxLength }, (_, index) => {
    const data = keys.map((_, idx) =>
      idx < freezedIndex
        ? ''
        : (convertDataTable[idx] && convertDataTable[idx][index])?.toString() ??
          '',
    );
    const origin =
      (convertDataTable[0] && convertDataTable[0][index])?.toString() || '';
    return { data, origin };
  });

  return newArray;
};

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const TableContainer = styled.View(() => ({
  flex: 1,
  flexDirection: 'row',
}));

const HeaderContainer = styled(XView)(({ theme }) => ({
  paddingTop: theme.space[4] + 1, // For adjusting display height
  paddingLeft: theme.space[12],
  alignItems: 'center',
  gap: theme.space[5],
}));

const HelperText = styled(H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  paddingLeft: theme.space[12],
  paddingBottom: theme.space[2],
  paddingTop: theme.space[4],
}));
