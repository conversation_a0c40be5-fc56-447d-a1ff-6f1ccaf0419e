import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import Cell, {
  CellProps,
} from 'features/savedProposals/proposalTable/components/Cell';
import { memo } from 'react';
import { View } from 'react-native';

function FreezeTextCell({ text, ...rest }: { text: string } & CellProps) {
  return (
    <Cell {...rest}>
      <Container>
        <TypographyH7 fontWeight="medium">{text}</TypographyH7>
      </Container>
    </Cell>
  );
}

export default memo(FreezeTextCell);

const Container = styled(View)(() => ({ width: '100%' }));
const TypographyH7 = styled(Typography.H7)(() => ({ textAlign: 'center' }));
