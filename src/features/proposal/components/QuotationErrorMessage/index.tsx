import React from 'react';
import { View, Text, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

type QuotationErrorMessageProps = {
  message: string;
};

const QuotationErrorMessage: React.FC<QuotationErrorMessageProps> = ({
  message,
}) => {
  const { t } = useTranslation(['proposal']);
  const theme = useTheme();

  return (
    <StyledContainer>
      <Icon.InfoCircle size={24} fill={theme.colors.error} />
      <StyledErrorMessage>{message}</StyledErrorMessage>
    </StyledContainer>
  );
};

export default QuotationErrorMessage;

// StyledContainer
const StyledContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[20],
  padding: theme.space[3],
  flexDirection: 'row',
  alignItems: 'center',
  borderBottomColor: theme.colors.palette.fwdGrey[100],
  borderBottomWidth: 1,
}));

const StyledErrorMessage = styled.Text(({ theme }) => ({
  color: theme.colors.error,
  marginLeft: theme.space[2],
}));
