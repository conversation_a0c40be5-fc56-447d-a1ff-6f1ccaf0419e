import { useTheme } from '@emotion/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Svg, {
  SvgProps,
  G,
  Rect,
  Text,
  Path,
  Circle,
  Defs,
  ClipPath,
} from 'react-native-svg';

type PreviewPdfButtonProps = SvgProps & {
  variant?: 'disabled' | 'error' | 'success';
};

const PreviewPdfButton = ({ variant, ...props }: PreviewPdfButtonProps) => {
  const { colors } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const bgColor = useMemo(() => {
    switch (variant) {
      case 'disabled':
        return colors.palette.fwdGreyDark;
      case 'error':
        return colors.error;
      case 'success':
      default:
        return colors.primary;
    }
  }, [colors, variant]);

  const title = useMemo(() => {
    switch (variant) {
      case 'disabled':
        return t('proposal:footer.previewPdf.title.disabled');
      case 'error':
        return t('proposal:footer.previewPdf.title.error');
      case 'success':
      default:
        return t('proposal:footer.previewPdf.title.success');
    }
  }, [variant, t]);

  return (
    <Svg width={163} height={108} viewBox="0 0 163 108" fill="none" {...props}>
      <G clipPath="url(#a)">
        <Rect width={155} height={229} x={6} y={4} fill={bgColor} rx={8} />
        <Text x="54%" y={20} fill="#fff" textAnchor="middle" fontWeight="bold">
          {title}
        </Text>
      </G>
      <Path fill="#fff" d="M8 34a8 8 0 0 1 8-8h135a8 8 0 0 1 8 8v75H8V34Z" />
      <Path
        fill="#E87722"
        d="M141.704 38.329a.313.313 0 0 1 0 .454l-1.659 1.57a.355.355 0 0 1-.37.07.321.321 0 0 1-.208-.297v-3.14c0-.13.082-.247.208-.298a.357.357 0 0 1 .371.07l1.658 1.57Z"
      />
      <Path
        fill="#183028"
        d="M131.303 34.227a1.31 1.31 0 0 0-.424.542 8.816 8.816 0 0 0-.294.913l-.087.314-1.079 4.05-1.256-5.181c-.107-.495-.398-.8-1.117-.8H119.367c-.353-.001-.366.354-.367.358v8.395s.006.179.186.179h.714c.512 0 1.167-.316 1.167-1.186V39.253h2.689c1.026 0 1.276-.699 1.276-1.097v-.147c0-.141-.05-.227-.229-.227H121.067v-2.118h5.282l1.594 5.86c.049.176.328 1.104.328 1.104.108.325.096.372.386.372h.645c.794 0 1.068-.467 1.208-.796.071-.178.12-.257.234-.66l1.405-5.052 1.487 5.027.308 1.107c.107.324.096.373.386.373h.645c.795 0 1.069-.467 1.209-.798.069-.178.138-.35.252-.751l1.461-5.78h4.525c.389 0 .702.032.985.113.365.105.689.365.959.774.263.401.412 1.103.419 1.974 0 .983-.308 1.855-.88 2.326-.127.11-.276.195-.453.257a2.209 2.209 0 0 1-.521.12c-.179.017-.414.052-.725.055h-.004l-.987.001h-1.418l-.007-.001c-.307 0-.316.17-.316.243v.351c0 .512.442 1.1 1.33 1.1h.03l1.723-.001a7.86 7.86 0 0 0 1.184-.093c.355-.059.688-.156.999-.296.31-.14.597-.324.858-.552.33-.295.6-.63.809-1.003.208-.373.361-.79.457-1.247.091-.433.136-.74.136-1.254v-.01c-.015-1.662-.529-2.843-1.554-3.667a3.237 3.237 0 0 0-1.321-.662 6.929 6.929 0 0 0-1.597-.162h-5.482c-.301.003-.752.151-.926.827l-1.189 5.148-1.264-4.036v.003l-.085-.297c-.122-.43-.22-.739-.293-.926a1.316 1.316 0 0 0-.393-.518c-.188-.158-.454-.237-.794-.237-.338 0-.603.076-.799.227Z"
      />
      <Rect width={90} height={4} x={38} y={51} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={60} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={68} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={76} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={84} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={92} fill="#DBDFE1" rx={1} />
      <Rect width={128} height={4} x={19} y={100} fill="#DBDFE1" rx={1} />

      {variant === 'error' && (
        <>
          <Circle cx={83} cy={66} r={16} fill="#fff" />
          <Path
            fill={bgColor}
            fillRule="evenodd"
            d="M83 82c8.837 0 16-7.163 16-16s-7.163-16-16-16-16 7.163-16 16 7.163 16 16 16Zm-1.554-23.62c0-.209.175-.38.39-.38h2.332c.214 0 .389.171.389.38v8.763c0 .21-.175.38-.389.38h-2.333a.386.386 0 0 1-.389-.38V58.38Zm1.556 16.763a2.357 2.357 0 0 1-1.62-.686 2.262 2.262 0 0 1-.666-1.6c0-.598.24-1.172.666-1.6a2.357 2.357 0 0 1 1.62-.686c.61.013 1.191.259 1.619.686.427.427.666 1.002.666 1.6 0 .598-.239 1.173-.666 1.6a2.356 2.356 0 0 1-1.62.686Z"
            clipRule="evenodd"
          />
        </>
      )}

      <Defs>
        <ClipPath id="a">
          <Path fill="#fff" d="M6 4h155v105H6z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
export default PreviewPdfButton;
