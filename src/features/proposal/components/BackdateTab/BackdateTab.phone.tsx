import { useTheme } from '@emotion/react';
import React from 'react';
import { Pressable } from 'react-native';
import BackdateTabContent from '../BasePlanBackdate/BackdateTabContent';
import { BackdateTabProps } from '.';
import { useRegisterTab, useTabState } from '../TabContext';
import { useBackdate } from 'features/proposal/hooks/useBackdate';

const BackdateTab = ({ control }: BackdateTabProps) => {
  const { colors } = useTheme();
  useRegisterTab({
    tabKey: 'backdate',
  });
  const { onTabPress: onBasePlanTabPress } = useTabState('basePlan');
  const { isActive, onTabPress: onBackdateTabPress } = useTabState('backdate');
  const { isChecked, resetBackdate } = useBackdate(control);

  return (
    <Pressable
      style={{
        backgroundColor: isActive
          ? colors.palette.white
          : colors.palette.fwdOrange[20],
        flex: isActive ? 5 : 7,
      }}
      onPress={onBackdateTabPress}>
      <BackdateTabContent
        isActive={isActive}
        isChecked={isChecked}
        onCheckboxChange={v => {
          if (v) {
            // same as press on the Backdate tab
            onBackdateTabPress();
          } else {
            // force agent back to base plan tab
            resetBackdate();
            onBasePlanTabPress();
          }
        }}
      />
    </Pressable>
  );
};

export default BackdateTab;
