import DeviceBasedRendering from 'components/DeviceBasedRendering';
import BackdateTabPhone from './BackdateTab.phone';
import BackdateTabTablet from './BackdateTab.tablet';
import { PressableProps } from 'react-native';
import { SiFormValues } from 'features/proposal/types';
import { Control } from 'react-hook-form';

export type BackdateTabProps = PressableProps & {
  control: Control<SiFormValues>;
};

export default function BackdateTab(props: BackdateTabProps) {
  return (
    <DeviceBasedRendering
      phone={<BackdateTabPhone {...props} />}
      tablet={<BackdateTabTablet {...props} />}
    />
  );
}
