import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AddFundsModalPhone, { AddFundModalProps } from './AddFundsModal.phone';
import AddFundsModalTablet from './AddFundsModal.tablet';

export default function AddFundsModal(props: AddFundModalProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return isTabletMode ? (
    <AddFundsModalTablet {...props} />
  ) : (
    <AddFundsModalPhone {...props} />
  );
}
