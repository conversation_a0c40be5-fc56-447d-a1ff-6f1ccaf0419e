import React, { useCallback, useState } from 'react';
import { Linking, Pressable, View } from 'react-native';

import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import DialogPhone from 'components/Dialog.phone';
import {
  Box,
  Button,
  Card,
  Icon,
  LargeLabel,
  Typography,
  XView,
} from 'cube-ui-components';
import { useFieldArray, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { RootStackParamList } from 'types';

import styled from '@emotion/native';
import TextMore from 'components/TextMore';
import RpqRiskResultComponent from 'features/proposal/components/RpqQuestionForm/RpqRiskResultComponent';
import StepTitle from 'features/proposal/components/StepTitle';
import useFundsColorCode from 'features/proposal/hooks/fund/useFundsColorCode';
import useFundsDefinitions from 'features/proposal/hooks/useFundsDefinitions';
import { useGetRpqByLeadId } from 'hooks/useGetLeadFna';
import { StyleSheet } from 'react-native';
import ViewShot from 'react-native-view-shot';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { ListFundTitle } from '../../common';
import FundInputRow from '../../common/FundInputRow';
import FundsAllocationPieChart from '../../common/FundsAllocationPieChart';
import { usePieChartContext } from '../../common/PieChartContext';
import { PIE_CHART_SIZE_PHONE } from '../../constants';
import { FundsAllocationProps } from '../../types';
import AddFundsModal from 'features/proposal/components/AddFundsModal';
import { FundOption } from 'types/quotation';
import { updateSelectedFunds } from 'features/proposal/hooks/fund/useFundsAllocation';
import { countryModuleSiConfig } from 'utils/config/module';

const ViewFundFactSheetButton = styled.Pressable(({ theme: { space } }) => ({
  marginTop: space[5],
  alignSelf: 'flex-start',
}));

export default function FundsAllocation({
  step,
  isStartRPQ,
  leadId,
  setValue,
  control,
  clearErrors,
  maxFund,
  triggerValidation,
  containerStyle,
}: FundsAllocationProps) {
  const { t } = useTranslation(['common', 'proposal']);
  const { colors, space, typography } = useTheme();
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const [isReview, setReviewRisk] = useState(false);

  const { data: leadRpqData } = useGetRpqByLeadId(leadId);
  const [isShowModal, setIsShowModal] = useState(false);

  const { pieChartWrapperRef } = usePieChartContext();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'fundsAllocation',
  });

  const onAddFunds = (funds: FundOption[]) => {
    updateSelectedFunds(
      watchFundFieldArray,
      funds,
      append,
      remove,
      triggerValidation,
    );
  };

  const onRemoveFund = useCallback(
    (fid: string) => {
      const idx = fields.findIndex(f => f.fid === fid);
      if (idx !== -1) {
        remove(idx);
        triggerValidation();
      }
    },
    [fields, remove],
  );

  const watchFundFieldArray = useWatch({
    control,
    name: 'fundsAllocation',
  });

  const [selectedSlice, setSelectedSlice] = useState<number>(-1);

  const fundsDefinition = useFundsDefinitions();
  const fundFactSheet = fundsDefinition[0]?.fundFactSheet;

  const handleOnSliceSelected = useCallback(
    (pieceIndex: number) => {
      if (pieceIndex !== selectedSlice) {
        setSelectedSlice(pieceIndex);
        return;
      }
      setSelectedSlice(-1);
    },
    [selectedSlice, setSelectedSlice],
  );

  const { getColorCode } = useFundsColorCode();

  const onViewFundFactSheet = () => {
    if (fundFactSheet?.urlType === 'pdf') {
      navigate('PdfViewer', {
        url: fundFactSheet?.url?.en,
        title: t('proposal:funds.fundFactSheet'),
        fileName: t('proposal:funds.fundFactSheet'),
      });
    } else {
      const factSheetUrl = renderLabelByLanguage(fundFactSheet?.url);
      if (factSheetUrl) Linking.openURL(factSheetUrl);
    }
  };

  return (
    <>
      <DialogPhone visible={isReview}>
        <View
          style={{
            alignItems: 'flex-end',
            // marginTop: sizes[4]
          }}>
          <Pressable onPress={() => setReviewRisk(false)}>
            <Icon.Close size={24}></Icon.Close>
          </Pressable>
        </View>
        <KeyboardAwareScrollView>
          <RpqRiskResultComponent isReview={true} />
        </KeyboardAwareScrollView>
      </DialogPhone>
      <Card
        style={[
          {
            elevation: 1,
            padding: space[3],
            ...(isStartRPQ && {
              // height: sizes[42],
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }),
          },
          StyleSheet.flatten(containerStyle),
        ]}
        borderRadius="medium">
        <XView>
          <StepTitle
            step={step}
            titleText={`Funds (${fields.length})`}
            titleTextStyle={{ color: colors.palette.black, fontWeight: '700' }}
          />
          <Button
            text={`${t('common:add')}`}
            style={{
              marginLeft: 'auto',
            }}
            contentStyle={{ paddingHorizontal: space[5] }}
            onPress={() => setIsShowModal(true)}
            variant="secondary"
          />
        </XView>

        <ViewFundFactSheetButton onPress={onViewFundFactSheet}>
          <LargeLabel
            color={colors.palette.fwdAlternativeOrange[100]}
            fontWeight="bold">
            {t('proposal:rpq.viewFundFactsheet')}
          </LargeLabel>
        </ViewFundFactSheetButton>

        {fields.length > 0 && !isStartRPQ && (
          <>
            <ViewShot ref={pieChartWrapperRef}>
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: space[4],
                }}>
                <FundsAllocationPieChart
                  selectedIndex={selectedSlice}
                  funds={watchFundFieldArray}
                  size={PIE_CHART_SIZE_PHONE}
                  innerSize={PIE_CHART_SIZE_PHONE * 0.65}
                  onSlicePress={handleOnSliceSelected}
                />
              </View>
            </ViewShot>

            <Box paddingTop={space[6]}>
              <ListFundTitle fontWeight="bold">
                {`${t('proposal:rpq.addedFunds')} (${fields.length})`}
              </ListFundTitle>
            </Box>

            {fields?.map((fund, index) => (
              <FundInputRow
                key={fund.id}
                index={index}
                fund={fund}
                color={getColorCode(fund?.fid, index)}
                isLast={fields.length - 1 === index}
                control={control}
                setValue={setValue}
                clearErrors={clearErrors}
                isSelected={selectedSlice === index}
                onRemoveFund={onRemoveFund}
              />
            ))}

            <Box mt={space[6]}>
              <TextMore
                text={t('proposal:rpq.fundAllocationDescription')}
                numLines={4}
                style={{
                  color: colors.palette.fwdGreyDarker,
                  fontSize: typography.body.size,
                  lineHeight: typography.body.lineHeight,
                }}
                buttonTextStyle={{
                  fontSize: typography.body.size,
                  lineHeight: typography.body.lineHeight,
                }}
              />
            </Box>
          </>
        )}
      </Card>
      <AddFundsModal
        visible={isShowModal}
        maxFund={maxFund}
        onClose={() => setIsShowModal(false)}
        onAdd={onAddFunds}
      />
    </>
  );
}
