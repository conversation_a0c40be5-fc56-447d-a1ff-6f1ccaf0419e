import { Switch, Typography, Row, Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Control } from 'react-hook-form';
import { SiFormValues } from 'features/proposal/types';
import useFields from 'features/proposal/hooks/useFields';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { useTranslation } from 'react-i18next';
import useBasePlanDefinition from 'features/proposal/hooks/useBasePlanDefinition';
import { useEffect } from 'react';

type AutoFundBalanceOptionProps = {
  onChange?: (value: string) => void;
  onBlur?: () => void;
  value?: string;
};

const AutoFundBalanceSwitch = ({
  value,
  onChange,
  onBlur,
}: AutoFundBalanceOptionProps) => {
  const { space, colors, borderRadius } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const autoBalanceOption = useFields(['autoBalanceOption']).get(
    'autoBalanceOption',
  );

  const title = t('proposal:funds.autoFundBalancingOption');

  const selectedOption = (autoBalanceOption?.options || []).find(
    item => item?.value === value,
  );
  const label = selectedOption?.label
    ? renderLabelByLanguage(selectedOption?.label)
    : t('proposal:rider.switch.no');

  const handleChange = (checked: boolean) => {
    onChange?.(checked ? 'Y' : 'N');
    onBlur?.();
  };

  useEffect(() => {
    if (onChange && !value) {
      onChange?.('N');
    }
  }, [onChange]);

  return (
    <Row
      justifyContent="space-between"
      alignItems="center"
      p={space[4]}
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}
      borderRadius={borderRadius['x-small']}>
      <Typography.Label fontWeight="bold">{title}</Typography.Label>

      <Switch
        checked={value === 'Y'}
        onChange={handleChange}
        label={label as string}
      />
    </Row>
  );
};

const AutoFundBalanceOption = ({
  control,
}: {
  control: Control<SiFormValues>;
}) => {
  const { space } = useTheme();

  const basePlanDefinition = useBasePlanDefinition();

  const basicConfig = basePlanDefinition?.basicConfig;

  if (basicConfig && 'autoBalanceOption' in basicConfig) {
    return (
      <Box pt={space[4]}>
        <Input
          control={control}
          as={AutoFundBalanceSwitch}
          name="basePlan.autoBalanceOption"
        />
      </Box>
    );
  }

  return null;
};

export default AutoFundBalanceOption;
