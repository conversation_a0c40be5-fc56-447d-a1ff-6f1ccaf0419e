import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Picker, Row, Typography } from 'cube-ui-components';
import { LinearGradient } from 'expo-linear-gradient';
import RiskArrowDownSVG from 'features/proposal/assets/RiskArrowDownSVG';
import { useSIFormSummary } from 'features/proposal/components/SalesIllustrationDetailsModal/useSIFormSummary';
import useFundOptions from 'features/proposal/hooks/fund/useFundOptions';
import useFundsColorCode from 'features/proposal/hooks/fund/useFundsColorCode';
import useBasePlanInfo, {
  useBasePlanCurrency,
} from 'features/proposal/hooks/useBasePlanInfo';
import { FundStrategy, SiFormValues } from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import React, { useEffect, useMemo } from 'react';
import { UseFieldArrayReplace } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { DimensionValue, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { TranslationField } from 'types';
import { Currency } from 'types/quotation';

type Props = {
  replace: UseFieldArrayReplace<SiFormValues, 'fundsAllocation'>;
  triggerValidation: () => void;
};

const FundStrategyPicker = ({ replace, triggerValidation }: Props) => {
  const { space } = useTheme();
  const { t } = useTranslation(['proposal']);
  const currency = useBasePlanCurrency();
  const basePlanInfo = useBasePlanInfo();

  const { funds } = useSIFormSummary();

  const currentRiskLevel = funds[0]?.riskLevel ?? '';
  const { getColorCode } = useFundsColorCode();

  const selectedStrategy = useBoundStore(
    state => state.quotation?.selectedFundStrategy ?? currentRiskLevel,
  );
  const setSelectedStrategy = useBoundStore(
    state => state.quotationActions.updateFundStrategy,
  );
  const subscribeFundStrategy = useBoundStore(
    state => state.quotationActions.subscribeFundStrategy,
  );

  const { fundStrategyOptions, fundsByPid } = useFundOptions({
    pid: basePlanInfo?.productId,
  });

  const getItemLabel = (value: string | undefined) => {
    return fundStrategyOptions?.find(i => i.value === value)?.label;
  };

  const calculateArrowPosition = (value: string): DimensionValue => {
    const selectedIndex = fundStrategyOptions?.findIndex(
      i => i.value === value,
    );

    return selectedIndex !== -1 ? `${(selectedIndex + 1) * 20 - 12.5}%` : '5%';
  };

  const onStrategyChanged = (value?: FundStrategy) => {
    const fundsByStrategy =
      (fundsByPid ?? [])
        ?.filter(fund => fund.value === value && fund.selected)
        ?.map((fund, index) => {
          return {
            fundName: fund.label,
            underlyingFundName: fund.label as TranslationField,
            fid: fund.fid,
            allocation: fund.default || 0,
            currency: currency as Currency,
            min: fund.min,
            max: fund.max,
            colorCode: getColorCode(fund.fid, index),
            riskLevel: fund.value,
            disabled: fund.disabled as unknown as boolean,
          };
        }) || [];

    replace(fundsByStrategy);

    triggerValidation();
  };

  useEffect(() => {
    const unsubscribe = subscribeFundStrategy(onStrategyChanged);
    return unsubscribe;
  }, []);

  const handleOnItemSelected = (value: FundStrategy) => {
    setSelectedStrategy(value);
    onStrategyChanged(value);
  };

  const pottentialTextPosition = useMemo(() => {
    if (!fundStrategyOptions) return 'center';

    const itemIndex = fundStrategyOptions?.findIndex(
      i => i.value === selectedStrategy,
    );

    if (itemIndex < (fundStrategyOptions?.length - 1) / 2) {
      return 'left';
    } else if (itemIndex > (fundStrategyOptions?.length - 1) / 2) {
      return 'right';
    } else {
      return 'center';
    }
  }, [fundStrategyOptions, selectedStrategy]);

  return (
    <View>
      <SelectStrategyLabel>
        {t('proposal:funds.selectStrategy')}
      </SelectStrategyLabel>
      <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
        <Picker
          type="chip"
          size="large"
          items={fundStrategyOptions}
          value={selectedStrategy}
          onChange={value => handleOnItemSelected(value as FundStrategy)}
        />
      </ScrollView>

      <Row marginTop={space[8]} alignItems="center">
        {selectedStrategy && (
          <ArrowIconContainer left={calculateArrowPosition(selectedStrategy)}>
            <RiskArrowDownSVG />
          </ArrowIconContainer>
        )}

        <PottentialBar
          colors={['#E2F5F0', '#FF9B0A']}
          locations={[0.01, 0.95]}
          end={{ x: 0.9, y: 1 }}
          start={{ x: 0.1, y: 0.1 }}>
          {selectedStrategy ? (
            <PottentialText
              textAlignPosition={pottentialTextPosition}
              fontWeight="medium">
              {t('proposal:funds.dynamicPottential', {
                type: getItemLabel(selectedStrategy),
              })}
            </PottentialText>
          ) : (
            <>
              <PottentialText textAlignPosition={'left'} fontWeight="medium">
                {t('proposal:funds.dynamicPottential', {
                  type: getItemLabel(FundStrategy.LOW),
                })}
              </PottentialText>

              <PottentialText textAlignPosition={'right'} fontWeight="medium">
                {t('proposal:funds.dynamicPottential', {
                  type: getItemLabel(FundStrategy.HIGH),
                })}
              </PottentialText>
            </>
          )}
        </PottentialBar>
      </Row>
    </View>
  );
};

export default FundStrategyPicker;

const PottentialBar = styled(LinearGradient)(
  ({ theme: { space, borderRadius } }) => ({
    height: space[6],
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius['x-small'],
    gap: space[1],
    paddingHorizontal: space[4],
    flexGrow: 1,
  }),
);

const ArrowIconContainer = styled.View<{
  left: DimensionValue;
}>(({ theme: { space }, left = '5%' }) => ({
  position: 'absolute',
  top: -space[4],
  left: left,
  zIndex: 1,
}));

const PottentialText = styled(Typography.Label)<{
  textAlignPosition: 'left' | 'right' | 'center';
}>(({ textAlignPosition = 'left' }) => ({
  textAlign: textAlignPosition,
  flex: 1,
}));

const SelectStrategyLabel = styled(Typography.Label)(
  ({ theme: { space } }) => ({
    textAlign: 'left',
    marginBottom: space[4],
  }),
);
