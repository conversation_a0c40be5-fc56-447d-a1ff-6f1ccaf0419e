import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography, XView } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { chunk } from 'utils';
import { ProductLogo, Props } from '.';
import InsuredItem from '../../InsuredItem';
import { CommonDataItem } from '../components/CommonDataItem';
import { useBasePlanFieldConfig } from 'features/proposal/hooks/useFields';

const BasePlanSection = ({
  productName,
  currency,
  insureds,
  image: { productLogoWidth, productLogoHeight, imgUrl },
  basePlanFields: {
    sumAssured,
    modalPremium,
    paymentMode,
    policyTerm,
    maxPolicyTerm,
    premiumType,
    paymentType,
    premiumTerm,
    annualTerm,
    maxPremiumTerm,
    totalAnnualPrem,
    totalPrem,
    totalInitialPrem,
    basePrem,
    annualPrem,
    gcpOption,
    deathBenefitOption,
    saverAnnualPrem,
    contributionTerm,
    regularPayout,
    customerSegment,
    classes,
    planOption,
  },
}: Props) => {
  const { space, borderRadius, colors } = useTheme();
  const { t } = useTranslation(['proposal', 'common']);
  const { getConfigLabel } = useBasePlanFieldConfig();

  return (
    <Box
      flex={1}
      borderRadius={borderRadius.large}
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}
      mt={space[4]}>
      <Column px={space[4]} pt={space[4]} pb={space[2]} flex={1}>
        <Typography.ExtraLargeBody fontWeight="bold">
          {productName}
        </Typography.ExtraLargeBody>
        <Row mt={space[3]} mb={space[4]} flex={1}>
          <Column flex={1}>
            <Typography.Label
              fontWeight="bold"
              color={colors.palette.fwdGreyDarker}>
              {sumAssured.label || t('proposal:basicPlan.sumAssured')}
            </Typography.Label>
            <XView style={{ marginVertical: space[1], marginRight: space[2] }}>
              <Typography.SmallLabel
                color={colors.primary}
                style={{ marginTop: space[1], marginRight: space[1] }}>
                {currency}
              </Typography.SmallLabel>
              <Typography.H6 fontWeight="bold" color={colors.primary}>
                {sumAssured.value}
              </Typography.H6>
            </XView>
          </Column>
          <Column flex={1}>
            <Typography.Label
              fontWeight="bold"
              color={colors.palette.fwdGreyDarker}>
              {t('proposal:basicPlan.basePrem')}
            </Typography.Label>
            <XView style={{ marginVertical: space[1], marginRight: space[2] }}>
              <Typography.SmallLabel
                color={colors.palette.fwdBlue[100]}
                style={{ marginTop: space[1], marginRight: space[1] }}>
                {currency}
              </Typography.SmallLabel>
              <Typography.H6
                fontWeight="bold"
                color={colors.palette.fwdBlue[100]}>
                {modalPremium.value}
              </Typography.H6>
            </XView>
          </Column>
        </Row>
        <Row>
          <Column flex={1}>
            <ProductLogo
              source={{ uri: imgUrl }}
              width={productLogoWidth}
              height={productLogoHeight}
            />
          </Column>
        </Row>

        <Row mt={space[4]} flex={1}>
          <Column flex={1}>
            <Typography.LargeLabel
              fontWeight="bold"
              color={colors.palette.fwdDarkGreen[100]}>
              {t('proposal:salesIllustrationDetail.applicationDetail')}
            </Typography.LargeLabel>

            <Row marginTop={space[4]} flexWrap="wrap" flex={1}>
              {classes.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.classes')}
                  value={classes.value}
                />
              )}

              {planOption.isShow && (
                <CommonDataItem
                  label={
                    planOption?.label ?? t('proposal:basicPlan.planOption')
                  }
                  value={planOption.value}
                />
              )}

              <CommonDataItem
                label={t('proposal:basicPlan.paymentMode')}
                value={paymentMode.value}
              />

              {policyTerm.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.policyTerm')}
                  value={policyTerm.value}
                />
              )}

              {maxPolicyTerm.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.policyTerm')}
                  value={maxPolicyTerm.value}
                />
              )}

              {premiumType.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.paymentType')}
                  value={premiumType.value}
                />
              )}

              {paymentType.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.paymentType')}
                  value={paymentType.value}
                />
              )}

              {premiumTerm.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.premiumTerm')}
                  value={premiumTerm.value}
                />
              )}

              {maxPremiumTerm.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.premiumTerm')}
                  value={maxPremiumTerm.value}
                />
              )}

              {totalAnnualPrem.isShow && (
                <CommonDataItem
                  label={
                    totalAnnualPrem.label ?? t('proposal:basicPlan.annualPrem')
                  }
                  value={totalAnnualPrem.value}
                />
              )}

              {totalPrem.isShow && (
                <CommonDataItem
                  label={totalPrem.label ?? t('proposal:basicPlan.totalPrem')}
                  value={totalPrem.value}
                />
              )}

              {totalInitialPrem.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.totalPrem')}
                  value={totalInitialPrem.value}
                />
              )}

              {basePrem.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.basePrem')}
                  value={basePrem.value}
                />
              )}

              {annualPrem?.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.annualPrem')}
                  value={annualPrem.value}
                />
              )}

              {annualTerm?.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.annualPrem')}
                  value={annualTerm.value}
                />
              )}

              {saverAnnualPrem.isShow && (
                <CommonDataItem
                  label={
                    saverAnnualPrem?.label ??
                    t('proposal:basicPlan.saverAnnualPrem')
                  }
                  value={saverAnnualPrem.value}
                />
              )}

              {deathBenefitOption.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.deathBenefit')}
                  value={deathBenefitOption.value}
                />
              )}

              {contributionTerm.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.premiumTerm')}
                  value={contributionTerm.value}
                />
              )}

              {customerSegment.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.customerSegment')}
                  value={customerSegment.value}
                />
              )}

              {regularPayout.isShow && (
                <CommonDataItem
                  label={t('proposal:basicPlan.regularPayout')}
                  value={regularPayout.value}
                />
              )}

              {gcpOption.isShow && (
                <CommonDataItem
                  label={getConfigLabel('gcpOption') || t('proposal:basicPlan.gcpOption')}
                  value={gcpOption.value}
                />
              )}
            </Row>
          </Column>
        </Row>
      </Column>

      <Box
        py={space[4]}
        px={space[4]}
        bgColor={colors.palette.fwdGrey[20]}
        borderBottomLeftRadius={borderRadius.large}
        borderBottomRightRadius={borderRadius.large}>
        <Typography.Label
          color={colors.palette.fwdDarkGreen[50]}
          fontWeight="bold">
          {t('proposal:rider.coveragePerson')}
        </Typography.Label>

        {chunk(insureds, 2).map(row => (
          <Row flex={1} key={row?.[0]?.id} marginTop={space[2]}>
            {row.map(item => (
              <Row flex={1} key={item?.id} flexBasis={'100%'}>
                {item ? <InsuredItem data={item} /> : null}
              </Row>
            ))}
          </Row>
        ))}
      </Box>
    </Box>
  );
};

export default BasePlanSection;
