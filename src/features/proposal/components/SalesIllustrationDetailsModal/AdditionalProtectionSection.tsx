import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import useRiderDefinitions from 'features/proposal/hooks/useRiderDefinitions';
import React from 'react';
import {
  CharityOrg,
  Insured,
  Plan,
  RiderCode,
  RiderPlanFieldDefinition,
} from 'types/quotation';
import ADDRiderSection from './components/ADDRiderSection';
import CIWaiverRiderSection from './components/CIWaiverRiderSection';
import CommonRiderSection from './components/CommonRiderSection';
import HajjRiderSection from './components/HajjRiderSection';
import MedicalRiderSection from './components/MedicalRiderSection';
import RiderSectionContainer from './components/RiderSectionContainer';
import TermRiderSection from './components/TermRiderSection';
import MedicalRiderPlusSection from './components/MedicalRiderPlusSection';
import WOPRiderSection from './components/WOPRiderSection';

type AdditionalProtectionSectionProps = {
  charityOrgList: Array<CharityOrg | null>;
};

export default function AdditionalProtectionSection({
  charityOrgList,
}: AdditionalProtectionSectionProps) {
  const selectableRiderDefinitions = useRiderDefinitions({
    selectableOnly: true,
  });

  const allRiderDefinitions = useRiderDefinitions();

  const { allParties } = useQuotationInsureds();

  const riderPlanInfo = useRiderPlanInfo(undefined, {
    forDisplayOnly: true,
  }) as Plan[];

  const riderPlanValues = riderPlanInfo
    .filter(Boolean)
    .map(rider => ({
      ...rider,
      definition: (rider?.partOfBasicPlan
        ? allRiderDefinitions
        : selectableRiderDefinitions
      ).find(it => it.pid === rider.pid), // if rider is part of basic plan use allRiderDefinitions to find the definition
      insuredPersons: allParties.filter(
        person => person?.insuredId === rider?.insuredId,
      ),
    }))
    // remove topup riders
    .filter(
      item =>
        item?.partOfBasicPlan ||
        (item.definition &&
          item.definition?.planConfig?.displayCategory?.default !== 'topUp'),
    );

  // to merge all insured person of Medical Rider into one array
  const uniqueRiderPlanValues = riderPlanValues.reduce(
    (prev, cur) => {
      const existedRider = prev.find(item => item.pid === cur.pid);
      if (existedRider) {
        for (const currentInsuredPerson of cur.insuredPersons) {
          const hasInsuredPerson = (existedRider.insuredPersons || []).find(
            person => person.id === currentInsuredPerson?.id,
          );

          if (!hasInsuredPerson)
            existedRider.insuredPersons.push(currentInsuredPerson);
        }

        return prev;
      } else {
        return [...prev, cur];
      }
    },
    [] as Array<
      Plan & {
        definition?: RiderPlanFieldDefinition;
        insuredPersons: Array<Insured>;
      }
    >,
  );

  return uniqueRiderPlanValues.map((rider, id) => {
    if (rider.pid === RiderCode.MDCLPPR) {
      return (
        <MedicalRiderSection
          rider={rider}
          id={id}
          insuredPersons={rider.insuredPersons}
          key={rider.pid}
        />
      );
    }

    if ([RiderCode.ME11, RiderCode.MEDIK].includes(rider.pid as RiderCode)) {
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <MedicalRiderPlusSection rider={rider} id={id} />
        </RiderSectionContainer>
      );
    }

    if (rider.pid === RiderCode.TPB)
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <TermRiderSection
            rider={rider}
            id={id}
            charityOrgList={charityOrgList}
          />
        </RiderSectionContainer>
      );

    if (rider.pid === RiderCode.CIWOC)
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <CIWaiverRiderSection rider={rider} id={id} />
        </RiderSectionContainer>
      );

    if (rider.pid === RiderCode.HAJJ)
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <HajjRiderSection rider={rider} id={id} />
        </RiderSectionContainer>
      );

    if (rider.pid === RiderCode.ADDB)
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <ADDRiderSection rider={rider} id={id} />
        </RiderSectionContainer>
      );

    if (
      [RiderCode.WOP, RiderCode.WLP, RiderCode.WLPP].includes(
        rider.pid as RiderCode,
      )
    )
      return (
        <RiderSectionContainer
          key={rider.pid}
          insuredPersons={rider.insuredPersons}>
          <WOPRiderSection rider={rider} id={id} />
        </RiderSectionContainer>
      );

    return (
      <RiderSectionContainer
        key={rider.pid}
        insuredPersons={rider.insuredPersons}>
        <CommonRiderSection rider={rider} id={id} />
      </RiderSectionContainer>
    );
  });
}
