import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import useRiderForm from 'features/proposal/hooks/rider/useRiderForm';
import { useBasePlanCurrency } from 'features/proposal/hooks/useBasePlanInfo';
import useFields from 'features/proposal/hooks/useFields';
import { OptionConfig } from 'features/proposal/types';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TranslationField } from 'types';
import { Plan, RiderCode, RiderPlanFieldDefinition } from 'types/quotation';
import { formatCurrencyWithMask } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { CommonDataItem } from './CommonDataItem';
import { decimalPlacesByCountry } from 'constants/decimalPlaces';
type CommonRiderSectionProps = {
  rider: Plan & {
    definition?: RiderPlanFieldDefinition;
  };
  id: number;
};

export default function CommonRiderSection({
  rider,
  id,
}: CommonRiderSectionProps) {
  const { space } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const currency = useBasePlanCurrency();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const {
    isPolicyTermShow,
    isSumAssuredShow,
    isPremiumTermShow,
    isTotalPremShow,
    isBasePremShow,
    isSinceYearShow,

    isReturnOfPremiumShow,
    isClassesShow,
    isDeductibleShow,
    isRetirementOptionShow,

    isMonthlyIncomeShow,
    renderLabel,
  } = useRiderForm(rider.definition);

  const classesOptions =
    (useFields(['classes'], 'riderPlan', rider?.definition?.pid).get('classes')
      ?.options as OptionConfig<number | string>[] | []) || [];

  const retirementOptions =
    (useFields(['retirementOption'], 'riderPlan', rider?.definition?.pid).get(
      'retirementOption',
    )?.options as OptionConfig<number | string>[] | []) || [];

  const monthlyIncomeOptions =
    (useFields(['monthlyIncome'], 'riderPlan', rider?.definition?.pid).get(
      'monthlyIncome',
    )?.options as OptionConfig<number | string>[] | []) || [];

  const riderFields = (
    <>
      {isMonthlyIncomeShow && (
        <CommonDataItem
          label={renderLabel('monthlyIncome')}
          value={
            renderLabelByLanguage(
              monthlyIncomeOptions.find(
                item => item.value === rider?.monthlyIncome,
              )?.label,
            ) ?? t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}
      {isReturnOfPremiumShow && (
        <CommonDataItem
          label={renderLabel('rop')}
          value={rider?.rop ?? t('proposal:basicPlan.textField.placeholder')}
        />
      )}

      {isClassesShow && (
        <CommonDataItem
          label={renderLabel('classes')}
          value={
            renderLabelByLanguage(
              classesOptions.find(item => item.value === rider?.classes)?.label,
            ) ?? t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isSumAssuredShow && (
        <CommonDataItem
          label={t('proposal:rider.sumAssured')}
          value={
            rider.sumAssured
              ? `${currency} ${formatCurrencyWithMask(rider.sumAssured, decimalPlacesByCountry)}`
              : t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      <CommonDataItem
        label={t('proposal:rider.policyTerm')}
        value={
          rider.policyTerm
            ? `${rider.policyTerm} ${t('common:years')}`
            : t('proposal:basicPlan.textField.placeholder')
        }
      />

      {/* hard code to hide the premiumTerm for these riders */}
      {![RiderCode.ILCI, RiderCode.ILPR].includes(rider?.pid as RiderCode) && (
        <CommonDataItem
          label={t('proposal:rider.premiumTerm')}
          value={
            rider.premiumTerm
              ? `${rider.premiumTerm} ${t('common:years')}`
              : t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isTotalPremShow && (
        <CommonDataItem
          label={t('proposal:rider.totalPrem')}
          value={
            rider.totalPrem
              ? `${currency} ${formatCurrencyWithMask(rider.totalPrem, 2)}`
              : t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isBasePremShow && (
        <CommonDataItem
          label={t('proposal:rider.basePrem')}
          value={
            rider.basePrem
              ? `${currency} ${formatCurrencyWithMask(rider.basePrem, 2)}`
              : t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isDeductibleShow && (
        <CommonDataItem
          label={renderLabel('deductible')}
          value={
            rider?.deductible ?? t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isRetirementOptionShow && (
        <CommonDataItem
          label={renderLabel('retirementOption')}
          value={
            renderLabelByLanguage(
              retirementOptions.find(
                item => item.value === rider?.retirementOption,
              )?.label,
            ) ?? t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}

      {isSinceYearShow && (
        <CommonDataItem
          label={renderLabel('sinceYear')}
          value={
            String(rider?.sinceYear) ??
            t('proposal:basicPlan.textField.placeholder')
          }
        />
      )}
    </>
  );

  return (
    <>
      <Row px={space[4]} pt={space[4]}>
        <Column mr={isTabletMode ? space[4] : space[2]}>
          <Typography.LargeBody fontWeight="bold">
            {`${id + 1}.`}
          </Typography.LargeBody>
        </Column>

        <Column flex={1}>
          <Typography.LargeBody fontWeight="bold">
            {renderLabelByLanguage(rider?.productName as TranslationField)}
          </Typography.LargeBody>

          {isTabletMode && (
            <Row my={space[4]} mr={space[10]} flexWrap="wrap" rowGap={space[4]}>
              {riderFields}
            </Row>
          )}
        </Column>
      </Row>

      {!isTabletMode && (
        <Box flex={1}>
          <Row
            flexWrap="wrap"
            flex={1}
            paddingTop={space[4]}
            paddingBottom={space[2]}
            px={space[4]}>
            {riderFields}
          </Row>
        </Box>
      )}
    </>
  );
}
