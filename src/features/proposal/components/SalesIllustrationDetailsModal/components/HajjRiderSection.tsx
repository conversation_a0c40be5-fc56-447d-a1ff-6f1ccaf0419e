import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import useSubRiders from 'features/proposal/hooks/rider/useSubRider';
import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { RiderFormValues } from 'features/proposal/types';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Plan, RiderPlanFieldDefinition } from 'types/quotation';
import { formatCurrencyWithMask } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { CommonDataItem } from './CommonDataItem';

import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';

type HajjRiderSectionProps = {
  rider: RiderFormValues & {
    definition?: RiderPlanFieldDefinition;
  };
  id: number;
};

export default function HajjRiderSection({ rider, id }: HajjRiderSectionProps) {
  const { space, colors } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const basePlanInfo = useBasePlanInfo();

  const riderPlans = useRiderPlanInfo() as Plan[];

  const subRiders = useSubRiders(rider.definition).map(item => ({
    definition: item,
    ...(riderPlans.find(it => it?.pid === item.pid) ?? {}),
  }));

  return (
    <>
      <Row px={space[4]} pt={space[4]}>
        <Column mr={space[4]}>
          <Typography.LargeBody fontWeight="bold">
            {`${id + 1}.`}
          </Typography.LargeBody>
        </Column>

        <Column flex={1}>
          <Typography.LargeBody fontWeight="bold">
            {renderLabelByLanguage(rider.definition?.label)}
          </Typography.LargeBody>

          <Row my={space[4]} mr={space[10]}>
            <CommonDataItem
              label={t('proposal:rider.sumAssured')}
              value={
                rider.sumAssured
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.sumAssured,
                    )}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.policyTerm')}
              value={
                rider.policyTerm
                  ? `${rider.policyTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.premiumTerm')}
              value={
                rider.premiumTerm
                  ? `${rider.premiumTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.totalPrem')}
              value={
                rider.totalPrem
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.totalPrem,
                      2,
                    )}`
                  : '--'
              }
            />
          </Row>
        </Column>
      </Row>

      {Boolean(subRiders.length) && (
        <Box
          mx={space[11]}
          py={space[4]}
          borderTopColor={colors.palette.fwdGreyDark}
          borderTopWidth={1}>
          <Typography.Label
            fontWeight="bold"
            color={colors.palette.fwdDarkGreen[50]}>
            {t('proposal:rider.optionalBenefit')}
          </Typography.Label>

          {subRiders.map(subRider => (
            <Box key={subRider.pid} mt={space[4]}>
              <Typography.Label
                fontWeight="bold"
                color={colors.palette.fwdDarkGreen[100]}>
                {renderLabelByLanguage(subRider?.definition?.label)}
              </Typography.Label>

              <Row mr={space[3]} mt={space[4]}>
                <CommonDataItem
                  label={t('proposal:rider.sumAssured')}
                  value={
                    subRider.sumAssured
                      ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                          subRider.sumAssured,
                        )}`
                      : '--'
                  }
                />
                <CommonDataItem
                  label={t('proposal:rider.policyTerm')}
                  value={
                    subRider.policyTerm
                      ? `${subRider.policyTerm} ${t('common:years')}`
                      : '--'
                  }
                />
                <CommonDataItem
                  label={t('proposal:rider.premiumTerm')}
                  value={
                    subRider.premiumTerm
                      ? `${subRider.premiumTerm} ${t('common:years')}`
                      : '--'
                  }
                />
                <CommonDataItem
                  label={t('proposal:rider.totalPrem')}
                  value={
                    subRider.totalPrem
                      ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                          subRider.totalPrem,
                          2,
                        )}`
                      : '--'
                  }
                />
              </Row>
            </Box>
          ))}
        </Box>
      )}
    </>
  );
}
