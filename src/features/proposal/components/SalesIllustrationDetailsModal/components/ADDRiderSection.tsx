import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import useSubRiders from 'features/proposal/hooks/rider/useSubRider';
import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { OptionConfig, RiderFormValues } from 'features/proposal/types';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Plan, RiderCode, RiderPlanFieldDefinition } from 'types/quotation';
import { formatCurrencyWithMask } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { CommonDataItem } from './CommonDataItem';

import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';
import useFields from 'features/proposal/hooks/useFields';
import { TranslationField } from 'types';

type ADDRiderSectionProps = {
  rider: RiderFormValues & {
    definition?: RiderPlanFieldDefinition;
  };
  id: number;
};

export default function ADDRiderSection({ rider, id }: ADDRiderSectionProps) {
  const { space, colors } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const basePlanInfo = useBasePlanInfo();

  const riderPlans = useRiderPlanInfo() as Plan[];

  const subRiderIds = useSubRiders(rider.definition).map(r => r?.pid);

  const addedSubRiders = useMemo(() => {
    const subRiders = riderPlans.filter(r => subRiderIds.includes(r?.pid));
    const optionalSubRiderIndex = subRiders?.findIndex(
      item => item?.pid === RiderCode.ADOP,
    );

    // move the Optional package benefit rider to the top of the list
    if (optionalSubRiderIndex !== -1) {
      subRiders.unshift(subRiders.splice(optionalSubRiderIndex, 1)[0]);
    }

    return subRiders;
  }, [riderPlans, subRiderIds]);

  const annualIncomeOptions =
    (useFields(['annualIncome'], 'riderPlan', RiderCode.ADIA).get(
      'annualIncome',
    )?.options as OptionConfig<number | string>[] | []) || [];

  const monthlyIncomeBenefitOptions =
    (useFields(['classes'], 'riderPlan', RiderCode.ADIA).get('classes')
      ?.options as OptionConfig<number | string>[] | []) || [];

  return (
    <>
      <Row px={space[4]} pt={space[4]}>
        <Column mr={space[4]}>
          <Typography.LargeBody fontWeight="bold">
            {`${id + 1}.`}
          </Typography.LargeBody>
        </Column>

        <Column flex={1}>
          <Typography.LargeBody fontWeight="bold">
            {renderLabelByLanguage(rider.definition?.label)}
          </Typography.LargeBody>

          <Row my={space[4]} mr={space[10]}>
            <CommonDataItem
              label={t('proposal:rider.sumAssured')}
              value={
                rider.sumAssured
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.sumAssured,
                    )}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.policyTerm')}
              value={
                rider.policyTerm
                  ? `${rider.policyTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.premiumTerm')}
              value={
                rider.premiumTerm
                  ? `${rider.premiumTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.totalPrem')}
              value={
                rider.totalPrem
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.totalPrem,
                      2,
                    )}`
                  : '--'
              }
            />
          </Row>
        </Column>
      </Row>

      {addedSubRiders.length > 0 && (
        <Box mx={space[11]}>
          {addedSubRiders.map(subRider => (
            <Box
              pt={space[4]}
              mb={space[4]}
              borderTopColor={colors.palette.fwdGreyDark}
              borderTopWidth={1}>
              <Typography.Label
                fontWeight="bold"
                color={colors.palette.fwdDarkGreen[50]}>
                {subRider?.pid === RiderCode.ADOP
                  ? t('proposal:rider.optionalPackageBenefit')
                  : t('proposal:rider.optionalBenefit')}
              </Typography.Label>

              <Box key={subRider.pid} mt={space[4]}>
                <Typography.Label
                  fontWeight="bold"
                  color={colors.palette.fwdDarkGreen[100]}>
                  {subRider?.pid === RiderCode.ADOP
                    ? t('proposal:rider.medReimbursementPurchase')
                    : renderLabelByLanguage(
                        subRider?.productName as TranslationField,
                      )}
                </Typography.Label>

                <Row mr={space[3]} mt={space[4]}>
                  <CommonDataItem
                    label={t('proposal:rider.sumAssured')}
                    value={
                      subRider?.sumAssured
                        ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                            subRider?.sumAssured,
                          )}`
                        : '--'
                    }
                  />
                  <CommonDataItem
                    label={t('proposal:rider.policyTerm')}
                    value={
                      subRider?.policyTerm
                        ? `${subRider.policyTerm} ${t('common:years')}`
                        : '--'
                    }
                  />
                  <CommonDataItem
                    label={t('proposal:rider.premiumTerm')}
                    value={
                      subRider?.premiumTerm
                        ? `${subRider.premiumTerm} ${t('common:years')}`
                        : '--'
                    }
                  />
                  <CommonDataItem
                    label={t('proposal:rider.totalPrem')}
                    value={
                      subRider?.totalPrem
                        ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                            subRider.totalPrem,
                            2,
                          )}`
                        : '--'
                    }
                  />
                </Row>

                {subRider?.pid === RiderCode.ADIA && (
                  <Row mr={space[3]} mt={space[4]}>
                    <CommonDataItem
                      label={t('proposal:rider.annualIncome')}
                      value={
                        renderLabelByLanguage(
                          annualIncomeOptions.find(
                            item => item.value === subRider.annualIncome,
                          )?.label,
                        ) ?? '--'
                      }
                    />
                    <CommonDataItem
                      label={t('proposal:rider.monthlyIncomeBenefit')}
                      value={
                        renderLabelByLanguage(
                          monthlyIncomeBenefitOptions.find(
                            item => item.value === subRider.classes,
                          )?.label,
                        ) ?? '--'
                      }
                    />
                  </Row>
                )}
              </Box>
            </Box>
          ))}
        </Box>
      )}
    </>
  );
}
