import { useTheme } from '@emotion/react';
import { DropdownPanel, Icon } from 'cube-ui-components';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import BasePlanTextFieldContainer from '../BasePlanBox/BasePlanTextFieldContainer';
import RoundOutlineTextField from '../RoundOutlineText';
import useFields from 'features/proposal/hooks/useFields';
import { OptionConfig } from 'features/proposal/types';
import { renderLabelByLanguage } from 'utils/helper/translation';
import getSelectedItem from 'features/proposal/untils/getSelectedItem';

type MultiplierDropdownProps = {
  value?: number;
  onChange: (value?: OptionConfig<string>) => void;
  isLoading?: boolean;
};

export default function MultiplierDropdown({
  value,
  onChange,
  isLoading,
}: MultiplierDropdownProps) {
  const { colors } = useTheme();
  const { t } = useTranslation(['proposal', 'common']);
  const configOptions = useFields(['percLoading']).get('percLoading')?.options;
  const options = useMemo(
    () =>
      Array.isArray(configOptions)
        ? (configOptions as OptionConfig<string>[])
        : [],
    [configOptions],
  );

  const [modalVisible, setModalVisible] = useState(false);
  const selectedItem = useMemo(
    () => getSelectedItem(options, value?.toString()),
    [value],
  );

  const showModal = () => {
    if (isLoading) return;
    setModalVisible(true);
  };

  return (
    <>
      <BasePlanTextFieldContainer title={t('proposal:loading.multiplier')}>
        <RoundOutlineTextField
          isActive={true}
          value={
            !value || value == 100
              ? t('proposal:loading.multiplier.default')
              : `x ${value}%`
          }
          onPress={showModal}
          right={<Icon.Dropdown size={20} fill={colors.primary} />}
        />
      </BasePlanTextFieldContainer>

      <DropdownPanel<OptionConfig<string>, number>
        title={t('proposal:loading.multiplier')}
        actionLabel={t('common:done')}
        visible={modalVisible}
        data={options}
        selectedItem={selectedItem}
        getItemLabel={item => renderLabelByLanguage(item.label) ?? '--'}
        getItemValue={item => parseInt(item.value)}
        onDismiss={() => setModalVisible(false)}
        onDone={onChange}
      />
    </>
  );
}
