import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Picker, Row, SmallLabel } from 'cube-ui-components';
import useFields, { FieldsType } from 'features/proposal/hooks/useFields';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { View, ViewStyle } from 'react-native';
import { renderLabelByLanguage } from 'utils/helper/translation';
import BasePlanFieldLabel from './BasePlanFieldLabel';

export type BasePlanBooleanPickerProps = {
  title?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  disabled?: boolean;
  fieldName: keyof FieldsType;
  value?: string;
  style?: ViewStyle;
};

const UnderLabel = styled(SmallLabel)(({ theme: { colors, space } }) => ({
  color: colors.palette.fwdGreyDarker,
  paddingTop: space[1],
}));

const checkIfValueIsTrue = (value: string | boolean) => {
  return (
    (typeof value === 'string' &&
      ['Y', 'YES', 'TRUE'].includes(value.toUpperCase())) ||
    (typeof value === 'boolean' && value === true)
  );
};

const BasePlanBooleanPicker = ({
  title,
  value,
  disabled,
  fieldName,
  onChange,
  onBlur,
}: BasePlanBooleanPickerProps) => {
  const { space } = useTheme();
  const { t } = useTranslation();

  const configOptions = useFields([fieldName]).get(fieldName)?.options;

  const options = configOptions?.map(o => {
    return {
      text: checkIfValueIsTrue(o.value as string | boolean)
        ? t('yes')
        : t('no'),
      value: o.value as string,
    };
  });

  const selectedOptionLabel = renderLabelByLanguage(
    configOptions?.find(o => o.value === value)?.label,
  );

  const handleOnChange = (value: string) => {
    onChange?.(value);
    onBlur?.();
  };

  return (
    <View>
      {title && <BasePlanFieldLabel>{title}</BasePlanFieldLabel>}

      <Row alignItems="center" gap={space[2]}>
        {Array.isArray(options) && options?.length > 0 ? (
          <View>
            <Picker
              type="chip"
              value={value}
              onChange={handleOnChange}
              items={options}
              disabled={disabled}
            />

            <UnderLabel>{selectedOptionLabel}</UnderLabel>
          </View>
        ) : (
          <View style={{ height: space[8] }}></View>
        )}
      </Row>
    </View>
  );
};

export default BasePlanBooleanPicker;
