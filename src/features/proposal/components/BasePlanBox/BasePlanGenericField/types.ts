import { AutocompleteProps } from 'components/Autocomplete';
import { OptionConfig } from 'features/proposal/types';
import { TextInputProps } from 'react-native';
import { BasePlanDropdownProps } from '../BasePlanDropdown';
import { BasePlanPickerProps } from '../BasePlanPicker';
import { BasePlanReadonlyFieldProps } from '../BasePlanReadonlyField/BasePlanReadonlyField';

export type BasePlanGenericFieldProps = {
  suffix?: string;
  precision?: number;
  customOptions?: OptionConfig<number>[];
  pickerType?: 'chip' | 'autocomplete';
  autoCompleteValueType?: 'number' | 'string';
  inputProps?: TextInputProps;
  emptyValue?: string;
  hideEditIcon?: boolean;
  containerWidth?: number | null;
} & BasePlanDropdownProps &
  BasePlanPickerProps &
  BasePlanReadonlyFieldProps &
  Omit<
    AutocompleteProps<OptionConfig<string>, string>,
    'getItemLabel' | 'getItemValue' | 'data'
  >;
