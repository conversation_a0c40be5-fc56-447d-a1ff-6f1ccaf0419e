import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import { Icon, Typography } from 'cube-ui-components';
import React from 'react';
import { Pressable, StyleProp, ViewStyle } from 'react-native';

const Container = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdOrange[20],
  padding: space[4],
}));
const Content = styled.View(({ theme: { space } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const Arrow = styled.View(({ theme: { space, colors } }) => ({
  position: 'absolute',
  top: -10,
  right: space[3],
  width: 0,
  height: 0,
  borderLeftWidth: 10,
  borderLeftColor: 'transparent',
  borderRightWidth: 10,
  borderRightColor: 'transparent',
  borderBottomWidth: 10,
  borderBottomColor: colors.palette.fwdOrange[5],
}));

const StyledText = styled(Typography.Body)(({ theme: { colors, space } }) => ({
  flex: 1,
  color: colors.onBackground,
}));

type ViewPdfReminderProps = {
  text: string;
  isVisible: boolean;
  onRequestClose: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  arrowStyle?: StyleProp<ViewStyle>;
};

const ViewPdfReminder: React.FC<ViewPdfReminderProps> = ({
  text,
  isVisible,
  onRequestClose,
  containerStyle,
  arrowStyle,
}) => {
  const { colors } = useTheme();

  if (!isVisible) {
    return null;
  }

  return (
    <Container style={[containerStyle]}>
      <Arrow style={arrowStyle} />
      <Content>
        <StyledText fontWeight="bold">{text}</StyledText>
        <Pressable onPress={onRequestClose} hitSlop={HIT_SLOP_SPACE(4)}>
          <Icon.Close size={24} fill={colors.onBackground} />
        </Pressable>
      </Content>
    </Container>
  );
};

export default ViewPdfReminder;
