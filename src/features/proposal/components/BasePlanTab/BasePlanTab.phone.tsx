import { useTheme } from '@emotion/react';
import React from 'react';
import { Pressable } from 'react-native';
import StepTitle from '../StepTitle';
import { BasePlanTabProps } from '.';
import { useRegisterTab, useTabState } from '../TabContext';

const BasePlanTab = ({ ...props }: BasePlanTabProps) => {
  const { colors, space } = useTheme();
  useRegisterTab({
    tabKey: 'basePlan',
  });
  const { isActive, onTabPress } = useTabState('basePlan');

  return (
    <Pressable
      {...props}
      style={{
        backgroundColor: isActive
          ? colors.palette.white
          : colors.palette.fwdOrange[20],
        justifyContent: 'center',
        flex: isActive ? 5 : 3,
      }}
      onPress={onTabPress}>
      {!isActive && (
        <StepTitle
          titleText={''}
          step={1}
          titleTextStyle={{
            color: colors.palette.black,
            fontWeight: '700',
          }}
          stepIncidatorStyle={{ backgroundColor: colors.palette.white }}
          style={{ marginLeft: space[3] }}
        />
      )}
    </Pressable>
  );
};

export default BasePlanTab;
