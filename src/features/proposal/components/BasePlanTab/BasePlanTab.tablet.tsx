import styled from '@emotion/native';
import React from 'react';
import { BasePlanTabProps } from '.';
import BasePlanTabContent from '../StickyHeader/BasePlanSectionLabel.tablet';
import { useRegisterTab, useTabState } from '../TabContext';

const TabContainer = styled.Pressable<{ isActive: boolean }>(
  ({ theme: { colors }, isActive }) => ({
    backgroundColor: isActive
      ? colors.palette.white
      : colors.palette.fwdOrange[20],
    flex: 7,
    justifyContent: 'center',
  }),
);
const BasePlanTab = ({ ...props }: BasePlanTabProps) => {
  useRegisterTab({
    tabKey: 'basePlan',
  });
  const { isActive, onTabPress } = useTabState('basePlan');

  return (
    <TabContainer {...props} isActive={isActive} onPress={onTabPress}>
      <BasePlanTabContent />
    </TabContainer>
  );
};

export default BasePlanTab;
