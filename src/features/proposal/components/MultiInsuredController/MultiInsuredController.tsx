import { ScrollView } from 'react-native';
import React, { useState } from 'react';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import { Insured } from 'types/quotation';
import { Box, Button, Icon, Row, Typography } from 'cube-ui-components';
import CoveragePersonRowItem from '../Rider/MedicalRider/CoveragePersonRowItem';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import AddFamilyMemberModal from '../Rider/MedicalRider/AddFamilyMemberModal';
import { DeleteModal } from 'features/coverageDetails/components/common/DeleteModal';
import useInsuredAmount from 'features/proposal/hooks/useInsuredAmount';
import { useQuotationCalculationStatus } from 'features/proposal/hooks/useQuotationCalculation';
import { mapQuotationInsuredToParties } from 'features/proposal/untils/quotationUtils';
import { InsuredFormValues } from 'features/coverageDetails/validation/common/insuredSchema';

type MultiInsuredController = {
  triggerCalculation: () => Promise<void>;
};

const MultiInsuredController = ({
  triggerCalculation,
}: MultiInsuredController) => {
  const {
    insureds,
    owner,
    mainInsured,
    saveQuotationInsured,
    deleteQuotationInsured,
    activeCase,
    isDeletingParty,
  } = useQuotationInsureds();

  const { canAddChild, canAddSpouse } = useInsuredAmount({ insureds });

  const [isInsuredEditing, setInsuredEditing] = useState(false);
  const [showAddInsuredModal, setShowAddInsuredModal] = useState(false);
  const [selectingInsured, setSelectingInsured] = useState<Partial<Insured>>();
  const [showDeleteInsuredModal, setDeleteInsuredModal] = useState(false);

  const isWaitingQuotationResult = useQuotationCalculationStatus();

  const { t } = useTranslation(['common', 'proposal']);

  const { space, colors } = useTheme();

  const onDeleteCoveragePerson = async () => {
    try {
      await deleteQuotationInsured(selectingInsured?.insuredId);
      await triggerCalculation();
    } catch (error) {
      console.error(error);
    } finally {
      onCloseDeleteInsuredModal();
    }
  };

  const onCloseDeleteInsuredModal = () => {
    setDeleteInsuredModal(false);
    setSelectingInsured(undefined);
  };

  const onDeleteInsuredPress = (insured: Insured) => {
    setDeleteInsuredModal(true);
    setSelectingInsured(insured);
  };

  const onEditInsured = (insured: Insured) => {
    setShowAddInsuredModal(true);

    const formattedInsured = mapQuotationInsuredToParties(
      [insured],
      activeCase?.parties ?? [],
    )[0];

    setSelectingInsured(formattedInsured);
  };

  const onSaveQuotationInsured = async (formValues: InsuredFormValues) => {
    try {
      await saveQuotationInsured(formValues);
      await triggerCalculation();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Box
      flex={1}
      backgroundColor={colors.palette.fwdGrey[20]}
      padding={space[5]}>
      <Box flex={1}>
        <Row alignItems="center" justifyContent="space-between">
          <Typography.Label
            fontWeight="bold"
            color={colors.palette.fwdDarkGreen[50]}>
            {t('proposal:policyInsured')}
          </Typography.Label>
          <Button
            onPress={() => setInsuredEditing(!isInsuredEditing)}
            text={
              isInsuredEditing
                ? t('common:done')
                : t('proposal:insuredDetails.edit')
            }
            variant="text"
            textStyle={{ fontWeight: 'bold' }}></Button>
        </Row>

        <Box flex={1}>
          <ScrollView
            contentContainerStyle={{
              rowGap: space[5],
            }}>
            {insureds.map(i => (
              <CoveragePersonRowItem
                item={i}
                shouldLeftDisplay={false}
                onDeletePress={onDeleteInsuredPress}
                onEditPress={onEditInsured}
                isActionDisabled={false}
                checkedValue={false}
                isCheckboxDisabled={false}
                isActionDisplayed={isInsuredEditing}
              />
            ))}
          </ScrollView>
        </Box>

        <Button
          text={t('common:add')}
          variant="secondary"
          rounded
          icon={Icon.Plus}
          contentStyle={{
            paddingHorizontal: space[3],
            paddingBottom: space[2],
          }}
          textStyle={{ fontWeight: 'bold' }}
          style={{ width: 120, alignSelf: 'center' }}
          onPress={() => setShowAddInsuredModal(true)}
          disabled={!canAddChild && !canAddSpouse}
        />
      </Box>

      <AddFamilyMemberModal
        visible={showAddInsuredModal}
        onClose={() => {
          setShowAddInsuredModal(false);
          setSelectingInsured(undefined);
        }}
        onSaveParty={onSaveQuotationInsured}
        canAddSpouse={canAddSpouse}
        canAddChild={canAddChild}
        existingInsured={selectingInsured}
        owner={owner}
        mainInsured={mainInsured}
      />

      <DeleteModal
        dialogVisible={showDeleteInsuredModal}
        onConfirm={onDeleteCoveragePerson}
        onDeny={onCloseDeleteInsuredModal}
        title={t('proposal:rider.confirmDeleteInsuredTitle', {
          name: selectingInsured?.fullName,
        })}
        subTitle={t('proposal:rider.confirmDeleteInsuredSubtitle', {
          name: selectingInsured?.fullName,
        })}
        denyLabel={t('common:cancel')}
        removeLabel={t('common:delete')}
        isLoading={isDeletingParty || !!isWaitingQuotationResult}
      />
    </Box>
  );
};

export default MultiInsuredController;
