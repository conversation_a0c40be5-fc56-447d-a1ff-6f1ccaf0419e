import styled from '@emotion/native';
import React from 'react';
import { Typography, XView } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export type CalculationTextInputProps = {
  title: string;
  value: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  isActive?: boolean;
};

const Container = styled(XView)(({ theme: { space } }) => ({
  minWidth: 210,
  alignItems: 'center',
  marginBottom: space[4],
}));

const Title = styled(Typography.Body)(({ theme: { space } }) => ({
  marginBottom: 2,
  flexBasis: '40%',
}));

const InputContainer = styled.View(
  ({ theme: { borderRadius, colors, space } }) => ({
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: borderRadius['x-large'],
    borderColor: colors.palette.fwdOrange[100],
    borderWidth: 1,
    paddingHorizontal: space[3],
    paddingVertical: space[2],
    flexBasis: '60%',
  }),
);

const TextField = styled.TextInput(({ theme: { typography } }) => ({
  flex: 1,
  fontSize: typography.largeLabel.size,
  // fontFamily: Fonts.FWDCircularTT.Medium,
  lineHeight: typography.largeLabel.lineHeight,
}));

export default function CalculationOutlineInput({
  title,
  value,
  left,
  right,
  isActive,
}: CalculationTextInputProps) {
  const { colors } = useTheme();
  return (
    <Container>
      <Title>{title}</Title>
      <InputContainer
        style={[!isActive && { borderColor: colors.palette.fwdDarkGreen[20] }]}>
        {left}
        <TextField
          editable={false}
          value={value}
          style={{ fontWeight: 'bold' }}
        />
        {right}
      </InputContainer>
    </Container>
  );
}
