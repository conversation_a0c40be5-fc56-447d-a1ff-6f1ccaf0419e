import { useTheme } from '@emotion/react';
import { useInputEventListenerContext } from 'components/Input/InputEventListener';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import useRiderForm from 'features/proposal/hooks/rider/useRiderForm';
import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';
import useSubRiders from 'features/proposal/hooks/rider/useSubRider';
import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { RiderFormValues } from 'features/proposal/types';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Plan, RiderCode } from 'types/quotation';
import RiderDropdownField from '../RiderFields/RiderDropdownField';
import RiderGenericField from '../RiderFields/RiderGenericField';
import RiderSwitch from '../RiderFields/RiderSwitch';
import RiderTextField from '../RiderFields/RiderTextField';
import MonthlyIncomeSubRider from './SubRiders/MonthlyIncomeSubRider';
import ReadonlySubRiderFields from './SubRiders/ReadonlySubRiderFields';
import SimpleToggleSubRider from './SubRiders/SimpleToggleSubRider';
import { ADDRiderProps } from './types';

export default function ADDRider({
  control,
  position,
  riderDefinition,
  onChange,
  setValue,
}: ADDRiderProps) {
  const { space } = useTheme();
  const { t } = useTranslation(['common', 'proposal']);
  const inputEventListener = useInputEventListenerContext();
  const {
    isPolicyTermShow,
    isPolicyTermDisabled,
    isSumAssuredShow,
    isSumAssuredDisabled,
    isPremiumTermShow,
    isPremiumTermDisabled,
    isTotalPremShow,
    isTotalPremDisabled,
    isClassesDisabled,
  } = useRiderForm(riderDefinition);

  const basePlanInfo = useBasePlanInfo();

  const riderPlanInfo = useRiderPlanInfo() as Plan[];

  const subriders = useSubRiders(riderDefinition) ?? [];

  const handleToggleOptionalPackageBenefit = (val: string) => {
    onChange('classes', val);

    const ADOPRiderDefinition = subriders.find(
      rider => rider.pid === RiderCode.ADOP,
    );

    if (ADOPRiderDefinition) {
      // add ADOP rider if switch is on manually
      if (val === 'OPYES') {
        const newRiderValue: RiderFormValues = {
          pid: ADOPRiderDefinition.pid,
        };
        setValue(`riderPlans.${ADOPRiderDefinition.position}`, newRiderValue);
      } else {
        // remove ADOP rider if switch is off
        setValue(`riderPlans.${ADOPRiderDefinition.position}`, null);
      }
    }

    inputEventListener?.onBlur?.(`riderPlans.${position}.classes`);
  };

  const optionalPackageBenefitRider = (riderPlanInfo ?? []).find(
    r => r?.pid === RiderCode.ADOP,
  );

  // don't display the ADOP rider
  const filteredSubRiders = subriders.filter(r => r.pid !== RiderCode.ADOP);

  return (
    <Column>
      <Row alignItems="center" flexDirection="row" columnGap={space[3]}>
        {isSumAssuredShow && (
          <RiderTextField
            control={control}
            name={`riderPlans.${position}.sumAssured`}
            fieldName="sumAssured"
            riderDefinition={riderDefinition}
            label={t('proposal:rider.sumAssured')}
            disabled={isSumAssuredDisabled}
            currency={basePlanInfo?.currency}
            shouldUnregister
          />
        )}
        {isPolicyTermShow && (
          <RiderGenericField
            control={control}
            name={`riderPlans.${position}.policyTerm`}
            label={t('proposal:rider.policyTerm')}
            fieldName="policyTerm"
            onChange={(val: string | number | null) => {
              onChange('policyTerm', val ?? 0);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${position}.policyTerm`,
              );
            }}
            disabled={isPolicyTermDisabled}
            riderDefinition={riderDefinition}
            unit={t('years')}
          />
        )}
        {isPremiumTermShow && (
          <RiderDropdownField
            control={control}
            name={`riderPlans.${position}.premiumTerm`}
            label={t('proposal:rider.premiumTerm')}
            fieldName="premiumTerm"
            onChange={(val: string | number | null) => {
              onChange('premiumTerm', val ?? 0);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${position}.premiumTerm`,
              );
            }}
            disabled={isPremiumTermDisabled}
            riderDefinition={riderDefinition}
            unit={t('years')}
          />
        )}
        {isTotalPremShow && (
          <RiderTextField
            control={control}
            name={`riderPlans.${position}.totalPrem`}
            fieldName="totalPrem"
            riderDefinition={riderDefinition}
            label={`${t('proposal:rider.totalPrem')}`}
            disabled={isTotalPremDisabled}
            currency={basePlanInfo?.currency}
            shouldUnregister
          />
        )}
      </Row>

      <Box alignItems="center" marginTop={space[2]} marginBottom={space[4]}>
        <RiderSwitch
          control={control}
          name={`riderPlans.${position}.classes`}
          fieldName="classes"
          label={t('proposal:rider.optionalPackageBenefit')}
          subLabel={t('proposal:rider.medReimbursementPurchase')}
          onChange={handleToggleOptionalPackageBenefit}
          disabled={isClassesDisabled}
          riderDefinition={riderDefinition}
          checkedOptionValue="OPYES">
          {optionalPackageBenefitRider && (
            <ReadonlySubRiderFields rider={optionalPackageBenefitRider} />
          )}
        </RiderSwitch>
      </Box>

      {filteredSubRiders.length > 0 ? (
        <Typography.Label
          fontWeight="bold"
          style={{
            display: 'flex',
            flexBasis: '100%',
          }}>
          {t('proposal:rider.optionalBenefit')}
        </Typography.Label>
      ) : null}

      <Row
        alignItems="center"
        flexDirection="column"
        flexWrap="wrap"
        marginTop={space[4]}>
        <Column flex={1}>
          {filteredSubRiders.map(rider =>
            rider.pid === RiderCode.ADIA ? (
              <MonthlyIncomeSubRider
                key={rider.pid}
                control={control}
                riderDefinition={rider}
                setValue={setValue}
              />
            ) : (
              <SimpleToggleSubRider
                key={rider.pid}
                control={control}
                riderDefinition={rider}
                setValue={setValue}
              />
            ),
          )}
        </Column>
      </Row>
    </Column>
  );
}
