import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { PortalProvider } from '@gorhom/portal';
import AutocompletePopup from 'components/AutocompletePopup';
import DatePickerCalendar from 'components/DatePickerCalendar';
import Input from 'components/Input';
import NameField from 'components/NameField';
import {
  Box,
  Button,
  Column,
  Picker,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import useCoverageForm from 'features/coverageDetails/hooks/common/useCoverageForm';
import { RelationshipValue } from 'features/proposal/types';
import { TFuncKey } from 'i18next';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { Occupation, Religion } from 'types/optionList';
import { Insured } from 'types/quotation';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useValidationYupResolver } from 'utils/validation';

import CustomModal from 'components/Modal/CustomModal';
import insuredFormSetup, {
  InsuredFormValues,
} from 'features/coverageDetails/validation/common/insuredSchema';
import { SaleIllustrationTargetType } from 'features/home/<USER>/CreateSaleIllustrationModal/types';
import { object, string } from 'yup';

type Props = {
  visible: boolean;
  existingInsured?: Partial<Insured>;
  onClose: () => void;
  onSaveParty: (
    insuredFormValue: InsuredFormValues & { id?: string },
  ) => void | Promise<void>;
  canAddSpouse: boolean;
  canAddChild: boolean;
  owner?: Insured;
  mainInsured?: Insured;
};

export default function AddFamilyMemberModal({
  visible,
  existingInsured,
  onClose,
  onSaveParty,
  canAddSpouse,
  canAddChild,
  owner,
  mainInsured,
}: Props) {
  const { t } = useTranslation(['common', 'proposal', 'coverageDetails']);
  const { space } = useTheme();

  const initialInsuredFormData = {
    ...insuredFormSetup.insuredFormDefaultValues,
    nationality: 'MYS',
  };

  const insuredFormResolver = useValidationYupResolver(
    insuredFormSetup.insuredFormValidationSchema.clone().concat(
      object({
        religion: string(),
      }),
    ),
  );

  const {
    setValue,
    handleSubmit,
    reset,
    control,
    formState: { isValid, errors, isSubmitting },
  } = useForm<InsuredFormValues>({
    mode: 'onBlur',
    defaultValues: initialInsuredFormData,
    resolver: insuredFormResolver,
  });

  const isEntity =
    (owner?.clientType ?? '').toLowerCase() ===
    SaleIllustrationTargetType.ENTITY.toLowerCase();

  const {
    age,
    gender: genderCoverageForm,
    smokingHabit: smokingHabitCoverageForm,
    occupation: occupationCoverageForm,
    relationship: relationshipCoverageForm,
  } = useCoverageForm({
    ownerGender: isEntity ? mainInsured?.gender : owner?.gender,
    setValue,
    control,
    isExtraInsured: true,
  });

  useEffect(() => {
    if (existingInsured && visible) {
      reset({
        ...initialInsuredFormData,
        dob: new Date(existingInsured?.dob ?? ''),
        gender: existingInsured?.gender,
        firstName: existingInsured?.firstName,
        smokingHabit: existingInsured?.smoker ?? '',
        occupation: existingInsured?.occupation ?? '',
        occupationClass: existingInsured?.occClass ?? '',
        relationship: existingInsured?.relationship ?? '',
      });
    }
  }, [existingInsured, visible, reset]);

  const limitedRelationshipList = (
    relationshipCoverageForm?.options ?? []
  ).filter(r => {
    if (existingInsured && existingInsured?.relationship) {
      const selectedRelationship = relationshipCoverageForm.options?.find(
        o => o.value === existingInsured?.relationship,
      );

      if (selectedRelationship) {
        const nanoRelationShip = selectedRelationship?.otherValue?.nano;

        if (
          nanoRelationShip === RelationshipValue.CHILD &&
          r?.otherValue?.nano === RelationshipValue.SPOUSE &&
          !canAddSpouse
        ) {
          return false;
        }

        if (
          nanoRelationShip === RelationshipValue.SPOUSE &&
          !canAddChild &&
          r?.otherValue?.nano === RelationshipValue.CHILD
        ) {
          return false;
        }
      }

      return true;
    }

    if (r?.otherValue?.nano === RelationshipValue.SPOUSE && !canAddSpouse) {
      return false;
    }

    if (r?.otherValue?.nano === RelationshipValue.CHILD && !canAddChild) {
      return false;
    }

    return true;
  });

  const onConfirm = async (insuredFormData: InsuredFormValues) => {
    try {
      await onSaveParty({ ...insuredFormData, id: existingInsured?.id });
      onCloseModal();
    } catch (error) {
      console.error(error);
      throw error;
    }
  };

  const onCloseModal = () => {
    reset(initialInsuredFormData);
    onClose();
  };

  return (
    <CustomModal isVisible={visible} style={{ margin: 0 }}>
      <PortalProvider>
        <Container>
          <Box marginBottom={space[5]}>
            <Typography.H5 fontWeight="bold">
              {t('proposal:rider.addCoveragePerson')}
            </Typography.H5>
          </Box>
          <Box>
            <Input
              control={control}
              as={Picker}
              name={'relationship'}
              label={t('coverageDetails:formFields.relationshipToCertOwner')}
              items={limitedRelationshipList ?? []}
              error={t(errors.relationship?.message as TFuncKey)}
              shouldIgnoreEventListener
              type="chip"
            />
            <Row
              alignItems="center"
              flexDirection="row"
              columnGap={space[5]}
              marginTop={space[5]}>
              <Column flex={1}>
                <Input
                  control={control}
                  as={NameField}
                  name="firstName"
                  label={t('coverageDetails:formFields.firstName')}
                  error={t(errors?.firstName?.message as TFuncKey)}
                  shouldIgnoreEventListener
                />
              </Column>
            </Row>
            <Row flexDirection="row" columnGap={space[5]} marginTop={space[4]}>
              <Column flex={1}>
                <Input
                  control={control}
                  as={Picker}
                  disabled={genderCoverageForm?.disabled}
                  label={t('coverageDetails:gender')}
                  name="gender"
                  type="text"
                  items={genderCoverageForm?.options}
                  error={t(errors?.gender?.message as TFuncKey)}
                  shouldIgnoreEventListener
                />
              </Column>
              <Column flex={1}>
                <Row alignItems="flex-start" gap={space[3]}>
                  <Column flex={3}>
                    <Input
                      control={control}
                      as={DatePickerCalendar}
                      name="dob"
                      label={t('coverageDetails:formFields.dateOfBirth')}
                      hint="DD/MM/YYYY"
                      maxDate={new Date()}
                      formatDate={val => (val ? dateFormatUtil(val) : '')}
                      shouldIgnoreEventListener
                    />
                  </Column>
                  <Column flex={1}>
                    <TextField
                      value={age}
                      disabled
                      label={t('coverageDetails:formFields.age')}
                    />
                  </Column>
                </Row>
              </Column>
            </Row>
            <Row flexDirection="row" columnGap={space[5]} marginTop={space[4]}>
              <Column flex={1}>
                <Input
                  control={control}
                  as={Picker}
                  name="smokingHabit"
                  type="text"
                  label={t('coverageDetails:formFields.smokingHabit')}
                  error={undefined}
                  disabled={smokingHabitCoverageForm?.disabled}
                  items={smokingHabitCoverageForm?.options}
                  shouldIgnoreEventListener
                />
              </Column>
              <Column flex={1}>
                <Input
                  control={control}
                  as={AutocompletePopup<Occupation, string>}
                  name="occupation"
                  label={t('coverageDetails:formFields.occupation')}
                  disabled={occupationCoverageForm?.disabled}
                  data={occupationCoverageForm?.options ?? []}
                  getItemLabel={item => item.label}
                  getItemValue={item => item.value}
                  hint={occupationCoverageForm?.occupationClass}
                  onChange={occupationCoverageForm?.onChange}
                  shouldIgnoreEventListener
                  searchable
                />
              </Column>
            </Row>
          </Box>

          <Row justifyContent="center" alignItems="center" marginTop={space[6]}>
            <Box marginRight={space[6]}>
              <Button
                text={t('common:cancel')}
                variant="secondary"
                textStyle={{ paddingHorizontal: space[16] }}
                onPress={onCloseModal}
              />
            </Box>

            <Button
              textStyle={{ paddingHorizontal: space[16] }}
              text={`${t('common:confirm')}`}
              disabled={!isValid}
              onPress={handleSubmit(onConfirm)}
              loading={isSubmitting}
            />
          </Row>
        </Container>
      </PortalProvider>
    </CustomModal>
  );
}

const Container = styled(View)(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    padding: space[12],
    marginLeft: 'auto',
    marginRight: 'auto',
    width: '80%',
  }),
);
