import { useTheme } from '@emotion/react';
import { useInputEventListenerContext } from 'components/Input/InputEventListener';
import { Box, Button, Column, Icon, Row, Typography } from 'cube-ui-components';
import useRiderForm from 'features/proposal/hooks/rider/useRiderForm';
import { RiderFormValues, SiFormValues } from 'features/proposal/types';
import React, { useState } from 'react';
import { Control, UseFormSetValue, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ViewProps } from 'react-native';
import { Insured, RiderCode, RiderPlanFieldDefinition } from 'types/quotation';

import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import RiderDropdownField from '../RiderFields/RiderDropdownField';
import RiderSwitch from '../RiderFields/RiderSwitch';

import { useQuotationCalculationStatus } from 'features/proposal/hooks/useQuotationCalculation';
import AddFamilyMemberModal from './AddFamilyMemberModal';
import CoveragePersonRowItem from './CoveragePersonRowItem';

import { DeleteModal } from 'features/coverageDetails/components/common/DeleteModal';
import useInsuredAmount from 'features/proposal/hooks/useInsuredAmount';
import useRiderDefinitions from 'features/proposal/hooks/useRiderDefinitions';

import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { mapQuotationInsuredToParties } from 'features/proposal/untils/quotationUtils';
import RiderGenericField from '../RiderFields/RiderGenericField';

import { InsuredFormValues } from 'features/coverageDetails/validation/common/insuredSchema';
import RiderTextField from '../RiderFields/RiderTextField';
import styled from '@emotion/native';

export interface MedicalRiderProps extends ViewProps {
  control: Control<SiFormValues>;
  index: number;
  riderDefinition?: RiderPlanFieldDefinition;
  onChange: (name: keyof RiderFormValues, value: string | number) => void;
  setValue: UseFormSetValue<SiFormValues>;
}

export default function MedicalRider({
  control,
  index,
  riderDefinition,
  onChange,
  setValue,
}: MedicalRiderProps) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['common', 'proposal', 'savedProposals']);

  const [addFamilyMemberModal, setAddFamilyMemberModal] = useState(false);
  const [selectingInsured, setSelectingInsured] = useState<Partial<Insured>>();
  const [showDeleteInsuredModal, setDeleteInsuredModal] = useState(false);

  const isWaitingQuotationResult = useQuotationCalculationStatus();
  const allRiderDefinitions = useRiderDefinitions();

  const inputEventListener = useInputEventListenerContext();
  const {
    isDeductibleShow,
    isDeductibleDisabled,
    isPlanShow,
    isPlanDisabled,
    isExecutiveDisabled,
    isExecutivePlanShow,
    isRoomAndBoardDisabled,
    isRoomAndBoardShow,
    // isPremiumTermShow,
    // isPremiumDisabled,
    // isPolicyTermShow,
    isPolicyTermDisabled,
    // isTotalPremDisabled,
  } = useRiderForm(riderDefinition);

  const basePlanInfo = useBasePlanInfo();

  const riderFormValues = useWatch({ control, name: 'riderPlans' });

  const {
    allParties,
    owner,
    insureds,
    mainInsured,
    isEntityFlow,
    saveQuotationInsured,
    deleteQuotationInsured,
    activeCase,
    isDeletingParty,
  } = useQuotationInsureds();

  const { canAddChild, canAddSpouse } = useInsuredAmount({
    insureds: allParties,
  });

  const insureableParties = isEntityFlow ? insureds : allParties; // Entity (owner) can not be insured

  const findMedicalPlanValue = (insuredId?: string) => {
    if (!insuredId) {
      return;
    }

    return riderFormValues.find(
      r => r?.pid === RiderCode.MDCLPPR && r?.insuredId === insuredId,
    );
  };

  const findRiderDefinition = (insuredId?: string) => {
    const targetSubIndex = allParties.findIndex(
      i => i?.insuredId === insuredId,
    );

    if (targetSubIndex < 0) {
      return;
    }

    return allRiderDefinitions?.find(
      rd =>
        rd?.pid === RiderCode.MDCLPPR && rd?.subIndex === `i${targetSubIndex}`,
    );
  };

  const toggleMedicalRiderForInsured = (
    insuredId: string | undefined,
    isChecked: boolean,
  ) => {
    if (!insuredId) {
      return;
    }
    if (isChecked) {
      const found = findRiderDefinition(insuredId);

      const newRiderValue: RiderFormValues = {
        pid: RiderCode.MDCLPPR,
        planCode: RiderCode.MDCLPPR,
        insuredId: insuredId,
      };

      /* 
        when adding Medical Rider to coverage person, 
        the index does not matter as long as it does not override the existing plan index
        -> add it to the last index of quotation.plans[] 
      */
      setValue(
        `riderPlans.${found ? found.position : riderFormValues.length}`,
        newRiderValue,
      );
    } else {
      const riderIndex = riderFormValues.findIndex(
        r => r?.pid === RiderCode.MDCLPPR && r?.insuredId === insuredId,
      );

      // delete corresponding medical rider
      setValue(`riderPlans.${riderIndex}`, null);
    }

    inputEventListener?.onBlur?.(`riderPlans`);
  };

  const onSaveMedicalRiderInsured = async (formValues: InsuredFormValues) => {
    console.log('onSaveMedicalRiderInsured, insured: ', formValues);

    try {
      const insuredId = await saveQuotationInsured(formValues);

      toggleMedicalRiderForInsured(insuredId, true);
    } catch (error) {
      console.error(error);
    }
  };

  const onDeleteCoveragePerson = async () => {
    try {
      await deleteQuotationInsured(selectingInsured?.insuredId);
      toggleMedicalRiderForInsured(selectingInsured?.insuredId, false);
    } catch (error) {
      console.error(error);
    } finally {
      onCloseDeleteInsuredModal();
    }
  };

  const onEditInsured = (insured: Insured) => {
    setAddFamilyMemberModal(true);

    const formattedInsured = mapQuotationInsuredToParties(
      [insured],
      activeCase?.parties ?? [],
    )[0];

    setSelectingInsured(formattedInsured);
  };

  const onDeleteInsuredPress = (insured: Insured) => {
    setDeleteInsuredModal(true);
    setSelectingInsured(insured);
  };

  const onCloseDeleteInsuredModal = () => {
    setDeleteInsuredModal(false);
    setSelectingInsured(undefined);
  };

  const onToggleExecutivePlan = (val: string) => {
    riderFormValues.forEach((rider, index) => {
      if (rider?.pid === RiderCode.MDCLPPR) {
        setValue(`riderPlans.${index}.executivePlan`, val);
      }
    });

    inputEventListener?.onBlur?.(`riderPlans.${index}.executivePlan`);
  };

  return (
    <Column flex={1}>
      {isExecutivePlanShow && (
        <Row mb={space[4]} mr={space[3]}>
          <RiderSwitch
            control={control}
            name={`riderPlans.${index}.executivePlan`}
            fieldName="executivePlan"
            subLabel={t('proposal:rider.medicalExecutivePlan')}
            onChange={onToggleExecutivePlan}
            disabled={isExecutiveDisabled}
            riderDefinition={riderDefinition}
            switchContainerStyle={{
              paddingVertical: space[4],
              borderRadius: borderRadius['x-small'],
            }}
          />
        </Row>
      )}

      <Row
        alignItems="center"
        flexDirection="row"
        gap={space[3]}
        flexWrap="wrap">
        {isPlanShow && (
          <RiderDropdownField
            control={control}
            name={`riderPlans.${index}.plan`}
            label={t('proposal:rider.classes')}
            fieldName="plan"
            onChange={val => {
              onChange('classes', val as string);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${index}.classes`,
              );
            }}
            disabled={isPlanDisabled}
            riderDefinition={riderDefinition}
          />
        )}

        {isDeductibleShow && (
          <RiderDropdownField
            control={control}
            name={`riderPlans.${index}.deductible`}
            label={t('proposal:rider.deductible')}
            fieldName="deductible"
            onChange={val => {
              onChange('deductible', val as string);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${index}.deductible`,
              );
            }}
            disabled={isDeductibleDisabled}
            riderDefinition={riderDefinition}
          />
        )}

        {isRoomAndBoardShow && (
          <RiderGenericField
            control={control}
            name={`riderPlans.${index}.roomAndBoard`}
            fieldName="roomAndBoard"
            disabled={isRoomAndBoardDisabled}
            riderDefinition={riderDefinition}
            label={t('proposal:rider.roomAndBoard')}
          />
        )}
      </Row>

      <Box mt={space[2]}>
        <Typography.H7 fontWeight="bold">
          {t('proposal:insuredDetails.title')}
        </Typography.H7>
      </Box>

      <Box
        flex={1}
        marginTop={space[4]}
        marginRight={space[4]}
        paddingX={space[4]}
        paddingBottom={space[4]}
        borderWidth={1}
        borderColor={colors.palette.fwdGrey[100]}>
        <Row justifyContent="space-between" alignItems="center" flex={1}>
          <Box my={space[3]}>
            <Typography.Body color={colors.secondary}>
              {t('proposal:rider.coveragePerson')}
            </Typography.Body>
          </Box>

          {!basePlanInfo?.isMultipleInsureds && (
            <Button
              text={t('common:add')}
              variant="text"
              icon={Icon.Plus}
              onPress={() => setAddFamilyMemberModal(true)}
              contentStyle={{ paddingRight: 0 }}
              textStyle={{ fontWeight: 'bold' }}
              disabled={!canAddChild && !canAddSpouse}
            />
          )}
        </Row>

        {Array.isArray(insureableParties) && (
          <Column rowGap={space[4]}>
            {insureableParties.map(item => {
              // with FAMILYCI product, there's just only one Medical Rider and Medical Executive Rider for all insureds
              const relatedMedicalRiderIndex = riderFormValues.findIndex(
                it =>
                  it?.pid === RiderCode.MDCLPPR &&
                  it?.insuredId === item?.insuredId,
              );

              const relatedExecutivePlanRiderIndex = riderFormValues.findIndex(
                it =>
                  it?.pid === RiderCode.MDCLPPREX &&
                  it?.insuredId === item?.insuredId,
              );

              const isCheckboxDisabled = !!findRiderDefinition(item?.insuredId)
                ?.disabled;

              return (
                <Box key={item.insuredId}>
                  <CoveragePersonRowItem
                    shouldLeftDisplay={insureableParties.length > 1}
                    item={item}
                    onCheckBoxPress={toggleMedicalRiderForInsured}
                    onDeletePress={onDeleteInsuredPress}
                    onEditPress={onEditInsured}
                    isActionDisabled={!!isWaitingQuotationResult}
                    isCheckboxDisabled={
                      !!isWaitingQuotationResult || isCheckboxDisabled
                    }
                    checkedValue={!!findMedicalPlanValue(item?.insuredId)}
                    isActionDisplayed={!basePlanInfo?.isMultipleInsureds}
                  />

                  <DetailInsuredContainer disabled={isCheckboxDisabled}>
                    <RiderTextField
                      control={control}
                      name={`riderPlans.${relatedMedicalRiderIndex}.policyTerm`}
                      label={t('proposal:rider.policyTerm')}
                      fieldName="policyTerm"
                      disabled={isPolicyTermDisabled}
                      riderDefinition={riderDefinition}
                      unit={t('years')}
                    />

                    <RiderTextField
                      control={control}
                      name={`riderPlans.${relatedMedicalRiderIndex}.premiumTerm`}
                      fieldName="premiumTerm"
                      disabled={true}
                      riderDefinition={riderDefinition}
                      label={t('proposal:rider.premiumTerm')}
                      unit={t('years')}
                    />

                    <RiderTextField
                      control={control}
                      name={`riderPlans.${relatedMedicalRiderIndex}.totalPrem`}
                      fieldName="totalPrem"
                      riderDefinition={riderDefinition}
                      label={`${t('proposal:rider.totalPrem')}`}
                      disabled={true}
                      currency={basePlanInfo?.currency}
                    />

                    {relatedExecutivePlanRiderIndex !== -1 && (
                      <Box flex={1}>
                        <RiderTextField
                          control={control}
                          name={`riderPlans.${relatedExecutivePlanRiderIndex}.totalPrem`}
                          fieldName="totalPrem"
                          riderDefinition={riderDefinition}
                          label={`${t(
                            'proposal:rider.executiveRiderModalContribution',
                          )}`}
                          disabled={true}
                          currency={basePlanInfo?.currency}
                        />
                      </Box>
                    )}
                  </DetailInsuredContainer>
                </Box>
              );
            })}
          </Column>
        )}
      </Box>

      <AddFamilyMemberModal
        visible={addFamilyMemberModal}
        existingInsured={selectingInsured}
        onClose={() => {
          setAddFamilyMemberModal(false);
          setSelectingInsured(undefined);
        }}
        onSaveParty={onSaveMedicalRiderInsured}
        owner={owner}
        mainInsured={mainInsured}
        canAddSpouse={canAddSpouse}
        canAddChild={canAddChild}
      />

      <DeleteModal
        dialogVisible={showDeleteInsuredModal}
        onConfirm={onDeleteCoveragePerson}
        onDeny={onCloseDeleteInsuredModal}
        title={t('proposal:rider.confirmDeleteInsuredTitle', {
          name: selectingInsured?.fullName,
        })}
        subTitle={t('proposal:rider.confirmDeleteInsuredSubtitle', {
          name: selectingInsured?.fullName,
        })}
        denyLabel={t('common:cancel')}
        removeLabel={t('common:delete')}
        isLoading={isDeletingParty}
      />
    </Column>
  );
}

const DetailInsuredContainer = styled(Row)<{ disabled: boolean }>(
  ({ theme: { colors, space }, disabled }) => ({
    backgroundColor: colors.palette.fwdGrey[20],
    marginTop: space[4],
    marginLeft: space[10],
    flexWrap: 'wrap',
    paddingTop: space[4],
    opacity: disabled ? 0.3 : 1,
  }),
);
