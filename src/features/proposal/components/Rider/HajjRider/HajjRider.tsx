import { useTheme } from '@emotion/react';
import { useInputEventListenerContext } from 'components/Input/InputEventListener';
import { Column, Row } from 'cube-ui-components';
import useRiderForm from 'features/proposal/hooks/rider/useRiderForm';
import useSubRiders from 'features/proposal/hooks/rider/useSubRider';
import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { RiderFormValues, SiFormValues } from 'features/proposal/types';
import React from 'react';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ViewProps } from 'react-native';
import { RiderPlanFieldDefinition } from 'types/quotation';
import RiderDropdownField from '../RiderFields/RiderDropdownField';
import RiderGenericField from '../RiderFields/RiderGenericField';
import RiderSwitch from '../RiderFields/RiderSwitch';
import RiderTextField from '../RiderFields/RiderTextField';
import HajjSubRider from './HajjSubRider';

export interface HajjRiderProps extends ViewProps {
  control: Control<SiFormValues>;
  index: number;
  riderDefinition?: RiderPlanFieldDefinition;
  onChange: (
    name: keyof RiderFormValues,
    value: string | number | null,
  ) => void;
}

export default function HajjRider({
  control,
  index,
  riderDefinition,
  onChange,
}: HajjRiderProps) {
  const { space } = useTheme();
  const { t } = useTranslation(['common', 'proposal']);
  const inputEventListener = useInputEventListenerContext();
  const {
    isPolicyTermShow,
    isPolicyTermDisabled,
    isSumAssuredShow,
    isSumAssuredDisabled,
    isPremiumTermShow,
    isPremiumTermDisabled,
    isTotalPremShow,
    isTotalPremDisabled,
    isClassesShow,
    isClassesDisabled,
  } = useRiderForm(riderDefinition);
  const basePlanInfo = useBasePlanInfo();

  const subRiders = useSubRiders(riderDefinition);

  return (
    <Column>
      <Row alignItems="center" flexDirection="row" columnGap={space[3]}>
        {isSumAssuredShow && (
          <RiderTextField
            control={control}
            name={`riderPlans.${index}.sumAssured`}
            fieldName="sumAssured"
            riderDefinition={riderDefinition}
            label={t('proposal:rider.sumAssured')}
            disabled={isSumAssuredDisabled}
            currency={basePlanInfo?.currency}
            shouldUnregister
          />
        )}
        {isPolicyTermShow && (
          <RiderGenericField
            control={control}
            name={`riderPlans.${index}.policyTerm`}
            label={t('proposal:rider.policyTerm')}
            fieldName="policyTerm"
            onChange={val => {
              onChange('policyTerm', val);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${index}.policyTerm`,
              );
            }}
            disabled={isPolicyTermDisabled}
            riderDefinition={riderDefinition}
            unit={t('years')}
          />
        )}
        {isPremiumTermShow && (
          <RiderDropdownField
            control={control}
            name={`riderPlans.${index}.premiumTerm`}
            label={t('proposal:rider.premiumTerm')}
            fieldName="premiumTerm"
            onChange={val => {
              onChange('premiumTerm', val);
              inputEventListener?.onDropdownPicked?.(
                `riderPlans.${index}.premiumTerm`,
              );
            }}
            disabled={isPremiumTermDisabled}
            riderDefinition={riderDefinition}
            unit={t('years')}
          />
        )}
        {isTotalPremShow && (
          <RiderTextField
            control={control}
            name={`riderPlans.${index}.totalPrem`}
            fieldName="totalPrem"
            riderDefinition={riderDefinition}
            label={`${t('proposal:rider.totalPrem')}`}
            disabled={isTotalPremDisabled}
            currency={basePlanInfo?.currency}
            shouldUnregister
          />
        )}
      </Row>

      <Row
        alignItems="center"
        flexDirection="row"
        marginTop={space[2]}
        marginBottom={space[4]}>
        {isClassesShow && (
          <RiderSwitch
            control={control}
            name={`riderPlans.${index}.classes`}
            fieldName="classes"
            label={t('proposal:rider.optionalBenefit')}
            subLabel={t('proposal:rider.cashPaymentAndCashAllowance')}
            onChange={val => {
              onChange('classes', val);
              inputEventListener?.onBlur?.(`riderPlans.${index}.classes`);
            }}
            disabled={isClassesDisabled}
            riderDefinition={riderDefinition}
          />
        )}
      </Row>

      {subRiders?.length > 0 &&
        subRiders.map(rider => (
          <HajjSubRider
            key={rider?.position}
            control={control}
            riderDefinition={rider}
          />
        ))}
    </Column>
  );
}
