import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Card, Typography, XView } from 'cube-ui-components';
import { useRiderPlanInfo } from 'features/proposal/hooks/rider/useRiderPlanInfo';
import { getSubRiderList } from 'features/proposal/hooks/rider/useSubRider';
import { useQuotationCalculationStatus } from 'features/proposal/hooks/useQuotationCalculation';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import useRiderDefinitions from 'features/proposal/hooks/useRiderDefinitions';
import {
  RiderFormValues,
  SiFormValues,
  TopUpFilter,
} from 'features/proposal/types';
import { calculateDefaultSumAssured } from 'features/proposal/untils/getFormDefaultValues';
import riderDisplayCategoryFilter from 'features/proposal/untils/riderDisplayCategoryFilter';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import isEqual from 'lodash/isEqual';
import pick from 'lodash/pick';
import uniqBy from 'lodash/uniqBy';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  useFieldArray,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ViewProps } from 'react-native';
import { ProductId } from 'types/products';
import { Plan, RiderCode, RiderPlanFieldDefinition } from 'types/quotation';
import { cloneDeep } from 'utils/helper/objectUtil';
import AddRiderModal from '../AddRiderModal';
import StepTitle from '../StepTitle';
import ADDRider from './ADDRider';
import CIWaiverRider from './CIWaiverRider';
import ForAllRiderOptions from './ForAllRidersOptions';
import HajjRider from './HajjRider';
import MedicalPlusRider from './MedicalPlusRider';
import MedicalRider from './MedicalRider';
import MedikRider from './MedikRider';
import RiderForm from './RiderForm';
import RiderRow from './RiderRow';
import TPBRider from './TPBRider';

function mergeRiderPlanPayload(
  riderValues: (RiderFormValues | null)[],
  riderPlanInfo: (Plan | null)[],
): [boolean, SiFormValues['riderPlans']] {
  let isUpdated = false;
  const result: SiFormValues['riderPlans'] = [];

  riderPlanInfo.forEach((plan, index) => {
    if (!riderValues[index] && !plan) {
      result[index] = null;
    }

    // API response remove rider case
    if (!!riderValues[index] && !plan) {
      isUpdated = true;
      result[index] = null;
    }

    // API add new rider for form values
    if (!riderValues[index] && !!plan) {
      isUpdated = true;
      result[index] = cloneDeep(plan);
    }

    // merge the API response to existing form values
    if (!!riderValues[index] && !!plan) {
      const foundRider = cloneDeep(riderValues[index]);
      const properties: Array<keyof RiderFormValues> = [
        'policyTerm',
        'sumAssured',
        'planOption',
        'totalPrem',
        'premiumTerm',
        'premium',
        'basePrem',
        'sinceYear',
        'certificateYearFrom',
        'certificateYearTo',
        'classes',
        'insuredId',
        'roomAndBoard',
        'gcpOption',
      ];

      const pickedRider = pick(foundRider, properties);
      const pickedPlan = pick(plan, properties);

      if (foundRider && !isEqual(pickedRider, pickedPlan)) {
        isUpdated = true;

        Object.assign(foundRider, pickedPlan);
      }
      result[index] = foundRider;
    }
  });

  return [isUpdated, result];
}

export type RidersProps = {
  // react hook form fields for funds allocations
  setValue: UseFormSetValue<SiFormValues>;
  getValues: UseFormGetValues<SiFormValues>;
  control: Control<SiFormValues>;
  afterUpdateSumAssured?: (pid: string) => void;
  onRiderPlanUpdated: () => void;
  productId?: ProductId;
  isAllRidersMandatory: boolean;
  step?: number;
  sectionFilter?: TopUpFilter;
} & ViewProps;

export default function Rider({
  setValue,
  getValues,
  control,
  onRiderPlanUpdated,
  productId,
  isAllRidersMandatory,
  step,
  sectionFilter,
}: RidersProps) {
  const { t } = useTranslation(['proposal', 'common']);
  const { colors, space, borderRadius } = useTheme();
  const riderDefinitions = useRiderDefinitions();
  const { allParties, proposers } = useQuotationInsureds();
  const [isShowModal, setIsShowModal] = useState(false);
  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);

  const { isTabletMode } = useLayoutAdoptionCheck();

  const { fields } = useFieldArray({
    control,
    name: 'riderPlans',
  });
  const riderPlanInfo = useRiderPlanInfo();
  const riderPlanValues = useWatch({ control, name: 'riderPlans' });

  const alterationValue = useWatch({ control, name: 'alterations' });

  const isWaitingQuotationResult = useQuotationCalculationStatus();

  const mainInsuredId = allParties.find(i => i?.isMainInsured)?.insuredId;
  const proposerId = proposers[0]?.insuredId;

  useEffect(() => {
    if (Array.isArray(riderPlanInfo)) {
      const latestFormValues = getValues('riderPlans') ?? [];
      const [isUpdated, mergedPayload] = mergeRiderPlanPayload(
        latestFormValues,
        riderPlanInfo,
      );
      if (isUpdated) {
        setValue('riderPlans', mergedPayload);
      }
    }
  }, [riderPlanInfo, setValue, getValues]);

  const handleRiders = (newRiders: boolean[]) => {
    let isUpdated = false;

    newRiders.forEach((isSelected, index) => {
      if (isSelected && !riderPlanValues[index]) {
        console.log('add the rider for position: ' + index);
        const pid = riderDefinitions[index].pid;
        const newRiderValue: RiderFormValues = {
          pid,
          sumAssured: calculateDefaultSumAssured({
            planConfig: riderDefinitions[index].planConfig,
            plan: { pid } as Plan,
            isResumed: false,
            saRecommendation: rawQuotation?.saRecommendation,
          })[0],
        };

        switch (pid) {
          case RiderCode.MDCLPPR:
            newRiderValue.insuredId = mainInsuredId; // add mainInsuredId to Medical Rider
            break;
          case RiderCode.PYR:
          case RiderCode.PYRL:
          case RiderCode.PB8:
          case RiderCode.PB4:
          case RiderCode.GPB1:
          case RiderCode.ILPR:
            // if rider is Payor/Payor Lite Rider, set the insuredId to be proposerId
            newRiderValue.insuredId = proposerId;
            newRiderValue.payerInd =
              riderDefinitions[index]?.planConfig?.payerInd?.default;
            break;
          default:
            break;
        }

        setValue(`riderPlans.${index}`, newRiderValue);
        isUpdated = true;
        return;
      }

      if (!isSelected && !!riderPlanValues[index]) {
        console.log(
          'remove the rider for ' + JSON.stringify(riderPlanValues[index]),
        );
        setValue(`riderPlans.${index}`, null);
        isUpdated = true;
        return;
      }
    });

    if (isUpdated) {
      onRiderPlanUpdated();
    }
  };

  const addButtonVisible = useMemo(() => {
    return !(productId === 'ICP' || isAllRidersMandatory);
  }, [isAllRidersMandatory, productId]);

  const riderFormVisible = useMemo(() => {
    return !(productId === 'ICP' || isAllRidersMandatory);
  }, [isAllRidersMandatory, productId]);

  const riderList = useMemo(() => {
    const subRiderIds = getSubRiderList(riderDefinitions).map(
      rider => rider.pid,
    );
    let filteredList = fields
      .map((field, index) => {
        const definition = riderDefinitions[index];
        const value = riderPlanValues[index];
        return { field, value, definition, position: index };
      })
      .filter(({ definition, value }) => {
        // basic checking
        if (!value || !definition) {
          return false;
        }

        // if the rider is part of basic plan, then it should be always visible
        // override the disabled flag and embedInd flag
        if (definition?.properties?.partOfBasicPlan) {
          return true;
        }

        return (
          !definition.disabled &&
          !definition?.embedInd &&
          !definition?.properties?.embedInd &&
          !definition?.isBasicPlan &&
          subRiderIds.every(riderId => riderId !== definition.pid)
        );
      });

    // move CI Waiver Rider to the bottom of the list
    const ciWaiverRiderIndex = filteredList?.findIndex(
      item => item?.definition?.pid === RiderCode.CIWOC,
    );

    if (ciWaiverRiderIndex !== -1) {
      filteredList.push(filteredList.splice(ciWaiverRiderIndex, 1)[0]);
    }

    if (sectionFilter !== TopUpFilter.ALL) {
      filteredList = riderDisplayCategoryFilter({
        list: filteredList,
        isTopUpSection: sectionFilter === TopUpFilter.TOPUP_ONLY,
      });
    }

    return uniqBy(filteredList, 'definition.pid');
  }, [riderDefinitions, fields, sectionFilter, riderPlanValues]);

  const handleOnRiderRemoved =
    (definition: RiderPlanFieldDefinition, position: number) => () => {
      // hidden the remove button is rider is mandatory or
      if (definition?.mandatory || isWaitingQuotationResult > 0) {
        return null;
      }

      console.log('remove the rider for position: ' + position);
      setValue(`riderPlans.${position}`, null, {
        shouldValidate: false,
      });

      // remove charityOrgList from quotation alterations
      if (definition?.pid === RiderCode.TPB) {
        setValue('alterations', {
          ...alterationValue,
          charityOrgList: undefined,
        });
      }

      console.log(
        'remove the related sub-rider:',
        definition?.properties?.SubRiders,
      );
      riderDefinitions
        .filter(rider =>
          (definition?.properties?.SubRiders || '').includes(rider.pid),
        )
        .forEach(rider => {
          setValue(`riderPlans.${rider.position}`, null, {
            shouldValidate: false,
          });
        });

      onRiderPlanUpdated();
    };

  const onChange = useCallback(
    (position: number) =>
      (
        name: keyof RiderFormValues,
        v: number | string | boolean | undefined,
      ) => {
        setValue(`riderPlans.${position}.${name}`, v);
      },
    [setValue],
  );

  const Container = isTabletMode ? TabletContainer : PhoneContainer;

  return (
    <>
      <Container>
        <XView>
          <StepTitle
            step={step}
            titleText={`${
              sectionFilter === TopUpFilter.TOPUP_ONLY
                ? t('proposal:rider.topUpRiders')
                : t('proposal:rider.riders')
            }`}
            titleTextStyle={{
              color: colors.palette.black,
              fontWeight: '700',
              marginTop: space[1],
            }}
            style={{
              flexShrink: 1,
              alignItems: 'flex-start',
              marginRight: space[11],
            }}
          />
          {addButtonVisible && (
            <Button
              text={`${t('common:add')}`}
              style={{
                marginLeft: 'auto',
              }}
              contentStyle={{ paddingHorizontal: space[5] }}
              onPress={() => setIsShowModal(true)}
              variant="secondary"
            />
          )}
        </XView>

        {riderList.length === 0 ? (
          <Typography.H8
            color={colors.palette.fwdDarkGreen[50]}
            style={{
              paddingLeft:
                space[3] + // marginRight of StepIndicator
                (step
                  ? 38 // width of StepIndicator
                  : 0),
            }}>
            {sectionFilter === TopUpFilter.TOPUP_ONLY
              ? t('proposal:rider.addTopUp')
              : t('proposal:rider.addAdditionalProtection')}
          </Typography.H8>
        ) : (
          riderList.map(({ field, definition, position }, index) => {
            if (definition.pid === RiderCode.HAJJ) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  riderDefinition={definition}
                  onRemove={handleOnRiderRemoved(definition, position)}>
                  <HajjRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.ADDB) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  riderDefinition={definition}
                  onRemove={
                    !definition?.mandatory &&
                    handleOnRiderRemoved(definition, position)
                  }>
                  <ADDRider
                    control={control}
                    position={position}
                    riderDefinition={definition}
                    setValue={setValue}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.TPB) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  riderDefinition={definition}
                  onRemove={handleOnRiderRemoved(definition, position)}>
                  <TPBRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.CIWOC) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  onRemove={
                    !definition?.mandatory &&
                    handleOnRiderRemoved(definition, position)
                  }
                  riderDefinition={definition}>
                  <CIWaiverRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.ME11) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  onRemove={
                    !definition?.mandatory &&
                    handleOnRiderRemoved(definition, position)
                  }
                  riderDefinition={definition}>
                  <MedicalPlusRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.MEDIK) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  onRemove={
                    !definition?.mandatory &&
                    handleOnRiderRemoved(definition, position)
                  }
                  riderDefinition={definition}>
                  <MedikRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            if (definition.pid === RiderCode.MDCLPPR) {
              return (
                <RiderRow
                  key={field.id}
                  planName={`${index + 1}. ${definition?.label.en}`}
                  isLast={index === riderList.length - 1}
                  noForm={!riderFormVisible}
                  onRemove={
                    !definition?.mandatory &&
                    handleOnRiderRemoved(definition, position)
                  }
                  riderDefinition={definition}>
                  <MedicalRider
                    control={control}
                    index={position}
                    riderDefinition={definition}
                    setValue={setValue}
                    onChange={onChange(position)}
                  />
                </RiderRow>
              );
            }

            return (
              <RiderRow
                key={field.id}
                planName={`${index + 1}. ${definition?.label.en}`}
                isLast={index === riderList.length - 1}
                noForm={!riderFormVisible}
                onRemove={
                  !definition?.mandatory &&
                  handleOnRiderRemoved(definition, position)
                }
                riderDefinition={definition}>
                <RiderForm
                  control={control}
                  index={position}
                  riderDefinition={definition}
                  onChange={onChange(position)}
                />
              </RiderRow>
            );
          })
        )}

        {riderList.length > 0 && (
          <ForAllRiderOptions
            riderDefinition={riderList[0].definition}
            control={control}
            setValue={setValue}
          />
        )}
      </Container>

      <AddRiderModal
        formValues={riderPlanValues}
        visible={isShowModal}
        onClose={() => setIsShowModal(false)}
        onAddPressed={riders => {
          handleRiders(riders);
          setIsShowModal(false);
        }}
        topUpFilter={sectionFilter}
        title={
          sectionFilter === TopUpFilter.TOPUP_ONLY
            ? t('proposal:rider.topUpRiders')
            : t('proposal:rider.addRiders')
        }
      />
    </>
  );
}

const PhoneContainer = styled(Card)(({ theme: { space, borderRadius } }) => ({
  marginBottom: space[5],
  paddingVertical: space[6],
  paddingHorizontal: space[3],
  borderRadius: borderRadius.medium,
  elevation: 1,
}));

const TabletContainer = styled(Box)(({ theme: { space, borderRadius } }) => ({
  paddingVertical: space[6],
  paddingHorizontal: space[5],
  borderRadius: borderRadius.medium,
}));
