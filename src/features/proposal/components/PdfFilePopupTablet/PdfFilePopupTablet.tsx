import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { SiPdf } from 'features/proposal/types';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View, ViewProps } from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import PdfFileRow from './PdfFileRow';

type PdfFilePopupProps = {
  onPress?: (pdfType: SiPdf) => void;
  onClose?: () => void;
  pdfPopupItems: { title: string; fileType: SiPdf; disabled: boolean }[];
};

const Container = styled(View)<ViewProps & { height: number }>(
  ({ theme, height }) => ({
    backgroundColor: theme.colors.palette.fwdOrange[100],
    flex: 1,
    paddingTop: theme.space[4],
    height,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
  }),
);

const Header = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdOrange[100],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
  paddingBottom: theme.space[4],
  paddingHorizontal: theme.space[6],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const Body = styled.View(({ theme }) => ({
  marginHorizontal: theme.space[3],
  backgroundColor: theme.colors.background,
  flex: 1,
  paddingHorizontal: theme.space[3],
  paddingTop: theme.space[4],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const Divider = styled.View(({ theme: { colors, space } }) => ({
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  marginVertical: space[3],
}));

const Popup = styled.View<{ width: number }>(
  ({ theme: { space, elevation }, width }) => ({
    position: 'absolute',
    bottom: 0,
    margin: 0,
    justifyContent: 'flex-end',
    left: space[2],
    width,
    zIndex: 10,
    elevation: 10,
    ...elevation[10],
  }),
);

const HeaderTitle = styled(Typography.H7)(({ theme: { space } }) => ({
  marginLeft: space[2],
}));

const PdfFilePopupTablet = ({
  onClose,
  onPress,
  pdfPopupItems: items,
}: PdfFilePopupProps) => {
  const { colors } = useTheme();
  const { t } = useTranslation(['common', 'proposal']);

  const { width, height } = useSafeAreaFrame();
  return (
    <Popup width={Math.round((width * 2) / 5)}>
      <Container height={Math.round(height / 2.25)}>
        <Header>
          <Row alignItems="center">
            <Icon.DocumentCopy fill={colors.palette.white} size={24} />
            <HeaderTitle color={colors.palette.white} fontWeight="bold">
              {t('proposal:footer.previewPdf.pdfModal.header', {
                count: items.length,
              })}
            </HeaderTitle>
          </Row>

          <TouchableOpacity onPress={onClose}>
            <Icon.CloseCircleFill fill={colors.palette.white} size={24} />
          </TouchableOpacity>
        </Header>
        <Body>
          {items.map((file, index) => (
            <>
              <PdfFileRow
                key={file.title}
                title={file.title}
                disabled={file.disabled}
                onPress={() => onPress?.(file.fileType)}
              />
              {items.length !== index + 1 && <Divider />}
            </>
          ))}
        </Body>
      </Container>
    </Popup>
  );
};

export default PdfFilePopupTablet;
