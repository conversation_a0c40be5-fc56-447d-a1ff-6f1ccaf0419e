import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Column,
  Row,
  SmallLabel,
  TextField,
  Typography,
  addErrorBottomToast,
  addToast,
} from 'cube-ui-components';
import useSavePdf from 'features/pdfViewer/hooks/useSavePdf';
import { useSendEmail } from 'hooks/useSendEmail';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, ViewStyle } from 'react-native';
import { ModalProps } from 'react-native-modal';

import CustomModal from 'components/Modal/CustomModal';
import AttachmentItem from 'features/pdfViewer/components/AttachmentItem';
import { Attachment } from 'features/pdfViewer/components/EmailSharing/EmailSharing';
import { PdfViewerProps } from 'features/pdfViewer/components/PdfViewer';
import { View } from 'react-native';
import { country } from 'utils/context';
import { countryModuleSiConfig } from 'utils/config/module';

interface SendBenefitIllustrationModalProps extends Partial<ModalProps> {
  onCancel?: () => void;
  onEmailSent?: () => void;
  pdfInfo?: PdfViewerProps;
}

const MailBodyPreview = styled.ScrollView(
  ({
    theme: {
      space,
      components: { textField },
    },
  }) => {
    const { container } = textField;
    return {
      ...container,
      ...container.locked,
      height: 220,
      paddingVertical: space[2],
      paddingLeft: space[4],
      paddingRight: space[3],
    };
  },
);

const DEFAULT_PRODUCT_HIGHLIGHT_NAME = 'Product highlight sheet';
const DEFAULT_BROCHURE_PDF = 'Product brochure';

export default function SendBenefitIllustrationModal({
  onCancel,
  pdfInfo,
  onEmailSent,
  ...rest
}: SendBenefitIllustrationModalProps) {
  const theme = useTheme();

  const { t } = useTranslation([
    'common',
    'proposal',
    'coverageDetails',
    'pdfViewer',
  ]);

  const ownerEmail = pdfInfo?.mailConfig?.mailTo ?? '';
  const agentEmail = pdfInfo?.mailConfig?.mailCc ?? '';
  const subject = pdfInfo?.mailConfig?.subject ?? '';
  const content = pdfInfo?.mailConfig?.content ?? '';
  const html = pdfInfo?.mailConfig?.html ?? '';

  const attachments = useMemo(() => {
    const data = [
      {
        attachmentBase64: pdfInfo?.base64,
        attachmentUrl: pdfInfo?.url,
        attachmentName:
          pdfInfo?.attachmentName || t('proposal:salesIllustration'),
        type: PDF_TYPE.SI,
      },
    ];
    if (pdfInfo?.productHighlightUrl) {
      data.push({
        attachmentBase64: undefined,
        attachmentUrl: pdfInfo?.productHighlightUrl,
        attachmentName: DEFAULT_PRODUCT_HIGHLIGHT_NAME,
        type: PDF_TYPE.PRODUCT_HIGHLIGHT,
      });
    }

    if (pdfInfo?.brochureUrl) {
      data.push({
        attachmentBase64: undefined,
        attachmentUrl: encodeURI(pdfInfo?.brochureUrl),
        attachmentName: DEFAULT_BROCHURE_PDF,
        type: PDF_TYPE.PRODUCT_BROCHURE,
      });
    }

    return data;
  }, [pdfInfo]);

  const checkedAttachments = attachments.map(item => ({
    ...item,
    selected: true,
  }));

  const [mailTo, setMailTo] = useState<string>(ownerEmail || '');

  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [localAttachments, setLocalAttachments] =
    useState<Attachment[]>(checkedAttachments);

  const [prevAttachments, setPrevAttachments] = useState(attachments);

  const { pdfToBase64 } = useSavePdf();
  const { mutateAsync: sendEmail } = useSendEmail();

  if (attachments !== prevAttachments) {
    setPrevAttachments(attachments);
    setLocalAttachments(checkedAttachments);
  }

  const convertPdfToBase64 = async ({
    attachmentBase64,
    attachmentUrl,
    attachmentName,
  }: {
    attachmentBase64?: string;
    attachmentUrl?: string;
    attachmentName: string;
  }) => {
    let base64Pdf: string | null | undefined = attachmentBase64;

    if (!attachmentBase64 && attachmentUrl) {
      base64Pdf = await pdfToBase64({
        url: attachmentUrl,
        defaultName: attachmentName,
      });
    }

    if (base64Pdf) {
      return {
        fileName: `${attachmentName}.pdf`,
        base64: base64Pdf,
      };
    } else throw new Error();
  };

  const onSend = async () => {
    setIsSendingEmail(true);

    const convertPromises = localAttachments
      .filter(att => att.selected)
      .map(convertPdfToBase64);

    try {
      const base64Pdfs = await Promise.all(convertPromises);

      if (!Array.isArray(base64Pdfs) || base64Pdfs?.length === 0) {
        addErrorBottomToast([
          {
            message: t('pdfViewer:attachmentNotFound'),
          },
        ]);
        return;
      }

      const attachmentObj = base64Pdfs.reduce(
        (obj, { fileName, base64 }) => ({ ...obj, [fileName]: base64 }),
        {},
      );

      const { status } = await sendEmail({
        attachments: attachmentObj,
        emailToRecipients: [mailTo],
        emailCcRecipients: [agentEmail],
        emailBccRecipients: [],
        emailTitle: subject,
        emailBody: html,
        isHtmlText: true,
        isFIB: country === 'ib' ? 'Y' : 'N',
      });

      if (['200 OK', '200'].includes(status)) {
        onCancel?.();

        onEmailSent?.();

        setTimeout(() => {
          addToast([
            {
              message: t('pdfViewer:emailHasBeenSent'),
            },
          ]);
        }, 200);
      } else {
        throw new Error(t('pdfViewer:failedToSendEmail'));
      }
    } catch (e) {
      addErrorBottomToast([
        {
          message: t('pdfViewer:unableToSendEmail'),
        },
      ]);
    } finally {
      setIsSendingEmail(false);
    }
  };

  return (
    <CustomModal {...rest}>
      <Root
        nestedScrollEnabled={true}
        contentContainerStyle={{
          padding: theme.space[12],
        }}>
        <Typography.H6 fontWeight="bold" color={theme.colors.palette.black}>
          {t('proposal:sendBenefitIllustration.title')}
        </Typography.H6>

        <Container>
          <Row marginTop={theme.space[6]}>
            <Column flex={1} marginRight={theme.space[3]}>
              <TextField
                placeholder={t(
                  'proposal:sendBenefitIllustration.to.placeholder',
                )}
                label={t('proposal:sendBenefitIllustration.to')}
                inputStyle={
                  {
                    color: theme.colors.palette.fwdGreyDarker,
                  } as ViewStyle
                }
                onChangeText={text => setMailTo(text)}
                defaultValue={mailTo}
                disabled={!countryModuleSiConfig?.email?.isMailToEnabled}
              />
            </Column>
            <Column flex={1} marginLeft={theme.space[3]}>
              <TextField
                placeholder={t(
                  'proposal:sendBenefitIllustration.cc.placeholder',
                )}
                label={t('proposal:sendBenefitIllustration.cc')}
                inputStyle={
                  {
                    color: theme.colors.palette.fwdGreyDarker,
                  } as ViewStyle
                }
                value={agentEmail}
                editable={false}
                disabled
              />
            </Column>
          </Row>

          <Row marginTop={theme.space[6]}>
            <Column flex={1}>
              <TextField
                value={subject}
                label={t('proposal:sendBenefitIllustration.subject')}
                inputStyle={
                  {
                    color: theme.colors.palette.fwdGreyDarker,
                  } as ViewStyle
                }
                editable={false}
                disabled
              />
            </Column>
          </Row>

          <Row marginTop={theme.space[6]}>
            <Column flex={1}>
              <SmallLabel
                style={{
                  position: 'absolute',
                  top: -theme.space[2],
                  left: theme.space[3],
                  zIndex: 2,
                  backgroundColor: theme.colors.background,
                }}>
                {t('proposal:sendBenefitIllustration.yourMessage')}
              </SmallLabel>
              <MailBodyPreview nestedScrollEnabled={true}>
                <Typography.Body color={theme.colors.palette.fwdGreyDarker}>
                  {content}
                </Typography.Body>
              </MailBodyPreview>
            </Column>
          </Row>

          <Row marginTop={theme.space[6]}>
            <Typography.LargeLabel
              color={theme.colors.palette.fwdDarkGreen[100]}>
              {t('proposal:sendBenefitIllustration.attachment')}
            </Typography.LargeLabel>
          </Row>

          {localAttachments.map(att => (
            <AttachmentItem
              name={att.attachmentName}
              onToggleSelect={() => {
                setLocalAttachments(prev =>
                  prev.map(item => {
                    if (item.attachmentName === att.attachmentName)
                      return { ...item, selected: !item.selected };
                    return item;
                  }),
                );
              }}
              type={att.type}
              selected={att.selected}
            />
          ))}
        </Container>

        <Row
          justifyContent="center"
          alignItems="center"
          marginTop={theme.space[6]}>
          <Box marginRight={theme.space[4]}>
            <Button
              variant="secondary"
              text={t('common:cancel')}
              textStyle={{ paddingHorizontal: theme.space[18] }}
              onPress={onCancel}
            />
          </Box>

          <Button
            text={t('proposal:pdfModal.send')}
            textStyle={{ paddingHorizontal: theme.space[20] }}
            onPress={onSend}
            loading={isSendingEmail}
            disabled={!mailTo}
          />
        </Row>
      </Root>
    </CustomModal>
  );
}

const Root = styled(ScrollView)(
  ({ theme: { colors, borderRadius, space } }) => ({
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: borderRadius.small,
    marginHorizontal: space[40],
  }),
);

const Container = styled(View)(({ theme: { colors, borderRadius } }) => ({
  flex: 1,
  backgroundColor: colors.background,
  borderRadius: borderRadius.small,
}));
