import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Typography } from 'cube-ui-components';
import RiskArrowSVG from 'features/proposal/assets/RiskArrowSVG';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { t } from 'i18next';
import * as React from 'react';
import { View, ViewStyle } from 'react-native';
import { RPQResult } from 'types/quotation';
import { renderLabelByLanguage } from 'utils/helper/translation';

interface BoxScoreProps {
  name: string;
  value: string;
  styles?: ViewStyle;
}

const BoxScore = (props: BoxScoreProps) => {
  const { sizes, colors } = useTheme();
  const { name, value } = props;
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <View
      style={{
        flexDirection: 'column',
      }}>
      <View
        style={{
          height: sizes[isTabletMode ? 5 : 9],
          ...props.styles,
        }}>
        <RpqResultLabel>{name}</RpqResultLabel>
      </View>
      <Typography.H5
        fontWeight="bold"
        style={{
          color: colors.palette.fwdOrange[100],
        }}>
        {value}
      </Typography.H5>
    </View>
  );
};

const RiskInfo = ({
  rpqResult,
  style,
  isTabletMode,
}: {
  rpqResult: RPQResult;
  style: ViewStyle;
  isTabletMode?: boolean;
}) => {
  const { sizes } = useTheme();

  return (
    <RiskInfoContainer style={style}>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <BoxScore name={t('proposal:rpq.score')} value={rpqResult?.rpqScore} />
        <BoxScore
          name={t('proposal:rpq.riskLevel')}
          value={renderLabelByLanguage(rpqResult?.productRisk) ?? ''}
        />
        <BoxScore
          name={t('proposal:rpq.suitableProductRisk')}
          value={renderLabelByLanguage(rpqResult?.riskLevelLabel) ?? ''}
          styles={
            isTabletMode
              ? undefined
              : {
                  width: sizes[20],
                }
          }
        />
      </View>
    </RiskInfoContainer>
  );
};

const RiskChartComponent = ({ rpqResult }: { rpqResult: RPQResult }) => {
  const { sizes, colors } = useTheme();

  const YourArrow = ({ isActive }: { isActive: boolean }) => (
    <View
      style={{
        marginBottom: sizes[1],
        flexDirection: 'row',
        justifyContent: 'center',
      }}>
      <Box
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          opacity: isActive ? 1 : 0,
        }}>
        <Typography.H7
          fontWeight="bold"
          style={{
            color: colors.palette.fwdOrange[100],
            marginBottom: sizes[1],
          }}>
          {t('proposal:rpq.you')}
        </Typography.H7>
        <RiskArrowSVG />
      </Box>
    </View>
  );
  const RiskComponent = ({
    boxStyles,
    riskInfo,
    isActive,
  }: {
    boxStyles?: ViewStyle;
    riskInfo: string;
    isActive: boolean;
  }) => {
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
        }}>
        <YourArrow isActive={isActive} />
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
          }}>
          <View
            style={{
              flexDirection: 'column',
              flex: 1,
            }}>
            <Box
              style={{
                ...boxStyles,
                height: sizes[3],
              }}></Box>
            <Typography.SmallBody
              style={{
                textAlign: 'center',
                fontWeight: '400',
                color: colors.palette.fwdGreyDarker,
                marginTop: sizes[2],
              }}>
              {riskInfo}
            </Typography.SmallBody>
          </View>
        </View>
      </View>
    );
  };
  return (
    <View
      style={{
        flex: 1,
        flexDirection: 'row',
      }}>
      <RiskComponent
        riskInfo={t('proposal:rpq.conservative')}
        isActive={rpqResult?.riskLevel === 'L'}
        boxStyles={{
          backgroundColor: colors.palette.fwdOrange[20],
        }}
      />
      <RiskComponent
        riskInfo={t('proposal:rpq.balanced')}
        isActive={rpqResult?.riskLevel === 'M'}
        boxStyles={{
          backgroundColor: colors.palette.fwdOrange[50],
        }}
      />
      <RiskComponent
        riskInfo={t('proposal:rpq.aggressive')}
        isActive={rpqResult?.riskLevel === 'H'}
        boxStyles={{
          backgroundColor: colors.palette.fwdOrange[100],
        }}
      />
    </View>
  );
};

const RiskFormComponent = ({
  rpqResult,
  isRiskProfileReviewPage = false,
}: {
  rpqResult: RPQResult;
  isRiskProfileReviewPage?: boolean;
}) => {
  const { sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <>
      <RiskInfo
        rpqResult={rpqResult}
        style={{
          marginTop: sizes[isTabletMode ? 2 : 5],
        }}
        isTabletMode={isTabletMode}
      />
      {isRiskProfileReviewPage && <Space />}
      <RiskChartComponent rpqResult={rpqResult} />
    </>
  );
};

export default RiskFormComponent;

const RiskInfoContainer = styled.View(({ theme: { sizes } }) => ({
  flex: 1,
  marginTop: sizes[5],
}));

const RpqResultLabel = styled(Typography.SmallLabel)(
  ({ theme: { colors, sizes } }) => ({
    color: colors.palette.fwdGreyDarkest,
    fontWeight: '400',
    lineHeight: sizes[4],
  }),
);

const Space = styled.View(({ theme }) => ({
  height: theme.space[4],
}));
