import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Button, Typography } from 'cube-ui-components';
import useBoundStore from 'hooks/useBoundStore';
import { t, TFuncKey } from 'i18next';
import { View } from 'react-native';
import RiskFormComponent from './RiskForm';
import { RpqRiskResultProps } from './RpqRiskResultComponent';
import ReadMore from './RpqTextMore';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import useToggle from 'hooks/useToggle';
import useRpqPdf from './hooks/useRpqPdf';

const RpqRiskResultComponent = (props: RpqRiskResultProps) => {
  const { space } = useTheme();

  const rpqResult = useBoundStore(state => state.quotation?.rpqResult);

  const [pdfVisible, showPdf, hidePdf] = useToggle();
  const { pdfGeneratorRef } = useRpqPdf();

  return (
    <Container style={props.styles}>
      {props?.headerComponent}
      <Typography.H6 fontWeight="bold">
        {t('proposal:rpq.investmentRiskAnalysis')}
      </Typography.H6>

      {rpqResult && (
        <View
          style={{
            gap: space[5],
            marginBottom: space[10],
          }}>
          <RiskFormComponent rpqResult={rpqResult} />
        </View>
      )}

      <Typography.H7 fontWeight="bold" style={{ marginBottom: space[2] }}>
        {t('proposal:rpq.riskExplanation')}
      </Typography.H7>
      {!props.isReview ? (
        <ReadMore numLines={2} text={rpqResult?.policyStatement.en || ''} />
      ) : (
        <Typography.H7>
          {t(
            `proposal:rpq.riskLevelStatement.${rpqResult?.riskLevel}` as TFuncKey,
          )}
        </Typography.H7>
      )}

      {!props.hideViewPdf && (
        <Button
          text={t('proposal:rpq.viewPdf')}
          onPress={showPdf}
          contentStyle={{ paddingHorizontal: space[4], marginTop: space[3] }}
          variant="secondary"
        />
      )}

      <PdfViewer
        title={t('pdfViewer:RPQ')}
        onClose={hidePdf}
        visible={pdfVisible}
        pdfGenerator={pdfGeneratorRef.current}
      />
    </Container>
  );
};

export default RpqRiskResultComponent;

const Container = styled.View(({ theme: { colors, sizes } }) => ({
  backgroundColor: colors.palette.white,
  borderRadius: sizes[2],
}));
