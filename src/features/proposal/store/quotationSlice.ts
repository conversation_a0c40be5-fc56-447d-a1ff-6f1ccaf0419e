import { differenceInCalendarDays, parse } from 'date-fns';
import { StoreSlice } from 'types';
import { PartyType } from 'types/party';
import {
  Alterations,
  Insured,
  Plan,
  Proposer,
  Quotation,
  QuotationResponse,
  RiderPlanFieldDefinition,
  Summary,
  Warning,
  WarningCode,
} from 'types/quotation';
import { ObjectUtil } from 'utils';
import { StateCreator } from 'zustand';
import {
  CriticalErrorCode,
  CriticalErrorType,
  QuotationSlice,
  QuotationSliceState,
} from '../types';

function getAgeErrors(warnings?: Warning[]): CriticalErrorType[] | undefined {
  if (!warnings) return;

  const result: CriticalErrorType[] = [];

  for (const w of warnings) {
    if (/^proposers\.\d+\.age$/.test(w.id)) {
      const errorCode =
        w.code === WarningCode.INVALID_MIN
          ? CriticalErrorCode.OWNER_AGE_TOO_YOUNG
          : CriticalErrorCode.OWNER_AGE_TOO_OLD;
      result.push({ errorCode });
    }

    if (w.id === 'insured.age' || /^insured\.\d+\.age$/.test(w.id)) {
      const errorCode =
        w.code === WarningCode.INVALID_MIN
          ? CriticalErrorCode.INSURED_TOO_YOUNG
          : CriticalErrorCode.INSURED_TOO_OLD;
      result.push({ errorCode });
    }
  }

  return result.length > 0 ? result : undefined;
}

function detectExpiredQuotation(
  response: QuotationResponse,
): CriticalErrorType[] | undefined {
  if (!response.quotation.expiryDate) {
    return undefined;
  }

  const expiryDate = parse(
    response.quotation.expiryDate,
    'yyyy-MM-dd',
    new Date(),
  );
  if (isNaN(expiryDate.getTime())) {
    return undefined;
  }

  if (differenceInCalendarDays(expiryDate, Date.now()) >= 0) {
    return undefined;
  }

  return [{ errorCode: CriticalErrorCode.QUOTATION_EXPIRED }];
}

export function parseQuotationResponse(
  response: QuotationResponse,
): QuotationSliceState {
  const quotation = response.quotation;
  const plans = quotation.plans;
  const basicInfo = quotation.basicInfo;
  const planTemplate = response.template.template.options;
  const fundTemplate = response.template.optionsMap?.funds?.options;

  // Check plans length to ensure there are enough plans
  // TODO: no base plan handling
  const hasBasePlan = plans.length > 0;
  const hasRiderPlan = plans.length > 1;
  const hasFund = !!fundTemplate;
  const hasAlternations = !!quotation.alterations;
  const hasBasePlanDefinition = planTemplate.length > 0;
  const hasAlternationsDefinition =
    hasBasePlanDefinition && !!planTemplate[0].alterationConfig;
  const hasRiderPlanDefinition = planTemplate.length > 1;
  const hasFundDefinition = fundTemplate?.length > 0;
  const hasWarnings = (response.warnings?.length ?? 0) > 0;
  const isEntityFlow =
    quotation.proposers?.length > 0 &&
    quotation.proposers[0]?.clientType?.toUpperCase() ===
      PartyType.ENTITY.toUpperCase();

  let criticalErrors: CriticalErrorType[] | undefined;
  const ageErrors = getAgeErrors(response.warnings);
  if (ageErrors) {
    criticalErrors = [...ageErrors];
  }
  const expiredError = detectExpiredQuotation(response);
  if (expiredError) {
    criticalErrors = [...expiredError];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { fundsPieChartImage, ...rawQuotation } = response.quotation;

  const isVul = hasBasePlan
    ? plans?.[0].isVUL ?? planTemplate?.[0]?.planConfig?.isVUL?.default
    : false;

  return {
    // raw data without fundsPieChartImage
    rawQuotation,
    //  --------------------------------

    hasBasePlan,
    hasRiderPlan,
    hasFund,
    hasAlternations,
    hasBasePlanDefinition,
    hasRiderPlanDefinition,
    hasFundDefinition,
    hasErrors: hasWarnings,
    isEntityFlow,
    isFundTopUpSameAsContribution:
      quotation.fundsTopUpSameAsContribution ?? false,
    hasTopUpWithdrawal: quotation.hasTopUpWithdraw ?? false,
    isQuickSi: quotation.isQuickSI ?? false,
    isVul,
    riderPremfrBasic: quotation.riderPremfrBasic ?? false,

    basicInfo: basicInfo,
    rpqResult: quotation.rpq,
    alterations: quotation.alterations,
    quotationName: quotation.quotationName,
    quotationId: quotation.id,
    values: {
      summary: quotation.summary
        ? (ObjectUtil.cloneDeep(quotation.summary) as Summary)
        : undefined,
      basePlan: hasBasePlan
        ? (ObjectUtil.cloneDeep(plans[0]) as Plan)
        : undefined,
      riderPlans: hasRiderPlan ? plans.slice(1).map(plan => plan) : undefined,
      funds:
        hasFund && quotation.funds?.fundAllocation
          ? ObjectUtil.cloneDeep(quotation.funds?.fundAllocation)
          : undefined,
      alterations: hasAlternations
        ? ObjectUtil.cloneDeep(quotation.alterations)
        : undefined,
    },
    definitions: {
      basePlan: hasBasePlanDefinition ? planTemplate[0] : undefined,
      riderPlans: hasRiderPlanDefinition
        ? (planTemplate.slice(1) as RiderPlanFieldDefinition[]).map(
            (rpd, i) => ({ ...rpd, position: i }),
          )
        : undefined,
      funds: hasFundDefinition ? fundTemplate : undefined,
      alterations: hasAlternationsDefinition
        ? planTemplate[0].alterationConfig
        : undefined,
    },
    errors: hasWarnings ? response.warnings : undefined,
    criticalErrors,
    riskPrefValidationResult: quotation.riskPrefValidationResult,

    // helper on correct screen form value update actions
    // --------------------------------
    isAlterationsUpdated: false,
    isSavedQuotation: false,

    // quotation party info
    // --------------------------------
    insureds: quotation.insureds,
    proposers: quotation.proposers,

    // reminders
    // --------------------------------
    reminders: quotation.reminders,
  };
}

export const createQuotationSlice: StateCreator<
  StoreSlice,
  [],
  [],
  QuotationSlice
> = (set, get, api) => ({
  quotationActions: {
    setQuotationId: quotationId => {
      set(state => {
        if (!state.quotation) {
          state.quotation = {};
        }
        state.quotation.quotationId = quotationId;
        return state;
      });
    },

    update: data => {
      set(state => {
        const quotationId = state.quotation?.quotationId;
        const selectedFundStrategy = state.quotation?.selectedFundStrategy;
        state.quotation = parseQuotationResponse(data);
        state.quotation.quotationId = quotationId;
        state.quotation.selectedFundStrategy = selectedFundStrategy;
        return state;
      });
    },

    addParty: ({ party, partyRole }) => {
      set(state => {
        if (partyRole === 'Insured' && party.id) {
          state.quotation?.insureds?.push(party as Insured);
        }
        if (partyRole === 'Proposer' && party.id) {
          state.quotation?.proposers?.push(party as Proposer);
        }
        return state;
      });
    },

    removeParty: ({ partyId }) => {
      set(state => {
        if (state.quotation) {
          state.quotation.insureds = state.quotation?.insureds?.filter(
            i => i?.insuredId !== partyId,
          );
        }

        return state;
      });
    },

    updateParty: ({ party }) => {
      set(state => {
        if (Array.isArray(state?.quotation?.insureds)) {
          const index = state.quotation.insureds?.findIndex(
            i => i?.insuredId === party?.insuredId,
          );
          if (index !== -1) {
            state.quotation.insureds[index] = { ...party };
          }
        }

        return state;
      });
    },

    reset: () => {
      set(state => {
        state.quotation = undefined;
        return state;
      });
    },

    updateRpqResult: data => {
      set(state => {
        if (state.quotation) {
          state.quotation.rpqResult = { ...data };
        }

        return state;
      });
    },

    unsetRpqResult: () => {
      set(state => {
        if (state.quotation) {
          delete state.quotation.rpqResult;
          delete state.quotation.selectedFundStrategy;
        }
        return state;
      });
    },

    updateFundStrategy: data => {
      set(state => {
        if (state.quotation) {
          state.quotation.selectedFundStrategy = data;
        }

        return state;
      });
    },

    subscribeFundStrategy: callback => {
      const unsubscribe = api.subscribe((newState, oldState) => {
        if (
          newState.quotation?.selectedFundStrategy !==
          oldState.quotation?.selectedFundStrategy
        ) {
          callback(newState.quotation?.selectedFundStrategy);
        }
      });
      return unsubscribe;
    },

    updateAlterations: (alterations?: Alterations) => {
      set(state => {
        if (state.quotation) {
          state.quotation.alterations = alterations;
          state.quotation.isAlterationsUpdated = true;
        }
        return state;
      });
    },

    onSavedQuotation: (quotation: Quotation) => {
      set(state => {
        const { id, quotationName, proposalNum } = quotation;
        if (state.quotation && state.quotation.rawQuotation) {
          state.quotation.quotationId = id;
          state.quotation.quotationName = quotationName;
          state.quotation.isSavedQuotation = true;

          // temp patch the rawQuoation and proposalNum
          state.quotation.rawQuotation.id = id;
          state.quotation.rawQuotation.proposalNum = proposalNum;
        }
        return state;
      });
    },

    updateIsPdfViewed: isPdfViewed => {
      set(state => {
        if (state.quotation) state.quotation.isPdfViewed = isPdfViewed;
        return state;
      });
    },
  },
});
