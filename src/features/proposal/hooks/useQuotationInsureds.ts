import useSaveCoverageDetails from 'features/coverageDetails/hooks/common/useSaveCoverageDetails';
import useBoundStore from 'hooks/useBoundStore';
import { RelationshipValue } from '../types';
import { Gender, SmokingHabit } from 'types/person';
import { PartyType } from 'types/party';
import { useGetCase } from 'hooks/useGetCase';
import uniqBy from 'lodash/unionBy';
import { InsuredFormValues } from 'features/coverageDetails/validation/common/insuredSchema';
import { useDeleteParty } from 'hooks/useParty';

export default function useQuotationInsureds() {
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: activeCase } = useGetCase(caseId ?? '');

  const insureds = useBoundStore(state => state.quotation?.insureds) ?? [];
  const proposers = useBoundStore(state => state.quotation?.proposers) ?? [];
  const isEntityFlow = useBoundStore(state => state.quotation?.isEntityFlow);

  // need to combine insureds and proposers array
  // for some products, the insureds does not contain the proposer
  const allParties = uniqBy([...insureds, ...proposers], 'insuredId');

  const mainInsured = allParties.find(person => person?.isMainInsured);

  const owner = allParties.find(
    p =>
      p?.relationship === RelationshipValue.OWNER ||
      p?.clientType.toUpperCase() === PartyType.ENTITY,
  );

  const ownerIsInsured = !!owner?.isMainInsured;

  const { saveInsuredParty } = useSaveCoverageDetails({
    ownerIsInsured,
  });

  const { mutateAsync: deleteParty, isLoading: isDeletingParty } =
    useDeleteParty();

  const agentId = useBoundStore(state => state.auth.agentCode);

  const {
    addParty: addQuotationParty,
    removeParty: removeQuotationParty,
    updateParty: updateQuotationParty,
  } = useBoundStore(state => state.quotationActions);

  const saveQuotationInsured = async (insured: InsuredFormValues) => {
    if (!caseId) {
      throw new Error('caseId is undefined');
    }

    const insuredId = await saveInsuredParty({
      insured,
      caseId,
    });

    if (!insuredId) {
      throw new Error('insuredId is undefined');
    }

    if (!agentId) {
      throw new Error('agentId is undefined');
    }

    const party = {
      id: insuredId,
      agentId,
      insuredId,
      title: '',
      firstName: insured?.firstName ?? '',
      gender: insured?.gender as Gender,
      middleName: '',
      surname: '',
      lastName: '',
      fullName: insured?.firstName ?? '',
      extensionName: '',
      dob: insured.dob,
      clientType: PartyType.INDIVIDUAL,
      relationship: insured?.nanoRelationship,
      mobileNumber: '',
      mobileNumberCountryCode: '',
      email: '',
      createdDT: new Date(),
      updatedDT: new Date(),
      leadSourceCode: 'Self generated',
      smoker: insured?.smokingHabit as SmokingHabit,
      occClass: insured?.occupationClass,
      occGroupCode: insured?.occupationGroupCode,
    };

    const isExistingParty = allParties?.find(i => i?.id === insuredId);

    if (isExistingParty) {
      updateQuotationParty({ party });
    } else {
      addQuotationParty({
        partyRole: 'Insured',
        party,
      });
    }
    return insuredId;
  };

  const deleteQuotationInsured = async (insuredId: string | undefined) => {
    if (!insuredId) {
      throw new Error('No insuredId found');
    }

    if (!caseId) {
      throw new Error('No quotation found');
    }
    // remove coverage person from quotation insureds[]
    await deleteParty({ caseId, partyId: insuredId });

    removeQuotationParty({ partyId: insuredId });
  };

  return {
    insureds,
    proposers,
    allParties,
    owner,
    mainInsured,
    ownerIsInsured,
    isEntityFlow,
    saveQuotationInsured,
    deleteQuotationInsured,
    activeCase,
    isDeletingParty,
  };
}
