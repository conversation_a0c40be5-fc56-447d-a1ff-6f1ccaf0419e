import useBoundStore from 'hooks/useBoundStore';
import { useEffect } from 'react';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { Plan, WarningCode } from 'types/quotation';
import { BASE_PLAN_FORM_KEYS } from '../components/SalesIllustrationDetailsModal/constants';
import { SiFormValues } from '../types';
import { cloneDeep } from 'lodash';

const useBasePlanFormValues = ({
  setValue,
  getValues,
  pid,
}: {
  setValue: UseFormSetValue<SiFormValues>;
  getValues: UseFormGetValues<SiFormValues>;
  pid: string;
}) => {
  const rawQuotationStoreBasePlan = useBoundStore(
    state => state.quotation?.values?.basePlan,
  );

  const quotationStoreErrors = useBoundStore(state => state.quotation?.errors);

  useEffect(() => {
    if (rawQuotationStoreBasePlan) {
      const latestBasePlanFormValues = getValues('basePlan') ?? {};

      const newBasePlanValues = cloneDeep(latestBasePlanFormValues);

      BASE_PLAN_FORM_KEYS.forEach(property => {
        if (
          rawQuotationStoreBasePlan?.[property as keyof Plan] !==
          latestBasePlanFormValues?.[property]
        ) {
          Object.assign(newBasePlanValues, {
            [property]: rawQuotationStoreBasePlan[property as keyof Plan],
          });
        }
      });

      /* reset base plan inputs only if error.pid is the base plan pid
         base plan pid = quotation pid
      */

      if (quotationStoreErrors) {
        quotationStoreErrors.forEach(error => {
          if (error?.pid === pid && error.code === WarningCode.INVALID_VALUE) {
            Object.assign(newBasePlanValues, {
              [error.id]: undefined,
            });
          }
        });
      }

      setValue('basePlan', newBasePlanValues, {
        shouldValidate: false,
      });
    }
  }, [
    pid,
    quotationStoreErrors,
    rawQuotationStoreBasePlan,
    getValues,
    setValue,
  ]);
};

export default useBasePlanFormValues;
