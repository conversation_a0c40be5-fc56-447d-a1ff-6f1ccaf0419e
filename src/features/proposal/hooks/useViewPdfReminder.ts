import { useCallback, useEffect, useState } from 'react';

const useViewPdfReminder = ({ isDisabled }: { isDisabled: boolean }) => {
  const [isShowPdfReminder, setShowPdfReminder] = useState<boolean>(false);

  useEffect(() => {
    if (!isDisabled) {
      setShowPdfReminder(true);
    }
  }, [isDisabled]);

  const handleOnCloseReminder = useCallback(() => {
    setShowPdfReminder(false);
  }, [setShowPdfReminder]);

  return {
    isShowPdfReminder,
    handleOnCloseReminder,
  };
};

export default useViewPdfReminder;
