import { useMutation } from '@tanstack/react-query';
import { saveQuotation } from 'api/quotationApi';
import { UpsertQuotationArgs } from '../types';

export default function useSaveQuotation({
  onSuccess,
}: {
  onSuccess?:
    | ((data: string, variables: UpsertQuotationArgs, context: unknown) => void)
    | undefined;
}) {
  return useMutation({
    mutationFn: ({ caseId, quotationName, quotation }: UpsertQuotationArgs) =>
      saveQuotation(
        {
          ...quotation,
          quotationName,
        },
        caseId,
      ),
    onSuccess,
  });
}
