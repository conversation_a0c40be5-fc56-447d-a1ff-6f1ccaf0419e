import { TranslationField } from 'types';
import { PaymentMode } from 'types/proposal';
import {
  Alterations,
  BasePlanFieldDefinition,
  BasicInfo,
  CharityOrg,
  FundAllocation,
  FundOption,
  Insured,
  Plan,
  Proposer,
  Quotation,
  QuotationResponse,
  RPQResult,
  RiderPlanFieldDefinition,
  RiskPrefValidationResult,
  Summary,
  Warning,
} from 'types/quotation';

export enum TopUpFilter {
  TOPUP_ONLY = 'topUpOnly',
  NON_TOPUP_ONLY = 'nonTopUpOnly',
  ALL = 'all',
}

export enum CriticalErrorCode {
  INSURED_TOO_YOUNG = 'InsuredTooYoung',
  INSURED_TOO_OLD = 'InsuredTooOld',
  OWNER_AGE_TOO_YOUNG = 'OwnerAgeTooYoung',
  OWNER_AGE_TOO_OLD = 'OwnerAgeTooOld',
  QUOTATION_EXPIRED = 'QuotationExpired',
}

export type CriticalErrorType = {
  errorCode: CriticalErrorCode;
};

export enum CalculationBy {
  SUM_ASSURED = 'SumAssured',
  PREMIUM = 'Premium',
}

export enum RiskLevel {
  ALL = 'ALL',
  LOW = 'L',
  MID = 'M',
  HIGHT = 'H',
}

export enum RiskPreference {
  CONSERVATIVE = 'Conservative',
  MODERATELY_CONSERVATIVE = 'Moderate Conservative',
  BALANCED = 'Balance',
  MODERATELY_AGGRESSIVE = 'Moderate Aggressive',
  AGGRESSIVE = 'Aggressive',
}

// 'LO' | 'LM' | 'ME' | 'MH' | 'HI'
export enum FundStrategy {
  LOW = 'LO',
  LOW_MEDIUM = 'LM',
  MEDIUM = 'ME',
  MEDIUM_HIGH = 'MH',
  HIGH = 'HI',
  MEDIUMI = 'MI',
}

export enum FundType {
  ALL = 'ALL',
  EQUITY = 'EQUITY',
  MANAGED = 'MANAGED',
  BOND = 'BOND',
}

export enum CalculateBy {
  BY_MODAL_PREMIUM = 'BY_MODAL_PREMIUM',
  BY_SUM_ASSURED = 'BY_SUM_ASSURED',
}

export enum SiPdf {
  PRODUCT_BROCHURE = 'Product brochure',
  TIPS_PRODUCT_BROCHURE = 'TIPS Brochure',
  SI_PROPOSAL = 'SI proposal',
  PRODUCT_HIGHLIGHTS = 'Product highlights',
  RIPLAY = 'RIPLAY',
}

export type OptionConfig<T> = {
  value: T;
  label: TranslationField;
};

export type ConfigDataType =
  | 'string'
  | 'number'
  | 'options'
  | 'date'
  | 'age'
  | 'boolean';

export type FieldDefinition<T, TKey = string> = {
  key: TKey;
  dataType: ConfigDataType;
  options?: OptionConfig<T>[];
  mandatory?: boolean;
  hidden?: boolean;
  disabled?: boolean;
  default?: T;
  label?: TranslationField;
};

export type BasePlanFormValues = {
  premium?: number;
  sumAssured?: number;
  basePrem?: number;
  paymentMode?: PaymentMode;
  premiumTerm?: number; // Previously paymentTerm
  flatLoading?: number; // Previously flatExtra
  flatDuration?: number;
  percLoading?: string; // Previously multiplier
  forfeiture?: string;
  premiumType?: string;
  policyTerm?: number;
  annualizedPrem?: number;
  paymentType?: string;
  payoutOption?: string;
  annualPrem?: number;
  classes?: string;
  totalPrem?: number;
  deathBenefitOption?: string;
  familyCrisisCover?: number;
  customerSegment?: string;
  contributionTerm?: string;
  regularPayout?: string;
  expiryAge?: number;
  calcBy?: string;
  autoBalanceOption?: string;
  totalAnnualPrem?: number;
  saverAnnualPrem?: number;
  totalInitialPrem?: number;
  // fields for base plan that has backdate
  backDate?: Date | string;
  toBackdate?: string;
  maxPolicyTerm?: number;
  maxPremiumTerm?: number;
  gcpOption?: string;
  planOption?: string;

  minReturnOfPrem?: string;
  primarySubsidiaryInd?: string;
  unit?: string;
  needTargetAV?: string;
  targetAVMin?: number;
  targetAVPolicyYear?: number;
  targetAVPreferredScenario?: string;
  iMaxAnnualPrem?: number;
  code?: string;
  currency?: string;
  livingBenefitAge?: number;
  morbidityFlatLoading?: number;
  morbidityPercLoading?: number;
};

export type RiderFormValues = {
  pid: string;
  planCode?: string;
  premium?: number;
  policyTerm?: number;
  sumAssured?: number;
  flatLoading?: string; // Previously flatExtra
  flatDuration?: number;
  percLoading?: string; // Previously multiplier
  planOption?: number;
  premiumTerm?: number;
  totalPrem?: number;
  basePrem?: number;
  sinceYear?: number;
  certificateYearFrom?: number;
  certificateYearTo?: number;
  classes?: string;
  adbWaiver?: boolean;
  hajjWaiver?: boolean;
  termWaiver?: boolean;
  medicalWaiver?: boolean;
  addbWaiver?: boolean;
  medicalPlusWaiver?: boolean;
  waivePIDList?: boolean;
  annualPrem?: number;
  annualIncome?: number;
  constent1?: boolean;
  constent2?: boolean;
  selected?: boolean;
  deductible?: string;
  roomAndBoard?: number;
  plan?: string;
  executivePlan?: string;
  insuredId?: string;
  insuredIndex?: number;
  payerInd?: boolean;
  includePlusRider?: string;
  rop?: string;
  retirementOption?: string;
  gcpOption?: string;
  monthlyIncome?: string;
  sumAssuredOption?: string;
  morbidityPercLoading?: number;
};

export type FundFormValues = FundOption & {
  allocation?: number;
  topUp?: number;
  unallocated?: boolean;
};

export type ForAllRiderValues = {
  riderNFO?: string;
};

export enum InvestmentPreferenceReasonOption {
  HIGH = 'HighRisk',
  LOW = 'LowRisk',
  OTHER = 'Others',
}

export type FundValidationFeedback = {
  investmentPreference?: InvestmentPreferenceReasonOption;
  comments?: string;
};

export interface SiFormValues {
  basePlan: BasePlanFormValues;
  riderPlans: Array<RiderFormValues | null>;
  fundsAllocation: FundFormValues[];
  alterations?: Alterations;
  quotationId?: string;
  quotationName?: string;
  forAllRiderValues?: ForAllRiderValues;
  riskPref?: RiskPreference;
}

export interface CharityFormValues {
  charityOrgList?: Array<CharityOrg | null>;
}

export type QuotationSliceState = Partial<{
  // Active Quotation Id
  quotationId: string;

  // Raw Data from API
  rawQuotation: Quotation;

  // ----------------
  // Flags for if fields are presented
  hasBasePlan: boolean;
  hasRiderPlan: boolean;
  hasFund: boolean;
  hasAlternations: boolean;
  hasBasePlanDefinition: boolean;
  hasRiderPlanDefinition: boolean;
  hasFundDefinition: boolean;
  hasErrors: boolean;
  isEntityFlow: boolean;
  isFundTopUpSameAsContribution?: boolean;
  hasTopUpWithdrawal?: boolean;
  isQuickSi?: boolean;
  isVul?: boolean;
  riderPremfrBasic?: boolean;

  basicInfo: BasicInfo;
  values: QuotationCalculationValue;
  errors?: Warning[];
  criticalErrors?: CriticalErrorType[];

  // template.template related data
  definitions: {
    basePlan?: BasePlanFieldDefinition;
    riderPlans?: RiderPlanFieldDefinition[];
    funds?: FundOption[];
    alterations?: BasePlanFieldDefinition['alterationConfig'];
  };

  rpqResult?: RPQResult;
  selectedFundStrategy?: FundStrategy;

  alterations?: Alterations;

  quotationName?: string;

  riskPrefValidationResult?: RiskPrefValidationResult;
  fundValidationFeedback: FundValidationFeedback;

  // helper values for cross screen form value update
  isAlterationsUpdated?: boolean;
  isSavedQuotation?: boolean;
  isPdfViewed: boolean;

  // parties related data
  insureds: Insured[];
  proposers: Proposer[];

  // reminders
  reminders?: Quotation['reminders'];
}>;

export type QuotationSlice = {
  quotation?: QuotationSliceState;
  quotationActions: {
    setQuotationId: (quotationId: string) => void;
    update: (data: QuotationResponse) => void;
    addParty: (params: {
      party: Insured | Proposer;
      partyRole: 'Insured' | 'Proposer';
    }) => void;
    removeParty: ({ partyId }: { partyId: string }) => void;
    updateParty: ({ party }: { party: Insured }) => void;
    reset: () => void;
    updateRpqResult: (data: RPQResult) => void;
    unsetRpqResult: () => void;
    updateFundStrategy: (fundStrategy?: FundStrategy) => void;
    subscribeFundStrategy: (
      callback: (fundStrategy?: FundStrategy) => void,
    ) => () => void;
    updateAlterations: (alterations?: Alterations) => void;
    onSavedQuotation: (quotation: Quotation) => void;
    updateIsPdfViewed: (isViewed: boolean) => void;
  };
};

export type Flags = {
  isFundTopUpSameAsContribution?: boolean;
  hasTopUpWithdrawal?: boolean;
  isQuickSi?: boolean;
  riderPremfrBasic?: boolean;
};

export type QuotationCalculationValue = {
  summary?: Summary;
  basePlan?: Plan;
  riderPlans?: (Plan | null)[];
  funds?: FundAllocation[];
  alterations?: Alterations;
};

export type LabelValueOption<T> = {
  label: string;
  value: T;
};

export type UpsertQuotationArgs = {
  caseId: string;
  quotationName: string;
  quotation: Partial<Quotation> & { isSelectedQuotation?: boolean };
};

export enum RelationshipValue {
  EMPLOYEE = 'EMPLOYEE',
  CHILD = 'CHILD',
  SPOUSE = 'SPOUSE',
  OWNER = 'SELF',
  ENTITY_OWNER = 'OTHER',
}

export enum DisplayCategories {
  TOP_UP = 'topUp',
}

export const LIST_PRODUCT_SHOW_RIDER_TOP_UP = ['ILB', 'ILBP'];

export enum SiNextButtonAction {
  SEND_EMAIL,
  PROCEED_NEXT_STEP,
  VIEW_PDF,
}
