import { useTheme } from '@emotion/react';
import { Box, ProgressBar, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {} from 'react-native';
import { country } from 'utils/context';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { ColoredDotIndicator } from './RecognitionDetailsAchievementTablet';

export default function AchievementBarWithTargetAndShortFallTablet({
  percent,
  completion,
  shortfall,
  target,
}: {
  percent: number;
  completion: number;
  shortfall: number;
  target: number;
}) {
  const { sizes, colors, borderRadius } = useTheme();
  const isAchieved = percent >= 100;
  const displayPercent = percent < 1 ? percent * 100 : percent;
  const { t: ct } = useTranslation('common');
  const { t: pt } = useTranslation('performance');

  return (
    <Box>
      <Row style={{ justifyContent: 'space-between', paddingTop: sizes[4] }}>
        <Typography.H7
          fontWeight={'bold'}
          color={isAchieved ? colors.palette.alertGreen : colors.primary}>
          {displayPercent.toFixed(0)}%{' '}
          {pt('performance.recognition.mdrtComplete')}
        </Typography.H7>
        <Typography.Label color={colors.palette.fwdGreyDarker}>
          {pt('performance.recognition.target')}
          {': '}
          {ct('currencySymbol') + ' ' + numberToThousandsFormat(target)}
        </Typography.Label>
      </Row>
      <Box
        minH={sizes[4]}
        py={sizes[4]}
        borderRightColor={colors.palette.fwdGreyDark}
        justifyContent={'flex-end'}>
        <Row position="relative">
          {country === 'ib' && (
            <Row
              zIndex={1}
              h={20}
              w={'100%'}
              position="absolute"
              alignItems="center"
              justifyContent="space-between">
              <Box w={18} />
              <Box
                backgroundColor={colors.surface}
                justifyContent="center"
                alignItems="center"
                borderRadius={borderRadius.full}
                h={36}
                w={36}>
                <Typography.Label fontWeight="bold">25%</Typography.Label>
              </Box>
              <Box
                backgroundColor={colors.surface}
                justifyContent="center"
                alignItems="center"
                borderRadius={borderRadius.full}
                h={36}
                w={36}>
                <Typography.Label fontWeight="bold">50%</Typography.Label>
              </Box>
              <Box
                backgroundColor={colors.surface}
                justifyContent="center"
                alignItems="center"
                borderRadius={borderRadius.full}
                h={36}
                w={36}>
                <Typography.Label fontWeight="bold">75%</Typography.Label>
              </Box>
              <Box
                backgroundColor={colors.surface}
                justifyContent="center"
                alignItems="center"
                borderRadius={borderRadius.full}
                h={36}
                w={36}>
                <Typography.Label fontWeight="bold">100%</Typography.Label>
              </Box>
            </Row>
          )}
          <ProgressBar
            progress={percent === 0 ? 0.5 : percent}
            height={sizes[4]}
            type={'basic'}
            containerStyle={{
              backgroundColor: colors.surface,
            }}
          />
        </Row>
      </Box>
      <Row style={{ justifyContent: 'space-between' }}>
        <Row style={{ minWidth: 88, gap: sizes[1], alignItems: 'center' }}>
          <ColoredDotIndicator
            fill={isAchieved ? colors.palette.alertGreen : colors.primary}
          />
          <Row style={{ gap: sizes[1], alignItems: 'center' }}>
            <Typography.Label color={colors.palette.fwdGreyDarker}>
              {pt('performance.recognition.completion')}
            </Typography.Label>
            <Typography.LargeLabel color={colors.secondary}>
              {ct('currencySymbol') + ' ' + numberToThousandsFormat(completion)}
            </Typography.LargeLabel>
          </Row>
        </Row>
        <Row style={{ minWidth: 88, gap: sizes[1], alignItems: 'center' }}>
          <ColoredDotIndicator fill={colors.surface} />
          <Row style={{ gap: sizes[1], alignItems: 'center' }}>
            <Typography.Label color={colors.palette.fwdGreyDarker}>
              {pt('performance.recognition.shortfall')}
            </Typography.Label>
            <Typography.LargeLabel color={colors.secondary}>
              {ct('currencySymbol') + ' ' + numberToThousandsFormat(shortfall)}
            </Typography.LargeLabel>
          </Row>
        </Row>
      </Row>
    </Box>
  );
}
