import React, { memo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetFooter,
  useBottomSheetInternal,
  KEYBOARD_STATE,
} from '@gorhom/bottom-sheet';
import { But<PERSON>, Row } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { FnaRejectRecommendedReason } from 'types/case';

export interface FooterProps extends BottomSheetFooterProps {
  reason?: FnaRejectRecommendedReason;
  handleClose: () => void;
  onConfirm: (reason: FnaRejectRecommendedReason) => void;
}

const ClientReasonFooter = memo(
  ({ reason, handleClose, onConfirm, ...props }: FooterProps) => {
    const { t } = useTranslation(['product', 'common']);
    const { space, colors } = useTheme();
    const bottomSheetInternal = useBottomSheetInternal(true);
    const animatedFooterHeight = bottomSheetInternal?.animatedFooterHeight;
    const animatedKeyboardState = bottomSheetInternal?.animatedKeyboardState;

    const [footerHeight, setFooterHeight] = useState(
      Math.max(animatedFooterHeight?.value || 0, 0),
    );

    const animatedStyle = useAnimatedStyle(
      () => ({
        transform: [
          {
            translateY:
              animatedKeyboardState?.value === KEYBOARD_STATE.SHOWN
                ? footerHeight
                : 0,
          },
        ],
      }),
      [animatedKeyboardState?.value, footerHeight],
    );

    const { isNarrowScreen } = useWindowAdaptationHelpers();
    const { bottom: bottomInset } = useSafeAreaInsets();

    return (
      <BottomSheetFooter {...props}>
        <Animated.View
          onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
          style={[
            animatedStyle,
            {
              paddingHorizontal: space[isNarrowScreen ? 3 : 4],
              paddingTop: space[4],
              paddingBottom: space[4] + bottomInset,
              backgroundColor: colors.background,
              borderTopWidth: 1,
              borderColor: colors.palette.fwdGrey[100],
              flexDirection: 'row',
              justifyContent: 'center',
            },
          ]}>
          <Row justifyContent="center" gap={space[4]}>
            <Button
              variant="secondary"
              text={t('product:back')}
              onPress={handleClose}
              style={{ width: space[41] }}
            />
            <Button
              text={t('product:continue')}
              onPress={() => {
                if (reason) {
                  onConfirm(reason);
                  handleClose();
                }
              }}
              style={{ width: space[41] }}
              disabled={
                reason === undefined ||
                reason === FnaRejectRecommendedReason.NOT_REJECT
              }
            />
          </Row>
        </Animated.View>
      </BottomSheetFooter>
    );
  },
);

export default ClientReasonFooter;