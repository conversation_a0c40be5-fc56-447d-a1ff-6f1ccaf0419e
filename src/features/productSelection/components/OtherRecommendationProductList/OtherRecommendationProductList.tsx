import {
  View,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
  LayoutChangeEvent,
  StyleProp,
  ViewStyle,
  useWindowDimensions,
} from 'react-native';
import React, { useState } from 'react';
import { Box, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { ListProduct, Product } from 'types/products';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ProductCard from 'features/productSelection/components/ProductCard';

export type OtherRecommendationProductListProps = {
  sections: ListProduct[];
  onSectionLayout: (index: number) => (event: LayoutChangeEvent) => void;
  onScroll: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollViewRef: React.RefObject<ScrollView>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  onPressItemSelect: (p: Product) => void;
  isNarrowScreen?: boolean;
};

const Container = styled.View(({ theme: { space, colors } }) => ({
  flex: 1,
  paddingBottom: space[4],
  backgroundColor: colors.palette.fwdGrey[50],
}));

const OtherRecommendationProductList = ({
  sections,
  onSectionLayout,
  onScroll,
  scrollViewRef,
  contentContainerStyle,
  onPressItemSelect,
  isNarrowScreen,
}: OtherRecommendationProductListProps) => {
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { width } = useWindowDimensions();
  const [productListWidth, setProductListWidth] = useState<number[]>([]);

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={onScroll}
        ref={scrollViewRef}
        style={{
          minHeight: isTabletMode ? 323 : 304,
        }}
        contentContainerStyle={contentContainerStyle}>
        {sections.map((section, sectionIndex) => (
          <View
            key={section.title}
            style={{
              paddingTop: space[isTabletMode ? 5 : 6],
              minHeight: isTabletMode ? 323 : 304,
            }}
            onLayout={onSectionLayout(sectionIndex)}>
            <Typography.H6
              fontWeight="bold"
              style={{ paddingHorizontal: space[isTabletMode ? 6 : 4] }}>
              {section.title} ({section.data.length})
            </Typography.H6>
            <ScrollView
              showsHorizontalScrollIndicator={false}
              style={{
                paddingTop: space[isTabletMode ? 6 : 4],
              }}
              onContentSizeChange={w => {
                setProductListWidth(prev => [...prev, w]);
              }}
              contentContainerStyle={{
                paddingLeft: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 3 : 4],
                paddingRight: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 1 : 2],
              }}
              horizontal>
              {section.data.map((item, sectionDataIndex) => {
                const isLast = sectionDataIndex === section.data.length - 1;
                return (
                  <Box
                    mr={space[3]}
                    key={`${item.pid}_%{index}`}
                    zIndex={999 - sectionDataIndex}
                    overflow="visible">
                    <ProductCard
                      {...item}
                      onPress={onPressItemSelect}
                      style={{ flex: 1 }}
                      isInRecommendation
                      popUpDirection={
                        productListWidth[sectionIndex] > width && isLast
                          ? 'left'
                          : 'right'
                      }
                    />
                  </Box>
                );
              })}
            </ScrollView>
          </View>
        ))}
      </ScrollView>
    </Container>
  );
};

export default OtherRecommendationProductList;
