import React, { memo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetFooter,
  useBottomSheetInternal,
  KEYBOARD_STATE,
} from '@gorhom/bottom-sheet';
import { Box, Button, Row } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';

export interface FooterProps extends BottomSheetFooterProps {
  onGetFnaDoc: () => void;
  handleClose: () => void;
  onConfirm: () => void;
  disabled: boolean;
  waiverChecked: boolean;
  onToggleWaiverChecked: (value: boolean) => void;
  providedServiceGuideChecked: boolean;
  onToggleServiceGuideChecked: (value: boolean) => void;
  isGettingFnaDoc: boolean;
  isSaving: boolean;
  isViewedDoc: boolean;
}

const ClientWaiverFooter = memo(
  ({
    onGetFnaDoc,
    handleClose,
    onConfirm,
    disabled,
    waiverChecked,
    onToggleWaiverChecked,
    providedServiceGuideChecked,
    onToggleServiceGuideChecked,
    isGettingFnaDoc,
    isSaving,
    isViewedDoc,
    ...props
  }: FooterProps) => {
    const { t } = useTranslation(['product', 'common']);
    const theme = useTheme();
    const isPrimaryLoading = isSaving && !isGettingFnaDoc;
    const isSecondaryLoading = isGettingFnaDoc;
    const { space, colors } = useTheme();
    const bottomSheetInternal = useBottomSheetInternal(true);
    const animatedFooterHeight = bottomSheetInternal?.animatedFooterHeight;
    const animatedKeyboardState = bottomSheetInternal?.animatedKeyboardState;

    const [footerHeight, setFooterHeight] = useState(
      Math.max(animatedFooterHeight?.value || 0, 0),
    );

    const animatedStyle = useAnimatedStyle(
      () => ({
        transform: [
          {
            translateY:
              animatedKeyboardState?.value === KEYBOARD_STATE.SHOWN
                ? footerHeight
                : 0,
          },
        ],
      }),
      [animatedKeyboardState?.value, footerHeight],
    );

    const { isNarrowScreen } = useWindowAdaptationHelpers();
    const { bottom: bottomInset } = useSafeAreaInsets();

    return (
      <BottomSheetFooter {...props}>
        <Animated.View
          onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
          style={[
            animatedStyle,
            {
              paddingHorizontal: space[isNarrowScreen ? 3 : 4],
              paddingTop: space[4],
              paddingBottom: space[4] + bottomInset,
              backgroundColor: colors.background,
              borderTopWidth: 1,
              borderColor: colors.palette.fwdGrey[100],
              flexDirection: 'row',
              justifyContent: 'center',
            },
          ]}>
          <Row gap={theme.space[4]} flex={1}>
            {isViewedDoc ? (
              <>
                <Button
                  variant="secondary"
                  text={t('product:previewCFF')}
                  onPress={onGetFnaDoc}
                  loading={isSecondaryLoading}
                  disabled={isSecondaryLoading}
                  style={{ flex: 1 }}
                />
                <Button
                  disabled={disabled || isPrimaryLoading}
                  loading={isPrimaryLoading}
                  onPress={onConfirm}
                  text={t('product:done')}
                  style={{ flex: 1 }}
                />
              </>
            ) : (
              <Button
                text={t('product:previewCFF')}
                onPress={onGetFnaDoc}
                loading={isSecondaryLoading}
                disabled={isSecondaryLoading}
                style={{ flex: 1 }}
              />
            )}
          </Row>
        </Animated.View>
      </BottomSheetFooter>
    );
  },
);

export default ClientWaiverFooter;
