import { useMutation, useQuery } from '@tanstack/react-query';
import { getImage, getProductList } from 'api/proposalApi';
import { toQuotationParty } from 'features/proposal/untils/quotationUtils';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback } from 'react';
import { AgentProfile, LicenseInfo } from 'types';
import { PartyRole } from 'types/party';
import { ProductListRequest } from 'types/products';
import { country } from 'utils/context';

export type ProductListQueryParams = Omit<ProductListRequest, 'agent'> & {
  agent?: AgentProfile;
  pid?: string;
};

function convertLicenseToStringArray(
  licenses: string[] | LicenseInfo[],
): string[] | undefined {
  if (!licenses) return undefined;
  if (typeof licenses[0] === 'string') return licenses as string[];

  return (licenses as LicenseInfo[]).map(license => license.type);
}

function convertParamsToRequestBody(
  params: ProductListQueryParams,
): ProductListRequest {
  const agentInfo = params.agent;
  return {
    ...params,
    agent: !agentInfo
      ? undefined
      : {
          licenseChecking: 'Y', // hard code for nano API
          agentCode: agentInfo?.agentId,
          fullName: agentInfo?.person.fullName,
          licenses: convertLicenseToStringArray(agentInfo?.licenses),
          trainingCodes: agentInfo?.trainingCourses,
        },
  };
}

export const useProductListQuery = () => {
  const { data: agentInfo } = useGetAgentProfile();
  const channel = useGetCubeChannel();

  return useMutation({
    mutationFn: async (
      params: Omit<ProductListQueryParams, 'agent' | 'channel'>,
    ) => {
      const pl = await getProductList(
        convertParamsToRequestBody({
          ...params,
          agent: ['ph', 'id'].includes(country) ? undefined : agentInfo, // dont pass agent in ph and id
          channel,
        }),
      );
      if (!params?.pid) return pl;

      return [pl.find(p => p.pid === params.pid)];
    },
  });
};

export const useGetProductList = (params: ProductListQueryParams) => {
  return useQuery({
    queryKey: ['getProductList', params],
    queryFn: () => {
      return getProductList(convertParamsToRequestBody(params));
    },
  });
};

export const useProductImageQuery = (url?: string) => {
  return useQuery({
    queryKey: ['getProductImage', url],
    queryFn: async () => await getImage(url),
  });
};

export const useFormatProductListParties = () => {
  const { data: optionList } = useGetOptionList();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: aCase } = useGetCase(caseId ?? '');

  const formatParties = useCallback(() => {
    const insureds = [];
    const proposers = [];
    if (aCase && aCase.parties) {
      const agentId = aCase.agent?.id;
      for (let index = 0; index < aCase.parties.length; index++) {
        const party = aCase.parties[index];
        if (party?.roles?.includes(PartyRole.INSURED)) {
          insureds.push(
            toQuotationParty(
              agentId,
              party,
              optionList?.RELATIONSHIP.options,
              optionList?.OCCUPATION?.options,
            ),
          );
        }
        if (party?.roles?.includes(PartyRole.PROPOSER)) {
          proposers.push(
            toQuotationParty(
              agentId,
              party,
              optionList?.RELATIONSHIP.options,
              optionList?.OCCUPATION?.options,
            ),
          );
        }
      }
    }
    return {
      insureds,
      proposers,
    };
  }, [
    aCase,
    optionList?.OCCUPATION?.options,
    optionList?.RELATIONSHIP.options,
  ]);

  const { insureds, proposers } = formatParties();

  return {
    insureds,
    proposers,
  };
};
