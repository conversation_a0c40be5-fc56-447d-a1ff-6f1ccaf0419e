import { useMutation } from '@tanstack/react-query';
import { postRecruitApproval } from 'api/eRecruitApi';
import { RecruitApprovalRequestBody } from 'features/eRecruit/ph/types';

export function usePostRecruitApproval() {
  return useMutation({
    mutationFn: (data: RecruitApprovalRequestBody) => postRecruitApproval(data),
    onError: error => console.log('usePostRecruitApproval error', error),
  });
}
