import { Icon } from 'cube-ui-components';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import {
  CheckApplicationProgressGroup,
  CheckApplicationProgressItem,
  CheckApplicationProgressSubgroup,
  CheckApplicationRouteGroupKey,
  CheckApplicationRouteItemKey,
  CheckApplicationRouteSubgroupKey,
  IBCheckApplicationRouteGroupKey,
} from 'features/eRecruit/types/progressBarTypes';
import { useERecruitCheckApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitCheckApplicationProgressBarStore';
import { useERecruitCheckApplicationStore } from 'features/eRecruit/util/store/ERecruitCheckApplicationStore';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from '../useGetERecruitApplicationForm';

export const useGenerateIBCheckApplicationRoutes = () => {
  const { t } = useTranslation(['eRecruit']);

  const generateRouteKey = (
    keys: [
      CheckApplicationRouteGroupKey | undefined,
      CheckApplicationRouteSubgroupKey | undefined,
      CheckApplicationRouteItemKey | undefined,
    ],
  ) => {
    return keys.join('-');
  };

  const updateCompletedStatus = (
    groups: CheckApplicationProgressGroup[],
    completedMap: Record<string, boolean>,
  ) => {
    groups.forEach(group => {
      if (!group.items || group.items.length === 0) {
        group.completed =
          completedMap[
            generateRouteKey([group.routeKey, undefined, undefined])
          ];
      } else {
        group.items.forEach(item => {
          if ((item as CheckApplicationProgressSubgroup).items) {
            const subgroup = item as CheckApplicationProgressSubgroup;
            subgroup.items.forEach(item => {
              item.completed =
                completedMap[
                  generateRouteKey([
                    group.routeKey,
                    subgroup.routeKey,
                    item.routeKey,
                  ])
                ];
            });
            subgroup.completed =
              subgroup.items.filter(i => i.completed).length ===
              subgroup.items.length;
          } else {
            item.completed =
              completedMap[
                generateRouteKey([
                  group.routeKey,
                  undefined,
                  item.routeKey as CheckApplicationRouteItemKey,
                ])
              ];
          }
          group.completed =
            group.items.filter(i => i.completed).length === group.items.length;
        });
      }
    });
  };

  const { completedMap, groupKey, subgroupKey, itemKey, setProgressBarState } =
    useERecruitCheckApplicationProgressBarStore(
      state => ({
        completedMap: state.completedMap,
        groupKey: state.groupKey,
        subgroupKey: state.subgroupKey,
        itemKey: state.itemKey,
        setProgressBarState: state.setProgressBarState,
      }),
      shallow,
    );

  const {
    updatedRegistrationStagingId,
    setPosition,
    setLeaderInformationData,
  } = useERecruitCheckApplicationStore(
    state => ({
      updatedRegistrationStagingId: state.registrationStagingId,
      position: state.position,
      setPosition: state.updatePosition,
      leaderInformationData: state.leaderInformation,
      setLeaderInformationData: state.updateleaderInformation,
      resetERecruitmentStoreState: state.resetERecruitmentStoreState,
    }),
    shallow,
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();

  const registrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = registrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingIdParam ||
      updatedRegistrationStagingId?.toString() ||
      '',
  );

  useEffect(() => {
    if (recruitmentCache) {
      setProgressBarState({
        groupKey: groupKey,
        subgroupKey: subgroupKey,
        itemKey: itemKey,
      });
      setPosition({
        position: recruitmentCache?.position.position ?? '',
        jobType: recruitmentCache?.position.jobType ?? '',
        agencyType: recruitmentCache?.position.agencyType ?? '',
        reportingBranch: recruitmentCache?.position.branchCode ?? '',
        done: Boolean(
          recruitmentCache?.position.position &&
            recruitmentCache?.position.jobType &&
            recruitmentCache?.position.agencyType &&
            (recruitmentCache?.position.agencyType === 'FA'
              ? true
              : recruitmentCache?.position.branchCode),
        ),
      });
      setLeaderInformationData({
        agentCode: recruitmentCache?.leaderInformation.agentCode ?? '',
        name: recruitmentCache?.leaderInformation.name ?? '',
        alcFwdName: recruitmentCache?.leaderInformation.alcFwdName ?? '',
        done: Boolean(recruitmentCache?.leaderInformation.agentCode),
      });
    }
  }, [recruitmentCache, groupKey, subgroupKey, itemKey]);

  useEffect(() => {
    const uploadItems: CheckApplicationProgressItem[] = [
      {
        routeKey: 'documentUpload',
        title: '  •  ' + 'Documents',
        icon: <Icon.DocumentCopy />,
        barTitle: 'Documents',
      },
    ];

    const groups: Array<CheckApplicationProgressGroup> = [
      {
        routeKey: 'additionalInformation',
        title: 'Additional information',
        showSubProgress: true,
        items: [
          {
            routeKey: 'additionalInformation',
            title: '  •  ' + 'Additional information',
            barTitle: 'Additional information',
            icon: <Icon.Account />,
          },
        ],
      },
      {
        routeKey: 'review',
        title: 'Review information',
        items: [
          {
            routeKey: 'review',
            title: '  •  ' + 'Review information',
            barTitle: 'Review information',
            icon: <Icon.Account />,
          },
        ],
      },
      {
        routeKey: 'documentUpload',
        title: 'Documents',
        items: uploadItems,
        full: true,
      },
    ];

    updateCompletedStatus(groups, completedMap);

    const step1 = groups[0];
    step1.items[0].completed = groupKey !== 'additionalInformation';
    step1.completed = step1.items[0].completed;
    const step2 = groups[1];
    step2.items[0].completed =
      groupKey !== 'review' && groupKey !== 'additionalInformation';
    step2.completed = step2.items[0].completed;
    step2.disabled = !step1.completed;
    const step3 = groups[2];
    step3.disabled = !step1.completed;

    setProgressBarState({
      groups,
    });
  }, [completedMap, setProgressBarState, t, groupKey, subgroupKey, itemKey]);
};
