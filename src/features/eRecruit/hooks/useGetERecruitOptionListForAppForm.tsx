import {
  mapConfigToPicker,
  mapConfigToOption,
} from 'features/eRecruit/my/utils/convertOptionListFunction';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import { useMemo } from 'react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { DATA_COUNTRY_CODE } from 'features/lead/components/AddLeadForm/countryCodeList';

export default function useGetERecruitOptionListForAppForm() {
  const { data: eRecruitOnlyOptionList, isLoading: isERCLoading } =
    useGetERecruitConfig();
  const { data: optionList, isLoading: isOLLooading } = useGetOptionList();
  const countryCodeOptions = useMemo(
    () =>
      DATA_COUNTRY_CODE.map(({ label, value }) => ({
        label,
        value,
      })),
    [],
  );
  const eRecruitConfigs = useMemo(
    () => ({
      ethnicityConfig:
        mapConfigToPicker(eRecruitOnlyOptionList?.raceList) ?? [],
      genderConfig: mapConfigToPicker(eRecruitOnlyOptionList?.genderList) ?? [],
      maritalConfig:
        mapConfigToPicker(eRecruitOnlyOptionList?.maritalList) ?? [],
      religionConfig:
        mapConfigToPicker(eRecruitOnlyOptionList?.religionList) ?? [],
      citizenshipConfig:
        mapConfigToPicker(eRecruitOnlyOptionList?.nationalityList) ?? [],
      educationConfig:
        mapConfigToPicker(eRecruitOnlyOptionList?.educationList) ?? [],
      ethnicityConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.raceList) ?? [],
      genderConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.genderList) ?? [],
      maritalConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.maritalList) ?? [],
      religionConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.religionList) ?? [],
      citizenshipConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.nationalityList) ?? [],
      educationConfigToOption:
        mapConfigToOption(eRecruitOnlyOptionList?.educationList) ?? [],
      intermediateTypeOptions:
        mapConfigToOption(eRecruitOnlyOptionList?.intermediaryTypeList) ?? [],
      jobTypeOptions:
        mapConfigToOption(eRecruitOnlyOptionList?.candidateTypeList) ?? [],
      reportingBranchOptions: mapConfigToOption(
        eRecruitOnlyOptionList?.branchList ?? [],
      ),
      reportingAlcBranchOptions: mapConfigToOption(
        eRecruitOnlyOptionList?.alcBranchList ?? [],
      ),
      agencyTypeOptions: mapConfigToOption(
        eRecruitOnlyOptionList?.agentAgreementList ?? [],
      ),
      cityOptions: mapConfigToOption(eRecruitOnlyOptionList?.cityList ?? []),
      stateOptions: mapConfigToOption(eRecruitOnlyOptionList?.stateList ?? []),
      bankOptions: mapConfigToOption(eRecruitOnlyOptionList?.bankList ?? []),
      spouseInsuranceRank:
        mapConfigToOption(eRecruitOnlyOptionList?.spouseInsuranceRankList) ??
        [],

      familyGeneralTakafulCompaniesList:
        mapConfigToOption(
          eRecruitOnlyOptionList?.familyGenernalTakafulCompaniesList,
        ) ?? [],
      lifeGeneralTakafulCompaniesList:
        mapConfigToOption(
          eRecruitOnlyOptionList?.liftGenernalTakafulCompaniesList,
        ) ?? [],
      jobTypeToPicker:
        mapConfigToPicker(eRecruitOnlyOptionList?.candidateTypeList) || [],
      agencyTypeToPicker: mapConfigToPicker(
        eRecruitOnlyOptionList?.agentAgreementList || [],
      ),
      postCodeOptions: mapConfigToOption(
        eRecruitOnlyOptionList?.postalCodeList || [],
      ),
      rawPostStateCityList: eRecruitOnlyOptionList?.postStateCityList ?? [],
      positionList: mapConfigToOption(
        eRecruitOnlyOptionList?.positionList || [],
      ),
    }),
    [eRecruitOnlyOptionList],
  );

  const idnERecruitConfigs = useMemo(
    () => ({
      idTypeList: mapConfigToOption(eRecruitOnlyOptionList?.idTypeList) || [],
      salesOfficeList:
        mapConfigToOption(eRecruitOnlyOptionList?.salesOfficeList) || [],
      domicileList:
        mapConfigToOption(eRecruitOnlyOptionList?.domicileList) || [],
      superiorAgentCodeList:
        mapConfigToOption(eRecruitOnlyOptionList?.superiorAgentCodeList) || [],
      agentPositionList:
        mapConfigToOption(eRecruitOnlyOptionList?.agentPositionList) || [],
      examCityList:
        mapConfigToOption(eRecruitOnlyOptionList?.examCityList) || [],
      identityList:
        mapConfigToOption(eRecruitOnlyOptionList?.identityList) || [],
      refList: mapConfigToOption(eRecruitOnlyOptionList?.refList) || [],
      areaManagerList:
        mapConfigToOption(eRecruitOnlyOptionList?.areaManagerList) || [],
      dmsStatusList:
        mapConfigToOption(eRecruitOnlyOptionList?.dmsStatusList) || [],
      regulatoryList: eRecruitOnlyOptionList?.regulatoryList ?? [],
      occupationList:
        mapConfigToOption(eRecruitOnlyOptionList?.occupationList) || [],
      industryList:
        mapConfigToOption(eRecruitOnlyOptionList?.industryList) || [],
      provinceList:
        mapConfigToOption(eRecruitOnlyOptionList?.provinceList) || [],
      allBankBranchList:
        mapConfigToOption(eRecruitOnlyOptionList?.branchList) || [],
    }),
    [eRecruitOnlyOptionList],
  );

  const optionConfig = useMemo(
    () => ({
      maleTitleConfig:
        optionList?.CUBE_TITLE?.options
          ?.filter(item => item?.gender?.male === 'True')
          ?.map(item => ({ label: item.label, value: item.value })) ?? [],
      femaleTitleConfig:
        optionList?.CUBE_TITLE?.options
          ?.filter(item => item?.gender?.female === 'True')
          ?.map(item => ({ label: item.label, value: item.value })) ?? [],
      postCodeListWithStateCity: optionList?.POSTCODE?.options ?? [],
    }),
    [optionList],
  );

  return {
    isLoading: isERCLoading || isOLLooading,
    countryCodeOptions,
    ...optionConfig,
    ...eRecruitConfigs,
    ...idnERecruitConfigs,
  };
}
