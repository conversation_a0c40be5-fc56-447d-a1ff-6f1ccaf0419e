import { useTheme } from '@emotion/react';
import { FlashList } from '@shopify/flash-list';
import { Column, H6, Icon, Label, Row, SmallLabel } from 'cube-ui-components';
import { isToday } from 'date-fns';
import { useGetTodoRecruitList } from 'features/eRecruit/hooks/ph/useGetRecruitsList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import CandidateStatusFlagLabel from '../../components/CandidateStatusFlagLabel';
import { Table } from '../components/Table';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import SearchCandidatesButton from '../components/SearchCandidatesButton';
import SearchSection from './SearchSection';

const TABLE_CONFIG = {
  candidateName: { width: '19%' },
  recruitBy: { width: '19%' },
  pendingLicenseType: { width: '15%' },
  status: { width: '24%' },
  lastUpdate: { width: '23%' },
} as const;

// * No filter in todo in phrase 1
// const CHIP_FILTER_CONFIG = [
// { type: 'pending-review', label: 'Pending review' },
// { type: 'auth-to-deduct', label: 'Authority to deduct' },
// ];

export default function ToDoTab() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();
  const { data: optionList } = useGetOptionList();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const [isSearching, setIsSearching] = useState(false);

  // Sort
  const [sortDesc, setSortDesc] = useState(true);
  const sortOrder = sortDesc ? 'DESC' : 'ASC';

  // Data
  const { data: toDoList, isLoading: isToDoListLoading } =
    useGetTodoRecruitList({
      sortOrder,
    });

  if (isSearching) {
    return (
      <SearchSection
        tab={'toDo'}
        data={toDoList}
        setIsSearching={setIsSearching}
      />
    );
  }

  return (
    <>
      <Row pb={space[4]} alignItems="center" justifyContent="space-between">
        <H6 fontWeight="bold" children={'To do'} />
        <SearchCandidatesButton onPress={() => setIsSearching(true)} />
      </Row>

      <Label
        children={`${t('total')} (${toDoList?.length ?? 0})  |  ${t(
          'candidates.displayDataDuration',
        )}`}
        color={colors.palette.fwdGreyDarkest}
        style={{ paddingBottom: space[3] }}
      />

      <Table.TableWrapper>
        {/* Table header */}
        <Row
          style={{
            minHeight: 70, // value from Figma
            backgroundColor: colors.primary,
            borderTopLeftRadius: space[4],
            borderTopRightRadius: space[4],
            padding: space[4],
            alignItems: 'center',
          }}>
          <Row
            w={TABLE_CONFIG.candidateName.width}
            alignItems="center"
            justifyContent="space-between">
            <Label
              fontWeight="medium"
              children={'Candidate name'}
              color={colors.palette.white}
            />
            <Table.VerticalSeparator color={colors?.palette.white} />
          </Row>

          <Row w={TABLE_CONFIG.recruitBy.width} justifyContent="space-between">
            <Label
              fontWeight="medium"
              children={'Recruit by'}
              color={colors.palette.white}
              style={{ paddingHorizontal: space[4] }}
            />
            <Table.VerticalSeparator color={colors?.palette.white} />
          </Row>

          <Row
            w={TABLE_CONFIG.pendingLicenseType.width}
            alignItems="center"
            justifyContent="space-between">
            <Label
              fontWeight="medium"
              children={'Pending\nlicense type'}
              color={colors.palette.white}
              style={{ paddingHorizontal: space[4] }}
            />
            <Table.VerticalSeparator color={colors?.palette.white} />
          </Row>

          <Row
            w={TABLE_CONFIG.status.width}
            px={space[4]}
            justifyContent="space-between">
            <Label
              fontWeight="medium"
              children={'Status'}
              color={colors.palette.white}
            />
            <Table.VerticalSeparator color={colors?.palette.white} />
          </Row>

          <TouchableOpacity
            style={{ width: TABLE_CONFIG.lastUpdate.width }}
            onPress={() => setSortDesc(!sortDesc)}>
            <Row alignItems="center" gap={space[1]}>
              <Label
                fontWeight="medium"
                children={'Last update'}
                color={colors.palette.white}
              />
              {sortDesc ? (
                <Icon.ArrowDown size={space[4]} fill={colors.background} />
              ) : (
                <Icon.ArrowUp size={space[4]} fill={colors.background} />
              )}
            </Row>
          </TouchableOpacity>
        </Row>

        {/* Table row */}
        {isToDoListLoading ? (
          <Table.LoadingPlaceholder />
        ) : (
          <FlashList
            data={toDoList ?? []}
            bounces={false}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={70}
            renderItem={({ item, index }) => {
              // Info.
              const formattedGender =
                getLabelFromValue(optionList?.GENDER?.options, item?.gender) ??
                '--';
              const dob = dateFormatUtil(item?.birthDate) ?? '--';
              const age =
                String(calculateAge(new Date(item?.birthDate))) ?? '--';

              // Status with time
              const [approveRegDueDate, approveRegDueTime] =
                item?.approveRegistrationDueDate?.split(' ') ?? [];
              const formattedApproveRegDueDate =
                dateFormatUtil(approveRegDueDate) ?? '--';
              const formattedApproveRegDueTime =
                formatTime24to12(approveRegDueTime) ?? '--';
              const isApproveRegDueDateToday = isToday(
                new Date(approveRegDueDate),
              );

              // Last update date
              const lateUpdateDate = dateFormatUtil(item?.updated_at) ?? '--';

              // Press handlers
              const applicationInfo = {
                status: item?.status,
                recruitID: item?.recruitID,
                recruitName: item?.recruitName,
                recruiterID: item?.recruiterID,
                recruiterName: item?.recruiterName,
                formattedGender,
                dob,
                age,
                emailAddress: item?.emailAddress,
                mobileNumber: item?.mobileNumber,
                formattedRegDate: '',
                disapproveReason: '',
                formattedDeclinedDate: '',
              };

              const onItemPressHandler = () => {
                if (item?.status !== 'ToDo') return;
                navigation.navigate('ReviewCandidateApplication', {
                  applicationInfo,
                });
              };

              const onReviewButtonPressHandler = () => {
                if (item?.status !== 'ToDo') return;
                navigation.navigate('ReviewCandidateApplication', {
                  applicationInfo,
                });
              };

              return (
                <TouchableOpacity onPress={() => onItemPressHandler()}>
                  <Row
                    style={{
                      backgroundColor:
                        index % 2 === 0
                          ? colors.background
                          : colors.palette.fwdGrey[20],
                      paddingVertical: space[3],
                      paddingHorizontal: space[4],
                      borderBottomLeftRadius:
                        index === Number(toDoList?.length) - 1
                          ? borderRadius.large
                          : undefined,
                      borderBottomRightRadius:
                        index === Number(toDoList?.length) - 1
                          ? borderRadius.large
                          : undefined,
                      alignItems: 'center',
                    }}>
                    <Row
                      w={TABLE_CONFIG.candidateName.width}
                      alignItems="center"
                      justifyContent="space-between">
                      <Column gap={space[1]} pr={space[4]}>
                        <Label
                          children={item?.recruitName ?? '--'}
                          color={colors.palette.fwdDarkGreen[100]}
                        />
                        <SmallLabel
                          color={colors.secondaryVariant}
                          children={`${formattedGender} | ${dob} (${age} y.o.)`}
                        />
                      </Column>
                      <Table.VerticalSeparator />
                    </Row>

                    <Row
                      w={TABLE_CONFIG.recruitBy.width}
                      alignItems="center"
                      justifyContent="space-between">
                      <Label
                        children={item?.recruiterName ?? '--'}
                        color={colors.palette.fwdDarkGreen[100]}
                        style={{ paddingHorizontal: space[4] }}
                      />
                      <Table.VerticalSeparator />
                    </Row>

                    <Row
                      w={TABLE_CONFIG.pendingLicenseType.width}
                      alignItems="center"
                      justifyContent="space-between">
                      <Label
                        children={item?.pendingLicenseType ?? '--'}
                        color={colors.palette.fwdDarkGreen[100]}
                        style={{ paddingHorizontal: space[4] }}
                      />
                      <Table.VerticalSeparator />
                    </Row>

                    <Row
                      w={TABLE_CONFIG.status.width}
                      px={space[4]}
                      alignItems="center"
                      justifyContent="space-between">
                      <CandidateStatusFlagLabel
                        status={item?.status}
                        dueDate={formattedApproveRegDueDate}
                        dueTime={formattedApproveRegDueTime}
                        isDueDateToday={isApproveRegDueDateToday}
                      />
                      <Table.VerticalSeparator />
                    </Row>

                    <Row
                      w={TABLE_CONFIG.lastUpdate.width}
                      alignItems="center"
                      justifyContent="space-between">
                      <Label
                        children={lateUpdateDate}
                        color={colors.palette.fwdDarkGreen[100]}
                      />

                      <Table.ReviewButton
                        onPress={() => onReviewButtonPressHandler()}
                      />

                      <Icon.ChevronRight fill={colors.primary} />
                    </Row>
                  </Row>
                </TouchableOpacity>
              );
            }}
            ItemSeparatorComponent={() => <Table.HorizontalSeparator />}
            ListEmptyComponent={<Table.EmptyRecordPlaceholder />}
            ListFooterComponentStyle={{ height: 70 }} // estimatedItemSize
          />
        )}
      </Table.TableWrapper>
    </>
  );
}

/**
 * Parse 24-hour time to 12-hour time
 * e.g. 18:30 -> 6:30 PM
 */
const formatTime24to12 = (time24: string) => {
  if (!time24) return;
  const [hours, minutes] = time24.split(':');
  const date = new Date();
  date.setHours(+hours); // set the hours, using unary plus operator to convert string to number
  date.setMinutes(+minutes); // set the minutes
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });
};
