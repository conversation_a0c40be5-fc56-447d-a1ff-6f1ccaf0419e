import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import {
  Body,
  Chip,
  Column,
  H7,
  Icon,
  Label,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import { isToday } from 'date-fns';
import {
  CandidateListItem,
  CandidateListItem as CandidateListItemType,
} from 'features/eRecruit/ph/types';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Dispatch, SetStateAction, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleProp,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { RootStackParamList } from 'types';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import CandidateStatusFlagLabel from '../../components/CandidateStatusFlagLabel';
import { DrawerItem } from '../CandidatesListScreen';
import { Table } from '../components/Table';

const TODO_APPROVED_TABLE_CONFIG = {
  candidateName: { width: '19%' },
  recruitBy: { width: '19%' },
  pendingLicenseType: { width: '15%' },
  status: { width: '24%' },
  lastUpdate: { width: '23%' },
} as const;

const DECLINED_TABLE_CONFIG = {
  candidateName: { width: '30%' },
  recruitBy: { width: '30%' },
  pendingLicenseType: { width: '0%' },
  status: { width: '20%' },
  lastUpdate: { width: '20%' },
} as const;

export default function SearchSection({
  tab,
  data = [],
  setIsSearching,
}: {
  tab: DrawerItem;
  data?: CandidateListItem[];
  setIsSearching: Dispatch<SetStateAction<boolean>>;
}) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const TABLE_CONFIG =
    tab === 'toDo' || tab === 'approved'
      ? TODO_APPROVED_TABLE_CONFIG
      : DECLINED_TABLE_CONFIG;

  const LICENSE_TYPE_FILTER_CONFIG = {
    toDo: [
      // * No filter in todo in phrase 1
      // { type: 'pending-review', label: 'Pending review' },
      // { type: 'auth-to-deduct', label: 'Authority to deduct' },
    ],
    approved: [
      {
        type: 'pending-dual',
        label: `${t('candidates.pendingLicense')}: ${t(
          'candidates.pendingLicenseType.dual',
        )}`,
      },
      {
        type: 'pending-trad',
        label: `${t('candidates.pendingLicense')}: ${t(
          'candidates.pendingLicenseType.traditional',
        )}`,
      },
      {
        type: 'pending-var',
        label: `${t('candidates.pendingLicense')}: ${t(
          'candidates.pendingLicenseType.variable',
        )}`,
      },
      { type: 'completed', label: t('candidates.allStatusCompleted') },
    ],
    declined: [],
  } as const;

  const [searchResultVisible, setSearchResultVisible] = useState(false);

  const ref = useRef<TextInput>(null);

  const [tempQuery, setTempQuery] = useState(''); // TextField value before search/ onBlur
  const [query, setQuery] = useState('');

  const [isFocused, setIsFocused] = useState(false);

  const handleBackPress = () => {
    setQuery('');
    ref.current?.clear();
    ref.current?.blur();
    setIsSearching(false);
  };

  // Sort
  const [sortDesc, setSortDesc] = useState(true);

  // Filter
  const [activeLicenseType, setActiveLicenseType] = useState('');
  const licenseTypeFilter = LICENSE_TYPE_FILTER_CONFIG?.[tab];
  const haveLicenseTypeFilter = licenseTypeFilter?.length > 0;

  // Data
  const { data: optionList } = useGetOptionList();

  const searchResult: CandidateListItemType[] = useMemo(
    () =>
      data?.filter(
        item =>
          (item?.recruitName &&
            item?.recruitName.toLowerCase().includes(query.toLowerCase())) ||
          (item?.mobileNumber &&
            item?.mobileNumber.toLowerCase().includes(query.toLowerCase())),
      ),
    [query],
  );

  const mutatedSearchResult = useMemo(() => {
    return searchResult
      .sort((a, b) =>
        sortDesc
          ? new Date(b.updated_at ?? 0)?.getTime() -
            new Date(a.updated_at ?? 0)?.getTime()
          : new Date(a.updated_at ?? 0)?.getTime() -
            new Date(b.updated_at ?? 0)?.getTime(),
      )
      .filter(item => {
        if (activeLicenseType) {
          return item?.filterStatus === activeLicenseType;
        } else {
          return true;
        }
      });
  }, [searchResult, sortDesc, activeLicenseType]);

  const DISPLAY_DATA = mutatedSearchResult ?? [];

  return (
    <>
      <Column flex={1}>
        <Row alignItems="center" justifyContent="center" gap={space[4]}>
          <TouchableOpacity onPress={() => handleBackPress()}>
            <Icon.ArrowLeft fill={colors.palette.fwdDarkGreen[100]} />
          </TouchableOpacity>

          <SearchInputContainer isFocused={isFocused}>
            {query === '' && (
              <View style={{ marginRight: space[1] }}>
                <Icon.Search
                  fill={colors.palette.fwdGreyDark}
                  size={sizes[5]}
                />
              </View>
            )}
            <SearchInput
              ref={ref}
              placeholder={t('searchCandidate')}
              onChangeText={text => setTempQuery(text)}
              autoCapitalize={'none'}
              enablesReturnKeyAutomatically={true}
              returnKeyType={'search'}
              autoComplete={'off'}
              autoCorrect={false}
              autoFocus={true}
              onFocus={() => setIsFocused(true)}
              onBlur={() => {
                setQuery(tempQuery);
                setIsFocused(false);
                setSearchResultVisible(true);
              }}
              selectionColor={colors.primary}
              placeholderTextColor={colors.palette.fwdGreyDark}
            />
            {query != '' && (
              <TouchableOpacity
                onPress={() => {
                  setQuery('');
                  setActiveLicenseType('');
                  ref.current?.clear();
                  setSearchResultVisible(false);
                }}>
                <Icon.CloseCircle fill={colors.onBackground} size={sizes[5]} />
              </TouchableOpacity>
            )}
          </SearchInputContainer>
        </Row>

        <SmallLabel
          children={'e.g. Candidate’s name, mobile number'}
          style={{
            color: colors.palette.fwdGreyDarker,
            paddingTop: space[1],
            paddingLeft: space[12], // space[6] + size[5] + space[1]
          }}
        />

        {!searchResultVisible ? (
          <></>
        ) : (
          <>
            <H7
              fontWeight="bold"
              children={`${t('materials.search.searchResult')} (${
                mutatedSearchResult?.length
              })`}
              style={{ paddingVertical: space[4] }}
            />

            {haveLicenseTypeFilter && (
              <Row
                pb={space[4]}
                minHeight={sizes[10]} // for the height of the chip when it's focused
                alignItems="center">
                <Body
                  children={t('filterBy')}
                  color={colors.palette.fwdGreyDarkest}
                  style={{ paddingRight: space[2] }}
                />
                <Row gap={space[1]}>
                  {licenseTypeFilter?.map(({ type, label }) => (
                    <Chip
                      key={type}
                      label={label}
                      focus={type === activeLicenseType}
                      onPress={() => setActiveLicenseType(type)}
                    />
                  ))}
                </Row>
              </Row>
            )}

            <Table.TableWrapper>
              {/* Table header */}
              <Row
                style={{
                  minHeight: 70, // value from Figma
                  backgroundColor: colors.primary,
                  borderTopLeftRadius: space[4],
                  borderTopRightRadius: space[4],
                  padding: space[4],
                  alignItems: 'center',
                }}>
                <Row
                  w={TABLE_CONFIG.candidateName.width}
                  alignItems="center"
                  justifyContent="space-between">
                  <Label
                    fontWeight="medium"
                    children={'Candidate name'}
                    color={colors.palette.white}
                  />
                  <Table.VerticalSeparator color={colors?.palette.white} />
                </Row>

                <Row
                  w={TABLE_CONFIG.recruitBy.width}
                  justifyContent="space-between">
                  <Label
                    fontWeight="medium"
                    children={'Recruit by'}
                    color={colors.palette.white}
                    style={{ paddingHorizontal: space[4] }}
                  />
                  <Table.VerticalSeparator color={colors?.palette.white} />
                </Row>

                {tab !== 'declined' && (
                  <Row
                    w={TABLE_CONFIG.pendingLicenseType.width}
                    alignItems="center"
                    justifyContent="space-between">
                    <Label
                      fontWeight="medium"
                      children={'Pending\nlicense type'}
                      color={colors.palette.white}
                      style={{ paddingHorizontal: space[4] }}
                    />
                    <Table.VerticalSeparator color={colors?.palette.white} />
                  </Row>
                )}

                <Row
                  w={TABLE_CONFIG.status.width}
                  px={space[4]}
                  justifyContent="space-between">
                  <Label
                    fontWeight="medium"
                    children={'Status'}
                    color={colors.palette.white}
                  />
                  <Table.VerticalSeparator color={colors?.palette.white} />
                </Row>

                <TouchableOpacity
                  style={{ width: TABLE_CONFIG.lastUpdate.width }}
                  onPress={() => setSortDesc(!sortDesc)}>
                  <Row alignItems="center" gap={space[1]}>
                    <Label
                      fontWeight="medium"
                      children={'Last update'}
                      color={colors.palette.white}
                    />
                    {sortDesc ? (
                      <Icon.ArrowDown
                        size={space[4]}
                        fill={colors.background}
                      />
                    ) : (
                      <Icon.ArrowUp size={space[4]} fill={colors.background} />
                    )}
                  </Row>
                </TouchableOpacity>
              </Row>

              {/* Table row */}
              <FlashList
                data={DISPLAY_DATA}
                bounces={false}
                showsVerticalScrollIndicator={false}
                estimatedItemSize={70}
                renderItem={({ item, index }) => {
                  // Info.
                  const formattedGender =
                    getLabelFromValue(
                      optionList?.GENDER?.options,
                      item?.gender,
                    ) ?? '--';
                  const dob = dateFormatUtil(item?.birthDate) ?? '--';
                  const age =
                    String(calculateAge(new Date(item?.birthDate))) ?? '--';

                  // Status with time
                  const [approveRegDueDate, approveRegDueTime] =
                    item?.approveRegistrationDueDate?.split(' ') ?? [];
                  const formattedApproveRegDueDate =
                    dateFormatUtil(approveRegDueDate) ?? '--';
                  const formattedApproveRegDueTime =
                    formatTime24to12(approveRegDueTime) ?? '--';
                  const isApproveRegDueDateToday = isToday(
                    new Date(approveRegDueDate),
                  );
                  const [regDate, regTime] =
                    item?.registeredDate?.split(' ') ?? [];
                  const formattedRegDate = dateFormatUtil(regDate) ?? '--';
                  const [declinedDate, declinedTime] =
                    item?.declinedRegistrationDateTime?.split(' ') ?? [];
                  const formattedDeclinedDate =
                    dateFormatUtil(declinedDate) ?? '--';

                  // Last update date
                  const lateUpdateDate =
                    dateFormatUtil(item?.updated_at) ?? '--';

                  // Press handlers
                  const applicationInfo = {
                    status: item?.status,
                    recruitID: item?.recruitID,
                    recruitName: item?.recruitName,
                    recruiterID: item?.recruiterID,
                    recruiterName: item?.recruiterName,
                    formattedGender,
                    dob,
                    age,
                    emailAddress: item?.emailAddress,
                    mobileNumber: item?.mobileNumber,
                    formattedRegDate: formattedRegDate,
                    disapproveReason: item?.disapproveReason,
                    formattedDeclinedDate: formattedDeclinedDate,
                  };

                  const onItemPressHandler = () => {
                    if (item?.status === 'ToDo')
                      navigation.navigate('ReviewCandidateApplication', {
                        applicationInfo,
                      });
                    if (
                      item?.status === 'Approved' ||
                      item?.status === 'Declined'
                    )
                      navigation.navigate('CandidateProfile', {
                        applicationInfo,
                      });
                    return;
                  };

                  const onReviewButtonPressHandler = () => {
                    if (item?.status === 'ToDo')
                      navigation.navigate('ReviewCandidateApplication', {
                        applicationInfo,
                      });
                    if (
                      item?.status === 'Approved' ||
                      item?.status === 'Declined'
                    )
                      navigation.navigate('CandidateProfile', {
                        applicationInfo,
                      });
                    return;
                  };

                  return (
                    <TouchableOpacity onPress={() => onItemPressHandler()}>
                      <Row
                        style={{
                          backgroundColor:
                            index % 2 === 0
                              ? colors.background
                              : colors.palette.fwdGrey[20],
                          paddingVertical: space[3],
                          paddingHorizontal: space[4],
                          borderBottomLeftRadius:
                            index === Number(DISPLAY_DATA?.length) - 1
                              ? borderRadius.large
                              : undefined,
                          borderBottomRightRadius:
                            index === Number(DISPLAY_DATA?.length) - 1
                              ? borderRadius.large
                              : undefined,
                          alignItems: 'center',
                        }}>
                        <Row
                          w={TABLE_CONFIG.candidateName.width}
                          alignItems="center"
                          justifyContent="space-between">
                          <Column gap={space[1]} pr={space[4]}>
                            <Label
                              children={item?.recruitName ?? '--'}
                              color={colors.palette.fwdDarkGreen[100]}
                            />
                            <SmallLabel
                              color={colors.secondaryVariant}
                              children={`${formattedGender} | ${dob} (${age} y.o.)`}
                            />
                          </Column>
                          <Table.VerticalSeparator />
                        </Row>

                        <Row
                          w={TABLE_CONFIG.recruitBy.width}
                          alignItems="center"
                          justifyContent="space-between">
                          <Label
                            children={item?.recruiterName ?? '--'}
                            color={colors.palette.fwdDarkGreen[100]}
                            style={{ paddingHorizontal: space[4] }}
                          />
                          <Table.VerticalSeparator />
                        </Row>

                        {tab !== 'declined' && (
                          <Row
                            w={TABLE_CONFIG.pendingLicenseType.width}
                            alignItems="center"
                            justifyContent="space-between">
                            <Label
                              children={item?.pendingLicenseType ?? '--'}
                              color={colors.palette.fwdDarkGreen[100]}
                              style={{ paddingHorizontal: space[4] }}
                            />
                            <Table.VerticalSeparator />
                          </Row>
                        )}

                        <Row
                          w={TABLE_CONFIG.status.width}
                          px={space[4]}
                          alignItems="center"
                          justifyContent="space-between">
                          <CandidateStatusFlagLabel
                            status={item?.status}
                            filterStatus={item?.filterStatus}
                            dueDate={formattedApproveRegDueDate}
                            dueTime={formattedApproveRegDueTime}
                            isDueDateToday={isApproveRegDueDateToday}
                          />
                          <Table.VerticalSeparator />
                        </Row>

                        <Row
                          w={TABLE_CONFIG.lastUpdate.width}
                          alignItems="center"
                          justifyContent="space-between">
                          <Label
                            children={lateUpdateDate}
                            color={colors.palette.fwdDarkGreen[100]}
                          />

                          {item?.status !== 'Declined' && (
                            <Table.ReviewButton
                              onPress={() => onReviewButtonPressHandler()}
                            />
                          )}

                          <Icon.ChevronRight fill={colors.primary} />
                        </Row>
                      </Row>
                    </TouchableOpacity>
                  );
                }}
                ItemSeparatorComponent={() => <Table.HorizontalSeparator />}
                ListEmptyComponent={<Table.EmptySearchRecordPlaceholder />}
                ListFooterComponentStyle={{ height: 70 }} // estimatedItemSize
              />
            </Table.TableWrapper>
          </>
        )}
      </Column>
    </>
  );
}

// search bar
interface SearchBarContainerProps {
  show?: boolean;
  active?: boolean;
  isFocused?: boolean;
  style?: StyleProp<ViewStyle>;
}

const SearchInputContainer = styled.View<SearchBarContainerProps>(
  ({ theme, active, isFocused }) => {
    return {
      flex: 1,
      borderRadius: theme.borderRadius.full,
      borderColor:
        active || isFocused
          ? theme.colors.primary
          : theme.colors.palette.fwdDarkGreen[20],
      borderWidth: 1,
      backgroundColor: theme.colors.background,
      paddingHorizontal: theme.space[4],
      flexDirection: 'row',
      minHeight: theme.sizes[12],
      alignItems: 'center',
    };
  },
);

const SearchInput = styled.TextInput<SearchBarContainerProps>(({ theme }) => ({
  flex: 1,
  color: theme.colors.onSurface,
  fontFamily: 'FWDCircularTT-Book',
}));

/**
 * Parse 24-hour time to 12-hour time
 * e.g. 18:30 -> 6:30 PM
 */
const formatTime24to12 = (time24: string) => {
  if (!time24) return;
  const [hours, minutes] = time24.split(':');
  const date = new Date();
  date.setHours(+hours); // set the hours, using unary plus operator to convert string to number
  date.setMinutes(+minutes); // set the minutes
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });
};
