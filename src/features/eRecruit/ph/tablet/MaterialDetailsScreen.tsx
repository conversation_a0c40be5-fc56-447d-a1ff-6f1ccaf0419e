import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Box, Column, H4, H7, Icon, LargeBody, Row } from 'cube-ui-components';
import { ResizeMode, Video } from 'expo-av';
import * as Network from 'expo-network';
import { NetworkStateType } from 'expo-network';
import { useGetSingleRecruitmentMaterial } from 'hooks/useGetRecruitmentMaterials';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Alert,
  ScrollView,
  Share,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import { RootStackParamList } from 'types';
import { CategoryType } from 'types/recruitmentMaterials';
import * as FileSystem from 'expo-file-system';
import useBoundStore from 'hooks/useBoundStore';

export default function MaterialDetailsScreen() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const { width } = useWindowDimensions();

  const { setAppLoading, setAppIdle } = useBoundStore(
    store => store.appActions,
  );

  const route = useRoute<RouteProp<RootStackParamList, 'MaterialDetails'>>();
  const { id } = route.params;
  const materialUid = id ?? 'no-id';

  const { data } = useGetSingleRecruitmentMaterial({ materialUid });

  const videoUri = data?.video?.url ?? '';
  const category = data?.category ?? '';
  const displayTitle = data?.display_title ?? '';
  const description = data?.description ?? '';
  const isShareable = category === 'recruitment';

  // Video
  const videoRef = useRef(null);
  const [status, setStatus] = useState({});

  useEffect(() => {
    async function getNetworkStateAsync() {
      const networkStatus = await Network.getNetworkStateAsync();
      if (networkStatus.type !== NetworkStateType.WIFI) {
        Alert.alert(t('materials.networkAlert'));
      }
    }
    getNetworkStateAsync();
  }, []);

  // Share video
  const [downloadMediaProgress, setDownloadMediaProgress] = useState(0);
  const isDownloading = downloadMediaProgress > 0 && downloadMediaProgress < 1;

  const callback = (downloadProgress: FileSystem.DownloadProgressData) => {
    const progress =
      downloadProgress.totalBytesWritten /
      downloadProgress.totalBytesExpectedToWrite;
    setDownloadMediaProgress(progress);
  };

  const downloadResumable = FileSystem.createDownloadResumable(
    videoUri,
    FileSystem?.documentDirectory + 'Share-video.mp4',
    {},
    callback,
  );

  const shareVideo = async (videoUri: string) => {
    if (!videoUri) return;
    try {
      const { uri } =
        (await downloadResumable.downloadAsync()) as FileSystem.FileSystemDownloadResult;
      if (uri) await Share.share({ url: uri });
      return;
    } catch (error: any) {
      console.error('Share failed', error.message);
      return;
    }
  };

  useEffect(() => {
    isDownloading ? setAppLoading() : setAppIdle();
  }, [isDownloading]);

  return (
    <>
      <ScreenHeader
        route={'Materials'}
        customTitle={'Materials'}
        isLeftArrowBackShown
      />

      <Column
        backgroundColor={colors.palette.fwdOrange[100]}
        minHeight={'20%'}
        paddingTop={space[8]}
        paddingBottom={space[6]}
        paddingX={width * 0.1}
        justifyContent="space-between">
        <H4
          fontWeight={'bold'}
          numberOfLines={2}
          children={displayTitle ?? '--'}
          color={colors.palette.white}
        />

        <Row alignItems="center" justifyContent="space-between">
          <H7
            fontWeight="medium"
            children={
              t(`materials.category.${category as CategoryType}`) +
              ' ' +
              t('materials.fileType.video')
            }
            style={{ color: colors.palette.white }}
          />

          {isShareable && (
            <TouchableOpacity
              disabled={isDownloading}
              onPress={() => shareVideo(videoUri)}>
              <Box
                p={space[2]}
                borderWidth={2}
                borderRadius={borderRadius.full}
                borderColor={colors.palette.white}>
                <Icon.Share
                  height={sizes[6]}
                  width={sizes[6]}
                  fill={colors.palette.white}
                />
              </Box>
            </TouchableOpacity>
          )}
        </Row>
      </Column>

      <ScrollView bounces={false}>
        <Row paddingTop={space[8]} paddingX={width * 0.1} gap={space[8]}>
          <Column flex={1}>
            <VideoPlayerContainer>
              <Video
                ref={videoRef}
                style={{ flex: 1 }}
                source={{ uri: videoUri }}
                isLooping
                shouldPlay={false}
                useNativeControls
                resizeMode={ResizeMode.COVER}
                onPlaybackStatusUpdate={status => setStatus(() => status)}
                volume={1.0} // Enforce fixed volume
                isMuted={false} // Disable mute
              />
            </VideoPlayerContainer>
            <Rect />
          </Column>

          <Column flex={1}>
            <LargeBody children={description} />
          </Column>
        </Row>
      </ScrollView>
    </>
  );
}

const VideoPlayerContainer = styled.View(({ theme }) => ({
  width: '100%',
  height: theme.sizes[70],
  borderRadius: theme.borderRadius.large,
  overflow: 'hidden',
}));

const Rect = styled.View(({ theme }) => ({
  width: theme.sizes[10],
  height: theme.sizes[1],
  borderRadius: theme.borderRadius['x-small'],
  backgroundColor: theme.colors.palette.fwdDarkGreen[100],
  alignSelf: 'center',
  marginVertical: theme.space[2],
}));
