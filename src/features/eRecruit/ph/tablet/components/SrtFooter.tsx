import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, H6, Row } from 'cube-ui-components';
import { ButtonVariant } from 'cube-ui-components/dist/cjs/components/Button/types';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleProp, View, ViewStyle } from 'react-native';

export default function SrtFooter({
  onPress,
  label,
  isLoading,
  isSecondaryLoading,
  note,
  style,
  disabled,
  secondaryLabel,
  onSecondaryPress,
  secondaryStyle,
  subText,
  variant,
}: {
  subText?: string;
  disabled?: boolean;
  onPress?: () => void;
  label?: string;
  isLoading?: boolean;
  isSecondaryLoading?: boolean;
  note?: string;
  style?: StyleProp<ViewStyle>;
  secondaryLabel?: string;
  onSecondaryPress?: () => void;
  secondaryStyle?: StyleProp<ViewStyle>;
  variant?: ButtonVariant;
}) {
  const { t } = useTranslation(['eApp']);
  const { space, colors, sizes } = useTheme();
  const throttlingPress = useRef(false);
  const throttlingSecondaryPress = useRef(false);

  const handlePress = React.useCallback(async () => {
    if (throttlingPress.current) return;
    throttlingPress.current = true;
    onPress && (await onPress());
    throttlingPress.current = false;
  }, [onPress]);

  const handleSecondaryPress = React.useCallback(async () => {
    if (throttlingSecondaryPress.current) return;
    throttlingSecondaryPress.current = true;
    onSecondaryPress && (await onSecondaryPress());
    throttlingSecondaryPress.current = false;
  }, [onSecondaryPress]);

  return (
    <Container>
      {Boolean(note) && (
        <Box ml={space[6]}>
          <H6 fontWeight="bold" color={colors.primary}>
            {note}
          </H6>
        </Box>
      )}
      <Box flex={1} />
      <Row
        alignItems="center"
        justifyContent="flex-end"
        my={space[4]}
        mr={space[6]}
        gap={space[5]}>
        {!!secondaryLabel && (
          <Button
            text={secondaryLabel}
            variant="secondary"
            style={[{ minWidth: 200 }, secondaryStyle]}
            onPress={handleSecondaryPress}
            disabled={isLoading}
            loading={isSecondaryLoading}
          />
        )}
        <Button
          disabled={disabled || isSecondaryLoading}
          variant={variant}
          text={label ?? t('eApp:next')}
          subtext={subText ?? ''}
          style={[{ minWidth: 200 }, style]}
          loading={isLoading}
          onPress={handlePress}
        />
      </Row>
    </Container>
  );
}

const Container = styled(View)(({ theme: { sizes, colors } }) => ({
  flexDirection: 'row',
  width: '100%',
  alignItems: 'center',
  backgroundColor: colors.palette.white,
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  height: sizes[21],
}));
