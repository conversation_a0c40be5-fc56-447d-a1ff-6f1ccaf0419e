import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Column, H6, LargeLabel, Row, SmallLabel } from 'cube-ui-components';
import { Card } from 'features/lead/components/LeadProfile/Card';
import { useTranslation } from 'react-i18next';
import { RecruitmentStackParamList, RootStackParamList } from 'types';

export default function PersonalInfoSection() {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();

  const route =
    useRoute<RouteProp<RootStackParamList, 'ReviewCandidateApplication'>>();
  const { applicationInfo } =
    route.params as RecruitmentStackParamList['ReviewCandidateApplication'];

  const INFO_CONFIG = [
    {
      type: 'name',
      label: t('candidates.review.fullName'),
      value: applicationInfo?.recruitName ?? '--',
    },
    {
      type: 'birthday',
      label: t('candidates.review.dob'),
      value: `${applicationInfo?.dob ?? '--'} (${
        applicationInfo?.age ?? '--'
      } y.o.)`,
    },
    {
      type: 'gender',
      label: t('candidates.review.gender'),
      value: applicationInfo?.formattedGender ?? '--',
    },
    {
      type: 'contactNo',
      label: t('candidates.review.contactNumber'),
      value: applicationInfo?.mobileNumber ?? '--',
    },
    {
      type: 'email',
      label: t('candidates.review.email'),
      value: applicationInfo?.emailAddress ?? '--',
    },
    {
      type: 'recruiter',
      label: t('candidates.review.recruiter'),
      value: applicationInfo?.recruiterName ?? '--',
    },
  ];

  return (
    <>
      <H6
        fontWeight="bold"
        children={t('candidates.review.personalInfo2')}
        style={{ paddingVertical: space[5] }}
      />

      <Container>
        <Row flexWrap={'wrap'}>
          {INFO_CONFIG?.map(({ type, label, value }, index) => {
            return (
              <Column
                key={type}
                width={'33%'}
                gap={space[2]}
                paddingBottom={index < 3 ? space[4] : 0}>
                <SmallLabel
                  children={label}
                  style={{ color: colors.palette.fwdGreyDarker }}
                />
                <LargeLabel children={value} />
              </Column>
            );
          })}
        </Row>
      </Container>
    </>
  );
}

const Container = styled(Card)(({ theme: { borderRadius, space } }) => ({
  width: '100%',
  padding: space[5],
  borderRadius: borderRadius.large,
}));
