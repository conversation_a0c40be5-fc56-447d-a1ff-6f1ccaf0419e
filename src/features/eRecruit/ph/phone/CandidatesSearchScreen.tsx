import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';
import ResponsiveView from 'components/ResponsiveView';
import {
  H7,
  H8,
  Icon,
  Row,
  SearchBar,
  SearchBarRef,
  SmallBody,
} from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Platform, ScrollView, TouchableOpacity, View } from 'react-native';
import { EdgeInsets, useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { FlashList } from '@shopify/flash-list';
import { NoResults } from 'features/lead/components/NoResult';
import CandidateListItem from '../components/CandidateListItem';
import { CandidateListItem as CandidateListItemType } from 'features/eRecruit/ph/types';
import useBoundStore from 'hooks/useBoundStore';
import {
  useGetApprovedRecruitList,
  useGetDeclineRecruitList,
  useGetTodoRecruitList,
} from 'features/eRecruit/hooks/ph/useGetRecruitsList';

const SORT_ORDER = 'ASC';

export default function CandidatesSearchScreen() {
  const { t } = useTranslation('eRecruit');
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const { sizes, colors, space } = useTheme();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const { data: toDoList, isLoading: isToDoListLoading } =
    useGetTodoRecruitList({
      sortOrder: SORT_ORDER,
    });
  const { data: approvedList, isLoading: isApprovedListLoading } =
    useGetApprovedRecruitList({ sortOrder: SORT_ORDER });
  const { data: declinedList, isLoading: isDeclinedListLoading } =
    useGetDeclineRecruitList({ sortOrder: SORT_ORDER });

  const isLoading =
    isToDoListLoading || isApprovedListLoading || isDeclinedListLoading;

  const [query, setQuery] = useState('');

  const resetSearch = () => {
    setQuery('');
    navigation.canGoBack() && navigation.goBack();
  };

  const {
    candidateSearch: { recentSearchCandidateItem },
    candidateSearchActions: { updateRecentCandidateSearch, trimRecentSearch },
  } = useBoundStore();

  useEffect(() => {
    if (recentSearchCandidateItem?.length > 5) trimRecentSearch();
  }, [recentSearchCandidateItem]);

  const emptyArr: CandidateListItemType[] = [];

  const data: CandidateListItemType[] = useMemo(
    () =>
      emptyArr.concat(toDoList ?? [], approvedList ?? [], declinedList ?? []),
    [toDoList, approvedList, declinedList],
  );

  const searchResult: CandidateListItemType[] = useMemo(
    () =>
      data.filter(
        item =>
          (item?.recruitName &&
            item?.recruitName.toLowerCase().includes(query.toLowerCase())) ||
          (item?.mobileNumber &&
            item?.mobileNumber.toLowerCase().includes(query.toLowerCase())),
      ),
    [query],
  );

  useEffect(() => {
    return () => resetSearch();
  }, []);

  const searchRef = useRef<SearchBarRef>(null);

  const handleSearch = (text: string) => setQuery(text);

  const handleClearBtn = () => {
    searchRef.current?.onClear();
    searchRef.current?.input.focus();
    setQuery('');
  };

  const SearchResultComponent = () => {
    return (
      <FlashList
        contentContainerStyle={{
          paddingHorizontal: isNarrowScreen ? sizes[3] : sizes[4],
          backgroundColor: colors.surface,
          paddingVertical: isNarrowScreen ? sizes[3] : sizes[4],
        }}
        ItemSeparatorComponent={() => <Row paddingBottom={space[3]} />}
        renderItem={({ item }) => (
          <CandidateListItem
            {...item}
            onRecentSearchPress={() => {
              if (recentSearchCandidateItem?.includes(item)) return;
              updateRecentCandidateSearch(item);
            }}
          />
        )}
        data={searchResult}
        keyExtractor={({ recruitID, recruitName }) =>
          `candidate_${recruitID}_${recruitName}`
        }
        estimatedItemSize={sizes[16]}
        ListEmptyComponent={!isLoading ? <NoResults /> : <></>}
      />
    );
  };

  return (
    <>
      <SearchContainer insets={insets}>
        <HeaderContainer
          narrowStyle={{ height: sizes[11], paddingTop: sizes[6] }}>
          <ResponsiveView
            narrowStyle={{ alignItems: 'flex-end', paddingLeft: sizes[1] }}
            style={{ flex: 1, alignItems: 'flex-start' }}>
            <CustomHeaderBackButton
              fill={colors.background}
              size={25}
              onPressBack={resetSearch}
            />
          </ResponsiveView>
          <H7
            fontWeight="bold"
            children={t('searchCandidate')}
            color={colors.background}
            style={{ flex: 4, textAlign: 'center' }}
          />
          <View style={{ flex: 1 }}></View>
        </HeaderContainer>

        <SearchBarContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchBar
            onChangeQuery={() => null}
            ref={searchRef}
            variant={'round'}
            placeholder=""
            collapseOnBlur={false}
            showClearBtnAfterQuery
            inputProps={{
              enablesReturnKeyAutomatically: true,
              returnKeyType: 'search',
              autoComplete: 'off',
              autoCorrect: false,
              spellCheck: false,
              onSubmitEditing(e) {
                handleSearch(e.nativeEvent.text);
              },
              autoFocus: true,
              style: { fontSize: sizes[4] },
            }}
            iconLeft={
              <ResponsiveView style={{ paddingRight: sizes[1] }}>
                <Icon.Search fill={colors.palette.fwdGreyDark} />
              </ResponsiveView>
            }
            isLoading={isLoading}
            activeBorderColor={colors.primary}
            inactiveBorderColor={colors.primary}
            style={{
              height: isWideScreen ? sizes[13] : sizes[12],
              paddingVertical: sizes[1],
            }}
            focusAfterClear
            debouncedDelay={0}
            onPressIconRight={handleClearBtn}
          />
        </SearchBarContainer>
        <SearchHintContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchHint children={'e.g. Candidate’s name, mobile number'} />
        </SearchHintContainer>
      </SearchContainer>

      <SearchResultContainer>
        {query ? (
          <SearchResultComponent />
        ) : (
          <>
            {recentSearchCandidateItem.length > 0 && (
              <ScrollView
                keyboardDismissMode="on-drag"
                keyboardShouldPersistTaps="never"
                contentContainerStyle={{ paddingBottom: space[4] }}>
                <RecentSearchContainer>
                  <H7
                    fontWeight="bold"
                    color={colors.secondaryVariant}
                    style={{
                      paddingHorizontal: sizes[4],
                      paddingTop: sizes[3],
                    }}>
                    {t('recentSearchTitle')}
                  </H7>

                  {!!recentSearchCandidateItem.length && (
                    <>
                      <RecentSearchCategoryTitle
                        children={`${t('searchCandidate')} (${
                          recentSearchCandidateItem?.length
                        })`}
                      />

                      <FlashList
                        contentContainerStyle={{
                          paddingHorizontal: isNarrowScreen
                            ? sizes[3]
                            : sizes[4],
                          backgroundColor: colors.surface,
                        }}
                        ItemSeparatorComponent={() => (
                          <Row paddingBottom={space[3]} />
                        )}
                        renderItem={({ item }) => {
                          return (
                            <TouchableOpacity
                              onPress={() => updateRecentCandidateSearch(item)}>
                              <CandidateListItem {...item} />
                            </TouchableOpacity>
                          );
                        }}
                        data={recentSearchCandidateItem}
                        keyExtractor={({ recruitID, recruitName }) =>
                          `candidate_${recruitID}_${recruitName}`
                        }
                        estimatedItemSize={3}
                      />
                    </>
                  )}
                </RecentSearchContainer>
              </ScrollView>
            )}
          </>
        )}
      </SearchResultContainer>
    </>
  );
}

const SearchContainer = styled.View(
  ({ theme, insets }: { insets: EdgeInsets; theme?: Theme }) => ({
    paddingTop: insets.top,
    backgroundColor: theme?.colors.primary,
    paddingBottom: theme?.space[5],
  }),
);

const HeaderContainer = styled(ResponsiveView)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal: Platform.OS === 'ios' ? theme.space[3] : 0,
}));

const SearchBarContainer = styled(ResponsiveView)(({ theme }) => ({
  paddingTop: theme.space[3],
  paddingBottom: theme.space[2],
  paddingHorizontal: theme?.space[4],
}));

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const SearchHint = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.background,
  paddingHorizontal: theme?.space[4],
}));

const SearchHintContainer = styled(ResponsiveView)(({ theme }) => ({
  color: theme.colors.primary,
  paddingHorizontal: theme?.space[4],
}));

const RecentSearchContainer = styled.View(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
  flex: 1,
}));

const RecentSearchCategoryTitle = styled(H8)(({ theme }) => {
  return {
    paddingHorizontal: theme.sizes[4],
    paddingVertical: theme.sizes[2],
    color: theme.colors.palette.fwdGreyDarkest,
  };
});
