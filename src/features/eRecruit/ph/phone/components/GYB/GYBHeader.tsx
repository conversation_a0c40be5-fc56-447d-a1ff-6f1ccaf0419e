import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Column,
  Icon,
  Row,
  SmallLabel,
  Text,
} from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { ViewStyle } from 'react-native';
import Animated, { AnimatedStyleProp } from 'react-native-reanimated';

export const HEADER_HEIGHT = 57;

export const GYBHeader = memo(function GYBHeader({
  style,
}: {
  style?: AnimatedStyleProp<ViewStyle>;
}) {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation('eRecruit');
  return (
    <Animated.View style={style}>
      <Container>
        <Box w={sizes[8]} />
        <Column width={`${(220 / 351) * 100}%`} gap={3}>
          <FirstColumn>
            {t('GYBAttendees.attendee')}
            {/* {'\n'} */}
          </FirstColumn>
          <SmallLabel color={colors.onSecondary}>
            {t('GYBAttendees.recruiterLeader')}
          </SmallLabel>
        </Column>
        <Row alignItems="center">
          <Body color={colors.onSecondary} style={{ width: '40%' }}>
            {t('GYBAttendees.attendedDate')}
          </Body>
          <Icon.ArrowDown size={sizes[4]} fill={colors.onSecondary} />
        </Row>
      </Container>
    </Animated.View>
  );
});
const Container = styled.View(({ theme }) => ({
  height: HEADER_HEIGHT,
  backgroundColor: theme.colors.secondary,
  paddingHorizontal: theme.space[3],
  flexDirection: 'row',
  alignItems: 'center',
}));

export const FakeHeader = styled.View(() => ({
  height: HEADER_HEIGHT,
}));

const SText = styled(Text)(({ theme }) => ({
  color: theme.colors.onSecondary,
  fontSize: theme.typography.body.size,
  width: `37%`,
}));

const FirstColumn = styled(SText)(() => ({
  // width: `${(220 / 351) * 100}%`,
}));
