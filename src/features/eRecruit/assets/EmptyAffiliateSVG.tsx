import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export default function EmptyAffiliateSVG(
  props: SvgProps & {
    width?: number;
    height?: number;
  },
) {
  return (
    <Svg
      width={props?.width ?? 160}
      height={props?.height ?? 134}
      viewBox="0 0 160 134"
      fill="none"
      {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M96.24 32.78l17.126-10.733h15.386c.962 0 1.304.681.802 1.464l-4.995 7.798c-.718 1.13-.701 1.842 0 2.936l4.992 7.799c.496.782.121 1.47-.801 1.47H97.987c-1.249 0-1.747-.58-1.747-1.747V32.78z"
        fill="#F3BB90"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M111.851 34.465H79.113V13h32.738c.831 0 1.512.71 1.512 1.584v18.297c0 .873-.681 1.584-1.512 1.584z"
        fill="#FFECDE"
      />
      <Path
        d="M77.535 14.584a1.582 1.582 0 113.162 0v104.838c0 .873-.707 1.578-1.584 1.578a1.577 1.577 0 01-1.578-1.578V14.584z"
        fill="#D1D6D4"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M89.125 94.254v13.483a2.746 2.746 0 01-2.74 2.738H74.152c-2.714 0-5.233-.664-7.277-1.782-1.375-.753-1.046-.616-2.673-.616h-4.92V93.906h7.48c2.163 0 1.57-.137 3.418-.998 1.89-.886 2.428-1.4 4.814-1.4l2.542-.355 3.164-.443.316-.044 5.115.835.007.002h.008c.***************.02.005h.01c1.508 0 2.949 1.238 2.949 2.746z"
        fill="#fff"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M100.725 74.945v14.17h-5.394c-1.278 0-.975-.081-2.05.528-.16.09-.325.18-.492.264-.236.12-.478.234-.725.344a15.506 15.506 0 01-5.917 1.253h-.011a6.294 6.294 0 01-.282.004H73.619a2.751 2.751 0 01-2.744-2.738V75.287a2.751 2.751 0 012.744-2.738l.038-.007h.005l.004-.002h.002l3.868-.668.958-.167 2.207.287 4.305.557c2.438 0 3.025.531 4.944 1.451 1.89.91 1.232.945 3.468.945h7.307z"
        fill="#fff"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.611 71.62zM60.123 62.587l-.258 2.32-.584 5.244v-14.17l.842 6.606z"
        fill="#A5CDFF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M89.125 56.32v13.483a2.752 2.752 0 01-2.74 2.746H74.152c-.137 0-.272-.002-.409-.004-.027-.002-.055-.002-.082-.002a18.857 18.857 0 01-.606-.03l-.2-.015a15.302 15.302 0 01-.629-.063c-.12-.015-.24-.03-.36-.049a8.926 8.926 0 01-.654-.105l-.217-.04c-.007-.002-.013-.004-.02-.004a14.557 14.557 0 01-1.396-.35c-.122-.038-.247-.076-.369-.116-.122-.04-.245-.083-.365-.127l-.285-.105a11.132 11.132 0 01-.436-.175c-3.188-1.344-2.207-1.213-5.936-1.213h-2.907V55.98h8.982c.939-3.217 4.541-6.172 9.273-7.296.843-.2 1.723-.342 2.63-.418.133-.017.264-.03.395-.03a2.672 2.672 0 110 5.345h5.824a2.746 2.746 0 012.74 2.738z"
        fill="#fff"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M134 74.946v14.17a2.086 2.086 0 01-2.082 2.082h-29.114a2.09 2.09 0 01-2.079-2.082v-14.17c0-1.148.932-2.087 2.079-2.087h29.114c1.15 0 2.082.94 2.082 2.087z"
        fill="#D1D6D4"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M119.285 31.309c-.717 1.13-.7 1.841 0 2.936l4.993 7.798c.496.783.121 1.47-.801 1.47h5.273c.92 0 1.298-.687.802-1.47l-4.993-7.796c-.7-1.095-.717-1.805 0-2.936l4.993-7.796c.502-.783.162-1.464-.802-1.464h-5.273c.962 0 1.303.681.801 1.464l-4.993 7.794z"
        fill="#F3BB90"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M59.282 55.98v14.171a2.06 2.06 0 01-.614 1.468 2.22 2.22 0 01-.308.255 2.368 2.368 0 01-.264.15c-.274.133-.58.207-.903.207H28.08a2.052 2.052 0 01-1.076-.302c-.027-.019-.057-.035-.084-.055a2.22 2.22 0 01-.308-.255A2.071 2.071 0 0126 70.151v-14.17c0-1.15.939-2.083 2.08-2.083h29.113a2.124 2.124 0 01.715.127c.**************.365.175.144.089.277.192.395.31.38.378.614.897.614 1.47z"
        fill="#D1D6D4"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M58.36 71.874zM58.668 54.51z"
        fill="#8ABFFF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M59.282 93.906v14.171a2.093 2.093 0 01-2.089 2.088H28.08a2.093 2.093 0 01-2.08-2.088V93.906a2.09 2.09 0 012.08-2.08h29.113c1.15 0 2.089.939 2.089 2.08z"
        fill="#D1D6D4"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M100.725 83.842v5.273h-5.394c-1.278 0-.975-.082-2.05.528a13.533 13.533 0 01-1.645.785 15.176 15.176 0 01-5.22 1.069c-.08.002-.16.008-.24.01h-.017c-.748.015-1.497.021-2.248.026h-.06c-1.047.006-2.098.004-3.15 0-1.055-.005-2.112-.013-3.165-.017-.848-.005-1.695-.009-2.541-.009h-1.376a2.751 2.751 0 01-2.744-2.738V86.24h14.979c1.651 0 3.379-.371 4.826-1.19 1.877-1.065 2.5-1.208 4.651-1.208h5.394z"
        fill="#FFECDE"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M89.125 94.255v13.483a2.746 2.746 0 01-2.74 2.738H74.152c-2.714 0-5.233-.664-7.277-1.782-1.375-.753-1.046-.616-2.673-.616h-4.92v-5.267h5.273c2.134.006 2.78.12 4.855 1.255 1.428.789 3.122 1.137 4.742 1.137h6.607c1.268 0 3.092-.623 3.092-2.679v-10.99h.034c.188 0 .485-.013.8-.026.548-.018 1.166-.04 1.447-.006l.006.002h.008l.02.004h.01c1.508 0 2.949 1.239 2.949 2.747zM89.125 56.32v13.483a2.752 2.752 0 01-2.74 2.746H74.152c-.137 0-.272-.002-.409-.004l-.078-.004a14.793 14.793 0 01-.61-.027l-.2-.015c-.105-.008-.21-.02-.316-.03a14.14 14.14 0 01-.673-.082 8.927 8.927 0 01-.654-.105c-.074-.013-.146-.028-.217-.04-.007-.002-.013-.005-.02-.005a14.557 14.557 0 01-1.396-.35c-.122-.038-.247-.076-.369-.116-.122-.04-.245-.082-.365-.126-.095-.034-.19-.07-.285-.106a11.29 11.29 0 01-.436-.175c-3.188-1.344-2.207-1.213-5.936-1.213h-2.907v-5.244h5.274c2.02.085 2.792.407 5.621 1.597 1.249.532 2.624.77 3.976.77H80.7c2.08 0 3.151-.513 3.151-2.744V53.582h2.534a2.746 2.746 0 012.74 2.738z"
        fill="#FFECDE"
      />
    </Svg>
  );
}
