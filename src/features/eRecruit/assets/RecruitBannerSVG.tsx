import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, Clip<PERSON><PERSON>, SvgProps } from 'react-native-svg';

export default function RecruitBannerSVG(
  props: SvgProps & {
    width?: number;
    height?: number;
  },
) {
  return (
    <Svg
      width={props?.width ?? 244}
      height={props?.height ?? 100}
      viewBox="0 0 244 100"
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_20197_8053)">
        <Path
          d="M160.488 6.625c-8.917 0-15.957 7.138-15.957 16.179 0 3.33.938 5.234 2.816 9.041 1.877 3.807.469 5.948-.704 7.138-.235.238 0 .714.234.714h13.611c8.917 0 15.957-7.852 15.957-16.893s-7.04-16.179-15.957-16.179z"
          fill="#F3BB90"
        />
        <Path
          d="M171.048 24.945h-19.712c-.704 0-1.173-.476-1.173-1.19 0-.713.469-1.19 1.173-1.19h19.712c.704 0 1.173.477 1.173 1.19 0 .714-.469 1.19-1.173 1.19zM171.048 18.997h-19.712c-.704 0-1.173-.476-1.173-1.19 0-.713.469-1.19 1.173-1.19h19.712c.704 0 1.173.476 1.173 1.19s-.469 1.19-1.173 1.19zM160.019 30.418h-8.683c-.469 0-.939-.476-.939-.952s.47-.952.939-.952h8.683c.469 0 .938.476.938.952 0 .714-.469.952-.938.952z"
          fill="#E87722"
        />
        <Path
          d="M126.827 69.94s14.549 22.126 25.579 24.743c11.029 2.617 33.791-7.851 33.791-7.851l-.234-7.614s-25.579 1.19-31.915-.476c-6.336-1.427-12.906-16.892-12.906-16.892"
          fill="#FED241"
        />
        <Path
          d="M111.574 118l-2.346-47.823s2.816-13.562 13.61-13.562c4.694 0 7.744-.238 9.856 0 7.04.714 8.214 5.234 9.152 8.327.235.714-.469 37.593-.938 49.489 0 1.903-1.643 3.331-3.52 3.331l-25.814.238z"
          fill="#FED241"
        />
        <Path
          d="M127.531 55.901c-.938 0-1.877 0-2.816.238-.938-.714-1.408-1.427-1.408-1.427v-12.61l3.99 3.33 5.632 1.428v8.09s0 .713-.47 1.427c-1.642-.476-3.285-.476-4.928-.476z"
          fill="#F8E3D2"
        />
        <Path
          d="M133.398 49.002c1.173 0 2.347-.238 2.816-.714 3.285-5.472-.469-19.986-.469-19.986l-12.203 2.142-.469 9.755c0 2.617.938 4.996 3.05 6.661 2.347 1.428 4.928 2.142 7.275 2.142z"
          fill="#FBE2D0"
        />
        <Path
          d="M129.409 30.92s3.989.713 8.213 0c2.816-.477 2.581-2.856 2.112-4.283-.235-.714-.939-.952-1.408-.714-1.408.476-3.989.714-7.744.238s-4.693-1.19-7.275 0c-3.285 1.665-2.581 7.851-2.581 7.851l1.877 12.372c0 .238.235.238.47.238.234 0 .469-.238.469-.475 0-1.428.235-7.614.235-9.755.704-1.19 1.408-1.19 2.112-.952.704.238.938.952.938 1.665V48.05c0 1.427.939 2.617 2.112 3.093 2.582.952 6.805 2.617 8.213 2.141 2.112-.713.704-10.468.704-10.468s-2.346-.952-4.223.951c-1.878 2.142-4.224 0-4.224 0V30.92z"
          fill="#183028"
        />
        <Path
          d="M124.481 34.964c.938 0 1.877.714 2.112 1.903.234 1.19-.47 2.142-1.408 2.38-.939 0-1.878-.714-2.112-1.904-.235-1.19.469-2.141 1.408-2.38z"
          fill="#FBE2D0"
        />
        <Path
          d="M113.451 88.021c0 5.71 4.694 24.269 6.102 29.979h-7.04c-1.643 0-3.051-.714-3.755-2.141-4.458-6.9-10.794-18.32-10.794-25.934 0-11.42 18.303-30.693 18.303-30.693l10.795 12.135c-.235.237-13.611 9.992-13.611 16.654z"
          fill="#FED241"
        />
        <Path
          d="M122.838 74.697s-12.202 9.755-9.152 16.655L119.553 118s-3.99-24.269-3.286-30.693c0 0 6.571-9.04 8.448-9.517l-1.877-3.093z"
          fill="#F9E49C"
        />
        <Path
          d="M132.225 53.522l-8.214-.238c-.469 0-.704.238-.938.476l-.939 1.903c-.235.476.235.952.469.952h9.387c.469 0 .939-.476.939-.952v-1.427c.234-.238-.235-.714-.704-.714z"
          fill="#FED241"
        />
        <Path
          d="M81.537 54.712l-3.755 8.565-6.336-5.472 4.459-12.61 5.632 9.517z"
          fill="#F8E3D2"
        />
        <Path
          d="M83.649 55.426c.939.238 2.112.238 2.581-.238 3.755-4.283 2.347-16.893 2.347-16.893l-4.459-3.807-6.805 3.093-1.643 6.9c-.47 2.38.235 6.424 1.643 8.09a15.38 15.38 0 006.336 2.855zM114.859 97.776l-20.65-.714-14.784-18.796 5.397 22.603s1.408 2.141 4.459 3.331c3.05.952 25.578-3.807 25.578-3.807h.235c-.235-.952-.235-1.665-.235-2.617z"
          fill="#FBE2D0"
        />
        <Path
          d="M77.079 104.914c1.407-2.855 5.631-10.707 9.151-18.082 1.174-2.617 1.408-5.948.235-8.566-3.05-7.137-7.744-16.892-7.744-16.892l-7.04-3.807s-8.917 11.658-11.03 19.272c-1.876 6.662-2.58 23.316-2.815 27.837l19.243.238z"
          fill="#B7E0D5"
        />
        <Path
          d="M62.998 79.218s9.856 23.317 14.55 23.317c4.693 0 30.975-11.42 30.975-11.42l-.704-4.045-26.752 4.52s-2.346-6.424-3.989-13.8c-.704-2.855-2.816-5.234-5.866-5.948-6.336-.951-8.214 7.376-8.214 7.376z"
          fill="#FBE2D0"
        />
        <Path
          d="M76.14 42.102s2.816 1.903 11.968-4.045c1.407.476-.705-12.848-12.438-8.803-8.213 2.617-7.744 10.706-7.274 14.99.469 4.758-2.112 7.85-5.163 11.42-2.816 2.855-2.581 8.327-4.459 11.182-2.112 3.569-4.458 3.33-6.336 6.9-1.877 3.806-2.111 8.327.47 11.896 1.408 1.903 4.693 6.662 9.856 4.996 2.816-.951 3.754-5.948 3.754-10.468 0-3.807 1.878-7.376 4.928-9.755 2.816-2.142 5.867-5.235 6.57-8.566 1.409-6.423-1.876-19.747-1.876-19.747z"
          fill="#183028"
        />
        <Path
          d="M74.966 42.34c.705-.476 1.878-.238 2.347.713.704.952.704 1.904 0 2.38-.704.475-1.877.237-2.347-.714-.703-.714-.703-1.904 0-2.38zM106.646 90.163l-.469-1.428c-.47-1.427.234-3.33 1.877-3.807l7.979-2.617c1.408-.476 3.285.238 3.754 1.904.704 2.379-.469 4.758-2.816 5.71l-6.57 2.141c-1.643.476-3.286-.238-3.755-1.903z"
          fill="#FBE2D0"
        />
        <Path
          d="M107.819 87.07c.704.476 1.408.238 1.877-.476l3.051-4.759c.469-.713.469-1.903-.235-2.14-.469-.239-1.408-.239-1.877.475l-3.051 4.758c-.469.476-.234 1.428.235 2.142zM185.152 83.442l-.025-1.502c-.025-1.504 1.212-3.12 2.922-3.096l8.396-.177c1.485-.044 3.067 1.186 3.021 2.917-.033 2.482-1.859 4.417-4.382 4.643l-6.91.132c-1.71-.023-3.068-1.186-3.022-2.916z"
          fill="#FBE2D0"
        />
        <Path
          d="M187.185 80.825c.531.66 1.274.638 1.933.093l4.323-3.663c.659-.545 1.012-1.683.41-2.116-.377-.365-1.273-.638-1.933-.092l-4.322 3.662c-.589.318-.647 1.297-.411 2.116zM112.513 99.917l-.235-1.427c-.235-1.428.939-2.855 2.347-3.093l7.978-.952c1.408-.238 2.816.952 3.051 2.38.234 2.379-1.408 4.52-3.755 4.758l-6.336.714c-1.642.237-2.816-.952-3.05-2.38z"
          fill="#FBE2D0"
        />
        <Path
          d="M114.39 97.062c.469.714 1.408.714 1.877 0l3.755-3.807c.469-.475.704-1.665.234-2.14-.469-.477-1.408-.715-1.877 0l-3.754 3.806c-.704.714-.704 1.665-.235 2.141z"
          fill="#FBE2D0"
        />
        <Path
          d="M71.68 37.581c.94 3.57 6.102 5.473 11.5 3.807 5.397-1.427 8.213-5.472 7.743-9.279-.469-4.996-6.1-5.472-11.498-3.807-5.397 1.428-8.917 5.71-7.744 9.28zM81.772 44.242c0 .476.234.714.703.714.235 0 .47-.476.47-.951 0-.476-.235-.714-.704-.714-.47 0-.47.476-.47.952zM85.761 43.29c0 .476.235.714.47.714.234 0 .468-.475.468-.95 0-.476-.234-.714-.469-.714-.234 0-.469.475-.469.95z"
          fill="#183028"
        />
        <Path
          d="M84.353 51.143h-.939c-.938-.238-1.642-.714-1.642-.714s-.235-.238 0-.476c0 0 .234-.238.47 0 0 0 2.346 1.904 4.458-.476 0 0 .234-.238.47 0v.476c-1.174.714-2.113 1.19-2.817 1.19zM84.587 52.57c-.469 0-.703-.238-.703-.238-.235 0-.235-.238 0-.238 0-.237.234-.237.234 0 0 0 .47.238 1.172 0h.235v.238c-.469 0-.703.238-.938.238zM85.996 48.05c-.235 0-.235 0 0 0-.235-.238-.235-.476-.235-.476.47-.238.704-.714.704-.952 0-.475-.47-.951-.939-1.19-.704-.237-.938-2.616-.938-3.568 0-.238 0-.238.234-.238.235 0 .235 0 .235.238 0 1.428.235 2.855.47 3.093.469.238 1.173.714 1.173 1.428.234.475 0 1.19-.704 1.665z"
          fill="#E77825"
        />
        <Path
          d="M132.929 36.39c0 .476.234.952.703.952s.704-.238.704-.951c0-.476-.235-.951-.704-.951s-.703.475-.703.95z"
          fill="#183028"
        />
        <Path
          d="M135.979 41.15c-.469 0-.704-.476-.938-.476 0 0 0-.238.234-.238 0 0 .235 0 .235.238 0 0 .235.238.704.238.235 0 .469 0 .704-.238s.469-.714 0-2.617a53.134 53.134 0 00-.469-2.141s0-.238.234-.238c0 0 .235 0 .235.238 0 0 .235.951.704 2.141.235 1.665.235 2.617 0 2.855s-.469.238-.469.476c-.939-.238-1.174-.238-1.174-.238z"
          fill="#E77825"
        />
        <Path
          d="M170.049 114.194l13.611-40.686c.469-1.427 2.111-1.665 3.05-.713.939 1.19 2.112 3.093 3.52 6.662.235.475.235.951 0 1.427-4.224 9.28-3.989 25.22-9.856 34.975-.234.476-.938.952-1.642.952h-7.04c-1.174-.238-2.112-1.428-1.643-2.617z"
          fill="#FBE2D0"
        />
        <Path
          d="M206.657 57.091l-5.632-9.517-7.275 3.807 2.581 8.09h.235c2.112-2.38 6.805-1.19 10.091-2.38z"
          fill="#F8E3D2"
        />
        <Path
          d="M200.086 32.823l2.112 11.42c-1.173.239-1.408 1.666-.939 2.618v.952c-1.642 1.903-4.693 4.52-9.386 4.758-7.275.238-6.336-14.276-6.336-14.276l14.549-5.472z"
          fill="#FBE2D0"
        />
        <Path
          d="M205.483 40.437c-.234-.952.47-2.855.939-3.57 3.051-4.52-.939-6.185-2.347-6.661-1.642-.476-1.877-3.093-3.989-4.283-3.285-1.427-5.397 1.666-7.04 1.666-1.877 0-3.52-2.142-5.632-.238-2.112 1.903-1.642 3.807-2.346 4.996-.704.952-2.582-.952-4.459 2.38-2.816 4.996 4.928 4.996 4.928 4.996s4.459.714 9.386-2.617c.704-.476 1.643-.476 2.112 0 1.643 1.427 3.755 10.706 3.755 10.706l1.408-1.903c.235 0 3.989-1.666 3.285-5.472z"
          fill="#183028"
        />
        <Path
          d="M201.494 42.34c-.235.952-.939 1.666-1.877 1.428-.939-.238-1.643-.952-1.408-1.904.234-.951.938-1.665 1.877-1.427.939 0 1.643.951 1.408 1.903z"
          fill="#FBE2D0"
        />
        <Path
          d="M182.955 75.412v-.714c2.347-9.041 7.744-18.796 17.6-19.034h.235c13.845 0 23.936 11.183 26.986 15.465.704.952.704 1.904 0 2.855l-8.917 12.848c-.235.476-.469.952-.469 1.428v26.171a2.368 2.368 0 01-2.347 2.38h-30.741c-1.408 0-2.347-1.19-2.347-2.38V75.412z"
          fill="#F89F60"
        />
        <Path
          d="M207.595 56.616l-3.754-4.283-9.387 1.903-.235 3.331 13.376-.951z"
          fill="#F89F60"
        />
        <Path
          d="M220.206 114.431l8.917-15.703-11.264-11.182 10.325-14.752s12.672 14.276 12.672 24.03c0 4.521-4.693 12.135-9.856 19.511a1.286 1.286 0 01-1.173.713h-8.213c-1.408-.237-2.112-1.427-1.408-2.617z"
          fill="#FBE2D0"
        />
        <Path
          d="M195.861 41.15c-.234.238-.468.476-.703.238-.234 0-.469-.476-.234-.713.234-.238.469-.476.703-.238.234.238.234.475.234.713zM188.821 42.102c-.234.238-.469.475-.703.238-.234 0-.469-.476-.235-.714.235-.237.469-.475.704-.237.234 0 .469.475.234.713z"
          fill="#183028"
        />
        <Path
          d="M191.638 46.385c-.234 0-.234 0-.469-.238-.469-.238-.469-1.19-.469-2.617 0-.952.234-1.903.234-1.903s0-.238.235-.238c0 0 .235 0 .235.238 0 0-.235.951-.235 1.903 0 1.666.235 2.141.469 *************.47.238.704.238.235 0 .47-.238.47-.476 0 0 0-.237.234-.237 0 0 .235 0 .235.237 0 .238-.235.476-.704.714s-.704.238-.939.238zM192.108 48.288h-.704c-.704 0-1.174-.475-1.174-.475v-.238h.235s1.877 1.19 3.285-.714h.235v.238c-.469.952-1.173 1.19-1.877 1.19zM192.342 49.477h-.704s-.234-.237 0-.237c0 0 .235-.238.235 0 0 0 .234.237.938-.238h.234v.238c-.234 0-.469 0-.703.237z"
          fill="#E77825"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_20197_8053">
          <Path fill="#fff" transform="translate(0 -15)" d="M0 0H272V133H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
