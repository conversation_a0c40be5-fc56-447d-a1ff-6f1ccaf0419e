import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SvgProps } from 'react-native-svg';

export default function RecruitmentStaffSVG(
  props: SvgProps & {
    width?: number;
    height?: number;
  },
) {
  return (
    <Svg width={92} height={73} viewBox="0 0 92 73" fill="none" {...props}>
      <G clipPath="url(#clip0_21490_33709)">
        <Path
          d="M71.545.813c-8.766 0-15.686 6.98-15.686 15.824 0 3.258.923 5.12 2.768 8.843 1.846 3.723.462 5.817-.692 6.981-.23.233 0 .698.231.698h13.38c8.765 0 15.685-7.68 15.685-16.522C87.23 7.794 80.31.812 71.545.812z"
          fill="#B6E6D8"
        />
        <Path
          d="M44.365 39.37c1.469-.346 3.099-1.17 3.323-2.649.112-.763-.187-1.551-.013-2.315.373-1.712 2.377-2.143 3.82-2.611 1.767-.567 2.999-2.02 3.223-3.867.224-1.786-.46-3.584-1.518-5.05-1.867-2.586-4.131-3.817-6.919-5.197-2.351-1.17-3.633-3.387-5.848-4.668a8.245 8.245 0 00-5.114-1.046c-.398.049-.809.123-1.194.037-1.058-.247-1.842-.85-3-.887-1.169-.037-2.338.221-3.446.616-3.235 1.133-6.47 3.46-7.416 6.884-.585 2.143-.772 4.224-2.787 5.58-2.551 1.711-5.177 3.288-4.99 6.785.112 2.081 1.53 4.077 3.534 4.73 1.306.43 2.912.369 3.807 1.391.498.579.66 1.367 1.033 2.032.772 1.38 2.427 2.082 4.02 2.143 1.592.062 3.147-.406 4.666-.862 2.277-.69 4.815-2.057 7.13-1.478 2.612.653 5.002 1.047 7.69.431z"
          fill="#183028"
        />
        <Path
          d="M54.595 53.631s3.683 4.877 6.757 4.877c3.073 0 13.5-12.906 13.5-12.906l2.452 3.916S68.556 68.386 60.58 68.546c-7.976.148-13.961-8.658-13.812-9.114.15-.456 5.214-5.788 5.214-5.788h2.613v-.013z"
          fill="#F3BB90"
        />
        <Path
          d="M40.177 42.535s5.91-.221 8.91 1.836c2.152 1.465 6.88 9.741 6.88 9.741l-5.437 4.582s1.294 4.988-3.77 7.5l-.921 12.07s-9.52 5.468-22.398 0c0 0-2.451-22.637-4.753-20.654-2.302 1.97-3.683 3.72-3.683 3.72l-8.599-8.425s9.37-9.286 12.468-10.751c7.28-3.412 13.153-.136 13.153-.136l8.138.517h.012z"
          fill="#FED141"
        />
        <Path
          d="M14.204 60.542s-4.99 5.764-4.418 7.352c.573 1.59 9.382 14.73 10.565 17.218 1.182 2.488.833 3.966.833 3.966l-4.492 2.167s-.236-3.51-1.207-4.544C14.515 85.666-.342 70.518.006 65.727c.348-4.779 7.155-12.094 7.155-12.094l7.03 6.897.013.012zM42.781 24.009s.647-1.269 1.332-.85c1.73 1.035.199 2.845-.822 3.313l-.51-2.463z"
          fill="#F3BB90"
        />
        <Path
          d="M31.154 10.883c-4.816.862-8.263 5.235-7.964 10.062a9.545 9.545 0 001.331 4.323c1.717 3.19 3.136 5.752 4.729 7.55 1.617 1.822 2.538 4.162 2.463 6.589l-.074 2.45c5.948 2.143 8.274.666 8.274.666l-.82-7.156c6.01-1.404 3.906-12.439 3.533-15.727 0-.123-.025-.247-.037-.37-.635-5.505-5.836-9.384-11.423-8.387h-.012z"
          fill="#F3BB90"
        />
        <Path
          d="M31.515 20.65c-1.468 2.18-3.06 4.334-4.056 6.76-.112.272-.896 2.267-.572 2.44-1.705-.998-2.912-2.649-3.97-4.324-1.816-2.894-1.816-6.601-.348-9.63 1.456-2.993 4.268-4.792 7.516-5.42 3.484-.677 7.453.05 9.942 2.71 2.326 2.5 2.824 5.936 3.422 9.138-.685-3.596-5.923-5.751-9.022-6.7.087.025-1.841 3.387-1.94 3.547-.312.493-.636.973-.959 1.466l-.012.012z"
          fill="#183028"
        />
        <Path
          d="M27.787 27.62c-.187-.296-.498-.493-.834-.591-.697-.197-1.916-.345-2.625.542-1.058 1.305 1.692 4.162 3.882 2.34 0 0 .597-.74-.423-2.291zM71.695 49.82s1.506-3.411 3.46-4.741c1.953-1.33 3.459-.8 3.459-.8l2.115-1.306s1.232 6.638-3.87 9.63c-.423.247-5.164-2.783-5.164-2.783z"
          fill="#F3BB90"
        />
        <Path
          d="M75.653 35.914l5.462 1.564.797 1.441-3.783 12.944-1.456.788-5.463-1.564-.796-1.44 3.783-12.945 1.456-.788z"
          fill="#0696A8"
        />
        <Path
          d="M81.627 44.718a.923.923 0 00.684-1.552l-1.555-1.65a.946.946 0 00-1.32-.05.923.923 0 00-.049 1.306l1.555 1.65a.94.94 0 00.685.296z"
          fill="#F3BB90"
        />
        <Path
          d="M81.025 46.25a.923.923 0 00.684-1.552l-1.555-1.65a.946.946 0 00-1.319-.05.923.923 0 00-.05 1.305l1.556 1.65a.94.94 0 00.684.296z"
          fill="#F3BB90"
        />
        <Path
          d="M80.4 47.652a.923.923 0 00.684-1.552l-1.555-1.65a.946.946 0 00-1.319-.05.923.923 0 00-.05 1.306l1.556 1.65a.94.94 0 00.684.296z"
          fill="#F3BB90"
        />
        <Path
          d="M79.421 48.576c.137 0 .286-.05.398-.148a.604.604 0 00.075-.862l-1.418-1.663a.62.62 0 00-.871-.074.604.604 0 00-.075.862l1.418 1.663a.615.615 0 00.473.222z"
          fill="#F3BB90"
        />
        <Path
          d="M33.007 28.361c-1.468.357-2.95-.53-3.31-1.983a2.705 2.705 0 012.003-3.276c1.469-.357 2.95.53 3.31 1.971a2.705 2.705 0 01-2.003 3.276v.012zm-1.232-4.938c-1.282.32-2.078 1.6-1.754 2.882a2.407 2.407 0 002.911 1.736c1.282-.32 2.078-1.6 1.755-2.882a2.407 2.407 0 00-2.912-1.736zM41.1 26.376c-1.468.357-2.948-.53-3.31-1.97a2.705 2.705 0 012.004-3.276c1.468-.357 2.95.53 3.31 1.983a2.705 2.705 0 01-2.003 3.276v-.013zm-1.231-4.938c-1.282.32-2.078 1.6-1.755 2.882a2.407 2.407 0 002.912 1.736c1.282-.32 2.078-1.6 1.755-2.882a2.407 2.407 0 00-2.912-1.736z"
          fill="#183028"
        />
        <Path
          d="M29.94 26.474l-2.471.456.059.314 2.47-.456-.058-.314zM31.886 26.266c.063.419.374.726.685.677.31-.05.51-.431.448-.862-.063-.419-.374-.727-.685-.677-.31.049-.51.43-.448.862zM39.87 23.852c.063.418.374.726.685.677.311-.05.51-.431.448-.862-.062-.419-.373-.727-.684-.677-.311.049-.51.43-.448.862z"
          fill="#183028"
        />
        <Path
          d="M37.032 32.226a3.238 3.238 0 01-.846 0c-.92-.123-1.568-.591-1.593-.604a.245.245 0 01-.05-.332.217.217 0 01.324-.05c.1.062 2.327 1.626 4.156-.812.075-.111.224-.123.336-.05.1.075.124.222.05.333-.772 1.035-1.63 1.429-2.364 1.527l-.013-.012zM37.28 33.43c-.449.061-.735-.087-.747-.099-.112-.061-.162-.21-.1-.32.063-.11.2-.16.311-.099.025 0 .436.197 1.046-.16a.242.242 0 01.323.087.237.237 0 01-.087.32 1.98 1.98 0 01-.747.27zM37.229 29.187c-.087.024-.175 0-.224-.086a.229.229 0 01.05-.308c.447-.308.647-.653.584-1.023-.062-.455-.547-.886-.858-.985-.66-.21-.946-2.241-1.045-3.116-.013-.123.074-.234.186-.246a.213.213 0 01.237.197c.149 1.38.497 2.66.746 2.746.41.136 1.058.64 1.157 1.33.05.37-.037.924-.771 1.442l-.075.036.013.013z"
          fill="#E77825"
        />
        <Path
          d="M35.013 25.414l-.224-.234c1.792-1.761 3.297-.788 3.31-.776l-.187.271s-1.306-.825-2.899.739z"
          fill="#183028"
        />
        <Path
          d="M70.49 7.63l-10.134 2.662a.797.797 0 00-.566.98l4.551 16.653a.817.817 0 00.992.573l12.908-3.392a.797.797 0 00.566-.98L74.328 7.739a1.09 1.09 0 00-1.327-.766l-2.51.66v-.003z"
          fill="#E87722"
        />
        <Path
          d="M72.08 13.91l-7.161 1.895a.426.426 0 01-.52-.3.42.42 0 01.299-.515l7.16-1.895a.426.426 0 01.52.3.42.42 0 01-.298.516zM72.682 16.02l-7.161 1.895a.426.426 0 01-.52-.3.42.42 0 01.298-.516l7.161-1.895a.426.426 0 01.52.3.42.42 0 01-.298.516zM73.244 18.134l-7.16 1.894a.426.426 0 01-.52-.3.42.42 0 01.298-.516l7.16-1.894a.426.426 0 01.52.3.42.42 0 01-.298.516zM76.229 13.548l-.479.823 3.506 2.008.479-.823-3.506-2.008z"
          fill="#fff"
        />
        <Path
          d="M81.312 8.724c.967.558 1.117 2.108.334 3.463l-1.923 3.332-3.501-2.021 1.923-3.331c.78-1.355 2.2-2.001 3.167-1.443z"
          fill="#F3BB90"
        />
        <Path
          d="M76.673 10.489a1.504 1.504 0 011.247.062l.219-.378c.057-.1.118-.194.182-.287l-.005-.002a2.299 2.299 0 00-1.927-.117c-1.664.64-1.901 2.304-2.114 3.77-.131.92-.257 1.79-.68 2.395-.272.385-.853.462-1.31.403a.386.386 0 00-.433.334.391.391 0 00.338.433c.37.048.72.028 1.03-.054.429-.112.783-.342 1.012-.673.529-.757.673-1.756.811-2.724.2-1.378.387-2.681 1.63-3.162z"
          fill="#183028"
        />
        <Path
          d="M72.708 20.927l2.312 1.334 4.205-5.913-3.5-2.022-3.017 6.6zM72.003 23.657l.972.561L75 22.256l-2.312-1.334-.686 2.735z"
          fill="#F3BB90"
        />
        <Path
          d="M72.22 23.802l-.382.662a.272.272 0 00.1.371.272.272 0 00.373-.098l.383-.661-.474-.274z"
          fill="#183028"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_21490_33709">
          <Path fill="#fff" d="M0 0H92V73H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
