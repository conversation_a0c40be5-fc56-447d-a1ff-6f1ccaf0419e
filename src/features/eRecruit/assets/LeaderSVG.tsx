import React from 'react';
import Svg, { Path } from 'react-native-svg';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';

export default function LeaderSVG(
  props: SvgPictogramProps & { isDisabled?: boolean },
) {
  const { size, height, width, isDisabled } = props;
  const iconHeight = size ?? height ?? 40;
  const iconWidth = size ?? width ?? 40;
  return (
    <Svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 40 40"
      fill="none"
      {...props}>
      {isDisabled ? (
        <>
          <Path
            d="M27.114 22.617c-.16-.039-.319-.078-.473-.134L21.3 20.52h-6.935a536.678 536.678 0 01-5.117 1.96c-.18.067-.364.115-.552.16a3.567 3.567 0 00-2.7 3.462v2.366a2.927 2.927 0 002.927 2.927H26.91a2.927 2.927 0 002.927-2.927v-2.386a3.568 3.568 0 00-2.723-3.465z"
            fill="#B3B6B8"
          />
          <Path
            d="M17.74 21.31c-3.795 0-6.898-3.104-6.898-6.899v-.955c0-3.795 3.103-6.898 6.898-6.898 3.795 0 6.899 3.103 6.899 6.898v.955c0 3.792-3.104 6.899-6.899 6.899z"
            fill="#DBDFE1"
          />
          <Path
            d="M31.862 22.463a6.366 6.366 0 00-4.237-1.61 6.405 6.405 0 00-4.779 2.14 6.396 6.396 0 00.527 9.015 6.373 6.373 0 004.24 1.61 6.389 6.389 0 004.776-2.14c2.341-2.63 2.103-6.674-.527-9.015z"
            fill="#636566"
          />
          <Path
            d="M28.044 23.962l.773 1.568a.48.48 0 00.359.26l1.73.253c.39.056.547.535.264.812l-1.252 1.221a.478.478 0 00-.137.42l.297 1.723a.475.475 0 01-.69.501l-1.548-.812a.476.476 0 00-.443 0l-1.548.812a.474.474 0 01-.69-.501l.297-1.723a.478.478 0 00-.137-.42l-1.252-1.22a.476.476 0 01.263-.813l1.731-.252a.48.48 0 00.359-.26l.773-1.57a.475.475 0 01.851 0z"
            fill="#fff"
          />
        </>
      ) : (
        <>
          <Path
            d="M27.114 22.617c-.16-.039-.319-.078-.473-.134L21.3 20.52h-6.935a536.678 536.678 0 01-5.117 1.96c-.18.067-.364.115-.552.16a3.567 3.567 0 00-2.7 3.462v2.366a2.927 2.927 0 002.927 2.927H26.91a2.927 2.927 0 002.927-2.927v-2.386a3.568 3.568 0 00-2.723-3.465z"
            fill="#E87722"
          />
          <Path
            d="M17.74 21.31c-3.795 0-6.898-3.104-6.898-6.899v-.955c0-3.795 3.103-6.898 6.898-6.898 3.795 0 6.899 3.103 6.899 6.898v.955c0 3.792-3.104 6.899-6.899 6.899z"
            fill="#F3BB90"
          />
          <Path
            d="M31.862 22.463a6.366 6.366 0 00-4.237-1.61 6.405 6.405 0 00-4.779 2.14 6.396 6.396 0 00.527 9.015 6.373 6.373 0 004.24 1.61 6.389 6.389 0 004.776-2.14c2.341-2.63 2.103-6.674-.527-9.015z"
            fill="#183028"
          />
          <Path
            d="M28.044 23.962l.773 1.568a.48.48 0 00.359.26l1.73.253c.39.056.547.535.264.812l-1.252 1.221a.478.478 0 00-.137.42l.297 1.723a.475.475 0 01-.69.501l-1.548-.812a.476.476 0 00-.443 0l-1.548.812a.474.474 0 01-.69-.501l.297-1.723a.478.478 0 00-.137-.42l-1.252-1.22a.476.476 0 01.263-.813l1.731-.252a.48.48 0 00.359-.26l.773-1.57a.475.475 0 01.851 0z"
            fill="#fff"
          />
        </>
      )}
    </Svg>
  );
}
