import { TouchableOpacity } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import { Typography, Row, Box, Icon } from 'cube-ui-components';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import FlagLabel from 'components/FlagLabel';
import { useTranslation } from 'react-i18next';
import { contactHelper } from 'features/lead/utils';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
} from 'types/eRecruit';
import { filterLabelMap } from 'features/eRecruit/config';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';

export default function CandidateCard({
  info,
}: {
  info: ApplicationListResponds;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const candidate = 'eRecruit.candidate';
  const processedDate = dateHandler({
    cubeStatus: info.cubeStatus,
    stage: info.stage,
    info,
  });

  const updateDate = processedDate ? new Date(processedDate) : undefined;

  const candidateLabelHandler = (status: CubeStatusKeys | null) => {
    switch (status) {
      case 'APPROVED':
        return (
          <FlagLabel
            medium
            type={'leadStatus_lightGreen20'}
            content={t(`${candidate}.approved`)}
            Icon={Icon.Tick}
          />
        );
      case 'REJECTED':
        return (
          <FlagLabel
            medium
            type={'disable_grey'}
            content={t(`${candidate}.rejected`)}
          />
        );
      case 'PENDING_PAYMENT':
      case 'PENDING_LEADER_APPROVAL':
      case 'REMOTE_CHECKING':
      case 'REMOTE_SIGNATURE':
        return (
          <FlagLabel
            medium
            type={'primary_orange'}
            content={filterLabelMap?.[status]}
          />
        );

      default:
        return <></>;
    }
  };

  return (
    <Row
      gap={space[2]}
      alignItems="center"
      paddingY={space[3]}
      paddingLeft={space[3]}
      paddingRight={space[2]}
      borderRadius={borderRadius.large}
      backgroundColor={colors.background}>
      <Box flex={1} gap={space[2]}>
        <Typography.LargeLabel color={colors.primary} fontWeight="bold">
          {info.name}
        </Typography.LargeLabel>
        <Row minH={space[5]} justifyContent="space-between" alignItems="center">
          <Typography.SmallLabel color={colors.palette.fwdDarkGreen[50]}>
            Last update date {updateDate ? dateFormatUtil(updateDate) : '--'}
          </Typography.SmallLabel>
          {candidateLabelHandler(info.cubeStatus)}
        </Row>
      </Box>
      <TouchableOpacity
        hitSlop={HIT_SLOP_SPACE(1)}
        onPress={() => {
          if (info.mobilePhone) {
            contactHelper({
              method: 'call',
              phoneNumber: info.mobilePhone,
            });
          } else {
            console.log('no mobile phone');
          }
        }}>
        <Icon.CallText size={space[6]} />
      </TouchableOpacity>
      <Icon.ChevronRight
        fill={colors.palette.fwdDarkGreen[50]}
        size={space[4]}
      />
    </Row>
  );
}

const dateHandler = ({
  cubeStatus,
  stage,
  info,
}: {
  cubeStatus: CubeStatusKeys;
  stage: ApplicationStageKeys;
  info: ApplicationListResponds;
}) => {
  //  * stage handling
  if (stage === 'NEW_APPLICATION') {
    return info.lstUpdDate;
  }

  //  *** cubeStatus handling
  switch (cubeStatus) {
    case 'PENDING_PAYMENT':
    case 'PENDING_LEADER_APPROVAL':
      return info.submissionDate;
    case 'APPROVED':
      return info.approvedDate;
    case 'REJECTED':
      return info.rejectedDate;
    case 'REMOTE_CHECKING':
    case 'REMOTE_SIGNATURE':
    default:
      return info.lstUpdDate;
  }
};
