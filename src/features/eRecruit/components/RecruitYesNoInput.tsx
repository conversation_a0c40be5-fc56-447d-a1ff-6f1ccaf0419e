import React, { useMemo } from 'react';
import RadioButtonGroupInput, {
  RadioButtonGroupInputProps,
} from 'components/RadioButtonGroupInput';
import Input, { InputProps } from 'components/Input/Input';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';

export default function RecruitYesNoInput<F extends FieldValues>({
  label,
  ...props
}: Pick<
  InputProps<RadioButtonGroupInputProps<boolean>, boolean, F>,
  'control' | 'name' | 'onChange' | 'shouldHighlightOnUntouched' | 'initialHighlight'
> & {
  label?: string;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, typography } = useTheme();
  const items = useMemo(
    () => [
      {
        label: t('eRecruit.application.personalDetails.question.yes'),
        value: true,
      },
      {
        label: t('eRecruit.application.personalDetails.question.no'),
        value: false,
      },
    ],
    [t],
  );

  return (
    <Input<boolean, F, RadioButtonGroupInputProps<boolean>>
      as={RadioButtonGroupInput}
      {...props}
      items={items}
      label={label}
      style={{ gap: space[4] }}
      labelStyle={{
        fontSize: typography.label.size,
        lineHeight: typography.label.lineHeight,
      }}
      labelFontWeight="bold"
    />
  );
}
