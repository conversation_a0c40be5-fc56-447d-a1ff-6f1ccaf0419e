import {
  ApplicationFormRequest,
  ApplicationFormResponds,
  IDNApproverComments,
  IDNEmergencyContact,
  IDNFinancingProgramComment,
  IDNRegulatorysFull,
  ListItem,
} from 'types/eRecruit';
import { ParsingAppFormDataActionKeys } from 'types/eRecruit';
import { AgencyType, Designation } from 'features/eRecruit/ib/type';
import { cloneDeep } from 'utils/helper/objectUtil';
import { format } from 'date-fns';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import { InferType } from 'yup';
import { otherDetailsSchema } from 'features/eRecruit/id/validations/otherDetailsSchema';
import { defaultCountryCode } from 'features/eRecruit/config';

export function parsingOtherDetailsData({
  input,
  isSameAsResidentialAddress,
  recruitmentCache,
  pressAction,
  cityList,
}: {
  input: InferType<typeof otherDetailsSchema>;
  isSameAsResidentialAddress: boolean;
  recruitmentCache?: ApplicationFormResponds;
  pressAction?: ParsingAppFormDataActionKeys;
  cityList: Array<ListItem>;
}) {
  const stage = pressAction === 'next' ? 'DOCUMENT' : 'OTHER_DETAILS';

  if (!recruitmentCache) {
    return;
  }
  const recruitmentCacheClone = cloneDeep(recruitmentCache);

  const cityIdnDescriptionForResidential = cityList.find(
    city => city.itemCode === input?.contact?.address?.city,
  )?.longDesc?.id;

  const residentialAddressInput = {
    line1: input?.contact?.address?.line1,
    line2: input?.contact?.address?.line2 ?? '',
    // For idn, city is unexpectedly saving the description but not the itemCode
    // Therefore, transform the itemCode to the description when submitting to nameOfBusiness
    // and transform the description to the itemCode after getting data from BE to pre-fill a saved application
    city: cityIdnDescriptionForResidential ?? '',
    state: input?.contact?.address?.province,
    postCode: input?.contact?.address?.postCode,
    district: input?.contact?.address?.district,
    subDistrict: input?.contact?.address?.subDistrict,
    neighborhoodAssociation: input?.contact?.address?.neighborhoodAssociation,
    communityAssociation: input?.contact?.address?.communityAssociation,
  } satisfies Partial<ApplicationFormRequest['contact']['address']>;

  const cityIdnDescriptionForBusiness = cityList.find(
    city => city.itemCode === input?.contact?.businessAddress?.city,
  )?.longDesc?.id;

  const businessAddressInput = isSameAsResidentialAddress
    ? residentialAddressInput
    : ({
        line1: input?.contact?.businessAddress?.line1,
        line2: input?.contact?.businessAddress?.line2 ?? '',
        // For idn, city is unexpectedly saving the description but not the itemCode
        // Therefore, transform the itemCode to the description when submitting to nameOfBusiness
        // and transform the description to the itemCode after getting data from BE to pre-fill a saved application
        city: cityIdnDescriptionForBusiness ?? '',
        state: input?.contact?.businessAddress?.province,
        postCode: input?.contact?.businessAddress?.postCode,
        district: input?.contact?.businessAddress?.district,
        subDistrict: input?.contact?.businessAddress?.subDistrict,
        neighborhoodAssociation:
          input?.contact?.businessAddress?.neighborhoodAssociation,
        communityAssociation:
          input?.contact?.businessAddress?.communityAssociation,
      } satisfies typeof residentialAddressInput);

  const bankInformationInput = {
    accountNumber: input?.bankInformation?.accountNumber,
    bankName: input?.bankInformation?.bankName,
    branchName: input?.bankInformation?.branchName,
  } satisfies Partial<ApplicationFormRequest['contact']['bankInformation']>;

  const positionInput = {
    position: input?.candidatePosition?.position,
    salesOffice: input?.candidatePosition?.salesOffice,
    domicile: input?.candidatePosition?.domicile,
    superiorAgentCode: input?.candidatePosition?.superiorAgentCode,
    superiorAgentName: null,
    financingProgram: input?.candidatePosition?.isHaveFinancingProgram,
    ref: input?.candidatePosition?.ref,
    osAreaManager: input?.candidatePosition?.osAreaManager,
  } satisfies Partial<ApplicationFormRequest['position']>;

  const commentInput = {
    approvalComments: input?.candidatePosition?.optionalComment
      ? [
          {
            comment: input?.candidatePosition?.optionalComment,
          },
        ]
      : [],
  } satisfies IDNApproverComments;

  const cityIdnDescriptionForEmergencyContact = cityList.find(
    city => city.itemCode === input?.emergencyContact?.city,
  )?.longDesc?.id;

  const emergencyContactInput = {
    emergencyContact:
      Boolean(
        input?.emergencyContact?.fullName &&
          input?.emergencyContact?.mobileNumber &&
          input?.emergencyContact?.address &&
          input?.emergencyContact?.city &&
          input?.emergencyContact?.state &&
          input?.emergencyContact?.postCode,
      ) == false
        ? null
        : {
            fullName: (input?.emergencyContact?.fullName ?? null) as string,
            residentNumber: (input?.emergencyContact?.residenceNumber ??
              null) as string,
            mobile: input?.emergencyContact?.mobileNumber
              ? {
                  countryCode: defaultCountryCode,
                  number: (input?.emergencyContact?.mobileNumber ??
                    null) as string,
                  type: 'MOBILE',
                }
              : null,
            address: (input?.emergencyContact?.address ?? null) as string,
            // For idn, city is unexpectedly saving the description but not the itemCode
            // Therefore, transform the itemCode to the description when submitting to nameOfBusiness
            // and transform the description to the itemCode after getting data from BE to pre-fill a saved application
            city: (cityIdnDescriptionForEmergencyContact ?? null) as string,

            state: (input?.emergencyContact?.state ?? null) as string,
            postCode: input?.emergencyContact?.postCode ?? '',
          },
  } satisfies IDNEmergencyContact;

  const ownershipInterestsInForm =
    input?.conflictOfInterest?.ownershipInterests?.[0];
  const externalEmploymentInForm =
    input?.conflictOfInterest?.externalEmployments?.[0];
  const businessAffiliationInterestsInForm =
    input?.conflictOfInterest?.businessAffiliationInterests?.[0];
  const relationshipGovernmentOfficialsInForm =
    input?.conflictOfInterest?.relationshipGovernmentOfficials?.[0];
  const otherInterestInForm = input?.conflictOfInterest?.otherInterests?.[0];

  const regulatoryInput = {
    regulatorys: {
      'S-1-1': {
        checked: input?.regulatorys['S-1-1'].checked,
        detail: input?.regulatorys['S-1-1'].detail ?? null,
      },
      'S-1-2': {
        checked: input?.regulatorys['S-1-2'].checked,
        detail: input?.regulatorys['S-1-2'].detail ?? null,
      },
      'S-2-1': {
        checked: input?.regulatorys['S-2-1'].checked,
        detail: input?.regulatorys['S-2-1'].detail ?? null,
      },
      'S-2-2': {
        checked: input?.regulatorys['S-2-2'].checked,
        detail: input?.regulatorys['S-2-2'].detail ?? null,
      },
      'S-2-3': {
        checked: input?.regulatorys['S-2-3'].checked,
        detail: input?.regulatorys['S-2-3'].detail ?? null,
      },
      'S-3-1': {
        checked: input?.conflictOfInterest?.ownershipInterest,
        answer:
          ownershipInterestsInForm &&
          input?.conflictOfInterest?.ownershipInterest
            ? {
                s31DateAcquired: format(
                  new Date(ownershipInterestsInForm?.dateAcquired),
                  'yyyy-MM-dd',
                ),
                s31PercentageOfOwnership:
                  ownershipInterestsInForm?.percentageOfOwnership?.toString(),
                s31NameOfBusiness: ownershipInterestsInForm?.nameOfBusiness,
                s31NameOfOwner: ownershipInterestsInForm?.nameOfOwner,
                s31NatureOfBusiness: ownershipInterestsInForm?.natureOfBusiness,
              }
            : undefined,
      },
      'S-3-2': {
        checked: input?.conflictOfInterest?.externalEmployment,
        answer:
          externalEmploymentInForm &&
          input?.conflictOfInterest?.externalEmployment
            ? {
                s32Details: externalEmploymentInForm?.details,
                s32Position: externalEmploymentInForm?.position,
                s32CompensationReceived:
                  externalEmploymentInForm?.compensationReceived
                    ? 'true'
                    : 'false',
                s32NatureOfBusiness: externalEmploymentInForm?.natureOfBusiness,
                s32NameOfBusiness: externalEmploymentInForm?.nameOfBusiness,
              }
            : undefined,
      },
      'S-3-3': {
        checked: input?.conflictOfInterest?.businessAffiliationInterest,
        answer:
          businessAffiliationInterestsInForm &&
          input?.conflictOfInterest?.businessAffiliationInterest
            ? {
                s33DateCommencement: format(
                  new Date(
                    businessAffiliationInterestsInForm?.dateCommencementEmployment,
                  ),
                  'yyyy-MM-dd',
                ),

                s33NameOfFamily:
                  businessAffiliationInterestsInForm?.nameOfFamilyMember,
                s33NatureOfBusiness:
                  businessAffiliationInterestsInForm?.natureOfBusiness,
                s33Position:
                  businessAffiliationInterestsInForm?.positionDepartment,
                s33NameOfBusiness:
                  businessAffiliationInterestsInForm?.nameOfBusiness,
              }
            : undefined,
      },
      'S-3-4': {
        checked: input?.conflictOfInterest?.relationshipGovernmentOfficial,
        answer:
          relationshipGovernmentOfficialsInForm &&
          input?.conflictOfInterest?.relationshipGovernmentOfficial
            ? {
                s34Position:
                  relationshipGovernmentOfficialsInForm?.positionDepartment,
                s34NameOfGovernment:
                  relationshipGovernmentOfficialsInForm?.nameOfGovernment,
                s34Relationship:
                  relationshipGovernmentOfficialsInForm?.relationship,
              }
            : undefined,
      },
      'S-3-5': {
        checked: input?.conflictOfInterest?.otherInterest,
        detail: input?.conflictOfInterest?.otherInterest
          ? otherInterestInForm?.details
          : undefined,
        // answer: 'testing answer',
      },
    },
  } satisfies IDNRegulatorysFull;

  const parsedObj = {
    ...recruitmentCacheClone,
    contact: {
      ...recruitmentCacheClone?.contact,
      address: {
        ...recruitmentCacheClone?.contact?.address,
        ...residentialAddressInput,
      },
      businessAddress: {
        ...recruitmentCacheClone?.contact?.businessAddress,
        ...businessAddressInput,
      },
      bankInformation: {
        ...recruitmentCacheClone?.contact?.bankInformation,
        ...bankInformationInput,
      },
      emergencyContact:
        recruitmentCacheClone?.contact?.emergencyContact != null
          ? {
              ...recruitmentCacheClone?.contact?.emergencyContact,
              ...emergencyContactInput?.emergencyContact,
            }
          : emergencyContactInput?.emergencyContact,
    },
    position: {
      ...recruitmentCacheClone?.position,
      ...positionInput,
    },

    stage,
    ...commentInput,
    ...regulatoryInput,
  } satisfies ApplicationFormResponds;

  return parsedObj;
}

// only agency right now not banca
export function assignCandidateBranchCode({
  recruiterDesignation,
  selectedAgencyType,
  recruiterBranchCode,
  selectedBranchCode,
  branchList,
}: {
  recruiterDesignation: Designation;
  selectedAgencyType: AgencyType;
  recruiterBranchCode: string;
  selectedBranchCode: string;
  branchList: {
    label: string;
    value: string;
  }[];
}) {
  const isValidRecruiterBranchCode = getLabelFromValue(
    branchList,
    recruiterBranchCode,
  );
  const isValidSelectedBranchCode = getLabelFromValue(
    branchList,
    selectedBranchCode,
  );

  if (selectedAgencyType === 'FA' || selectedBranchCode === 'FA') {
    return 'FA';
  }
  if (recruiterDesignation === 'RD' && !selectedBranchCode) {
    // recruiter designation is RD and no selected branch code
    return '';
  }
  // return selectedBranchCode if it is not empty or return recruiterBranchCode
  return (
    (isValidSelectedBranchCode && selectedBranchCode) ||
    (isValidRecruiterBranchCode && recruiterBranchCode) ||
    ''
  );
}

export function isBusinessAddressSameAsResidential(
  address: InferType<typeof otherDetailsSchema>['contact']['address'],
  businessAddress: InferType<
    typeof otherDetailsSchema
  >['contact']['businessAddress'],
): boolean {
  if (!address || !businessAddress) return false;

  const fields = Object.keys(address) as Array<
    keyof InferType<typeof otherDetailsSchema>['contact']['address']
  >;

  return fields.every(field => address?.[field] === businessAddress?.[field]);
}

const getCityItemCodeByValue = (
  cityValue: string | null,
  cityList: Array<ListItem>,
): string =>
  cityValue
    ? cityList.find(city => Object.values(city.longDesc).includes(cityValue))
        ?.itemCode ?? ''
    : '';

export function convertSavedApplicationForHookForm({
  parsedObj,
  cityList,
  currentAgentCode,
}: {
  parsedObj: ApplicationFormResponds;
  cityList: Array<ListItem>;
  currentAgentCode: string | null;
}) {
  const address = {
    line1: parsedObj?.contact?.address?.line1 ?? '',
    line2: parsedObj?.contact?.address?.line2 ?? '',
    city: getCityItemCodeByValue(parsedObj?.contact?.address?.city, cityList),
    province: parsedObj?.contact?.address?.state ?? '',
    postCode: parsedObj?.contact?.address?.postCode ?? '',
    district: parsedObj?.contact?.address?.district ?? '',
    subDistrict: parsedObj?.contact?.address?.subDistrict ?? '',
    neighborhoodAssociation:
      parsedObj?.contact?.address?.neighborhoodAssociation ?? '',
    communityAssociation:
      parsedObj?.contact?.address?.communityAssociation ?? '',
  } satisfies InferType<typeof otherDetailsSchema>['contact']['address'];

  const businessAddress = {
    line1: parsedObj?.contact?.businessAddress?.line1 ?? '',
    line2: parsedObj?.contact?.businessAddress?.line2 ?? '',
    // city: parsedObj?.contact?.businessAddress?.city as string,
    city: getCityItemCodeByValue(
      parsedObj?.contact?.businessAddress?.city,
      cityList,
    ),
    province: parsedObj?.contact?.businessAddress?.state ?? '',
    postCode: parsedObj?.contact?.businessAddress?.postCode ?? '',
    district: parsedObj?.contact?.businessAddress?.district ?? '',
    subDistrict: parsedObj?.contact?.businessAddress?.subDistrict ?? '',
    neighborhoodAssociation:
      parsedObj?.contact?.businessAddress?.neighborhoodAssociation ?? '',
    communityAssociation:
      parsedObj?.contact?.businessAddress?.communityAssociation ?? '',
  } satisfies InferType<
    typeof otherDetailsSchema
  >['contact']['businessAddress'];
  return {
    contact: {
      address,
      businessAddress,
      isBusinessSameAsResidentialAddress: isBusinessAddressSameAsResidential(
        address,
        businessAddress,
      ),
    },
    bankInformation: {
      accountNumber: parsedObj?.contact?.bankInformation?.accountNumber ?? '',
      bankName: parsedObj?.contact?.bankInformation?.bankName ?? '',
      branchName: parsedObj?.contact?.bankInformation?.branchName ?? '',
    },
    candidatePosition: {
      position: parsedObj?.position?.position ?? '',
      salesOffice: parsedObj?.position?.salesOffice ?? '',
      domicile: parsedObj?.position?.domicile ?? '',
      osAreaManager:
        parsedObj?.position?.osAreaManager == 'null-null'
          ? ''
          : parsedObj?.position?.osAreaManager ?? '',
      superiorAgentCode: parsedObj?.position?.superiorAgentCode ?? '',
      ref: parsedObj?.position?.ref ?? '',
      superiorAgentName: parsedObj?.position?.superiorAgentName ?? '',
      isHaveFinancingProgram: parsedObj?.position?.financingProgram ?? false,

      optionalComment: currentAgentCode
        ? parsedObj?.approvalComments?.find(
            section => section.approverAgentCode === currentAgentCode,
          )?.comment ?? null
        : null,
    },
    emergencyContact: {
      fullName: parsedObj?.contact?.emergencyContact?.fullName ?? '',
      residenceNumber:
        parsedObj?.contact?.emergencyContact?.residentNumber ?? '',
      mobileCountryCode:
        parsedObj?.contact?.emergencyContact?.mobile?.countryCode ??
        defaultCountryCode,
      mobileNumber: parsedObj?.contact?.emergencyContact?.mobile?.number ?? '',

      address: parsedObj?.contact?.emergencyContact?.address ?? '',
      city: getCityItemCodeByValue(
        parsedObj?.contact?.emergencyContact?.city ?? null,
        cityList,
      ),
      state: parsedObj?.contact?.emergencyContact?.state ?? '',
      postCode: parsedObj?.contact?.emergencyContact?.postCode ?? '',
    },
    conflictOfInterest: {
      ownershipInterest: parsedObj?.regulatorys?.['S-3-1']?.checked as boolean,
      externalEmployment: parsedObj?.regulatorys?.['S-3-2']?.checked as boolean,
      businessAffiliationInterest: parsedObj?.regulatorys?.['S-3-3']
        ?.checked as boolean,
      relationshipGovernmentOfficial: parsedObj?.regulatorys?.['S-3-4']
        ?.checked as boolean,
      otherInterest: parsedObj?.regulatorys?.['S-3-5']?.checked as boolean,
      ownershipInterests: parsedObj?.regulatorys?.['S-3-1']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NatureOfBusiness,
              nameOfOwner:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31NameOfOwner,
              percentageOfOwnership: parseFloat(
                parsedObj?.regulatorys?.['S-3-1']?.answer
                  ?.s31PercentageOfOwnership,
              ),
              dateAcquired:
                parsedObj?.regulatorys?.['S-3-1']?.answer?.s31DateAcquired,
              // relationship: '', // Add a default or appropriate value for 'relationship'
            },
          ]
        : [],
      externalEmployments: parsedObj?.regulatorys?.['S-3-2']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-2']?.answer?.s32NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-2']?.answer?.s32NatureOfBusiness,
              position: parsedObj?.regulatorys?.['S-3-2']?.answer?.s32Position,
              details: parsedObj?.regulatorys?.['S-3-2']?.answer?.s32Details,
              compensationReceived:
                parsedObj?.regulatorys?.['S-3-2']?.answer
                  ?.s32CompensationReceived === 'true',
            },
          ]
        : [],
      businessAffiliationInterests: parsedObj?.regulatorys?.['S-3-3']?.answer
        ? [
            {
              nameOfBusiness:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NameOfBusiness,
              natureOfBusiness:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NatureOfBusiness,
              nameOfFamilyMember:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33NameOfFamily,
              positionDepartment:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33Position,
              dateCommencementEmployment:
                parsedObj?.regulatorys?.['S-3-3']?.answer?.s33DateCommencement,
              // relationship: '',
            },
          ]
        : [],
      relationshipGovernmentOfficials: parsedObj?.regulatorys?.['S-3-4']?.answer
        ? [
            {
              nameOfGovernment:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34NameOfGovernment,
              positionDepartment:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34Position,
              relationship:
                parsedObj?.regulatorys?.['S-3-4']?.answer?.s34Relationship,
            },
          ]
        : [],
      otherInterests: parsedObj?.regulatorys?.['S-3-5']?.detail
        ? [
            {
              details: parsedObj?.regulatorys?.['S-3-5']?.detail,
            },
          ]
        : [],
    },
    regulatorys: {
      'S-1-1': {
        checked: parsedObj?.regulatorys?.['S-1-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-1-1']?.detail as string,
      },
      'S-1-2': {
        checked: parsedObj?.regulatorys?.['S-1-2']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-1-2']?.detail as string,
      },
      'S-2-1': {
        checked: parsedObj?.regulatorys?.['S-2-1']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-1']?.detail as string,
      },
      'S-2-2': {
        checked: parsedObj?.regulatorys?.['S-2-2']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-2']?.detail as string,
      },
      'S-2-3': {
        checked: parsedObj?.regulatorys?.['S-2-3']?.checked as boolean,
        detail: parsedObj?.regulatorys?.['S-2-3']?.detail as string,
      },
    },
  } satisfies InferType<typeof otherDetailsSchema>;
}
