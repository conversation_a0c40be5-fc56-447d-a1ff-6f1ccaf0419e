import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import AutocompletePopup from 'components/AutocompletePopup';
import DatePickerCalendar from 'components/DatePickerCalendar';
import TabletFooter from 'components/Footer/TabletFooter';
import Input from 'components/Input';
import {
  addToast,
  Box,
  Button,
  Column,
  H6,
  H7,
  Icon,
  Picker,
  Row,
  TextField,
  Label,
  LargeBody,
} from 'cube-ui-components';
import { subYears } from 'date-fns';
import { tabletStyles } from 'features/coverageDetails/utils/common/customStyles';
import {
  DEFAULT_COUNTRY_PHONE_CODE_MY,
  defaultCountryCode,
} from 'features/eRecruit/config';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import ProgressStepBar from 'features/eRecruit/components/tablet/ProgressStepBar';
import { NewApplicationFormValues, SavedActionProps } from 'types/eRecruit';
import { ApplicationFormResponds, PhoneInfoTypeKeys } from 'types/eRecruit';
import { PHONE_NUMBER_TYPE_REGEX } from 'features/eRecruit/util/inputMaskHelper';
import React, { useEffect, useRef, useState } from 'react';
import {
  SubmitErrorHandler,
  SubmitHandler,
  useForm,
  UseFormReturn,
  FieldPath,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation, Trans } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IDNNewApplicationFormParmaList, RootStackParamListMap } from 'types';
import { PersonalDetailsTabERecruitApp } from 'types/eRecruit';
import { CountryCode } from 'types/optionList';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { InferType, ObjectSchema } from 'yup';

import { calculateAgeAlternative } from 'features/eRecruit/util/calculateAgeAlternative';
import LoadingIndicator from 'components/LoadingIndicator';
import TabButtonSet from 'features/eRecruit/ib/tablet/components/TabButton';
import PhoneField from 'components/PhoneField';
import { yupResolver } from '@hookform/resolvers/yup';
import GATracking from 'utils/helper/gaTracking';

// UI / Icons
import NewIdentitySectionIcon from 'features/eRecruit/ib/tablet/asset/NewIdentitySectionIcon';
import NewContactDetailsIcon from 'features/eRecruit/ib/tablet/asset/NewContactDetailsIcon';
import NewInsuranceExpIcon from 'features/eRecruit/ib/tablet/asset/NewInsuranceExpIcon';

// types
import { OptionConfig } from 'features/eRecruit/ib/type';

// to be isolated for IDN
import {
  // initialApplicationData,
  personalDetailsSchema,
} from 'features/eRecruit/id/validations/personalDetailsSchema';
import {
  backendToFormValues,
  personalDetailsFunctions,
} from 'features/eRecruit/id/utils/personalDetailsFunctions';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import RecruitYesNoInput from 'features/eRecruit/components/RecruitYesNoInput';
import { BlacklistedIDErrorModal } from 'features/eRecruit/id/tablet/components/BlacklistedIDErrorModal';

type SchemaType<T> = T extends ObjectSchema<infer U> ? U : never;
type PersonalDetailsSchemaType = SchemaType<typeof personalDetailsSchema>;

type PersonalDetailsFieldPath = FieldPath<
  InferType<typeof personalDetailsSchema>
>;

const REGISTRATION_ID_BLACKLIST = 'REGISTRATION_STAGING_NRIC_BLACKLIST';

export default function PersonalDetailsTab({
  isRerouted,
  setIsRerouted,
}: PersonalDetailsTabERecruitApp) {
  const [isErrorModalVisible, setIsErrorModalVisible] =
    useState<boolean>(false);

  const { space, colors } = useTheme();
  const { t } = useTranslation(['eApp', 'eRecruit']);
  // Navigation
  const route =
    useRoute<RouteProp<RootStackParamListMap['my'], 'ERecruitApplication'>>();
  const navigationToMain =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  const navigation =
    useNavigation<NavigationProp<IDNNewApplicationFormParmaList>>();
  const registrationStagingId = route.params?.registrationStagingId ?? '';

  // React query
  const { data: recruitmentData } = useGetApplicationData(
    registrationStagingId ?? '',
  );

  const { mutateAsync, isLoading } = useSaveERecruitApplicationForm();

  // Variables
  const recruitmentCache = registrationStagingId ? recruitmentData : null;

  const { isLoading: isConfigLoading, idTypeList } =
    useGetERecruitOptionListForAppForm();

  const dataFromBE = recruitmentData
    ? backendToFormValues(recruitmentData)
    : undefined;

  // React hook form
  const hookForm = useForm({
    defaultValues: {
      contact: {
        countryCode: defaultCountryCode,
        officeNumberCountryCode: defaultCountryCode,
      },
      identity: {
        numberOfDependence: '0',
      },
    },
    values: dataFromBE,
    mode: 'onBlur',
    resolver: yupResolver(personalDetailsSchema),
  });

  const {
    trigger,
    watch,
    handleSubmit,
    getValues,
    control,
    resetField,
    formState: { isValid, errors },
  } = hookForm;
  useEffect(() => {
    const resetToDefaultIdentity = () =>
      resetField('identity.identity', {
        defaultValue:
          idTypeList?.find(i => i.value == 'KTP')?.value ?? undefined,
      });
    if (watch('identity.identity') == '') {
      resetToDefaultIdentity();
      console.log(
        'identity.identity is not set, setting default value: ',
        watch('identity.identity') == '',
      );
    }
    if (dataFromBE) {
      //  filling the FE form with data from BE shall be done in useForm hook, not in useEffect
      return;
    }

    // if not dataFromBE, set default value for identity.identity
    // idTypeList is feteched from BE, so it is not available in the first render
    // when idTypeList is available, using it in defaultValues in useForm hook does not trigger rendering,
    // and in values in useForm hook raise type error
    // so set default value for identity.identity in useEffect
    resetToDefaultIdentity();
  }, [watch, dataFromBE, idTypeList, resetField]);

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
    useIncompleteFields({
      schema: personalDetailsSchema,
      control,
      watch,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
    });

  const currentFormData = getValues();

  const saveAction = (
    props: SavedActionProps<InferType<typeof personalDetailsSchema>>,
  ) => {
    try {
      console.log('~~~saveAction~~');
      const { data, onSuccess, onError } = props;
      if (!recruitmentCache) {
        console.log(
          'recruitmentCache is undefined, but not block in PersonalDetailsTab',
        );
        // return;
      }
      const pressAction = props.pressAction ? props.pressAction : 'save';
      const updatedData = personalDetailsFunctions({
        input: data,
        recruitmentCache,
        pressAction,
      });
      mutateAsync(updatedData as any, {
        onSuccess: onSuccess,
        onError:
          onError ??
          (error => {
            addToast([
              {
                message: t('eRecruit:eRecruit.pleaseTryAgainLater'),
              },
            ]);
            console.log(
              '🚀 ~ file: ProgressStepBar==== ~ onValidSubmit error:',
              error,
            );
          }),
      });
    } catch (error) {
      console.error('Error in saveAction --- PersonalDetailsTab:', error);
    }
  };

  const onValidSave: SubmitHandler<
    InferType<typeof personalDetailsSchema>
  > = data => {
    const isNecessaryToSave = () =>
      trigger([
        'identity.fullName',
        'identity.gender',
        'contact.email',
        'contact.countryCode',
        'contact.phoneNumber',
      ]);
    isNecessaryToSave().then(isValid => {
      if (!isValid) {
        return;
      }
      saveAction({
        data,
        onSuccess: async data => {
          if (data?.registrationStagingId) {
            GATracking.logCustomEvent('recruitment', {
              action_type: 'application_created',
            });
            navigationToMain.navigate('Main');
            addToast([
              {
                IconLeft: Icon.Tick,
                message: 'Application saved.',
              },
            ]);
            return;
          }
        },
        onError: (error: any) => {
          if (error?.response?.data?.status === REGISTRATION_ID_BLACKLIST) {
            return onToggleErrorModal();
          }
          addToast([
            {
              message: t('eRecruit:eRecruit.pleaseTryAgainLater'),
            },
          ]);
          console.log(
            '🚀 ~ file: PersonalDetailsTab ~ onValidSave error:',
            error,
          );
        },
      });
    });
    console.log('--- isNecssaryToSave: ');
  };

  const onInvalidSave: SubmitErrorHandler<NewApplicationFormValues> = error => {
    console.log('===== onInvalidSave error: ', error);
    // isPCEChecked(error);
  };

  const onValidSubmit: SubmitHandler<
    InferType<typeof personalDetailsSchema>
  > = data => {
    saveAction({
      data,
      pressAction: 'next',
      onError: (error: any) => {
        if (error?.response?.data?.status === REGISTRATION_ID_BLACKLIST) {
          return onToggleErrorModal();
        }
        addToast([
          {
            message: t('eRecruit:eRecruit.pleaseTryAgainLater'),
          },
        ]);
        console.log(
          '🚀 ~ file: PersonalDetailsTab ~ onValidSubmit error:',
          error,
        );
      },
      onSuccess: async data => {
        if (data?.registrationStagingId) {
          GATracking.logCustomEvent('recruitment', {
            action_type: 'application_created',
          });
          console.log(
            '🚀 ~ data?.registrationStagingId:',
            data?.registrationStagingId,
          );
          navigation.navigate('newApplicationOtherDetails', {
            registrationStagingId: String(data.registrationStagingId),
          });
          setIsRerouted && setIsRerouted(true);
          return;
        }
      },
    });
  };

  // const [isErrorModalShown, setIsErrorModalShown] = useState(false);

  // const isPCEChecked = (fieldsErrs: FieldErrors<NewApplicationFormValues>) => {
  //   // errors?. && setIsErrorModalShown(true)
  //   fieldsErrs?.personalInformation?.insuranceCertPce?.type === 'oneOf' &&
  //     setIsErrorModalShown(true);
  // };
  const onInvalidSubmit: SubmitErrorHandler<
    NewApplicationFormValues
  > = error => {
    console.log('~~~~ onInvalidSubmit error: ', error);
    // isPCEChecked(error);
  };

  const mandatoryFieldsForSingle = [
    'identity.fullName',
    'identity.gender',
    'identity.dateOfBirth',
    'identity.identity',
    'identity.idNumber',
    'identity.birthPlace',
    'identity.religion',
    'identity.maritalStatus',
    'identity.numberOfDependence',
    'personalInformation.education',
    'personalInformation.industry',
    'personalInformation.presentOccupation',
    'insuranceExperience.haveLifeInsuranceExp',
    'insuranceExperience.haveGeneralInsuranceExp',
    'contact.phoneNumber',
    'contact.countryCode',
    'contact.email',
  ] as const satisfies PersonalDetailsFieldPath[];

  // const citizen = watch('identity.citizen');

  // const isOtherProQualiChecked = watch(
  //   'personalInformation.otherQualifications',
  // );
  // const isOtherProQualiDescFilled = Boolean(
  //   watch('personalInformation.otherQualificationsDesc'),
  // );

  const isAllMandatoryFieldsFilled = watch(mandatoryFieldsForSingle).every(
    (inputData, idx) => {
      const fieldPath = mandatoryFieldsForSingle[idx];
      if (
        fieldPath == 'insuranceExperience.haveGeneralInsuranceExp' ||
        fieldPath == 'insuranceExperience.haveLifeInsuranceExp'
      ) {
        return typeof inputData === 'boolean';
      }
      return Boolean(inputData);
    },
  );

  const isSubmitButtonDisabled = () =>
    !isAllMandatoryFieldsFilled || isLoading || !isValid;

  useEffect(() => {
    if (isRerouted) {
      console.log(
        'already rerounted during this application----',
        recruitmentData?.stage,
      );
      return;
    }
    if (recruitmentData?.stage) {
      const currentStage = recruitmentData.stage;
      // * temp setTimeout hack for fixing no navigation when resuming App flow
      setTimeout(() => {
        console.log('useEffect~~~~~~~~ renavigating to other page');

        switch (currentStage) {
          case 'PERSONAL_DETAILS':
            break;
          case 'OTHER_DETAILS':
            navigation.navigate('newApplicationOtherDetails', {
              registrationStagingId: registrationStagingId,
            });
            break;
          case 'DOCUMENT':
            navigation.navigate('newApplicationDocuments', {
              registrationStagingId: registrationStagingId,
            });
            break;
          case 'CONSENT':
            navigation.navigate('newApplicationConsent', {
              registrationStagingId: registrationStagingId,
            });
            break;
          default:
            break;
        }
      }, 250);
      setIsRerouted && setIsRerouted(true);
    }
  }, [recruitmentData?.stage, navigation]);

  // const [isSavingNotNext, setIsSavingNotNext] = useState(false);

  // const onReSubmitToSetInsuranceCertPceTrue = (isSavingNotNext: boolean) => {
  //   setValue('personalInformation.insuranceCertPce', true);
  //   trigger().then(isValid => {
  //     setIsErrorModalShown(false);
  //     if (!isValid) {
  //       return console.log('onReSubmit isValid: ', isValid);
  //     }
  //     isSavingNotNext
  //       ? handleSubmit(onValidSave, onInvalidSave)()
  //       : handleSubmit(onValidSubmit, onInvalidSubmit)();
  //   });
  // };

  const onToggleErrorModal = () => setIsErrorModalVisible(prev => !prev);

  return (
    <>
      <ProgressStepBar
        type="triggerWithStepBarControlsOnSuccess"
        triggerValidation={trigger}
        onPressValidSave={saveAction}
        saveForLaterData={currentFormData}
        onPressInValidSave={() =>
          console.log('Personal Details Tab ~ onPressInValidSave')
        }
      />
      <KeyboardAwareScrollView
        ref={scrollRef}
        enableResetScrollToCoords={false}
        style={{
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          paddingHorizontal: space[8],
          paddingVertical: space[4],
          backgroundColor: colors.palette.fwdGrey[50],
        }}>
        <View style={{ flex: 1, gap: space[3] }}>
          <IdentityDetailsSection hookForm={hookForm} />
          <ContactDetailsSection hookForm={hookForm} />
          <InsuranceExperience hookForm={hookForm} />
        </View>
        <LargeBody style={{ paddingVertical: space[3] }}>
          <Trans
            i18nKey={
              'eRecruit:eRecruit.application.personalDetails.agentApproval.withEmailParam' as any
            }
            values={{ email: '<EMAIL>' }}
            components={{
              emailLink: <LargeBody color={colors.primary} />,
            }}
          />
        </LargeBody>
      </KeyboardAwareScrollView>
      <TabFooter
        onSaveForLater={() => {
          // setIsSavingNotNext(true);
          // handleSubmit(onValidSave, onInvalidSave)();
          const currentFormData = getValues();
          onValidSave(currentFormData);
        }}
        onNext={() => {
          // setIsSavingNotNext(false);
          handleSubmit(onValidSubmit, onInvalidSubmit)();
        }}
        isLoading={isLoading}
        isSaveBtnDisabled={isLoading}
        isSubmitBtnDisabled={isSubmitButtonDisabled()}
        LeftComponent={() => (
          <Row justifyContent="flex-start" gap={space[5]}>
            {isAllMandatoryFieldsFilled
              ? null
              : totalIncompleteRequiredFields !== undefined &&
                totalIncompleteRequiredFields > 0 && (
                  <Row ml={space[1]} alignSelf="center" alignItems="center">
                    <Icon.Warning size={space[6]} />
                    <Box width={space[2]} />
                    <LargeBody>
                      {t(
                        'eRecruit:eRecruit.application.personalDetails.incompleteFields',
                        { total: totalIncompleteRequiredFields },
                      )}
                    </LargeBody>
                    <Box width={space[4]} />
                    <TouchableOpacity
                      hitSlop={ICON_HIT_SLOP}
                      onPress={focusOnNextIncompleteField}>
                      <Row alignItems="center">
                        <Label
                          fontWeight="medium"
                          color={colors.palette.fwdAlternativeOrange[100]}>
                          {t(
                            'eRecruit:eRecruit.application.personalDetails.goToTheField',
                          )}
                        </Label>
                        <Icon.ChevronDown
                          fill={colors.palette.fwdAlternativeOrange[100]}
                          size={space[4]}
                        />
                      </Row>
                    </TouchableOpacity>
                  </Row>
                )}
          </Row>
        )}
      />
      <BlacklistedIDErrorModal
        onCancel={onToggleErrorModal}
        visible={isErrorModalVisible}
      />
    </>
  );
}

function IdentityDetailsSection({
  hookForm,
}: {
  hookForm: UseFormReturn<PersonalDetailsSchemaType>;
}) {
  const { t } = useTranslation(['eApp', 'eRecruit']);
  const { space, colors } = useTheme();

  const {
    control,
    trigger,
    watch,
    getValues,
    setValue,
    formState: { errors },
  } = hookForm;

  const maxDate = subYears(new Date(), 18);
  const minDate = subYears(new Date(), 60);
  const ageValue = getValues('identity.dateOfBirth');
  const age = ageValue ? calculateAgeAlternative(ageValue) : undefined;
  const dob = watch('identity.dateOfBirth');
  const numberOfDependence = watch('identity.numberOfDependence');
  const validateNumberOfDependence = () =>
    trigger('identity.numberOfDependence');

  const {
    isLoading,
    genderConfig,
    maritalConfig,
    religionConfig,
    idTypeList,
    occupationList,
    industryList,
    educationConfig,
  } = useGetERecruitOptionListForAppForm();

  return (
    <Container>
      <LabelStyle>
        {/* <PictogramIcon.Agent size={space[10]} /> */}
        <NewIdentitySectionIcon />
        <H6 fontWeight="bold">
          {t(`eRecruit:eRecruit.application.personalDetails.identityDetails`)}
        </H6>
      </LabelStyle>
      {isLoading ? (
        <Box>
          <LoadingIndicator />
        </Box>
      ) : (
        <Box rowGap={space[5]}>
          {/* -------------------- Row 1 --------------------*/}
          <Row gap={space[5]}>
            <Input
              control={control}
              as={TextField}
              name="identity.fullName"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.fullName`,
              )}
              error={errors?.identity?.fullName?.message}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Input
              control={control}
              as={Picker}
              name="identity.gender"
              type="text"
              label={t(`eRecruit:eRecruit.application.personalDetails.gender`)}
              style={{ flex: 1 }}
              items={genderConfig}
              error={errors?.identity?.gender?.message}
              onChange={() => {
                setTimeout(() => trigger('identity.gender'), 500);
              }}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Row style={{ flex: 1, gap: space[3] }}>
              <Input
                control={control}
                as={DatePickerCalendar}
                name="identity.dateOfBirth"
                style={{
                  flex: 0.8,
                }}
                label={t(
                  `eRecruit:eRecruit.application.personalDetails.dateOfBirth`,
                )}
                hint="DD/MM/YYYY"
                formatDate={() => (dob ? dateFormatUtil(dob) : '')}
                minDate={minDate}
                maxDate={maxDate}
                value={dob ?? maxDate}
                onChange={() =>
                  setTimeout(() => trigger('identity.dateOfBirth'), 100)
                }
                placeholder="DD / MM / YYYY"
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
              <TextField
                disabled={true}
                label={t('eRecruit:eRecruit.application.personalDetails.age')}
                value={age}
                style={{ flex: 0.22 }}
              />
            </Row>
          </Row>

          {/* -------------------- Row 2 -------------------- */}
          <Row gap={space[5]}>
            <Input
              control={control}
              as={AutocompletePopup<OptionConfig, string>}
              name="identity.identity"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.identity`,
              )}
              error={errors?.identity?.identity?.message}
              data={idTypeList}
              getItemLabel={item => item?.label}
              getItemValue={item => item?.value}
              searchable
              disabled={idTypeList.length == 1 && !!watch('identity.identity')}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Input
              control={control}
              as={TextField}
              name="identity.idNumber"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.icNumber`,
              )}
              keyboardType="number-pad"
              error={errors?.identity?.idNumber?.message}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
              // placeholder={t('eApp:nricPlaceholder')}
              // onBlur={() => {
              //   if (NRICNumber && dobNric && dobNric !== dob) {
              //     console.log('~~~~~dateOfBirth on blur updated.');
              //     setValue('identity.dateOfBirth', dobNric);
              //   }
              //   if (NRICNumber && genderNric && genderNric != gender) {
              //     console.log('~~~~~genderNric on blur updated.');
              //     setValue('identity.gender', genderNric);
              //   }
              // }}
            />

            <Input
              control={control}
              as={TextField}
              name="identity.birthPlace"
              label={t(
                `eRecruit:eRecruit.application.personalDetails.birthPlace`,
              )}
              style={{ flex: 1 }}
              error={errors?.identity?.birthPlace?.message}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </Row>

          {/* -------------------- Row 3 --------------------*/}
          <Row gap={space[5]}>
            <Column gap={space[2]}>
              <Input
                control={control}
                as={Picker}
                name="identity.religion"
                label={t(
                  `eRecruit:eRecruit.application.personalDetails.religion`,
                )}
                type="chip"
                items={religionConfig}
                size="large"
                labelStyle={{
                  marginLeft: space[3],
                }}
                onChange={() => {
                  setTimeout(() => {
                    trigger('identity.religion');
                  }, 500);
                }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Column>
            <Column gap={space[2]}>
              <Input
                control={control}
                as={Picker}
                name="identity.maritalStatus"
                label={t(
                  `eRecruit:eRecruit.application.personalDetails.maritalStatus`,
                )}
                type="chip"
                items={maritalConfig}
                size="large"
                labelStyle={{
                  marginLeft: space[3],
                }}
                onChange={() => {
                  setTimeout(() => {
                    trigger('identity.maritalStatus');
                  }, 500);
                }}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={false}
              />
            </Column>
          </Row>

          {/* -------------------- Row 4 --------------------*/}
          <Row gap={space[5]}>
            {/* <Input
              control={control}
              as={TextField}
              name="identity.taxCode"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.numberOfDependents`,
              )}
              onChangeText={text => {
                const trimmedText = text.replace(/\s+/g, '');
                setValue('identity.taxCode', trimmedText);
              }}
              error={errors?.identity?.taxCode?.message}
            /> */}
            <Input
              control={control}
              as={TextField}
              name="identity.numberOfDependence"
              style={{ flex: 1 }}
              editable={false}
              value={numberOfDependence}
              right={
                <TouchableOpacity
                  onPress={() =>
                    updateDependenceValue(
                      numberOfDependence,
                      1,
                      setValue,
                      validateNumberOfDependence,
                    )
                  }>
                  <Icon.PlusCircle />
                </TouchableOpacity>
              }
              left={
                <TouchableOpacity
                  onPress={() =>
                    updateDependenceValue(
                      numberOfDependence,
                      -1,
                      setValue,
                      validateNumberOfDependence,
                    )
                  }
                  disabled={
                    !numberOfDependence || !parseInt(numberOfDependence)
                  }>
                  <Icon.MinusCircle
                    fill={
                      numberOfDependence && parseInt(numberOfDependence) > 0
                        ? colors.primary
                        : colors.palette.fwdGreyDark
                    }
                  />
                </TouchableOpacity>
              }
              label={t(
                `eRecruit:eRecruit.application.personalDetails.numOfDependents`,
              )}
              error={errors?.identity?.numberOfDependence?.message}
              inputStyle={{ textAlign: 'center' }}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Input
              control={control}
              as={AutocompletePopup<(typeof educationConfig)[number], string>}
              data={educationConfig}
              getItemLabel={item => item?.text ?? '--'}
              getItemValue={item => item?.value}
              name="personalInformation.education"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.education`,
              )}
              error={errors?.personalInformation?.education?.message}
              searchable
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Input
              control={control}
              as={AutocompletePopup<OptionConfig, string>}
              data={industryList}
              getItemLabel={item => item?.label}
              getItemValue={item => item?.value}
              name="personalInformation.industry"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.industry`,
              )}
              error={errors?.personalInformation?.industry?.message}
              searchable
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
          </Row>

          {/* -------------------- Row 5 --------------------*/}
          <Row gap={space[5]}>
            <Input
              control={control}
              as={AutocompletePopup<OptionConfig, string>}
              name="personalInformation.presentOccupation"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.presentOccupation`,
              )}
              error={errors?.personalInformation?.presentOccupation?.message}
              data={occupationList}
              getItemLabel={item => item?.label}
              getItemValue={item => item?.value}
              searchable
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={false}
            />
            <Input
              control={control}
              as={TextField}
              name="personalInformation.npwp"
              style={{ flex: 1 }}
              label={t(
                `eRecruit:eRecruit.application.personalDetails.npwp.optional`,
              )}
              error={errors?.personalInformation?.npwp?.message}
              hint={t(
                'eRecruit:eRecruit.application.personalDetails.npwp.hint',
              )}
            />
          </Row>
        </Box>
      )}
    </Container>
  );
}

function ContactDetailsSection({
  hookForm,
}: {
  hookForm: UseFormReturn<PersonalDetailsSchemaType>;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();
  const {
    control,
    setValue,
    formState: { errors },
  } = hookForm;

  const { countryCodeOptions } = useGetERecruitOptionListForAppForm();

  return (
    <Container>
      <LabelStyle>
        <NewContactDetailsIcon />
        <H6 fontWeight="bold">
          {t(`eRecruit.application.personalDetails.contactDetails`)}
        </H6>
      </LabelStyle>

      <Row gap={space[5]}>
        <Row style={{ flex: 1, gap: space[3] }}>
          <Input
            control={control}
            as={AutocompletePopup<CountryCode, string>}
            data={countryCodeOptions}
            modalTitle={'Country code'}
            name="contact.countryCode"
            style={{ flex: 0.4 }}
            label={t(`eRecruit.application.personalDetails.code`)}
            getItemValue={item => item?.value}
            getItemLabel={item => item?.label}
            keyExtractor={item => item?.value + item?.label}
            getDisplayedLabel={item => getCountryCodeValue(item)}
            error={errors?.contact?.countryCode?.message}
            disabled
          />

          <Input
            autoCapitalize="none"
            control={control}
            as={PhoneField}
            name="contact.phoneNumber"
            label={t(`eRecruit.application.personalDetails.mobileNumber`)}
            style={{ flex: 0.6 }}
            onChangeText={value => {
              setValue(
                'contact.phoneNumber',
                value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
              );
            }}
            keyboardType="numeric"
            error={errors?.contact?.phoneNumber?.message}
            size="large"
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            initialHighlight={false}
          />
        </Row>
        <Input
          autoCapitalize="none"
          control={control}
          as={TextField}
          name="contact.email"
          style={{ flex: 1 }}
          label={t(`eRecruit.application.personalDetails.email`)}
          error={errors?.contact?.email?.message}
          shouldHighlightOnUntouched={Input.defaultHighlightCheck}
          initialHighlight={false}
        />
        <Row style={{ flex: 1, gap: space[3] }}>
          <Input
            control={control}
            as={AutocompletePopup<CountryCode, string>}
            data={countryCodeOptions}
            modalTitle={'Country code'}
            name="contact.officeNumberCountryCode"
            style={{ flex: 0.4 }}
            label={t(`eRecruit.application.personalDetails.code`)}
            getItemValue={item => item?.value}
            getItemLabel={item => item?.label}
            keyExtractor={item => item?.value + item?.label}
            getDisplayedLabel={item => getCountryCodeValue(item)}
            error={errors?.contact?.officeNumberCountryCode?.message}
            disabled
          />
          <Input
            control={control}
            as={PhoneField}
            name="contact.officePhoneNumber"
            label={t(
              `eRecruit.application.personalDetails.officePhoneOptional`,
            )}
            style={{ flex: 0.7 }}
            onChangeText={value => {
              setValue(
                'contact.officePhoneNumber',
                value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
              );
            }}
            keyboardType="numeric"
            error={errors?.contact?.officePhoneNumber?.message}
            size="large"
          />
        </Row>
      </Row>
    </Container>
  );
}

function InsuranceExperience({
  hookForm,
}: {
  hookForm: UseFormReturn<PersonalDetailsSchemaType>;
}) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const control = hookForm.control;
  const trigger = hookForm.trigger;

  return (
    <Container>
      <LabelStyle>
        <NewInsuranceExpIcon />
        <H6 fontWeight="bold">
          {t(`eRecruit.application.personalDetails.insuranceExperience`)}
        </H6>
      </LabelStyle>

      <Row gap={space[2]}>
        <Label style={{ width: space[4] }}>1.</Label>
        <RecruitYesNoInput
          control={control}
          name={'insuranceExperience.haveLifeInsuranceExp'}
          label={t('eRecruit.application.personalDetails.questionOne')}
          onChange={() => {
            setTimeout(() => {
              trigger('insuranceExperience.haveLifeInsuranceExp');
            }, 500);
          }}
          shouldHighlightOnUntouched={Input.defaultHighlightCheckForBoolean}
          initialHighlight={false}
        />
      </Row>
      <Row gap={space[2]}>
        <Label style={{ width: space[4] }}>2.</Label>
        <RecruitYesNoInput
          control={control}
          name={'insuranceExperience.haveGeneralInsuranceExp'}
          label={t('eRecruit.application.personalDetails.questionTwo')}
          onChange={() => {
            setTimeout(() => {
              trigger('insuranceExperience.haveGeneralInsuranceExp');
            }, 500);
          }}
          shouldHighlightOnUntouched={Input.defaultHighlightCheckForBoolean}
          initialHighlight={false}
        />
      </Row>
    </Container>
  );
}

function TabFooter({
  onSaveForLater,
  onNext,
  isLoading,
  isSaveBtnDisabled,
  isSubmitBtnDisabled,
  LeftComponent = () => <></>,
}: {
  onSaveForLater: () => void;
  onNext: () => void;
  isLoading: boolean;
  isSaveBtnDisabled: boolean;
  isSubmitBtnDisabled: boolean;
  LeftComponent?: () => JSX.Element;
}) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  return (
    <TabletFooter
      style={{
        paddingHorizontal: space[6],
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}>
      <LeftComponent />

      <Row justifyContent="flex-end" gap={space[5]}>
        <Button
          variant={'secondary'}
          text={t(`eRecruit.application.otherDetails.saveForLater`)}
          onPress={onSaveForLater}
          loading={isLoading}
          // disabled={isLoading || !isFirstLastNamesFilled()}
          disabled={isSaveBtnDisabled}
          style={{ width: space[50] }}
          textStyle={{ ...tabletStyles.mainButtonText }}
          contentStyle={{ minHeight: space[13] }}
        />
        <Button
          variant={'primary'}
          text={t(`eRecruit.application.otherDetails.next`)}
          subtext={t(`eRecruit.application.personalDetails.occupationDetails`)}
          disabled={isSubmitBtnDisabled}
          onPress={onNext}
          loading={isLoading}
          style={{ width: space[50] }}
          textStyle={{ ...tabletStyles.mainButtonText }}
          contentStyle={{ minHeight: space[13], minWidth: space[50] }}
        />
      </Row>
    </TabletFooter>
  );
}

const Container = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[6],
    borderRadius: borderRadius.large,
    gap: space[5],
    flex: 1,
  }),
);

const LabelStyle = styled(Row)(({ theme: { space } }) => ({
  justifyContent: 'flex-start',
  alignItems: 'center',
  gap: space[2],
}));

export const SaveButtonStyle = styled(TouchableOpacity)(
  ({ theme: { space, colors }, disabled }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: space[2],
    borderRadius: space[1],
    borderColor: colors.primary,
    borderWidth: 2,
    lineHeight: 52,
    height: '100%',
    width: 160,
    opacity: disabled ? 0.5 : 1,
  }),
);

export const SaveButtonText = styled(H7)(({ theme: { colors } }) => ({}));

export const BottomStyle = styled(Row)(({ theme: { space, colors } }) => ({
  gap: 15,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
}));

export const Footer = styled(View)(({ theme: { space, colors } }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  return {
    maxHeight: 100,
    width: '100%',
    paddingHorizontal: space[8],
    borderWidth: 1,
    borderColor: colors.background,
    paddingTop: space[4],
    paddingBottom: space[2] + bottomInset,
    backgroundColor: colors.background,
  };
});

export const getCountryCodeValue = (item: CountryCode) =>
  item?.value.split(' - ')[0];

const findRegistrationData = (
  registration: ApplicationFormResponds['identity']['registration'],
  registrationKey: ApplicationFormResponds['identity']['registration'][number]['type'],
) => registration?.find(item => item?.type === registrationKey)?.number ?? '';

const findPhoneData = (
  contactInfo: ApplicationFormResponds['contact'],
  contactType: PhoneInfoTypeKeys,
) => {
  const targetInfo = contactInfo?.phones?.find(
    item => item?.type === contactType,
  );

  return {
    countryCode:
      targetInfo?.countryCode ??
      (country === 'my' ? DEFAULT_COUNTRY_PHONE_CODE_MY : ''),
    number: targetInfo?.number ?? '',
  };
};

const updateDependenceValue = (
  currentValue: string | undefined,
  incrementValue: number,
  setValue: UseFormSetValue<any>,
  trigger?: () => void,
) => {
  const cur = isNaN(parseInt(currentValue || ''))
    ? 0
    : parseInt(currentValue || '');
  const newValue = Math.max(cur + incrementValue, 0); // Prevent negative values
  setValue('identity.numberOfDependence', newValue.toString());
  trigger && trigger();
};
