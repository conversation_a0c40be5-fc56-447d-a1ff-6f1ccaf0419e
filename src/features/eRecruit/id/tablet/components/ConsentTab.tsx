import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Box, Column, Icon, LargeLabel, Row } from 'cube-ui-components';
import Animated, { LinearTransition } from 'react-native-reanimated';
import ProgressStepBar from 'features/eRecruit/components/tablet/ProgressStepBar';
import { RootStackParamListMap } from 'types/navigation';
import { IConsentType } from 'features/eRecruit/my/type';
import { PdfContentSection } from 'features/eRecruit/id/tablet/components/PdfContentSection';
import { RecruitKey } from 'utils/translation/i18next';
import { ConsentFooter } from 'features/eRecruit/id/tablet/components/ConsentFooter';
import { IDNSignatureBottomSheet } from 'features/eRecruit/id/tablet/components/IDNSignatureBottomSheet';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import useBoundStore from 'hooks/useBoundStore';
import { useGetConsentList } from 'features/eRecruit/hooks/useGetConsentList';
import { DocumentResource } from 'api/eRecruitApi';
import { t } from 'i18next';
import LoadingIndicator from 'components/LoadingIndicator';
import { country } from 'utils/context';

export function ConsentTab() {
  const { setAppLoading, setAppIdle } = useBoundStore(
    state => state.appActions,
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['my'], 'ERecruitApplication'>>();
  const registrationStagingIdParam = route.params?.registrationStagingId ?? '';

  const { data, isLoading } = useGetApplicationData(
    registrationStagingIdParam.toString(),
  );

  const { data: consentList, isLoading: isConsentListLoading } =
    useGetConsentList({
      registrationStagingId: registrationStagingIdParam.toString(),
    });

  const backendDrivenList = buildStepsFromApi(consentList);

  const initialTab = useMemo(() => backendDrivenList?.[0], [backendDrivenList]);

  const [selectedTabFromDynamic, setSelectedTabFromDynamic] =
    useState<FEConsentStep | null>(initialTab);

  const [isShowF2FSignature, setIsShowF2FSignature] = useState(false);
  const [isShowRemoteSignature, setIsShowRemoteSignature] = useState(false);
  const [isFooterShown, setIsFooterShown] = useState(false);

  const { space, colors } = useTheme();

  const handlePressNext = () => {
    const activeTabIdx = backendDrivenList?.findIndex(
      e => selectedTabFromDynamic && e.id === selectedTabFromDynamic.id,
    );
    const isLastTab = activeTabIdx === backendDrivenList?.length - 1;
    if (isLastTab) {
      return setIsShowF2FSignature(true);
    }

    backendDrivenList &&
      setSelectedTabFromDynamic(backendDrivenList[activeTabIdx + 1]);
  };

  const showFooter = useCallback(() => {
    setIsFooterShown(true);
  }, []);

  const hideSignaturePopup = () => {
    setIsShowF2FSignature(false);
    setIsShowRemoteSignature(false);
  };

  useEffect(() => {
    setIsFooterShown(false);
  }, [selectedTabFromDynamic]); // effect runs whenever pressedTab changes

  useEffect(() => {
    if (isLoading) {
      setAppLoading();
    } else setAppIdle();

    if (!selectedTabFromDynamic && backendDrivenList && initialTab) {
      setSelectedTabFromDynamic(initialTab);
    }
  }, [
    isLoading,
    setAppLoading,
    setAppIdle,
    selectedTabFromDynamic,
    backendDrivenList,
    initialTab,
  ]);

  return (
    <Animated.View style={{ flex: 1 }}>
      <ProgressStepBar
        isTabGreyWhenDisabled={country == 'id'}
        type="lastStep"
      />
      <Animated.View
        layout={LinearTransition}
        style={{
          flex: 1,
          flexDirection: 'row',
        }}>
        <Column width={312} p={space[6]}>
          <SideBarContainerStyle>
            {isConsentListLoading ? (
              <Box boxSize={24} alignSelf="center">
                <LoadingIndicator size={24} />
              </Box>
            ) : (
              backendDrivenList?.map((item, index) => {
                const isLastItem = index === backendDrivenList.length - 1;

                const isActive = selectedTabFromDynamic?.id === item.id;

                const onPress = () => {
                  setSelectedTabFromDynamic(item);
                };

                const isAfterCurrentTab =
                  index >
                  backendDrivenList.findIndex(
                    tab => tab.id === selectedTabFromDynamic?.id,
                  );

                return (
                  <React.Fragment key={item.id}>
                    <TouchableOpacity
                      onPress={onPress}
                      disabled={isAfterCurrentTab}>
                      <Row
                        justifyContent="space-between"
                        alignItems="center"
                        backgroundColor={colors.background}>
                        <LargeLabel
                          style={{
                            width: '90%',
                          }}
                          fontWeight={isActive ? 'bold' : 'normal'}
                          color={
                            isActive
                              ? colors.primary
                              : colors.palette.fwdDarkGreen[100]
                          }>
                          {item.label + ' from BE'}
                        </LargeLabel>
                        <Icon.ChevronRight
                          size={16}
                          fill={
                            isActive
                              ? colors.primary
                              : colors.palette.fwdGreyDarker
                          }
                        />
                      </Row>
                    </TouchableOpacity>
                    {!isLastItem && <Line />}
                  </React.Fragment>
                );
              })
            )}
          </SideBarContainerStyle>
        </Column>
        {/* <Column width={312} p={space[6]}>
          <SideBarContainerStyle>
            {ID_SIDE_BAR.map((item, index) => {
              const isLastItem = index === ID_SIDE_BAR.length - 1;

              const isActive = selectedTab.type === item.type;

              const onPress = () => {
                if (agreementList.some(e => e.type === item.type)) {
                  setSelectedTab(item);
                }
              };

              return (
                <React.Fragment key={item.type}>
                  <TouchableOpacity onPress={onPress}>
                    <Row
                      justifyContent="space-between"
                      alignItems="center"
                      backgroundColor={colors.background}>
                      <LargeLabel
                        style={{
                          width: '90%',
                        }}
                        fontWeight={isActive ? 'bold' : 'normal'}
                        color={
                          isActive
                            ? colors.primary
                            : colors.palette.fwdDarkGreen[100]
                        }>
                        {t(item.label)}
                      </LargeLabel>
                      <Icon.ChevronRight
                        size={16}
                        fill={
                          selectedTab.type === item.type
                            ? colors.primary
                            : colors.palette.fwdGreyDarker
                        }
                      />
                    </Row>
                  </TouchableOpacity>
                  {!isLastItem && <Line />}
                </React.Fragment>
              );
            })}
          </SideBarContainerStyle>
        </Column> */}
        {selectedTabFromDynamic && (
          <PdfContentSection
            configFromBackend={
              selectedTabFromDynamic satisfies DocumentResource
            }
            showFooter={showFooter}
            registrationStagingId={registrationStagingIdParam}
          />
        )}
      </Animated.View>

      {/* ----------- Footer */}
      {isFooterShown ? (
        <ConsentFooter
          configFromBackend={selectedTabFromDynamic}
          handlePress={handlePressNext}
          setIsShowRemoteSignature={setIsShowRemoteSignature}
        />
      ) : null}
      {(isShowRemoteSignature || isShowF2FSignature) && (
        <IDNSignatureBottomSheet
          signatureType={isShowRemoteSignature ? 'Remote' : 'F2F'}
          registrationStagingId={registrationStagingIdParam}
          setIsSignatureVisible={hideSignaturePopup}
        />
      )}
    </Animated.View>
  );
}

const SideBarContainerStyle = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[4],
    borderRadius: borderRadius.medium,
  }),
);

const Line = styled.View(({ theme }) => ({
  borderColor: theme.colors.surface,
  borderTopWidth: 1,
  height: 1,
  marginVertical: theme.space[4],
}));

export type ISideBarConfig = {
  type: IConsentType;
  label: RecruitKey;
  nextBtnLabel: RecruitKey;
};

export type IAgreement = {
  agreed: boolean;
} & ISideBarConfig;

type BaseStep = Omit<ISideBarConfig, 'nextBtnLabel'>;

// const BASE_STEPS: BaseStep[] = [
//   {
//     type: CONSENT_FORM_TYPES.AGENT_CONTACT_PREVIEW,
//     label: 'eRecruit.application.consent.agentAgreement',
//   },
//   {
//     type: CONSENT_FORM_TYPES.LEADER_CONTRACT,
//     label: 'eRecruit.application.consent.leaderAgreement',
//   },
//   {
//     type: CONSENT_FORM_TYPES.AAJI_CODE_OF_ETHICS,
//     label: 'eRecruit.application.consent.aajiCodeOfEthics',
//   },
//   {
//     type: CONSENT_FORM_TYPES.FWD_CODE_OF_ETHICS_OF_SALES,
//     label: 'eRecruit.application.consent.fwdCodeOfEthicsOfSales',
//   },
//   {
//     type: CONSENT_FORM_TYPES.FWD_CODE_OF_ETHICS,
//     label: 'eRecruit.application.consent.fwdCodeOfEthics',
//   },
//   {
//     type: CONSENT_FORM_TYPES.NON_TWISTING_LETTER,
//     label: 'eRecruit.application.consent.nonTwistingLetter',
//   },
//   {
//     type: CONSENT_FORM_TYPES.PERSONAL_DATA_PROTECTION,
//     label: 'eRecruit.application.consent.personalDataProtection',
//   },
//   {
//     type: CONSENT_FORM_TYPES.CONFIRMATION_LETTER,
//     label: 'eRecruit.application.consent.confirmationLetter',
//   },
// ];

export type FEConsentStep = DocumentResource & {
  label: string;
  nextBtnLabel: string;
};

const buildStepsFromApi = (
  docList: DocumentResource[] | undefined,
): FEConsentStep[] => {
  if (!docList) return [];
  return docList.map(doc => ({
    ...doc,
    label: docIdToLabel?.[doc.id] ?? doc.id,
    nextBtnLabel: 'eRecruit.application.consent.next',
  }));
};

const docIdToLabel: Record<DocumentResource['id'], string> = {
  AgentAgreement: t('eRecruit:eRecruit.application.consent.agentAgreement'),
  LeaderAgreement: t('eRecruit:eRecruit.application.consent.leaderAgreement'),
  AAJICodeOfEthics: t('eRecruit:eRecruit.application.consent.aajiCodeOfEthics'),
  FWDCodeOfEthicsOfSales: t(
    'eRecruit:eRecruit.application.consent.fwdCodeOfEthicsOfSales',
  ),
  FWDCodeOfEthics: t('eRecruit:eRecruit.application.consent.fwdCodeOfEthics'),
  PersonalDataProtection: t(
    'eRecruit:eRecruit.application.consent.personalDataProtection',
  ),
  ConfirmationLetter: t(
    'eRecruit:eRecruit.application.consent.confirmationLetter',
  ),
};
