import {
  Row,
  PictogramIcon,
  H6,
  Column,
  <PERSON>iew,
  H7,
  Body,
  LargeBody,
  CubePictogramIcon,
} from 'cube-ui-components';
import { View } from 'react-native';

import { useTheme } from '@emotion/react';
import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';
import InfoField, {
  InfoFieldContainer,
} from 'features/eRecruit/components/InfoField';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';

type ContactProps = ApplicationFormResponds['contact'] | undefined;

export function EmergencyContact({
  contact,
  configList,
}: {
  contact: ContactProps;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, sizes } = useTheme();
  const emergencyContact = contact?.emergencyContact;
  return (
    <View>
      <Row
        gap={space[2]}
        style={{ alignItems: 'center', paddingBottom: space[5] }}>
        <CubePictogramIcon.Alarm size={sizes[10]} />
        <H6 fontWeight="bold" color="#333">
          {t('eRecruit.application.otherDetails.emergencyContact')}
        </H6>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t(`eRecruit.application.personalDetails.fullName`)}
            data={emergencyContact?.fullName ?? 'N/A'}
          />
          <InfoField
            label={t(`eRecruit.application.otherDetails.residenceNumber`)}
            data={emergencyContact?.residentNumber ?? 'N/A'}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t(`eRecruit.application.personalDetails.phoneNumber`)}
            data={
              emergencyContact?.mobile
                ? `${
                    emergencyContact?.mobile.countryCode
                      ? `${emergencyContact?.mobile.countryCode} `
                      : ''
                  }${emergencyContact?.mobile.number}`
                : 'N/A'
            }
          />
          <InfoField label={''} data={''} />
        </InfoFieldContainer>
        <Column gap={space[1]}>
          <LabelStyle>
            {t('eRecruit.application.otherDetails.addressLine')}
          </LabelStyle>
          <DataStyle>
            {emergencyContact?.address ? `${emergencyContact?.address},` : ''}
            {emergencyContact?.state
              ? ` ${
                  configList?.provinceList?.find(
                    item => item.itemCode === emergencyContact?.state,
                  )?.longDesc.en
                },`
              : ''}
            {emergencyContact?.city ? ` ${emergencyContact?.city}` : ''}
            {emergencyContact?.postCode ? ` ${emergencyContact?.postCode}` : ''}
          </DataStyle>
        </Column>
      </Column>
    </View>
  );
}

const DataStyle = styled(LargeBody)(({ theme: { colors } }) => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const LabelStyle = styled(LargeBody)(
  ({ theme: { colors, space, borderRadius } }) => ({
    color: colors.palette.fwdGreyDarker,
    flex: 1,
  }),
);
