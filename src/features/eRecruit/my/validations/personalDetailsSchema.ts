import { t } from 'i18next';
import { object, string, boolean, date, ref } from 'yup';
import { DefaultValues } from 'react-hook-form';
import { calculateAge } from 'utils/helper/calculateAge';
import { DEFAULT_COUNTRY_PHONE_CODE_MY } from 'features/eRecruit/config';
import { invalidDateMessage } from 'features/eApp/validations/eAppErrorMessages';
import { nullableDate } from 'features/eApp/validations/eAppCommonSchema';
import { Gender } from 'types/person';
import { validateNewNRIC } from 'utils/helper/idNumberUtils';
import { isSameDay } from 'date-fns';
import { NewApplicationFormValues } from 'types/eRecruit';

const maxNameLength = 30;
const maxTitleLength = 30;
const maxIcNumberLength = 12;
const minPhoneNumberlength = 9;
const yearOfPassingLength = 4;
const professionalQualiLength = 30;
const isZeroToNine = /^[0-9]+$/;
const emailRegex = /^[a-zA-Z0-9\-_\.]+@[a-zA-Z0-9\-_]+(\.[a-zA-Z0-9\-_]+)+$/;
const nameLengthErrorMsg = t('form.nameTooLong');

const yearOfPassingErrorMsgNumberOnly = t('form.inputNumberOnly');
const yearOfPassingErrorMsg = t('form.yearOfPassingLimitCharacters');
const icNumberErrorMsg = t('form.icNumberLimitCharacters');
const taxNumberErrorMsg = t('form.taxNumberFormatError');
const nricNotMatchedWithDateGenderMsg = t('form.nricError.dateNotMatch');

const REQUIRED_INPUT = t('validation.required');
const INVALID_FORMAT = t('form.invalidFormat');
const minPhoneNumberHandler = (min: number) => {
  return `Must be at least ${min} characters`;
};
export const validateNewNRICTakaful = (
  nric: string,
  dob: Date | undefined,
  gender: Gender | undefined,
) => {
  // Extract date of birth
  const nricDob = nric.substring(0, 6);
  const year = nricDob.substring(0, 2);
  const month = nricDob.substring(2, 4);
  const day = nricDob.substring(4, 6);
  const parsedDateOfBirthLower = new Date(`19${year}-${month}-${day}`);
  const parsedDateOfBirthUpper = new Date(`20${year}-${month}-${day}`);

  // Extract gender
  const genderDigit = nric.charAt(11);
  const parsedGender =
    parseInt(genderDigit) % 2 === 0 ? Gender.FEMALE : Gender.MALE;
  if (
    !dob ||
    !parsedDateOfBirthLower ||
    !parsedDateOfBirthUpper ||
    !gender ||
    !parsedGender
  ) {
    return false;
  }
  return (
    isSameDay(dob, parsedDateOfBirthUpper) ||
    isSameDay(dob, parsedDateOfBirthLower)
  );
};

export const initialApplicationData: DefaultValues<NewApplicationFormValues> = {
  identity: {
    firstName: '',
    lastName: '',
    gender: '',
    title: '',
    dateOfBirth: null,
    icNumber: '',
    citizen: '',
    ethnicity: '',
    religion: '',
    maritalStatus: '',
    passportNo: '',
    oldIcNumber: '',
    taxCode: '',
  },
  contact: {
    countryCode: DEFAULT_COUNTRY_PHONE_CODE_MY,
    phoneNumber: '',
    email: '',
    officeNumberCountryCode: DEFAULT_COUNTRY_PHONE_CODE_MY,
    officePhoneNumber: '',
  },
  personalInformation: {
    educationType: undefined,
    takafulCertFamily: false,
    takafulCertGeneral: false,
    insuranceCertPce: false,
    insuranceCertCeilli: false,
    insuranceCertGeneral: false,
    islamicCert: false,
    MFPCQualification: false,
    MFCPYearOfPassing: '',
    FPAMQualification: false,
    FPAMYearOfPassing: '',
    otherQualifications: false,
    otherQualificationsDesc: '',
  },
  spouseInformation: {
    firstName: '',
    lastName: '',
    icNumber: '',
    numberOfDependence: null,
    occupation: '',
    passportNo: '',
    oldIcNumber: '',
  },
};

export const validatePhoneMY = string()
  .required(REQUIRED_INPUT)
  .test('testPhoneNumber', function (value) {
    //TODO:error msg
    const { countryCode } = this.parent;
    const minLength = 11;
    const maxLength = 15;
    const countryCodeLength = parseInt(countryCode)?.toString()?.length ?? 0;
    const combinedLength = countryCodeLength + (value?.length ?? 0);

    const errorMessage = `Length should be from ${
      minLength - countryCodeLength
    } to ${maxLength - countryCodeLength} digits`;
    const hasValue = value && value !== '';

    if (
      hasValue &&
      (combinedLength < minLength || combinedLength > maxLength)
    ) {
      return this.createError({ message: errorMessage });
    }

    if (hasValue && !isZeroToNine.test(value)) {
      return this.createError({ message: INVALID_FORMAT });
    }

    return true;
  });

export const personalDetailsSchema = object().shape({
  identity: object().shape({
    firstName: string()
      .max(maxNameLength, nameLengthErrorMsg)
      .required(REQUIRED_INPUT),
    lastName: string()
      .max(maxNameLength, nameLengthErrorMsg)
      .required(REQUIRED_INPUT),
    gender: string().required(REQUIRED_INPUT),
    title: string().max(maxTitleLength).required(REQUIRED_INPUT),
    dateOfBirth: nullableDate().test({
      name: 'min-age',
      test: value => {
        if (value instanceof Date) {
          const age = calculateAge(value);
          return age >= 18 && age <= 60;
        }
        return false;
      },
      message: invalidDateMessage,
    }),
    icNumber: string()
      .matches(isZeroToNine, icNumberErrorMsg)
      .min(maxIcNumberLength, icNumberErrorMsg)
      .max(maxIcNumberLength, icNumberErrorMsg)
      .required(REQUIRED_INPUT)
      .test(
        'is-same-dob-and-gender',
        nricNotMatchedWithDateGenderMsg,
        function (value, ctx) {
          const dob: Date | undefined = ctx.resolve(ref('dateOfBirth'));
          const gender: Gender | undefined = ctx.resolve(ref('gender'));
          if (!value) return true;
          const res = validateNewNRICTakaful(value, dob, gender);
          return res;
        },
      ),
    citizen: string().required(REQUIRED_INPUT),
    ethnicity: string().required(REQUIRED_INPUT),
    religion: string().required(REQUIRED_INPUT),
    maritalStatus: string().required(REQUIRED_INPUT),
    passportNo: string().nullable().notRequired(),
    oldIcNumber: string().nullable().notRequired(),
    taxCode: string()
      .required(REQUIRED_INPUT)
      .matches(/^[A-Za-z]{2}\d{10,11}$/, taxNumberErrorMsg),
  }),

  contact: object().shape({
    countryCode: string().required(REQUIRED_INPUT),
    phoneNumber: validatePhoneMY,
    email: string()
      .matches(emailRegex, t('form.invalidEmail'))
      .required(REQUIRED_INPUT),
    officePhoneNumber: string()
      .nullable()
      .notRequired()
      .test('testOfficePhoneNumber', INVALID_FORMAT, function (value) {
        //TODO:error msg
        if (value && value !== '') {
          return isZeroToNine.test(value);
        }
        return true;
      }),
    //TODO: error msg and no
    officeNumberCountryCode: string().when('officePhoneNumber', {
      is: (officePhoneNumber: string) => Boolean(officePhoneNumber),
      then: schema => schema.required(REQUIRED_INPUT),
      otherwise: schema => schema.nullable().notRequired(),
    }),
  }),
  personalInformation: object().shape({
    educationType: string().required(REQUIRED_INPUT),
    takafulCertFamily: boolean().test({
      name: 'required',
      message: REQUIRED_INPUT,
      test: value => value,
    }),
    takafulCertGeneral: boolean().nullable().notRequired().default(false),
    insuranceCertPce: boolean().nullable().notRequired().default(false),
    insuranceCertCeilli: boolean().nullable().notRequired().default(false),
    insuranceCertGeneral: boolean().nullable().notRequired().default(false),
    islamicCert: boolean().nullable().notRequired().default(false),
    MFPCQualification: boolean().nullable().notRequired().default(false),
    MFCPYearOfPassing: string().when('MFPCQualification', {
      is: true,
      then: schema =>
        schema
          .min(yearOfPassingLength, yearOfPassingErrorMsg)
          .max(yearOfPassingLength, yearOfPassingErrorMsg)
          .matches(isZeroToNine, yearOfPassingErrorMsgNumberOnly)
          .required(REQUIRED_INPUT),
      otherwise: schema => schema.nullable().notRequired(),
    }),
    FPAMQualification: boolean().nullable().notRequired().default(false),
    FPAMYearOfPassing: string().when('FPAMQualification', {
      is: true,
      then: schema =>
        schema
          .min(yearOfPassingLength, yearOfPassingErrorMsg)
          .max(yearOfPassingLength, yearOfPassingErrorMsg)
          .matches(isZeroToNine, yearOfPassingErrorMsgNumberOnly)
          .required(REQUIRED_INPUT),
      otherwise: schema => schema.nullable().notRequired(),
    }),
    otherQualifications: boolean().nullable().notRequired().default(false),
    otherQualificationsDesc: string().when('otherQualifications', {
      is: true,
      then: schema =>
        schema
          .max(
            professionalQualiLength,
            t('form.invalidProfessionalQualiLength'),
          )
          .required(REQUIRED_INPUT),
      otherwise: schema => schema.nullable().notRequired(),
    }),
  }),

  spouseInformation: object().when('identity.maritalStatus', {
    is: 'M',
    then: schema =>
      object().shape({
        firstName: string()
          .max(maxNameLength, nameLengthErrorMsg)
          .required(REQUIRED_INPUT),
        lastName: string()
          .max(maxNameLength, nameLengthErrorMsg)
          .required(REQUIRED_INPUT),
        icNumber: string()
          .min(maxIcNumberLength, icNumberErrorMsg)
          .max(maxIcNumberLength, icNumberErrorMsg)
          .matches(isZeroToNine, icNumberErrorMsg)
          .required(REQUIRED_INPUT),
        numberOfDependence: string().required(REQUIRED_INPUT),
        occupation: string().required(REQUIRED_INPUT),
        passportNo: string().optional(),
        oldIcNumber: string().optional(),
      }),
  }),
});
