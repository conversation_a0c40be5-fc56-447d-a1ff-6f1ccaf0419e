import { object, mixed, string, ObjectSchema, array, number } from 'yup';
import { t } from 'i18next';
import { formPhoneNumber } from 'utils/validation/customValidation';
import {
  GenderCodeUnion,
  NewCandidateFormValues,
} from 'features/eRecruit/my/type';
import { country } from 'utils/context';
import { validatePhoneMY } from './personalDetailsSchema';
const emailRegex = /^[a-zA-Z0-9\-_\.]+@[a-zA-Z0-9\-_]+(\.[a-zA-Z0-9\-_]+)+$/;
const REQUIRED_INPUT = t('validation.required');
export const invalidInputMessage = () => t('form.invalidInput');

export const initialCandidateData: NewCandidateFormValues = {
  id: '',
  firstName: '',
  lastName: '',
  gender: null,
  emailAddress: '',
  countryCode: '',
  phoneNumber: '',
};

export const requiredAddCandidateFieldsObj = {
  firstName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  lastName: string()
    .required(REQUIRED_INPUT)
    .validateLeadName(invalidInputMessage()),
  emailAddress: string()
    .nullable()
    .matches(emailRegex, t('form.invalidEmail'))
    .notRequired(),
  countryCode: string().required(REQUIRED_INPUT),
  phoneNumber:
    country == 'my' ? validatePhoneMY : formPhoneNumber('countryCode'),
} as const;

export const addNewCandidateSchema = object({
  ...requiredAddCandidateFieldsObj,
  gender: string<GenderCodeUnion>().nullable(),
});
