import { baseUrl } from 'utils/context';

export function formatBytes(bytes: number) {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    const kilobytes = (bytes / 1024).toFixed(1);
    return kilobytes + ' KB';
  } else {
    const megabytes = (bytes / (1024 * 1024)).toFixed(1);
    return megabytes + ' MB';
  }
}

export function getDocumentImageUri(params: {
  fileId: string;
  registrationStagingId: string;
}) {
  const { registrationStagingId, fileId } = params;
  const imageUri = `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationStagingId=${registrationStagingId}&fileId=${fileId}`;

  return imageUri;
}

export function getNewDocumentImageUri(params: {
  fileId: string;
  registrationStagingId: string;
}) {
  const { registrationStagingId, fileId } = params;
  const imageUri = `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationId=${registrationStagingId}&fileId=${fileId}`;

  return imageUri;
}

export function getNewDocumentImageUriIDN(params: {
  fileId: string;
  registrationStagingId: string;
  fileKey: string;
}) {
  const { registrationStagingId, fileId, fileKey } = params;
  const imageUri = `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationId=${registrationStagingId}&fileId=${fileId}&fileKey=${fileKey}`;

  return imageUri;
}
