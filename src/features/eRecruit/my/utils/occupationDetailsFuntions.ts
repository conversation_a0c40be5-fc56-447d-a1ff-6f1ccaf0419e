import { cloneDeep } from 'utils/helper/objectUtil';
import { ParsingAppFormDataActionKeys } from 'types/eRecruit';
import { WorkExperience } from 'features/eRecruit/my/type';
import { ApplicationFormResponds, WorkingExperienceType } from 'types/eRecruit';
import { format } from 'date-fns';

export type AlterationOccupationSection = {
  workingExperiences: {
    type: string;
    basicSalary: number | null;
    companyAddress?: string | null;
    companyEmail?: string | null;
    companyName: string | null;
    companyPhoneCountryCode?: string | null;
    companyPhone?: string | null;
    dateApplied: string | null;
    dateTermination: string | null;
    intermediaryType?: string | null;
    position?: string | null;
    rank?: string | null;
  }[];
  spouseInformation: {
    type: string | null;
    companyName: string | null;
    rank: string | null;
    salary: number | null;
    dateApplied: string | null;
    dateTermination: string | null;
  };
};

type ArrayElement<ArrayType extends readonly unknown[]> =
  ArrayType extends readonly (infer ElementType)[] ? ElementType : never;

const convertWorkExperience = (
  workExpList: WorkExperience[],
  type: WorkingExperienceType,
) => {
  let requestWorkExpList: AlterationOccupationSection['workingExperiences'] =
    [];
  workExpList.forEach(workExp => {
    let isPushToList = false;
    for (const field in workExp) {
      if (field === 'companyPhoneCountryCode') {
        continue;
      }
      if (!!workExp[field as keyof typeof workExp]) {
        isPushToList = true;
      }
    }

    if (isPushToList) {
      switch (type) {
        case WorkingExperienceType.PREVIOUS:
          requestWorkExpList.push({
            companyAddress: workExp.companyAddress || null,
            companyEmail: workExp.companyEmail || null,

            companyName: workExp.companyName || null,
            companyPhoneCountryCode: workExp.companyPhoneCountryCode || null,
            companyPhone: workExp.companyPhone || null,
            position: workExp.position || null,
            dateApplied: workExp.dateApplied
              ? format(workExp.dateApplied, 'yyyy-MM-dd')
              : null,
            dateTermination: workExp.dateTermination
              ? format(workExp.dateTermination, 'yyyy-MM-dd')
              : null,
            basicSalary: workExp.basicSalary
              ? parseFloat(workExp.basicSalary)
              : null,
            type,
          });
          break;

        case WorkingExperienceType.TAKAFUL:
        case WorkingExperienceType.INSURANCE:
          requestWorkExpList.push({
            intermediaryType: workExp.intermediaryType || null,
            companyName: workExp.companyName || null,
            rank: workExp.rank || null,
            basicSalary: workExp.basicSalary
              ? parseFloat(workExp.basicSalary)
              : null,
            dateApplied: workExp.dateApplied
              ? format(workExp.dateApplied, 'yyyy-MM-dd')
              : null,
            dateTermination: workExp.dateTermination
              ? format(workExp.dateTermination, 'yyyy-MM-dd')
              : null,
            type,
          });
          break;
      }
    }
  });

  return requestWorkExpList;
};

export const parsingOccupationData = ({
  input,
  spouseInsuranceExpToggleCheck,
  recruitmentCache,
  pressAction,
}: {
  input: {
    currentOccupation: WorkExperience;
    previousOccupationList: WorkExperience[];
    familyTakafulExperienceList: WorkExperience[];
    lifeInsuranceExperienceList: WorkExperience[];
    spouseInsuranceExperience: WorkExperience;
  };
  spouseInsuranceExpToggleCheck: boolean;
  recruitmentCache?: ApplicationFormResponds;
  pressAction?: ParsingAppFormDataActionKeys;
}) => {
  let stage = '';

  if (pressAction === 'save') {
    stage = 'OCCUPATION_DETAILS';
  } else if (pressAction === 'next') {
    stage = 'OTHER_DETAILS';
  }
  if (!recruitmentCache) {
    return;
  }
  const recruitmentCacheClone = cloneDeep(recruitmentCache);

  const currentOccupationOutput = {
    companyAddress: input?.currentOccupation?.companyAddress || null,
    companyEmail: input?.currentOccupation?.companyEmail || null,

    companyName: input?.currentOccupation?.companyName || null,
    companyPhoneCountryCode:
      input?.currentOccupation?.companyPhoneCountryCode || null,
    companyPhone: input?.currentOccupation?.companyPhone || null,
    position: input?.currentOccupation?.position || null,
    dateApplied: input?.currentOccupation?.dateApplied
      ? format(input.currentOccupation.dateApplied, 'yyyy-MM-dd')
      : null,
    dateTermination: input?.currentOccupation?.dateTermination
      ? format(input.currentOccupation.dateTermination, 'yyyy-MM-dd')
      : null,
    basicSalary: input?.currentOccupation?.basicSalary
      ? parseInt(input.currentOccupation.basicSalary)
      : null,
    type: WorkingExperienceType.PRESENT,
  };

  const spouseInformation: AlterationOccupationSection['spouseInformation'] =
    spouseInsuranceExpToggleCheck
      ? {
          ...recruitmentCacheClone.spouseInformation,
          type: input.spouseInsuranceExperience.intermediaryType || null,
          companyName: input.spouseInsuranceExperience.companyName || null,
          rank: input.spouseInsuranceExperience.rank || null,
          dateApplied: input.spouseInsuranceExperience.dateApplied
            ? format(input.spouseInsuranceExperience.dateApplied, 'yyyy-MM-dd')
            : null,
          dateTermination: input.spouseInsuranceExperience.dateTermination
            ? format(
                input.spouseInsuranceExperience.dateTermination,
                'yyyy-MM-dd',
              )
            : null,
          salary: input.spouseInsuranceExperience.salary
            ? parseFloat(input.spouseInsuranceExperience.salary)
            : null,
          spouseInsuranceRepresent: spouseInsuranceExpToggleCheck,
        }
      : {
          ...recruitmentCacheClone.spouseInformation,
          type: null,
          companyName: null,
          rank: null,
          dateApplied: null,
          dateTermination: null,
          salary: null,
        };

  const output = {
    ...recruitmentCacheClone,
    workingExperiences: [
      // currentOccupationOutput,
      ...convertWorkExperience(
        input.previousOccupationList,
        WorkingExperienceType.PREVIOUS,
      ),
      ...convertWorkExperience(
        input.familyTakafulExperienceList,
        WorkingExperienceType.TAKAFUL,
      ),
      ...convertWorkExperience(
        input.lifeInsuranceExperienceList,
        WorkingExperienceType.INSURANCE,
      ),
    ],
    spouseInformation: spouseInformation,
    // stage: getStage('OCCUPATION_DETAILS', recruitmentCacheClone.stage),
    stage: stage,
  };
  if (currentOccupationOutput.companyName !== null) {
    output.workingExperiences.push(currentOccupationOutput);
  }

  return output;
};
