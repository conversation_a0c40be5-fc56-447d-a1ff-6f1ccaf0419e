import { baseUrl, buildNumber, appVersion, country } from 'utils/context';
import { RECRUIT_ENDPOINT } from 'api/eRecruitApi';
import { IConsentType } from 'features/eRecruit/my/type';

export const getPrivacyFormPdfUrl = (registrationStagingId: string) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/privacy-form/${registrationStagingId}`;

export const getConsentFormPdfUrl = (registrationStagingId: string) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/consent-form/${registrationStagingId}`;

export const getConductFormPdfUrl = (registrationStagingId: string) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/conduct-form/${registrationStagingId}`;

export const getAgentAgreementPdfUrl = (registrationStagingId: string) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/agent-agreement/${registrationStagingId}`;

export const getConsentPdfUrl = (
  registrationStagingId: string,
  formType?: IConsentType,
) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/form?registrationStagingId=${registrationStagingId}&formType=${formType}`;

export const getDynamicPdfUrl = (dataObject: Record<string, string | null>) =>
  `${baseUrl}/api-gateway${RECRUIT_ENDPOINT}/document/form${createDynamicUriParams(
    dataObject,
  )}`;

function createDynamicUriParams(dataObject: Record<string, string | null>) {
  // Get an array of the object's keys and filter out any keys
  // that have a null or undefined value.
  const validKeys = Object.keys(dataObject).filter(
    key => dataObject[key] !== null && dataObject[key] !== undefined,
  );

  // If there are no valid keys left, return an empty string.
  if (validKeys.length === 0) {
    return '';
  }

  // Map each valid key to a "key=encodedValue" string.
  const params = validKeys.map(key => {
    const value = dataObject[key];
    // encodeURIComponent is crucial for ensuring characters like '/', '&', '?'
    // are treated as part of the value, not as URL delimiters.
    const encodedValue = encodeURIComponent(value);
    return `${key}=${encodedValue}`;
  });

  // Join all the parameter strings with '&' and prepend a '?'.
  return `?${params.join('&')}`;
}
