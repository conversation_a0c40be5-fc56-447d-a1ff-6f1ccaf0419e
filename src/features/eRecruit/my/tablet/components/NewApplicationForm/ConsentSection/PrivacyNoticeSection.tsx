import { StyleSheet } from 'react-native';
import FloatingHintButton from 'components/FloatingHintButton';
import { Dimensions, View, useWindowDimensions } from 'react-native';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { getPrivacyFormPdfUrl } from './pdfFilesConfig';
import { ConsentSectionProps } from 'types/eRecruit';
import useBoundStore from 'hooks/useBoundStore';
import Pdf from 'react-native-pdf';
import { useEffect, useState } from 'react';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

// let totalPages = 3; // you can create a global param for this
export function PrivacyNoticeSection({
  registrationStagingId,
  showFooter,
}: ConsentSectionProps) {
  const { t } = useTranslation('eRecruit');
  // const [totalPages, setTotalPages] = useState(1);

  const uri = getPrivacyFormPdfUrl(registrationStagingId ?? '');

  const { bottom } = useSafeAreaInsets();
  const { width, height } = useWindowDimensions();

  console.log('🤲🤲🤲 ~ file: PrivacyNoticeSection.tsx:27 ~ uri:', uri);
  const token = useBoundStore(state => state.auth.authInfo.accessToken);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const headers = {
    Authorization: `Bearer ${token}`,
    'x-agent-id': agentId ?? '',
  };

  return (
    <View style={styles.pdfcontainer}>
      <Pdf
        scale={1}
        source={{ uri, headers, cache: true }}
        onPageChanged={(page, numberOfPages) => {
          console.log('page, totalPages', page, numberOfPages);
          if (showFooter) {
            page === numberOfPages && showFooter();
          }
        }}
        onError={error => console.log(error)}
        onPressLink={uri => console.log(`Link pressed: ${uri}`)}
        style={[
          styles.pdf,
          {
            width: width - 312,
            height: height,
          },
        ]}
        trustAllCerts={false}
      />
      <FloatingHintButton
        visible={true}
        text={t(`eRecruit.application.consent.scrollToBottom`)}
        containerStyle={{ bottom: 12 + bottom }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  pdfcontainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pdf: {
    flex: 1,
    backgroundColor: colors.fwdGreyDarkest,
    paddingTop: 14,
  },
});
