import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { getRecruitmentDocumentImage } from 'api/eRecruitApi';

import Portal from 'components/Portal/Portal';
import { Icon } from 'cube-ui-components';
import { documentDirectory, downloadAsync } from 'expo-file-system';
import useBoundStore from 'hooks/useBoundStore';
import { useRef, useEffect, useState } from 'react';
import { Dimensions, ImageSourcePropType, Pressable } from 'react-native';
import {
  PinchGestureHandlerGestureEvent,
  PanGestureHandlerGestureEvent,
  PanGestureHandler,
  PinchGestureHandler,
} from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedGestureHandler,
  withSpring,
  useAnimatedStyle,
  withTiming,
  FadeIn,
} from 'react-native-reanimated';
import Svg, { Image } from 'react-native-svg';

export default function ZoomableImageViewer({
  uri,
  isBase64,
  onDismiss,
}: {
  uri: string;
  isBase64: boolean;
  onDismiss: () => void;
}) {
  const { colors } = useTheme();

  const imageRef = useRef(null);
  const pinchRef = useRef(null);
  const panRef = useRef(null);
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const { width, height } = Dimensions.get('window');
  const imageWidth = width * 0.85;
  const imageHeight = height * 0.85;
  const initialTranslateX = (width - imageWidth) / 2;
  const initialTranslateY = (height - imageHeight) / 2;

  const handleZoom = useAnimatedGestureHandler<PinchGestureHandlerGestureEvent>(
    {
      onActive: event => {
        scale.value = event.scale;
      },
      onEnd: () => {
        if (scale.value < 1) {
          scale.value = withSpring(1);
        }
      },
    },
  );

  const handlePan = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    { startX: number; startY: number }
  >({
    onStart: (_, context) => {
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      translateX.value = context.startX + event.translationX;
      translateY.value = context.startY + event.translationY;
    },
  });

  const imageStyle = useAnimatedStyle((): any => {
    return {
      width: imageWidth,
      height: imageHeight,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  useEffect(() => {
    // Center the image initially
    translateX.value = withTiming(initialTranslateX, { duration: 0 });
    translateY.value = withTiming(initialTranslateY, { duration: 0 });
  }, []);

  // !
  // const getImageHeader = {
  //   Accept: 'application/json',
  //   'Content-Type': 'application/json',
  //   Authorization:
  //     'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
  // };

  // !
  const [uploadedImg, setUploadedImg] = useState<string>('');

  async function getUploadedImg(uri: string) {
    const res = await getRecruitmentDocumentImage(uri);

    if (res) setUploadedImg(res);

    return res;
  }

  useEffect(() => {
    getUploadedImg(uri);
  }, []);

  return (
    <Portal>
      <Animated.View
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        }}>
        <CloseButton onPress={onDismiss}>
          <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
        </CloseButton>

        <PanGestureHandler
          ref={panRef}
          onGestureEvent={handlePan}
          simultaneousHandlers={pinchRef}>
          <Animated.View style={[{ flex: 1 }, imageStyle]} entering={FadeIn}>
            <PinchGestureHandler ref={pinchRef} onGestureEvent={handleZoom}>
              <Animated.View
                style={{
                  flex: 1,
                }}>
                <Svg
                  width={width}
                  height={height}
                  viewBox={`-${width / 5} -${height / 5} ${width} ${height}`}
                  style={{ flex: 1 }}>
                  {uploadedImg && (
                    <Image
                      ref={imageRef}
                      width="50%"
                      height="50%"
                      href={{
                        uri: isBase64
                          ? 'data:image/png;base64,' + uri
                          : uploadedImg,
                        // method: 'GET',
                        // headers: getImageHeader,
                      }}
                    />
                  )}
                </Svg>
              </Animated.View>
            </PinchGestureHandler>
          </Animated.View>
        </PanGestureHandler>
      </Animated.View>
    </Portal>
  );
}

const CloseButton = styled(Pressable)(({ theme }) => ({
  maxWidth: 40,
  backgroundColor: theme.colors.background,
  padding: theme.space[2],
  borderRadius: theme.borderRadius.full,
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 9999,
  //
  position: 'absolute',
  top: 150,
  right: 250,
}));
