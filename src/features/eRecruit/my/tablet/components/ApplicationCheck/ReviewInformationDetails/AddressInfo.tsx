import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Row, PictogramIcon, H6, Column, H7 } from 'cube-ui-components';
import { ApplicationFormResponds } from 'types/eRecruit';
import React from 'react';
import { View } from 'react-native';

export type ContactInfo = {
  email: string;
  phones:
    | {
        countryCode: string;
        number: string;
        type: string;
      }[]
    | null[];
  address: {
    line1: string | null;
    line2: string | null;
    city: string | null;
    cityDesc: string | null;
    state: string | null;
    stateDesc: string | null;
    postCode: string | null;
  };
  businessAddress: {
    line1: string | null;
    line2: string | null;
    city: string | null;
    cityDesc: string | null;
    state: string | null;
    stateDesc: string | null;
    postCode: string | null;
  };
  bankInformation: {
    accountNumber: string | null;
    icNumber: string | null;
    bankName: string | null;
  };
};

type ContactDetailsProps = ApplicationFormResponds['contact'] | undefined;

export function AddressInfo({ contact }: { contact: ContactDetailsProps }) {
  const { colors, space } = useTheme();
  return (
    <View>
      <Row
        gap={space[2]}
        style={{ alignItems: 'center', paddingBottom: space[5] }}>
        <PictogramIcon.Home2 size={space[10]} />
        <H6 fontWeight="bold">{'Address information'}</H6>
      </Row>
      <Column gap={space[2]}>
        <View style={{ paddingBottom: space[1] }}>
          <LabelStyle>{'Residential Address'}</LabelStyle>
          <DataStyle>
            {contact?.address.line1 ? `${contact?.address.line1},` : ''}
            {contact?.address.line2 ? ` ${contact?.address.line2},` : ''}
            {contact?.address.city ? ` ${contact?.address.city},` : ''}
            {contact?.address.state ? ` ${contact?.address.state},` : ''}
            {'Malaysia'}
          </DataStyle>
        </View>
        {contact?.businessAddress.line1 !== null && (
          <View style={{ paddingBottom: space[1] }}>
            <LabelStyle>{'Business Address'}</LabelStyle>
            <DataStyle>
              {contact?.businessAddress.line1
                ? `${contact?.businessAddress.line1},`
                : ''}
              {contact?.businessAddress.line2
                ? ` ${contact?.businessAddress.line2},`
                : ''}
              {contact?.businessAddress.city
                ? ` ${contact?.businessAddress.city},`
                : ''}
              {contact?.businessAddress.state
                ? ` ${contact?.businessAddress.state},`
                : ''}
              {' Malaysia'}
            </DataStyle>
          </View>
        )}
      </Column>
    </View>
  );
}

const DataStyle = styled(H7)(({ theme: { colors } }) => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const LabelStyle = styled(H7)(({ theme: { colors, space, borderRadius } }) => ({
  color: colors.palette.fwdGreyDarker,
  flex: 1,
}));
