import { useTheme } from '@emotion/react';
import { Column, H6, PictogramIcon, Row, XView } from 'cube-ui-components';
import React from 'react';
import { View } from 'react-native';
import InfoField from 'features/eRecruit/components/InfoField';
import { ApplicationFormResponds } from 'types/eRecruit';

export type WorkingExperiences =
  | {
      type: string;
      basicSalary: number;
      companyAddress: string;
      companyEmail: string;
      companyName: string;
      companyPhoneCountryCode: string | null;
      companyPhone: string | null;
      dateApplied: string;
      dateTermination: string;
      intermediaryType: string;
      position: string;
      rank: string;
    }[]
  | [];

type WorkingExperiencesProps = ApplicationFormResponds['workingExperiences'];

export function CurrentOccupation({ work }: { work: WorkingExperiencesProps }) {
  const { colors, space } = useTheme();
  return (
    <View>
      <Row
        gap={space[2]}
        style={{ alignItems: 'center', paddingBottom: space[5] }}>
        <PictogramIcon.Work3 size={space[10]} />
        <H6 fontWeight="bold">{'Current Occupation'}</H6>
      </Row>
      {work &&
        work
          .filter(work => work.type === 'PRESENT')
          .map((item, index) => (
            <React.Fragment key={'Current' + index}>
              <Column gap={space[2]}>
                <XView>
                  <InfoField
                    label={'Position held'}
                    data={item.position ?? 'N/A'}
                  />
                  <InfoField
                    label={'Name of company'}
                    data={item.companyName ?? 'N/A'}
                  />
                </XView>
                <XView>
                  <InfoField
                    label={'Last draw salary'}
                    data={item.basicSalary.toString() ?? 'N/A'}
                  />
                  <InfoField
                    label={'Date appointed'}
                    data={item.dateApplied ?? 'N/A'}
                  />
                </XView>

                <XView>
                  <InfoField
                    label={'Date termination'}
                    data={item.dateTermination ?? 'N/A'}
                  />
                  <InfoField
                    label={'Contact no'}
                    data={item.companyPhone ?? 'N/A'}
                  />
                </XView>
                <XView>
                  <InfoField
                    label={'Email'}
                    data={item.companyEmail ?? 'N/A'}
                  />
                  <InfoField label={''} data={''} />
                </XView>
                <XView>
                  <InfoField
                    label={'Company address'}
                    data={item.companyAddress ?? 'N/A'}
                  />
                  <InfoField label={''} data={''} />
                </XView>
              </Column>
            </React.Fragment>
          ))}
    </View>
  );
}
