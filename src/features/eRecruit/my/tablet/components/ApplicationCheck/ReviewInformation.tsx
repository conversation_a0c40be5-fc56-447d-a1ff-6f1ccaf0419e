import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Typography } from 'cube-ui-components';

import { ScrollView, TouchableOpacity, View } from 'react-native';
import { IdentityDetails } from './ReviewInformationDetails/IdentityDetails';
import { ContactDetails } from './ReviewInformationDetails/ContactDetails';
import { Qualification } from './ReviewInformationDetails/Qualification';
import { SpouseInfo } from './ReviewInformationDetails/SpouseInfo';
import {
  CurrentOccupation,
  WorkingExperiences,
} from './ReviewInformationDetails/CurrentOccupation';
import { PreviousOccupation } from './ReviewInformationDetails/PreviousOccupation';
import { AddressInfo } from './ReviewInformationDetails/AddressInfo';
import { BankInfo } from './ReviewInformationDetails/BankInfo';
import { LeaderInfo } from './ReviewInformationDetails/LeaderInfo';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  ProgressBarConfig,
  ProgressBarItem,
} from 'screens/ERecruitScreen/ERecruitCheckApplicationScreen/ERecruitCheckAppScreen.tablet';
import { CheckApplicationScreensParamList } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';

import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import { SpouseOccupation } from './ReviewInformationDetails/SpouseOccupation';
import { SupervisorAndCandidateInfo } from './ReviewInformationDetails/SupervisorCandidateInfo';
import { Dispatch, SetStateAction, useCallback, useEffect } from 'react';
import { cloneDeep } from 'lodash';
import { useFocusEffect } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

export function ReviewInformation({
  candidateId,
  navigation,
  barStatus,
  setBarStatus,
}: {
  candidateId: number | null;
  navigation: NativeStackNavigationProp<
    CheckApplicationScreensParamList,
    any,
    undefined
  >;
  barStatus: ProgressBarConfig;
  setBarStatus: Dispatch<SetStateAction<ProgressBarConfig>>;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');

  useFocusEffect(
    useCallback(() => {
      const currentPage: ProgressBarItem['name'] = 'reviewInformation';
      const currentPageIdx = barStatus.findIndex(p => p.name === currentPage);
      setBarStatus(currentStatus =>
        currentStatus.map((item, i) => ({
          ...item,
          onfocus: item.name === currentPage,
          hightlighted: item.name === currentPage || currentPageIdx > i,
          completed: currentPageIdx > i,
        })),
      );
    }, []),
  );

  const { data: applicationData, isLoading } = useGetApplicationData(
    candidateId == null ? '' : candidateId.toString(),
    //* enabled: Boolean(applicationId), the hook is disabled when ""
  );
  //   console.log('reviewInformation', JSON.stringify(applicationData));
  const { data: configList } = useGetERecruitConfig();

  // console.log('reviewInformation', JSON.stringify(applicationData));

  const identity = applicationData?.identity;
  const contact = applicationData?.contact;
  const qualifications = applicationData?.qualifications;
  const spouse = applicationData?.spouseInformation;
  const work = applicationData?.workingExperiences ?? [];
  const leader = applicationData?.leaderInformation;
  const position = applicationData?.position;
  const introducerCode = applicationData?.introducerCode;
  const introducerName = applicationData?.introducerName;

  return (
    <>
      <ScrollView>
        <Column
          paddingX={space[8]}
          paddingTop={space[4]}
          paddingBottom={space[30]}
          flex={1}
          gap={space[4]}>
          <GreenWhiteCard>
            <GreenHeader>
              <Typography.H7 fontWeight="bold" color={colors.background}>
                {t('eRecruit.application.review.personalDetails')}
              </Typography.H7>
            </GreenHeader>
            <WhiteContent>
              <IdentityDetails identity={identity} configList={configList} />
              <WhiteContentSpace />
              <ContactDetails contact={contact} />
              <WhiteContentSpace />
              <Qualification qualifications={qualifications} />
              <WhiteContentSpace />
              {spouse?.firstName && spouse?.lastName && (
                <SpouseInfo spouse={spouse} />
              )}
            </WhiteContent>
          </GreenWhiteCard>
          {work.length > 0 && (
            <GreenWhiteCard>
              <GreenHeader>
                <Typography.H7 fontWeight="bold" color={colors.background}>
                  {t('eRecruit.application.review.occupationDetails')}
                </Typography.H7>
              </GreenHeader>
              <WhiteContent>
                {work.find(item => item.type === 'PRESENT') && (
                  <>
                    <CurrentOccupation work={work} />
                    <WhiteContentSpace />
                  </>
                )}
                {work.find(item => item.type === 'PREVIOUS') && (
                  <>
                    <PreviousOccupation work={work} workType={'PREVIOUS'} />
                    <WhiteContentSpace />
                  </>
                )}
                {work.find(item => item.type === 'TAKAFUL') && (
                  <>
                    <PreviousOccupation work={work} workType={'TAKAFUL'} />
                    <WhiteContentSpace />
                  </>
                )}
                {work.find(item => item.type === 'INSURANCE') && (
                  <>
                    <PreviousOccupation work={work} workType={'INSURANCE'} />
                    <WhiteContentSpace />
                  </>
                )}
                {spouse?.firstName && spouse?.lastName && (
                  <SpouseOccupation spouse={spouse} />
                )}
              </WhiteContent>
            </GreenWhiteCard>
          )}
          <GreenWhiteCard>
            <GreenHeader>
              <Typography.H7 fontWeight="bold" color={colors.background}>
                {t('eRecruit.application.review.otherDetails')}
              </Typography.H7>
            </GreenHeader>
            <WhiteContent>
              <AddressInfo contact={contact} />
              <WhiteContentSpace />
              <BankInfo contact={contact} />
              <WhiteContentSpace />
              <SupervisorAndCandidateInfo
                position={position}
                introducerName={introducerName}
                introducerCode={introducerCode}
                configList={configList}
              />
              <WhiteContentSpace />
              <LeaderInfo leader={leader} />
            </WhiteContent>
          </GreenWhiteCard>
        </Column>
      </ScrollView>
      <View
        style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          backgroundColor: colors.background,
          paddingHorizontal: space[6],
          paddingVertical: space[4],
          alignItems: 'flex-end',
          borderTopColor: colors.palette.fwdGrey[100],
          borderTopWidth: 1,
        }}>
        <TouchableOpacity
          style={{
            backgroundColor: colors.primary,
            width: 200,
            height: 52,
            paddingHorizontal: space[4],
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: space[1],
          }}
          onPress={() => {
            let currentStatus = cloneDeep(barStatus);
            const indexOfAddInfo = currentStatus.findIndex(
              item => item.name === 'additionalInformation',
            );
            const indexOfReviewInfo = currentStatus.findIndex(
              item => item.name === 'reviewInformation',
            );
            const indexOfDocuments = currentStatus.findIndex(
              item => item.name === 'documents',
            );
            currentStatus[indexOfAddInfo].completed = true;
            currentStatus[indexOfReviewInfo].completed = true;
            currentStatus[indexOfDocuments].completed = false;
            setBarStatus(currentStatus);
            navigation.navigate('documents');
          }}>
          <Typography.LargeLabel color={colors.background} fontWeight="bold">
            Next
          </Typography.LargeLabel>
          <Typography.SmallBody color={colors.background}>
            {t('eRecruit.application.review.reviewInformation')}
          </Typography.SmallBody>
        </TouchableOpacity>
      </View>
    </>
  );
}
const GreenWhiteCard = styled(View)(({ theme: { colors, space } }) => ({
  gap: 0,
  width: '100%',
}));

const GreenHeader = styled(View)(({ theme: { colors, space } }) => ({
  backgroundColor: colors.palette.fwdDarkGreen[100],
  paddingHorizontal: space[6],
  paddingVertical: space[3],
  borderTopRightRadius: space[4],
  borderTopLeftRadius: space[4],
}));

const WhiteContent = styled(View)(({ theme: { colors, space } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
  paddingVertical: space[5],
  borderBottomRightRadius: space[4],
  borderBottomLeftRadius: space[4],
}));

const WhiteContentSpace = styled(View)(({ theme: { colors, space } }) => ({
  padding: space[4],
}));
