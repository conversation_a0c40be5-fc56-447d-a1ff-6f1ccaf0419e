import styled from '@emotion/native';
import { Portal } from '@gorhom/portal';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import React from 'react';
import { Modal } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { RootSiblingParent } from 'react-native-root-siblings';
import { country } from 'utils/context';

type AddNewCandidateModalProps = {
  visible: boolean;
  children?: React.ReactNode;
};

export default function AddNewCandidateModal({
  visible,
  children,
}: AddNewCandidateModalProps) {
  return (
    <Portal>
      <Modal
        visible={visible}
        animationType="fade"
        transparent={true}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <KeyboardAwareScrollView
          contentContainerStyle={{
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
            backgroundColor: 'rgba(0,0,0,0.5)',
          }}>
          <ModalContainer>{children}</ModalContainer>
          {country === 'id' && (
            <RootSiblingParent>
              <></>
            </RootSiblingParent>
          )}
        </KeyboardAwareScrollView>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  width: '80%',
  borderRadius: sizes[4],
  presentationStyle: 'formSheet',
  padding: sizes[12],
}));
