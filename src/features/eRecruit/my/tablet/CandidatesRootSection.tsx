import { ScrollView, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import { Typography, Row, Icon, Chip } from 'cube-ui-components';
import styled from '@emotion/native';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import AddNewCandidateButton from 'features/eRecruit/my/tablet/components/AddNewCandidateButton';
import { useTranslation } from 'react-i18next';
import CandidatesList from 'features/eRecruit/components/CandidateList';
import {
  CubeStatusKeys,
  SortDirectionKeys,
  cubeStatusList,
} from 'types/eRecruit';
import { filterLabelMap } from 'features/eRecruit/config';

import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import SearchingCandidatesListSection from 'features/eRecruit/my/tablet/SearchingCandidatesList';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useGetERApplicationList } from '../../hooks/useGetERApplicationList';

export default function CandidatesRootSection() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { bottom } = useSafeAreaInsets();

  const [activeFilters, setActiveFilters] = useState<CubeStatusKeys>();
  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const [isSearching, setIsSearching] = useState(false);

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList: activeFilters ? [activeFilters] : [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
  });

  const candidatesList = data?.data ?? [];

  const candidateCount = candidatesList?.length ?? 0;

  if (isSearching) {
    return (
      <CommonAnimatedViewWrapper>
        <SearchingCandidatesListSection setIsSearching={setIsSearching} />
      </CommonAnimatedViewWrapper>
    );
  }
  return (
    <CommonAnimatedViewWrapper>
      <HeaderContainer>
        <Row
          minH={space[11]}
          alignItems="center"
          justifyContent="space-between">
          <Typography.H6 fontWeight="bold">Candidates</Typography.H6>
          <Row alignItems="center" gap={space[4]}>
            <SearchButton onPress={() => setIsSearching(!isSearching)}>
              <Icon.Search />
            </SearchButton>
          </Row>
        </Row>
        <FilterSectionRow>
          <CandidateSectionSubLabel>
            {t(`eRecruit.candidate.filterBy`)}
          </CandidateSectionSubLabel>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              gap: space[2],
            }}>
            {cubeStatusList
              .filter(status => status !== 'RESUME_APPLICATION')
              .map(status => (
                <Chip
                  key={status}
                  label={filterLabelMap?.[status]}
                  focus={activeFilters === status}
                  onPress={() =>
                    activeFilters === status
                      ? setActiveFilters(undefined)
                      : setActiveFilters(status)
                  }
                />
              ))}
          </ScrollView>
        </FilterSectionRow>

        <Row alignItems="center" gap={space[2]} paddingY={space[2]}>
          <CandidateSectionSubLabel>
            {t('candidate.total.withCount', {
              count: numberToThousandsFormat(candidateCount) as any,
            })}
          </CandidateSectionSubLabel>
          <TouchableOpacity
            onPress={() => {
              setOrder(order === 'oldest' ? 'newest' : 'oldest');
            }}>
            <Row alignItems="center">
              <Typography.Body fontWeight="bold" color={colors.primary}>
                Last update
              </Typography.Body>
              {order === 'newest' ? (
                <Icon.ArrowUp size={space[4]} />
              ) : (
                <Icon.ArrowDown size={space[4]} />
              )}
            </Row>
          </TouchableOpacity>
        </Row>
      </HeaderContainer>

      <View style={{ position: 'absolute', right: 24, bottom: 28, zIndex: 10 }}>
        <AddNewCandidateButton />
      </View>
      <CandidatesList
        isLoading={isLoading}
        data={candidatesList}
        isRefreshing={isRefetching}
        onRefresh={refetch}
        contentContainerStyle={{ paddingBottom: 80 + bottom }}
      />
    </CommonAnimatedViewWrapper>
  );
}

const CandidateSectionSubLabel = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const CommonAnimatedViewWrapper = styled(AnimatedViewWrapper)(({ theme }) => ({
  flex: 23,
  backgroundColor: theme.colors.surface,
}));

const HeaderContainer = styled.View(({ theme }) => ({
  gap: theme.space[3],
  paddingHorizontal: theme.space[6],
  paddingTop: theme.space[5],
  paddingBottom: theme.space[2],
}));

const SearchButton = styled.TouchableOpacity(({ theme }) => ({
  borderRadius: theme.borderRadius.full,
  height: theme.space[11],
  width: theme.space[11],
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: theme.colors.palette.white,
  borderWidth: 1,
  borderColor: theme.colors.primary,
}));
