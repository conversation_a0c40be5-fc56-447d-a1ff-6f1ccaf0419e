import { View, TouchableOpacity } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Row,
  Icon,
  Typography,
  Box,
  LoadingIndicator,
  Chip,
} from 'cube-ui-components';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useMemo, useState } from 'react';
import { dateFormatUtil } from 'utils/helper/formatUtil';

import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
} from 'types/eRecruit';
import NoDataFound from 'features/eRecruit/my/tablet/applicationStatus/NoDataFound';
import { FlashList } from '@shopify/flash-list';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

const lastUpdateHandler = (item: ApplicationListResponds) => {
  switch (item.cubeStatus) {
    case 'PENDING_PAYMENT':
    case 'PENDING_LEADER_APPROVAL':
      return item.submissionDate ?? '';
    case 'REMOTE_CHECKING':
    case 'REMOTE_SIGNATURE':
    case 'RESUME_APPLICATION':
      return item.lstUpdDate ?? '';
    default:
      return '';
  }
};

export default function ERInProgressScreen({
  filterStatus,
}: {
  filterStatus: CubeStatusKeys | undefined;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eRecruit']);
  const [filterBy, setFilterBy] = useState<CubeStatusKeys | undefined>(
    filterStatus,
  );
  const [isSortDate, setIsSortDate] = useState(false);

  const { isLoading: isAppstatusDataLoading, data: newData } =
    useGetERApplicationList({
      cubeStatusList: [
        'PENDING_PAYMENT',
        'PENDING_LEADER_APPROVAL',
        'REMOTE_CHECKING',
        'REMOTE_SIGNATURE',
        'RESUME_APPLICATION',
      ],
      limit: 99999,
    });

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !newData
        ? []
        : newData?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePosition ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [newData, isSortDate],
  );

  const processedData = useMemo(
    () =>
      filterBy
        ? sortedAppStatusData.filter(item => item.status == filterBy)
        : sortedAppStatusData,
    [sortedAppStatusData, filterBy],
  );
  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    filterBy !== tabFilter ? setFilterBy(tabFilter) : setFilterBy(undefined);
  };

  return (
    <Box flex={1} bgColor={colors.palette.fwdGrey[50]}>
      <Typography.H6 fontWeight="bold" style={{ paddingTop: space[2] }}>
        {t('eRecruit:applicationStatus.inProgress')}
      </Typography.H6>
      <Row py={space[3]} gap={space[2]} alignItems="center">
        <Typography.Body
          fontWeight="normal"
          color={colors.palette.fwdGreyDarkest}>
          {t('eRecruit:applicationStatus.filterBy')}
        </Typography.Body>
        <ScrollView horizontal contentContainerStyle={{ gap: space[1] }}>
          <Chip
            focus={filterBy === 'REMOTE_SIGNATURE'}
            label={t('eRecruit:candidate.status.pendingRemoteSignature')}
            onPress={() => tagOnPress('REMOTE_SIGNATURE')}
          />
          <Chip
            focus={filterBy === 'REMOTE_CHECKING'}
            label={t('eRecruit:candidate.status.remoteCheckingRequired')}
            onPress={() => tagOnPress('REMOTE_CHECKING')}
          />
          <Chip
            focus={filterBy === 'PENDING_PAYMENT'}
            label={t('eRecruit:candidate.status.pendingPayment')}
            onPress={() => tagOnPress('PENDING_PAYMENT')}
          />
          <Chip
            focus={filterBy === 'PENDING_LEADER_APPROVAL'}
            label={t('eRecruit:candidate.status.pendingLeaderApproval')}
            onPress={() => tagOnPress('PENDING_LEADER_APPROVAL')}
          />
          <Chip
            focus={filterBy === 'RESUME_APPLICATION'}
            label={t('eRecruit:candidate.status.resumeApplication')}
            onPress={() => tagOnPress('RESUME_APPLICATION')}
          />
        </ScrollView>
      </Row>
      <View style={{ paddingBottom: space[3] }}>
        <Typography.Body color={colors.palette.fwdGreyDarkest}>
          {t('eRecruit:applicationStatus.totalCaseShownFromLast90Days', {
            count: processedData?.length,
          })}
        </Typography.Body>
      </View>
      <ERTable
        data={processedData}
        isListProcessing={isAppstatusDataLoading}
        isSortDate={isSortDate}
        setIsSortDate={setIsSortDate}
      />
    </Box>
  );
}

const TableHeader = ({
  isSortDate,
  setIsSortDate,
}: {
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eRecruit']);

  return (
    <Row
      p={space[4]}
      bgColor={colors.primary}
      borderTopLeftRadius={space[4]}
      borderTopRightRadius={space[4]}>
      <Row style={{ flex: 286 }}>
        <Typography.Label fontWeight="medium" color={colors.background}>
          {t('eRecruit:applicationStatus.table.candidateName')}
        </Typography.Label>
      </Row>
      <Row style={{ flex: 100 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <Typography.Label fontWeight="medium" color={colors.background}>
          {t('eRecruit:applicationStatus.table.position')}
        </Typography.Label>
      </Row>
      <Row style={{ flex: 286 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <Typography.Label fontWeight="medium" color={colors.background}>
          {t('eRecruit:applicationStatus.table.status')}
        </Typography.Label>
      </Row>
      <Row style={{ flex: 140 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <TouchableOpacity
          onPress={() => {
            setIsSortDate(!isSortDate);
          }}>
          <Row justifyContent="center" alignItems="center">
            <Typography.Label fontWeight="medium" color={colors.background}>
              {t('eRecruit:applicationStatus.table.lastUpdate')}
            </Typography.Label>
            <Box boxSize={space[1]} />
            {isSortDate ? (
              <Icon.ArrowUp size={space[4]} fill={colors.background} />
            ) : (
              <Icon.ArrowDown size={space[4]} fill={colors.background} />
            )}
          </Row>
        </TouchableOpacity>
      </Row>
    </Row>
  );
};

const ERTable = ({
  data,
  isListProcessing,
  isSortDate,
  setIsSortDate,
}: {
  data: ERTableContent[];
  isListProcessing: boolean;
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();

  return (
    <>
      <TableHeader isSortDate={isSortDate} setIsSortDate={setIsSortDate} />
      <FlashList
        data={data}
        bounces={false}
        estimatedItemSize={70}
        contentContainerStyle={{
          backgroundColor: colors.palette.fwdGrey[50],
          paddingBottom: bottom > space[4] ? bottom : space[4],
        }}
        ItemSeparatorComponent={() => (
          <Box backgroundColor={colors.palette.fwdGrey[100]} h={1} />
        )}
        ListEmptyComponent={() => (
          <>
            {isListProcessing ? (
              <Box
                backgroundColor={colors.background}
                minHeight={60}
                justifyContent="center"
                alignItems="center"
                borderBottomRadius={16}>
                <Box h={space[5]} width={space[5]}>
                  <LoadingIndicator size={space[5]} />
                </Box>
              </Box>
            ) : (
              <NoDataFound />
            )}
          </>
        )}
        renderItem={({ item, index }) => (
          <TableItem
            lastIndex={data.length}
            index={index}
            {...(item as ERTableContent)}
          />
        )}
        keyExtractor={data => String(data.recruitId)}
      />
    </>
  );
};

function TableItem({
  displayName,
  position,
  status,
  lastUpdated,
  index,
  lastIndex,
  stage,
  recruitId,
}: {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  index: number | null;
  lastIndex: number | null;
  stage: ApplicationStageKeys;
  recruitId?: number;
}) {
  const { colors, space, sizes } = useTheme();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();

  const onPressItem = () => {
    if (recruitId) {
      navigate('ERecruitCandidateProfile', {
        id: recruitId,
        registrationId:
          status == 'PENDING_LEADER_APPROVAL' || status == 'PENDING_PAYMENT'
            ? recruitId
            : null,
        registrationStagingId:
          status == 'PENDING_LEADER_APPROVAL' || status == 'PENDING_PAYMENT'
            ? null
            : recruitId,
        stage: stage,
        cubeStatus: status,
      });
    }
  };
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          onPressItem();
        }}
        style={{
          borderBottomLeftRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          borderBottomRightRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          backgroundColor:
            index && (index + 1) % 2 == 0
              ? colors.palette.fwdGrey[20]
              : colors.background,
        }}>
        <Row marginX={space[4]} my={space[4]} flex={1}>
          <View style={{ flex: 286, justifyContent: 'center' }}>
            <Typography.Body color={colors.secondary}>
              {displayName}
            </Typography.Body>
          </View>
          <Row alignItems="center" flex={100}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Typography.Body color={colors.secondary}>
              {position}
            </Typography.Body>
          </Row>
          <Row alignItems="center" flex={286}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />

            <StatusColoring status={status} />
          </Row>
          <Row alignItems="center" flex={140}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Row
              style={{
                justifyContent: 'space-between',
                width: '100%',
                paddingRight: space[2],
              }}>
              <Typography.Body>{dateFormatUtil(lastUpdated)}</Typography.Body>
              <Icon.ChevronRight fill={colors.primary} size={24} />
            </Row>
          </Row>
        </Row>
      </TouchableOpacity>
    </>
  );
}

function StatusColoring({ status }: { status: CubeStatusKeys }) {
  const { colors, space, sizes } = useTheme();
  let colorCombo;
  let displayStatus;
  switch (status) {
    case 'REMOTE_SIGNATURE':
      colorCombo = {
        backgroundColor: colors.palette.fwdOrange[20],
        wordColor: colors.primary,
      };
      displayStatus = 'Pending remote signature';
      break;
    case 'REMOTE_CHECKING':
      colorCombo = {
        backgroundColor: colors.palette.fwdOrange[20],
        wordColor: colors.primary,
      };
      displayStatus = 'Remote checking required';
      break;
    case 'PENDING_PAYMENT':
      colorCombo = {
        backgroundColor: colors.palette.fwdGrey[50],
        wordColor: colors.palette.fwdGreyDarker,
      };
      displayStatus = 'Pending payment';
      break;
    case 'PENDING_LEADER_APPROVAL':
      colorCombo = {
        backgroundColor: colors.palette.fwdGrey[50],
        wordColor: colors.palette.fwdGreyDarker,
      };
      displayStatus = 'Pending leader approval';
      break;
    case 'RESUME_APPLICATION':
      colorCombo = {
        backgroundColor: colors.palette.fwdBlue[20],
        wordColor: colors.palette.fwdBlue[100],
      };
      displayStatus = 'Resume application';
      break;
    default:
      colorCombo = {
        backgroundColor: '',
        wordColor: '',
      };
      displayStatus = '';
  }
  return (
    <View
      style={{
        backgroundColor: colorCombo.backgroundColor,
        paddingHorizontal: space[1],
        paddingVertical: 2,
        borderRadius: 2,
      }}>
      <Typography.SmallLabel color={colorCombo.wordColor}>
        {displayStatus}
      </Typography.SmallLabel>
    </View>
  );
}
