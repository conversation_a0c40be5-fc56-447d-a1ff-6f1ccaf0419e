import { View, TouchableOpacity } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Row,
  Icon,
  Typography,
  Box,
  LoadingIndicator,
} from 'cube-ui-components';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useMemo, useState } from 'react';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import NoDataFound from 'features/eRecruit/my/tablet/applicationStatus/NoDataFound';

import { ApplicationStageKeys, CubeStatusKeys } from 'types/eRecruit';
import { FlashList } from '@shopify/flash-list';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';

type ERTableContent = {
  displayName: string;
  position?: string;
  rejectDate: string;
  recruitId?: number;
  status: CubeStatusKeys;
  stage: ApplicationStageKeys;
};

export default function ERRejectedScreen() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eRecruit']);
  const [filterBy, setFilterBy] = useState<'FWO' | 'FWM' | 'FWP' | null>(null);

  const [isSortDate, setIsSortDate] = useState(false);

  const { isLoading: isRejectedDataLoading, data: newData } =
    useGetERApplicationList({
      cubeStatusList: ['REJECTED'],
      limit: 99999,
    });

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !newData
        ? []
        : newData?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePosition ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              status: item.cubeStatus,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.rejectDate).getTime() -
                  new Date(b?.rejectDate).getTime()
                : new Date(b?.rejectDate).getTime() -
                  new Date(a?.rejectDate).getTime(),
            ),
    [newData, isSortDate],
  );

  const processedData = useMemo(
    () =>
      filterBy
        ? sortedAppStatusData.filter(item => item.position == filterBy)
        : sortedAppStatusData,
    [sortedAppStatusData, filterBy],
  );

  return (
    <Box flex={1} bgColor={colors.palette.fwdGrey[50]}>
      <Typography.H6 fontWeight="bold" style={{ paddingTop: space[2] }}>
        {t('eRecruit:applicationStatus.rejected')}
      </Typography.H6>
      <View style={{ paddingVertical: space[3] }}>
        <Typography.Body color={colors.palette.fwdGreyDarkest}>
          {t('eRecruit:applicationStatus.totalCaseShownFromLast90Days', {
            count: processedData?.length,
          })}
        </Typography.Body>
      </View>
      <ERTable
        data={processedData}
        isListProcessing={isRejectedDataLoading}
        isSortDate={isSortDate}
        setIsSortDate={setIsSortDate}
      />
    </Box>
  );
}

const TableHeader = ({
  isSortDate,
  setIsSortDate,
}: {
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eRecruit']);

  return (
    <Row
      p={space[4]}
      bgColor={colors.primary}
      borderTopLeftRadius={space[4]}
      borderTopRightRadius={space[4]}>
      <Row style={{ flex: 470 }}>
        <Typography.Label fontWeight="medium" color={colors.background}>
          {t('eRecruit:applicationStatus.table.candidateName')}
        </Typography.Label>
      </Row>
      <Row style={{ flex: 100 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <Typography.Label fontWeight="medium" color={colors.background}>
          {t('eRecruit:applicationStatus.table.position')}
        </Typography.Label>
      </Row>
      <Row style={{ flex: 140 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <TouchableOpacity
          onPress={() => {
            setIsSortDate(!isSortDate);
          }}>
          <Row justifyContent="center" alignItems="center">
            <Typography.Label fontWeight="medium" color={colors.background}>
              {t('eRecruit:applicationStatus.table.rejectDate')}
            </Typography.Label>
            <Box boxSize={space[1]} />
            {isSortDate ? (
              <Icon.ArrowUp size={space[4]} fill={colors.background} />
            ) : (
              <Icon.ArrowDown size={space[4]} fill={colors.background} />
            )}
          </Row>
        </TouchableOpacity>
      </Row>
    </Row>
  );
};

const ERTable = ({
  data,
  isListProcessing,
  isSortDate,
  setIsSortDate,
}: {
  data: ERTableContent[];
  isListProcessing: boolean;
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  return (
    <>
      <TableHeader isSortDate={isSortDate} setIsSortDate={setIsSortDate} />
      <FlashList
        data={data}
        bounces={false}
        estimatedItemSize={70}
        contentContainerStyle={{
          backgroundColor: colors.palette.fwdGrey[50],
          paddingBottom: bottom > space[4] ? bottom : space[4],
        }}
        ItemSeparatorComponent={() => (
          <Box backgroundColor={colors.palette.fwdGrey[100]} h={1} />
        )}
        ListEmptyComponent={() => (
          <>
            {isListProcessing ? (
              <Box
                backgroundColor={colors.background}
                minHeight={60}
                justifyContent="center"
                alignItems="center"
                borderBottomRadius={16}>
                <Box h={space[5]} width={space[5]}>
                  <LoadingIndicator size={space[5]} />
                </Box>
              </Box>
            ) : (
              <NoDataFound />
            )}
          </>
        )}
        renderItem={({ item, index }) => (
          <TableItem
            lastIndex={data.length}
            index={index}
            {...(item as ERTableContent)}
          />
        )}
        keyExtractor={data => String(data.recruitId)}
      />
    </>
  );
};

function TableItem({
  displayName,
  position,
  recruitId,
  rejectDate,
  index,
  lastIndex,
  status,
  stage,
}: {
  displayName: string;
  position?: string;
  recruitId?: number;
  rejectDate: string;
  index: number | null;
  lastIndex: number | null;
  status: CubeStatusKeys;
  stage: ApplicationStageKeys;
}) {
  const { colors, space, sizes } = useTheme();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();

  const onPressItem = () => {
    if (recruitId)
      navigate('ERecruitCandidateProfile', {
        id: recruitId,
        registrationId: recruitId,
        registrationStagingId: recruitId,
        stage: stage,
        cubeStatus: status ?? '',
      });
  };

  return (
    <>
      <TouchableOpacity
        onPress={() => {
          onPressItem();
        }}
        style={{
          borderBottomLeftRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          borderBottomRightRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          backgroundColor:
            index && (index + 1) % 2 == 0
              ? colors.palette.fwdGrey[20]
              : colors.background,
        }}>
        <Row marginX={space[4]} my={space[4]} flex={1}>
          <Row alignItems="center" flex={470}>
            <Typography.Body color={colors.secondary}>
              {displayName}
            </Typography.Body>
          </Row>
          <Row alignItems="center" flex={100}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Typography.Body color={colors.secondary}>
              {position}
            </Typography.Body>
          </Row>
          <Row alignItems="center" flex={140}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Row
              style={{
                justifyContent: 'space-between',
                width: '100%',
                paddingRight: space[2],
              }}>
              <Typography.Body>{dateFormatUtil(rejectDate)}</Typography.Body>
              <Icon.ChevronRight fill={colors.primary} size={24} />
            </Row>
          </Row>
        </Row>
      </TouchableOpacity>
    </>
  );
}

function StatusColoring({ status }: { status: string }) {
  const { colors, space, sizes } = useTheme();
  let colorCombo;
  switch (status) {
    case 'Pending remote signature':
      colorCombo = {
        backgroundColor: colors.palette.fwdOrange[20],
        wordColor: colors.primary,
      };
      break;
    case 'Remote checking required':
      colorCombo = {
        backgroundColor: colors.palette.fwdOrange[20],
        wordColor: colors.primary,
      };
      break;
    case 'Pending payment':
      colorCombo = {
        backgroundColor: colors.palette.fwdGrey[50],
        wordColor: colors.palette.fwdGreyDarker,
      };
      break;
    case 'Pending leader approval':
      colorCombo = {
        backgroundColor: colors.palette.fwdGrey[50],
        wordColor: colors.palette.fwdGreyDarker,
      };
      break;
    case 'Resume application':
      colorCombo = {
        backgroundColor: colors.palette.fwdBlue[100],
        wordColor: colors.palette.fwdBlue[20],
      };
      break;
    default:
      colorCombo = {
        backgroundColor: '',
        wordColor: '',
      };
  }
  return (
    <View
      style={{
        backgroundColor: colorCombo.backgroundColor,
        paddingHorizontal: space[1],
        paddingVertical: 2,
        borderRadius: 2,
      }}>
      <Typography.SmallLabel color={colorCombo.wordColor}>
        {status}
      </Typography.SmallLabel>
    </View>
  );
}
