import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column } from 'cube-ui-components';
import { FollowUpApplication } from './FollowUpApplication';
import { useRef, useState } from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { SortDirectionKeys } from 'types/eRecruit';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import { useGetERecruitStat } from 'features/eRecruit/hooks/useGetERecruitStat';

export const QUERY_STATUS_HEIGHT = 40;

export const ApplicationSection = ({
  handleScroll,
}: {
  handleScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
}) => {
  const { colors, space } = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);

  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    followUpList: true,
    cubeStatusList: [
      'PENDING_PAYMENT',
      'PENDING_LEADER_APPROVAL',
      'REMOTE_CHECKING',
      'REMOTE_SIGNATURE',
    ],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
  });
  const { data: homeData, refetch: homeDataRefetch } = useGetERecruitStat();

  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    refetch();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  return (
    <ScrollView
      ref={scrollViewRef}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={() => onRefresh()} />
      }
      style={{
        backgroundColor: colors.palette.fwdOrange[20],
      }}
      onScroll={handleScroll}>
      <Column
        gap={space[4]}
        backgroundColor={colors.palette.fwdOrange[20]}
        padding={space[4]}>
        <FollowUpApplication
          data={data}
          isLoading={refreshing}
          isRefetching={refreshing}
          refetch={refetch}
          order={order}
          setOrder={setOrder}
          homeData={homeData}
          homeDataRefetch={homeDataRefetch}
        />

        <EmptyBottomView />
      </Column>
    </ScrollView>
  );
};

const EmptyBottomView = styled.View(({ theme }) => ({
  paddingVertical: theme.space[6],
}));
