import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ActionPanel, Column, Icon, Row, Typography } from 'cube-ui-components';
import { contactCandidate } from 'features/eRecruit/util/contactUtils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export const ContactButtonPanel = ({
  isOpen,
  setIsOpen,
  email,
  phoneNumber,
}: {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  email: string;
  phoneNumber: string;
}) => {
  const { space, colors } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation('eRecruit');

  const onPressSocialMediaButton = (type: 'email' | 'call') => {
    return contactCandidate({
      method: type,
      info: type === 'email' ? email : phoneNumber,
      errorMsg: t(
        'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided',
      ),
    });
  };

  return (
    <ActionPanel
      visible={isOpen}
      handleClose={() => {
        setIsOpen(false);
      }}
      title={'Contact your candidate'}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[isWideScreen ? 5 : 4] + bottom,
          ios: 0,
        }),
      }}>
      <Row justifyContent="space-evenly" minH={space[14]} marginTop={space[4]}>
        <Column gap={space[4]} alignItems="center" flex={1}>
          <SocialMediaButton onPress={() => onPressSocialMediaButton('email')}>
            <Icon.Email fill={colors.background} size={space[5]} />
          </SocialMediaButton>
          <Typography.H8>{'Email'}</Typography.H8>
        </Column>
        <Column gap={space[4]} alignItems="center" flex={1}>
          <SocialMediaButton onPress={() => onPressSocialMediaButton('call')}>
            <Icon.Call fill={colors.background} size={space[5]} />
          </SocialMediaButton>
          <Typography.H8>{'Call'}</Typography.H8>
        </Column>
      </Row>
    </ActionPanel>
  );
};

const SocialMediaButton = styled.TouchableOpacity(({ theme }) => ({
  height: theme.space[14],
  width: theme.space[14],
  borderRadius: theme.borderRadius.full,
  backgroundColor: theme.colors.primary,
  justifyContent: 'center',
  alignItems: 'center',
}));
