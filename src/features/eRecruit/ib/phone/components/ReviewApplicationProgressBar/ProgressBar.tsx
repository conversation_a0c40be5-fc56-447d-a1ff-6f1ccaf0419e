import { useEffect } from 'react';
import { shallow } from 'zustand/shallow';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import {
  ReviewApplicationProgressItem,
  ReviewApplicationProgressSubgroup,
} from 'features/eRecruit/types/progressBarTypes';
import ProgressBarPhone from './ProgressBar.phone';
import { useGenerateIBReviewApplicationRoutes } from 'features/eRecruit/hooks/ib/useGenerateIBReviewApplicationRoutes';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';

export interface InternalProgressBarProps {
  onChangeTopOffset?: (top: number) => void;
}

export default function ProgressBar(props: InternalProgressBarProps) {
  const {
    completedMap,
    setProgressBarState,
    groups,
    groupKey,
    subgroupKey,
    itemKey,
  } = useERecruitReviewApplicationProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
      groups: state.groups,
      groupKey: state.groupKey,
      subgroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  /**
   * Set current progress group key, expanded group key, item key to first element in groups
   */
  useEffect(() => {
    if (Array.isArray(groups) && groups.length > 0) {
      if (typeof groupKey !== 'undefined') {
        return;
      }
      const group = groups[0];
      const subgroup = group.items[0];
      setProgressBarState({
        groupKey: group.routeKey,
        expandedGroupKey: group.routeKey,
        subgroupKey: 'items' in subgroup ? subgroup.routeKey : undefined,
        itemKey:
          'items' in subgroup
            ? (subgroup as ReviewApplicationProgressSubgroup).items?.[0]
                ?.routeKey
            : subgroup.routeKey,
      });
    }
  }, [groups, groupKey, setProgressBarState]);

  /**
   * Update group, item group, item respectively based on their key
   */
  useEffect(() => {
    if (groups.length > 0) {
      const groupIndex = groups.findIndex(i => i.routeKey === groupKey);
      const activeGroup = groups[groupIndex];
      let activeSubgroup: ReviewApplicationProgressSubgroup | undefined =
        undefined;
      let activeItem: ReviewApplicationProgressItem | undefined = undefined;
      if (activeGroup && activeGroup.items) {
        const subgroupIndex = activeGroup.items.findIndex(
          i => i.routeKey === subgroupKey,
        );
        const itemIndex = activeGroup.items.findIndex(
          i => i.routeKey === itemKey,
        );
        const subgroup = activeGroup.items[
          subgroupIndex
        ] as ReviewApplicationProgressSubgroup;
        const item = activeGroup.items[
          itemIndex
        ] as ReviewApplicationProgressItem;
        if (subgroup?.items) {
          activeSubgroup = subgroup;
          activeItem = activeSubgroup?.items.find(i => i.routeKey === itemKey);
        } else {
          activeItem = item;
        }
      }
      setProgressBarState({
        group: activeGroup,
        groupIndex,
        subgroup: activeSubgroup as ReviewApplicationProgressSubgroup,
        item: activeItem as ReviewApplicationProgressItem,
      });
    }
  }, [groups, groupKey, subgroupKey, itemKey, setProgressBarState]);

  useGenerateIBReviewApplicationRoutes();

  return (
    <DeviceBasedRendering
      tablet={<></>}
      phone={<ProgressBarPhone {...props} />}
    />
  );
}
