import { memo } from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import ProgressItem from './ProgressItem';
import { shallow } from 'zustand/shallow';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import {
  ReviewApplicationProgressGroup,
  ReviewApplicationProgressSubgroup,
} from 'features/eRecruit/types/progressBarTypes';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';

const Container = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    width: '100%',
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    height: theme.sizes[10],
    flexDirection: 'row',
    alignItems: 'center',
  };
});

const Line = styled.View<{ color: string }>(({ theme, color }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    width: theme.sizes[1] / 2,
    height: '100%',
    backgroundColor: color,
    position: 'absolute',
    top: 0,
    left: isNarrowScreen ? 23 : 27,
  };
});

const TextSpace = styled.View(({ theme }) => ({
  width: theme.space[11],
}));

interface Props {
  index: number;
  subgroup: ReviewApplicationProgressSubgroup;
  groupIndex: number;
  group: ReviewApplicationProgressGroup;
}

export const ProgressSubgroup = memo(function ({
  index,
  subgroup,
  groupIndex,
  group,
}: Props) {
  const { colors } = useTheme();
  const { expandedGroupKey } = useERecruitReviewApplicationProgressBarStore(
    state => ({
      expandedGroupKey: state.expandedGroupKey,
    }),
    shallow,
  );

  const disabled =
    typeof subgroup?.disabled === 'boolean'
      ? subgroup.disabled
      : group.disabled;
  const color = disabled ? colors.palette.fwdGreyDark : colors.secondary;
  const expanded = group.routeKey === expandedGroupKey;
  const greyLine = colors.palette.fwdGrey[100];
  const expendLine = colors.primary;
  const lineColor = expanded && !disabled ? expendLine : greyLine;

  return (
    <>
      <Container>
        <Line color={lineColor} />
        <TextSpace />
        <Typography.LargeBody fontWeight={'normal'} color={color}>
          {subgroup.title}
        </Typography.LargeBody>
      </Container>
      {subgroup.items.map((item, itemIndex) => (
        <ProgressItem
          key={`item-${itemIndex}`}
          index={itemIndex}
          item={item}
          groupIndex={groupIndex}
          group={group}
          subgroup={subgroup}
          subgroupIndex={index}
        />
      ))}
    </>
  );
});
export default ProgressSubgroup;
