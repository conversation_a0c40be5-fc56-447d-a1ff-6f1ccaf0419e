import { TouchableOpacity } from 'react-native';
import React, { Fragment } from 'react';
import { useTheme } from '@emotion/react';
import { Row, Icon, Box, ExtraSmallLabel, Column } from 'cube-ui-components';
import RecruitmentProgressCard from 'features/eRecruit/my/tablet/components/RecruitmentProgress/RecruitmentProgressCard';
import { useTranslation } from 'react-i18next';
import { TableConfigItem, TimeSectionKeys } from 'types/eRecruit';
import Skeleton from 'components/Skeleton';

export default function SkeletonSetRecruitmentTarget({
  timeSection,
}: {
  timeSection: TimeSectionKeys;
}) {
  const { space, sizes, borderRadius, colors } = useTheme();
  const { t } = useTranslation('eRecruit');
  const skeletonArr = Array(3).fill(0);

  const timePeriod = ' ' + (timeSection ?? '-');

  const recruitmentTargetHeader: Array<TableConfigItem> = [
    {
      content: 'Candidates',
    },
    {
      content: 'Submitted',
    },
    {
      content: 'Approved',
    },
  ] as const;

  const eRecruitTargetStatConfig = [
    {
      content: '',
    },
    {
      content: '',
    },
    {
      content: '',
    },
    {
      content: '',
    },
  ];

  return (
    <Box padding={space[4]} gap={space[5]} borderRadius={borderRadius['large']}>
      <Row alignItems="center" justifyContent="space-between">
        <Row gap={space[1]} alignItems="center">
          <RecruitmentProgressCard.Title>
            {t('eRecruit.targetOfThis')} {timePeriod.trim()}
          </RecruitmentProgressCard.Title>
          <Icon.InfoCircle fill={colors.palette.fwdAlternativeOrange[100]} />
        </Row>
        <TouchableOpacity onPress={() => {}}>
          <Row gap={space[1]} alignItems="center">
            <Icon.Edit
              size={space[6]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
          </Row>
        </TouchableOpacity>
      </Row>
      <Column gap={space[2]} flex={1}>
        <Row gap={space[5]} paddingY={space[3]}>
          {recruitmentTargetHeader.map((item, index) => {
            return (
              <Box flex={1} alignItems="center" key={`${item}_${index}`}>
                <ExtraSmallLabel>{item.content}</ExtraSmallLabel>
              </Box>
            );
          })}
        </Row>

        <Row flex={1} alignItems="center" justifyContent="center">
          {skeletonArr.map((item, index) => (
            <Fragment key={`${item}_${index}`}>
              <Skeleton width={sizes[24]} height={sizes[4]} radius={4} />
              {index != skeletonArr.length - 1 && (
                <Icon.ChevronRight size={space[5]} />
              )}
            </Fragment>
          ))}
        </Row>
      </Column>
    </Box>
  );
}
