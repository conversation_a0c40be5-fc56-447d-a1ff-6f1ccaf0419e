import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  H7,
  LargeLabel,
  Row,
  TextField,
} from 'cube-ui-components';
import { DEFAULT_COUNTRY_PHONE_CODE_MY } from 'features/eRecruit/config';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import {
  initialApplicationData,
  personalDetailsSchema,
} from 'features/eRecruit/ib/validations/personalDetailsSchema';
import { PHONE_NUMBER_TYPE_REGEX } from 'features/eRecruit/util/inputMaskHelper';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { CountryCode } from 'types/optionList';
import { ContactDetailsInfo } from 'features/eRecruit/util/store/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';
import NewContactDetailsIcon from 'features/eRecruit/ib/tablet/asset/NewContactDetailsIcon';
import FormFooter from '../../utils/FormFooter';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

interface Props {
  onDismiss: () => void;
  value: ContactDetailsInfo;
  onDone: (data: ContactDetailsInfo) => void;
}

export default function ContactDetailsSection({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation('eRecruit');
  const { space, colors, sizes } = useTheme();

  const { countryCodeOptions } = useGetERecruitOptionListForAppForm();
  const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];

  const hookForm = useForm({
    defaultValues: {
      ...initialApplicationData.contact,
      contact: {
        countryCode: DEFAULT_COUNTRY_PHONE_CODE_MY,
        officeNumberCountryCode: DEFAULT_COUNTRY_PHONE_CODE_MY,
      },
    },
    resolver: yupResolver(personalDetailsSchema.pick(['contact'])),
    mode: 'onBlur',
  });

  const {
    control,
    trigger,
    watch,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints(true);
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  const submit = async () => {
    const isValid = await trigger();
    if (isValid) {
      onDone(
        getValues().contact &&
          ({ done: true, ...getValues().contact } as ContactDetailsInfo),
      );
      bottomSheetProps.bottomSheetRef.current?.close();
    } else {
      console.log('Validation failed', errors);
    }
  };

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={!isAllMandatoryFieldsFilled}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel={t('eRecruit.application.done')}
        />
      );
    },
    [submit],
  );

  const mandatoryFields = [
    'contact.countryCode',
    'contact.phoneNumber',
    'contact.email',
  ] as const;

  const isAllMandatoryFieldsFilled = watch(mandatoryFields).every(item =>
    Boolean(item),
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  useEffect(() => {
    if (value) {
      setValue(
        'contact.countryCode',
        value.countryCode ? value.countryCode : DEFAULT_COUNTRY_PHONE_CODE_MY,
      );
      setValue('contact.phoneNumber', value.phoneNumber);
      setValue('contact.email', value.email);
      setValue('contact.officePhoneNumber', value.officePhoneNumber);
      setValue(
        'contact.officeNumberCountryCode',
        value.officeNumberCountryCode
          ? value.officeNumberCountryCode
          : DEFAULT_COUNTRY_PHONE_CODE_MY,
      );
    }
  }, [value]);

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          onDismiss={onDismiss}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <View onLayout={handleContentLayout}>
            <Box px={space[isNarrowScreen ? 3 : 4]}>
              <Row alignItems="center" gap={space[1]}>
                <NewContactDetailsIcon width={sizes[10]} height={sizes[10]} />
                <H7 color={colors.palette.fwdOrange[100]} fontWeight="bold">
                  {t('eRecruit.application.personalDetails.contactDetails')}
                </H7>
              </Row>
            </Box>
            <BottomSheetKeyboardAwareScrollView
              keyboardDismissMode="on-drag"
              style={{ paddingHorizontal: space[isNarrowScreen ? 3 : 4] }}>
              <Box paddingBottom={space[4]}>
                <LargeLabel color="#333333" fontWeight="medium"></LargeLabel>
                <Column gap={space[4]}>
                  <Row style={{ flex: 1, gap: space[3] }}>
                    <Input
                      control={control}
                      as={AutocompletePopup<CountryCode, string>}
                      data={countryCodeOptions}
                      modalTitle={'Country code'}
                      name="contact.countryCode"
                      style={{ flex: 4 }}
                      label={t(`eRecruit.application.personalDetails.code`)}
                      getItemValue={item => item.value}
                      getItemLabel={item => item.label}
                      keyExtractor={item => item.value + item.label}
                      getDisplayedLabel={item => getCountryCodeValue(item)}
                      error={errors?.contact?.countryCode?.message}
                      disabled
                    />

                    <Input
                      control={control}
                      as={PhoneField}
                      name="contact.phoneNumber"
                      label={t(
                        `eRecruit.application.personalDetails.mobileNumber`,
                      )}
                      style={{ flex: 6 }}
                      onChangeText={value => {
                        setValue(
                          'contact.phoneNumber',
                          value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                        );
                      }}
                      keyboardType="numeric"
                      error={errors?.contact?.phoneNumber?.message}
                      size="large"
                    />
                  </Row>

                  <Input
                    control={control}
                    as={TextField}
                    name="contact.email"
                    style={{ flex: 1 }}
                    label={t(`eRecruit.application.personalDetails.email`)}
                    error={errors?.contact?.email?.message}
                  />

                  <Row style={{ flex: 1, gap: space[3] }}>
                    <Input
                      control={control}
                      as={AutocompletePopup<CountryCode, string>}
                      data={countryCodeOptions}
                      modalTitle={'Country code'}
                      name="contact.officeNumberCountryCode"
                      style={{ flex: 4 }}
                      label={t(`eRecruit.application.personalDetails.code`)}
                      getItemValue={item => item.value}
                      getItemLabel={item => item.label}
                      keyExtractor={item => item.value + item.label}
                      getDisplayedLabel={item => getCountryCodeValue(item)}
                      error={errors?.contact?.officeNumberCountryCode?.message}
                      disabled
                    />
                    <Input
                      control={control}
                      as={PhoneField}
                      name="contact.officePhoneNumber"
                      label={t(
                        `eRecruit.application.personalDetails.officePhoneOptional`,
                      )}
                      style={{ flex: 7 }}
                      onChangeText={value => {
                        setValue(
                          'contact.officePhoneNumber',
                          value.replace(PHONE_NUMBER_TYPE_REGEX, ''),
                        );
                      }}
                      keyboardType="numeric"
                      error={errors?.contact?.officePhoneNumber?.message}
                      size="large"
                    />
                  </Row>
                </Column>
                <BottomSheetFooterSpace />
              </Box>
            </BottomSheetKeyboardAwareScrollView>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
