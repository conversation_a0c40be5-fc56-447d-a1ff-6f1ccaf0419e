import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import Input from 'components/Input';
import Portal from 'components/Portal/Portal';
import {
  Box,
  Column,
  DatePicker,
  H7,
  I<PERSON>,
  Picker,
  Row,
  SmallLabel,
  TextField,
} from 'cube-ui-components';
import useGetERecruitOptionListForAppForm from 'features/eRecruit/hooks/useGetERecruitOptionListForAppForm';
import { useCallback, useEffect, useState } from 'react';
import {
  FieldArrayMethodProps,
  Resolver,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { DoneStatus } from 'features/eRecruit/util/store/ERecruitStore';
import { useERecruitApplicationSnapPoints } from 'features/eRecruit/hooks/ib/useERecruitApplicationSnapPoints';
import { useBottomSheet } from 'features/eAppV2/common/hooks/useBottomSheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { addDays, isAfter, subYears } from 'date-fns';
import NewSuitcaseIcon from 'features/eRecruit/ib/tablet/asset/NewSuitcaseIcon';
import {
  alphabetSpaceRegex,
  INVALID_INPUT,
  normalizedSpaceString,
  occupationInitialApplicationData,
  REQUIRED_INPUT,
} from 'features/eRecruit/ib/validations/occupationDetailsSchema';
import { TouchableOpacity } from 'react-native';
import SearchableDropdown from 'components/SearchableDropdown';
import { MapConfigToOptionItem } from 'features/eRecruit/my/utils/convertOptionListFunction';
import React from 'react';
import { LifeInsuranceExperience } from 'features/eRecruit/ib/type';
import FormFooter from '../../utils/FormFooter';
import { AddButton } from '../../utils/AddButton';
import { salaryValidation } from '../../utils/SalaryValidation';
import { array, date, object, string } from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';

interface Props {
  onDismiss: () => void;
  value: (LifeInsuranceExperience & DoneStatus)[];
  onDone: (data: (LifeInsuranceExperience & DoneStatus)[]) => void;
}

export default function LifeOrGeneralExperienceSection({
  onDismiss,
  value,
  onDone,
}: Props) {
  const { t } = useTranslation(['eRecruit', 'common']);
  const { space, colors, sizes } = useTheme();

  const lifeInsuranceExperienceListSchema = object().shape({
    lifeInsuranceExperienceList: array().of(
      object().shape({
        intermediaryType: string().required(REQUIRED_INPUT),
        companyName: normalizedSpaceString()
          .required(REQUIRED_INPUT)
          .validateCompanyName(INVALID_INPUT),
        rank: normalizedSpaceString()
          .required(REQUIRED_INPUT)
          .matches(alphabetSpaceRegex, t('common:form.invalidFormat')),
        basicSalary: string().required(REQUIRED_INPUT),
        dateApplied: date().required(REQUIRED_INPUT),
        dateTermination: date().required(REQUIRED_INPUT),
      }),
    ),
  });

  const hookForm = useForm<{
    lifeInsuranceExperienceList: {
      intermediaryType: string;
      companyName: string;
      rank: string;
      basicSalary: string;
      dateApplied: Date;
      dateTermination: Date;
    }[];
  }>({
    defaultValues: {
      ...occupationInitialApplicationData,
    },
    resolver: yupResolver(lifeInsuranceExperienceListSchema) as Resolver<{
      lifeInsuranceExperienceList: {
        intermediaryType: string;
        companyName: string;
        rank: string;
        basicSalary: string;
        dateApplied: Date;
        dateTermination: Date;
      }[];
    }>,
    mode: 'onBlur',
  });

  const {
    control,
    watch,
    trigger,
    formState: { errors },
    getValues,
    setValue,
  } = hookForm;

  const {
    fields: lifeInsExpFields,
    append: appendLifeInsuranceExperience,
    remove: removeLifeInsuranceExperience,
  } = useFieldArray({
    name: 'lifeInsuranceExperienceList',
    control,
  });

  const maximumLifeGeneralInsuranceExp = 7;

  const isAddingLifeExpDisabled =
    lifeInsExpFields?.length >= maximumLifeGeneralInsuranceExp;

  const bottomSheetProps = useBottomSheet();
  const snapPoints = useERecruitApplicationSnapPoints();

  const { intermediateTypeOptions, lifeGeneralTakafulCompaniesList } =
    useGetERecruitOptionListForAppForm();

  const maxDate = subYears(new Date(), -100);

  const appendListFocusOption: FieldArrayMethodProps = { shouldFocus: false };

  const submit = async () => {
    const isValid = await trigger();
    if (isValid) {
      onDone(
        getValues()?.lifeInsuranceExperienceList?.map(
          lifeInsuranceExperience => ({
            ...lifeInsuranceExperience,
            done: true,
          }),
        ) as (LifeInsuranceExperience & DoneStatus)[],
      );
      bottomSheetProps.bottomSheetRef.current?.close();
      setTimeout(() => {
        onDismiss();
      }, 100);
    } else {
      console.log('Validation failed', errors);
    }
  };

  useEffect(() => {
    if (value && value.length > 0 && value[0].companyName != '') {
      removeLifeInsuranceExperience(0);
      value.forEach(item => {
        appendLifeInsuranceExperience({
          intermediaryType: item.intermediaryType,
          companyName: item.companyName,
          rank: item.rank,
          basicSalary: item.basicSalary,
          dateApplied: (item.dateApplied ?? undefined) as Date,
          dateTermination: (item.dateTermination ?? undefined) as Date,
        });
      });
    }
  }, [value]);

  const lifeInsuranceExperienceList = watch('lifeInsuranceExperienceList');

  const someFieldsIsFilled = lifeInsuranceExperienceList?.some(
    lifeInsuranceExperience =>
      Object.values(lifeInsuranceExperience).some(field => Boolean(field)),
  );

  const everyFieldsIsCompleted = lifeInsuranceExperienceList?.every(
    lifeInsuranceExperience =>
      Object.values(lifeInsuranceExperience).every(field => Boolean(field)),
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FormFooter
          {...props}
          primaryDisabled={someFieldsIsFilled && !everyFieldsIsCompleted}
          onPrimaryPress={submit}
          primaryLoading={false}
          primaryLabel={t('eRecruit:eRecruit.application.done')}
        />
      );
    },
    [submit],
  );

  const [showErrorField, setShowErrorField] = useState<string[]>([]);

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          index={1}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          {...bottomSheetProps}
          footerComponent={renderFooter}
          style={{ padding: 0 }}>
          <Box px={space[4]}>
            <Row alignItems="center" gap={space[1]}>
              <NewSuitcaseIcon width={sizes[10]} height={sizes[10]} />
              <H7 color={colors.palette.fwdOrange[100]} fontWeight="bold">
                {`Life / General Insurance Experience`}
              </H7>
            </Row>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            bottomOffset={space[10]}
            keyboardDismissMode="on-drag"
            style={{
              paddingHorizontal: space[4],
              flex: 1,
            }}>
            <Box paddingY={space[4]}>
              <Column gap={space[6]}>
                {lifeInsuranceExperienceList?.map((field, idx) => {
                  const dateApplied = watch(
                    `lifeInsuranceExperienceList.${idx}.dateApplied`,
                  );

                  const dateTermination = watch(
                    `lifeInsuranceExperienceList.${idx}.dateTermination`,
                  );

                  if (isAfter(dateApplied as Date, addDays(new Date(), -1))) {
                    setValue(
                      `lifeInsuranceExperienceList.${idx}.dateApplied`,
                      addDays(new Date(), -1),
                    );
                  }

                  return (
                    <React.Fragment key={`${field}_${idx}`}>
                      <Row alignItems="center" justifyContent="space-between">
                        <H7 fontWeight="bold" key={idx}>
                          {`${(idx ?? 0) + 1}. `}
                          {t(
                            `eRecruit:eRecruit.application.occupationDetails.company`,
                          )}
                        </H7>
                        {lifeInsExpFields.length === 1 && idx === 0 ? (
                          <TouchableOpacity disabled>
                            <Icon.Delete fill={colors.palette.fwdGreyDark} />
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            onPress={() => {
                              if (lifeInsExpFields?.length === 1) {
                                console.log('last item should not be deleted');
                                return;
                              }
                              removeLifeInsuranceExperience(idx);
                            }}>
                            <Icon.Delete
                              fill={colors.palette.fwdDarkGreen[100]}
                            />
                          </TouchableOpacity>
                        )}
                      </Row>
                      <Column gap={space[2]}>
                        <SmallLabel color={colors.palette.fwdGreyDarker}>
                          {t(
                            `eRecruit:eRecruit.application.occupationDetails.typeOfIntermediary`,
                          )}
                        </SmallLabel>
                        <Row>
                          <Picker
                            type="chip"
                            size="small"
                            containerStyle={{
                              flex: 1,
                              flexWrap: 'wrap',
                              rowGap: space[2],
                            }}
                            items={intermediateTypeOptions}
                            value={watch(
                              `lifeInsuranceExperienceList.${idx}.intermediaryType`,
                            )}
                            onChange={value => {
                              setValue(
                                `lifeInsuranceExperienceList.${idx}.intermediaryType`,
                                value,
                              );
                            }}
                          />
                        </Row>
                      </Column>
                      <Input
                        searchable
                        shouldUnregister
                        control={control}
                        as={SearchableDropdown<MapConfigToOptionItem, string>}
                        data={lifeGeneralTakafulCompaniesList}
                        getItemLabel={item => item.label}
                        getItemValue={item => item.value}
                        name={`lifeInsuranceExperienceList.${idx}.companyName`}
                        label={t(
                          `eRecruit:eRecruit.application.occupationDetails.companyName`,
                        )}
                        style={{ flex: 1 }}
                      />
                      <Input
                        shouldUnregister
                        control={control}
                        as={TextField}
                        name={`lifeInsuranceExperienceList.${idx}.rank`}
                        label={t(
                          `eRecruit:eRecruit.application.occupationDetails.rank`,
                        )}
                        style={{ flex: 1 }}
                        onChangeText={value => {
                          setValue(
                            `lifeInsuranceExperienceList.${idx}.rank`,
                            value?.replace(/[^A-Za-z\s]/g, '') ?? '',
                          );
                        }}
                      />
                      <Input
                        shouldUnregister
                        control={control}
                        as={TextField}
                        keyboardType="numeric"
                        name={`lifeInsuranceExperienceList.${idx}.basicSalary`}
                        label={t(
                          `eRecruit:eRecruit.application.occupationDetails.basicSalary(RM)`,
                        )}
                        onChangeText={value => {
                          setValue(
                            `lifeInsuranceExperienceList.${idx}.basicSalary`,
                            salaryValidation(value),
                          );
                        }}
                        style={{ flex: 1 }}
                      />
                      <Input
                        shouldUnregister
                        control={control}
                        as={DatePicker}
                        name={`lifeInsuranceExperienceList.${idx}.dateApplied`}
                        maxDate={
                          dateTermination
                            ? isAfter(dateTermination as Date, new Date())
                              ? addDays(new Date(), -1)
                              : addDays(dateTermination as Date, -1)
                            : addDays(new Date(), -1)
                        }
                        value={dateApplied}
                        style={{
                          flex: 1,
                        }}
                        label={t(
                          'eRecruit:eRecruit.application.occupationDetails.dateAppoint',
                        )}
                        hint={t('eRecruit:eRecruit.application.dateHint')}
                        formatDate={value =>
                          value ? dateFormatUtil(value) : ''
                        }
                        error={
                          !dateApplied
                            ? errors.lifeInsuranceExperienceList?.[idx]
                                ?.dateApplied?.message
                            : ''
                        }
                      />
                      <Input
                        shouldUnregister
                        control={control}
                        as={DatePicker}
                        name={`lifeInsuranceExperienceList.${idx}.dateTermination`}
                        minDate={
                          dateApplied
                            ? addDays(dateApplied as Date, 1)
                            : new Date()
                        }
                        maxDate={maxDate}
                        defaultValue={addDays(dateApplied ?? new Date(), 1)}
                        value={dateTermination}
                        style={{
                          flex: 1,
                        }}
                        label={t(
                          'eRecruit:eRecruit.application.occupationDetails.dateTermination',
                        )}
                        hint={t('eRecruit:eRecruit.application.dateHint')}
                        formatDate={value =>
                          value ? dateFormatUtil(value) : ''
                        }
                        error={
                          !dateTermination
                            ? errors.lifeInsuranceExperienceList?.[idx]
                                ?.dateTermination?.message
                            : ''
                        }
                      />
                    </React.Fragment>
                  );
                })}

                <AddButton
                  onPress={() =>
                    appendLifeInsuranceExperience(
                      {
                        intermediaryType: '',
                        companyName: '',
                        rank: '',
                        basicSalary: '',
                        dateApplied: undefined as unknown as Date,
                        dateTermination: undefined as unknown as Date,
                      },
                      appendListFocusOption,
                    )
                  }
                  isDisabled={isAddingLifeExpDisabled}
                />
              </Column>
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
