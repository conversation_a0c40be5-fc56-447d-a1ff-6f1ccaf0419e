import { ApplicationFormResponds } from 'types/eRecruit';
import { cloneDeep } from 'utils/helper/objectUtil';

export function parsePCEFunction({
  recruitmentCache,
}: {
  recruitmentCache?: ApplicationFormResponds;
}) {
  if (!recruitmentCache) {
    return;
  }
  const recruitmentCacheClone = cloneDeep(recruitmentCache);

  const certifications = cloneDeep(
    recruitmentCache?.qualifications?.certifications,
  );

  const hasPCE = Boolean(
    certifications.find(item => item.type === 'INSURANCE_PCE'),
  );

  if (!hasPCE) {
    recruitmentCacheClone.qualifications.certifications = [
      ...certifications,
      {
        type: 'INSURANCE_PCE',
        issueDate: null,
        yearOfPassing: null,
        otherDesc: null,
      },
    ];
  }
  return recruitmentCacheClone;
}
