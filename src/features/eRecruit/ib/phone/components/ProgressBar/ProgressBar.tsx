import { useSaveApplicationProgress } from 'hooks/useApplicationProgress';
import { useEffect } from 'react';
import { shallow } from 'zustand/shallow';
import { ApplicationProgress } from 'api/caseApi';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useGenerateERecruitRoutes } from 'features/eRecruit/hooks/useGenerateERecruitRoutes';
import { useERecruitProgressBarStore } from 'features/eRecruit/util/store/ERecruitProgressBarStore';
import {
  ProgressItem,
  ProgressSubgroup,
} from 'features/eRecruit/types/progressBarTypes';
import ProgressBarPhone from './ProgressBar.phone';

export interface InternalProgressBarProps {
  onChangeTopOffset?: (top: number) => void;
}

export default function ProgressBar(props: InternalProgressBarProps) {
  const { saveApplicationProgress } = useSaveApplicationProgress();
  const {
    completedMap,
    setProgressBarState,
    groups,
    groupKey,
    subgroupKey,
    itemKey,
  } = useERecruitProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
      groups: state.groups,
      groupKey: state.groupKey,
      subgroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  useEffect(() => {
    const saveApplicationProgressAction = async () => {
      const data = Object.keys(completedMap).reduce(
        (acc: { [key: string]: ApplicationProgress }, key: string) => {
          if (typeof completedMap[key] === 'boolean') {
            acc[key] = completedMap[key]
              ? ApplicationProgress.COMPLETED
              : ApplicationProgress.IN_PROGRESS;
          }
          return acc;
        },
        {},
      );
      await saveApplicationProgress(data);
    };
    saveApplicationProgressAction();
  }, [completedMap, saveApplicationProgress]);

  /**
   * Set current progress group key, expanded group key, item key to first element in groups
   */
  useEffect(() => {
    if (Array.isArray(groups) && groups.length > 0) {
      if (typeof groupKey !== 'undefined') {
        return;
      }
      const group = groups[0];
      const subgroup = group.items[0];
      setProgressBarState({
        groupKey: group.routeKey,
        expandedGroupKey: group.routeKey,
        subgroupKey: 'items' in subgroup ? subgroup.routeKey : undefined,
        itemKey:
          'items' in subgroup
            ? (subgroup as ProgressSubgroup).items?.[0]?.routeKey
            : subgroup.routeKey,
      });
    }
  }, [groups, groupKey, setProgressBarState]);

  /**
   * Update group, item group, item respectively based on their key
   */
  useEffect(() => {
    if (groups.length > 0) {
      const groupIndex = groups.findIndex(i => i.routeKey === groupKey);
      const activeGroup = groups[groupIndex];
      let activeSubgroup: ProgressSubgroup | undefined = undefined;
      let activeItem: ProgressItem | undefined = undefined;
      if (activeGroup && activeGroup.items) {
        const subgroupIndex = activeGroup.items.findIndex(
          i => i.routeKey === subgroupKey,
        );
        const itemIndex = activeGroup.items.findIndex(
          i => i.routeKey === itemKey,
        );
        const subgroup = activeGroup.items[subgroupIndex] as ProgressSubgroup;
        const item = activeGroup.items[itemIndex] as ProgressItem;
        if (subgroup?.items) {
          activeSubgroup = subgroup;
          activeItem = activeSubgroup?.items.find(i => i.routeKey === itemKey);
        } else {
          activeItem = item;
        }
      }
      setProgressBarState({
        group: activeGroup,
        groupIndex,
        subgroup: activeSubgroup,
        item: activeItem,
      });
    }
  }, [groups, groupKey, subgroupKey, itemKey, setProgressBarState]);

  useGenerateERecruitRoutes();

  return (
    <DeviceBasedRendering
      tablet={<></>}
      phone={<ProgressBarPhone {...props} />}
    />
  );
}
