import { memo } from 'react';
import { View } from 'react-native';
import Animated from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import { useERecruitProgressBarStore } from 'features/eRecruit/util/store/ERecruitProgressBarStore';
import ProgressBarHeader from './ProgressBarHeader';
import ProgressBreadcrumb from './ProgressBreadcrumb';
import ProgressModal from './ProgressModal';

export const ProgressBarPhone = memo(function ProgressBar() {
  const { t } = useTranslation(['eRecruit']);
  const { visible, hideModal, progressBarZIndex } = useERecruitProgressBarStore(
    state => ({
      visible: state.visible,
      hideModal: state.hideModal,
      progressBarZIndex: state.progressBarZIndex,
    }),
    shallow,
  );

  return (
    <Animated.View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: progressBarZIndex,
      }}>
      <ProgressBarHeader title={'Application'} />
      <ProgressBreadcrumb />
      <ProgressModal visible={visible} onClose={hideModal} />
      <View style={{ height: 1 }} />
    </Animated.View>
  );
});
export default ProgressBarPhone;
