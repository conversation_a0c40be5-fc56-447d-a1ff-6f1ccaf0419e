import { memo, useCallback, useMemo } from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import ProgressSubgroup from './ProgressSubgroup';
import ProgressItem from './ProgressItem';
import { TouchableHighlight } from 'react-native';

import { shallow } from 'zustand/shallow';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Animated, {
  FadeIn,
  FadeOut,
  LinearTransition,
} from 'react-native-reanimated';
import {
  ProgressGroup as ProgressGroupType,
  ProgressItem as ProgressItemType,
  ProgressSubgroup as ProgressSubgroupType,
  RouteItemKey,
} from 'features/eRecruit/types/progressBarTypes';
import {
  ERecruitProgressBarState,
  useERecruitProgressBarStore,
} from 'features/eRecruit/util/store/ERecruitProgressBarStore';

const Container = styled.View(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    width: '100%',
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    height: theme.sizes[10],
    flexDirection: 'row',
    alignItems: 'center',
  };
});

const StepContainer = styled.View<{ color: string; bgColor: string }>(
  ({ theme, color, bgColor }) => ({
    borderWidth: 1,
    borderColor: color,
    alignItems: 'center',
    justifyContent: 'center',
    width: theme.sizes[6],
    height: theme.sizes[6],
    borderRadius: theme.sizes[3],
    backgroundColor: bgColor,
  }),
);

const Line = styled.View<{ color: string; position: 'top' | 'bottom' }>(
  ({ theme, color, position }) => {
    const { isNarrowScreen } = useWindowAdaptationHelpers();
    return {
      width: theme.sizes[1] / 2,
      height: theme.sizes[2],
      backgroundColor: color,
      position: 'absolute',
      top: position === 'top' ? 0 : undefined,
      bottom: position === 'bottom' ? 0 : undefined,
      left: isNarrowScreen ? 23 : 27,
    };
  },
);

const TextSpace = styled.View(({ theme }) => ({
  width: theme.space[3],
}));

interface Props {
  index: number;
  group: ProgressGroupType;
}

export const ProgressGroup = memo(function ProgressGroup({
  index,
  group,
}: Props) {
  const { colors } = useTheme();
  const { groupKey, groups, expandedGroupKey, setProgressBarState, hideModal } =
    useERecruitProgressBarStore(
      state => ({
        groupKey: state.groupKey,
        groups: state.groups,
        expandedGroupKey: state.expandedGroupKey,
        setProgressBarState: state.setProgressBarState,
        hideModal: state.hideModal,
      }),
      shallow,
    );
  const step = index + 1;
  const active = group.routeKey === groupKey;
  const expanded = group.routeKey === expandedGroupKey;
  const color = group.disabled
    ? colors.palette.fwdGreyDark
    : expanded
    ? colors.primary
    : colors.secondary;
  const bgColor = expanded ? colors.primary : 'transparent';
  const isLastGroup = step === groups.length;

  const showTopLine = step > 1;

  const showBottomLine = !isLastGroup;

  const onPress = useCallback(() => {
    const state: Partial<ERecruitProgressBarState> = {
      expandedGroupKey: group.routeKey,
    };
    if (!group.items || group.items.length === 0) {
      state.groupKey = group.routeKey;
      state.subgroupKey = undefined;
      state.itemKey = undefined;
      hideModal();
    } else if (
      group.items.length > 0 &&
      group.items.filter(i => (i as ProgressItemType).hidden).length ===
        group.items.length
    ) {
      state.groupKey = group.routeKey;
      state.subgroupKey = undefined;
      state.itemKey = group.items[0].routeKey as RouteItemKey;
      hideModal();
    }

    setProgressBarState(state);
  }, [setProgressBarState, group, hideModal]);

  const [topLineColor, bottomLineColor, greyLine, expandedLine] =
    useMemo(() => {
      const greyLine = colors.palette.fwdGrey[100];
      const expandedLine = colors.primary;

      const prevGroup = groups[index - 1];
      const prevSubgroup = findLastSubgroup(prevGroup);
      const prevItem = findLastItemInGroup(prevGroup);

      const prevItemDisabled =
        typeof prevItem?.disabled === 'boolean'
          ? prevItem.disabled
          : typeof prevSubgroup?.disabled === 'boolean'
          ? prevSubgroup.disabled
          : prevGroup?.disabled;

      const topLineColor =
        prevGroup &&
        prevGroup.routeKey === expandedGroupKey &&
        prevGroup.items.length > 0 &&
        !prevItemDisabled
          ? expandedLine
          : greyLine;

      const bottomLineColor =
        expanded && group.items.length > 0 ? expandedLine : greyLine;

      return [topLineColor, bottomLineColor, greyLine, expandedLine];
    }, [colors, groups, expanded, expandedGroupKey, group, index]);

  return (
    <Animated.View layout={LinearTransition.duration(200)}>
      <TouchableHighlight
        disabled={group.disabled}
        onPress={onPress}
        underlayColor={colors.palette.fwdGrey[50]}>
        <Container>
          {showTopLine && <Line position="top" color={topLineColor} />}
          {showBottomLine && <Line position="bottom" color={greyLine} />}
          {showBottomLine && expanded && (
            <Line position="bottom" color={bottomLineColor} />
          )}
          <StepContainer color={color} bgColor={bgColor}>
            <Typography.Label
              fontWeight="bold"
              color={expanded ? colors.onPrimary : color}>
              {step}
            </Typography.Label>
          </StepContainer>
          <TextSpace />
          <Typography.LargeLabel
            fontWeight={expanded ? 'bold' : 'normal'}
            color={color}>
            {group.title}
          </Typography.LargeLabel>
        </Container>
      </TouchableHighlight>
      {expanded && (
        <Animated.View
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(200)}>
          {group.items.map((item, i) => {
            if ((item as ProgressSubgroupType)?.items) {
              return (
                <ProgressSubgroup
                  key={'item-group' + i}
                  index={i}
                  subgroup={item as ProgressSubgroupType}
                  groupIndex={index}
                  group={group}
                />
              );
            }
            return (
              <ProgressItem
                key={`item-${i}`}
                index={i}
                item={item as ProgressItemType}
                groupIndex={index}
                group={group}
              />
            );
          })}
        </Animated.View>
      )}
    </Animated.View>
  );
});
export default ProgressGroup;

const findLastItemInGroup = (group: ProgressGroupType) => {
  if (group && group.items) {
    let item = group.items[group.items.length - 1];
    const subgroup = item as ProgressSubgroupType;
    if (subgroup?.items) {
      item = subgroup.items[subgroup.items.length - 1];
    }
    return item;
  }
  return undefined;
};

const findLastSubgroup = (group: ProgressGroupType) => {
  if (group && group.items) {
    const item = group.items[group.items.length - 1];
    const subgroup = item as ProgressSubgroupType;
    if (subgroup?.items) {
      return subgroup;
    }
  }
  return undefined;
};
