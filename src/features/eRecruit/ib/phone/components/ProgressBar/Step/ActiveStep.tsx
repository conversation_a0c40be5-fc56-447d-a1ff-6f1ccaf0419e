import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import { ProgressGroup } from 'features/eRecruit/types/progressBarTypes';
import { memo, useEffect, useState } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

const Container = styled(Animated.View)(({ theme }) => ({
  height: theme.sizes[6],
  borderRadius: theme.sizes[3],
  overflow: 'hidden',
  flex: 1,
  justifyContent: 'center',
  backgroundColor: theme.colors.primaryVariant,
}));

const Text = styled(Typography.Text)(({ theme }) => ({
  position: 'absolute',
  paddingHorizontal: theme.space[2],
  fontSize: 12,
  flex: 1,
}));

const ProgressBar = styled(Animated.View)(({ theme }) => ({
  borderRadius: theme.sizes[3],
  backgroundColor: theme.colors.primary,
  width: '100%',
  top: 0,
  bottom: 0,
  left: '-100%',
  position: 'absolute',
}));

interface Props {
  step: number;
  subStep: number;
  subTotalStep: number;
  showSubProgress?: boolean;
  title: string;
  group?: ProgressGroup;
  itemKey?: string;
}

export const ActiveStep = memo(function ActiveStep({
  step,
  subStep,
  subTotalStep,
  title,
  showSubProgress,
  group,
  itemKey,
}: Props) {
  const theme = useTheme();
  const { colors, animation } = theme;
  const [width, setWidth] = useState(0);
  const progressXTranslation = useSharedValue(0);
  const subgroup = group?.items?.[subStep - 1];
  const stepHaveChild = subgroup && 'items' in subgroup ? subgroup.items : [];
  const stepChildList = stepHaveChild || group?.items || [];

  const stepChildPos =
    (stepChildList?.findIndex(item => item?.routeKey === itemKey) as number) +
    1;

  const countChildStepList = stepChildList?.length as number;

  const progressCalculate =
    stepChildPos === countChildStepList || !stepHaveChild
      ? (width / subTotalStep) * subStep
      : width / subTotalStep +
        (width / subTotalStep / countChildStepList) * stepChildPos;

  useEffect(() => {
    progressXTranslation.value = withTiming(progressCalculate, {
      duration: animation.duration,
    });
  }, [
    subStep,
    subTotalStep,
    animation.duration,
    width,
    progressCalculate,
    progressXTranslation,
  ]);

  const progressStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateX: progressXTranslation.value,
        },
      ],
    }),
    [progressXTranslation.value],
  );
  return (
    <Container onLayout={e => setWidth(e.nativeEvent.layout.width)}>
      <ProgressBar style={progressStyle} />
      <Text
        numberOfLines={1}
        fontWeight="bold"
        color={colors.onPrimary}>{`${step}. ${title}${
        showSubProgress ? ` (${subStep}/${subTotalStep})` : ''
      }`}</Text>
    </Container>
  );
});
export default ActiveStep;
