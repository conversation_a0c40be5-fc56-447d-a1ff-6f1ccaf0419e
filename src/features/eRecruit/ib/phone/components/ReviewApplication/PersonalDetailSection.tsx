import styled from '@emotion/native';
import { Column, Box, Icon, Typography, Row } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React from 'react';
import { ScrollView } from 'react-native';
import { ReviewAgentApplicationResponds } from 'types/eRecruit';
import { OccupationDetailsReview } from '../ApplicationForm/ReviewInformationSection/OccupationDetailsReview';
import { OtherDetailsReview } from '../ApplicationForm/ReviewInformationSection/OtherDetailsReview';
import { PersonalDetailsReview } from '../ApplicationForm/ReviewInformationSection/PersonalDetailsReview';
import { useTheme } from '@emotion/react';
import { useGetReviewAgentsApplicationDetails } from 'features/eRecruit/hooks/useGetReviewAgentsApplicationDetails';
import { useTranslation } from 'react-i18next';
import ApprovalTracker from './ApprovalTracker';
import { useRoute } from '@react-navigation/native';
import BinocularsIcon from 'features/eRecruit/ib/tablet/asset/Binoculars';
import AdvancedCollapsibleCard from './AdvancedCollapsibleCard';
import { useGetAgentInfo } from 'hooks/useGetAgentInfo';
import ERecruitFooter from '../utils/ERecruitFooter';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';
import { shallow } from 'zustand/shallow';
export default function PersonalDetailSection() {
  const { space, colors, borderRadius } = useTheme();
  const route = useRoute();

  const { applicationId } = route.params as ReviewAgentApplicationResponds;
  const { data, isLoading } = useGetReviewAgentsApplicationDetails(
    applicationId as number,
  );

  const applicationData: ReviewAgentApplicationResponds = data;
  const { setProgressBarState } = useERecruitReviewApplicationProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
      groups: state.groups,
      groupKey: state.groupKey,
      subgroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  const { t } = useTranslation('eRecruit');
  const { data: agentInfo } = useGetAgentInfo(
    data?.approvalComments?.[0]?.approverAgentCode,
  );

  const CollapsedDescription = () => {
    if (data?.approvalComments?.length > 0)
      return (
        <Row py={10} alignContent="center" gap={space[2]}>
          <Icon.TickCircle fill={colors.palette.alertGreen} size={18} />
          <Column flex={1}>
            <Typography.H7 fontWeight="bold" color={colors.palette.alertGreen}>
              {agentInfo?.agentName + ' approved'}
            </Typography.H7>
          </Column>
        </Row>
      );
    return <></>;
  };
  return (
    <>
      <ScrollView>
        <Container>
          <Box paddingY={space[4]}>
            {data?.approvalComments?.length > 0 && (
              <Box
                backgroundColor={colors.background}
                borderRadius={borderRadius.large}
                marginBottom={space[4]}>
                <AdvancedCollapsibleCard
                  title={t('Approval tracker')}
                  TitleIcon={BinocularsIcon}
                  isDefaultCollapse={true}
                  description={<CollapsedDescription />}>
                  <ApprovalTracker
                    data={data?.approvalComments}
                    isLoading={isLoading}
                  />
                </AdvancedCollapsibleCard>
              </Box>
            )}
            <Column gap={space[4]}>
              <PersonalDetailsReview data={applicationData} />
              {applicationData?.workingExperiences.length > 0 && (
                <OccupationDetailsReview data={applicationData} />
              )}
              <OtherDetailsReview data={applicationData} />
            </Column>
          </Box>
        </Container>
      </ScrollView>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={false}
        onPrimaryPress={() =>
          setProgressBarState({
            groupKey: 'documentUpload',
            subgroupKey: 'documentUpload',
            itemKey: 'documentUpload',
          })
        }
        primarySubLabel="Review documents"
      />
    </>
  );
}
const Container = styled.View(({ theme: { space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
  };
});
