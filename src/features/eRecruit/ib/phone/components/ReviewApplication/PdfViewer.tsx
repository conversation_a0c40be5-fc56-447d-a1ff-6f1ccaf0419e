import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { BottomSheetDefaultBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import { useTheme } from '@emotion/react';
import { Column, H6 } from 'cube-ui-components';
import Pdf from 'react-native-pdf';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useBoundStore from 'hooks/useBoundStore';
export default function PdfViewer({
  title,
  setShowViewer,
  resetPdf,
  uri,
}: {
  title: string;
  setShowViewer: Dispatch<SetStateAction<boolean>>;
  resetPdf: () => void;
  uri: string;
}) {
  const headers = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Authorization:
      'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
  };
  const { space, sizes, colors, borderRadius } = useTheme();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['90%'], []);
  const { bottom: bottomInset } = useSafeAreaInsets();
  const renderBackdrop = useCallback(
    (props: BottomSheetDefaultBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        pressBehavior={'close'}
        onPress={() => onCancel()}
      />
    ),
    [],
  );
  const onCancel = () => {
    setShowViewer(false);
    resetPdf();
  };
  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enableOverDrag={false}>
        <Column
          paddingTop={space[4]}
          paddingX={space[4]}
          flex={1}
          gap={space[4]}
          paddingBottom={bottomInset}>
          <H6 fontWeight="bold">{title}</H6>
          <Column flex={1} borderRadius={borderRadius.medium} overflow="hidden">
            <Pdf
              scale={0.9}
              minScale={0.9}
              maxScale={5.0}
              source={{
                uri,
                headers,
              }}
              style={{ flex: 1, backgroundColor: '#525558' }}
              onError={e => {
                console.error('Fail to load the PDF in PDF Viewer', e);
              }}
              trustAllCerts={false}
            />
          </Column>
        </Column>
      </BottomSheet>
    </Portal>
  );
}
