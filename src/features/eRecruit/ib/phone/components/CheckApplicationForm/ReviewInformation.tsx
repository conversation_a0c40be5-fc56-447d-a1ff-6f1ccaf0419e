import { useTheme } from '@emotion/react';
import { Box, Column } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import { ApplicationFormResponds } from 'types/eRecruit';
import { useERecruitStore } from 'features/eRecruit/util/store/ERecruitStore';
import { shallow } from 'zustand/shallow';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamListMap } from 'types';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { PersonalDetailsReview } from '../ApplicationForm/ReviewInformationSection/PersonalDetailsReview';
import { OtherDetailsReview } from '../ApplicationForm/ReviewInformationSection/OtherDetailsReview';
import { ScrollView } from 'react-native';
import styled from '@emotion/native';
import ERecruitFooter from '../utils/ERecruitFooter';
import { useERecruitCheckApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitCheckApplicationProgressBarStore';
import { OccupationDetailsReview } from '../ApplicationForm/ReviewInformationSection/OccupationDetailsReview';

export default function ReviewInformation() {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();

  const { completedMap, setProgressBarState } =
    useERecruitCheckApplicationProgressBarStore(
      state => ({
        completedMap: state.completedMap,
        setProgressBarState: state.setProgressBarState,
      }),
      shallow,
    );

  const { registrationStagingId } = useERecruitStore(
    state => ({
      registrationStagingId: state.registrationStagingId,
    }),
    shallow,
  );

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();
  const routeRegistrationStagingId = route.params?.registrationStagingId ?? '';
  const registrationStagingIdParam = routeRegistrationStagingId;

  const { data: recruitmentCache, isInitialLoading } = useGetApplicationData(
    registrationStagingId
      ? `${registrationStagingId}`
      : registrationStagingIdParam,
  );

  const onSubmit = async () => {
    setProgressBarState({
      groupKey: 'documentUpload',
      subgroupKey: 'documentUpload',
      itemKey: 'documentUpload',
    });
  };

  const work = recruitmentCache?.workingExperiences ?? [];

  return (
    <Column flex={1}>
      <ScrollView>
        <Container>
          <Box paddingY={space[4]}>
            <Column gap={space[4]}>
              <PersonalDetailsReview
                data={recruitmentCache as ApplicationFormResponds}
              />
              {work.length > 0 && (
                <OccupationDetailsReview
                  data={recruitmentCache as ApplicationFormResponds}
                />
              )}
              <OtherDetailsReview
                data={recruitmentCache as ApplicationFormResponds}
                showAgencyType={false}
                showDeclarationOfConflictOfInterest={false}
              />
            </Column>
          </Box>
        </Container>
      </ScrollView>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={false}
        onPrimaryPress={onSubmit}
        primarySubLabel={'Documents'}
      />
    </Column>
  );
}

const Container = styled.View(({ theme: { space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
  };
});
