import { memo, useCallback, useRef } from 'react';
import Modal from 'react-native-modal';
import {
  LayoutChangeEvent,
  StyleSheet,
  View,
  useWindowDimensions,
} from 'react-native';
import styled from '@emotion/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import ProgressList from './ProgressList';
import ModalHeader from './ModalHeader';
import Handler from './Handler';

const Container = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderBottomLeftRadius: theme.sizes[4],
  borderBottomRightRadius: theme.sizes[4],
  overflow: 'hidden',
  paddingBottom: 29,
}));

interface Props {
  visible: boolean;
  onClose: () => void;
}

export const ProgressModal = memo(function ProgressModal({
  visible,
  onClose,
}: Props) {
  const theme = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { height } = useWindowDimensions();
  const contentHeight = useSharedValue(0);
  const headerHeight = useSharedValue(44);
  const touchedRef = useRef(false);

  const style = useAnimatedStyle(() => ({
    height: Math.min(
      contentHeight.value + 29 + headerHeight.value,
      height - bottom,
    ),
  }));

  const onContentLayout = useCallback((e: LayoutChangeEvent) => {
    contentHeight.value = withTiming(e.nativeEvent.layout.height, {
      duration: touchedRef.current ? 500 : 0,
    });
    touchedRef.current = true;
  }, []);

  const onHeaderLayout = useCallback((e: LayoutChangeEvent) => {
    headerHeight.value = e.nativeEvent.layout.height;
  }, []);

  const { t } = useTranslation(['eApp']);

  return (
    <Modal
      animationIn="slideInDown"
      animationOut="slideOutUp"
      animationInTiming={theme.animation.duration}
      animationOutTiming={theme.animation.duration}
      backdropTransitionOutTiming={0}
      backdropTransitionInTiming={0}
      isVisible={visible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="up"
      propagateSwipe={true}
      backdropColor="#000"
      backdropOpacity={0.5}
      style={styles.modal}>
      <Container style={style}>
        <View onLayout={onHeaderLayout}>
          <ModalHeader title={t('eApp:yourProgress')} goBack={onClose} />
        </View>
        <Animated.ScrollView style={{ width: '100%' }}>
          <View
            onLayout={onContentLayout}
            style={{ flex: 1, paddingTop: theme.space[4] }}
            onStartShouldSetResponder={() => true}>
            <ProgressList />
          </View>
        </Animated.ScrollView>
        <Handler />
      </Container>
    </Modal>
  );
});
export default ProgressModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-start',
  },
});
