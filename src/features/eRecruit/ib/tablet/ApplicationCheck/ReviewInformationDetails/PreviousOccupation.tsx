import { useTheme } from '@emotion/react';
import { Column, H6, H7, PictogramIcon, Row, XView } from 'cube-ui-components';
import InfoField from 'features/eRecruit/components/InfoField';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import React from 'react';
import { View } from 'react-native';

import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';
import { formatCurrency } from 'utils';
import { country } from 'utils/context';

type WorkingExperiences =
  | {
      type: string;
      basicSalary: number;
      companyAddress: string;
      companyEmail: string;
      companyName: string;
      companyPhoneCountryCode: string | null;
      companyPhone: string | null;
      dateApplied: string;
      dateTermination: string;
      intermediaryType: string;
      position: string;
      rank: string;
    }[]
  | [];
type WorkingExperiencesProps = ApplicationFormResponds['workingExperiences'];
const currencyLabel = country === 'ib' || country === 'my' ? 'RM ' : '';

export function PreviousOccupation({
  work,
  workType,
  configList,
}: {
  work: WorkingExperiencesProps;
  workType: string;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { colors, space } = useTheme();
  const titleHandler = (workType: string) => {
    if (workType === 'PREVIOUS') {
      return 'Previous Occupation';
    }
    if (workType === 'TAKAFUL') {
      return 'Family/General Takaful Experience';
    }
    if (workType === 'INSURANCE') {
      return 'Life/General Insurance Experience';
    }
  };
  return (
    <View>
      <Row
        gap={space[2]}
        style={{ alignItems: 'center', paddingBottom: space[5] }}>
        <PictogramIcon.Work3 size={space[10]} />
        <H6 fontWeight="bold">{titleHandler(workType)}</H6>
      </Row>
      {work &&
        work
          .filter(work => work.type === workType)
          .map((item, index) => (
            <>
              {workType === 'TAKAFUL' || workType === 'INSURANCE' ? (
                <OtherExperience
                  item={item}
                  index={index}
                  workType={workType}
                  configList={configList}
                />
              ) : (
                <React.Fragment key={workType + index}>
                  <Row
                    style={{
                      paddingBottom: space[3],
                      alignItems: 'center',
                      gap: space[2],
                    }}>
                    <H7 fontWeight="bold">
                      {index + 1}. {item.companyName ?? 'N/A'}
                    </H7>
                  </Row>
                  <Column gap={space[2]}>
                    <XView>
                      <InfoField
                        label={'Position held'}
                        data={item.position ?? 'N/A'}
                      />
                      <InfoField
                        label={'Name of company'}
                        data={item.companyName ?? 'N/A'}
                      />
                    </XView>
                    <XView>
                      <InfoField
                        label={'Last draw salary'}
                        data={
                          currencyLabel + formatCurrency(item.basicSalary) ??
                          'N/A'
                        }
                      />
                      <InfoField
                        label={'Date appointed'}
                        data={formatDate(item.dateApplied) ?? 'N/A'}
                      />
                    </XView>

                    <XView>
                      <InfoField
                        label={'Date termination'}
                        data={formatDate(item.dateTermination) ?? 'N/A'}
                      />
                      <InfoField
                        label={'Contact no'}
                        data={
                          item.companyPhoneCountryCode !== null &&
                          item.companyPhone !== null
                            ? item.companyPhoneCountryCode +
                              ' ' +
                              item.companyPhone
                            : 'N/A'
                        }
                      />
                    </XView>
                    <XView>
                      <InfoField
                        label={'Email'}
                        data={item.companyEmail ?? 'N/A'}
                      />
                      <InfoField label={''} data={''} />
                    </XView>
                    <XView>
                      <InfoField
                        label={'Company address'}
                        data={item.companyAddress ?? 'N/A'}
                      />
                      <InfoField label={''} data={''} />
                    </XView>
                  </Column>
                </React.Fragment>
              )}
              {work.length - 1 !== index && (
                <View style={{ height: space[5] }}></View>
              )}
            </>
          ))}
    </View>
  );
}

const OtherExperience = ({
  item,
  index,
  workType,
  configList,
}: {
  item: any;
  index: number;
  workType: string;
  configList: GetERecruitConfigResponse | undefined;
}) => {
  const { space, sizes } = useTheme();
  return (
    <React.Fragment key={workType + index}>
      <Row
        style={{
          paddingBottom: space[3],
          alignItems: 'center',
          gap: space[2],
        }}>
        <H7 fontWeight="bold">
          {index + 1}.{' '}
          {workType === 'INSURANCE'
            ? configList?.liftGenernalTakafulCompaniesList.find(
                itemfromlist => itemfromlist.itemCode === item.companyName,
              )?.longDesc.en ?? 'N/A'
            : workType === 'TAKAFUL'
            ? configList?.familyGenernalTakafulCompaniesList.find(
                itemfromlist => itemfromlist.itemCode === item.companyName,
              )?.longDesc.en ?? 'N/A'
            : 'N/A'}
        </H7>
      </Row>
      <Column gap={space[2]}>
        <XView>
          <InfoField
            label={'Type of Intermediary'}
            data={
              configList?.intermediaryTypeList.find(
                itemfromlist =>
                  itemfromlist.itemCode === item?.intermediaryType,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={'Name of company'}
            data={
              workType === 'INSURANCE'
                ? configList?.liftGenernalTakafulCompaniesList.find(
                    itemfromlist => itemfromlist.itemCode === item.companyName,
                  )?.longDesc.en ?? 'N/A'
                : workType === 'TAKAFUL'
                ? configList?.familyGenernalTakafulCompaniesList.find(
                    itemfromlist => itemfromlist.itemCode === item.companyName,
                  )?.longDesc.en ?? 'N/A'
                : 'N/A'
            }
          />
        </XView>
        <XView>
          <InfoField label={'Rank'} data={item.rank ?? 'N/A'} />
          <InfoField
            label={'Last draw salary'}
            data={currencyLabel + formatCurrency(item.basicSalary) ?? 'N/A'}
          />
        </XView>

        <XView>
          <InfoField
            label={'Date appointed'}
            data={formatDate(item.dateApplied) ?? 'N/A'}
          />
          <InfoField
            label={'Date termination'}
            data={formatDate(item.dateTermination) ?? 'N/A'}
          />
        </XView>
      </Column>
    </React.Fragment>
  );
};
