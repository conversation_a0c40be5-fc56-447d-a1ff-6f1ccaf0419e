import { useTheme } from '@emotion/react';
import FlagLabel from 'components/FlagLabel';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import { filterLabelMap } from 'features/eRecruit/config';
import { useTranslation } from 'react-i18next';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
} from 'types/eRecruit';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function CandidateListCard({
  info,
}: {
  info: ApplicationListResponds;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const candidate = 'eRecruit.candidate';
  const processedDate = dateHandler({
    cubeStatus: info.cubeStatus,
    stage: info.stage,
    info,
  });
  const updateDate = processedDate ? new Date(processedDate) : undefined;
  const candidateLabelHandler = (status: CubeStatusKeys | null) => {
    switch (status) {
      case 'APPROVED':
        return (
          <FlagLabel
            type={'leadStatus_lightGreen20'}
            content={t(`${candidate}.approved`)}
            Icon={Icon.Tick}
          />
        );
      case 'REJECTED':
        return (
          <FlagLabel
            type={'disable_grey'}
            content={t(`${candidate}.rejected`)}
          />
        );
      case 'PENDING_LEADER_APPROVAL':
        return (
          <FlagLabel
            type={country === 'id' ? 'disable_grey' : 'primary_orange'}
            content={filterLabelMap?.[status]}
          />
        );
      case 'PENDING_PAYMENT':
      case 'REMOTE_CHECKING':
      case 'REMOTE_SIGNATURE':
        return (
          <FlagLabel
            type={'primary_orange'}
            content={filterLabelMap?.[status]}
          />
        );

      default:
        return <></>;
    }
  };

  return (
    <Row
      style={{
        borderRadius: borderRadius.large,
        paddingVertical: space[3],
        paddingHorizontal: space[4],
        backgroundColor: colors.background,
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
      <Column gap={space[2]}>
        <Typography.LargeLabel fontWeight="bold">
          {info.name}
        </Typography.LargeLabel>
        {candidateLabelHandler(info.cubeStatus)}
        {info.candidatePosition ? (
          <Typography.Label>
            Position: {info.candidatePosition}
          </Typography.Label>
        ) : (
          <Typography.Label>{'   '}</Typography.Label>
        )}
        <Typography.Label color={colors.palette.fwdDarkGreen[50]}>
          {' '}
          Last update: {updateDate ? dateFormatUtil(updateDate) : '--'}
        </Typography.Label>
      </Column>
      <Icon.ChevronRight size={space[6]} fill={colors.secondaryVariant} />
    </Row>
  );
}

const dateHandler = ({
  cubeStatus,
  stage,
  info,
}: {
  cubeStatus: CubeStatusKeys;
  stage: ApplicationStageKeys;
  info: ApplicationListResponds;
}) => {
  //  * stage handling
  if (stage === 'NEW_APPLICATION') {
    return info.lstUpdDate;
  }

  //  *** cubeStatus handling
  switch (cubeStatus) {
    case 'PENDING_PAYMENT':
    case 'PENDING_LEADER_APPROVAL':
      return info.submissionDate;
    case 'APPROVED':
      return info.approvedDate;
    case 'REJECTED':
      return info.rejectedDate;
    case 'REMOTE_CHECKING':
    case 'REMOTE_SIGNATURE':
    default:
      return info.lstUpdDate;
  }
};
