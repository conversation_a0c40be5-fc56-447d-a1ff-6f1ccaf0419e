import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { Box, Column, Icon, Row } from 'cube-ui-components';
import { View } from 'react-native';

export default function SkeletonERecruitFollowUpApp() {
  const { colors, space } = useTheme();
  return (
    <>
      {Array.from({ length: 5 }).map((_, i) => {
        return (
          <Box
            key={i}
            mb={space[3]}
            pl={space[4]}
            py={space[4]}
            borderRadius={space[4]}
            bgColor={colors.background}>
            <Column style={{ gap: space[1] }}>
              <Skeleton width={'70%'} height={16} radius={2} />
              <Skeleton width={'45%'} height={16} radius={2} />
              <Skeleton width={'45%'} height={16} radius={2} />
              <Skeleton width={'20%'} height={16} radius={2} />
            </Column>
            <View style={{ position: 'absolute', top: '50%', right: 8 }}>
              <Icon.ChevronRight
                fill={colors.palette.fwdDarkGreen[50]}
                size={24}
              />
            </View>
          </Box>
        );
      })}
    </>
  );
}
