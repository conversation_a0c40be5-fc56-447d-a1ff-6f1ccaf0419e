import { satisfies } from 'compare-versions';
import { QuestionsMap } from 'features/eRecruit/types';
import { CubeStatusKeys } from 'types/eRecruit';

export const filterLabelMap: Record<CubeStatusKeys, string> = {
  PENDING_PAYMENT: 'Pending payment',
  PENDING_LEADER_APPROVAL: 'Pending leader approval',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  REMOTE_CHECKING: 'Remote checking',
  REMOTE_SIGNATURE: 'Pending remote signature',
  RESUME_APPLICATION: 'Resume application',
  POTENTIAL_CANDIDATE: 'Created',
};

export const approvedRejectedStatusFilterLabelMap: Record<string, string> = {
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
};

export const questionsMap = {
  ownershipInterest: {
    key: 'ownershipInterest',
    title: 'Ownership Interest',
    qBody: [
      'Is there any significant shareholding or interests held by you or by a member of your immediate family ',
      ' in any business enterprise / entity / trust (i.e. greater than 5% of issued share capital / interests)?',
    ],
    tooltipContentList: [
      'Immediate family member includes parents, siblings, spouse, children, and immediate in-laws (e.g. father-in-law).',
    ],
  },
  externalEmployment: {
    key: 'externalEmployment',
    title: 'External Directorship / Employment',
    qBody: [
      'Is there any existing or proposed position held by you as director in any external business enterprise / entity or are you engaged in any employment or commercial duties (paid or unpaid) outside FWD?',
    ],
    tooltipContentList: [],
  },
  businessAffiliationInterest: {
    key: 'businessAffiliationInterest',
    title: 'Business Affiliations Interests',
    qBody: [
      'Is an immediate family member ',
      ' employed by any business enterprise / entity within FWD group ? The information provided will be used to determine whether there is any perceived, potential or actual conflict of interest.',
    ],
    tooltipContentList: [
      'Immediate family member includes parents, siblings, spouse, children, and immediate in-laws (e.g. father-in-law).',
    ],
  },

  relationshipGovernmentOfficial: {
    key: 'relationshipGovernmentOfficial',
    title: 'Relationships with Government Officials',
    qBody: [
      'Are you or any immediate family member ',
      ' a government official ',
      ' with a senior position ',
      ' ?',
    ],
    tooltipContentList: [
      'Immediate family member includes parents, siblings, spouse, children, and immediate in-laws (e.g. father-in-law).',
      'Government Official includes any officer, employee or any person acting in an official capacity for or on behalf of any:\n(i) Government, or department, ministry, agency, authority, or branch of government, including entities or organizations owned or controlled by any governmental entity; \n(ii) Political party, which includes party officials and candidates; or \n(iii) Public international organisation.',
      'Senior position is defined as a government official holding a legislative, administrative or judicial post or a carrying out a prominent function (e.g. Head of State, Chief Secretary of Administration, Head of a government bureau, Chief Justice etc).',
    ],
  },
  otherInterest: {
    key: 'otherInterest',
    title: 'Other Perceived, Potential or Actual Conflicts of Interest',
    qBody: [
      'Are there any other Perceived, Potential or Actual Conflicts of Interest to declare? If ‘yes’, please provide details below.',
    ],
    tooltipContentList: [],
  },
} as const satisfies QuestionsMap;
