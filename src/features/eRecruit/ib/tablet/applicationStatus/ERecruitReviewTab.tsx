import { View, FlatList, TouchableOpacity, Animated } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Row,
  Icon,
  Typography,
  Box,
  LoadingIndicator,
  Chip,
} from 'cube-ui-components';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useEffect, useRef, useState } from 'react';
import { ObjectUtil } from 'utils';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import NoDataFound from 'features/eRecruit/my/tablet/applicationStatus/NoDataFound';

type ERTableContent = {
  displayName: string;
  position: string;
  status: string;
  lastUpdated: string;
  recruitId: string;
};

export default function ERReviewScreen() {
  const { colors, space, borderRadius } = useTheme();
  const [filterBy, setFilterBy] = useState('none');
  const [isListProcessing, setIsListProcessing] = useState(false);
  const [isSortDate, setIsSortDate] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const tableContent: ERTableContent[] = [
    {
      displayName: 'Hardy Leung',
      position: 'FWO',
      status: 'Require your review',
      lastUpdated: '2022-12-11',
      recruitId: 'fbsrt435t34264y',
    },
    {
      displayName: 'Gary Leung',
      position: 'FWO',
      status: 'Approved',
      lastUpdated: '2023-12-31',
      recruitId: 'fdv35b45hyhtre',
    },
    {
      displayName: 'Pardy Peung',
      position: 'FWO',
      status: 'Rejected',
      lastUpdated: '2023-12-09',
      recruitId: '43g5hetrnrtynee',
    },
    {
      displayName: 'Pardy Peung',
      position: 'FWO',
      status: 'Rejected',
      lastUpdated: '2023-10-09',
      recruitId: '43g5hetrnrtyneee',
    },
  ];

  const dataWithDateSort = tableContent.sort((a, b) => {
    return (
      new Date(a.lastUpdated).getTime() - new Date(b.lastUpdated).getTime()
    );
  });
  useEffect(() => {
    tableContent &&
      tableContent.map(item => {
        item.status == 'Require your review' && setShowMessage(true);
      });
  }, []);

  const [data, setData] = useState(tableContent ? dataWithDateSort : []);

  useEffect(() => {
    if (filterBy === 'none') {
      data;
    } else if (filterBy === 'Require your review') {
      tableContent &&
        setData(
          tableContent?.filter(item => item.status == 'Require your review'),
        );
    } else if (filterBy === 'Approved') {
      tableContent &&
        setData(tableContent?.filter(item => item.status == 'Approved'));
    } else if (filterBy === 'Rejected') {
      tableContent &&
        setData(tableContent?.filter(item => item.status == 'Rejected'));
    }
  }, [filterBy]);

  useEffect(() => {
    const cloneData = data && ObjectUtil.cloneDeep(data);
    const reverseData = cloneData.reverse();
    setData(reverseData);
  }, [isSortDate]);

  const dropAnim = useRef(new Animated.Value(-50)).current;
  useEffect(() => {
    Animated.timing(dropAnim, {
      toValue: 0,
      duration: 700,
      useNativeDriver: true,
    }).start();
  }, [dropAnim]);

  return (
    <Animated.View
      style={[
        { flex: 1 },
        showMessage && {
          transform: [{ translateY: dropAnim }],
        },
      ]}>
      {showMessage && (
        <Row
          backgroundColor={colors.palette.fwdOrange[5]}
          px={space[4]}
          py={space[3]}
          alignItems="center">
          <Icon.Warning size={space[5]} />
          <Box boxSize={space[2]} />
          <Typography.Body color={colors.primary}>
            Application will be automatically declined after 14 days without
            approval.
          </Typography.Body>
        </Row>
      )}
      <Box
        style={{
          backgroundColor: colors.palette.fwdGrey[50],
          width: '100%',
          padding: space[4],
          flex: 1,
        }}>
        <Typography.H6 fontWeight="bold">Review application</Typography.H6>
        <Row style={{ paddingVertical: space[3], alignItems: 'center' }}>
          <Typography.Body
            fontWeight="normal"
            color={colors.palette.fwdGreyDarkest}>
            Filter by
          </Typography.Body>
          <Box boxSize={space[2]} />
          <Chip
            focus={filterBy === 'Require your review'}
            label={'Require your review'}
            onPress={() => setFilterBy('Require your review')}
          />
          <Box boxSize={space[1]} />
          <Chip
            focus={filterBy === 'Approved'}
            label={'Approved'}
            onPress={() => setFilterBy('Approved')}
          />
          <Box boxSize={space[1]} />
          <Chip
            focus={filterBy === 'Rejected'}
            label={'Rejected'}
            onPress={() => setFilterBy('Rejected')}
          />
        </Row>
        <View style={{ paddingBottom: space[3] }}>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            Total case ({data.length}) | Displaying data from the last 90 days
          </Typography.Body>
        </View>
        <ERTable
          data={data}
          isListProcessing={isListProcessing}
          isSortDate={isSortDate}
          setIsSortDate={setIsSortDate}
        />
      </Box>
    </Animated.View>
  );
}

const TableHeader = ({
  isSortDate,
  setIsSortDate,
}: {
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  return (
    <Row
      style={{
        backgroundColor: colors.primary,
        borderTopLeftRadius: space[4],
        borderTopRightRadius: space[4],
        padding: space[4],
      }}>
      <Row style={{ flex: 286 }}>
        <Typography.Label color={colors.background}>
          Candidtate name
        </Typography.Label>
      </Row>
      <Row style={{ flex: 100 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <Typography.Label color={colors.background}>Position</Typography.Label>
      </Row>
      <Row style={{ flex: 286 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <Typography.Label color={colors.background}>Status</Typography.Label>
      </Row>
      <Row style={{ flex: 140 }}>
        <Box
          height={18}
          width={space[4]}
          borderLeft={1}
          borderColor={colors.palette.white}
        />
        <TouchableOpacity
          onPress={() => {
            setIsSortDate(!isSortDate);
          }}>
          <Row justifyContent="center" alignItems="center">
            <Typography.Label color={colors.background}>
              Last update
            </Typography.Label>
            <Box boxSize={space[1]} />
            {isSortDate ? (
              <Icon.ArrowUp size={space[4]} fill={colors.background} />
            ) : (
              <Icon.ArrowDown size={space[4]} fill={colors.background} />
            )}
          </Row>
        </TouchableOpacity>
      </Row>
    </Row>
  );
};

const ERTable = ({
  data,
  isListProcessing,
  isSortDate,
  setIsSortDate,
}: {
  data: any;
  isListProcessing: boolean;
  isSortDate: boolean;
  setIsSortDate: any;
}) => {
  const { colors, space, borderRadius } = useTheme();
  return (
    <FlatList
      data={data}
      bounces={false}
      style={{
        flex: 1,
        borderBottomLeftRadius: space[4],
        borderBottomRightRadius: space[4],
        marginBottom: space[40],
      }}
      ListHeaderComponent={() => (
        <TableHeader isSortDate={isSortDate} setIsSortDate={setIsSortDate} />
      )}
      ItemSeparatorComponent={() => (
        <Box backgroundColor={colors.palette.fwdGrey[100]} h={1} />
      )}
      ListEmptyComponent={() => (
        <>
          {isListProcessing ? (
            <Box
              backgroundColor={colors.background}
              minHeight={60}
              justifyContent="center"
              alignItems="center"
              borderBottomRadius={16}>
              <Box h={space[5]} width={space[5]}>
                <LoadingIndicator size={space[5]} />
              </Box>
            </Box>
          ) : (
            <NoDataFound />
          )}
        </>
      )}
      renderItem={({ item, index }) => (
        <TableItem lastIndex={data.length} index={index} {...item} />
      )}
      keyExtractor={({ recruitId }) => String(recruitId)}
    />
  );
};

function TableItem({
  displayName,
  position,
  status,
  lastUpdated,
  index,
  lastIndex,
}: {
  displayName: string;
  position: string;
  status: string;
  lastUpdated: string;
  index: number | null;
  lastIndex: number | null;
}) {
  const { colors, space, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // const onPressItem = () => {
  //   navigation.navigate('BirthdayCardScreen', {
  //     customerId: Number(customerId),
  //     customerName: displayName.en,
  //   });
  // };

  return (
    <>
      <TouchableOpacity
        style={{
          borderBottomLeftRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          borderBottomRightRadius:
            lastIndex && lastIndex - 1 == index ? space[4] : 0,
          backgroundColor:
            index && (index + 1) % 2 == 0
              ? colors.palette.fwdGrey[20]
              : colors.background,
        }}>
        <Row marginX={space[4]} my={space[4]} flex={1}>
          <View style={{ flex: 286, justifyContent: 'center' }}>
            <Typography.Body color={colors.secondary}>
              {displayName}
            </Typography.Body>
          </View>
          <Row alignItems="center" flex={100}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Typography.Body color={colors.secondary}>
              {position}
            </Typography.Body>
          </Row>
          <Row alignItems="center" flex={286}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />

            <StatusColoring status={status} />
          </Row>
          <Row alignItems="center" flex={140}>
            <Box
              height={18}
              width={space[4]}
              borderLeft={1}
              borderColor={colors.palette.fwdGrey[100]}
            />
            <Row
              style={{
                justifyContent: 'space-between',
                width: '100%',
                paddingRight: space[2],
              }}>
              <Typography.Body>{dateFormatUtil(lastUpdated)}</Typography.Body>
              <Icon.ChevronRight fill={colors.primary} size={24} />
            </Row>
          </Row>
        </Row>
      </TouchableOpacity>
    </>
  );
}

function StatusColoring({ status }: { status: string }) {
  const { colors, space, sizes } = useTheme();
  let colorCombo;
  switch (status) {
    case 'Rejected':
      colorCombo = {
        backgroundColor: colors.palette.alertRedLight,
        wordColor: colors.palette.alertRed,
      };
      break;
    case 'Approved':
      colorCombo = {
        backgroundColor: colors.palette.alertGreenLight,
        wordColor: colors.palette.alertGreen,
      };
      break;
    case 'Require your review':
      colorCombo = {
        backgroundColor: colors.palette.fwdBlue[20],
        wordColor: colors.palette.fwdBlue[100],
      };
      break;
    default:
      colorCombo = {
        backgroundColor: '',
        wordColor: '',
      };
  }
  return (
    <View
      style={{
        backgroundColor: colorCombo.backgroundColor,
        paddingHorizontal: space[1],
        paddingVertical: 2,
        borderRadius: 2,
      }}>
      <Typography.SmallLabel color={colorCombo.wordColor}>
        {status}
      </Typography.SmallLabel>
    </View>
  );
}
