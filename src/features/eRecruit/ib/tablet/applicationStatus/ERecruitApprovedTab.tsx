import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Chip, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import ERTable from 'features/eRecruit/ib/tablet/applicationStatus/ERApprovedTable';
import SearchingCandidatesListSection from 'features/eRecruit/ib/tablet/SearchingCandidatesList';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity } from 'react-native';
import { ERTableApprovedContent, positionList } from 'types/eRecruit';

export default function ERApprovedScreen() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eRecruit']);
  const [filterBy, setFilterBy] = useState<string[]>([]);
  const [isSortDate, setIsSortDate] = useState(false);
  // const [isListProcessing, setIsListProcessing] = useState(false);
  // const [data, setData] = useState<ERTableApprovedContent[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const {
    isLoading: isApprovedDataLoading,
    data: newData,
    refetch,
    isRefetching,
  } = useGetERApplicationList({
    cubeStatusList: ['APPROVED'],
    limit: 99999,
  });

  const mappedData = useMemo(
    () =>
      newData?.data?.map(item => ({
        displayName: item.name,
        position: item.candidatePositionCode ?? '--',
        approvalDate: item.approvedDate ?? '--',
        recruitId: item.registrationId ?? item.registrationStagingId,
        status: item.cubeStatus,
        stage: item.stage,
      })) ?? [],
    [newData],
  );

  const sortedAppStatusData: ERTableApprovedContent[] = useMemo(
    () =>
      mappedData.sort((a, b) =>
        isSortDate
          ? new Date(a?.approvalDate).getTime() -
            new Date(b?.approvalDate).getTime()
          : new Date(b?.approvalDate).getTime() -
            new Date(a?.approvalDate).getTime(),
      ),
    [mappedData, isSortDate],
  );

  const processedData = useMemo(
    () =>
      filterBy.length > 0
        ? sortedAppStatusData.filter(item => filterBy.includes(item.position))
        : sortedAppStatusData,
    [sortedAppStatusData, filterBy],
  );
  const tagOnPress = (tabFilter: string) => {
    filterBy.includes(tabFilter)
      ? setFilterBy(filterBy.filter(item => item !== tabFilter))
      : setFilterBy([...filterBy, tabFilter]);
  };

  if (isSearching) {
    return (
      <CommonAnimatedViewWrapper>
        <SearchingCandidatesListSection
          isApproved
          setIsSearching={setIsSearching}
        />
      </CommonAnimatedViewWrapper>
    );
  }
  return (
    <Column flex={1} bgColor={colors.palette.fwdGrey[50]}>
      {/* <SlidingSideModal
        hasHeaderBottomBar = {false}
        title={'Filter'}
        visible={isOpenFilter}
        onClose={() => setIsOpenFilter(false)}>
        <Box paddingX={space[4]} paddingY={0} gap={space[3]}>
          <Typography.H7 fontWeight="bold">Position</Typography.H7>
          <Row gap={space[1]}>
            <Chip
              focus={filterBy.includes('AGT')}
              label={'AGT'}
              onPress={() => tagOnPress('AGT')}
            />
            <Chip
              focus={filterBy.includes('UM')}
              label={'UM'}
              onPress={() => tagOnPress('UM')}
            />
            <Chip
              focus={filterBy.includes('AM')}
              label={'AM'}
              onPress={() => tagOnPress('AM')}
            />
          </Row>
        </Box>
      </SlidingSideModal> */}
      <Row justifyContent="space-between" alignItems="center">
        <Typography.H6 fontWeight="bold">
          {t('eRecruit:applicationStatus.approvedCandidates')}
        </Typography.H6>
        <TouchableOpacity onPress={() => setIsSearching(true)}>
          <Row
            justifyContent="center"
            gap={space[2]}
            alignItems="center"
            borderRadius={space[10]}
            paddingX={space[5]}
            paddingY={space[2]}
            borderColor={colors.palette.fwdOrange[50]}
            borderWidth={2}
            backgroundColor={colors.background}
            style={{
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 4,
              },
              shadowOpacity: 0.12,
              shadowRadius: 10,
              elevation: 5, // This is for Android
            }}>
            <Icon.Search size={18} />
            <Typography.LargeLabel color={colors.primary} fontWeight="medium">
              {t('eRecruit:eRecruit.searchCandidate')}
            </Typography.LargeLabel>
          </Row>
        </TouchableOpacity>
      </Row>
      <Row pt={space[3]} gap={space[2]} alignItems="center">
        <Typography.Body
          fontWeight="normal"
          color={colors.palette.fwdGreyDarkest}>
          {t('eRecruit:applicationStatus.filterBy')}
        </Typography.Body>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: space[1] }}>
          {positionList.map(position => (
            <Chip
              key={position}
              focus={filterBy.includes(position)}
              label={position}
              onPress={() => tagOnPress(position)}
            />
          ))}
        </ScrollView>
        {/* <TouchableOpacity
          onPress={() => {
            setIsOpenFilter(true);
          }}>
          <Icon.Filter fill={colors.onBackground} />
        </TouchableOpacity> */}
      </Row>
      <Typography.Body
        color={colors.palette.fwdGreyDarkest}
        style={{ paddingVertical: space[3] }}>
        {t('eRecruit:applicationStatus.totalCaseShownFromLast90Days', {
          count: processedData?.length,
        })}
      </Typography.Body>
      <ERTable
        type="refetchable"
        refetch={refetch}
        isRefetching={isRefetching}
        data={processedData}
        isListProcessing={isApprovedDataLoading}
        isSortDate={isSortDate}
        setIsSortDate={setIsSortDate}
      />
    </Column>
  );
}

const CommonAnimatedViewWrapper = styled(AnimatedViewWrapper)(({ theme }) => ({
  flex: 1000,
  backgroundColor: theme.colors.surface,
  paddingTop: theme.space[5],
}));
