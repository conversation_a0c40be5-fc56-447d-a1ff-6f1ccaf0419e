import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Column, Row } from 'cube-ui-components';
import { RemoteCheckingLinkModal } from 'features/eRecruit/components/CandidateProfile/RecruitmentRootSection';
import ShareLinkButton from 'features/eRecruit/components/CandidateProfile/ShareLinkButton';
import { useGetERCandidateProfile } from 'features/eRecruit/hooks/useGetERCandidateProfile';
import CheckApplicationButton from 'features/eRecruit/my/tablet/components/CheckApplicationButton';
import RemoveCandidateButton from 'features/eRecruit/my/tablet/components/RemoveCandidateButton';
import TriggerApplicationButton from 'features/eRecruit/my/tablet/components/TriggerApplicationButton';
import useBoundStore from 'hooks/useBoundStore';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen';
import {
  ERecruitCandidateProfileParamList,
  RootStackParamListMap,
} from 'types';
import CandidateApplicationStatus from './CandidateApplicationStatus';
import CandidatePersonalInfo from './CandidatePersonalInfo';
import CandidateSideBarContent from './CandidateSideBarContent';
import useToggle from 'hooks/useToggle';

export default function FIBCandidateProfileLayout(props: ERAppStatusProps) {
  const { top } = useSafeAreaInsets();
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const { navigate, goBack } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  const { stage, id, cubeStatus, registrationId, registrationStagingId } = props
    .route.params as ERecruitCandidateProfileParamList;
  const [showMessage, setShowMessage] = useState(false);
  const [isShowModal, onOpenModal, , setIsShowModal] = useToggle(false);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const { isLoading: isCPDetailsLoading, data: candidateProfileDetails } =
    useGetERCandidateProfile({
      registrationId: registrationId,
      registrationStagingId: registrationStagingId,
    });

  const buttonStyle = {
    height: '100%',
    paddingTop: space[5],
    flex: 0.4,
  };

  useEffect(() => {
    if (isCPDetailsLoading) {
      setAppLoading();
    } else setAppIdle();
  }, [isCPDetailsLoading, setAppIdle, setAppLoading]);

  useEffect(() => {
    cubeStatus == 'REMOTE_CHECKING' && setShowMessage(true);
  }, []);

  const dropAnim = useRef(new Animated.Value(-50)).current;
  useEffect(() => {
    Animated.timing(dropAnim, {
      toValue: 0,
      duration: 700,
      useNativeDriver: true,
    }).start();
  }, [dropAnim]);

  return (
    <Row backgroundColor={colors.palette.fwdOrange[5]} flex={1}>
      <RemoteCheckingLinkModal
        isShowModal={isShowModal}
        setIsShowModal={setIsShowModal}
        shareLink={candidateProfileDetails?.link}
        shareMessage={
          cubeStatus == 'PENDING_PAYMENT'
            ? candidateProfileDetails?.sharePaymentUrl
            : candidateProfileDetails?.shareLink
        }
        mobilePhone={candidateProfileDetails?.mobilePhone}
        cubeStatus={cubeStatus}
      />
      <Column flex={268} paddingTop={top + 15} paddingLeft={space[4]}>
        <CandidateSideBarContent
          candidateProfileDetails={candidateProfileDetails}
          cubeStatus={cubeStatus}
        />
      </Column>
      <Column
        flex={844}
        backgroundColor={colors.background}
        borderTopLeftRadius={space[10]}
        borderBottomLeftRadius={space[10]}
        paddingRight={space[8]}
        paddingLeft={space[10]}
        style={{
          shadowColor: 'rgba(184, 82, 6, 0.10)',
          shadowOffset: {
            width: 0,
            height: 4,
          },
          shadowOpacity: 0.5,
          shadowRadius: 20,
          elevation: 5, // This is for Android
        }}>
        <CandidateApplicationStatus
          createdDate={candidateProfileDetails?.createdDate}
          submissionDate={candidateProfileDetails?.submissionDate}
          lastPaidDate={candidateProfileDetails?.lastPaidDate}
          approvedDate={candidateProfileDetails?.approvedDate}
          rejectedDate={candidateProfileDetails?.rejectedDate}
          rejectReason={candidateProfileDetails?.rejectedReason}
          rejectedAgent={candidateProfileDetails?.rejectedAgent}
          cubeStatus={candidateProfileDetails?.cubeStatus}
          candidateName={candidateProfileDetails?.name}
          agencyAgreementType={candidateProfileDetails?.agencyAgreementType}
        />
        <CandidatePersonalInfo
          fullName={candidateProfileDetails?.name}
          mobilePhone={candidateProfileDetails?.mobilePhone}
          email={candidateProfileDetails?.email}
          gender={candidateProfileDetails?.gender}
          source={candidateProfileDetails?.source}
          candidateNo={candidateProfileDetails?.candidateRefId}
        />
        {cubeStatus == 'REJECTED' ||
        (candidateProfileDetails?.submissionDate == null &&
          cubeStatus !== 'REMOTE_CHECKING' &&
          cubeStatus !== 'REMOTE_SIGNATURE') ? (
          <RemoveCandidateButton
            registrationId={registrationId}
            registrationStagingId={registrationStagingId}
          />
        ) : (
          <></>
        )}
      </Column>
      {candidateProfileDetails?.submissionDate == null && (
        <Footer>
          <BottomStyle>
            {cubeStatus === 'REMOTE_CHECKING' ? (
              <CheckApplicationButton
                candidateProfileDetails={candidateProfileDetails}
              />
            ) : cubeStatus === 'REMOTE_SIGNATURE' ? (
              <ShareLinkButton
                candidateProfileDetails={candidateProfileDetails}
                onOpenModal={onOpenModal}
              />
            ) : (
              <>
                {/* <ShareRemoteLink
                    candidateProfileDetails={candidateProfileDetails}
                  /> */}
                <TriggerApplicationButton
                  label={
                    candidateProfileDetails?.stage
                      ? candidateProfileDetails?.stage == 'NEW_APPLICATION'
                        ? t('eRecruit.candidateProfile.startApplication')
                        : t('eRecruit.candidateProfile.resumeApplication')
                      : '--'
                  }
                  onPress={() => {
                    const currentApplicationId =
                      typeof candidateProfileDetails?.registrationStagingId ===
                      'number'
                        ? String(candidateProfileDetails?.registrationStagingId)
                        : '';

                    console.log(
                      '🤲🤲🤲 ~ file: onPress:158 ~ candidateProfileDetails:',
                      candidateProfileDetails,
                    );

                    const newAppAction = () => {
                      console.log(
                        "currentApplicationId is not passed as param, when candidateProfileDetails?.stage == 'NEW_APPLICATION",
                      );
                      navigate('ERecruitApplication');
                    };

                    return candidateProfileDetails?.stage
                      ? candidateProfileDetails?.stage == 'NEW_APPLICATION' &&
                        !currentApplicationId
                        ? newAppAction()
                        : navigate('ERecruitApplication', {
                            registrationStagingId: currentApplicationId,
                          })
                      : console.log(
                          'No candidateProfileDetails?.stage found, Not navigation',
                        );
                  }}
                />
              </>
            )}
          </BottomStyle>
        </Footer>
      )}
      {cubeStatus == 'PENDING_PAYMENT' &&
        candidateProfileDetails?.paymentUrl && (
          <Footer>
            <BottomStyle>
              <ShareLinkButton
                candidateProfileDetails={candidateProfileDetails}
                onOpenModal={onOpenModal}
              />
            </BottomStyle>
          </Footer>
        )}
    </Row>
  );
}

const Footer = styled(View)(({ theme: { space, colors } }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  return {
    maxHeight: 100,
    width: '100%',
    paddingHorizontal: space[8],
    borderWidth: 1,
    borderColor: colors.background,
    borderTopColor: colors.palette.fwdGrey[100],
    paddingTop: space[4],
    paddingBottom: space[2] + bottomInset,
    backgroundColor: colors.background,
    position: 'absolute',
    bottom: 0,
  };
});

const BottomStyle = styled(Row)(({ theme: { space, colors } }) => ({
  gap: 15,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
}));
