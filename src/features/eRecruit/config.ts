import { CubeStatusKeys } from 'types/eRecruit';
import { BuildCountry } from 'types';
import { country } from 'utils/context';

export const filterLabelMap: Record<CubeStatusKeys, string> = {
  PENDING_PAYMENT: 'Pending payment',
  PENDING_LEADER_APPROVAL: 'Pending leader approval',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  REMOTE_CHECKING: 'Remote checking',
  REMOTE_SIGNATURE:
    country === 'id' ? 'Pending remote signature' : 'Remote signature',
  RESUME_APPLICATION: 'Resume application',
  POTENTIAL_CANDIDATE: 'Created',
};

export const DEFAULT_COUNTRY_PHONE_CODE_MY = '+60';
export const DEFAULT_COUNTRY_PHONE_CODE_IDN = '+62';
export const DEFAULT_COUNTRY_PHONE_CODE_PH = '+63';

const defaultCountryCodeMap: Record<BuildCountry, string> = {
  my: DEFAULT_COUNTRY_PHONE_CODE_MY,
  ph: DEFAULT_COUNTRY_PHONE_CODE_PH,
  ib: DEFAULT_COUNTRY_PHONE_CODE_MY,
  id: DEFAULT_COUNTRY_PHONE_CODE_IDN,
};
export const defaultCountryCode = defaultCountryCodeMap?.[country] ?? '';
