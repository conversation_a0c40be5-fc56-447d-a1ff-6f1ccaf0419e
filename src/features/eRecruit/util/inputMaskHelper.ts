export function correctEmail(value: string) {
  let sanitizedValue = value;

  // Replace second "@" or second "." after the "@"
  const atIndex = value.indexOf('@');
  if (atIndex !== -1) {
    const secondAtIndex = value.indexOf('@', atIndex + 1);
    if (secondAtIndex !== -1) {
      sanitizedValue = value.substring(0, secondAtIndex);
    }
  }

  const consecutiveDotsRegex = /\.\./g;
  sanitizedValue = sanitizedValue.replace(consecutiveDotsRegex, '.');

  // Check if the email starts with a letter from "a" to "z"
  const startsWithLetterRegex = /^[a-zA-Z]/;
  if (!startsWithLetterRegex.test(sanitizedValue)) {
    sanitizedValue = '';
  }

  // Replace all other invalid characters
  sanitizedValue = sanitizedValue.replace(/[^a-zA-Z0-9._%@.-]/g, '');

  // Handle the sanitized value here (e.g., update state or call validation function)
  return sanitizedValue;
}

export function correctSalary(value: string) {
  let sanitizedValue = value;

  const dotIndex = value.indexOf('.');
  if (dotIndex !== -1) {
    const decimalPlaces = value.length - dotIndex - 1;
    if (decimalPlaces > 2) {
      sanitizedValue = value.substring(0, dotIndex + 3);
    }
    if (dotIndex === 9) {
      sanitizedValue = value.substring(0, dotIndex);
    }
  } else {
    sanitizedValue = value.substring(0, 8);
  }

  sanitizedValue = sanitizedValue.replace(SALARY_REGEX, '');

  if (sanitizedValue.length > 11) {
    sanitizedValue = sanitizedValue.substring(0, 11);
  }

  // if sanitizedValue has no decimal point and length is more than 8, then truncate it to 8
  if (sanitizedValue.indexOf('.') === -1 && sanitizedValue.length > 8) {
    return sanitizedValue.substring(0, 8);
  } else {
    return sanitizedValue;
  }
}

export function correctLengthLimit(value: string, length: number) {
  if (value.length > length) {
    return value.substring(0, length);
  }
  return value;
}

// export const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const PHONE_NUMBER_TYPE_REGEX = /[^0-9-]/g;

export const PHONE_NUMBER_TYPE_REGEX_DIGIT_ONLY = /[^0-9]/g;

export const SALARY_REGEX = /[^0-9.]|\.(?=.*\.)/g;
