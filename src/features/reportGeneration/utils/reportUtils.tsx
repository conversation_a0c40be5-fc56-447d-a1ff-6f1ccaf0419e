import _ from 'lodash';
import { Hierarchy, TeamMember } from 'types/team';

/**
 * Recursive function for normalizing team hierarchy list
 */
export interface MemberInfo {
  agentCode: string;
  agentName: string;
  designation: string;
}

export function flattenTeamHierarchy(data?: Hierarchy) {
  if (!data) return [];

  const result: MemberInfo[] = [];

  // Create self agent data and push to result
  const selfAgent = {
    agentCode: data?.agentCode,
    agentName: data?.agentName,
    designation: data?.designation,
  };
  result.push(selfAgent);

  // Recursive function to flatten the hierarchy
  function flattenHierarchy(data: TeamMember) {
    if (!_.isEmpty(data?.subteams)) {
      data?.subteams.forEach(subteam => {
        const subteamAgent: MemberInfo = {
          agentCode: subteam?.agentCode,
          agentName: subteam?.agentName,
          designation: subteam?.designation,
        };
        result.push(subteamAgent);
        flattenHierarchy(subteam);
      });
    }

    if (!_.isEmpty(data?.members)) {
      data?.members.forEach(member => {
        const memberAgent: MemberInfo = {
          agentCode: member?.agentCode,
          agentName: member?.agentName,
          designation: member?.designation,
        };
        result.push(memberAgent);
        flattenHierarchy(member);
      });
    }
  }

  flattenHierarchy(data);
  return result;
}
