import { useTheme } from '@emotion/react';
import { Box, Column, Row } from 'cube-ui-components';
import React, { SetStateAction } from 'react';
import { ScrollView } from 'react-native';
import AffiliatePerformance from './AffiliatePerformance';
import AffiliateProfile from './AffiliateProfile';
import RecentPosts from './RecentPosts';
import TopicsInterest from './TopicsInterest';
import TopChannels from './TopChannels';

// ------------------------------------  Affiliate Dashboard Overview Page ------------------------------------
export default function Overview({
  setIsTabActive,
}: {
  setIsTabActive: (value: SetStateAction<string>) => void;
}) {
  const { colors, space, borderRadius } = useTheme();

  return (
    <Row>
      <Box flex={1} backgroundColor={colors.palette.fwdGrey[50]}>
        <ScrollView
          contentContainerStyle={{
            gap: space[4],
            paddingLeft: space[8],
            paddingRight: space[8],
            paddingBottom: 200,
          }}>
          <AffiliatePerformance />

          <Row marginTop={space[8]} gap={space[8]}>
            <Column flex={58} display="flex">
              <AffiliateProfile />
              <RecentPosts setIsTabActive={setIsTabActive} />
            </Column>

            <Column flex={40} gap={space[5]}>
              <TopicsInterest />

              <TopChannels />
            </Column>
          </Row>
        </ScrollView>
      </Box>
    </Row>
  );
}
