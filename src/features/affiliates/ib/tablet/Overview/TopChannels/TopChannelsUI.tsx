import { useTheme } from '@emotion/react';
import { FlashList } from '@shopify/flash-list';
import { DoughnutChart } from 'components/Chart';
import { Box, Row, Typography } from 'cube-ui-components';
import FacebookSVG from 'features/affiliates/assets/FacebookSVG';
import GreenWhatsappSVG from 'features/affiliates/assets/GreenWhatsappSVG';
import MessengerSVG from 'features/affiliates/assets/MessengerSVG';
import PurpleInstagramSVG from 'features/affiliates/assets/PurpleInstagramSVG';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import {
  MediaLeadClickNum,
  StatsOfLeadGeneratedByChannels,
} from 'types/affiliateDashboard';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import TopChannelsUIEmptyData from './TopChannelsUIEmptyData';
import TabButton from 'features/affiliates/components/TabButton';

interface StatsOfLeadGeneratedByChannelsUIProps {
  responseData?: StatsOfLeadGeneratedByChannels;
}

export type TransformedData = {
  leadNum: number;
  viewTimes: number;
  media: string;
  icon: JSX.Element;
};

// DoughnutChart
type DataPoint = {
  color: string;
  percentage: number;
  opacity: number;
};

const TopChannelsUI: React.FC<StatsOfLeadGeneratedByChannelsUIProps> = ({
  responseData,
}) => {
  const { colors, space, sizes, borderRadius } = useTheme();
  const [isDayTabActive, setIsDayTabActive] = useState<boolean>(true);
  const [isYearTabActive, setIsYearTabActive] = useState<boolean>(false);
  const { t } = useTranslation(['affiliates']);
  const isEmptyData =
    responseData?.past30Days == null ||
    responseData?.thisYear == null ||
    responseData?.past30Days?.MediaLeadClickNum == null ||
    responseData?.thisYear?.MediaLeadClickNum == null;

  const iconMapping: Record<string, JSX.Element> = {
    Instagram: <PurpleInstagramSVG width={sizes[5]} height={sizes[5]} />,
    Facebook: <FacebookSVG width={sizes[5]} height={sizes[5]} />,
    Whatsapp: <GreenWhatsappSVG width={sizes[5]} height={sizes[5]} />,
    Messenger: <MessengerSVG width={sizes[5]} height={sizes[5]} />,
  };

  function mapItemToChartData(item: MediaLeadClickNum) {
    let color;
    switch (item.media) {
      case 'instagram':
        color = '#9B51E0';
        break;
      case 'facebook':
        color = '#1976D2';
        break;
      case 'whatsapp':
        color = '#6ABB55';
        break;
      case 'messenger':
        color = '#4DA6FF';
        break;
      default:
        color = colors.palette.fwdGrey[100]; // optional
    }

    return {
      color,
      percentage: percentageLeadNum(item.leadNum),
      opacity: 1,
    } as DataPoint;
  }

  const monthlyDonutChart = (
    responseData?.past30Days?.MediaLeadClickNum || []
  ).map((item: MediaLeadClickNum) => mapItemToChartData(item));

  const yearlyDonutChart = (
    responseData?.thisYear?.MediaLeadClickNum || []
  ).map((item: MediaLeadClickNum) => mapItemToChartData(item));

  function percentageLeadNum(leadNum: number) {
    const totalLeads = isDayTabActive
      ? responseData?.past30Days?.leadTotal
      : responseData?.thisYear?.leadTotal;
    if (!totalLeads || totalLeads <= 0) {
      return 0;
    }
    const percentage = (leadNum / totalLeads) * 100;
    return Math.ceil(percentage);
  }

  function sortedDataByLeadsAndViewTimes(responseData: MediaLeadClickNum[]) {
    return (
      responseData &&
      [...responseData].sort((a, b) => {
        const leadComparison = b.leadNum - a.leadNum;
        const viewTimesComparison = b.viewTimes - a.viewTimes;
        const alphaComparison = a.media.localeCompare(b.media, undefined, {
          sensitivity: 'base',
        });

        // Combine comparisons
        return leadComparison !== 0
          ? leadComparison
          : viewTimesComparison !== 0
          ? viewTimesComparison
          : alphaComparison;
      })
    );
  }

  function transformedData(responseData: MediaLeadClickNum[] | undefined) {
    const sortedData = sortedDataByLeadsAndViewTimes(responseData ?? []);

    // show max 5 channels
    const maxFiveChannels = sortedData?.slice(0, 5);

    // capitalize the first letter of media name
    const formattedMediaName = maxFiveChannels?.map(item => ({
      ...item,
      media: item.media.charAt(0).toUpperCase() + item.media.slice(1),
    }));

    const transformedData = formattedMediaName?.map(item => ({
      ...item,
      icon: iconMapping[item.media] ? (
        iconMapping[item.media]
      ) : (
        <>
          <Box
            borderRadius={borderRadius['x-small']}
            height={sizes[5]}
            width={sizes[5]}
            backgroundColor={colors.palette.fwdGrey[50]}
          />
        </>
      ),
    }));

    return transformedData;
  }

  const renderItem = ({ item }: { item: TransformedData }) => {
    return (
      <Box
        flexDirection="row"
        marginBottom={space[4]}
        justifyContent="space-between"
        gap={space[6]}>
        <Row style={{ flex: 1, alignItems: 'center' }} gap={space[2]}>
          {item.icon}
          <Typography.Body>{item.media}</Typography.Body>
        </Row>

        <Row gap={space[2]} flex={1} alignItems="center">
          <Typography.Body style={{ flex: 1 }}>
            {numberToThousandsFormat(item.leadNum)}
          </Typography.Body>
          <Typography.Body style={{ flex: 1 }}>
            {numberToThousandsFormat(item.viewTimes)}
          </Typography.Body>
        </Row>
      </Box>
    );
  };

  return (
    <>
      {isEmptyData ? (
        <TopChannelsUIEmptyData />
      ) : (
        <Box
          padding={space[5]}
          borderRadius={space[4]}
          backgroundColor={colors.background}
          gap={space[3]}>
          {/* Data analysis title */}
          <Typography.H7 fontWeight="bold" color={colors.secondary}>
            {t('affiliates:affiliates.topChannels')}
          </Typography.H7>

          {/* Tabs */}
          <Row gap={space[1]} marginTop={space[1]}>
            <TabButton
              onPress={() => {
                setIsDayTabActive(true);
                setIsYearTabActive(false);
                if (responseData?.past30Days != null) {
                  transformedData(responseData.past30Days.MediaLeadClickNum);
                }
              }}
              isActive={isDayTabActive}
              label={t('affiliates:affiliates.past30Days')}
            />
            <TabButton
              onPress={() => {
                setIsDayTabActive(false);
                setIsYearTabActive(true);
                if (responseData?.thisYear != null) {
                  transformedData(responseData.thisYear.MediaLeadClickNum);
                }
              }}
              isActive={isYearTabActive}
              label={t('affiliates:affiliates.thisYear')}
            />
          </Row>

          {/* Doughnut chart */}
          <Box
            px={space[5]}
            marginTop={space[1]}
            marginBottom={space[1]}
            justifyContent="center"
            flexDirection="row">
            <DoughnutChart
              data={isDayTabActive ? monthlyDonutChart : yearlyDonutChart}
              title={t('affiliates:affiliates.leadGenerated')}
              subtitle={
                isDayTabActive
                  ? responseData?.past30Days?.leadTotal
                  : responseData?.thisYear?.leadTotal
              }
              showPercentageLabel={false}
              padding={0}
              radius={104}
              customStyle={{
                titleStyle: {
                  fontSize: 14,
                  color: colors.secondary,
                  height: '46%',
                },
                subtitleStyle: {
                  fontSize: 31,
                  color: colors.secondary,
                  fontFamily: 'FWDCircularTT-Bold',
                  height: '53%',
                },
              }}
            />
          </Box>

          {/* Table header */}
          <Box
            flexDirection="row"
            justifyContent="space-between"
            gap={space[6]}>
            <Row style={{ flex: 1 }}>
              <Typography.SmallLabel>
                {t('affiliates:affiliates.channels')}
              </Typography.SmallLabel>
            </Row>

            <Row gap={space[2]} style={{ flex: 1 }}>
              <Typography.SmallLabel style={{ flex: 1 }}>
                {t('affiliates:affiliates.leadGenerated')}
              </Typography.SmallLabel>
              <Typography.SmallLabel
                color={colors.palette.fwdDarkGreen[100]}
                style={{ flex: 1 }}>
                {t('affiliates:affiliates.noOfClick')}
              </Typography.SmallLabel>
            </Row>
          </Box>

          {/* Table content */}
          <View style={{ flex: 1 }}>
            <FlashList
              estimatedItemSize={50}
              renderItem={renderItem}
              data={
                isDayTabActive
                  ? transformedData(responseData?.past30Days?.MediaLeadClickNum)
                  : transformedData(responseData?.thisYear?.MediaLeadClickNum)
              }
            />
          </View>
        </Box>
      )}
    </>
  );
};

export default TopChannelsUI;
