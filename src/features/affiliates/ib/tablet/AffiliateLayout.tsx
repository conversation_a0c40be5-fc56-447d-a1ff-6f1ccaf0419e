import { useTheme } from '@emotion/react';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { TouchableOpacity } from 'react-native';
import { useState } from 'react';
import Overview from './Overview';
import MakeAPost from './MakeAPost';
import { useTranslation } from 'react-i18next';
import History from './History';
import SearchSection from './SearchSection';
import { useGetRecentPostList } from 'features/affiliates/hooks/useGetRecentPostList';

export default function AffiliateLayout() {
  const { colors, space, getElevation } = useTheme();
  const { top } = useSafeAreaInsets();
  const [isTabActive, setIsTabActive] = useState('overview');
  const [isSearching, setIsSearching] = useState(false);

  const { t } = useTranslation(['affiliates']);

  const tabConfig = [
    {
      name: 'overview',
      title: 'Overview',
    },
    {
      name: 'makePost',
      title: 'Make a post',
    },
    {
      name: 'history',
      title: 'History',
    },
  ];
  const { isLoading, data } = useGetRecentPostList();
  if (isSearching) {
    return (
      <SearchSection
        isLoading={isLoading}
        setIsSearching={setIsSearching}
        data={data ?? []}
      />
    );
  }
  return (
    <Box paddingTop={top} backgroundColor={colors.background}>
      <Row
        backgroundColor={colors.palette.fwdGrey[50]}
        pr={33}
        px={space[8]}
        pt={space[6]}
        pb={space[7]}
        style={{ justifyContent: 'space-between' }}>
        <Row gap={space[4]}>
          {tabConfig.map(item => {
            const isActive = isTabActive === item.name;
            return (
              <TouchableOpacity
                style={{
                  paddingHorizontal: space[5],
                  paddingVertical: 10,
                  borderRadius: space[5],
                  backgroundColor: isActive
                    ? colors.primary
                    : colors.background,
                }}
                onPress={() => setIsTabActive(item.name)}
                key={item.name}>
                <Typography.H6
                  fontWeight={isActive ? 'bold' : 'normal'}
                  color={isActive ? colors.background : colors.onBackground}>
                  {item.title}
                </Typography.H6>
              </TouchableOpacity>
            );
          })}
        </Row>

        <TouchableOpacity onPress={() => setIsSearching(true)}>
          <Row
            w={space[72]}
            h={space[11]}
            paddingX={space[5]}
            paddingY={space[2]}
            gap={space[2]}
            borderRadius={space[5]}
            borderWidth={2}
            alignItems="center"
            borderColor={colors.palette.fwdOrange[50]}
            backgroundColor={colors.background}
            style={{
              ...getElevation(8),
            }}>
            <Icon.Search size={24} />
            <Typography.H7 color={colors.primary} fontWeight="medium">
              {t('affiliates:affiliates.searchPost')}
            </Typography.H7>
          </Row>
        </TouchableOpacity>
      </Row>
      {isTabActive === 'overview' && (
        <Overview setIsTabActive={setIsTabActive} />
      )}
      {isTabActive === 'makePost' && (
        <MakeAPost isLoading={isLoading} data={data ?? []} />
      )}
      {isTabActive === 'history' && <History />}
    </Box>
  );
}
