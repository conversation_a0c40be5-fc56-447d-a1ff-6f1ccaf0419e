import { useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Box,
  Button,
  Column,
  Icon,
  LoadingIndicator,
  Row,
  SmallLabel,
  Typography,
} from 'cube-ui-components';
import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { TouchableOpacity } from 'react-native';
import styled from '@emotion/native';
import FilterBar from 'features/affiliates/components/FilterBar';
import { useMemo, useState } from 'react';
import PurpleInstagramSVG from 'features/affiliates/assets/PurpleInstagramSVG';
import FacebookSVG from 'features/affiliates/assets/FacebookSVG';
import { DataType, fakeData } from '../../util/affiliateDummyData';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { Item } from 'features/policy/components/PolicyDetails/StyledComponents';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import VideoSVG from 'features/affiliates/assets/VideoSVG';
import SharePostModal from 'features/affiliates/components/SharePostModal';
import { useGetRecentPostList } from 'features/affiliates/hooks/useGetRecentPostList';
import {
  AffiliateDashboardRecentPost,
  AffiliateDashboardRecentPosts,
} from 'types/affiliateDashboard';
import { useGetCategoryList } from 'features/affiliates/hooks/useGetCategoryList';
import { AffiliateCategoryList } from 'features/affiliates/types/types';
import NoDataFound from 'features/eRecruit/ib/tablet/applicationStatus/NoDataFound';
import { EmptyRecord } from 'features/affiliates/components/EmptyRecord';
import EmptyCaseSVG from 'features/teamManagement/assets/EmptyCaseSVG';
import { useTranslation } from 'react-i18next';

export default function MakeAPost({
  isLoading,
  data,
}: {
  isLoading: boolean;
  data: AffiliateDashboardRecentPosts;
}) {
  const { t } = useTranslation('affiliates');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors, space } = useTheme();
  const [filterBy, setFilterBy] = useState<
    'Social_Media_Post' | 'FWD_Post' | null
  >(null);
  const [postFilter, setPostFilter] = useState(null);
  const [topicFilter, setTopicFilter] = useState<string[] | []>([]);
  const [materialsFilter, setMaterialFilter] = useState<string | null>(null);
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [isShownSortingDropdown, setIsShownSortingDropdown] = useState(false);
  const [sortingBy, setSortingBy] = useState<
    'Newest' | 'Oldest' | 'Most popular'
  >('Newest');

  const { isLoading: isLoadingCategory, data: categoryData } =
    useGetCategoryList();
  // const { isLoading, data } = useGetRecentPostList();

  const gatherUniqueCategoryIds = (
    data: AffiliateDashboardRecentPosts | undefined,
  ) => {
    if (!data || !categoryData) return [];

    // Use a Set for efficient uniqueness check
    const uniqueCategoryIds = new Set<string>();

    // Simplify data iteration and uniqueness check
    data.forEach(item => {
      if (item.categoryId1) uniqueCategoryIds.add(item.categoryId1);
      if (item.categoryId2) uniqueCategoryIds.add(item.categoryId2);
    });

    // Map and filter in one iteration
    return Array.from(uniqueCategoryIds).map(id => {
      const category = categoryData.find(
        category => category.id === parseInt(id),
      );
      return {
        id: id,
        label: category ? category.categoryName : 'Unknown',
      };
    });
  };

  const allTopics = gatherUniqueCategoryIds(data).filter(
    topic => topic !== undefined,
  );

  const filteredAndSortedData = useMemo(() => {
    if (!data) return [];
    let result = data;

    // Filter logic
    if (filterBy) {
      if (filterBy === 'FWD_Post') {
        result = result.filter(item => item.postType === filterBy);
      } else if (filterBy === 'Social_Media_Post') {
        result = result.filter(item => item.postType === 'Social_Media_Post');
      }
    }

    if (topicFilter && topicFilter.length > 0) {
      result = result.filter(
        item =>
          topicFilter.includes(item.categoryId1) ||
          topicFilter.includes(item.categoryId2),
      );
    }

    if (materialsFilter) {
      if (materialsFilter == 'video')
        result = result.filter(item => item.videoCount > 0);
      if (materialsFilter == 'image')
        result = result.filter(item => item.imageCount > 0);
    }

    // Sorting logic
    switch (sortingBy) {
      case 'Newest':
        result.sort(
          (a, b) =>
            new Date(b.publishDate).getTime() -
            new Date(a.publishDate).getTime(),
        );
        break;
      case 'Oldest':
        result.sort(
          (a, b) =>
            new Date(a.publishDate).getTime() -
            new Date(b.publishDate).getTime(),
        );
        break;
      case 'Most popular':
        result.sort((a, b) => b.viewIn90Days - a.viewIn90Days);
        break;
      default:
        // No sorting or default sorting logic here
        break;
    }

    return result;
  }, [data, filterBy, sortingBy, topicFilter, materialsFilter]);

  const count = filteredAndSortedData ? filteredAndSortedData.length : 0;

  // const allTopics = [...new Set(fakeData.map(item => item.category).flat())];
  // console.log('categoryData:', JSON.stringify(categoryData));
  // console.log('unq', allTopics);
  // console.log('makeAPost data:', JSON.stringify(data));
  // console.log('filteredAndSortedData: ', JSON.stringify(filteredAndSortedData));
  // console.log('topicFilter:', topicFilter);
  // console.log('materialsFilter:', materialsFilter);

  return (
    <Column
      backgroundColor={colors.palette.fwdGrey[50]}
      height={'100%'}
      px={space[8]}>
      <Box flex={1} gap={space[2]}>
        <FilterBar
          filterBy={filterBy}
          setFilterBy={setFilterBy}
          isOpenFilter={isOpenFilter}
          setIsOpenFilter={setIsOpenFilter}
          isShownSortingDropdown={isShownSortingDropdown}
          setIsShownSortingDropdown={setIsShownSortingDropdown}
          sortingBy={sortingBy}
          setSortingBy={setSortingBy}
          count={count}
          allTopics={allTopics}
          topicFilter={topicFilter}
          setTopicFilter={setTopicFilter}
          materialsFilter={materialsFilter}
          setMaterialFilter={setMaterialFilter}
        />

        <Container>
          <FlashList
            // keyExtractor={item => item.id.toString()}
            contentContainerStyle={{
              paddingTop: space[5],
              paddingBottom: 300,
            }}
            ListEmptyComponent={
              isLoading ? (
                <Box
                  minHeight={60}
                  justifyContent="center"
                  alignItems="center"
                  borderBottomRadius={16}>
                  <Box h={space[5]} width={space[5]}>
                    <LoadingIndicator size={space[5]} />
                  </Box>
                </Box>
              ) : (
                <Column
                  // backgroundColor={colors.background}
                  justifyContent="center"
                  alignItems="center"
                  style={{
                    paddingVertical: space[5],
                    borderBottomLeftRadius: space[4],
                    borderBottomRightRadius: space[4],
                  }}>
                  <EmptyCaseSVG width={100} height={space[21]} />
                  <Typography.LargeBody color={colors.palette.fwdGreyDarker}>
                    {t('affiliates.emptyRecord')}
                  </Typography.LargeBody>
                </Column>
              )
            }
            estimatedItemSize={100}
            data={filteredAndSortedData}
            renderItem={({ item }: { item: AffiliateDashboardRecentPost }) => (
              <MakeAPostCard item={item} categoryData={categoryData} />
            )}
          />
        </Container>
      </Box>
    </Column>
  );
}

export function MakeAPostCard({
  item,

  categoryData,
}: {
  item: AffiliateDashboardRecentPost;

  categoryData: AffiliateCategoryList | undefined;
}) {
  const { t } = useTranslation('affiliates');
  const [isShareModalVisible, setIsShareModalVisible] = useState(false);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors, space } = useTheme();

  const categoryName1 = categoryData?.find(
    category => category.id === parseInt(item.categoryId1 ?? ''),
  )?.categoryName;

  const categoryName2 = categoryData?.find(
    category => category.id === parseInt(item.categoryId2 ?? ''),
  )?.categoryName;

  return (
    <>
      <SharePostModal
        isVisible={isShareModalVisible}
        onClose={() => {
          setIsShareModalVisible(false);
        }}
        sharePostDetails={{
          id: item.id,
          bodyTitle: item.bodyTitle,
          bodyContent: item.bodyContent,
          postType: item.postType,
          articleUrl: item.articleUrl,
          imageUrl: item?.postFiles?.[0]?.fileUrl,
        }}
      />
      <TouchableOpacity
        key={item.id}
        onPress={() => {
          navigation.navigate('Affiliate');
          navigation.navigate('AffiliatePostDetails', { id: item.id });
          console.log('press on card');
        }}
        style={{
          zIndex: 2,
        }}>
        <Row
          marginBottom={space[6]}
          justifyContent="space-between"
          height={126}
          backgroundColor={colors.background}
          borderRadius={space[4]}
          marginLeft={10}
          // alignItems="center"
          pr={space[5]}
          paddingBottom={space[4]}>
          <Image
            placeholder={require('features/affiliates/assets/placeholder.png')}
            placeholderContentFit="cover"
            source={{ uri: item?.postFiles?.[0]?.fileUrl }}
            style={{
              width: 150,
              height: 96,
              borderRadius: space[4],
              position: 'absolute',
              top: -13,
              left: -10,
            }}
          />
          {/* i am separator line */}
          <Box boxSize={16 + 150 - 10} />
          {item.recommend === 'Y' && (
            <Box
              position="absolute"
              top={-space[4]}
              left={155}
              // maxW={100}
              paddingX={space[2]}
              paddingY={space[1]}
              borderRadius={space[3]}
              borderBottomLeftRadius={0}
              backgroundColor={colors.palette.fwdYellow[100]}>
              <Typography.SmallBody>
                {t('affiliates.recommended')}
              </Typography.SmallBody>
            </Box>
          )}
          <Column paddingTop={space[5]} gap={space[2]}>
            {item.postType == 'Social_Media_Post' && (
              <Row gap={space[1]}>
                <FacebookSVG height={14} width={14} />
                <PurpleInstagramSVG height={14} width={14} />
                <SmallLabel
                  fontWeight="medium"
                  color={colors.palette.fwdBlue[100]}>
                  {t('affiliates.madeForFacebook')}
                </SmallLabel>
              </Row>
            )}
            <Row gap={space[2]}>
              <Typography.SmallLabel>{categoryName1}</Typography.SmallLabel>
              {item.categoryId2 && (
                <>
                  <Typography.SmallLabel color={colors.palette.fwdGrey[100]}>
                    {'|'}
                  </Typography.SmallLabel>

                  <Typography.SmallLabel>{categoryName2}</Typography.SmallLabel>
                </>
              )}
            </Row>
            <Box width={342}>
              <Typography.LargeLabel fontWeight="medium">
                {item.bodyTitle}
              </Typography.LargeLabel>
            </Box>
          </Column>
          {/* i am separator line */}
          <Box boxSize={space[10]} />
          {item.commission !== '' && item.commission ? (
            <Column paddingTop={space[5]} gap={space[2]} width={105}>
              <Typography.SmallLabel
                fontWeight="medium"
                color={colors.palette.fwdBlue[100]}>
                {item.postType === 'Online_Product'
                  ? t('affiliates.onlineProduct')
                  : t('affiliates.agencyProduct')}
              </Typography.SmallLabel>
              {item.commission && (
                <Box width={105}>
                  <Typography.SmallLabel>
                    {item.commission}
                  </Typography.SmallLabel>
                </Box>
              )}
            </Column>
          ) : (
            <Column
              width={105}
              paddingTop={20}
              justifyContent="center"
              gap={space[2]}>
              <Typography.SmallLabel
                fontWeight="medium"
                color={colors.palette.fwdBlue[100]}>
                {item.postType === 'Online_Product'
                  ? t('affiliates.onlineProduct')
                  : t('affiliates.agencyProduct')}
              </Typography.SmallLabel>
            </Column>
          )}
          {/* i am separator line */}
          <Box boxSize={space[10]} />
          <Row
            alignItems="center"
            justifyContent="flex-start"
            paddingTop={20}
            gap={space[3]}
            width={117}>
            {item.imageCount > 0 && (
              <Row gap={1} alignItems="center">
                <Icon.Photo fill={colors.onBackground} size={18} />
                <Typography.Label>{item.imageCount}</Typography.Label>
              </Row>
            )}
            {item.videoCount > 0 && (
              <Row gap={1} alignItems="center">
                <VideoSVG height={18} width={18} />
                <Typography.Label>{item.videoCount}</Typography.Label>
              </Row>
            )}
          </Row>
          {/* i am separator line */}
          {/* <Box boxSize={space[10]} /> */}
          <Row paddingTop={20} alignItems="center">
            <TouchableOpacity
              onPress={() => {
                setIsShareModalVisible(true);
              }}
              style={{
                zIndex: 3,
                borderRadius: space[10],
                width: space[9],
                height: space[9],
                backgroundColor: colors.palette.fwdOrange[5],
                borderWidth: 1,
                borderColor: colors.palette.fwdOrange[20],
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Icon.Share fill={colors.palette.fwdOrange[100]} size={20} />
            </TouchableOpacity>
            {/* i am separator line */}
            <Box boxSize={space[5]} />
            <TouchableOpacity>
              <Icon.ChevronRight
                fill={colors.palette.fwdOrange[100]}
                size={24}
              />
            </TouchableOpacity>
          </Row>
        </Row>
      </TouchableOpacity>
    </>
  );
}

const Container = styled.View(() => ({
  flex: 1,
}));
