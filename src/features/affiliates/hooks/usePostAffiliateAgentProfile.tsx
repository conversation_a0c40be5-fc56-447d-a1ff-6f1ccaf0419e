import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postAffiliateAgentProfile } from 'api/affiliateApi';
import { AffiliateAgentProfileRequest } from 'types/affiliateDashboard';

export function usePostAffiliateAgentProfile() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ data }: { data: AffiliateAgentProfileRequest }) => {
      return postAffiliateAgentProfile(data);
    },
    onSuccess: async () => {
      console.log('AffiliateAgentProfileRequest updated');
      await queryClient.invalidateQueries(['AffiliateAgentProfileRequest']);
    },
  });
}
