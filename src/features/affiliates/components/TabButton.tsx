import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
} from 'react-native';
import React from 'react';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export default function TabButton(
  props: {
    isActive: boolean;
    label: string;
    containerStyle?: ViewStyle;
    Icon?: React.ReactNode;
    rightIcon?: React.ReactNode;
    isActiveMedium?: boolean;
  } & TouchableOpacityProps,
) {
  const { colors, space, borderRadius } = useTheme();
  const { isActive, label, containerStyle, isActiveMedium, ...rest } = props;
  return (
    <TouchableOpacity
      style={[
        {
          flex: 1,
          borderWidth: isActive ? 2 : 1,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: borderRadius['x-large'],
          paddingHorizontal: space[4],
          paddingVertical: space[2],
          borderColor: isActive ? colors.primary : colors.palette.fwdGrey[100],
          backgroundColor: isActive
            ? colors.primaryVariant3
            : colors.background,
        },
        containerStyle,
      ]}
      {...rest}>
      <Row alignItems="center" gap={space[1]}>
        {props?.Icon}
        <Typography.Label
          fontWeight={
            isActive ? (isActiveMedium ? 'normal' : 'medium') : 'normal'
          }
          color={colors.secondary}>
          {label}
        </Typography.Label>
        {props?.rightIcon}
      </Row>
    </TouchableOpacity>
  );
}
