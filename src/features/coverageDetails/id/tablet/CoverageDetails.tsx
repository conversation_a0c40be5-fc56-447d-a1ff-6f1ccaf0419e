import { useTheme } from '@emotion/react';
import { Header } from '@react-navigation/elements';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, H7, Icon, Row, Typography } from 'cube-ui-components';

import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { RootStackParamList } from 'types';

import GATracking from 'utils/helper/gaTracking';
import CoverageIndividualDetails from '../../components/id/tablet/CoverageIndividualDetails';

const CoverageDetailsScreenTablet = () => {
  const { t } = useTranslation(['coverageDetails']);
  const { colors, space } = useTheme();
  const { canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const onBackPress = () => {
    if (!canGoBack()) return;

    GATracking.logButtonPress({
      screenName: 'CoverageDetails',
      screenClass: 'Sales flow',
      actionType: 'non_cta_button',
      buttonName: 'Back',
    });
    goBack();
  };

  return (
    <Box bgColor={colors.surface} flex={1}>
      <View>
        <Header
          headerTitle={({ children }) => (
            <Typography.H6 fontWeight="bold" color={colors.onBackground}>
              {children}
            </Typography.H6>
          )}
          headerTitleAlign="left"
          title={t('coverageDetails:createSI')}
          headerLeft={() => (
            <TouchableOpacity onPress={onBackPress}>
              <Icon.ArrowLeft size={space[6]} fill={colors.palette.black} />
            </TouchableOpacity>
          )}
          headerLeftContainerStyle={{
            paddingLeft: space[4],
            justifyContent: 'center',
          }}
          headerRight={() => (
            <TouchableOpacity onPress={onBackPress}>
              <Row alignItems="center" gap={space[1]}>
                <Icon.Home size={space[6]} fill={colors.palette.black} />
                <H7 fontWeight="bold">{t('coverageDetails:home')}</H7>
              </Row>
            </TouchableOpacity>
          )}
          headerRightContainerStyle={{
            paddingRight: space[3],
            justifyContent: 'center',
          }}
          headerStyle={{
            backgroundColor: colors.background,
            borderBottomWidth: 1,
          }}
        />
      </View>

      <Box flex={1} marginTop={space[5]}>
        <CoverageIndividualDetails />
      </Box>
    </Box>
  );
};

export default memo(CoverageDetailsScreenTablet);
