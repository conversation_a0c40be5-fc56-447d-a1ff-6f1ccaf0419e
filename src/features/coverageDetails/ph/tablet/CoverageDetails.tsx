import { useTheme } from '@emotion/react';
import { Header } from '@react-navigation/elements';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  Box,
  Chip,
  Column,
  H7,
  Icon,
  Row,
  Typography,
} from 'cube-ui-components';

import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { RootStackParamList } from 'types';

import { SaleIllustrationTargetType } from 'features/home/<USER>/CreateSaleIllustrationModal/types';

import CoverageEntityDetails from 'features/coverageDetails/components/ph/tablet/CoverageEntityDetails';
import { useIdsFromRoute } from 'features/coverageDetails/hooks/common/useCoverageForm';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { noop } from 'lodash';
import GATracking from 'utils/helper/gaTracking';
import CoverageIndividualDetails from '../../components/ph/tablet/CoverageIndividualDetails';

const CoverageDetailsScreenTablet = () => {
  const { t } = useTranslation(['coverageDetails']);
  const { colors, space, sizes } = useTheme();
  const { leadId } = useIdsFromRoute();
  const { data: lead } = useGetLeadByLeadId(leadId);
  const isEntity = lead && !lead.isIndividual;
  const targetClientType = isEntity
    ? SaleIllustrationTargetType.ENTITY
    : SaleIllustrationTargetType.INDIVIDUAL;

  const { canGoBack, goBack } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const onBackPress = () => {
    if (canGoBack()) {
      goBack();
      GATracking.logButtonPress({
        screenName: 'CoverageDetails',
        screenClass: 'Sales flow',
        actionType: 'non_cta_button',
        buttonName: 'Back',
      });
    }
  };

  return (
    <Box bgColor={colors.surface} flex={1}>
      <View>
        <Header
          headerTitle={({ children }) => (
            <Typography.H6 fontWeight="bold" color={colors.onBackground}>
              {children}
            </Typography.H6>
          )}
          headerTitleAlign="left"
          title={t('coverageDetails:coverageDetails')}
          headerLeft={() => (
            <TouchableOpacity onPress={onBackPress}>
              <Icon.ArrowLeft size={space[6]} fill={colors.palette.black} />
            </TouchableOpacity>
          )}
          headerLeftContainerStyle={{
            paddingLeft: space[4],
            justifyContent: 'center',
          }}
          headerRight={() => (
            <TouchableOpacity onPress={onBackPress}>
              <Row alignItems="center" gap={space[1]}>
                <Icon.Home size={space[6]} fill={colors.palette.black} />
                <H7 fontWeight="bold">{t('coverageDetails:home')}</H7>
              </Row>
            </TouchableOpacity>
          )}
          headerRightContainerStyle={{
            paddingRight: space[3],
            justifyContent: 'center',
          }}
          headerStyle={{
            backgroundColor: colors.background,
            borderBottomWidth: 1,
          }}
        />
      </View>

      <Row
        marginTop={space[5]}
        marginBottom={space[4]}
        justifyContent="center"
        gap={space[5]}>
        <Column width={space[45]}>
          <Chip
            onPress={noop}
            icon={() => (
              <Icon.Account
                size={sizes[5]}
                fill={
                  targetClientType === SaleIllustrationTargetType.INDIVIDUAL
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100]
                }
              />
            )}
            label={t('coverageDetails:individual')}
            disabled={
              targetClientType !== SaleIllustrationTargetType.INDIVIDUAL
            }
            focus={targetClientType === SaleIllustrationTargetType.INDIVIDUAL}
            textPosition="right"
            size="large"
            stretch
          />
        </Column>

        <Column width={space[45]}>
          <Chip
            onPress={noop}
            icon={() => (
              <Icon.Office
                size={sizes[5]}
                fill={
                  targetClientType === SaleIllustrationTargetType.ENTITY
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100]
                }
              />
            )}
            label={t('coverageDetails:entity')}
            disabled={targetClientType !== SaleIllustrationTargetType.ENTITY}
            focus={targetClientType === SaleIllustrationTargetType.ENTITY}
            textPosition="right"
            size="large"
            stretch
          />
        </Column>
      </Row>

      {targetClientType === SaleIllustrationTargetType.ENTITY && (
        <CoverageEntityDetails />
      )}

      {targetClientType === SaleIllustrationTargetType.INDIVIDUAL && (
        <CoverageIndividualDetails />
      )}
    </Box>
  );
};

export default memo(CoverageDetailsScreenTablet);
