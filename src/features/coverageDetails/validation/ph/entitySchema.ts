import { PH_MOBILE_CODE } from 'constants/optionList';
import { PartyType } from 'types/party';
import { object, string, InferType, mixed } from 'yup';
import { TFuncKey } from 'i18next';
import parsePhoneNumberFromString from 'libphonenumber-js';
import { emailRegex, entityNameRegex } from 'constants/regex';
import { formPhoneNumber } from 'utils/validation/customValidation';

export const REQUIRED_INPUT: TFuncKey<['coverageDetails']> =
  'coverageDetails:validation.requiredInput';
export const INVALID_FORMAT: TFuncKey<['coverageDetails']> =
  'coverageDetails:validation.invalidFormat';
export const INVALID_DATE: TFuncKey<['coverageDetails']> =
  'coverageDetails:validation.invalidDate';

export const ENTITY_NAME_MAX_LENGTH_MESSAGE: TFuncKey<['coverageDetails']> =
  'coverageDetails:validation.error.maxLength120';

export const maximumEntityNameLength = 120;
export const maximumMobile = 16;
export const maximumEmail = 50;

export const titleMales = ['MR', 'REV', 'FR'];
export const titleFemales = ['MRS', 'MS', 'SR'];

export type EntityFormSchemaType = InferType<typeof entityFormValidationSchema>;
export type EntityFormSchemaKey = keyof EntityFormSchemaType;

export const initialEntityFormData: EntityFormSchemaType = {
  clientType: PartyType.ENTITY,
  code: PH_MOBILE_CODE,
  secondaryCode: PH_MOBILE_CODE,
  phoneMobile: '',
  secondaryPhoneMobile: '',
  email: '',
  name: '',
  natureOfBusiness: '',
  leadSource: '',
};
const convertPhoneNumber = (str: string, countryCode: string) => {
  const cleanedInput = str.replace(/\D/g, '');
  return countryCode + cleanedInput;
};
export const entityFormRequiredSchema = {
  leadId: string(),
  clientType: mixed<PartyType>()
    .oneOf(Object.values(PartyType))
    .required(REQUIRED_INPUT),
  name: string()
    .max(maximumEntityNameLength, ENTITY_NAME_MAX_LENGTH_MESSAGE)
    .required(REQUIRED_INPUT)
    .matches(entityNameRegex, {
      excludeEmptyString: true,
      message: INVALID_FORMAT,
    }),
  natureOfBusiness: string().required(REQUIRED_INPUT),
  leadSource: string().required(REQUIRED_INPUT),
  code: string().required(REQUIRED_INPUT),
  phoneMobile: formPhoneNumber('code').required(REQUIRED_INPUT),
  email: string()
    .required(REQUIRED_INPUT)
    .trim()
    .lowercase()
    .max(maximumEmail, INVALID_FORMAT)
    .matches(emailRegex, {
      excludeEmptyString: true,
      message: INVALID_FORMAT,
    }),
  secondaryCode: string().required(REQUIRED_INPUT),
  secondaryPhoneMobile: string()
    .required(REQUIRED_INPUT)
    .trim()
    .max(maximumMobile, INVALID_FORMAT)
    .test('secondaryPhoneMobile', INVALID_FORMAT, (value, { parent }) => {
      const countryCode = `+${parent.secondaryCode.split('-')[0]}`;
      const phoneNum = convertPhoneNumber(value, countryCode);
      if (!value) {
        return true;
      }
      const phoneNumber = parsePhoneNumberFromString(
        value,
        parsePhoneNumberFromString(phoneNum)?.country,
      );
      return phoneNumber && phoneNumber.isValid();
    }),
} as const;

export type EntityFormRequiredSchemaKey = keyof typeof entityFormRequiredSchema;

export const entityFormRequiredFields = Object.keys(
  entityFormRequiredSchema,
) as EntityFormRequiredSchemaKey[];

export const entityFormValidationSchema = object(entityFormRequiredSchema);
