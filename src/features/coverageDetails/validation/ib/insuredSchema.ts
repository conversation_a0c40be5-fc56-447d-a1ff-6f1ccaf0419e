import { ageLimit } from 'constants/dateOfBirth';
import { MY_COUNTRY, MY_MOBILE_CODE } from 'constants/optionList';
import { RelationshipValue } from 'features/proposal/types';
import { t } from 'i18next';
import { calculateAgeDiff } from 'utils/helper/calculateAge';
import validateSanctionCountry from 'utils/validation/ib/sanctionCountryValidation';
import { date, object, string } from 'yup';
import {
  INSURED_MAX_AGE,
  INSURED_MIN_AGE,
  INVALID_FORMAT,
  MAX_NAME_LENGHT,
  REQUIRED_INPUT,
} from '../common/constant';
import {
  InsuredFormBaseValues,
  insuredFormBaseDefaultValues,
  insuredFormSchemaBase,
} from '../common/insuredSchema.base';

interface IbInsuredFormValues extends Omit<InsuredFormBaseValues, 'dob'> {
  dob?: Date;
  smokingHabit: string;
  nationality: string;
  residence: string;
  religion: string;
  occupation: string;
  occupationClass: string;
  relationship: string;
  nanoRelationship: string;
  code: string;
}

export const ibInsuredFormSchema = object({
  ...insuredFormSchemaBase,
  dob: date()
    .required(REQUIRED_INPUT)
    .test({
      name: 'dob-validation',
      test: (value, ctx) => {
        const nanoRelationship = ctx.parent
          ?.nanoRelationship as RelationshipValue;

        const agentChannel = ctx.options.context?.agentChannel;

        // ignore the validation if the agent channel is not defined
        if (!agentChannel) {
          return true;
        }

        const minAge = ageLimit[agentChannel]?.[nanoRelationship]?.min?.value;
        const minAgeUnit =
          ageLimit[agentChannel]?.[nanoRelationship]?.min?.unit;

        const maxAge = ageLimit[agentChannel]?.[nanoRelationship]?.max?.value;
        const maxAgeUnit =
          ageLimit[agentChannel]?.[nanoRelationship]?.max?.unit;

        const minAgeDiff = calculateAgeDiff(value, minAgeUnit);
        const maxAgeDiff = calculateAgeDiff(value, maxAgeUnit);

        if (minAge && minAgeDiff < minAge) {
          return ctx.createError({
            message: t(INSURED_MIN_AGE, {
              age: `${minAge} ${
                minAgeUnit?.includes('year')
                  ? 'ANB'
                  : t(`common:${minAgeUnit}` as unknown as TemplateStringsArray)
              }`,
            }),
          });
        }

        if (maxAge && maxAgeDiff > maxAge) {
          return ctx.createError({
            message: t(INSURED_MAX_AGE, {
              age: `${maxAge} ${
                maxAgeUnit?.includes('year')
                  ? 'ANB'
                  : t(`common:${maxAgeUnit}` as unknown as TemplateStringsArray)
              }`,
            }),
          });
        }

        return !!value;
      },
    }),

  firstName: string()
    .required(REQUIRED_INPUT)
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_FORMAT),
  smokingHabit: string().required(REQUIRED_INPUT),
  nationality: string()
    .required(REQUIRED_INPUT)
    .test({
      name: 'sanction-country-validation',
      test: (value, ctx) => validateSanctionCountry(value, ctx, 'NATIONALITY'),
    }),
  residence: string()
    .required(REQUIRED_INPUT)
    .test({
      name: 'sanction-country-validation',
      test: (value, ctx) => validateSanctionCountry(value, ctx, 'COUNTRY'),
    }),
  residencyType: string(),
  religion: string().required(REQUIRED_INPUT),
  occupation: string()
    .required(REQUIRED_INPUT)
    .test({
      name: 'occ-validation',
      test: (value, ctx) => {
        const dob = ctx.parent.dob;
        const nanoRelationship = ctx.parent
          ?.nanoRelationship as RelationshipValue;

        const agentChannel = ctx.options.context?.agentChannel;
        const optionList = ctx.options.context?.optionList;
        const occupationOptions = optionList?.OCCUPATION?.options;
        const studentOccupationOption = occupationOptions?.find(
          (o: { label: string }) => o?.label === 'Student',
        );

        const minStudentAge = ageLimit[agentChannel]?.STUDENT?.min?.value;
        const maxStudentAge = ageLimit[agentChannel]?.STUDENT?.max?.value;

        const maxAgeUnit = ageLimit[agentChannel]?.STUDENT?.max?.unit;
        const minAgeUnit = ageLimit[agentChannel]?.STUDENT?.min?.unit;

        const minAgeDiff = calculateAgeDiff(dob, minAgeUnit);
        const maxAgeDiff = calculateAgeDiff(dob, maxAgeUnit);

        if (
          maxStudentAge &&
          minStudentAge &&
          minAgeDiff >= minStudentAge &&
          maxAgeDiff <= maxStudentAge &&
          nanoRelationship === RelationshipValue.CHILD &&
          value !== studentOccupationOption?.value
        ) {
          return ctx.createError({
            message: t('coverageDetails:validation.invalidOccupation'),
          });
        }

        return !!value;
      },
    }),
  occupationClass: string().required(REQUIRED_INPUT),
  relationship: string().required(REQUIRED_INPUT),
  nanoRelationship: string().required(REQUIRED_INPUT),
  lastName: string().nullable(),
});

export const ibInsuredFormSchemaDefaultValues: IbInsuredFormValues = {
  ...insuredFormBaseDefaultValues,
  code: MY_MOBILE_CODE,
  dob: undefined,
  smokingHabit: '',
  nationality: MY_COUNTRY,
  residence: MY_COUNTRY,
  religion: '',
  occupation: '',
  occupationClass: '',
  relationship: '',
  nanoRelationship: '',
};
