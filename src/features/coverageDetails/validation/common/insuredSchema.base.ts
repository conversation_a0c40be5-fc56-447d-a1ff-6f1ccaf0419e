import { PartyType } from 'types/party';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { date, string, mixed, boolean } from 'yup';
import {
  INVALID_FORMAT,
  INVALID_NAME_VALUE,
  MAX_NAME_LENGHT,
  REQUIRED_INPUT,
} from './constant';

const { defaultDate: dateOfBirthDefaultDate } = getDateOfBirthDropdownProps();

export const convertPhoneNumber = (str: string, countryCode: string) => {
  const cleanedInput = str.replace(/\D/g, '');
  return countryCode + cleanedInput;
};

export interface InsuredFormBaseValues {
  leadId?: string;
  dob: Date;
  gender: string;
  clientType: PartyType;
  firstName: string;
  lastName?: string;
  isEntityPolicyOwner?: boolean;
}

export const insuredFormSchemaBase = {
  leadId: string().nullable(),
  dob: date().required(REQUIRED_INPUT),
  gender: string().required(REQUIRED_INPUT),
  clientType: mixed<PartyType>()
    .oneOf(Object.values(PartyType))
    .required(REQUIRED_INPUT),
  firstName: string()
    .required(REQUIRED_INPUT)
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_NAME_VALUE),
  lastName: string()
    .nullable()
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_NAME_VALUE),
  isEntityPolicyOwner: boolean().optional(),
};

export const insuredFormBaseDefaultValues: InsuredFormBaseValues = {
  dob: dateOfBirthDefaultDate,
  gender: '',
  clientType: PartyType.INDIVIDUAL,
  firstName: '',
};
