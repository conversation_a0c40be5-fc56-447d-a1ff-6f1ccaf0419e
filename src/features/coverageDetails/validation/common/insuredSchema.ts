import { country } from 'utils/context';
import * as yup from 'yup';

import {
  myInsuredFormSchema,
  myInsuredFormSchemaDefaultValues,
} from '../my/insuredSchema';

import {
  ibInsuredFormSchema,
  ibInsuredFormSchemaDefaultValues,
} from '../ib/insuredSchema';

import { DefaultValues } from 'react-hook-form';
import { BuildCountry } from 'types';
import {
  phInsuredFormSchema,
  phInsuredFormSchemaDefaultValues,
} from '../ph/insuredSchema';
import {
  insuredFormBaseDefaultValues,
  insuredFormSchemaBase,
} from './insuredSchema.base';
import {
  idInsuredFormSchema,
  idInsuredFormSchemaDefaultValues,
} from '../id/insuredSchema';

let insuredFormValidationSchema;
const baseScehma = yup.object(insuredFormSchemaBase);
switch (country) {
  case 'ph':
    insuredFormValidationSchema = phInsuredFormSchema;
    break;
  case 'my':
    insuredFormValidationSchema = myInsuredFormSchema;
    break;
  case 'ib':
    insuredFormValidationSchema = ibInsuredFormSchema;
    break;
  case 'id':
    insuredFormValidationSchema = idInsuredFormSchema;
    break;

  default:
    insuredFormValidationSchema = baseScehma;
}

export type PhInsuredFormValues = yup.InferType<typeof phInsuredFormSchema>;
export type IbInsuredFormValues = yup.InferType<typeof ibInsuredFormSchema>;
export type MyInsuredFormValues = yup.InferType<typeof myInsuredFormSchema>;
export type IdInsuredFormValues = yup.InferType<typeof idInsuredFormSchema>;
export type BaseInsuredFormValues = yup.InferType<typeof baseScehma>;

export type InsuredFormValues<country extends BuildCountry = BuildCountry> =
  country extends 'ph'
    ? PhInsuredFormValues
    : country extends 'ib'
    ? IbInsuredFormValues
    : country extends 'my'
    ? MyInsuredFormValues
    : country extends 'id'
    ? IdInsuredFormValues
    : never;

let insuredFormDefaultValues: DefaultValues<InsuredFormValues>;
switch (country) {
  case 'ph':
    insuredFormDefaultValues = phInsuredFormSchemaDefaultValues;
    break;
  case 'my':
    insuredFormDefaultValues = myInsuredFormSchemaDefaultValues;
    break;
  case 'ib':
    insuredFormDefaultValues = ibInsuredFormSchemaDefaultValues;
    break;
  case 'id':
    insuredFormDefaultValues = idInsuredFormSchemaDefaultValues;
    break;
  default:
    insuredFormDefaultValues = insuredFormBaseDefaultValues;
}

export default {
  insuredFormValidationSchema,
  insuredFormDefaultValues,
};
