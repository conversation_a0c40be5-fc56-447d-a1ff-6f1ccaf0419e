import { PartyType } from 'types/party';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { formPhoneNumber } from 'utils/validation/customValidation';
import { date, string, mixed } from 'yup';
import {
  EMAIL_VALIDATE_REGEX,
  INVALID_FORMAT,
  INVALID_NAME_VALUE,
  MAX_NAME_LENGHT,
  REQUIRED_INPUT,
} from './constant';

const { defaultDate: dateOfBirthDefaultDate } = getDateOfBirthDropdownProps();

export const convertPhoneNumber = (str: string, countryCode: string) => {
  const cleanedInput = str.replace(/\D/g, '');
  return `+${countryCode}${cleanedInput}`;
};

export interface OwnerFormBaseValues {
  leadId?: string;
  dob: Date;
  gender: string;
  clientType: PartyType;
  code: string;
  phoneMobile: string;
  email?: string;
  title?: string;
  firstName: string;
  lastName?: string;
}

export const ownerFormSchemaBase = {
  dob: date().required(REQUIRED_INPUT),
  gender: string().required(REQUIRED_INPUT),
  clientType: mixed<PartyType>()
    .oneOf(Object.values(PartyType))
    .required(REQUIRED_INPUT),
  code: string().required(REQUIRED_INPUT),
  phoneMobile: formPhoneNumber('code').required(REQUIRED_INPUT),
  email: string().nullable().trim().lowercase().matches(EMAIL_VALIDATE_REGEX, {
    excludeEmptyString: true,
    message: INVALID_FORMAT,
  }),
  title: string().nullable(),
  firstName: string()
    .required(REQUIRED_INPUT)
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_NAME_VALUE),
  lastName: string()
    .nullable()
    .max(MAX_NAME_LENGHT, INVALID_FORMAT)
    .validateName(INVALID_NAME_VALUE),
};

export const ownerFormBaseDefaultValues: OwnerFormBaseValues = {
  dob: dateOfBirthDefaultDate,
  gender: '',
  clientType: PartyType.INDIVIDUAL,
  code: '',
  phoneMobile: '',
  email: '',
  title: '',
  firstName: '',
};
