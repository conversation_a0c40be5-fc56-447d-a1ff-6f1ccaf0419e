import { yupResolver } from '@hookform/resolvers/yup';
import { FieldValues, Resolver } from 'react-hook-form';
import { BuildCountry } from 'types';
import { country } from 'utils/context';
import * as yup from 'yup';

export const getFormConfig = <T extends FieldValues>(
  schemas: Partial<{
    [key in BuildCountry]: yup.ObjectSchema<T>;
  }>,
):
  | {
      schema: yup.ObjectSchema<T>;
      resolver: Resolver<T>;
      fields: string[];
    }
  | undefined => {
  const schema = schemas[country] as yup.ObjectSchema<T> | undefined;

  if (!schema) {
    return;
  }

  return {
    schema,
    fields: Object.keys(schema),
    resolver: yupResolver(schema) as unknown as Resolver<T>,
  };
};
