export default {
  coverageDetails: 'Coverage details',
  insuredPolicyOwner: 'Policy owner is the insured',
  saveAndGenerate: 'Save & generate plan',
  popupTitle: 'Is it an existing lead/ opportunity?',
  yes: 'Yes',
  popupContent: 'Click “Yes” to retrieve data',
  aboutPolicyOwner: 'About policy owner (myself)',
  aboutPolicyInsured: 'About policy insured',
  aboutPolicyOwnerContent:
    'Importance of Truthful Disclosure: It’s important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy ',
  close: 'Close',
  more: '...more',
  samePersonTitle: 'Is the policy owner the same person?',
  samePersonContent:
    'We found someone having these information in your record.',
  samePersonMobile: 'mobile',
  samePersonEmail: 'email',
  no: 'No',
  searchExistingLead: 'Search for existing lead/ opportunity',
  searchMobileOrEmail: 'Search mobile or email',
  search: 'Search',
  clientType: 'Client type',
  code: 'Code',
  mobile: 'Mobile',
  email: 'Email',
  salutationTitle: 'Salutation/Title',
  firstName: 'First name',
  middleName: 'Middle name (optional)',
  lastName: 'Last name',
  extensionName: 'Extension name (optional)',
  dateOfBirth: 'Date of birth',
  gender: 'Gender',
  male: 'Male',
  female: 'Female',
  open: 'Open',
  relationship: 'Relationship',
  relationshipToOwner: 'Relationship to owner',
  age: 'Age',
  personalInfo: 'Personal info',
  contactDetails: 'Contact details',
  primaryContactDetails: 'Primary contact details',
  secondaryContactDetails: 'Secondary contact details',
  entityName: 'Entity name',
  businessNature: 'Industry / Nature of business',
  leadSource: 'Lead source',
  createNewInsured: 'Or create new insured',
  searchExistingInsured: 'Search existing insured',
  emptyExistingInsured:
    'No pre-existing insured available. Please try another search.',
  existingInsured: 'Existing insured',
  insureds: 'Insureds',
  searchInsuredName: 'Search insured name',
  done: 'Done',
  reset: 'Reset',
  // errors
  'validation.requiredInput': 'Required field',
  'validation.invalidFormat': 'Invalid format',
  'validation.invalidNameValue':
    'Please confirm the Name are entered correctly.',
  'validation.invalidDate': 'Invalid date',
  'validation.nameStartsWithSpace': "Name shouldn't start with space",
  'validation.nameHasAdjacentSpaces': "Name shouldn't have adjacent spaces",
  'validation.nameHasHyphenNextToBlank':
    "Name shouldn't have hyphen next to space",
  'validation.nameHasSpecialCharacterAfterComma':
    'Name should only have alphabets/space after comma',
  'validation.insuredMinimumAge': 'Age cannot be less than 18 years old.',
  'validation.error.maxLength120': 'Maximum length is 120',
  'formFields.ownerIsPersonCovered': 'Policy owner is the insured',
  individual: 'Individual',
  entity: 'Entity',
  existingLead: 'Existing FWD lead',
  'existingLead.orCreate': 'Or create new',
  existingCompany: 'Existing Company',
  next: 'Next',
  'saveCoverageDetails.error.title': 'Unable to proceed',
  'saveCoverageDetails.error.content':
    'We are unable to proceed with your application based on your selection.',
};
