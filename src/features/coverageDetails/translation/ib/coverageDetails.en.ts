export default {
  dateFormat: 'DD/MM/YYYY',
  coverageDetails: 'Fill in customer details',
  createSI: 'Fill in customer details',
  insuredPolicyOwner: 'Policy owner is the insured',
  saveAndGenerate: 'Save & generate plan',
  productSelection: 'Product selection',
  popupTitle: 'Is it an existing lead/ opportunity?',
  yes: 'Yes',
  popupContent: 'Click “Yes” to retrieve data',
  aboutPolicyOwner: 'About policy owner (myself)',
  aboutPolicyOwnerContent:
    'Importance of Truthful Disclosure: It’s important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy ',
  close: 'Close',
  more: '...more',
  samePersonTitle: 'Is the policy owner the same person?',
  samePersonContent:
    'We found someone having these information in your record.',
  samePersonMobile: 'mobile',
  samePersonEmail: 'email',
  no: 'No',
  searchExistingLead: 'Search for existing FWD lead',
  searchMobileOrEmail: 'Lead name/ phone number/ email',
  search: 'Search',
  clientType: 'Client type',
  code: 'Code',
  mobile: 'Mobile',
  email: 'Email',
  salutationTitle: 'Salutation/Title',
  firstName: 'First name',
  middleName: 'Middle name (optional)',
  lastName: 'Last name',
  extensionName: 'Extension name (optional)',
  dateOfBirth: 'Date of birth',
  gender: 'Gender',
  open: 'Open',
  male: 'Male',
  female: 'Female',
  relationship: 'Relationship',
  relationshipToOwner: 'Relationship to owner',
  age: 'Age',
  addInsuredOwner: 'Add person covered',
  next: 'Next',
  subNext: 'Product selection',
  home: 'Home',
  class: 'Class',
  cancel: 'Cancel',
  remove: 'Remove',
  titleDeleteDialog: 'Are you sure to remove?',
  contentDeleteDialog:
    'Filled information under person covered will be removed',
  subtitleProductSelection: 'Product selection',
  'entityOrIndividualTabs.label': 'Benefit illustration for:',
  'entityOrIndividualTabs.individual': 'Individual',
  'entityOrIndividualTabs.organisation': 'Organisation',
  individual: 'Individual',
  entity: 'Organization',
  existingLead: 'Search existing lead',
  'existingLead.orCreateOwner': 'Or create new policy owner',
  'existingLead.orCreateInsured': 'Or create new life assured',
  existingCompany: 'Search existing company',
  'existingCompany.orCreate': 'Or create new company profile',
  title: 'Title',

  'formFields.title': 'About policy owner (myself)',
  'formFields.tellUsAbout': 'Tell us about ',
  'formFields.yourself': 'yourself',
  'formFields.certificateOwner': 'Policy Owner',
  'formFields.personCovered': 'Person covered',
  'formFields.firstName': 'Full name',
  'formFields.lastName': 'Last name',
  'formFields.age': 'Age',
  'formFields.nationality': 'Nationality',
  'formFields.residence': 'Country of Residence',
  'formFields.religion': 'Religion',
  'formFields.phoneCode': 'Code',
  'formFields.countryCode': 'Country Code',
  'formFields.searchCountryCode': 'Search for Country Code',
  'formFields.phoneNumber': 'Phone number',
  'formFields.businessPhoneNumber': 'Business phone number',
  'formFields.occupation': 'Occupation',
  'formFields.occupation.search.placeholder': 'Search for occupation',
  'formFields.occupationClass': 'Occupation class',
  'formFields.gender': 'Gender',
  'formFields.dateOfBirth': 'Date of birth',
  'formFields.smokingHabit': 'Smoking habit',
  'formFields.ownerIsPersonCovered': 'Policy owner is the life assured',
  'formFields.relationshipToCertOwner': 'Relationship to policy owner',
  'formFields.entity.title': 'About policy owner (Company)',
  'formFields.email': 'Email',
  'formFields.keyman.title': 'Life assured details',
  'formFields.keyman.existingFWDCustomer': 'Existing FWD customer',
  'formFields.companyName': 'Company name',
  'formFields.registrationNumberLatest': 'Registration number (Latest)',
  'formFields.representativeName': 'Authorised signatory name',
  'formFields.residencyType': 'Residency type',

  // errors
  'validation.requiredInput': 'Required field',
  'validation.invalidFormat': 'Invalid format',
  'validation.invalidNameValue':
    'Please confirm the Name are entered correctly.',
  'validation.invalidDate': 'Invalid date',
  'validation.invalidOwnerMinAge':
    'The minimum age of entry for Policy Owner is {{age}}',
  'validation.invalidOwnerMaxAge':
    'The maximum age of entry for Policy Owner is {{age}}',
  'validation.invalidInsuredMinAge':
    'The minimum age of entry for Life Assured is {{age}}',
  'validation.invalidInsuredMaxAge':
    'The maximum age of entry for  Life Assured is {{age}}',
  'validation.error.required': 'Required field',
  'validation.nameStartsWithSpace': "Name shouldn't start with space",
  'validation.nameHasAdjacentSpaces': "Name shouldn't have adjacent spaces",
  'validation.nameHasHyphenNextToBlank':
    "Name shouldn't have hyphen next to space",
  'validation.nameHasSpecialCharacterAfterComma':
    'Name should only have alphabets/space after comma',
  'validation.error.maxLength120': 'Maximum length is 120',
  'validation.invalidOccupation':
    'Unable to proceed with the selected Occupation',
  'validation.invalidApplication':
    'We are not able to proceed with your application. Please contact Customer Service Representative for more details',

  'searchExistingLeadModal.existingLeadTitle': 'Search for existing FWD lead',
  'searchExistingLeadModal.existingCompanyTitle': 'Search for existing company',
  'searchExistingLeadModal.individualLeadPlaceholder':
    'Lead name/ phone number/ email',
  'searchExistingLeadModal.entityLeadPlaceholder':
    'Company name/ phone number/ email',
  'searchExistingLeadModal.searchResults': 'Search Results',
  'searchExistingLeadModal.table.leadName': 'Lead name',
  'searchExistingLeadModal.table.mobile': 'Mobile',
  'searchExistingLeadModal.table.email': 'Email',
  'saveCoverageDetails.error.title': 'Unable to proceed',
  'saveCoverageDetails.error.content':
    'We are unable to proceed with your application based on your selection.',
};
