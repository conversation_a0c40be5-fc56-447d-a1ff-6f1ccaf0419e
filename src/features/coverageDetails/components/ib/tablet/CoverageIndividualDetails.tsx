import { useTheme } from '@emotion/react';
import { Box, Button, Column, Row } from 'cube-ui-components';
import useBoundStore from 'hooks/useBoundStore';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import PolicyOwnerForm from './PolicyOwnerForm';

import insuredFormSetup, {
  IbInsuredFormValues as InsuredFormValues,
} from 'features/coverageDetails/validation/common/insuredSchema';
import ownerFormSetup, {
  IbOwnerFormValues as OwnerFormValues,
} from 'features/coverageDetails/validation/common/ownerSchema';
import { useForm } from 'react-hook-form';

import { useTranslation } from 'react-i18next';

import { yupResolver } from '@hookform/resolvers/yup';
import TabletFooter from 'components/Footer/TabletFooter';
import IncompleteFields from 'components/IncompleteFields';
import SearchExistingLead from 'components/SearchExistingLead';
import { format } from 'date-fns';
import { useIdsFromRoute } from 'features/coverageDetails/hooks/common/useCoverageForm';
import useSaveCoverageDetails from 'features/coverageDetails/hooks/common/useSaveCoverageDetails';
import { useSearchLeadEnabled } from 'features/coverageDetails/hooks/common/useSearchExistingLead';
import { useLeadFormAdapter } from 'features/coverageDetails/hooks/useLeadFormAdapter';
import useRegionDefaultValues from 'features/coverageDetails/hooks/useRegionDefaultValues';
import { tabletStyles } from 'features/coverageDetails/utils/common/customStyles';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Lead } from 'types';
import { MYOptionList } from 'types/optionList';
import { PartyType } from 'types/party';
import { Gender } from 'types/person';
import { removeBlankValue } from 'utils';
import { BltsRefInfo } from 'utils/helper/bltsUtils';
import { ObjectSchema } from 'yup';
import PolicyInsuredForm from './PolicyInsuredForm';

const ownerFormValidationSchema =
  ownerFormSetup.ownerFormValidationSchema as ObjectSchema<OwnerFormValues>;
const insuredFormValidationSchema =
  insuredFormSetup.insuredFormValidationSchema as ObjectSchema<InsuredFormValues>;

export default function CoverageIndividualDetails() {
  const { space, colors } = useTheme();

  const { leadId } = useIdsFromRoute();

  const searchLeadEnabled = useSearchLeadEnabled();

  const agentCode = useBoundStore(state => state.auth.agentCode);
  const [foundLeadId, setFoundLeadId] = useState(leadId);

  const { t } = useTranslation(['coverageDetails', 'eApp']);

  const isSubFamilySharingPlan = !!useFnaStore(
    state => state.familySharePlanPolicyNumber,
  );
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [ownerIsInsured, setOwnerIsInsured] = useState(!isSubFamilySharingPlan);
  const [isSearchModalVisible, setSearchModalVisible] = useState(false);

  const { data: agentInfo } = useGetAgentProfile();

  const { data: optionList } = useGetOptionList<'ib'>();
  const { defaultPolicyOwnerValues } = useRegionDefaultValues<'ib'>({});
  const toLeadForm = useLeadFormAdapter();

  const {
    watch: ownerFormWatch,
    setValue: setOwnerFormValue,
    getValues: getOwnerFormValues,
    handleSubmit: handleOwnerFormSubmit,
    control: ownerFormControl,
    formState: { isValid: ownerFormIsValid, errors: ownerFormErrors },
    reset: ownerFormReset,
    trigger: ownerFormTrigger,
  } = useForm<
    OwnerFormValues,
    {
      ownerIsInsured: boolean;
      agentChannel: string | undefined;
      optionList: MYOptionList<string, 'ib'> | undefined;
    }
  >({
    mode: 'onBlur',
    defaultValues: defaultPolicyOwnerValues,
    resolver: yupResolver(ownerFormValidationSchema),
    context: {
      ownerIsInsured,
      agentChannel: agentInfo?.channel,
      optionList,
    },
  });

  const {
    watch: insuredFormWatch,
    setValue: setInsuredFormValue,
    handleSubmit: handleInsuredFormSubmit,
    control: insuredFormControl,
    trigger: insuredFormTrigger,
    formState: { isValid: insuredFormIsValid, errors: insuredFormErrors },
  } = useForm<
    InsuredFormValues,
    {
      agentChannel: string | undefined;
      optionList: MYOptionList<string, 'ib'> | undefined;
    }
  >({
    mode: 'onBlur',
    defaultValues: insuredFormSetup.insuredFormDefaultValues,
    resolver: yupResolver(insuredFormValidationSchema),
    context: {
      agentChannel: agentInfo?.channel,
      optionList,
    },
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    focusOnNextIncompleteField: focusOnNextOwnerForm,
    totalIncompleteRequiredFields: ownerIncompete,
  } = useIncompleteFields<OwnerFormValues>({
    control: ownerFormControl,
    schema: ownerFormValidationSchema,
    watch: ownerFormWatch,
    scrollRef,
    scrollTo: ({ y }) => {
      if (y !== undefined) scrollRef.current?.scrollToPosition(0, y || 0, true);
    },
    omit: ['occupationClass'],
  });

  const {
    focusOnNextIncompleteField: focusOnNextInsuredForm,
    totalIncompleteRequiredFields: insuredIncompete,
  } = useIncompleteFields<InsuredFormValues>({
    control: insuredFormControl,
    schema: insuredFormValidationSchema,
    watch: insuredFormWatch,
    scrollRef,
    scrollTo: ({ y }) => {
      if (y !== undefined) scrollRef.current?.scrollToPosition(0, y || 0, true);
    },
    omit: ['occupationClass', 'nanoRelationship'],
  });

  const isSubmitDisabled = useMemo(() => {
    if (!ownerFormIsValid) {
      return true;
    }

    if (!ownerIsInsured && !insuredFormIsValid) {
      return true;
    }

    return false;
  }, [insuredFormIsValid, ownerFormIsValid, ownerIsInsured]);

  const [ownerGender, ownerDoB] = ownerFormWatch(['gender', 'dob']);

  const { mutateAsync: createLead } = useCreateLead();

  const onSelectExistingLead = (lead: Lead) => {
    setFoundLeadId(lead.id);
    ownerFormReset({
      ...getOwnerFormValues(),
      ...toLeadForm(lead),
    });
  };

  const onCreateIndividualLead = useCallback(
    async ({
      caseId,
      owner,
      extra,
    }: {
      caseId: string;
      owner: OwnerFormValues;
      extra?: BltsRefInfo;
    }) => {
      await createLead({
        caseId,
        firstName: owner?.firstName,
        lastName: owner?.lastName || ' ', // extra space is required in this case
        nameExtension: removeBlankValue(owner?.extensionName),
        salutation: owner?.title || '',
        mobilePhoneCountryCode: owner?.code || '',
        mobilePhoneNumber: owner?.phoneMobile || '',
        genderCode: owner?.gender || '',
        birthDate: owner?.dob ? format(owner.dob, 'yyyy-MM-dd') : '',
        email: owner?.email || '',
        isIndividual: owner?.clientType === PartyType.INDIVIDUAL,
        extra,
        isSmoker: owner?.smokingHabit === 'S',
        occupationCode: owner?.occupation || '',
        occupationClassCode: owner?.occupationClass || '',
      });
    },
    [createLead],
  );

  const { captureCoverageDetails } = useSaveCoverageDetails({
    leadId: foundLeadId,
    ownerIsInsured,
    isEntity: false,

    onCreateLead: onCreateIndividualLead,
  });

  const handleIndividualFlowSubmit = async () => {
    try {
      if (!agentCode) throw new Error('Agent code is not found');

      setIsSubmitting(true);

      let isOwnerValidationSuccessful = false;
      let isInsuredValidationSuccessful = ownerIsInsured;
      let owner: OwnerFormValues | undefined = undefined;
      let insured: InsuredFormValues | undefined = undefined;
      await handleOwnerFormSubmit(
        ownerFormData => {
          console.log('policy owner form successful');
          owner = ownerFormData;
          isOwnerValidationSuccessful = true;
        },
        errors => {
          console.log('Policy owner form errors: ', errors);
          isOwnerValidationSuccessful = false;
        },
      )();
      if (!ownerIsInsured) {
        console.log('handleInsuredFormSubmit');
        await handleInsuredFormSubmit(
          insuredFormData => {
            console.log('insured form successful');
            insured = insuredFormData;
            isInsuredValidationSuccessful = true;
          },
          errors => {
            console.log('Insured form errors: ', errors);
            isInsuredValidationSuccessful = false;
          },
        )();
      }

      if (!(isOwnerValidationSuccessful && isInsuredValidationSuccessful)) {
        return;
      }

      if (owner) {
        await captureCoverageDetails({ owner, insured });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  /* trigger validation manually when the owner dob changes */
  useEffect(() => {
    if (ownerDoB) {
      ownerFormTrigger('dob');
    }
  }, [ownerFormTrigger, ownerDoB, ownerIsInsured]);

  return (
    <>
      <KeyboardAwareScrollView
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
        enableResetScrollToCoords={false}
        enableOnAndroid
        enableAutomaticScroll
        ref={scrollRef}>
        <Row flex={1} mb={space[25]}>
          <Column
            px={space[8]}
            alignItems="center"
            bgColor={colors.surface}
            width="100%">
            <PolicyOwnerForm
              control={ownerFormControl}
              errors={ownerFormErrors}
              setValue={setOwnerFormValue}
              getValues={getOwnerFormValues}
              onSearchLeadButtonPress={
                searchLeadEnabled
                  ? () => setSearchModalVisible(true)
                  : undefined
              }
            />

            <Box height={space[4]} />

            <Column
              justifyContent="flex-start"
              width="100%"
              height="auto"
              bgColor={colors.palette.white}
              p={space[6]}
              borderRadius={space[4]}
              gap={space[5]}>
              <PolicyInsuredForm
                control={insuredFormControl}
                errors={insuredFormErrors}
                setValue={setInsuredFormValue}
                ownerIsInsured={ownerIsInsured}
                setOwnerIsInsured={setOwnerIsInsured}
                ownerGender={ownerGender as Gender}
                isEntity={false}
                trigger={insuredFormTrigger}
              />
            </Column>
          </Column>
        </Row>
      </KeyboardAwareScrollView>
      <TabletFooter style={{ flexDirection: 'row' }}>
        <IncompleteFields
          incompleteCount={
            ownerIncompete + (ownerIsInsured ? 0 : insuredIncompete)
          }
          focusNext={() => {
            if (!ownerIsInsured) focusOnNextInsuredForm();
            // if both form opened, run this first so that insured form is highlighted as well

            if (ownerIncompete) focusOnNextOwnerForm();
          }}
        />
        <Box flex={1} />
        <Button
          variant={'primary'}
          text={t('coverageDetails:next')}
          subtext={t('coverageDetails:subtitleProductSelection')}
          onPress={handleIndividualFlowSubmit}
          disabled={isSubmitDisabled}
          loading={isSubmitting}
          style={{ width: space[40], alignSelf: 'flex-end' }}
          textStyle={{ ...tabletStyles.mainButtonText }}
          contentStyle={{ minHeight: space[13] }}
          gaParams={[
            {
              screenName: 'CoverageDetails',
              screenClass: 'Sales flow - individual',
              actionType: 'non_cta_button',
              buttonName: 'Submit',
            },
            {
              eventType: 'lead_created',
              formSource: 'quick_quote_form',
            },
          ]}
        />
      </TabletFooter>

      <SearchExistingLead
        isVisible={isSearchModalVisible}
        onClose={() => setSearchModalVisible(false)}
        onDone={onSelectExistingLead}
        isEntityLead={false}
      />
    </>
  );
}
