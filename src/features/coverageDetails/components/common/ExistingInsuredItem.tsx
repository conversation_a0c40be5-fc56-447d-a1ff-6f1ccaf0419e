import SearchableListItem from 'components/SearchableListItem';
import React, { useMemo } from 'react';
import { EntityInsured } from 'types/entityInsured';

type ItemProps<V> = {
  value: V;
  label: string;
  selected: boolean;
  onSelect: (value: V) => void;
  item: EntityInsured;
};
const ExistingLeadItem = React.memo(<V,>({ item, ...props }: ItemProps<V>) => {
  const subtexts = useMemo(
    () => [
      `${
        item.mobilePhoneCountryCode ? `+${item.mobilePhoneCountryCode} ` : ''
      }${item.mobilePhoneNumber}`,
      item.email,
    ],
    [item],
  );
  return <SearchableListItem {...props} subtexts={subtexts} />;
}) as <V>(props: ItemProps<V>) => JSX.Element;

export default ExistingLeadItem;
