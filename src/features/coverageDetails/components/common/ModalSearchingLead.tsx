import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ActionPanel, Button, H8, Icon, Row, SearchBar } from 'cube-ui-components';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface IProps {
  visible: boolean;
  setVisible: (v: boolean) => void;
  onAction: () => void;
}

const CORRECT_NUMBER = "60567890";

const ModalSearchingLead = (props: IProps) => {
  const { visible, setVisible, onAction } = props;
  const { t } = useTranslation(['coverageDetails']);
  const { colors } = useTheme();
  const [query, setQuery] = useState("")
  const [errorMessage, setErrorMessage] = useState("")

  const onSearch = () => {
    if (query !== CORRECT_NUMBER) {
      setErrorMessage("No pre-existing relationship with you. Please try another search.")
    } else {
      setErrorMessage("")
      setVisible(false);
      setTimeout(() => {
        onAction();
      }, 700);
    }
  }

  return (
    <ActionPanel
      title={t('coverageDetails:searchExistingLead')}
      handleClose={() => { setVisible(false) }}
      visible={visible}>
      {errorMessage && (
        <InfoContainer>
          <Icon.InfoCircle fill={colors.palette.alertRed} />
          <H8 color={colors.palette.alertRed}>{errorMessage}</H8>
        </InfoContainer>
      )}

      <SearchBar
        collapseOnBlur={false}
        onChangeQuery={setQuery}
        placeholder={t('coverageDetails:searchMobileOrEmail')}
        variant={'square'}
      />
      <ButtonSearch text={t('coverageDetails:search')} onPress={onSearch} disabled={!query} />
    </ActionPanel>
  )
}

const ButtonSearch = styled(Button)(({ theme: { space } }) => ({
  marginTop: space[10]
}))

const InfoContainer = styled(Row)(({ theme: { space, colors } }) => ({
  gap: space[1],
  backgroundColor: colors.palette.alertRedLight,
  padding: space[2],
  borderRadius: space[2],
  marginBottom: space[5]
}))

export default ModalSearchingLead;