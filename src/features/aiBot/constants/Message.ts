// import { AiFeedbackOption } from '../lib/interfaces';

export const AI_FEEDBACK_OTHER_ID = 'other';

// export const AI_FEEDBACK_OPTION: AiFeedbackOption[] = [
//   { id: 'inaccurate', name: 'Inaccurate', key: 'feedback.options.inaccurate' },
//   {
//     id: 'not-helpful',
//     name: 'Not Helpful',
//     key: 'feedback.options.nothelpful',
//   },
//   {
//     id: 'needs-refinement',
//     name: 'Needs refinement',
//     key: 'feedback.options.needs-refinement',
//   },
//   // { id: 'offensive', name: 'Offensive' },
//   // { id: AI_FEEDBACK_OTHER_ID, name: 'Other' }
// ];

export const AI_FEEDBACK_OTHER_TEXT =
  'Thank you! Can you tell us the your reason?';

export const DEFAULT_SUGGESTION_TARGET_ID = 'value';
