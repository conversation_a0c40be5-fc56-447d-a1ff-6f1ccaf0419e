import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import { useMemo } from 'react';
import { TextStyle } from 'react-native';
import { StyleProp } from 'react-native';

interface HighlightedTextProps {
  text?: string;
  searchText: string;
  additionalStyles?: StyleProp<TextStyle>;
  highlightStyle?: StyleProp<TextStyle>;
  fontWeight?: Typography.FontWeight;
  id: string;
}

export default function HighlightedText({
  text = '',
  searchText,
  additionalStyles,
  highlightStyle,
  fontWeight,
  id,
}: HighlightedTextProps) {
  const theme = useTheme();
  const { colors } = theme;

  const DEFAULT_HIGHLIGHT_STYLE: StyleProp<TextStyle> = useMemo(
    () => ({
      color: colors.palette.fwdOrange[100],
      fontWeight: 'bold',
    }),
    [theme],
  );

  const textArray = useMemo(
    () =>
      searchText ? text.split(new RegExp(`(${searchText})`, 'gi')) : [text],
    [text, searchText],
  );

  return (
    <>
      {textArray.map((item, index) => {
        const isHighlighted = index % 2 === 1;

        return (
          <Typography.LargeBody
            fontWeight={fontWeight}
            key={`${id}-${item}-${index}`}
            style={[
              additionalStyles,
              isHighlighted && (highlightStyle || DEFAULT_HIGHLIGHT_STYLE),
            ]}>
            {item}
          </Typography.LargeBody>
        );
      })}
    </>
  );
}
