import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/esm/theme/base';
import { useAppStore } from 'features/aiBot/store';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dimensions } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

const Container = styled(Animated.View)(
  ({ theme: { space, borderRadius } }) => ({
    position: 'absolute',
    backgroundColor: colors.fwdDarkGreen[50],
    paddingVertical: space[2],
    paddingHorizontal: space[3],
    borderRadius: borderRadius.small,
    top: Dimensions.get('window').height / 3,
    zIndex: 1,
  }),
);

const Text = styled(Typography.LargeLabel)(() => ({
  color: colors.white,
}));

let timeoutId: null | ReturnType<typeof setTimeout> = null;

export default function CopiedNotification() {
  const copiedTextCounter = useAppStore(state => state.copiedTextCounter);

  const clearCopiedCounterCounter = useAppStore(
    state => state.clearCopiedCounterCounter,
  );

  const [show, setShow] = useState(false);

  const { t } = useTranslation('aiBot');

  useEffect(() => {
    return () => {
      clearCopiedCounterCounter();
    };
  }, [clearCopiedCounterCounter]);

  function startTimer() {
    if (timeoutId) clearTimeout(timeoutId);

    timeoutId = setTimeout(() => {
      hide();
    }, 1000);
  }

  function hide() {
    setShow(false);
  }

  useEffect(() => {
    if (copiedTextCounter > 0) {
      setShow(true);
      startTimer();
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, []);

  if (!show) return <></>;

  return (
    <Container entering={FadeIn} exiting={FadeOut}>
      <Text>{t('common.copied')}</Text>
    </Container>
  );
}
