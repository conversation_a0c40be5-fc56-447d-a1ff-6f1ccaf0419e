import { PROMPT_ICON_NAME } from 'agent-guru';
import PromptPerformanceIcon from './PromptPerformanceIcon';
import PromptSaleTechniquesIcon from './PromptSaleTechniquesIcon';
import PromptPolicyKnowledgeIcon from './PromptPolicyKnowledgeIcon';
import PromptCustomerServicesIcon from './PromptCustomerServicesIcon';
import PromptOtherIcon from './PromptOtherIcon';

interface IconProps {
	width?: string;
	height?: string;
}

interface PromptIconCompProps {
	iconName?: string;
	iconProps?: IconProps;
}

export default function PromptIconComp({ iconName, iconProps }: PromptIconCompProps) {
	switch (iconName) {
		case PROMPT_ICON_NAME.PERFORMANCE:
			return <PromptPerformanceIcon {...iconProps} />;
		case PROMPT_ICON_NAME.SALES_TECHNIQUES:
			return <PromptSaleTechniquesIcon {...iconProps} />;
		case PROMPT_ICON_NAME.POLICY_KNOWLEDGE:
			return <PromptPolicyKnowledgeIcon {...iconProps} />;
		case PROMPT_ICON_NAME.CUSTOMER_SERVICE:
			return <PromptCustomerServicesIcon {...iconProps} />;
		case PROMPT_ICON_NAME.OTHER:
		default:
			return <PromptOtherIcon {...iconProps} />;
	}
}
