import { SvgIconProps } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/esm/theme/base';
import Svg, { Path } from 'react-native-svg';

export default function PinFillIcon(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || props.size || 14}
      height={props.height || props.size || 14}
      viewBox="0 0 14 14"
      fill="none">
      <Path
        d="M8.79608 0.52599L13.4579 5.1631C13.6788 5.38244 13.7261 5.72364 13.592 6.12411L13.5834 6.1498L11.8687 7.85512C11.7445 7.97896 11.5836 8.04713 11.4161 8.04713C11.2485 8.04713 11.0876 7.97896 10.9635 7.85512L10.6744 7.56794L9.21062 9.02395C9.47252 10.364 9.5586 12.1464 8.53882 13.17C8.46564 13.2721 8.33817 13.3449 8.1958 13.3656C8.00674 13.3926 7.80742 13.3284 7.63359 13.1845L7.62598 13.1773L4.68814 10.2554L1.43245 13.4934C1.32286 13.6028 1.14936 13.6673 0.972226 13.6673C0.811644 13.6673 0.648412 13.6143 0.52723 13.4934C0.268973 13.2369 0.268973 12.8496 0.526899 12.593L3.78259 9.35461L0.844753 6.43237C0.586828 6.17582 0.586828 5.78852 0.844753 5.53163C1.87281 4.50936 3.67035 4.59532 5.0209 4.85615L6.48435 3.40014L6.19563 3.11263C6.08803 3.00592 6.01783 2.86035 6.00327 2.71347C5.99035 2.58469 6.0132 2.39335 6.19563 2.21188L7.89086 0.52566C8.01502 0.402158 8.17593 0.333984 8.34347 0.333984C8.51101 0.333984 8.67192 0.402158 8.79608 0.52599Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
    </Svg>
  );
}
