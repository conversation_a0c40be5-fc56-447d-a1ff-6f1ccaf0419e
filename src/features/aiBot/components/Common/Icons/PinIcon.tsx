import { SvgIconProps } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/esm/theme/base';
import Svg, { Path } from 'react-native-svg';

export default function PinIcon(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || props.size || 20}
      height={props.height || props.size || 20}
      viewBox="0 0 20 20"
      fill="none">
      <Path
        d="M19.6867 7.24367L12.6939 0.288008C12.5076 0.10226 12.2663 0 12.015 0C11.7637 0 11.5223 0.10226 11.336 0.287514L8.79321 2.81685C8.51956 3.08905 8.48529 3.37607 8.50466 3.56922C8.52651 3.78955 8.6318 4.0079 8.79321 4.16796L9.22628 4.59923L7.0311 6.78325C5.00528 6.392 2.30898 6.26306 0.766886 7.79647C0.379997 8.1818 0.379997 8.76275 0.766886 9.14758L5.17364 13.5309L0.290104 18.3885C-0.0967842 18.7734 -0.0967842 19.3543 0.290601 19.7392C0.472374 19.9205 0.717221 20 0.958095 20C1.2238 20 1.48405 19.9032 1.64844 19.7392L6.53197 14.8821L10.9387 19.2649L10.9501 19.2758C11.2109 19.4917 11.5099 19.588 11.7935 19.5475C12.007 19.5164 12.1982 19.4072 12.308 19.254C13.8377 17.7187 13.7085 15.0451 13.3157 13.035L15.5114 10.8509L15.9449 11.2817C16.1312 11.4675 16.3725 11.5697 16.6239 11.5697C16.8752 11.5697 17.1165 11.4675 17.3028 11.2817L19.8749 8.72372L19.8878 8.68519C20.089 8.08448 20.0179 7.57268 19.6867 7.24367ZM16.4649 9.0972L16.0314 8.66593C15.6445 8.28109 15.0604 8.28109 14.6715 8.6679L11.4155 11.9862C11.1294 12.2702 11.039 12.5735 11.1319 12.9441C11.2039 13.2054 11.786 15.4383 11.2352 17.0162L2.86969 8.69507C3.5645 8.46783 5.03905 8.27615 6.95859 8.79585C7.27297 8.89762 7.65241 8.78795 7.92606 8.51575L11.1846 5.27455C11.6221 4.83932 11.6221 4.35915 11.1846 3.92343L10.7545 3.49611L12.0115 2.31098L17.6494 7.91898L16.4649 9.0972Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
    </Svg>
  );
}
