import { useTheme } from '@emotion/react';
import { SvgIconProps } from 'cube-ui-components';
import { Path, Svg } from 'react-native-svg';

export default function NewWindowIcon(props: SvgIconProps) {
  const { colors } = useTheme();

  return (
    <Svg
      width={props.width || props.size || 24}
      height={props.height || props.size || 24}
      viewBox="0 0 24 24"
      fill="none">
      <Path
        d="M5 21C4.45 21 3.97933 20.8043 3.588 20.413C3.19667 20.0217 3.00067 19.5507 3 19V5C3 4.45 3.196 3.97933 3.588 3.588C3.98 3.19667 4.45067 3.00067 5 ************************** 19.55 20.8043 20.021 20.413 20.413C20.0217 20.805 19.5507 21.0007 19 21H5ZM16 **********************************"
        fill={props.fill || colors.palette.fwdOrange[100]}
      />
    </Svg>
  );
}
