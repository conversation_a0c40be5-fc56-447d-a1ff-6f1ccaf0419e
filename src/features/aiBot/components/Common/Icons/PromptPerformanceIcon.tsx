import Svg, { ClipP<PERSON>, Defs, G, Mask, Path, Rect } from "react-native-svg";

export default function PromptPerformanceIcon({ width, height }: { width?: string; height?: string }) {
	return (
		<Svg width={width || "32"} height={height || "32"} viewBox="0 0 32 32" fill="none">
			<Rect width={width || "32"} height={height || "32"} rx="8" fill="#6ECEB2" />
			<G clip-path="url(#clip0_4378_181494)">
				<Mask
					id="mask0_4378_181494"
					maskUnits="userSpaceOnUse"
					x="6"
					y="6"
					width="20"
					height="20"
				>
					<Path d="M6.39844 6.3999H25.5984V25.5999H6.39844V6.3999Z" fill="white" />
				</Mask>
				<G mask="url(#mask0_4378_181494)">
					<Path
						d="M23.7194 21.6015H8.41382C8.23517 21.6015 8.06385 21.6725 7.93752 21.7988C7.8112 21.9251 7.74023 22.0965 7.74023 22.2751C7.74023 22.4538 7.8112 22.6251 7.93752 22.7514C8.06385 22.8778 8.23517 22.9487 8.41382 22.9487H23.7194C23.8079 22.9487 23.8955 22.9313 23.9772 22.8975C24.0589 22.8636 24.1332 22.814 24.1957 22.7514C24.2583 22.6889 24.3079 22.6146 24.3417 22.5329C24.3756 22.4512 24.393 22.3636 24.393 22.2751C24.393 22.1867 24.3756 22.0991 24.3417 22.0173C24.3079 21.9356 24.2583 21.8614 24.1957 21.7988C24.1332 21.7363 24.0589 21.6866 23.9772 21.6528C23.8955 21.6189 23.8079 21.6015 23.7194 21.6015Z"
						fill="white"
					/>
					<Path
						d="M24.0594 8.8551C24.0049 8.78528 23.9372 8.72688 23.8602 8.68325C23.7831 8.63962 23.6982 8.61163 23.6103 8.60085C23.5224 8.59008 23.4332 8.59674 23.3479 8.62046C23.2626 8.64418 23.1827 8.68451 23.113 8.73911L17.465 13.1527L13.8594 10.3127C13.7321 10.2129 13.5731 10.1623 13.4115 10.1701C13.25 10.1779 13.0966 10.2435 12.9794 10.3551L9.11542 14.0615C8.98654 14.1853 8.91206 14.3551 8.90831 14.5338C8.90456 14.7124 8.97185 14.8852 9.09542 15.0143C9.15833 15.0799 9.23385 15.132 9.31744 15.1676C9.40103 15.2032 9.49096 15.2216 9.58182 15.2215C9.75576 15.2216 9.92295 15.1542 10.0482 15.0335L13.4882 11.7335L17.0482 14.5375C17.1669 14.6307 17.3134 14.6813 17.4642 14.6813C17.6151 14.6813 17.7616 14.6307 17.8802 14.5375L23.945 9.79751C24.0147 9.74304 24.073 9.67539 24.1166 9.59839C24.1602 9.52139 24.1881 9.43654 24.1989 9.34873C24.2097 9.26092 24.203 9.17187 24.1794 9.08662C24.1557 9.00138 24.1155 8.92161 24.061 8.8519L24.0594 8.8551Z"
						fill="white"
					/>
					<Path
						d="M24.0394 8.85272C23.9221 8.71713 23.7558 8.63367 23.577 8.62071L19.8034 8.35113C19.7151 8.34427 19.6262 8.35501 19.542 8.38275C19.4579 8.41048 19.38 8.45466 19.3131 8.5127C19.2461 8.57075 19.1913 8.6415 19.1519 8.72088C19.1125 8.80026 19.0892 8.88669 19.0834 8.97513C19.0771 9.06338 19.0882 9.152 19.1162 9.23595C19.1441 9.31989 19.1883 9.39753 19.2463 9.46439C19.3042 9.53125 19.3748 9.58603 19.4539 9.62562C19.533 9.66521 19.6192 9.68884 19.7074 9.69512L22.805 9.91593L22.5874 12.7447C22.5748 12.9222 22.633 13.0976 22.7491 13.2325C22.8652 13.3673 23.03 13.4508 23.2074 13.4647H23.2594C23.4291 13.4646 23.5925 13.4006 23.717 13.2854C23.8416 13.1702 23.9181 13.0123 23.9314 12.8431L24.201 9.33912C24.208 9.25062 24.1975 9.1616 24.17 9.07718C24.1426 8.99276 24.0987 8.91459 24.041 8.84713L24.0394 8.85272Z"
						fill="white"
					/>
					<Path
						d="M17.6458 17.4687C17.5573 17.4687 17.4696 17.4862 17.3879 17.5201C17.3061 17.554 17.2318 17.6037 17.1692 17.6663C17.1067 17.7289 17.0571 17.8033 17.0233 17.8851C16.9895 17.9669 16.9721 18.0546 16.9722 18.1431V22.2759C16.9722 22.4546 17.0432 22.6259 17.1695 22.7522C17.2958 22.8785 17.4672 22.9495 17.6458 22.9495C17.8245 22.9495 17.9958 22.8785 18.1221 22.7522C18.2485 22.6259 18.3194 22.4546 18.3194 22.2759V18.1431C18.3195 18.0546 18.3022 17.9669 18.2684 17.8851C18.2346 17.8033 18.185 17.7289 18.1224 17.6663C18.0598 17.6037 17.9856 17.554 17.9038 17.5201C17.822 17.4862 17.7343 17.4687 17.6458 17.4687Z"
						fill="white"
					/>
					<Path
						d="M20.925 15.5591C20.8365 15.5591 20.7488 15.5766 20.6671 15.6105C20.5853 15.6444 20.511 15.6941 20.4484 15.7567C20.3859 15.8193 20.3363 15.8937 20.3025 15.9755C20.2687 16.0573 20.2513 16.145 20.2514 16.2335V22.2759C20.2514 22.4546 20.3224 22.6259 20.4487 22.7522C20.575 22.8786 20.7464 22.9495 20.925 22.9495C21.1037 22.9495 21.275 22.8786 21.4013 22.7522C21.5277 22.6259 21.5986 22.4546 21.5986 22.2759V16.2335C21.5986 16.0548 21.5277 15.8834 21.4014 15.7569C21.2751 15.6305 21.1037 15.5593 20.925 15.5591Z"
						fill="white"
					/>
					<Path
						d="M14.389 14.7511C14.2104 14.7511 14.039 14.8221 13.9127 14.9484C13.7864 15.0747 13.7154 15.2461 13.7154 15.4247V22.2759C13.7154 22.4546 13.7864 22.6259 13.9127 22.7522C14.039 22.8786 14.2104 22.9495 14.389 22.9495C14.5677 22.9495 14.739 22.8786 14.8653 22.7522C14.9917 22.6259 15.0626 22.4546 15.0626 22.2759V15.4247C15.0626 15.2461 14.9917 15.0747 14.8653 14.9484C14.739 14.8221 14.5677 14.7511 14.389 14.7511Z"
						fill="white"
					/>
					<Path
						d="M11.109 17.7383C10.9304 17.7383 10.759 17.8093 10.6327 17.9356C10.5064 18.0619 10.4354 18.2333 10.4354 18.4119V22.2759C10.4354 22.4546 10.5064 22.6259 10.6327 22.7522C10.759 22.8786 10.9304 22.9495 11.109 22.9495C11.2877 22.9495 11.459 22.8786 11.5853 22.7522C11.7117 22.6259 11.7826 22.4546 11.7826 22.2759V18.4119C11.7826 18.2333 11.7117 18.0619 11.5853 17.9356C11.459 17.8093 11.2877 17.7383 11.109 17.7383Z"
						fill="white"
					/>
				</G>
			</G>
			<Defs>
				<ClipPath id="clip0_4378_181494">
					<Rect width="19.2" height="19.2" fill="white" transform="translate(6.39844 6.3999)" />
				</ClipPath>
			</Defs>
		</Svg>
	);
}
