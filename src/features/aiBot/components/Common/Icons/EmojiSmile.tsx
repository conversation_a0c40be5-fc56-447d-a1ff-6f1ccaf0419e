import { colors } from 'cube-ui-components/dist/esm/theme/base';
import React from 'react';
import Svg, { Path } from 'react-native-svg';
export default function EmojiSmile(props: {
  size?: number;
  height?: number;
  width?: number;
  fill?: string;
}) {
  return (
    <Svg
      width={props.width || props.size || 20}
      height={props.height || props.size || 20}
      viewBox="0 0 20 20"
      fill="none">
      <Path
        d="M18.7817 6.36255C18.3104 5.22469 17.6146 4.17987 16.7174 3.28263C14.9232 1.48814 12.5377 0.5 10 0.5C7.46227 0.5 5.07675 1.48814 3.28263 3.28263C1.48814 5.07675 0.5 7.46263 0.5 10C0.5 12.5377 1.48814 14.9232 3.28263 16.7177C5.07675 18.5119 7.46227 19.5 10 19.5C12.5377 19.5 14.9232 18.5119 16.7174 16.7177C18.5119 14.9232 19.5 12.5377 19.5 10C19.5 8.73131 19.253 7.50041 18.7817 6.36255ZM10 17.6652C5.77366 17.6652 2.33512 14.2267 2.33512 10C2.33512 5.77366 5.77366 2.33512 10 2.33512C14.2263 2.33512 17.6649 5.77366 17.6649 10C17.6649 14.2267 14.2263 17.6652 10 17.6652Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
      <Path
        d="M8.55879 7.97472C8.55879 8.20963 8.46967 8.44455 8.29072 8.6235C8.11748 8.79675 7.8872 8.89192 7.64194 8.89192C7.39669 8.89192 7.16641 8.79675 6.99316 8.6235C6.83845 8.46879 6.63241 8.38359 6.41282 8.38359C6.19324 8.38359 5.9872 8.46879 5.83249 8.6235C5.47494 8.98104 4.89282 8.98104 4.53492 8.6235C4.36168 8.4499 4.26614 8.21962 4.26614 7.97436C4.26614 7.72947 4.36168 7.49918 4.53492 7.32594C5.03577 6.82474 5.70273 6.54918 6.41282 6.54883C7.12292 6.54883 7.78988 6.82474 8.29072 7.32594C8.46967 7.50453 8.55879 7.7398 8.55879 7.97472Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
      <Path
        d="M15.4651 8.6235C15.2918 8.79675 15.0615 8.89192 14.8163 8.89192C14.571 8.89192 14.3408 8.79675 14.1675 8.62314C14.0128 8.46879 13.8068 8.38359 13.5872 8.38359C13.3676 8.38359 13.1615 8.46879 13.0068 8.62314C12.6493 8.98104 12.0672 8.98104 11.7093 8.6235C11.536 8.4499 11.4405 8.21962 11.4405 7.97436C11.4405 7.72947 11.536 7.49918 11.7093 7.32594C12.2101 6.82474 12.8771 6.54883 13.5872 6.54883C13.9422 6.54918 14.2866 6.61798 14.6045 6.74952C14.9225 6.88106 15.2148 7.07534 15.4651 7.32594C15.8226 7.68348 15.8226 8.2656 15.4651 8.6235Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
      <Path
        d="M14.1034 10.4609H5.89665C5.43502 10.4609 5.0593 10.8367 5.0593 11.2983C5.0593 14.0225 7.27584 16.2386 10 16.2386C12.7242 16.2386 14.9407 14.0225 14.9407 11.2983C14.9407 10.8367 14.565 10.4609 14.1034 10.4609ZM10 14.5647C8.50496 14.5647 7.2156 13.5462 6.84202 12.1353H13.158C12.7844 13.5462 11.4951 14.5647 10 14.5647Z"
        fill={props.fill || colors.fwdDarkGreen[100]}
      />
    </Svg>
  );
}
