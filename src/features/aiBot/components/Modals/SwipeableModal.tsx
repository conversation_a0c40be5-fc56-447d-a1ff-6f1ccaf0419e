import Animated, {
  runOnJS,
  useAnimatedGestureHand<PERSON>,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  GestureHandlerRootView,
  PanGestureHandler,
} from 'react-native-gesture-handler';
import { KeyboardAvoidingView, LayoutChangeEvent, Modal } from 'react-native';
import { View } from 'react-native';
import { useEffect, useState } from 'react';
import styled from '@emotion/native';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';

const GestureView = styled(GestureHandlerRootView)(() => ({
  flex: 1,
  backgroundColor: 'transparent',
  position: 'relative',
  justifyContent: 'flex-end',
}));

const ModalView = styled(Animated.View)(({ theme: { borderRadius } }) => ({
  backgroundColor: colors.white,
  borderTopLeftRadius: borderRadius['xx-large'],
  borderTopRightRadius: borderRadius['xx-large'],
}));

const Layer = styled(Animated.View)(() => ({
  position: 'absolute',
  flex: 1,
  height: '100%',
  width: '100%',
  top: 0,
  left: 0,
}));

const SwipeContainer = styled(View)(({ theme: { space } }) => ({
  marginTop: space[2],
  marginBottom: space[4],
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'center',
}));

const SwipeView = styled(View)(({ theme: { borderRadius } }) => ({
  width: 40,
  height: 5,
  borderRadius: borderRadius.full,
}));

interface SwipeableModalProps {
  show: boolean;
  onClose: () => void;
  children: React.ReactNode;
  onLayoutUpdate?: (viewHeight: number, modalHeight: number) => void;
  disableSwipe?: boolean;
  triggerCloseAction?: boolean;
}

export default function SwipeableModal({
  show,
  onClose,
  children,
  onLayoutUpdate,
  disableSwipe = false,
  triggerCloseAction,
}: SwipeableModalProps) {
  const translateY = useSharedValue(500);
  const modalHeight = useSharedValue(0);

  const [visibleViewHeight, setVisibleViewHeight] = useState(0);

  // const [localShow, setLocalShow] = useState(show);

  useEffect(() => {
    if (!show) {
      translateY.value = withTiming(500, { duration: 100 }, () => {
        //setLocalShow(false);
      });
      return;
    }

    //setLocalShow(true);

    translateY.value = withTiming(0, { duration: 800 });
  }, [show]);

  useEffect(() => {
    if (triggerCloseAction) {
      translateY.value = withTiming(500, { duration: 700 }, () => {
        runOnJS(onClose)();
      });
    }
  }, [triggerCloseAction]);

  useEffect(() => {
    onLayoutUpdate?.(visibleViewHeight, modalHeight.value);
  }, [modalHeight, visibleViewHeight]);

  const gestureHandler = useAnimatedGestureHandler(
    {
      onStart: undefined,
      onActive: event => {
        if (disableSwipe) return;

        if (event.translationY > 0) translateY.value = event.translationY;
      },
      onEnd: () => {
        if (onClose) {
          if (translateY.value < 100) {
            translateY.value = withTiming(0, { duration: 100 });
            return;
          }

          translateY.value = withTiming(
            modalHeight.value,
            { duration: 300 },
            () => {
              runOnJS(onClose)();
            },
          );
        }
      },
    },
    [modalHeight],
  );

  const animatedStyle = useAnimatedStyle<{ transform: any }>(() => {
    return {
      transform: [
        {
          translateY: translateY.value,
        },
      ],
    };
  });

  const layerStyle = useAnimatedStyle(() => {
    const layerOpacity = 0.9 * 1 - translateY.value / modalHeight.value;

    return {
      backgroundColor: colors.fwdDarkGreen[100],
      opacity: Math.max(0, layerOpacity),
    };
  });

  const onLayout = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    modalHeight.value = height;
  };

  return (
    <Modal visible={show} style={{ flex: 1 }} transparent animationType="fade">
      <KeyboardAvoidingView
        behavior={'height'}
        keyboardVerticalOffset={-30}
        style={{ flex: 1 }}>
        <GestureView>
          <Layer
            style={[layerStyle]}
            onLayout={event =>
              setVisibleViewHeight(event.nativeEvent.layout.height)
            }></Layer>

          <PanGestureHandler onGestureEvent={gestureHandler}>
            <ModalView onLayout={onLayout} style={[animatedStyle]}>
              <SwipeContainer>
                <SwipeView
                  style={[
                    {
                      backgroundColor: disableSwipe
                        ? 'transparent'
                        : colors.fwdGrey[100],
                    },
                  ]}></SwipeView>
              </SwipeContainer>

              {children}
            </ModalView>
          </PanGestureHandler>
        </GestureView>
      </KeyboardAvoidingView>
    </Modal>
  );
}
