import { TouchableOpacity, View } from 'react-native';
import { useSessionsStore } from '../../store/sessionsStore';
import { useAppStore } from '../../store/appStore';
import { useTranslation } from 'react-i18next';
import { useSuggestionsStore } from '../../store/suggestionsStore';
import { generateRandomId, Session } from 'agent-guru';
import { selectCurrentSessionId, formatWsSuggestions } from 'agent-guru';
import { DEFAULT_SUGGESTIONS, NotificationSubType } from 'agent-guru';
import { Icon, Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SwipeableModal from '../Modals/SwipeableModal';
import { useState } from 'react';
import { useNotificationStore } from 'features/aiBot/store';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import PinIcon from '../Common/Icons/PinIcon';
import PinFillIcon from '../Common/Icons/PinFillIcon';

const TabletContainer = styled(Animated.View)(({ theme: { space } }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  backgroundColor: '#000000ac',
  borderBottomRightRadius: space[4],
  borderBottomLeftRadius: space[4],
  justifyContent: 'flex-end',
  zIndex: 1,
}));

const ModalContent = styled(View)<{ insetBottom: number }>(
  ({ theme: { space, colors }, insetBottom }) => ({
    backgroundColor: colors.palette.white,
    gap: space[3],
    justifyContent: 'center',
    paddingHorizontal: space[4],
    borderTopLeftRadius: space[4],
    borderTopRightRadius: space[4],
    paddingBottom: space[8] + insetBottom,
  }),
);

const ActionContainer = styled(TouchableOpacity)(
  ({ theme: { space, colors } }) => ({
    paddingVertical: space[5],
    flexDirection: 'row',
    alignItems: 'center',
    gap: space[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.fwdGrey[100],
  }),
);

interface HistoryModalI {
  showModal: boolean;
  selectedSession?: Session;
  handleCloseModal: () => void;
  isTablet: boolean;
}

export default function HistoryModalTablet({
  showModal,
  selectedSession,
  handleCloseModal,
  isTablet,
}: HistoryModalI) {
  const insets = useSafeAreaInsets();

  const addEvent = useNotificationStore(state => state.addEvent);

  // Selectors
  const pinSession = useSessionsStore(state => state.pinSession);

  const unpinSession = useSessionsStore(state => state.unpinSession);

  const deleteSession = useSessionsStore(state => state.deleteSession);

  const addSession = useSessionsStore(state => state.addSession);

  const currentSessionId = useAppStore(selectCurrentSessionId);

  const setCurrentSessionId = useAppStore(state => state.setCurrentSessionId);

  const setSuggestions = useSuggestionsStore(state => state.setSuggestions);

  const [triggerCloseAction, setTriggerCloseAction] = useState(false);

  const { t } = useTranslation(['aiBot']);

  const { space } = useTheme();

  async function createNewSession(): Promise<string> {
    const newSessionId = generateRandomId();

    const newSession: Session = {
      sessionId: newSessionId,
      isConversation: false,
      datetime: new Date().toString(),
      messages: [],
      state: {
        isAudioRecording: false,
      },
    };

    addSession(newSession);

    setCurrentSessionId(newSession.sessionId);

    setSuggestions(newSessionId, formatWsSuggestions(DEFAULT_SUGGESTIONS));

    return newSessionId;
  }

  function addNotification(text: string) {
    addEvent(
      {
        title: text,
        type: 'info',
      },
      NotificationSubType.BOTTOM,
    );
  }

  function handlePin() {
    if (!selectedSession) return;

    if (selectedSession?.isPinned) {
      unpinSession(selectedSession.sessionId);

      addNotification(t('aiBot:notification.chat.unpinned'));
    } else {
      pinSession(selectedSession.sessionId);

      addNotification(t('aiBot:notification.chat.pinned'));
    }

    setTriggerCloseAction(true);
  }

  function handleDelete() {
    if (!selectedSession) return;

    if (selectedSession.sessionId === currentSessionId) {
      // Create a new session and set it as currentSessionId
      createNewSession();
    }

    deleteSession(selectedSession.sessionId);

    addNotification(t('aiBot:notification.chat.deleted'));

    setTriggerCloseAction(true);
  }

  const Comp = () => (
    <View>
      <ModalContent insetBottom={insets.bottom}>
        <View>
          <Typography.H6 fontWeight="bold">
            {t('aiBot:history.search.action')}
          </Typography.H6>
        </View>
        <View style={{ marginTop: space[2] }}>
          <ActionContainer onPress={handlePin}>
            {!selectedSession?.isPinned ? (
              <PinIcon fill={colors.fwdOrange[100]} size={space[5]} />
            ) : (
              <PinFillIcon fill={colors.fwdOrange[100]} size={space[5]} />
            )}
            <Typography.LargeBody fontWeight="bold">
              {selectedSession?.isPinned
                ? t('aiBot:history.modal.unpinRecord')
                : t('aiBot:history.modal.pinRecord')}
            </Typography.LargeBody>
          </ActionContainer>
          <ActionContainer onPress={handleDelete}>
            <Icon.Delete fill={colors.fwdOrange[100]} size={space[6]} />
            <Typography.LargeBody fontWeight="bold">
              {t('aiBot:history.modal.deleteRecord')}
            </Typography.LargeBody>
          </ActionContainer>
        </View>
      </ModalContent>
    </View>
  );

  if (!showModal) return <></>;

  return isTablet ? (
    <TabletContainer entering={FadeIn.delay(1000)} exiting={FadeOut}>
      <Comp />
    </TabletContainer>
  ) : (
    <View style={{ flex: 1 }}>
      <SwipeableModal
        show={showModal}
        triggerCloseAction={triggerCloseAction}
        onClose={() => {
          handleCloseModal();

          setTriggerCloseAction(false);
        }}
        disableSwipe={false}>
        <Comp />
      </SwipeableModal>
    </View>
  );
}
