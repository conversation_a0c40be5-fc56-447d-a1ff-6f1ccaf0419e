import styled from '@emotion/native';
import { Box, Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { Message, formatDateWithTime } from 'agent-guru';
import { View } from 'react-native';
import LoaderSpinner from '../../Common/LoaderSpinner';
import { useTheme } from '@emotion/react';

const HeaderContainer = styled(View)(({ theme: { space } }) => ({
  marginTop: space[4],
  paddingTop: space[5],
  marginBottom: space[3],
}));

const SeparatorContainer = styled(View)(({ theme: { space } }) => ({
  marginVertical: space[3],
}));

const LoaderSpinnerContainer = styled(Box)(({ theme: { space } }) => ({
  paddingTop: space[6],
  paddingBottom: space[2],
  alignItems: 'center',
}));

interface ChatBubbleHeaderI {
  firstMessage?: Message;
  isLoading?: boolean;
}

export function ChatBubbleHeader({
  firstMessage,
  isLoading = false,
}: ChatBubbleHeaderI) {
  const { space } = useTheme();

  if (!firstMessage) return <></>;

  const firstMessageDate = firstMessage.datetime
    ? new Date(firstMessage.datetime)
    : null;
  if (!firstMessageDate) return <></>;

  return (
    <HeaderContainer>
      {isLoading && (
        <LoaderSpinnerContainer>
          <LoaderSpinner size={space[10]} width={space[1]} />
        </LoaderSpinnerContainer>
      )}

      <Typography.SmallBody
        color={colors.fwdDarkGreen[50]}
        style={{ textAlign: 'center' }}>
        {formatDateWithTime(firstMessageDate)}
      </Typography.SmallBody>
    </HeaderContainer>
  );
}

interface ChatBubbleSeparatorI {
  messages: Message[];
  leadingMessage: Message;
}

const INTERVAL = 60 * 10;

export function ChatBubbleSeparator({
  messages,
  leadingMessage,
}: ChatBubbleSeparatorI) {
  const leadingItemIndex = messages.findIndex(m => m.id === leadingMessage.id);
  const nextMessage = messages[leadingItemIndex + 1];

  const leadingMessageDate = leadingMessage.datetime
    ? new Date(leadingMessage.datetime)
    : null;
  const nextMessageDate = nextMessage.datetime
    ? new Date(nextMessage.datetime)
    : null;
  if (!leadingMessageDate || !nextMessageDate) return <></>;

  // Check if time elapsed is greater than X
  // If so, we display next message date time
  const timeDiff =
    (nextMessageDate.getTime() - leadingMessageDate.getTime()) / 1000; //in ms

  if (Math.round(timeDiff) < INTERVAL) {
    return <View style={{ height: 12 }}></View>;
  }

  return (
    <SeparatorContainer>
      <Typography.SmallBody
        color={colors.fwdDarkGreen[50]}
        style={{ textAlign: 'center' }}>
        {formatDateWithTime(nextMessageDate)}
      </Typography.SmallBody>
    </SeparatorContainer>
  );
}
