import { Message, calculateTotalScore } from 'agent-guru';
import { View } from 'react-native';
import styled from '@emotion/native';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { Star, StarFill } from 'cube-ui-components/dist/cjs/icons';

const ConversationRatingContainer = styled(View)(
  ({ theme: { space, borderRadius } }) => ({
    flexDirection: 'column',
    alignSelf: 'flex-start',
    backgroundColor: colors.white,
    padding: space[2],
    borderRadius: borderRadius.large,
    marginTop: space[3],
    gap: space[2],
  }),
);

const ConversationTitle = styled(Typography.LargeBody)({
  color: colors.fwdDarkGreen[100],
  fontWeight: '400',
});

const ConversationTitleScore = styled(Typography.ExtraLargeBody)({
  color: colors.fwdOrange[100],
  fontWeight: '400',
});

const ConversationRatings = styled(Typography.LargeBody)(() => ({
  width: 120,
  color: colors.fwdDarkGreen[100],
}));

const StarsContainer = styled(View)({
  flexDirection: 'row',
  alignItems: 'center',
});

interface ChatBubbleRatingI {
  message: Message;
}

function ChatBubbleRatingStars({ nbStars }: { nbStars: number }) {
  return (
    <StarsContainer>
      {new Array(4).fill(0).map((_, index) => {
        return nbStars > index ? (
          <StarFill key={`star-${index}`} width={20} height={20} />
        ) : (
          <Star key={`star-${index}`} width={20} height={20} />
        );
      })}
    </StarsContainer>
  );
}

export default function ChatBubbleRating({ message }: ChatBubbleRatingI) {
  const { t } = useTranslation(['aiBot']);

  function convertScore(score: number) {
    return Math.ceil(score / 25);
  }

  if (!message.conversationRating) return <></>;

  return (
    <ConversationRatingContainer>
      <ConversationTitle>
        {t('aiBot:conversation.rating.summary.youGot')}
        <ConversationTitleScore>
          {calculateTotalScore(message.conversationRating)}
        </ConversationTitleScore>
        {t('aiBot:conversation.rating.summary.outOf100')}
      </ConversationTitle>

      <View style={{ flexDirection: 'row' }}>
        <ConversationRatings>
          {t('aiBot:conversation.rating.summary.fluency')}
        </ConversationRatings>
        <ChatBubbleRatingStars
          nbStars={convertScore(message.conversationRating.fluencyScore)}
        />
      </View>

      <View style={{ flexDirection: 'row' }}>
        <ConversationRatings>
          {t('aiBot:conversation.rating.summary.conversation')}
        </ConversationRatings>
        <ChatBubbleRatingStars
          nbStars={convertScore(message.conversationRating.conversationScore)}
        />
      </View>

      <View style={{ flexDirection: 'row' }}>
        <ConversationRatings>
          {t('aiBot:conversation.rating.summary.knowledge')}
        </ConversationRatings>
        <ChatBubbleRatingStars
          nbStars={convertScore(message.conversationRating?.knowledgeScore)}
        />
      </View>

      <View style={{ flexDirection: 'row' }}>
        <ConversationRatings>
          {t('aiBot:conversation.rating.summary.helpful')}
        </ConversationRatings>
        <ChatBubbleRatingStars
          nbStars={convertScore(message.conversationRating?.helpfulScore)}
        />
      </View>
    </ConversationRatingContainer>
  );
}
