import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Source } from 'agent-guru';
import { Box, Typography } from 'cube-ui-components';
import { ChevronDown, ChevronUp } from 'cube-ui-components/dist/cjs/icons';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking, TouchableOpacity, useWindowDimensions } from 'react-native';
import OutsidePressHandler from 'react-native-outside-press';

const Container = styled(Box)(({ theme }) => ({
  borderTop: `1px solid ${theme.colors.palette.fwdGrey[100]}`,
  marginTop: theme.space[4],
  zIndex: 0,
  elevation: 0,
}));

const SourceTitleContainer = styled(TouchableOpacity)(({ theme }) => ({
  marginVertical: theme.space[3],
  gap: theme.space[1],
  alignSelf: 'flex-start',
  alignItems: 'center',
  flexDirection: 'row',
}));

const SourceTitle = styled(Typography.Body)(({ theme }) => ({
  fontWeight: 700,
}));

const SourceContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  flexWrap: 'wrap',
  flexDirection: 'row',
  gap: theme.space[2],
  zIndex: 0,
  elevation: 0,
}));

const SourceItem = styled(TouchableOpacity)<{ isActive: boolean }>(
  ({ theme, isActive }) => ({
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: isActive
      ? theme.colors.palette.fwdOrange[20]
      : theme.colors.palette.white,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: theme.colors.palette.fwdOrange[50],
    cursor: 'pointer',
    position: 'relative',
    zIndex: 0,
    elevation: 0,
  }),
);

const ItemIndex = styled(Typography.Body)(({ theme }) => ({
  border: 'none',
  backgroundColor: theme.colors.palette.fwdOrange[20],
  paddingVertical: theme.space[1],
  paddingHorizontal: theme.space[2],
  width: theme.space[6],
  color: theme.colors.palette.fwdOrange[100],
  fontSize: 14,
}));

const ItemLabel = styled(Typography.Body)<{wrapperWidth: number}>(({ theme, wrapperWidth }) => ({
  overflow: 'hidden',
  marginHorizontal: theme.space[2],
  color: theme.colors.palette.fwdOrange[100],
  fontSize: 14,
  maxWidth: wrapperWidth || '100%',
}));

const ItemInfo = styled(TouchableOpacity)<{
  screenWidth: number;
  isTabletMode: boolean;
  itemLeft: number | undefined;
  itemRight: number | undefined;
}>(({ theme, screenWidth, isTabletMode, itemLeft, itemRight }) => ({
  position: 'absolute',
  // maxWidth: theme.spacing(100),
  padding: theme.space[3],
  backgroundColor: theme.colors.palette.white,
  // boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.12)',
  borderRadius: 8,
  bottom: theme.space[10],
  left: itemLeft,
  right: itemRight,
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  maxWidth: isTabletMode ? (screenWidth * 30) / 100 : (screenWidth * 70) / 100,
  zIndex: 10,
  elevation: 10,
}));

const SourcesDisclaimer = styled(Typography.Body)(({ theme }) => ({
  fontSize: 14,
  fontWeight: 400,
  marginTop: theme.space[4],
}));

const ItemCreatedDate = styled(Typography.Body)(() => ({
  fontSize: 12,
  fontWeight: 400,
}));

const ItemHyperlinkText = styled(Typography.Body)(() => ({
  fontSize: 14,
  fontWeight: 700,
}));

const ItemHyperlinkUrl = styled(Typography.Body)(({ theme }) => ({
  fontSize: 12,
  fontWeight: 400,
  overflow: 'hidden',
  color: theme.colors.palette.fwdOrange[100],
  textDecorationLine: 'underline',
}));

const Link = styled(TouchableOpacity)(({ theme }) => ({}));

interface ChatSourceListProps {
  sources: Source[];
  hasInternalSource: boolean;
  hasExternalSource: boolean;
  externalSourceTimestamp: number | null;
  messageId: string;
}

const ChatSourceList = ({
  sources,
  hasExternalSource,
  hasInternalSource,
  externalSourceTimestamp,
  messageId,
}: ChatSourceListProps) => {
  const { t } = useTranslation(['aiBot']);
  const { colors, space } = useTheme();
  const [expanded, setExpanded] = useState<boolean>(true);
  const [selectedItem, setSelectedItem] = useState<number>(-1);
  const { width: screenWidth } = useWindowDimensions();

  const itemRef = useRef(null);
  const { isTabletMode } = useLayoutAdoptionCheck();

  const [itemsPosition, setItemsPosition] = useState<
    Array<{ width: number; height: number; x: number; y: number }>
  >([]);

  const [wrapperWidth, setWrapperWidth] = useState<number>(0);
  // const [itemWidth, setItemWidth] = useState<number>(0);
  // const [itemOffsetLeft, setItemOffsetLeft] = useState<number>(0);

  const [itemLeft, setItemLeft] = useState<number | undefined>(undefined);
  const [itemRight, setItemRight] = useState<number | undefined>(undefined);

  useEffect(() => {
    const offset = new Array(sources.length).fill({});
    setItemsPosition(offset);
  }, [sources]);

  const handleSelectItem = useCallback(
    (index: number) => {
      if (index === selectedItem) {
        setSelectedItem(-1);
        return;
      }

      setSelectedItem(index);
    },
    [selectedItem],
  );

  const getCreatedDate = useCallback(
    (timestamp?: number): string => {
      if (!timestamp) {
        return t('aiBot:sourcesList.unavailableDate');
      }

      const createdDate = new Date(timestamp);
      const day = createdDate.toLocaleDateString([], { day: '2-digit' });
      const month = createdDate.toLocaleDateString([], { month: 'short' });
      const year = createdDate.toLocaleDateString([], { year: 'numeric' });
      return `${day} ${month} ${year}`;
    },
    [t],
  );

  useEffect(() => {
    if (selectedItem < 0) return;

    const activeItemPosition = itemsPosition[selectedItem];
    const itemOffsetLeft = activeItemPosition.x;
    const itemWidth = activeItemPosition.width;
    const itemOffsetRight = itemOffsetLeft + itemWidth;
    if (itemOffsetLeft > wrapperWidth / 3 && itemOffsetRight > wrapperWidth * 2 / 3) {
      // Show tooltip from right if item is on the right side
      setItemRight(space[3]);
      setItemLeft(undefined);
    } else {
      setItemLeft(space[3]);
      setItemRight(undefined);
    }
  }, [selectedItem, wrapperWidth, itemsPosition]);

  const handleOpenUrl = useCallback(async (url?: string) => {
    if (!url) return;

    const supported = await Linking.canOpenURL(url);

    if (supported) {
      await Linking.openURL(url);
    }
  }, []);

  const ChevronIcon = useMemo(
    () => (expanded ? ChevronUp : ChevronDown),
    [expanded],
  );
  const disclaimerText = useMemo(
    () =>
      hasInternalSource
        ? t('aiBot:sourcesList.internalExternalSourceDisclaimer', {
            createdDate: getCreatedDate(externalSourceTimestamp || undefined),
          })
        : t('aiBot:sourcesList.externalSourceDisclaimer', {
            createdDate: getCreatedDate(externalSourceTimestamp || undefined),
          }),
    [hasInternalSource, externalSourceTimestamp, getCreatedDate, t],
  );

  return (
    <Container>
      {hasExternalSource && (
        <SourcesDisclaimer>{disclaimerText}</SourcesDisclaimer>
      )}
      <SourceTitleContainer onPress={() => setExpanded(!expanded)}>
        <SourceTitle>
          {t('aiBot:sourcesList.title', {
            numberOfSources: sources.length.toString(),
          })}
        </SourceTitle>
        <ChevronIcon
          width={16}
          height={16}
          fill={colors.palette.fwdGreyDarker}
        />
      </SourceTitleContainer>
      {expanded && (
        <SourceContainer
          onLayout={event => {
            const { width } = event.nativeEvent.layout;
            setWrapperWidth(width);
          }}>
          {sources.map((source, index) => {
            const isActive = selectedItem === index;
            return (
              <SourceItem
                onLayout={event => {
                  const { width, height, x, y } = event.nativeEvent.layout;
                  setItemsPosition(prev => {
                    const newItemsPosition = [...prev];
                    newItemsPosition[index] = { width, height, x, y };
                    return newItemsPosition;
                  });
                }}
                isActive={isActive}
                ref={isActive ? itemRef : null}
                key={index}
                onPress={() => handleSelectItem(index)}>
                <ItemIndex>{index + 1}</ItemIndex>
                <ItemLabel numberOfLines={1} ellipsizeMode='tail' wrapperWidth={wrapperWidth - space[10]}>{source.title}</ItemLabel>
                {isActive && (
                  <ItemInfo
                    isTabletMode={isTabletMode}
                    itemLeft={itemLeft}
                    itemRight={itemRight}
                    screenWidth={screenWidth}
                    onPress={e => e.stopPropagation()}>
                    <OutsidePressHandler
                      onOutsidePress={() => handleSelectItem(selectedItem)}>
                      {source.hyperlink?.text && (
                        <ItemHyperlinkText>
                          {source.hyperlink?.text}
                        </ItemHyperlinkText>
                      )}
                      {source.hyperlink?.url && (
                        <Link
                          onPress={() => handleOpenUrl(source.hyperlink?.url)}>
                          <ItemHyperlinkUrl numberOfLines={1}>
                            {source.hyperlink?.url}
                          </ItemHyperlinkUrl>
                        </Link>
                      )}
                      {!source.hyperlink && (
                        <ItemCreatedDate
                          numberOfLines={1}
                          ellipsizeMode={'tail'}>
                          {t('aiBot:sourcesList.internalSourceInfo', {
                            createdDate: getCreatedDate(source.timestamp),
                          })}
                        </ItemCreatedDate>
                      )}
                    </OutsidePressHandler>
                  </ItemInfo>
                )}
              </SourceItem>
            );
          })}
        </SourceContainer>
      )}
    </Container>
  );
};

export default ChatSourceList;
