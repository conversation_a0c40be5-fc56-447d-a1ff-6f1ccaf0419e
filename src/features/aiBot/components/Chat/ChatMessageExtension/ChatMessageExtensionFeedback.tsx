import styled from '@emotion/native';
import { Box, Typography, Icon, Button } from 'cube-ui-components';
import { AI_FEEDBACK_OPTION, useMessageEvent } from 'agent-guru';
import {
  useAppStore,
  useNotificationStore,
  useSessionsStore,
} from 'features/aiBot/store';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ListItem from '../../Common/ListItem';
import FloatingTextInput from '../../Common/FloatingTextInput';
import { useTheme } from '@emotion/react';
import { TouchableOpacity } from 'react-native';
import useChatWrapper from 'features/aiBot/hooks/useChatWrapper';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';

const Container = styled(Box)(({ theme: { colors, borderRadius, space } }) => ({
  backgroundColor: colors.palette.fwdOrange[5],
  borderRadius: borderRadius.small,
  paddingVertical: space[4],
  paddingHorizontal: space[3],
  gap: space[2],
  marginTop: space[3],
}));

const CloseContainer = styled(TouchableOpacity)(({ theme: { space } }) => ({
  position: 'absolute',
  right: space[4],
  top: space[4],
  zIndex: 1,
}));

const TextContainer = styled(Box)(({ theme: { colors } }) => ({
  backgroundColor: colors.palette.white,
  flex: 1,
}));

const MiddleContainer = styled(Box)(({ theme: { space } }) => ({
  flexDirection: 'row',
  flexWrap: 'wrap',
  gap: space[1],
}));

const BottomContainer = styled(Box)(({ theme: { space } }) => ({
  flexDirection: 'row',
  gap: space[3],
}));

interface ChatMessageExtensionFeedbackProps {
  sessionId: string;
  messageId: string;
}

export default function ChatMessageExtensionFeedback({
  sessionId,
  messageId,
}: ChatMessageExtensionFeedbackProps) {
  const getMessage = useSessionsStore(state => state.getMessage);

  const removeMessageExtensionFeedback = useSessionsStore(
    state => state.removeMessageExtensionFeedback,
  );

  const { space, sizes, colors } = useTheme();

  const [feedbackReason, setFeedbackReason] = useState<string>('');

  const [feedbackComment, setFeedbackComment] = useState<string>('');

  const [isLoading, setIsLoading] = useState(false);

  const { t } = useTranslation('aiBot');

  const message = useMemo(
    () => getMessage(sessionId, messageId),
    [getMessage, messageId, sessionId],
  );

  const { submitFeedback } = useMessageEvent(
    useChatWrapper(),
    useAppStore,
    useNotificationStore,
    useSessionsStore,
    message,
  );

  const isSubmitDisabled = useMemo(
    () => !feedbackReason || isLoading,
    [feedbackReason, isLoading],
  );

  async function onSubmitFeedback() {
    setIsLoading(true);

    await submitFeedback(feedbackReason, feedbackComment);

    setIsLoading(false);
  }

  return (
    <Container>
      <CloseContainer
        activeOpacity={ACTIVE_OPACITY}
        onPress={() => {
          removeMessageExtensionFeedback(sessionId, messageId);
        }}>
        <Icon.Close size={24} />
      </CloseContainer>
      <Typography.Body>{t('feedback.form.title')}</Typography.Body>
      <MiddleContainer>
        {AI_FEEDBACK_OPTION.map(option => {
          const { key, name, id } = option;
          const isSelected = id === feedbackReason;
          return (
            <ListItem
              key={id}
              isSelected={isSelected}
              isDisabled={isLoading}
              onPress={() => {
                if (isLoading) return;
                setFeedbackReason(id);
              }}>
              <Typography.Body
                fontWeight={isSelected ? 'bold' : 'normal'}
                style={{
                  color: isSelected
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdDarkGreen[100],
                }}>
                {key ? t(key, { defaultValue: name }) : name}
              </Typography.Body>
            </ListItem>
          );
        })}
      </MiddleContainer>
      <BottomContainer>
        <TextContainer>
          <FloatingTextInput
            value={feedbackComment}
            onChange={text => {
              setFeedbackComment(text);
            }}
            label={t('feedback.screen.comment') ?? ''}
            multiline={true}
            disabled={isLoading}
            additionalStyles={{
              paddingTop: space[4],
              paddingBottom: space[4],
              paddingHorizontal: space[4],
              maxHeight: 200,
            }}
          />
        </TextContainer>
        <Button
          text={t('feedback.form.submit')}
          variant="primary"
          disabled={isSubmitDisabled}
          loading={isLoading}
          style={{ minWidth: sizes[39] }}
          onPress={onSubmitFeedback}
        />
      </BottomContainer>
    </Container>
  );
}
