import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, Typography } from 'cube-ui-components';
import useLinkPreview from 'features/aiBot/hooks/useLinkPreview';
import useLinkRedirect from 'features/aiBot/hooks/useLinkRedirect';
import { MessageExtensionDeepLink } from 'agent-guru';
import { useCallback, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';

const Container = styled(TouchableOpacity)(
  ({ theme: { space, borderRadius, colors } }) => ({
    alignItems: 'flex-start',
    width: '100%',
    backgroundColor: colors.palette.white,
    marginBottom: space[3],
    borderRadius: borderRadius.small,
    borderWidth: 1,
    borderColor: colors.palette.fwdOrange[100],
    maxWidth: space[60],
    padding: space[3],
  }),
);

const TitleContainer = styled(Box)(({ theme: { space } }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-start',
  alignItems: 'center',
  width: '100%',
  gap: space[1],
}));

const PreviewBox = styled(Box)(() => ({
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  alignSelf: 'center',
  position: 'absolute',
}));

const PreviewContent = styled(Box)(() => ({
  justifyContent: 'center',
  alignSelf: 'center',
  alignContent: 'center',
}));

interface ChatMessageExtensionLinkProps {
  extension: MessageExtensionDeepLink;
  sessionId: string;
  messageId: string;
}

export default function ChatMessageExtensionLink({
  extension,
}: ChatMessageExtensionLinkProps) {
  const { colors, space } = useTheme();

  const redirect = useLinkRedirect();

  const { getPreviewProps } = useLinkPreview();

  const previewProps = useMemo(
    () => getPreviewProps(extension.linkId),
    [extension],
  );

  const onPressRedirect = useCallback(() => {
    // trigger the redirect
    redirect(extension.linkId);
  }, [extension.linkId]);

  const Link = useMemo(
    () => (
      <TitleContainer>
        <Typography.LargeBody
          fontWeight="bold"
          color={colors.palette.fwdOrange[100]}>
          {extension.linkTitle}
        </Typography.LargeBody>
        <Icon.ChevronRight size={14} />
      </TitleContainer>
    ),
    [extension.linkTitle],
  );

  if (!previewProps) {
    return <Box marginBottom={space[3]}>{Link}</Box>;
  }

  const { containerStyle = {}, contentStyle = {}, Component } = previewProps;

  return (
    <Container activeOpacity={ACTIVE_OPACITY} onPress={onPressRedirect}>
      <Box height={space[31]} width={'100%'} overflow="hidden">
        <PreviewBox style={containerStyle}>
          <PreviewContent style={contentStyle}>
            <Box pointerEvents="none">{Component}</Box>
          </PreviewContent>
        </PreviewBox>
      </Box>
      <Box>{Link}</Box>
    </Container>
  );
}
