import { Image, View } from 'react-native';
import { ChatBotAnimGIF } from 'features/aiBot/assets/images';
import styled from '@emotion/native';

const Container = styled(View)(({ theme: { space } }) => ({
  paddingVertical: space[4],
  paddingHorizontal: space[8],
}));

const BotAnim = styled(Image)(() => ({
  width: 81,
  height: 81,
}));

export default function ChatLoading() {
  return (
    <Container>
      <BotAnim source={ChatBotAnimGIF} />
    </Container>
  );
}
