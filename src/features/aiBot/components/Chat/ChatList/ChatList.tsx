import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  FeedbackConstant,
  Message,
  selectCurrentSessionId,
  throttle,
  useConversationFeedback,
  useSessionListener
} from 'agent-guru';
import { Box } from 'cube-ui-components';
import useChatHistoryWrapper from 'features/aiBot/hooks/useChatHistoryWrapper';
import { useAppStore, useSessionsStore } from 'features/aiBot/store';
import { sortMessages } from 'features/aiBot/utils/sort/messageSort.utils';
import useBoundStore from 'hooks/useBoundStore';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, NativeScrollEvent, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BottomNotification from '../../Notifications/BottomNotification';
import ChatHomeSuggestions from '../ChatHome/ChatHomeSuggestions';
import { ChatListHeader } from './ChatListHeader';
import ChatMessage from './ChatListMessage';
import { ChatListSeparator } from './ChatListSeparator';

const Container = styled(Box)(() => ({
  flex: 1,
  width: '100%',
}));

interface ChatListProps {
  isLoading: boolean;
  disableScroll: boolean;
  styleProps?: { paddingTop?: number };
  onScrollChat?: (scrollY: number) => void;
}

function ChatList({
  isLoading,
  disableScroll,
  styleProps = {},
  onScrollChat,
}: ChatListProps) {
  const { space } = useTheme();
  const { t } = useTranslation('aiBot');
  const chatContainerRef = useRef<FlatList<Message>>(null);
  const currentSessionId = useAppStore(selectCurrentSessionId);
  const agentId = useBoundStore.getState().auth.agentCode || '';
  const chatRatingTimeoutNumberId = useRef<NodeJS.Timeout|null>(null);
  const { isLoadingSessionMessage, isLoadingSessionLatestMessage, getSessionMessagesAndFormat } = useChatHistoryWrapper();
  const { handleShowChatRating, shouldShowRating } = useConversationFeedback({
    currentSessionId: currentSessionId || '',
    agentId,
    useAppStore,
    useSessionsStore
  });


  const scrollCounter = useAppStore(state => state.scrollCounter)
 
  const setShowIntroMessage = useAppStore(state => state.setShowIntroMessage);

  const session = useSessionListener(useAppStore, useSessionsStore);

  // Keep track of the previous messages to handle the scroll
  const chatMessagePrev = useRef<Message[]>([]);


  const { top } = useSafeAreaInsets();

  const messages = useMemo(() => {
    return sortMessages(session?.messages ?? []);
  }, [session]);

  useEffect(() => {
    if (chatRatingTimeoutNumberId?.current) {
      clearTimeout(chatRatingTimeoutNumberId.current)
    }
    if (shouldShowRating && messages.length && !session?.uiParams?.isStarRatingShowed) {
      chatRatingTimeoutNumberId.current = setTimeout(() => handleShowChatRating(), FeedbackConstant.CONVERSATION_STAR_RATING_TIMEOUT);
    }
  }, [messages, handleShowChatRating, shouldShowRating, session?.uiParams?.isStarRatingShowed])

  useEffect(() => {
    return () => {
      chatMessagePrev.current = []
    }
  }, [])

  useEffect(() => {
    if (messages.length > 0) {
      setShowIntroMessage(false);
    }
  }, [messages, setShowIntroMessage]);

  useEffect(() => {
    setTimeout(() => {
      if (disableScroll && chatContainerRef.current) {
        chatContainerRef.current.scrollToEnd({ animated: true });
        chatContainerRef.current &&
          chatContainerRef.current.setNativeProps({ scrollEnabled: false });
      }
      if (!disableScroll && chatContainerRef.current)
        chatContainerRef.current.setNativeProps({ scrollEnabled: true });
    });
  }, [disableScroll]);

  useEffect(() => {
    setTimeout(() => {
      if (chatContainerRef.current && (chatMessagePrev.current.length === 0 || scrollCounter)) {
        chatContainerRef.current.scrollToEnd({ animated: !disableScroll });
        chatMessagePrev.current = messages
      }
    });
  }, [disableScroll, messages, scrollCounter]);

  const keyExtractor = useCallback(
    (message: Message) => `${message.userType}-${message.id}`,
    [],
  );

  const itemSeparatorComponent = useCallback(
    ({ leadingItem: leadingMessage }: { leadingItem: Message }) => {
      return (
        <ChatListSeparator
          leadingMessage={leadingMessage}
          messages={messages}
        />
      );
    },
    [messages],
  );

  function renderItem({ item }: { item: Message; index: number }) {
    if (!currentSessionId) return <></>;
    return <ChatMessage sessionId={currentSessionId} message={{ ...item }} />;
  }

  const updateScrollEvent = useCallback(
    throttle(scrollY => {
      if (onScrollChat) onScrollChat(scrollY);
    }, 100),
    [throttle, onScrollChat],
  );

  const hasScrollReachedTop = ({ contentOffset }: NativeScrollEvent) => {
    return contentOffset.y < -40;
  };

  if (messages.length === 0) {
    return (
      <Container>
        <ScrollView
          onScroll={event => {
            updateScrollEvent(event.nativeEvent.contentOffset.y);
          }}
          style={{ marginTop: 0 }}
          contentContainerStyle={{
            // Top inset + Header height + Add space
            paddingTop: top + space[11] + space[6],
            paddingHorizontal: space[4],
          }}>
          <ChatHomeSuggestions />
        </ScrollView>
      </Container>
    );
  }

  return (
    <Box
      flex={1}
      width={'100%'}
      onLayout={() => {
        // Scroll to bottom even when the chat is disabled
        // If inline modal we have to force scroll to bottom
        if (disableScroll && chatContainerRef.current) {
          chatContainerRef.current.scrollToEnd({ animated: false });
        }
      }}>
      <FlatList
        ref={chatContainerRef}
        data={messages}
        scrollEnabled={!disableScroll}
        ItemSeparatorComponent={itemSeparatorComponent}
        renderItem={renderItem}
        onScroll={event => {
          // Only fetch and format session messages when session is from BE 
          if (hasScrollReachedTop(event.nativeEvent) && currentSessionId && !session?.uiParams?.isLoading && session?.isConversation && !session?.isSimulatedSession) {
            getSessionMessagesAndFormat(currentSessionId);
          }
          updateScrollEvent(event.nativeEvent.contentOffset.y);
        }}
        ListFooterComponent={() => (
          <Box height={isLoading ? space[8] : 0}></Box>
        )}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={() => (
          <ChatListHeader
            isLoading={isLoadingSessionMessage || isLoadingSessionLatestMessage || false}
            firstMessage={messages[0]}
            top={styleProps?.paddingTop ?? 0}
          />
        )}
      />

      <BottomNotification bottom={space[6]} />
    </Box>
  );
}

export default ChatList;
