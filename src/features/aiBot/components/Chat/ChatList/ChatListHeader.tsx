import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { Message, formatDateWithTime } from 'agent-guru';
import { useMemo } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LoaderSpinner from '../../Common/LoaderSpinner';

const Container = styled(Box)<{ marginTop: number }>(
  ({ theme, marginTop }) => ({
    marginBottom: theme.space[2],
    marginTop,
  }),
);

const DateText = styled(Typography.SmallLabel)(() => ({
  color: colors.fwdDarkGreen[50],
  textAlign: 'center',
}));

const SpinnerContainer = styled(Box)(({ theme: { space } }) => ({
  paddingTop: space[3],
  paddingBottom: space[3],
  alignItems: 'center',
  justifyContent: 'center',
}));

interface ChatBubbleHeaderI {
  firstMessage?: Message;
  top?: number;
  isLoading: boolean;
}

export function ChatListHeader({ firstMessage, isLoading }: ChatBubbleHeaderI) {
  const insets = useSafeAreaInsets();

  const { space, sizes } = useTheme();

  const containerMarginTop = useMemo(
    () => space[12] + insets.top,
    [space, insets],
  );

  const LoadingComp = (
    <SpinnerContainer>
      <LoaderSpinner size={sizes[8]} width={sizes[1]} />
    </SpinnerContainer>
  );

  const firstMessageDate = firstMessage?.datetime
    ? new Date(firstMessage.datetime)
    : null;

  if (!firstMessage || !firstMessageDate)
    return isLoading ? LoadingComp : <></>;

  return (
    <Container marginTop={containerMarginTop}>
      {isLoading ? LoadingComp : <></>}
      <DateText>{formatDateWithTime(firstMessageDate)}</DateText>
    </Container>
  );
}
