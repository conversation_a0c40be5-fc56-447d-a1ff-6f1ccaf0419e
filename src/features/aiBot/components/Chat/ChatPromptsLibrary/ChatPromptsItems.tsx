import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Prompt, selectCurrentSessionId } from 'agent-guru';
import LoadingIndicator from 'components/LoadingIndicator';
import { ITEMS_PER_PAGE } from 'constants/prompts';
import { Box, Typography } from 'cube-ui-components';
import PromptIconComp from 'features/aiBot/components/Common/Icons/PromptIconComp';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';
import useSuggestions from 'features/aiBot/hooks/useSuggestions';
import { useAppStore } from 'features/aiBot/store';
import { usePromptsStore } from 'features/aiBot/store/prompsStore';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, NativeScrollEvent, TouchableOpacity , useWindowDimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface ChatPromptsItemsProps {
  suggestions: Prompt[];
  scrollEnabled: boolean;
  handleAfterPressingPrompt?: () => void;
  fetchAllPrompts?: ({isFetchingMore, itemsPerPage}: {isFetchingMore?: boolean, itemsPerPage?: number}) => void;
}
const SuggestionItem = styled(TouchableOpacity)(
  ({ theme: { colors, space, borderRadius } }) => ({
    width: '49%',
    backgroundColor: colors.palette.white,
    borderRadius: borderRadius.small,
    padding: space[3],
    borderColor: colors.palette.white,
    borderWidth: 1,
  }),
);

const SuggestionIconContainer = styled(Box)(({theme}) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.space[2],
  marginBottom: theme.space[2],
}));

const NewTag = styled(Box)(({ theme: { space, colors, borderRadius } }) => ({
  position: 'absolute',
  right: 0,
  paddingVertical: space[1],
  paddingHorizontal: space[2],
  backgroundColor: colors.palette.fwdOrange[100],
  borderBottomLeftRadius: borderRadius.small,
  borderTopRightRadius: borderRadius.small
}));

const LoadingContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  paddingVertical: theme.space[6],
}));

const ChatPromptsItems = ({ suggestions, scrollEnabled, handleAfterPressingPrompt, fetchAllPrompts }: ChatPromptsItemsProps) => {
  const { space, colors } = useTheme();
  const { onPressSuggestion } = useSuggestions();
  const currentSessionId = useAppStore(selectCurrentSessionId);
    const { t } = useTranslation('aiBot');
  const isFetchingPrompts = usePromptsStore(state => state.isFetchingPrompts)

  const { height: screenHeight} = useWindowDimensions();
  const { bottom: bottomInset } = useSafeAreaInsets();

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }: NativeScrollEvent) => {
    const paddingToBottom = -10;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };
  

  const renderItem = useCallback(
    ({ item }: { item: Prompt }) => {
      return (
        <SuggestionItem
          activeOpacity={ACTIVE_OPACITY}
          onPress={() => {
            onPressSuggestion(item);
            handleAfterPressingPrompt?.();
          }}>
          <SuggestionIconContainer>
            <PromptIconComp iconName={item.iconName} />
            <Box flexDirection='row' alignItems='center' maxWidth={item.isNew ? '75%' : '100%'}>
              <Typography.Body
                style={{flex: 1}}
                fontWeight="bold"
                numberOfLines={1}
                ellipsizeMode="tail">
                  {item.title}
              </Typography.Body>
            </Box>
          </SuggestionIconContainer>

          <Typography.Body
            numberOfLines={2}
            ellipsizeMode="tail"
            color={colors.palette.fwdGreyDarkest}>
            {item.subtitle}
          </Typography.Body>
          {item.isNew && (
            <NewTag>
              <Typography.SmallLabel fontWeight="bold" color={colors.palette.white}>
                {t('promptGallery.new')}
              </Typography.SmallLabel>
            </NewTag>
          )}
        </SuggestionItem>
      );
    },
    [currentSessionId],
  );

  return (
    <Box style={{
      maxHeight: screenHeight - 180 - bottomInset,
      height: '100%'
    }}>
      <FlatList
        contentContainerStyle={{ gap: space[2] }}
        columnWrapperStyle={{ gap: space[2] }}
        scrollEnabled={scrollEnabled}
        onScroll={({ nativeEvent }) => {
          if (isCloseToBottom(nativeEvent) && scrollEnabled) {
            fetchAllPrompts?.({
              isFetchingMore: true,
              itemsPerPage: ITEMS_PER_PAGE.MOBILE
            });
            // Handle scroll to bottom if needed
          }
        }}
        style={{
          flexGrow: scrollEnabled ? 0 : 1
        }}
        numColumns={2}
        data={suggestions}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        ListFooterComponent={() => {
          if (!scrollEnabled || !isFetchingPrompts) {
            return <></>;
          }
  
          return (<LoadingContainer>
            <LoadingIndicator size={space[8]} />
          </LoadingContainer>)
        }}
      />
    </Box>
  );
};

export default ChatPromptsItems;
