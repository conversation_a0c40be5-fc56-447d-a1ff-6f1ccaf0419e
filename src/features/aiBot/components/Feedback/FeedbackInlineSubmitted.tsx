import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Icon, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const Container = styled(Box)<{ marginbottom: number }>(
  ({ theme: { space }, marginbottom }) => ({
    paddingTop: space[4],
    paddingBottom: space[6],
    marginBottom: marginbottom,
  }),
);

const Content = styled(Box)(({ theme: { space } }) => ({
  marginHorizontal: space[4],
  alignItems: 'center',
}));

interface FeedbackInlineSubmittedProps {
  onPress: () => void;
}

export default function FeedbackInlineSubmitted({
  onPress,
}: FeedbackInlineSubmittedProps) {
  const { colors, sizes, space } = useTheme();

  const { t } = useTranslation('aiBot');

  const insets = useSafeAreaInsets();

  return (
    <Container marginbottom={insets.bottom}>
      <Content>
        <Icon.TickCircleFill fill={colors.palette.alertGreen} size={63} />

        <Typography.ExtraLargeBody fontWeight="bold">
          {t('feedback.thankyou')}
        </Typography.ExtraLargeBody>

        <Typography.LargeBody
          style={{
            marginTop: space[2],
            maxWidth: sizes[80],
            textAlign: 'center',
          }}>
          {t('feedback.success.text')}
        </Typography.LargeBody>

        <Button
          text={t('feedback.backToHome')}
          style={{ width: '100%', marginTop: sizes[6] }}
          onPress={onPress}
        />
      </Content>
    </Container>
  );
}
