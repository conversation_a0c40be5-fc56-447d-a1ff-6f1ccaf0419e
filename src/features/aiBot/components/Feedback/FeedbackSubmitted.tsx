import styled from '@emotion/native';
import { Button, Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { useTranslation } from 'react-i18next';
import { Image, View } from 'react-native';
import { FeedbackDocument } from 'features/aiBot/assets/images';

//'features/aiBot/assets/images/feedback_document.png';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';

const Container = styled(View)(() => ({
  flex: 1,
  backgroundColor: colors.white,
}));

const Content = styled(View)(({ theme: { space } }) => ({
  paddingHorizontal: space[4],
  width: '100%',
  marginTop: space[4],
  alignItems: 'center',
}));

const ButtonContainer = styled(View)(({ theme: { space } }) => ({
  marginTop: space[6],
  gap: space[4],
  width: '100%',
}));

const Text = styled(Typography.LargeBody)(() => ({
  textAlign: 'center',
  maxWidth: 280,
  margin: 'auto',
}));

const Title = styled(Typography.ExtraLargeBody)(({ theme: { space } }) => ({
  marginBottom: space[2],
  maxWidth: 200,
  textAlign: 'center',
}));

const FeedbackImage = styled(Image)(({ theme: { space } }) => ({
  width: 165,
  height: 165,
  marginTop: space[5],
}));

interface AiBotFeedbackSubmittedProps {
  resetStep: () => void;
}

export default function AiBotFeedbackSubmitted({
  resetStep,
}: AiBotFeedbackSubmittedProps) {
  const { t } = useTranslation('aiBot');

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <Container>
      <Content>
        <FeedbackImage source={FeedbackDocument} />
        <Title fontWeight="bold">
          {t('aiBot:feedback.screen.thankFeedbackTitle')}
        </Title>
        <Text>{t('aiBot:feedback.screen.thankFeedbackText')}</Text>

        <ButtonContainer>
          <Button
            text={t('aiBot:feedback.screen.submitAnotherResponse') ?? ''}
            onPress={() => resetStep()}
            variant="primary"
          />

          <Button
            text={t('aiBot:feedback.screen.backToCube') ?? ''}
            onPress={() =>
              navigation.navigate('Main', {
                screen: 'Home',
              })
            }
            variant="secondary"
          />
        </ButtonContainer>
      </Content>
    </Container>
  );
}
