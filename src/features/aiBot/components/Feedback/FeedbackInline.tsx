import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAppStore, useSessionsStore } from 'features/aiBot/store';
import InlineModal from '../Modals/InlineModal';
import {
  Session,
  MessageCustomType,
  MessageDataStatus,
  UserType,
  selectCurrentSessionId,
  Message,
  MessageRatingData,
  MessageDataType,
} from 'agent-guru';
import FeedbackInlineForm from './FeedbackInlineForm';
import FeedbackInlineSubmitted from './FeedbackInlineSubmitted';
import { sendAppFeedbackRequest } from 'features/aiBot/api/feedbackApi';
import useBoundStore from 'hooks/useBoundStore';
import { getEnvVariable } from 'features/aiBot/utils/misc/misc';
import { EnvVariables } from 'features/aiBot/constants/EnvVariables';

interface FeedbackInlineProps {
  show: boolean;
  session: Session | null;
}

export default function FeedbackInline({ show, session }: FeedbackInlineProps) {
  const [step, setStep] = useState<'form' | 'submitted'>('form');

  const nextStep = () => setStep('submitted');

  const resetStep = () => setStep('form');

  const [closing, setClosing] = useState(false);

  const [isSubmitted, setSubmitted] = useState<boolean>(false);

  const agentCodeStore = useBoundStore(state => state.auth.agentCode);

  const currentSessionId = useAppStore(selectCurrentSessionId);

  const removeMessage = useSessionsStore(state => state.removeMessage);

  const updateMessageData = useSessionsStore(state => state.updateMessageData);


  useEffect(() => {
    return () => {
      resetStep();
    };
  }, []);

  useEffect(() => {
    setClosing(false);
    setSubmitted(false);
  }, [currentSessionId])

  const updateSessionState = useSessionsStore(
    state => state.updateSessionState,
  );

  const message = useMemo(():
    | null
    | (Message & { data: MessageRatingData }) => {
    if (!session) return null;

    for (let i = session.messages.length - 1; i >= 0; i--) {
      if (
        session.messages[i].userType === UserType.CUSTOM &&
        session.messages[i].customType === MessageCustomType.RATING &&
        session.messages[i].data?.type === MessageDataType.RATING
      ) {
        return session.messages[i] as Message & { data: MessageRatingData };
      }
    }

    return null;
  }, [session]);

  useEffect(() => {
    if (!message) return;

    const messageDataStatus = message.data?.status || '';

    if (
      step === 'form' &&
      !(messageDataStatus === MessageDataStatus.IN_PROGRESS) &&
      !(messageDataStatus === MessageDataStatus.SUBMITTED)
    ) {
      resetStep();
    }

  }, [closing, message, step]);


  useEffect(() => {
    if (!message || !currentSessionId) return;

    if (closing && !isSubmitted) {
      removeMessage(currentSessionId, message.id);
      updateSessionState(currentSessionId, {})
    }
  }, [message, isSubmitted, currentSessionId, closing, removeMessage])

  if (!show || !message) return <></>;

  return (
    <InlineModal
      show={true}
      onClose={() => {
        setClosing(true);
        if (!session?.sessionId) return;
    
        updateSessionState(session.sessionId, {
          isWaitingForFeedback: false,
        });

        resetStep();
      }}>
      {step === 'form' && (
        <FeedbackInlineForm
          messageId={message.id}
          messageData={message.data}
          onSubmitCompleted={() => {
            nextStep();
            setSubmitted(true);
          }}
        />
      )}
      {step === 'submitted' && (
        <FeedbackInlineSubmitted
          onPress={() => {
            if (!session?.sessionId) return;

            updateSessionState(session.sessionId, {
              isWaitingForFeedback: false,
            });

            resetStep();
          }}
        />
      )}
    </InlineModal>
  );
}
