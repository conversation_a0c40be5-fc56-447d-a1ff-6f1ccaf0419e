import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Button, Typography } from 'cube-ui-components';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';
import FloatingTextInput from '../Common/FloatingTextInput';
import {
  MessageDataStatus,
  MessageRatingData,
  selectCurrentSessionId,
  useConversationFeedback,
  useSessionListener,
} from 'agent-guru';
import { useAppStore, useSessionsStore } from 'features/aiBot/store';
import useBoundStore from 'hooks/useBoundStore';
import ChatMessageRating from '../Chat/ChatMessage/ChatMessageCustom/ChatMessageRating';
import { Separator } from 'components/SearchableDropdownPanel';

const Content = styled(View)(({ theme: { space } }) => ({
  paddingHorizontal: space[4],
  width: '100%',
  marginTop: space[4],
}));

const InputContainer = styled(View)(({ theme: { space } }) => ({
  marginTop: space[4],
}));

const ButtonContainer = styled(View)(({ theme: { space } }) => ({
  marginTop: space[5],
}));

const SubmitButton = styled(Button)(({ theme: { borderRadius } }) => ({
  borderRadius: borderRadius.large,
}));

const FeedbackReasonContainer = styled(View)(({ theme: { space } }) => ({
  gap: space[1],
  flexWrap: 'wrap',
  flexDirection: 'row',
  marginTop: space[3],
}));

const FeedbackReasonItem = styled(Pressable)<{ isSelected: boolean }>(
  ({ isSelected, theme: { space, borderRadius, colors } }) => ({
    borderWidth: 1,
    borderColor: isSelected
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
    backgroundColor: isSelected
      ? colors.palette.fwdOrange[5]
      : colors.palette.white,
    paddingVertical: space[2],
    paddingHorizontal: space[3],
    borderRadius: borderRadius['xx-large'],
  }),
);

interface FeedbackInlineFormProps {
  messageData: MessageRatingData;
  messageId: string;
  onSubmitCompleted: () => void;
}

export default function FeedbackInlineForm({
  messageData,
  messageId,
  onSubmitCompleted,
}: FeedbackInlineFormProps) {
  const currentSessionId = useAppStore(selectCurrentSessionId) || '';
  
  const currentSession = useSessionListener(useAppStore, useSessionsStore);

  const rating = useMemo(() => currentSession?.chatConversationRating?.rating || 0, [currentSession?.chatConversationRating]);

  const comment = useMemo(() => currentSession?.chatConversationRating?.comment || '', [currentSession?.chatConversationRating]);
  
  const reason = useMemo(() => currentSession?.chatConversationRating?.reason || '', [currentSession?.chatConversationRating]);

  const isLoading = useMemo(
    () => messageData?.status === MessageDataStatus.IN_PROGRESS,
    [messageData],
  );

  const updateMessageData = useSessionsStore(state => state.updateMessageData);

	const updateChatConversationRating = useSessionsStore(state => state.updateChatConversationRating);

  const { space, colors } = useTheme();

  const [error, setError] = useState('');

  const agentCodeStore = useBoundStore(state => state.auth.agentCode) || '';

  const { sendFeedback } = useConversationFeedback({
    currentSessionId,
    agentId: agentCodeStore,
    useAppStore,
    useSessionsStore,
  });

  const { t } = useTranslation('aiBot');

  async function submit() {
    setError('');

    if (!agentCodeStore || !currentSessionId || !currentSession) return;

    updateMessageData(currentSessionId, messageId, {
      ...messageData,
      status: MessageDataStatus.IN_PROGRESS,
    });

    let lastMessageId;

		for (let i = 0; i < currentSession.messages.length; i++) {
			if (currentSession.messages[i].id === messageId) {
				lastMessageId = currentSession.messages[i - 1]?.id;

				break;
			}
		}
    
    const [success, _] = await sendFeedback({
      agentId: agentCodeStore,
      rating: rating,
      comment,
      reason,
      lastMessageId
    })

    if (!success) {
      updateMessageData(currentSessionId, messageId, {
        ...messageData,
        status: MessageDataStatus.ERROR,
      });
      setError(t('error.connection'));
      return;
    }

    updateMessageData(currentSessionId, messageId, {
      ...messageData,
      status: MessageDataStatus.SUBMITTED,
    });

		updateChatConversationRating(currentSession?.sessionId, {
      ...currentSession.chatConversationRating,
			rating: rating,
			lastMessageId
		})

    onSubmitCompleted();
  }

  const FEEDBACK_REASONS: { text: string; value: string }[] = useMemo(
    () => {
      if (rating < 3) {
        return [
          { text: t('feedback.options.inaccurate'), value: 'inaccurate' },
          { text: t('feedback.options.nothelpful'), value: 'nothelpful' },
          { text: t('feedback.options.slow'), value: 'slow' },
          { text: t('feedback.options.other'), value: 'other' },
        ]
      } else if (rating < 5) {
        return [
          { text: t('feedback.options.accuracy'), value: 'accuracy' },
          { text: t('feedback.options.response-speed'), value: 'response-speed' },
          { text: t('feedback.options.relevancy'), value: 'relevancy' },
          { text: t('feedback.options.other'), value: 'other' },
        ]
      }

      return []
    },
    [t, rating],
  );

  const isSubmitActive = useMemo(
    () =>
      rating !== 0 &&
      ((rating === 5 && !!comment) || (rating < 5 && !!reason) ),
    [comment, rating, reason],
  );

  const feedbackTitle = useMemo(() =>
    rating < 3
      ? t('feedback.screen.feedbackTitle.dislike')
      : rating < 5
      ? t('feedback.screen.feedbackTitle.improvement')
      : t('feedback.screen.feedbackTitle.tellUsMore'),
    [rating, t]
  );

  return (
    <View style={{ paddingBottom: space[10] }}>
      <ChatMessageRating session={currentSession} allowRating={true} />

      <Separator />

      <Content>
        <Typography.LargeBody fontWeight="bold">
          {feedbackTitle}
        </Typography.LargeBody>

        <FeedbackReasonContainer>
          {FEEDBACK_REASONS.map(option => (
            <FeedbackReasonItem
              onPress={() => {
                if (!currentSessionId) return;
                updateChatConversationRating(currentSessionId, {
                  ...currentSession?.chatConversationRating,
                  reason: option.value
                });
                // setFeedbackReason(option.value);
              }}
              key={option.value}
              isSelected={reason === option.value}
              disabled={isLoading}
              style={{ opacity: isLoading ? 0.6 : 1 }}>
              {({ pressed }) => (
                <Typography.Body
                  style={{
                    color:
                      pressed || reason === option.value
                        ? colors.palette.fwdOrange[100]
                        : colors.palette.fwdDarkGreen[100],
                  }}>
                  {option.text}
                </Typography.Body>
              )}
            </FeedbackReasonItem>
          ))}
        </FeedbackReasonContainer>
      </Content>

      <Content>
        <InputContainer>
          <FloatingTextInput
            value={comment}
            onChange={text => {
              if (!currentSessionId) return;
              updateChatConversationRating(currentSessionId, {
                ...currentSession?.chatConversationRating,
                comment: text
              });
            }}
            label={t('feedback.screen.comment') ?? ''}
            multiline={true}
            disabled={isLoading}
            additionalStyles={{
              paddingTop: space[4],
              paddingBottom: space[4],
              paddingHorizontal: space[4],
              maxHeight: 200, //inputMaxHeight,
            }}
          />
        </InputContainer>

        <ButtonContainer>
          <SubmitButton
            text={t('feedback.screen.submit') ?? ''}
            loading={isLoading}
            disabled={!isSubmitActive}
            onPress={() => (isSubmitActive ? submit() : undefined)}
            contentStyle={{
              backgroundColor: isSubmitActive
                ? colors.palette.fwdOrange[100]
                : colors.palette.fwdDarkGreen[50],
            }}
            variant="primary"
          />
        </ButtonContainer>

        <View style={{ marginTop: space[1] }}>
          <Typography.LargeBody color={colors.palette.alertRed}>
            {error}
          </Typography.LargeBody>
        </View>
      </Content>
    </View>
  );
}
