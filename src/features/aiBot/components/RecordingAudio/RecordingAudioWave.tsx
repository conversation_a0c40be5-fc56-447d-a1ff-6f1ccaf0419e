import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useEffect, useMemo, useRef } from 'react';
import { LayoutChangeEvent, View } from 'react-native';
import Animated from 'react-native-reanimated';

const Container = styled(View)<{ height: number }>(
  ({ theme: { colors }, height }) => ({
    height,
    width: '100%',
    backgroundColor: colors.palette.white,
    position: 'relative',
  }),
);

const Content = styled(Animated.View)(({ theme: { colors } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: 2,
  backgroundColor: colors.palette.white,
  position: 'absolute',
  top: 0,
  right: 0,
  left: 0,
  bottom: 0,
  flex: 1,
  justifyContent: 'center',
  overflow: 'hidden',
}));

const Bar = styled(View)<{ barColor: string; barWidth: number }>(
  ({ theme: { borderRadius }, barColor, barWidth: width }) => ({
    width,
    backgroundColor: barColor,
    borderRadius: borderRadius['x-small'],
    transitionDuration: '100ms',
  }),
);

const VolumeBar = styled(View)<{
  intensity: number;
  color: string;
  width: number;
}>(({ width, intensity, color }) => ({
  height: intensity,
  width,
  backgroundColor: color,
}));

interface RecordingAudioWaveI {
  metering: number[];
  styleProps?: {
    colors?: string[];
    contentHeight?: number;
    barWidth?: number;
  };
  onDimensionChange: (
    width: number,
    waveWidth: number,
    waveGap: number,
  ) => void;
}

const MIN_BAR_HEIGHT = 3;

export default function RecordingAudioWave({
  metering,
  onDimensionChange,
  styleProps,
}: RecordingAudioWaveI) {
  const onLayoutContainer = (e: LayoutChangeEvent) => {
    const { width } = e.nativeEvent.layout;
    onDimensionChange(width, 3, 2);
  };

  const { colors } = useTheme();

  const DEFAULT_COLORS = useMemo(
    () => [
      colors.palette.fwdYellow[100],
      colors.palette.fwdOrange[100],
      colors.palette.fwdLightGreen[100],
      colors.palette.fwdOrange[100],
      colors.palette.fwdLightGreen[100],
      colors.palette.fwdBlue[100],
      colors.palette.fwdYellow[100],
      colors.palette.fwdBlue[100],
      colors.palette.fwdOrange[100],
    ],
    [],
  );

  const contentHeight = styleProps?.contentHeight ?? 100;
  const barWidth = styleProps?.barWidth ?? 3;

  const colorsRef = useRef(styleProps?.colors ?? DEFAULT_COLORS);

  useEffect(() => {
    colorsRef.current.unshift(...colorsRef.current.splice(1));
  }, [metering]);

  function renderHeight(val: number) {
    if (val < MIN_BAR_HEIGHT) {
      return MIN_BAR_HEIGHT;
    }

    return val;
  }

  return (
    <Container onLayout={onLayoutContainer} height={contentHeight}>
      <Content>
        {metering.map((vol, index) => {
          return (
            <VolumeBar
              key={index}
              intensity={renderHeight(vol)}
              color={colorsRef.current[index % colorsRef.current.length]}
              width={barWidth}
            />
          );
        })}
      </Content>
    </Container>
  );
}
