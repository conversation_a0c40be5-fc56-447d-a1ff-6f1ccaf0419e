import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { NotificationSubType, useNotification } from 'agent-guru';
import { View } from 'react-native';
import { useNotificationStore } from 'features/aiBot/store';

const Container = styled(View)<{ bottom: number }>(
  ({ theme: { space }, bottom }) => ({
    zIndex: 10,
    position: 'absolute',
    bottom: bottom,
    left: 0,
    width: '100%',
    paddingHorizontal: space[4],
    display: 'flex',
    flexDirection: 'column',
    gap: space[2],
    alignItems: 'center',
  }),
);

const Notification = styled(View)(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.palette.fwdDarkGreen[100],
    borderRadius: borderRadius.small,
    width: '100%',
    maxWidth: 524,
    padding: space[4],
  }),
);

interface BottomNotificationProps {
  bottom?: number;
}

export default function BottomNotification({
  bottom = 0,
}: BottomNotificationProps) {
  const events = useNotification(
    useNotificationStore,
    NotificationSubType.BOTTOM,
    1000 * 2,
  );

  return (
    <Container bottom={bottom}>
      {events.map(event => {
        return (
          <Notification key={event.id}>
            <Typography.Body color="white">{event.title}</Typography.Body>
          </Notification>
        );
      })}
    </Container>
  );
}
