import styled from "@emotion/native";
import { useTheme } from "@emotion/react";
import { Box, Icon, Typography } from "cube-ui-components";
import { useEffect, useState } from "react";
import { FlatList, TouchableOpacity } from "react-native";
import MarkdownText from "../Common/MarkdownText";


const Container = styled(Box)(({theme}) => ({
  backgroundColor: theme.colors.palette.white,
  padding: theme.space[3],
  borderRadius: theme.borderRadius.medium,
  gap: theme.space[3],
  shadowColor: 'rgba(0, 0, 0, 0.12)',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.8,
  shadowRadius: 8,
}))

const ItemContainer = styled(Box)<{isLast: boolean}>(({theme, isLast}) => ({
  width: '100%',
  borderBottomWidth: isLast ? 0 : 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  paddingBottom: isLast ? 0 : theme.space[3],
  gap: theme.space[1]
}))

const ItemTitle = styled(TouchableOpacity)(({theme}) => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: theme.space[4],
}))

const ItemContent = styled(Box)(({theme}) => ({
  overflow: 'hidden'
}))

export interface CollapsibleItem {
  content: string,
  title: string
  isCollapsed: boolean,
}

interface CollapsibleItemsProps {
  items: Array<CollapsibleItem>
  onPressCollapse: (index: number) => void;
}

const CollapsibleItems = ({items, onPressCollapse}: CollapsibleItemsProps) => {
  return <Container>
    {items.map((item, index) => {      
      return <ItemContainer key={`${item.title}-${index}`} isLast={index === items.length - 1}>
        <ItemTitle disabled={!item.content} onPress={() => onPressCollapse(index)}>
          <Typography.LargeBody fontWeight="bold" style={{flexShrink: 1}}>{item.title}</Typography.LargeBody>
          {item.content && <Box>
            {item.isCollapsed ? <Icon.ChevronDown /> : <Icon.ChevronUp />}
          </Box>}
        </ItemTitle>
       {!item.isCollapsed && <ItemContent>
          <MarkdownText text={item.content} id={`${item.title}-${index}`}/>
        </ItemContent>}
      </ItemContainer>
    })}
  </Container>
}

export default CollapsibleItems;