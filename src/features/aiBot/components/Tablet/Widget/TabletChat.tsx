import { KeyboardAvoidingView, Platform, View } from 'react-native';
import ChatInput from 'features/aiBot/components/Chat/ChatInput';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSession } from 'agent-guru';
import { getUseSessionParams } from 'features/aiBot/utils/hooks/useSession.utils';
import PopularTopics from 'features/aiBot/components/PopularTopics/PopularTopics';
import FeedbackModal from '../../Modals/Feedback/FeedbackModal';
import CopiedNotification from '../../Common/CopiedNotification';
import ChatNotification from '../../Notifications/ChatNotification';
import styled from '@emotion/native';
import ChatList from '../../Chat/ChatList/ChatList';

const Container = styled(KeyboardAvoidingView)(() => ({
  flex: 1,
  width: '100%',
  alignItems: 'center',
}));

export default function TabletChat() {
  const [showPopularTopics, setShowPopularTopics] = useState<boolean>(true);

  const currentSession = useSession(...getUseSessionParams()).currentSession;

  const uiParams = useMemo(() => currentSession?.uiParams, [currentSession]);

  const [isKeyboardOpen, setKeyboardIsOpen] = useState(false);

  const { showInputBar, hideSuggestions, isLoading } = useMemo(
    () => ({
      showInputBar: !(
        uiParams?.isLoading === true || uiParams?.hideInputBar === true
      ),
      hideSuggestions:
        uiParams?.isLoading === true || uiParams?.hideSuggestions === true,
      isLoading: uiParams?.isLoading === true,
    }),
    [uiParams],
  );

  const inputRef = useRef<View>(null);

  const [keyboardOffset, setKeyboardOffset] = useState(
    Platform.OS === 'ios' ? 70 : 40,
  );

  useEffect(() => {
    if (isKeyboardOpen) return;

    inputRef.current?.measure((_x, _y, _width, _height, _pageX, pageY) => {
      const offset = pageY - 562;

      setKeyboardOffset(offset);
    });
  });

  return (
    <Container behavior={'padding'} keyboardVerticalOffset={keyboardOffset}>
      <ChatList isLoading={isLoading} disableScroll={false} />

      {!hideSuggestions && (
        <Animated.View entering={FadeIn} exiting={FadeOut}>
          <PopularTopics
            showPopularTopics={showPopularTopics && !hideSuggestions}
          />
        </Animated.View>
      )}

      {showInputBar && (
        <Animated.View entering={FadeIn} exiting={FadeOut}>
          <View
            style={{
              overflow: 'hidden',
              borderBottomStartRadius: 16,
              borderBottomEndRadius: 16,
            }}>
            <ChatInput
              onToogleRecording={(isRecording: boolean) =>
                setShowPopularTopics(!isRecording)
              }
              onToogleKeyboard={o => setKeyboardIsOpen(o)}
              hasSpaceBelow={false}
              hasRoundedCorners={false}
              inputContainerRef={inputRef}
            />
          </View>
        </Animated.View>
      )}

      <FeedbackModal />

      <CopiedNotification />

      <ChatNotification />
    </Container>
  );
}
