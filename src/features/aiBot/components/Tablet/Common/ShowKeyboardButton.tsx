import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/esm/theme/base';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';

const Container = styled(TouchableOpacity)(
  ({ theme: { borderRadius, space } }) => ({
    position: 'absolute',
    alignSelf: 'center',
    bottom: space[3],
    zIndex: 1,
    alignItems: 'center',
    gap: space[2],
    height: space[10],
    backgroundColor: colors.fwdOrange[5],
    flexDirection: 'row',
    paddingVertical: space[2],
    paddingHorizontal: space[3],
    borderWidth: 1,
    borderColor: colors.fwdOrange[100],
    borderRadius: borderRadius.small,
  }),
);

interface ShowKeyboardButtonProps {
  onPress: () => void;
  show: boolean;
}

export function ShowKeyboardButton({ onPress, show }: ShowKeyboardButtonProps) {
  const { t } = useTranslation('aiBot');

  const { space } = useTheme();

  if (!show) return <></>;

  return (
    <Container activeOpacity={ACTIVE_OPACITY} onPress={onPress}>
      <Icon.KeyboardBack fill={colors.fwdOrange[100]} size={space[4]} />
      <Typography.Body color={colors.fwdOrange[100]} fontWeight="bold">
        {t('tablet.input.showKeyboard')}
      </Typography.Body>
      <Icon.ChevronUp fill={colors.fwdOrange[100]} size={space[4]} />
    </Container>
  );
}
