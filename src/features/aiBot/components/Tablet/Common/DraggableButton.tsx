import styled from '@emotion/native';
import { ACTIVE_OPACITY } from 'features/aiBot/constants/App';
import React, { ReactElement, useEffect, useState } from 'react';
import { Dimensions, GestureResponderEvent, TouchableOpacity } from 'react-native';
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView
} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';

const Container = styled(GestureHandlerRootView)({
  position: 'absolute',
  top: -60,
  left: -60,
  width: 60,
  height: 60,
  zIndex: 1,
});

interface DraggableButtonI {
  onPress?: (e: GestureResponderEvent) => void;
  onLongPress?: (e: GestureResponderEvent) => void;
  onPressOut?: (e: GestureResponderEvent, isLongPress: boolean) => void;
  onUpdatePosition?: (x?: number, y?: number) => void;
  children: ReactElement;
  minOffsetX?: number;
  minOffsetY?: number;
  maxOffsetX?: number;
  maxOffsetY?: number;
  isDraggable?: boolean;
  activeOpacity?: number;
  initX?: number;
  initY?: number;
  newPosition?: { x?: number; y?: number };
}

const DraggableButton = ({
  onPress,
  onLongPress,
  onPressOut,
  onUpdatePosition,
  children,
  minOffsetX = 0,
  minOffsetY = 0,
  maxOffsetX = Dimensions.get('window').width,
  maxOffsetY = Dimensions.get('window').height,
  isDraggable = true,
  activeOpacity = ACTIVE_OPACITY,
  initX = 0,
  initY = 0,
  newPosition,
}: DraggableButtonI) => {
  const translateX = useSharedValue(initX);
  const translateY = useSharedValue(initY);
  const contextX = useSharedValue(initX);
  const contextY = useSharedValue(initY);

  const [isLongPress, setIsLongPress] = useState(false);

  useEffect(() => {
    if (!newPosition) return;

    const { x, y } = newPosition;

    if (x) {
      translateX.value = withTiming(x, { duration: 400 }, () => {
        if (onUpdatePosition) {
          runOnJS(onUpdatePosition)(x, undefined);
        }
      });
    }

    if (y) {
      translateY.value = withTiming(y, { duration: 400 }, () => {
        if (onUpdatePosition) {
          runOnJS(onUpdatePosition)(undefined, y);
        }
      });
    }
  }, [newPosition]);

  useEffect(() => {
    translateX.value = initX;
    translateY.value = initY;

    if (onUpdatePosition) onUpdatePosition(initX, initY);
  }, [initX, initY]);

  const onDrag = Gesture.Pan()
    .onBegin(() => {
      contextX.value = translateX.value;
      contextY.value = translateY.value;
    })
    .onChange((event) => {
      if (!isDraggable) return;

      let newX = (contextX.value ?? initX) + event.translationX;
      let newY = (contextY.value ?? initY) + event.translationY;

      // Constrain newX within limits
      if (newX < minOffsetX) {
        newX = minOffsetX;
      } else if (newX > maxOffsetX) {
        newX = maxOffsetX;
      }

      // Constrain newY within limits
      if (newY < minOffsetY) {
        newY = minOffsetY;
      } else if (newY > maxOffsetY) {
        newY = maxOffsetY;
      }

      translateX.value = newX;
      translateY.value = newY;

      if (onUpdatePosition) runOnJS(onUpdatePosition)(newX, newY);
    });

  const animatedStyle = useAnimatedStyle<object>(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  return (
    <Container>
      <GestureDetector gesture={onDrag}>
        <Animated.View style={animatedStyle}>
          <TouchableOpacity
            activeOpacity={activeOpacity}
            onPress={onPress}
            onLongPress={e => {
              setIsLongPress(true);

              if (onLongPress) onLongPress(e);
            }}
            onPressOut={e => {
              if (onPressOut) onPressOut(e, isLongPress);
              setIsLongPress(false);
            }}>
            {children}
          </TouchableOpacity>
        </Animated.View>
      </GestureDetector>
    </Container>
  );
};

export default DraggableButton;
