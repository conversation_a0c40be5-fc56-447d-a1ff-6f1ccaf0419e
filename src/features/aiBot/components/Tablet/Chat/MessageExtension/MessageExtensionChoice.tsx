import { Typography } from 'cube-ui-components';
import { MessageExtensionChoice as IMessageExtensionChoice } from 'agent-guru';
import { View } from 'react-native';

interface MessageExtensionChoiceProps {
  extension: IMessageExtensionChoice;
}

export default function MessageExtensionChoice({
  extension,
}: MessageExtensionChoiceProps) {
  return (
    <View>
      {extension.options.map(option => {
        return (
          <View key={option.text}>
            <Typography.Body>{option.text}</Typography.Body>
          </View>
        );
      })}
    </View>
  );
}
