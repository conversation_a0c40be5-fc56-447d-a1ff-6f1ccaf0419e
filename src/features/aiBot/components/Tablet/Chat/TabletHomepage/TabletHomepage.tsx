import { Platform, ScrollView, View } from 'react-native';
import { useAppStore, useSuggestionsStore } from 'features/aiBot/store';
import { useCallback, useMemo } from 'react';
import { selectCurrentSessionId } from 'agent-guru';
import styled from '@emotion/native';
import CloseButton from '../../Common/CloseButton';
import useChatWrapper from 'features/aiBot/hooks/useChatWrapper';
import TabletHomepageSuggestions from './TabletHomepageSuggestions';
import TabletHomepageTop from './TabletHomepageTop';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import usePredictiveKeyboard from 'features/aiBot/hooks/usePredictiveKeyboard';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const Container = styled(View)(({theme: { space }}) => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  position: 'relative',
  paddingVertical: space[4],
}));

const Content = styled(ScrollView)(({ theme: { space } }) => ({
  width: '100%',
  minHeight: '76%',
  paddingHorizontal: space[14],
  zIndex: -1,
}));

interface TabletChatViewProps {
  onMessageSent: () => void;
  onPressHistory: () => void;
  onVoiceChatTrigger: () => void;
  onPressSeeAllPrompts: () => void;
}

export default function TabletChatHomepage({
  onMessageSent,
  onPressHistory,
  onVoiceChatTrigger,
  onPressSeeAllPrompts,
}: TabletChatViewProps) {
  const [isKeyboardOpen, isPredictiveKeyboardOpen] = usePredictiveKeyboard();
  const { top: safeTopInset } = useSafeAreaInsets();

  
  const showSuggestions = useMemo(
    () =>
      !isKeyboardOpen ||
      (isKeyboardOpen && isPredictiveKeyboardOpen !== false) ||
      Platform.OS === 'android',
    [isKeyboardOpen, isPredictiveKeyboardOpen],
  );

  const { sendANewMessage } = useChatWrapper();

  return (
    <Container style={{ marginBottom: -safeTopInset }}>
      <Content>
        <TabletHomepageTop
          onVoiceChatTrigger={onVoiceChatTrigger}
          onPressHistory={onPressHistory}
          onMessageSent={onMessageSent}
          sendANewMessage={sendANewMessage}
        />

        {showSuggestions && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <TabletHomepageSuggestions
              onPressSeeAllPrompts={onPressSeeAllPrompts}
            />
          </Animated.View>
        )}
      </Content>
    </Container>
  );
}
