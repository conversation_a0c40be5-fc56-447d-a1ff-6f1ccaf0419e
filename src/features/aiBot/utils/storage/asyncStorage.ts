import AsyncStorage from '@react-native-async-storage/async-storage';
import { generateRandomId } from '../misc/randomId';
import { CHAT_HISTORY_STORAGE_KEY, getStorageData } from './misc';
import { generateNewSession } from '../misc/misc';
import { Session, Message } from 'agent-guru';

export const STORAGE_KEY = 'chat-history';
export const DEVICE_RANDOM_ID = 'device-random-id';
export const DEVICE_BRAND = 'device-brand';
export const DEVICE_NAME = 'device-name';
export const OS_VERSION = 'os-version';

export const filterSessionsWithMessagesRequiringRating = async () => {
  const sessions: Session[] =
    (await getStorageData<Session[]>(CHAT_HISTORY_STORAGE_KEY)) || [];
  const filteredSessions = sessions.filter((session: Session) => {
    return (
      session.messages &&
      session.messages?.some(message => message?.requireAIRating === true)
    );
  });

  return filteredSessions;
};

export async function syncSessions(sessionId: string, message: Message[]) {
  const storedSession = await getSessionByID(sessionId);
  // console.log('--- syncSessions', storedSession, messages);
  // If session exists, update it
  if (storedSession) {
    const updatedSession = { ...storedSession, messages: message };
    updateChatHistoryStorage(updatedSession);
  } else {
    // Otherwise create it
    const newSession = generateNewSession(sessionId);
    newSession.messages = message;
    updateChatHistoryStorage(newSession);
  }
}

export const getSessionByID = async (
  sessionId: string,
): Promise<Session | null> => {
  try {
    const sessions = await AsyncStorage.getItem(STORAGE_KEY);

    if (sessions) {
      const parsedSessions: Session[] = JSON.parse(sessions);
      const session = parsedSessions.find(
        session => session.sessionId === sessionId,
      );

      return session || null;
    }

    return null;
  } catch (error) {
    console.error('Error retrieving session by ID:', error);
    return null;
  }
};

/**
 * Update local storage chat history
 * @param updatedSession
 * @returns
 */
export const updateChatHistoryStorage = async (
  updatedSession: Session,
): Promise<void> => {
  try {
    // Get sessions from AsyncStorage
    const sessions = await AsyncStorage.getItem(STORAGE_KEY);

    if (!sessions) {
      // Create a new session array if no sessions exist
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify([updatedSession]));
      return;
    }

    const parsedSessions: Session[] = JSON.parse(sessions);
    const existingSessionIndex = parsedSessions.findIndex(
      session => session.sessionId === updatedSession.sessionId,
    );

    if (existingSessionIndex === -1) {
      // Create a new session if it doesn't exist
      updatedSession.datetime = new Date().toString();
      parsedSessions.push(updatedSession);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(parsedSessions));
      return;
    }

    const originalSession = parsedSessions[existingSessionIndex];
    // Update existing session
    updatedSession.datetime = originalSession.datetime || new Date().toString();
    updatedSession.isConversation = originalSession.isConversation;
    updatedSession.isPinned = originalSession.isPinned;
    parsedSessions[existingSessionIndex] = updatedSession;

    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(parsedSessions));
  } catch (error) {
    console.error('Error updating session by ID:', error);
  }
};

/**
 * Update message by ID
 * @param sessionId
 * @param updatedMessage
 * @returns
 */
export const updateMessageByID = async (
  sessionId: string,
  updatedMessage: Message,
): Promise<void> => {
  try {
    const sessions = await AsyncStorage.getItem(STORAGE_KEY);

    if (!sessions) return;

    const messageID = updatedMessage.id;
    const parsedSessions: Session[] = JSON.parse(sessions);
    const sessionIndex = parsedSessions.findIndex(
      session => session.sessionId === sessionId,
    );

    if (sessionIndex === -1) return;

    // Update existing message
    const newMessages = parsedSessions[sessionIndex].messages.map(message =>
      message.id === messageID ? { ...message, ...updatedMessage } : message,
    );

    const updatedSession = {
      ...parsedSessions[sessionIndex],
      messages: newMessages,
    };

    parsedSessions[sessionIndex] = updatedSession;

    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(parsedSessions));
  } catch (error) {
    console.error('Error updating message by ID:', error);
  }
};

export const getMessagesBySessionAndMessageID = async (
  sessionId: string,
  messageID: string,
) => {
  try {
    const sessions =
      (await getStorageData<Session[]>(CHAT_HISTORY_STORAGE_KEY)) || [];
    const session = sessions.find(
      (session: Session) => session.sessionId === sessionId,
    );

    if (session) {
      const message = session.messages.find(
        (message: Message) => message.id === messageID,
      );
      return message;
    }
    return null;
  } catch (error) {
    console.error(error);
    return null;
  }
};

// Delete data from AsyncStorage
export const deleteSession = async (sessionId: string) => {
  try {
    const sessions =
      (await getStorageData<Session[]>(CHAT_HISTORY_STORAGE_KEY)) || [];

    const updatedSessions = sessions.map((session: Session) =>
      session.sessionId === sessionId
        ? { ...session, isDeleted: true }
        : session,
    );
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSessions));
  } catch (error) {
    console.error('Error updating pinned status', error);
  }
};

export const togglePinnedStatus = async (sessionId: string) => {
  try {
    const sessions =
      (await getStorageData<Session[]>(CHAT_HISTORY_STORAGE_KEY)) || [];
    const updatedSessions = sessions.map((session: Session) =>
      session.sessionId === sessionId
        ? { ...session, pinned: !session.isPinned }
        : session,
    );
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSessions));
  } catch (error) {
    console.error('Error updating pinned status', error);
  }
};

export const saveDeviceInfoToAsyncStorage = async (
  brand: string,
  deviceName: string,
  osVersion: string,
) => {
  try {
    // Check if random_id exists in AsyncStorage
    const existingRandomId = await AsyncStorage.getItem(DEVICE_RANDOM_ID);
    const existingBrand = await AsyncStorage.getItem(DEVICE_BRAND);
    const existingDeviceName = await AsyncStorage.getItem(DEVICE_NAME);
    const existingOsVersion = await AsyncStorage.getItem(OS_VERSION);

    if (!existingRandomId) {
      await AsyncStorage.setItem(DEVICE_RANDOM_ID, generateRandomId());
    }

    if (!existingBrand) {
      await AsyncStorage.setItem(DEVICE_BRAND, brand);
    }

    if (!existingDeviceName) {
      await AsyncStorage.setItem(DEVICE_NAME, deviceName);
    }

    if (!existingOsVersion) {
      await AsyncStorage.setItem(OS_VERSION, osVersion);
    }

    console.log('Device information and random ID saved to AsyncStorage');
  } catch (error) {
    console.error('Error saving device information and random ID:', error);
  }
};
