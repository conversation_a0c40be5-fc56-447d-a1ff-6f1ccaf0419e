import useCheckLogin from "hooks/useCheckLogin";
import { useCheckIsAiBotEnabled } from "../utils/useCheckIsAiBotEnabled";
import { default as useGuruContext } from 'features/aiBot/hooks/useAppContext';
import { useEffect, useMemo } from "react";
import useBoundStore from "hooks/useBoundStore";
import useLayoutAdoptionCheck from "hooks/useDeviceCheck";
import { generateRandomId, usePrompt, useSession } from "agent-guru";
import { useAppStore, useSessionsStore, useSuggestionsStore } from "../store";
import useChatHistoryWrapper from "./useChatHistoryWrapper";
import { usePromptsStore } from "../store/prompsStore";

export default function useInitializeAiBot() {

    const [isContextLoaded, contextError] = useGuruContext();

    const isAiBotEnabled = useCheckIsAiBotEnabled();
    const isLoggedIn = useCheckLogin();
    const agentId = useBoundStore(state => state.auth.agentCode);
    const { isTabletMode } = useLayoutAdoptionCheck();
    const { getSessionsAndFormat } = useChatHistoryWrapper();
    const { getSessionId } = useSession(useSessionsStore, useAppStore, generateRandomId);
    const { fetchLandingPagePrompts } = usePrompt(usePromptsStore);

    const hasFetchedSessions = useSessionsStore(
        state => state.hasFetchedSessions,
    );

    const isAiBotOn = useMemo(() =>
        // Is AiBot enable
        !!isAiBotEnabled &&
        // Only if the user is logged in
        isLoggedIn &&
        // Check if the context is loaded, and no error
        (!contextError && isContextLoaded)
        , [isAiBotEnabled, isLoggedIn, contextError, isContextLoaded]);

    const showAiBotButton = useMemo(() =>
        // Check if it's a tablet, 
        (isTabletMode && isAiBotOn), [isTabletMode, isAiBotOn]);

    useEffect(() => {
        if (isAiBotOn) {
            // Fetch dynamic prompts
            setTimeout(() => {
                fetchLandingPagePrompts();
            }, 1000)
        }
    }, [agentId, isAiBotOn])

    useEffect(() => {
        if (isAiBotOn) {
            // Initialize AiBot session
            getSessionId();
        }
    }, [isAiBotOn]);

    useEffect(() => {
        if (!hasFetchedSessions && isAiBotOn) {
            // Fetch AiBot history
            getSessionsAndFormat(true);
        }
    }, [hasFetchedSessions, isAiBotOn]);

    return {
        showAiBotButton
    };
}