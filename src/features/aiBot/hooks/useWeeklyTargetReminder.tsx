import { getLeadTrackingTeamBranchDownlineAgent } from 'api/teamApi';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAccountStore } from '../store';
import { TeamDownlineAgent } from 'types/team';
import { isBefore, subDays } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { differenceInCalendarDays } from 'date-fns/fp';
import { UntargetAgent } from 'agent-guru';
import { useAiBotTooltipStore } from '../store/tooltipStore';
import { TooltipType } from '../constants/Tooltip';
import { useGetFeatureFlags } from 'hooks/useGetFeatureFlags';
import useBoundStore from 'hooks/useBoundStore';

export default function useWeeklyTargetReminder() {
  const isLeader = useAccountStore(state => state.isLeader);
  const agentId = useBoundStore.getState().auth.agentCode;
  const [shouldShowTooltip, setShouldShowTooltip] = useState<boolean>(false);
  const [untargetDownlineAgent, setUntargetDownlineAgent] = useState<
    TeamDownlineAgent[]
  >([]);
  const [untargeAgentList, setUntargetAgentList] = useState<UntargetAgent[]>(
    [],
  );

  const { t } = useTranslation(['product', 'navigation', 'aiBot']);
  const setTooltipData = useAiBotTooltipStore(state => state.setData);
  const { data: featureFlags, isSuccess } = useGetFeatureFlags();

  const weeklyTargetReminderConfig = useMemo(() => featureFlags?.guru_nudge, [featureFlags]);
  const isWeeklyTargetReminderEnabled = useMemo(() => weeklyTargetReminderConfig?.available === true, [weeklyTargetReminderConfig])

  const calculateTargetNotSetDay = useCallback(
    (untargetDays: number) => {
      const timeUnit = untargetDays < 7 ? 'day' : 'week';
      const timeValue =
        untargetDays < 7 ? untargetDays : Math.floor(untargetDays / 7);

      return `${timeValue} ${t(
        `aiBot:tooltip.weeklyReminder.table.${timeUnit}.${
          timeValue > 1 ? 'plural' : 'singular'
        }`,
      )}`;
    },
    [t],
  );

  const verifyTargetInfo = useCallback(async () => {
    setShouldShowTooltip(false);

    if (!isLeader || !isWeeklyTargetReminderEnabled || !isSuccess || !weeklyTargetReminderConfig) {
      return;
    }

    // Check if the day of week is set
    const dayOfWeek = Number(
      weeklyTargetReminderConfig.settings.find(
        setting => setting.parameter === 'day_of_week',
      )?.value,
    );
    if (!Number.isInteger(dayOfWeek)) {
      return;
    }
    
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    // If not equal dayOfWeek, doing nothing;
    if (currentDate.getDay() !== dayOfWeek) return;

    const twoDaysBefore = subDays(currentDate, 2);
    const untargetDownlineAgentList: TeamDownlineAgent[] = [];

    const teamBranchDownlineAgent =
      await getLeadTrackingTeamBranchDownlineAgent();

    teamBranchDownlineAgent.downlineAgents.forEach(agent => {
      if (isBefore(new Date(agent.targetUpdatedAt || ''), twoDaysBefore)) {
        untargetDownlineAgentList.push(agent);
      }
    });

    setUntargetDownlineAgent(untargetDownlineAgentList);
    setShouldShowTooltip(untargetDownlineAgentList.length > 0);
  }, [
    isLeader,
    isSuccess,
    isWeeklyTargetReminderEnabled,
    weeklyTargetReminderConfig
  ]);

  useEffect(() => {
    setShouldShowTooltip(untargetDownlineAgent.length > 0);

    const untargetAgents: UntargetAgent[] = [];
    const currentDate = new Date();
    const twoDaysBefore = subDays(currentDate, 2);
    untargetDownlineAgent.forEach(agent => {
      if (agent.agentCode === agentId) return;

      untargetAgents.push({
        id: agent.agentCode,
        name: agent.displayName.en,
        untargetDays: calculateTargetNotSetDay(
          differenceInCalendarDays(
            new Date(agent.targetUpdatedAt || ''),
            twoDaysBefore,
          ),
        ),
      });
    });

    setUntargetAgentList(untargetAgents);
    setTooltipData(TooltipType.WeeklyTargetReminderTooltip, { untargetAgents });
  }, [untargetDownlineAgent, calculateTargetNotSetDay, setTooltipData, agentId]);

  return {
    verifyTargetInfo,
    untargetDownlineAgent,
    shouldShowTooltip,
    untargeAgentList,
    isWeeklyTargetReminderEnabled,
  };
}
