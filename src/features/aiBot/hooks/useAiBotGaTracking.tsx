import { useNavigationState } from '@react-navigation/native';
import { useCallback } from 'react';
import GATracking from 'utils/helper/gaTracking';

export default function useAiBotGaTracking() { 
  const screenName = useNavigationState(
    state => state?.routes?.[state.index]?.name,
  ) || 'Main';

  const trackingButtonClicked = async (buttonName: string) =>
    await GATracking.logButtonPress({
      screenName,
      screenClass: 'Agent Guru nudge',
      actionType: 'non_cta_button',
      buttonName: buttonName,
    });

  const trackingClickLearnMore = useCallback(
    async () => trackingButtonClicked('Learn more'),
    [],
  );
  const trackingClickCloseTooltip = useCallback(
    async () => trackingButtonClicked('Close tooltip'),
    [],
  );

  return {
    trackingClickLearnMore,
    trackingClickCloseTooltip,
  };
}
