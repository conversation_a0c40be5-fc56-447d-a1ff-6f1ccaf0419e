import { useSessionsStore } from 'features/aiBot/store';
import { useCallback, useMemo } from 'react';
import { Session } from 'agent-guru';

export default function useHistorySession(searchText: string) {
  const sessions = useSessionsStore(state => state.sessions);
  const nbSessionToDisplay = 50;

  const filterSessions = useCallback(
    (session: Session) => {
      return session.messages.some(message =>
        message.text
          ?.toLocaleLowerCase()
          .includes(searchText.toLocaleLowerCase()),
      );
    },
    [searchText],
  );

  const sessionsWithMessages = useMemo(
    () =>
      sessions
        // Keep only sessions with messages
        .filter(session => session.messages.length > 0)
        // Keep only session coming from the backend
        .filter(session => session.isConversation)
        // Filter sessions based on the search text
        .filter(filterSessions)
        // Sort sessions by date and pinned status
        .sort((a, b) => {
          const aIsPinned = a.isPinned ?? false;
          const bIsPinned = b.isPinned ?? false;
          if (aIsPinned === bIsPinned)
            return new Date(a.datetime || '') < new Date(b.datetime || '')
              ? 1
              : -1;
          return Number(bIsPinned) - Number(aIsPinned);
        })
        .slice(0, nbSessionToDisplay),
    [sessions, nbSessionToDisplay, searchText],
  );

  return sessionsWithMessages;
}
