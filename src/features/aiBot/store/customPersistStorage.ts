import AsyncStorage from '@react-native-async-storage/async-storage';
import { StateStorage } from 'zustand/middleware';
import * as SecureStore from 'expo-secure-store';

export const customPersistStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    const data = await AsyncStorage.getItem(name);

    return data;
  },
  setItem: async (name: string, value: string): Promise<void> => {
    await AsyncStorage.setItem(name, value);
  },
  removeItem: async (name: string): Promise<void> => {
    //console.log(name, 'has been deleted')

    AsyncStorage.removeItem(name);
  },
};

export const customPersistSecureStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    const data = await SecureStore.getItemAsync(name);

    return data;
  },
  setItem: async (name: string, value: string): Promise<void> => {
    //console.log(name, 'with value', value, 'has been saved');

    await SecureStore.setItemAsync(name, value);
  },
  removeItem: async (name: string): Promise<void> => {
    //console.log(name, 'has been deleted')

    SecureStore.deleteItemAsync(name);
  },
};
