import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React from 'react';
import HeaderMoreButton from '../components/Navigation/HeaderMoreButton';
import { Typography } from 'cube-ui-components';
import AvailableIcon from '../components/Common/Icons/AvailableIcon';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { View } from 'react-native';
import { Divider } from '../components/Tablet/Chat/TabletChatHeader';
import HeaderHomeButton from '../components/Navigation/HeaderHomeButton';

interface ChatHeaderProps {
  customTitle?: string;
  onPressClose?: () => void;
}

const LeftChildrenContainer = styled(View)(({theme: { space }}) => ({
  gap: space[2],
  flexDirection: 'row',
  alignItems: 'center'
}));

const RightChildrenContainer = styled(View)({
  flexDirection: 'row',
  alignItems: 'center'
});


const ChatHeader = ({ customTitle, onPressClose }: ChatHeaderProps) => {
  return (
    <>
      <ScreenHeader
        route={'AiBot'}
        leftChildren={
          <LeftChildrenContainer>
            <AvailableIcon />
            <Typography.LargeBody fontWeight='bold'>Agent Guru</Typography.LargeBody>
            
          </LeftChildrenContainer>
        }
        customBackgroundColor="transparent"
        showBottomSeparator={false}
        rightChildren={<RightChildrenContainer>
          <HeaderMoreButton />
          <Divider />
          <HeaderHomeButton onPressClose={onPressClose} />
        </RightChildrenContainer>}
        customTitle={customTitle}
      />
    </>
  );
};

export default ChatHeader;
