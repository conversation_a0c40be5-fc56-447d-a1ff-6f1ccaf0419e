import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { Image } from 'expo-image';
import FWDInsuranceLogoSVG from 'features/agentProfile/assets/FWDInsuranceLogoSVG';
import AnimatedTabTransitionWrapper from 'features/agentProfile/components/AnimatedTabTransitionWrapper';
import GenericAgentNameCard from 'features/agentProfile/components/GenericAgentNameCard';
import AwardSection from 'features/agentProfile/ph/phone/AwardSection';
import { captureAndShareScreenshot } from 'features/agentProfile/utils/agentNameCardUtils';
import AvatarPlaceholderSVG from 'features/home/<USER>/AvatarPlaceholderSVG';
import { useCheckAgentIsMDRT } from 'hooks/useCheckAgentIsMDRT';
import { useCountryCode } from 'hooks/useCountryCode';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import ViewShot from 'react-native-view-shot';
import ShareLink from '../../components/ShareLink';
import _ from 'lodash';

const AGENT_CARD_WIDTH = 436;

export default function PHProfileDetailsTab() {
  const { t: ct } = useTranslation('common');
  const { space, sizes, colors, borderRadius } = useTheme();
  const viewShotRef = useRef<ViewShot>(null);

  const { data: agentProfile } = useGetAgentProfile();
  const { data: isMDRT } = useCheckAgentIsMDRT();

  const agentId = agentProfile?.agentId ?? '';
  const agentProfilePicture = agentProfile?.agentPhotoUrl || '';
  const agentFirstName = agentProfile?.person?.firstName ?? '';
  const agentLastName = agentProfile?.person?.lastName ?? '';
  const agentJobTitle =
    (agentProfile?.companyTitle ?? ct(agentProfile?.designation as any)) +
    ` (${agentProfile?.designation ?? '--'})`;
  const agentEmail = agentProfile?.contact?.email ?? '';
  const agentMobilePhone = agentProfile?.contact?.mobilePhone ?? '';
  const hasAgentWorkAddress = !_.isEmpty(agentProfile?.contact?.workAddress);
  const agentWorkAddress = agentProfile?.contact?.workAddress?.join(', ');
  const isMDRTArchived = isMDRT ?? false;
  const isELITEArchived = agentProfile?.isELITE ?? false;

  const countryName = useCountryCode().toCountryName(
    String(agentProfile?.contact?.workAddressCountryCode),
  );

  const PERSONAL_DETAILS = [
    {
      type: 'agentCode',
      label: 'Agent code',
      value: agentId,
      visible: true,
    },
    {
      type: 'email',
      label: 'Email',
      value: agentEmail,
      visible: true,
    },
    {
      type: 'phone',
      label: 'Phone',
      value: agentMobilePhone,
      visible: true,
    },
    {
      type: 'address',
      label: 'Work address',
      value: agentWorkAddress + ', ' + countryName,
      visible: hasAgentWorkAddress,
    },
  ];

  return (
    <AnimatedTabTransitionWrapper>
      <Box>
        <Typography.H6
          fontWeight="bold"
          children={'Share name card & link'}
          style={{ paddingBottom: space[4] }}
        />

        <Box alignSelf="flex-start">
          <ViewShot
            style={{ alignSelf: 'flex-start' }}
            ref={viewShotRef}
            options={{
              fileName: 'agent-name-card',
              format: 'jpg',
              quality: 1.0,
            }}>
            <CardView>
              <GenericAgentNameCard.Frame
                widthInput={AGENT_CARD_WIDTH}
                icon={<FWDInsuranceLogoSVG />}>
                <Box maxWidth={'74%'}>
                  <Row alignItems="center">
                    <Box height={space[10]} width={space[10]}>
                      {agentProfilePicture ? (
                        <Image
                          style={{
                            height: space[10],
                            width: space[10],
                            borderRadius: borderRadius.full,
                          }}
                          source={agentProfilePicture}
                        />
                      ) : (
                        <Box
                          border={2}
                          borderColor={colors.background}
                          borderRadius={borderRadius.full}>
                          <AvatarPlaceholderSVG size={space[10] - 2 * 2} />
                        </Box>
                      )}
                    </Box>
                    <Box ml={space[2]}>
                      <GenericAgentNameCard.ExtraLargeBodyTitle>
                        {agentFirstName + ' ' + agentLastName}
                      </GenericAgentNameCard.ExtraLargeBodyTitle>
                    </Box>
                  </Row>
                  <Box mt={space[2]}>
                    <GenericAgentNameCard.LargeLabelTitle>
                      {agentJobTitle}
                    </GenericAgentNameCard.LargeLabelTitle>
                  </Box>
                </Box>
                <Box h={space[3]} />
                <Box
                  borderBottom={0.5}
                  borderColor={colors.palette.white}
                  maxW={'100%'}
                />
                <Box h={space[3]} />

                <GenericAgentNameCard.ContactCardSmallLabel
                  type={'email'}
                  value={agentEmail}
                />

                <Box mt={space[2]}>
                  <GenericAgentNameCard.ContactCardSmallLabel
                    type={'phoneNumber'}
                    value={agentMobilePhone}
                  />
                </Box>

                {hasAgentWorkAddress && agentWorkAddress && (
                  <>
                    <Box mt={space[2]}>
                      <GenericAgentNameCard.ContactCardSmallLabel
                        type={'address'}
                        value={agentWorkAddress + ','}
                      />
                      <GenericAgentNameCard.ContactCardSmallLabel
                        value={countryName}
                      />
                    </Box>
                  </>
                )}

                {isELITEArchived && (
                  <GenericAgentNameCard.ELITEIcon
                    size={40}
                    style={{
                      position: 'absolute',
                      left: 0,
                      bottom: 0,
                    }}
                  />
                )}

                {isMDRTArchived && (
                  <GenericAgentNameCard.MDRTIcon
                    size={40}
                    style={{
                      position: 'absolute',
                      left: isELITEArchived ? 50 : 0,
                      bottom: 0,
                    }}
                  />
                )}
              </GenericAgentNameCard.Frame>
            </CardView>
          </ViewShot>

          <ShareButton
            children={
              <Icon.Share
                size={sizes[7]}
                fill={colors.palette.fwdAlternativeOrange[100]}
              />
            }
            onPress={() => captureAndShareScreenshot(viewShotRef)}
          />
        </Box>
      </Box>

      <Box>
        <Box width={AGENT_CARD_WIDTH} pt={space[6]}>
          <ShareLink />
        </Box>

        <Separator />

        <Box>
          <Typography.H6
            fontWeight="bold"
            children={'Personal details'}
            style={{ paddingBottom: space[4] }}
          />
          <Column gap={space[5]}>
            {PERSONAL_DETAILS?.map(({ type, label, value, visible }) => {
              if (!visible) return null;
              return (
                <Row key={type}>
                  <Typography.LargeLabel
                    children={label}
                    style={{
                      width: '35%',
                      color: colors.palette.fwdGreyDarkest,
                    }}
                  />
                  <Typography.LargeLabel children={value} style={{ flex: 1 }} />
                </Row>
              );
            })}
          </Column>
        </Box>

        <Separator />

        <AwardSection />
      </Box>
    </AnimatedTabTransitionWrapper>
  );
}

const CardView = styled.View(({ theme }) => ({
  backgroundColor: 'transparent',
  borderRadius: theme.borderRadius.large,
  //
  shadowColor: '#000000',
  shadowOffset: {
    width: 0,
    height: 3,
  },
  shadowOpacity: 0.29,
  shadowRadius: 4.65,
  elevation: 7,
}));

const ShareButton = styled.TouchableOpacity(({ theme }) => ({
  position: 'absolute',
  top: 80,
  right: 0,
  backgroundColor: theme.colors.background,
  padding: theme.space[4],
  borderTopLeftRadius: theme.borderRadius.small,
  borderBottomLeftRadius: theme.borderRadius.small,
}));

const Separator = styled.View(({ theme }) => ({
  maxWidth: '100%',
  borderBottomWidth: 0.5,
  borderBottomColor: theme.colors.palette.fwdGreyDark,
  marginVertical: theme.space[6],
}));
