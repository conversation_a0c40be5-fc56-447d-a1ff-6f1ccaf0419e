import React from 'react';
import { View } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, H7 } from 'cube-ui-components';
import AchievementCard from './AchievementCard';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import { MOCK_MDRT_ACHIEVEMENT } from '../../MOCK_DATA';

const HAVE_ACHIEVEMENT = true;

export default function MdrtSection() {
  const { t } = useTranslation('agentProfile');
  const { colors, space } = useTheme();

  return (
    <Animated.View layout={LinearTransition}>
      <MainContainer>
        <H7 fontWeight="bold">
          {t('agentProfile.mdrtAchievement.title')} (
          {MOCK_MDRT_ACHIEVEMENT.length})
        </H7>

        {HAVE_ACHIEVEMENT ? (
          <View
            style={{
              marginTop: space[3],
            }}>
            <AchievementCard data={MOCK_MDRT_ACHIEVEMENT} />
          </View>
        ) : (
          <Body
            color={colors.palette.fwdGreyDarkest}
            children={t('agentProfile.mdrtAchievement.emptyCase')}
          />
        )}
      </MainContainer>
    </Animated.View>
  );
}

const MainContainer = styled.View(({ theme }) => ({
  paddingHorizontal: theme.space[4],
}));
