import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Row } from 'cube-ui-components';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AgentProfileKeys } from 'types/agent';

import AgentProfileTabBar from 'features/agentProfile/components/tablet/Tab/AgentProfileTabBar';
import { agentProfileTabs as countryBasedTabConfig } from 'features/agentProfile/tablet/config';

export default function AgentProfileLayout() {
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();

  const [currentTab, setCurrentTab] = useState<AgentProfileKeys>('Profile');

  const currentConfig = countryBasedTabConfig.find(
    ({ key }) => key === currentTab,
  );
  const CurrentTabContent =
    currentConfig && 'Component' in currentConfig
      ? currentConfig?.Component
      : () => <></>;

  const isPdf = currentConfig?.isPdf;

  return (
    <>
      <ScreenHeader route={'AgentProfile'} isLeftArrowBackShown />
      <ScreenContentContainer>
        <Row flex={1}>
          <View style={{ flex: 4, padding: space[6], paddingRight: 0 }}>
            <AgentProfileTabBar
              currentTab={currentTab}
              setCurrentTab={setCurrentTab}
            />
          </View>

          <View style={{ flex: 9 }}>
            {isPdf ? (
              <CurrentTabContent />
            ) : (
              <ScrollView
                showsVerticalScrollIndicator={false}
                style={{ flex: 1 }}
                contentContainerStyle={{
                  paddingTop: space[6],
                  paddingHorizontal: space[6],
                  paddingBottom:
                    bottom + space[5] > 30 ? 30 : bottom + space[5],
                }}>
                <CurrentTabContent />
              </ScrollView>
            )}
          </View>
        </Row>
      </ScreenContentContainer>
    </>
  );
}

const ScreenContentContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  flex: 1,
}));
