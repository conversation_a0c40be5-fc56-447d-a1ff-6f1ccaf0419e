import { Style<PERSON>rop, ViewStyle } from 'react-native';
import Animated, {
  AnimatedStyle,
  FadeIn,
  FadeOut,
  LinearTransition,
} from 'react-native-reanimated';

export default function AnimatedTabTransitionWrapper({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: StyleProp<AnimatedStyle<StyleProp<ViewStyle>>>;
}) {
  return (
    <Animated.View
      style={[style, { flex: 1 }]}
      layout={LinearTransition}
      entering={FadeIn.delay(150)}
      exiting={FadeOut.delay(150)}>
      {children}
    </Animated.View>
  );
}
