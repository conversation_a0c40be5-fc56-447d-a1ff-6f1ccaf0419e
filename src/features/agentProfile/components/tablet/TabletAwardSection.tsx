import React, { useState } from 'react';
import { Image, View } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { H6, LargeBody, XView } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import ResponsiveView from 'components/ResponsiveView';
import { eliteManagerMediumPNG, mdrtMediumPNG } from 'assets/images';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useTranslation } from 'react-i18next';
import { useCheckAgentIsMDRT } from 'hooks/useCheckAgentIsMDRT';
// import { MOCK_AWARDS } from './MOCK_DATA';

export default function TabletAwardSection() {
  const { t } = useTranslation('agentProfile');
  const { colors, space, borderRadius } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const [awardDimension, setAwardDimension] = useState(0);
  const AWARD_GAP = 8;

  const { data: agentProfile } = useGetAgentProfile();
  const { data: isMDRT } = useCheckAgentIsMDRT();

  const numberOfAchievements =
    Number(agentProfile?.isELITE ? 1 : 0) + Number(isMDRT ? 1 : 0);

  const Awards = () => {
    return (
      <>
        {agentProfile?.isELITE && (
          <View
            style={{
              width: awardDimension,
              height: awardDimension,
              alignItems: 'center',
            }}>
            <Image
              source={eliteManagerMediumPNG}
              style={{
                width: awardDimension,
                height: awardDimension,
                borderRadius: borderRadius.medium,
              }}
            />
          </View>
        )}
        {isMDRT && (
          <View
            style={{
              width: awardDimension,
              height: awardDimension,
              alignItems: 'center',
            }}>
            <Image
              source={mdrtMediumPNG}
              style={{
                width: awardDimension,
                height: awardDimension,
                borderRadius: borderRadius.medium,
              }}
            />
          </View>
        )}
      </>
    );
  };

  return (
    <MainContainer narrowStyle={{ paddingHorizontal: space[3] }}>
      <H6 fontWeight="bold">
        {t('agentProfile.achievementCollection.title')} ({numberOfAchievements})
      </H6>

      {numberOfAchievements > 0 && isWideScreen ? (
        <AwardScrollContainer
          style={{ width: '100%' }}
          horizontal
          onLayout={e => {
            const containerWidth = e.nativeEvent.layout.width || 0;
            const itemWidth = (containerWidth - AWARD_GAP * 3) / 8;
            containerWidth && setAwardDimension(itemWidth);
          }}>
          <Awards />
        </AwardScrollContainer>
      ) : numberOfAchievements > 0 && !isWideScreen ? (
        <AwardWrapContainer
          style={{ width: '100%' }}
          onLayout={e => {
            const containerWidth = e.nativeEvent.layout.width || 0;
            const itemWidth = (containerWidth - AWARD_GAP * 3) / 4;
            containerWidth && setAwardDimension(itemWidth);
          }}>
          <Awards />
        </AwardWrapContainer>
      ) : (
        <LargeBody
          color={colors.palette.fwdGreyDarkest}
          children={t('agentProfile.achievementCollection.emptyCase')}
        />
      )}
    </MainContainer>
  );
}

const MainContainer = styled(ResponsiveView)(({ theme }) => ({
  paddingHorizontal: theme.space[4],
}));

const AwardWrapContainer = styled(XView)(({ theme }) => ({
  flexWrap: 'wrap',
  marginTop: theme.space[4],
  gap: theme.space[2],
}));

const AwardScrollContainer = styled.ScrollView(({ theme }) => ({
  marginTop: theme.space[3],
}));
