import React from 'react';
import Svg, { <PERSON><PERSON><PERSON><PERSON>, De<PERSON>, G, <PERSON> } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function FWDInsuranceBSNLogoWhite(props: SvgIconProps) {
  return (
    <Svg
      width={props?.width ?? 59}
      height={props?.height ?? 28}
      viewBox="0 0 59 28"
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_37367_177084)" fill="#fff">
        <Path d="M46.298 9.088a.676.676 0 010 .958l-3.383 3.308a.713.713 0 01-.755.146.676.676 0 01-.425-.624V6.258a.68.68 0 01.425-.627.71.71 0 01.755.148l3.383 3.31" />
        <Path d="M25.091.48c-.402.319-.688.698-.865 1.14-.177.44-.371 1.079-.6 1.922l-.178.662-2.199 8.534-2.563-10.917C18.468.778 17.877.134 16.41.134H.747c-.72 0-.745.75-.747.757v17.691s.012.376.38.376h1.455c1.045 0 2.381-.665 2.381-2.498v-5.39h5.485c2.091 0 2.6-1.472 2.6-2.31V8.45c0-.298-.101-.478-.465-.478h-7.62V3.507h10.773l3.25 12.349c.1.37.669 2.324.669 2.324.219.685.194.785.786.785h1.317c1.618 0 2.177-.984 2.463-1.678.143-.376.245-.542.476-1.39L26.817 5.25l3.031 10.593.628 2.331c.218.683.196.789.788.789h1.316c1.619 0 2.179-.985 2.465-1.683.14-.376.281-.736.514-1.583l2.98-12.18h9.228c.793 0 1.432.068 2.007.24.744.22 1.406.77 1.958 1.632.535.844.84 2.323.854 4.16 0 2.069-.629 3.906-1.794 4.9a2.8 2.8 0 01-.926.542 4.377 4.377 0 01-1.062.252c-.364.037-.844.11-1.478.116h-.008l-2.013.003h-2.892l-.014-.003c-.625 0-.645.357-.645.512v.74c0 1.078.901 2.316 2.712 2.316h.063l3.512-.002a15.493 15.493 0 002.415-.194 7.926 7.926 0 002.037-.626 7.006 7.006 0 001.75-1.162 8.203 8.203 0 001.65-2.113 9.538 9.538 0 00.932-2.629c.185-.911.277-1.56.277-2.64V9.54c-.03-3.5-1.08-5.989-3.169-7.725A6.521 6.521 0 0051.237.42C50.315.192 49.226.08 47.981.08h-11.18c-.614.006-1.533.318-1.887 1.743L32.488 12.67 29.91 4.164v.007l-.173-.627c-.249-.907-.449-1.557-.598-1.951-.15-.392-.414-.755-.802-1.092-.383-.333-.924-.499-1.618-.499-.695 0-1.23.159-1.628.479M37.517 26.313c-.145.393-.452.67-1.014.67-.599 0-1.097-.42-1.124-1.001h3.188c0-.018.017-.197.017-.366 0-1.412-.823-2.278-2.2-2.278-1.14 0-2.19.91-2.19 2.313 0 1.485 1.076 2.35 2.3 2.35 1.095 0 1.8-.634 2.028-1.393l-1.005-.294v-.001zm-2.111-1.143c.027-.404.371-.868.997-.868.688 0 .978.43.997.868h-1.994zm-3.781-.734c.597 0 .879.375.987.741l1.077-.358c-.19-.74-.878-1.482-2.091-1.482-1.296 0-2.31.973-2.31 2.332 0 1.358 1.033 2.331 2.346 2.331 1.186 0 1.883-.75 2.082-1.483l-1.06-.348c-.099.34-.407.741-1.023.741-.616 0-1.14-.446-1.14-1.242 0-.795.516-1.233 1.132-1.233m-5.904.904c0-.51.307-.913.833-.913.58 0 .825.385.825.876v2.565h1.203v-2.77c0-.965-.507-1.74-1.611-1.74-.48 0-1.015.205-1.287.66v-.545h-1.166v4.395h1.203v-2.528zm-4.291 1.77c-.39 0-.581-.25-.581-.51 0-.34.245-.51.553-.554l1.005-.152v.196c0 .779-.47 1.02-.978 1.02m-1.783-.439c0 .688.58 1.32 1.53 1.32.66 0 1.087-.302 1.313-.65 0 .169.019.41.046.526h1.104a4.861 4.861 0 01-.054-.696v-2.162c0-.884-.525-1.67-1.938-1.67-1.196 0-1.838.759-1.91 1.446l1.067.224c.036-.385.327-.714.853-.714.525 0 .75.258.75.571 0 .151-.08.277-.334.313l-1.097.16c-.742.108-1.33.545-1.33 1.332zm-.426-3.215a2.584 2.584 0 00-.28-.019c-.382 0-.998.107-1.27.688v-.652h-1.166v4.394h1.202v-2.01c0-.947.535-1.242 1.15-1.242.11 0 .228.009.364.036v-1.196zm-4.776 4.412h1.15a7.353 7.353 0 01-.045-.795v-3.6h-1.203v2.555c0 .509-.309.867-.844.867-.56 0-.815-.394-.815-.885v-2.537h-1.203v2.779c0 .955.615 1.731 1.693 1.731.47 0 .987-.178 1.231-.589 0 .179.019.384.036.473zm-7.252-1.224c.055.5.516 1.358 1.856 1.358 1.167 0 1.728-.732 1.728-1.447 0-.643-.443-1.17-1.32-1.35l-.635-.133c-.245-.045-.408-.179-.408-.394 0-.25.254-.438.571-.438.508 0 .698.33.734.59l1.005-.223c-.055-.474-.48-1.268-1.748-1.268-.96 0-1.665.652-1.665 1.437 0 .617.39 1.126 1.248 1.314l.59.135c.343.071.48.232.48.427 0 .233-.19.44-.59.44-.525 0-.788-.323-.816-.672l-1.03.224zM3.668 25.34c0-.51.308-.913.833-.913.58 0 .824.385.824.876v2.565H6.53v-2.77c0-.965-.508-1.74-1.612-1.74-.48 0-1.014.205-1.286.66v-.545H2.464v4.395h1.203v-2.528zM.11 22.033c0 .394.334.724.742.724s.742-.33.742-.724a.747.747 0 00-.742-.741.737.737 0 00-.742.741zm1.348 1.439H.256v4.393h1.203v-4.393zM45.32 24.276c.909-.211 1.368-.695 1.368-1.45 0-1.065-.756-1.596-2.267-1.596h-2.766v6.5h3.093c1.48 0 2.218-.575 2.218-1.728 0-.932-.55-1.508-1.647-1.725zm-2.057-2.41h1.063c.575 0 1.007.343 1.007 1.03 0 .744-.415 1.116-.96 1.116h-1.11v-2.147zm1.07 5.227h-1.07v-2.45h1.177c.736 0 1.103.434 1.103 1.31 0 .76-.494 1.14-1.21 1.14zM50.31 23.6c-.794-.27-1.15-.74-1.15-1.116 0-.156.035-.288.103-.393l.063-.08c.089-.099.214-.17.377-.217 1.46-.36 2.488 1.304 2.488 1.304V21.73c-.606-.405-1.375-.487-1.796-.502h-.228c-.63.019-1.145.19-1.541.516-.42.346-.628.797-.628 1.363 0 .997.702 1.578 1.953 2.031.87.316 1.23.829 1.23 1.248a.683.683 0 01-.093.403s-.138.2-.43.284c-1.43.41-2.443-1.22-2.443-1.22v1.414c.593.368 1.326.448 1.733.461h.254l.054-.001-.006.001h.034v.001h.002c.716 0 1.286-.18 1.715-.541.429-.359.643-.839.643-1.435 0-1.013-.805-1.634-2.334-2.153zM58.358 21.23h-.116v4.608l-3.108-4.608h-1.211v6.5h.756v-4.277l2.859 4.276H59v-6.5h-.642z" />
      </G>
      <Defs>
        <ClipPath id="clip0_37367_177084">
          <Path fill="#fff" d="M0 0H59V28H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
