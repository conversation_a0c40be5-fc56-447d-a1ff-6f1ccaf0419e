import * as React from 'react';
import Svg, { <PERSON>, Rect, <PERSON>, <PERSON>, Defs, ClipPath } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';
import { View } from 'react-native';
import FWDInsuranceBSNLogo from './FWDInsuranceBSNLogo';

type MobileAgentNameCardSVGProps = SvgIconProps & {
  isHiddenDivider?: boolean;
};
export default function MobileAgentNameCardV2SVG(
  props: MobileAgentNameCardSVGProps,
) {
  return (
    <>
      <Svg
        width={props.width || 300}
        height={props.height || 200}
        viewBox="0 0 300 200"
        fill="none"
        {...props}>
        <G clipPath="url(#clip0_7802_65635)">
          <Rect width={300} height={200} rx={16} fill="#E87722" />
          <Mask
            id="a"
            maskUnits="userSpaceOnUse"
            x={0}
            y={0}
            width={300}
            height={200}>
            <Rect width={300} height={200} rx={16} fill="#E87722" />
          </Mask>
          <G mask="url(#a)">
            <Path
              d="M145.847 65.874c8.158 8.639 12.737 20.01 12.737 32.119 0 12.035-4.579 23.554-12.737 32.193L-71.477 352.437c-8.659 8.639-20.037 13.29-31.486 13.29-5.725 0-11.521-.959-17.031-3.47-16.53-7.088-27.264-23.554-27.264-41.94v-444.575c0-18.459 10.806-34.925 27.264-42.087 16.673-7.015 35.78-3.027 48.588 9.894L145.847 65.874z"
              fill="#FEF9F4"
              fillOpacity={0.3}
            />
          </G>
        </G>
        {!props.isHiddenDivider && <Path fill="#fff" d="M16 65H282V65.5H16z" />}
        <Defs>
          <ClipPath id="clip0_25881_145274">
            <Rect width="65" height="52" fill="white" />
          </ClipPath>
        </Defs>
      </Svg>
      <View style={{ position: 'absolute', top: 12, right: 12 }}>
        <FWDInsuranceBSNLogo />
      </View>
    </>
  );
}
