import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, Defs, ClipPath } from 'react-native-svg';

import { SvgIconProps } from 'cube-ui-components';
import { ColorValue } from 'react-native';

export default function FWDTakafulLogoSVG(
  props: SvgIconProps & { textColor?: ColorValue },
) {
  return (
    <Svg
      width={props?.width ?? 59}
      height={props?.height ?? 29}
      viewBox="0 0 59 29"
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_25678_66636)">
        <Path
          d="M47.134 9.417a.71.71 0 010 .992L43.7 13.835a.716.716 0 01-.498.206.7.7 0 01-.702-.7V6.484c0-.285.17-.54.432-.65a.71.71 0 01.768.153l3.435 3.429"
          fill="#fff"
        />
        <Path
          d="M25.599.496a2.85 2.85 0 00-.88 1.183c-.178.456-.377 1.118-.608 1.992l-.181.685-2.233 8.844-2.602-11.313C18.873.807 18.272.14 16.782.14H.878C.148.14.122.916.119.923v18.331s.012.39.386.39h1.478c1.061 0 2.417-.69 2.417-2.588v-5.584h5.569c2.123 0 2.641-1.527 2.641-2.395v-.322c0-.309-.103-.496-.473-.496H4.401V3.633h10.94l3.3 12.796c.102.383.68 2.408.68 2.408.222.71.197.813.798.813h1.337c1.643 0 2.21-1.02 2.501-1.738.146-.39.25-.561.485-1.44l2.91-11.031 3.078 10.976.637 2.416c.221.708.2.817.8.817h1.336c1.645 0 2.213-1.02 2.503-1.744.143-.39.286-.763.522-1.64l3.026-12.622h9.371c.806 0 1.455.071 2.038.249.755.228 1.428.799 1.988 1.691.543.875.854 2.407.868 4.31 0 2.145-.639 4.049-1.822 5.078-.264.24-.573.425-.94.56-.374.14-.736.227-1.079.262-.369.04-.857.115-1.5.12h-.01l-2.044.003H43.19l-.015-.003c-.635 0-.655.37-.655.531v.767c0 1.116.916 2.4 2.755 2.4h.064l3.566-.002c.908-.01 1.723-.076 2.452-.202a7.958 7.958 0 002.069-.648 7.063 7.063 0 001.778-1.205 8.488 8.488 0 001.675-2.19c.43-.815.747-1.723.946-2.723.189-.944.282-1.616.282-2.736v-.024c-.032-3.627-1.096-6.205-3.218-8.005A6.579 6.579 0 0052.151.434C51.214.196 50.108.08 48.844.08H37.49c-.622.007-1.556.33-1.915 1.807l-2.463 11.239-2.618-8.814v.007l-.175-.65c-.253-.94-.457-1.614-.608-2.022-.152-.406-.42-.783-.814-1.13-.389-.345-.938-.518-1.644-.518S26.005.164 25.6.495M26.688 28.87v-6.676h1.218v6.677h-1.218zm-2.114-.488c-.247.424-.77.609-1.246.609-1.09 0-1.713-.802-1.713-1.79v-2.868h1.218v2.62c0 .506.257.912.825.912.569 0 .852-.369.852-.894v-2.638h1.218v3.717c0 .35.027.663.046.82h-1.163a3.08 3.08 0 01-.037-.488zm-4.612-4.547v.498h1.018v1.052h-1.018v3.485h-1.227v-3.485h-.751v-1.052h.75v-.516c0-1.024.642-1.688 1.64-1.688.257 0 .505.046.606.092v1.033a1.375 1.375 0 00-.386-.046c-.274 0-.632.12-.632.627zm-3.82 3.2v-.202l-1.017.157c-.311.046-.559.22-.559.571 0 .267.193.526.586.526.514 0 .99-.25.99-1.051zm-1.447-.774l1.108-.166c.256-.037.339-.166.339-.323 0-.323-.247-.59-.76-.59-.514 0-.825.34-.862.738l-1.08-.231c.073-.71.723-1.494 1.932-1.494 1.429 0 1.961.811 1.961 1.724v2.232c0 .24.028.563.055.72h-1.117a3.128 3.128 0 01-.046-.545c-.23.36-.66.674-1.329.674-.962 0-1.548-.656-1.548-1.365 0-.812.595-1.264 1.346-1.375l.001.001zm-3.4-.026l1.84 2.629h-1.492L10.45 27.13l-.504.535v1.199H8.73v-6.677h1.218v3.827l1.557-1.687h1.594l-1.804 1.908zm-4.71.794v-.203l-1.017.157c-.312.046-.56.221-.56.572 0 .268.193.526.587.526.513 0 .99-.25.99-1.051v-.001zm-1.448-.775l1.108-.166c.256-.037.338-.166.338-.323 0-.323-.247-.59-.76-.59s-.825.34-.861.738l-1.081-.23c.074-.71.724-1.495 1.933-1.495 1.429 0 1.96.812 1.96 1.725v2.232c0 .24.028.562.056.719H6.712a3.123 3.123 0 01-.046-.544c-.229.36-.659.673-1.328.673-.962 0-1.549-.655-1.549-1.365 0-.812.596-1.264 1.347-1.375v.001zm-3.007-1.92h.907v1.088H2.13v1.9c0 .396.183.526.531.526.147 0 .311-.018.376-.037v1.015c-.11.046-.33.11-.687.11-.88 0-1.43-.525-1.43-1.401v-2.113H.106v-1.088h.23c.476 0 .696-.313.696-.719v-.636h1.1v1.355"
          fill={props?.textColor ?? '#1C2B25'}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_25678_66636">
          <Path fill="#fff" transform="translate(.105)" d="M0 0H58V29H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
