import { PH_MOBILE_CODE } from 'constants/optionList';
import { object, string, InferType } from 'yup';
import {
  invalidFormatMessage,
  maxLength16,
  maxLength50,
  maxLength60,
  requiredInputMessage,
} from '../../fnaErrorMessages';
import {
  emailRegex,
  maxEmailLength,
  maxMobileLength,
  maxNameLength,
  mobileWithout0PrefixRegex,
} from '../../fnaValidation';

export type PHCreateLeadFormSchemaType = InferType<
  typeof phCreateLeadFormValidationSchema
>;
export type PHCreateLeadFormSchemaKey = keyof PHCreateLeadFormSchemaType;

export const phInitialCreateLeadFormData: PHCreateLeadFormSchemaType = {
  firstName: '',
  lastName: '',
  countryCode: PH_MOBILE_CODE,
  mobileNumber: '',
  email: '',
};

export const phCreateLeadFormValidationSchema = object({
  firstName: string()
    .max(maxNameLength, maxLength60)
    .required(requiredInputMessage)
    .validateName(invalidFormatMessage),
  lastName: string()
    .max(maxNameLength, maxLength60)
    .required(requiredInputMessage)
    .validateName(invalidFormatMessage),
  countryCode: string().required(requiredInputMessage),
  mobileNumber: string()
    .required(requiredInputMessage)
    .max(maxMobileLength, maxLength16)
    .matches(mobileWithout0PrefixRegex, {
      excludeEmptyString: true,
      message: invalidFormatMessage,
    }),
  email: string()
    .matches(emailRegex, {
      excludeEmptyString: true,
      message: invalidFormatMessage,
    })
    .required(requiredInputMessage)
    .max(maxEmailLength, maxLength50),
});
