import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import IncompleteFields from 'components/IncompleteFields';
import {
  Body,
  Box,
  ExtraLargeBody,
  Icon,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { Concerns } from 'features/fna/components/concerns/Concerns';
import useToggle from 'hooks/useToggle';
import {
  default as React,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { SceneMap, TabView } from 'react-native-tab-view';
import { countryModuleFnaConfig } from 'utils/config/module';
import { useFnaConcerns } from '../hooks/useFnaConcerns';
import {
  getCheckLifeJourneyCompletion,
  useCheckFnaCompletion,
  useGetLifeJourneyIncompleteFields,
} from '../utils/helper/checkFnaCompletion';
import { useFnaStore } from '../utils/store/fnaStore';
import { ExploreNeeds } from './exploreNeeds/ExploreNeeds';
import { InternalFnaProps } from './Fna';
import FnaContinueButton from './FnaContinueButton';
import GoalsPlanning from './goals/GoalsPlanning';
import FnaHeader from './header/FnaHeader';
import LifeJourneyTablet from './lifeJourney/tablet/LifeJourney.tablet';
import LifeJourneySummaryTablet from './lifeJourney/tablet/LifeJourneySummary.tablet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import SummaryBottomSheet from './summary/SummaryBottomSheet';

export default function FnaTablet({
  isLoading,
  onViewPlans,
}: InternalFnaProps) {
  const { t } = useTranslation(['fna']);
  const { colors, space } = useTheme();
  const [planningGoalVisible, showPlanningGoal, hidePlanningGoal] = useToggle();
  const [isCalculatingGoal, setCalculatingGoal] = useState(false);

  const summarySheetRef = useRef<BottomSheetModal>(null);
  const [index, setIndex] = useState(0);

  const setHighlight = useFnaStore(state => state.setHighlight);

  const navigationState = useMemo(() => {
    return {
      index,
      routes,
    };
  }, [index]);

  const totalIncompleteRequiredFields = useGetLifeJourneyIncompleteFields();

  const highlightFields = useCallback(() => {
    setHighlight(true);
  }, [setHighlight]);

  const resetConcernsPlanning = useFnaStore(
    state => state.resetConcernsPlanning,
  );

  return (
    <>
      <Container>
        <FnaHeader title={isCalculatingGoal ? t('fna:tellUsAboutYou') : ''} />
        <TabView
          swipeEnabled={false}
          navigationState={navigationState}
          onIndexChange={setIndex}
          renderScene={renderScene}
          lazy
          renderTabBar={() => null}
        />
        <Footer>
          {isCalculatingGoal ? (
            <BackButton
              onPress={() => {
                setCalculatingGoal(false);
                setIndex(0);
              }}>
              <Icon.ArrowLeft
                size={20}
                fill={colors.palette.fwdAlternativeOrange[100]}
              />
              <LargeLabel
                fontWeight="bold"
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('fna:editPrioritization')}
              </LargeLabel>
            </BackButton>
          ) : (
            <IncompleteFields
              incompleteCount={totalIncompleteRequiredFields}
              focusNext={highlightFields}
              message={t('fna:incompleteFields', {
                count: totalIncompleteRequiredFields,
              })}
              style={{ marginLeft: space[6] }}
            />
          )}
          <Box flex={1} />
          <FnaContinueButton
            isCalculatingGoal={isCalculatingGoal}
            onPressBeforeCalculation={() => {
              if (countryModuleFnaConfig.hasGoalPlan) {
                resetConcernsPlanning();
                showPlanningGoal();
              } else {
                setCalculatingGoal(true);
                setIndex(1);
              }
            }}
            onPressAfterCalculation={onViewPlans}
            onPressSummary={() => summarySheetRef.current?.present()}
            isLoading={isLoading}
          />
        </Footer>
        {countryModuleFnaConfig.hasGoalPlan && (
          <GoalsPlanning
            isModalVisible={planningGoalVisible}
            onDismiss={hidePlanningGoal}
            onDone={() => {
              setCalculatingGoal(true);
              setIndex(1);
            }}
          />
        )}
      </Container>
      <SummaryBottomSheet
        ref={summarySheetRef}
        onViewPlans={onViewPlans}
        setIndex={setIndex}
      />
    </>
  );
}

const Container = styled(View)({
  backgroundColor: '#F9F9F9',
  flex: 1,
});

const Footer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: theme.colors.background,
  paddingHorizontal: theme.space[6],
  paddingVertical: theme.space[4],
  borderTopWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
}));

const BackButton = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.space[1],
  paddingVertical: theme.space[2],
  paddingHorizontal: theme.space[4],
}));

const ConcernSelection = () => {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  const { maxSelection } = useFnaConcerns();
  const lifeJourney = useFnaStore(state => state.lifeJourney);
  const isLifeJourneyCompleted = useMemo(
    () => getCheckLifeJourneyCompletion(lifeJourney),
    [lifeJourney],
  );
  return (
    <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
      <LifeJourneyTablet />
      <Box
        mt={isLifeJourneyCompleted ? -space[14] : space[10]}
        gap={space[2]}
        px={space[16]}
        overflow="visible">
        <Box gap={space[3]}>
          <Row
            alignItems="center"
            gap={space[3]}
            width={isLifeJourneyCompleted ? '40%' : '100%'}>
            <ExtraLargeBody fontWeight="bold">
              {t('fna:lifeStage.detail.concerns')}
            </ExtraLargeBody>
          </Row>
          <Body>
            {t('fna:lifeStage.detail.concerns.desc', { maxSelection })}
          </Body>
        </Box>
        <Row>
          <Concerns />
        </Row>
      </Box>
    </ScrollView>
  );
};

const GoalsCalculation = () => {
  const { space } = useTheme();
  const isFnaValid = useCheckFnaCompletion();
  return (
    <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
      <LifeJourneySummaryTablet />
      <Box mt={-space[12]}>
        <ExploreNeeds isFnaValid={isFnaValid} />
      </Box>
    </ScrollView>
  );
};

const renderScene = SceneMap({
  concernSelection: ConcernSelection,
  goalsCalculation: GoalsCalculation,
});

const routes = [{ key: 'concernSelection' }, { key: 'goalsCalculation' }];
