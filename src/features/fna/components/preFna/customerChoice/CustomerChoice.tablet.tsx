import { useTheme } from '@emotion/react';
import { Button, Column, H6, Icon, Row } from 'cube-ui-components';
import {
  AdviceType as AdviceTypeKey,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { shallow } from 'zustand/shallow';
import FullAdviceIcon from 'features/fna/components/icons/FullAdviceIcon';
import PartialAdviceIcon from 'features/fna/components/icons/PartialAdviceIcon';
import React, { useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import TabletBottomSheet from 'components/TabletBottomSheet';
import { CustomerChoiceProps } from './CustomerChoice';
import styled from '@emotion/native';
import ChoiceButton from './ChoiceButton';

export default function CustomerChoice({
  visible,
  onDismiss,
  onAccept,
  onBack,
}: CustomerChoiceProps) {
  const { space, colors, borderRadius, sizes } = useTheme();
  const { t } = useTranslation(['fna']);
  const { adviceType, updateAdviceType, vulnerableCustomerTag } = useFnaStore(
    state => ({
      adviceType: state.adviceType,
      updateAdviceType: state.updateAdviceType,
      vulnerableCustomerTag: state.vulnerable.vulnerableCustomerTag,
    }),
    shallow,
  );

  useEffect(() => {
    if (vulnerableCustomerTag === 'Y') {
      updateAdviceType(AdviceTypeKey.FULL);
    }
  }, [vulnerableCustomerTag]);

  const isValid = Boolean(adviceType);
  const navigation = useRootStackNavigation();
  const insets = useSafeAreaInsets();

  return (
    <TabletBottomSheet visible={visible} backdropColor="transparent">
      <Column
        pb={insets.bottom}
        backgroundColor={colors.background}
        paddingTop={space[6]}
        gap={space[2]}
        borderTopLeftRadius={borderRadius.large}
        borderTopRightRadius={borderRadius.large}>
        <Column paddingX={space[6]}>
          <Row alignItems="center" gap={space[3]}>
            <TouchableOpacity
              onPress={() => {
                onDismiss?.();
                if (navigation.canGoBack()) {
                  navigation.goBack();
                } else {
                  navigation.navigate('Main', {
                    screen: 'Home',
                  });
                }
              }}>
              <Icon.Close fill={colors.secondary} />
            </TouchableOpacity>

            <H6 fontWeight="bold">{t('fna:customerChoice.title')}</H6>
          </Row>
          <Row mx={space[20]} mt={space[8]} mb={space[6]} gap={space[6]}>
            <ChoiceButton
              type={AdviceTypeKey.FULL}
              label={t('fna:customerChoice.fullAdvice')}
              instruction={t('fna:customerChoice.fullAdvice.instruction')}
              description={t('fna:customerChoice.fullAdvice.description')}
              selected={adviceType === AdviceTypeKey.FULL}
              onPress={() => updateAdviceType(AdviceTypeKey.FULL)}
              icon={FullAdviceIcon}
            />
            <ChoiceButton
              type={AdviceTypeKey.PARTIAL}
              label={t('fna:customerChoice.simpleAdvice')}
              instruction={t('fna:customerChoice.simpleAdvice.instruction')}
              description={t('fna:customerChoice.simpleAdvice.description')}
              selected={adviceType === AdviceTypeKey.PARTIAL}
              onPress={() => updateAdviceType(AdviceTypeKey.PARTIAL)}
              icon={PartialAdviceIcon}
              disabled={vulnerableCustomerTag === 'Y'}
            />
          </Row>
        </Column>

        <Row
          alignItems="center"
          justifyContent="flex-end"
          gap={space[5]}
          paddingY={space[4]}
          paddingX={space[6]}
          borderTopWidth={1}
          borderColor={colors.palette.fwdGrey[100]}>
          <ActionButton
            text={t('fna:back')}
            variant="secondary"
            onPress={onBack}
            minWidth={sizes[29]}
          />
          <ActionButton
            variant="primary"
            text={t('fna:confirm')}
            onPress={() => {
              onAccept?.();
              onDismiss?.();
            }}
            disabled={!isValid}
            minWidth={sizes[50]}
          />
        </Row>
      </Column>
    </TabletBottomSheet>
  );
}

const ActionButton = styled(Button)<{
  minWidth?: number;
}>(({ minWidth }) => ({
  ...(minWidth && {
    minWidth,
  }),
}));
