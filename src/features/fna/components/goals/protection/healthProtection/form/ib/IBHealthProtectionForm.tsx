import { GoalFormProps } from 'features/fna/types/goal';
import { FnaState, initialFnaState } from 'features/fna/utils/store/fnaStore';
import React, { useEffect, useCallback } from 'react';
import IBHealthProtectionFormPhone from './IBHealthProtectionForm.phone';
import IBHealthProtectionFormTablet from './IBHealthProtectionForm.tablet';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { InternalHealthProtectionFormProps } from '../HealthProtectionForm';
import { IB_DEFAULT_MEDICAL_PROTECTION_TARGET_AMOUNT } from 'features/fna/constants/goalCalculation';
import { useGetAverageAnnualIncome } from 'features/fna/hooks/useGetAverageAnnualIncome';
import { countryModuleFnaConfig } from 'utils/config/module';

export type IBHealthProtectionFormProps = GoalFormProps<
  FnaState['healthProtectionGoal']
> & {
  levelOfHospitals: { label: string; displayLabel: string; value: number }[];
  isExistingHealthInsPolicies: boolean | null;
  updateSkipThisGoal: (disabled: boolean) => void;
  updateHospitalisationTargetAmount: (target: number | null) => void;
  updateHospitalisationCoverageAmount: (target: number | null) => void;
  updateHospitalisationExpiryAge: (target: number | null) => void;
  updateCriticalIllnessTargetAmount: (target: number | null) => void;
  updateCriticalIllnessCoverageAmount: (target: number | null) => void;
  updateCriticalIllnessExpiryAge: (target: number | null) => void;
  updateHaveExisting: (value: boolean) => void;
};

export default function IBHealthProtectionForm({
  formData: healthGoal,
  setFormData: updateHealthGoal,
  levelOfHospitals,
  isExistingHealthInsPolicies,
}: InternalHealthProtectionFormProps) {
  const averageAnnualIncome = useGetAverageAnnualIncome();

  useEffect(() => {
    if (healthGoal.enabled && countryModuleFnaConfig.hasDefaultGoalValue) {
      updateHealthGoal({
        hospitalisation:
          healthGoal.hospitalisation === null
            ? {
                targetAmount: IB_DEFAULT_MEDICAL_PROTECTION_TARGET_AMOUNT,
                coverageAmount: 0,
                expiryAge: 0,
                gapAmount: IB_DEFAULT_MEDICAL_PROTECTION_TARGET_AMOUNT,
              }
            : healthGoal.hospitalisation,
        criticalIllness:
          healthGoal.criticalIllness === null
            ? {
                targetAmount: averageAnnualIncome * 5,
                coverageAmount: 0,
                expiryAge: 0,
                gapAmount: averageAnnualIncome * 5,
              }
            : healthGoal.criticalIllness,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const calculateGap = useCallback(
    (target?: number | null, coverage?: number | null) => {
      if (typeof target !== 'number') return null;

      return Math.max(0, target - (coverage || 0));
    },
    [],
  );

  const updateSkipThisGoal = (disabled: boolean) => {
    if (disabled) {
      updateHealthGoal({
        ...initialFnaState.healthProtectionGoal,
        hospitalisation: {
          targetAmount: 0,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: 0,
        },
        criticalIllness: {
          targetAmount: 0,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: 0,
        },
        enabled: !disabled,
      });
    } else {
      updateHealthGoal({
        hospitalisation: {
          targetAmount: IB_DEFAULT_MEDICAL_PROTECTION_TARGET_AMOUNT,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: IB_DEFAULT_MEDICAL_PROTECTION_TARGET_AMOUNT,
        },
        criticalIllness: {
          targetAmount: averageAnnualIncome * 5,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: averageAnnualIncome * 5,
        },
        enabled: !disabled,
      });
    }
  };

  const updateHospitalisationTargetAmount = (target: number | null) => {
    updateHealthGoal({
      hospitalisation: {
        coverageAmount: null,
        expiryAge: null,
        ...healthGoal.hospitalisation,
        targetAmount: target,
        gapAmount: calculateGap(
          target,
          healthGoal.hospitalisation?.coverageAmount,
        ),
      },
    });
  };

  const updateCriticalIllnessTargetAmount = (target: number | null) => {
    updateHealthGoal({
      criticalIllness: {
        coverageAmount: null,
        expiryAge: null,
        ...healthGoal.criticalIllness,
        targetAmount: target,
        gapAmount: calculateGap(
          target,
          healthGoal.criticalIllness?.coverageAmount,
        ),
      },
    });
  };

  const updateHaveExisting = (hasSavings: boolean) => {
    if (hasSavings !== healthGoal.hasSavings) {
      updateHealthGoal({
        hasSavings,
        hospitalisation: {
          targetAmount: null,
          ...healthGoal.hospitalisation,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: calculateGap(healthGoal.hospitalisation?.targetAmount, 0),
        },
        criticalIllness: {
          targetAmount: null,
          ...healthGoal.criticalIllness,
          coverageAmount: 0,
          expiryAge: 0,
          gapAmount: calculateGap(healthGoal.criticalIllness?.targetAmount, 0),
        },
      });
    }
  };

  const updateHospitalisationCoverageAmount = (coverage: number | null) => {
    updateHealthGoal({
      hospitalisation: {
        targetAmount: null,
        expiryAge: null,
        ...healthGoal.hospitalisation,
        coverageAmount: coverage,
        gapAmount: calculateGap(
          healthGoal.hospitalisation?.targetAmount,
          coverage,
        ),
      },
    });
  };
  const updateHospitalisationExpiryAge = (value: number | null) => {
    updateHealthGoal({
      hospitalisation: {
        targetAmount: null,
        coverageAmount: null,
        gapAmount: null,
        ...healthGoal.hospitalisation,
        expiryAge: value,
      },
    });
  };

  const updateCriticalIllnessCoverageAmount = (coverage: number | null) => {
    updateHealthGoal({
      criticalIllness: {
        targetAmount: null,
        expiryAge: null,
        ...healthGoal.criticalIllness,
        coverageAmount: coverage,
        gapAmount: calculateGap(
          healthGoal.criticalIllness?.targetAmount,
          coverage,
        ),
      },
    });
  };

  const updateCriticalIllnessExpiryAge = (value: number | null) => {
    updateHealthGoal({
      criticalIllness: {
        targetAmount: null,
        coverageAmount: null,
        gapAmount: null,
        ...healthGoal.criticalIllness,
        expiryAge: value,
      },
    });
  };

  return (
    <DeviceBasedRendering
      tablet={
        <IBHealthProtectionFormTablet
          formData={healthGoal}
          setFormData={updateHealthGoal}
          levelOfHospitals={levelOfHospitals}
          isExistingHealthInsPolicies={isExistingHealthInsPolicies}
          updateSkipThisGoal={updateSkipThisGoal}
          updateHospitalisationTargetAmount={updateHospitalisationTargetAmount}
          updateCriticalIllnessTargetAmount={updateCriticalIllnessTargetAmount}
          updateHaveExisting={updateHaveExisting}
          updateHospitalisationCoverageAmount={updateHospitalisationCoverageAmount}
          updateHospitalisationExpiryAge={updateHospitalisationExpiryAge}
          updateCriticalIllnessCoverageAmount={updateCriticalIllnessCoverageAmount}
          updateCriticalIllnessExpiryAge={updateCriticalIllnessExpiryAge}
        />
      }
      phone={
        <IBHealthProtectionFormPhone
          formData={healthGoal}
          setFormData={updateHealthGoal}
          levelOfHospitals={levelOfHospitals}
          isExistingHealthInsPolicies={isExistingHealthInsPolicies}
          updateSkipThisGoal={updateSkipThisGoal}
          updateHospitalisationTargetAmount={updateHospitalisationTargetAmount}
          updateCriticalIllnessTargetAmount={updateCriticalIllnessTargetAmount}
          updateHaveExisting={updateHaveExisting}
          updateHospitalisationCoverageAmount={updateHospitalisationCoverageAmount}
          updateHospitalisationExpiryAge={updateHospitalisationExpiryAge}
          updateCriticalIllnessCoverageAmount={updateCriticalIllnessCoverageAmount}
          updateCriticalIllnessExpiryAge={updateCriticalIllnessExpiryAge}
        />
      }
    />
  );
}
