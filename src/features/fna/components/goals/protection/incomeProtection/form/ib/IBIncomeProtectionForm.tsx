import React, { useEffect } from 'react';

import {
  FnaState,
  initialFnaState,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import { useGetAverageAnnualIncome } from 'features/fna/hooks/useGetAverageAnnualIncome';
import { calculateAge } from 'utils/helper/calculateAge';
import { AGE_TO_REPLACE_INCOME } from 'features/fna/constants/goalCalculation';
import { InternalIncomeProtectionFormProps } from '../IncomeProtectionForm';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { IBIncomeProtectionFormPhone } from './IBIncomeProtectionForm.phone';
import { IBIncomeProtectionFormTablet } from './IBIncomeProtectionForm.tablet';
import { GoalFormProps } from 'features/fna/types/goal';
import { countryModuleFnaConfig } from 'utils/config/module';
import { country } from 'utils/context';

export interface IBIncomeProtectionFormProps
  extends GoalFormProps<FnaState['incomeProtectionGoal']> {
  onCalculatorPress: () => void;
  updateMonthlyExpenses: (monthlyExpenses: number | null) => void;
  updateHasSavings: (value: string) => void;
  updateCoverageAmount: (coverageAmount: number | null) => void;
  calculatorVisible: boolean;
  updateMonthlyExpensesCalculation: (
    result: Pick<
      FnaState['incomeProtectionGoal'],
      'monthlyExpenses' | 'expenses'
    >,
  ) => void;
  hideCalculator: () => void;
  updateSkipThisGoal: (disabled: boolean) => void;
  updateIncomeYears: (value: string) => void;
  updateHaveExisting: (value: boolean) => void;
  updateAnnualIncome?: (value: number | null) => void;
}

export default function IBIncomeProtectionForm({
  formData: incomeGoal,
  updateAnnualIncome,
  updateHasSavings,
  updateCoverageAmount,
  setFormData: updateIncomeGoal,
  onCalculatorPress,
  updateMonthlyExpenses,
  updateMonthlyExpensesCalculation,
  calculatorVisible,
  hideCalculator,
}: InternalIncomeProtectionFormProps) {
  const dob = useFnaStore(state => state.lifeJourney.dob);
  const ageToRetire = useFnaStore(state => state.lifeJourney.ageToRetire);
  const averageAnnualIncome = useGetAverageAnnualIncome();

  useEffect(() => {
    if (incomeGoal.enabled && countryModuleFnaConfig.hasDefaultGoalValue) {
      let ageToReplace = 0;
      switch (country) {
        case 'my':
          ageToReplace = ageToRetire;
          break;
        default:
          ageToReplace = AGE_TO_REPLACE_INCOME;
          break;
      }
      updateIncomeGoal({
        annualIncome:
          incomeGoal.annualIncome === null
            ? averageAnnualIncome
            : incomeGoal.annualIncome,
        yearsToReplace:
          incomeGoal.yearsToReplace === null && dob !== null
            ? Math.max(ageToReplace - calculateAge(dob), 0)
            : incomeGoal.yearsToReplace,
        coverageAmount:
          incomeGoal.coverageAmount === null ? 0 : incomeGoal.coverageAmount,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const updateSkipThisGoal = (disabled: boolean) => {
    let ageToReplace = 0;
    switch (country) {
      case 'my':
        ageToReplace = ageToRetire;
        break;
      default:
        ageToReplace = AGE_TO_REPLACE_INCOME;
        break;
    }
    if (disabled) {
      updateIncomeGoal({
        ...initialFnaState.incomeProtectionGoal,
        annualIncome: null,
        targetAmount: 0,
        gapAmount: 0,
        coverageAmount: 0,
        enabled: !disabled,
      });
    } else {
      updateIncomeGoal({
        annualIncome: averageAnnualIncome,
        yearsToReplace:
          dob === null ? null : Math.max(ageToReplace - calculateAge(dob), 0),
        coverageAmount: 0,
        gapAmount: null,
        targetAmount: null,
        enabled: !disabled,
      });
    }
  };

  const updateIncomeYears = (value: string) => {
    const yearsToReplace = Number(value);
    if (!isNaN(yearsToReplace)) {
      updateIncomeGoal({ yearsToReplace: yearsToReplace });
    }
  };

  const updateHaveExisting = (value: boolean) => {
    updateHasSavings(value ? 'yes' : 'no');
    if (value) {
      updateCoverageAmount(0);
    }
  };

  return (
    <DeviceBasedRendering
      tablet={
        <IBIncomeProtectionFormTablet
          formData={incomeGoal}
          setFormData={updateIncomeGoal}
          onCalculatorPress={onCalculatorPress}
          updateMonthlyExpenses={updateMonthlyExpenses}
          updateHasSavings={updateHasSavings}
          updateCoverageAmount={updateCoverageAmount}
          updateMonthlyExpensesCalculation={updateMonthlyExpensesCalculation}
          calculatorVisible={calculatorVisible}
          hideCalculator={hideCalculator}
          updateAnnualIncome={updateAnnualIncome}
          updateSkipThisGoal={updateSkipThisGoal}
          updateIncomeYears={updateIncomeYears}
          updateHaveExisting={updateHaveExisting}
        />
      }
      phone={
        <IBIncomeProtectionFormPhone
          formData={incomeGoal}
          setFormData={updateIncomeGoal}
          onCalculatorPress={onCalculatorPress}
          updateMonthlyExpenses={updateMonthlyExpenses}
          updateHasSavings={updateHasSavings}
          updateCoverageAmount={updateCoverageAmount}
          updateMonthlyExpensesCalculation={updateMonthlyExpensesCalculation}
          calculatorVisible={calculatorVisible}
          hideCalculator={hideCalculator}
          updateAnnualIncome={updateAnnualIncome}
          updateSkipThisGoal={updateSkipThisGoal}
          updateIncomeYears={updateIncomeYears}
          updateHaveExisting={updateHaveExisting}
        />
      }
    />
  );
}
