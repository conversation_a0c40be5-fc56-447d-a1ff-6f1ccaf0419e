import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { GoalFormProps } from 'features/fna/types/goal';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import RetirementFormPhone from './PHRetirementForm.phone';
import RetirementFormTablet from './PHRetirementForm.tablet';
import { FormProps } from '../RetirementForm';

export interface RetirementFormProps
  extends GoalFormProps<FnaState['retirementGoal']> {
  dob?: Date | null;
  updateAgeRetire?: (ageToRetire: number) => void;
  yearsToReceiveRetirementAllowance: number | null;
}

export default function PHRetirementForm({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  dob,
  updateAgeRetire,
  yearsToReceiveRetirementAllowance,
}: FormProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <RetirementFormTablet
          formData={retirementGoal}
          setFormData={updateRetirementGoal}
          dob={dob}
          updateAgeRetire={updateAgeRetire}
          yearsToReceiveRetirementAllowance={yearsToReceiveRetirementAllowance}
        />
      }
      phone={
        <RetirementFormPhone
          formData={retirementGoal}
          setFormData={updateRetirementGoal}
          dob={dob}
          updateAgeRetire={updateAgeRetire}
          yearsToReceiveRetirementAllowance={yearsToReceiveRetirementAllowance}
        />
      }
    />
  );
}
