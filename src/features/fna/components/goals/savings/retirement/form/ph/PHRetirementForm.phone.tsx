import { useTheme } from '@emotion/react';
import {
  Box,
  Dropdown,
  H6,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  booleanToYesNoValue,
  yesNoValueToBoolean,
} from 'features/fna/utils/helper/fnaUtils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import ElderlyCoupleLogo from 'features/fna/components/illustrations/ElderlyCoupleLogo';
import type { RetirementFormProps } from './PHRetirementForm';

export default function PHRetirementFormPhone({
  formData: retirementGoal,
  setFormData: updateRetirementGoal,
  dob,
  updateAgeRetire,
  yearsToReceiveRetirementAllowance,
}: RetirementFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.retirement.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <ElderlyCoupleLogo />
        </Box>
      </Row>
      <Row mt={space[5]}>
        <Row flex={1}>
          <LargeLabel>1.</LargeLabel>
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:savingsGoals.retirement.ageToRetire')}
          </LargeLabel>
        </Row>
        <Box w={space[3]} />
        <Dropdown
          data={[50, 55, 60, 65].filter(item =>
            dob ? item > calculateAge(dob) : true,
          )}
          getItemLabel={item => item.toString()}
          getItemValue={item => item}
          label={t('fna:savingsGoals.retirement.age')}
          modalTitle={t('fna:savingsGoals.retirement.ageToRetire')}
          value={retirementGoal.ageToRetire}
          onChange={ageToRetire => {
            updateRetirementGoal({ ageToRetire });
            updateAgeRetire?.(Number(ageToRetire));
          }}
          style={{ flex: 1 }}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>2.</LargeLabel>
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:savingsGoals.retirement.monthlyAllowance')}
          </LargeLabel>
        </Row>
        <Box w={space[3]} />
        <DecimalTextField
          label={t('fna:amount.label')}
          style={{ flex: 1 }}
          value={retirementGoal.monthlyAllowance}
          onChange={monthlyAllowance =>
            updateRetirementGoal({ monthlyAllowance })
          }
          max={MAX_AMOUNT_VALUE}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>3.</LargeLabel>
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:savingsGoals.retirement.yearsToReceive')}
          </LargeLabel>
        </Row>
        <Box w={space[3]} />
        <TextField
          label={t('fna:savingsGoals.retirement.years')}
          value={String(yearsToReceiveRetirementAllowance ?? '')}
          disabled
          keyboardType="numeric"
          style={{ flex: 1 }}
        />
      </Row>
      <Row mt={space[6]}>
        <Row flex={1}>
          <LargeLabel>4.</LargeLabel>
          <LargeLabel style={{ flex: 1 }}>
            {t('fna:savingsGoals.retirement.anyProgramStarted')}
          </LargeLabel>
        </Row>
        <Box w={space[3]} />
        <Row flex={1} justifyContent="space-between">
          <RadioButtonGroup
            value={booleanToYesNoValue(retirementGoal.hasSavings)}
            onChange={value => {
              const hasSavings = yesNoValueToBoolean(value);
              if (hasSavings !== retirementGoal.hasSavings) {
                updateRetirementGoal({
                  hasSavings,
                  coverageAmount: null,
                });
              }
            }}>
            <RadioButton label={t('fna:yes')} value="yes" />
            <RadioButton label={t('fna:no')} value="no" />
          </RadioButtonGroup>
        </Row>
      </Row>
      {retirementGoal.hasSavings && (
        <Row mt={space[6]}>
          <Row flex={1}>
            <LargeLabel>4.1.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.retirement.currentProgram')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            value={retirementGoal.coverageAmount}
            onChange={coverageAmount =>
              updateRetirementGoal({ coverageAmount })
            }
            style={{ flex: 1 }}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
      )}
    </Box>
  );
}
