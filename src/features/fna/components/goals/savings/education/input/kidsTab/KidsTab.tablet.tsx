import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, Typography } from 'cube-ui-components';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { KidsTabProps } from './KidsTab';

export default function KidsTab({
  disabled,
  text,
  icon,
  onPress,
  onRemove,
  isRemovable,
}: KidsTabProps) {
  const { space, colors } = useTheme();
  return (
    <TagContainer
      disabled={disabled}
      onlyIcon={!text && Boolean(icon)}
      onPress={onPress}>
      {Boolean(text) && <Content>{text}</Content>}
      {Boolean(icon) && icon}
      {isRemovable && (
        <>
          <Box width={sizes[1]} />
          <TouchableOpacity onPress={onRemove}>
            <Icon.CloseCircle fill={colors.secondary} size={space[5]} />
          </TouchableOpacity>
        </>
      )}
    </TagContainer>
  );
}

const TagContainer = styled.TouchableOpacity<{ onlyIcon?: boolean }>(
  ({ theme, onlyIcon, disabled }) => ({
    opacity: disabled ? 0.5 : 1,
    minHeight: theme.sizes[9],
    backgroundColor: theme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: onlyIcon ? 10 : theme.space[4],
    borderWidth: 1,
    borderColor: theme.colors.palette.fwdGrey[100],
    borderRadius: theme.borderRadius.full,
    marginRight: theme.sizes[1],
    flexDirection: 'row',
  }),
);

const Content = styled(Typography.LargeBody)<KidsTabProps>(({ theme }) => ({
  color: theme.colors.onSurface,
}));
