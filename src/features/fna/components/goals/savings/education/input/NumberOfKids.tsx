import { useTheme } from '@emotion/react';
import { Box, Icon, LargeLabel, Row } from 'cube-ui-components';
import React, { memo, useMemo, useState } from 'react';
import { TextInput, TouchableOpacity } from 'react-native';
import {
  FnaState,
  initialEducationKidGoal,
} from 'features/fna/utils/store/fnaStore';
import { GoalFormProps } from 'features/fna/types/goal';
import { cloneDeep } from 'utils/helper/objectUtil';
import { useTranslation } from 'react-i18next';
import { useGetMaxKids } from 'features/fna/hooks/useGetMaxKids';

interface NumberOfKidsProps extends GoalFormProps<FnaState['educationGoal']> {
  calculateTotalFunds: (newGoal: FnaState['educationGoal']['goals']) => void;
}

export const NumberOfKids = memo(function NumberOfKids({
  formData: educationGoal,
  setFormData: updateEducationGoal,
  calculateTotalFunds,
}: NumberOfKidsProps) {
  const { t } = useTranslation(['fna']);
  const theme = useTheme();
  const { space, sizes, colors } = useTheme();
  const [editing, setEditing] = useState(false);
  const [numberOfKids, setNumberOfKids] = useState(educationGoal.numberOfKids);
  const maxKids = useGetMaxKids();

  const title = useMemo(() => {
    return `${educationGoal.numberOfKids} ${
      educationGoal.numberOfKids === 1
        ? t('fna:savingsGoals.education.kid')
        : t('fna:savingsGoals.education.kids')
    }`;
  }, [educationGoal.numberOfKids, t]);

  const onBlur = () => {
    const kids = !numberOfKids ? 1 : numberOfKids;
    const goals = educationGoal.goals;
    const newGoals = [];
    for (let i = 0; i < kids; i++) {
      if (typeof goals[i] !== 'undefined') {
        newGoals.push(goals[i]);
      } else {
        const initial = cloneDeep(initialEducationKidGoal);
        newGoals.push(initial);
      }
    }
    setNumberOfKids(kids);
    updateEducationGoal({
      numberOfKids: kids,
      goals: newGoals,
    });
    calculateTotalFunds(newGoals);
    setEditing(false);
  };

  return (
    <>
      <TouchableOpacity onPress={() => setEditing(true)}>
        <Row alignItems="center">
          {editing ? (
            <TextInput
              value={(numberOfKids ?? '').toString()}
              maxLength={maxKids > 9 ? 2 : 1}
              onChangeText={text => {
                if (text.length === 0) {
                  setNumberOfKids(null);
                  return;
                }
                const numberOfKids = parseInt(text, 10);
                if (!Number.isNaN(numberOfKids) && numberOfKids <= maxKids) {
                  setNumberOfKids(numberOfKids);
                }
              }}
              style={{
                fontSize: theme.typography.largeLabel.size,
                lineHeight: theme.typography.largeLabel.lineHeight,
                fontFamily: 'FWDCircularTT-Bold',
              }}
              cursorColor={theme.components.textField.label.focus.default}
              selectionColor={theme.components.textField.label.focus.default}
              keyboardType="number-pad"
              returnKeyType="done"
              autoFocus
              onBlur={onBlur}
            />
          ) : (
            <LargeLabel fontWeight="bold">{title}</LargeLabel>
          )}
          <Box width={space[2]} />
          <Icon.Create
            size={sizes[5]}
            fill={colors.palette.fwdAlternativeOrange[100]}
          />
        </Row>
      </TouchableOpacity>
    </>
  );
});
