import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { InternalKidFormProps } from '../KidForm';
import PHKidFormTablet from './PHKidForm.tablet';
import PHKidFormPhone from './PHKidForm.phone';

export default function PHKidForm(props: InternalKidFormProps) {
  return (
    <DeviceBasedRendering
      tablet={<PHKidFormTablet {...props} />}
      phone={<PHKidFormPhone {...props} />}
    />
  );
}
