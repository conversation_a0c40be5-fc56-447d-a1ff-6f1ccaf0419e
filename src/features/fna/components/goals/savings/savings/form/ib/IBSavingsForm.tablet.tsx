import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import MoneyBagIncrementIcon from 'features/fna/components/icons/MoneyBagIncrementIcon';
import FnaInput from 'features/fna/components/common/FnaInput';
import DecimalTextField from 'components/DecimalTextField';
import IllustrationSavingsGoalIcon from 'features/fna/components/illustrations/IllustrationSavingsGoalIcon';
import type { IBSavingsFormProps } from './IBSavingsForm';

export default function IBSavingsForm({
  formData: savingsGoal,
  updatePurpose,
  updateTargetAmount,
  updateYearsToAchieve,
  updateCoverageAmount,
}: IBSavingsFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();

  return (
    <>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.savings.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationSavingsGoalIcon />
        </Box>
      </Row>
      <Content>
        <FormContainer
          enabled={Boolean(savingsGoal.enabled)}
          pointerEvents={savingsGoal.enabled ? 'auto' : 'none'}>
          <Row alignItems="center">
            <MoneyBagIncrementIcon />
            <Box w={space[3]} />
            <FnaInput
              label={t('fna:savingsGoals.savings.whatYouWantToSave')}
              placeholder={t('fna:savingsGoals.savings.customGoal')}
              right={<Icon.Create />}
              value={savingsGoal.purpose}
              style={{ flex: 1 }}
              onChange={updatePurpose}
            />
          </Row>
          <Row mt={space[8]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>1.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.expectedSavingsNeeded')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={savingsGoal.targetAmount}
              onChange={updateTargetAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>2.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.yearsNeeded')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.savings.years')}
              style={{ flex: 1 }}
              value={savingsGoal.yearsToAchieve}
              onChange={updateYearsToAchieve}
              maxLength={2}
              precision={0}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1.8} alignItems="center">
              <FieldLabel>3.</FieldLabel>
              <Box w={space[3]} />
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.currentSavings')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={savingsGoal.coverageAmount}
              onChange={updateCoverageAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        </FormContainer>
      </Content>
      <Box mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </>
  );
}

const Content = styled(View)(({ theme: { colors, borderRadius, space } }) => ({
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[4],
}));

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));

const FieldLabel = styled(LargeLabel)(({ theme: { sizes } }) => ({
  width: sizes[10],
  textAlign: 'center',
}));
