import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { View } from 'react-native';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import MoneyBagIncrementIcon from 'features/fna/components/icons/MoneyBagIncrementIcon';
import FnaInput from 'features/fna/components/common/FnaInput';
import DecimalTextField from 'components/DecimalTextField';
import MarkdownTextMore from 'components/MarkdownTextMore';
import IllustrationSavingsGoalIcon from 'features/fna/components/illustrations/IllustrationSavingsGoalIcon';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import type { IBSavingsFormProps } from './IBSavingsForm';

export default function IBSavingsForm({
  formData: savingsGoal,
  updatePurpose,
  updateTargetAmount,
  updateYearsToAchieve,
  updateCoverageAmount,
}: IBSavingsFormProps) {
  const { t } = useTranslation(['fna']);
  const { space } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.savings.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IllustrationSavingsGoalIcon width={111} height={79} />
        </Box>
      </Row>
      <Box>
        <FormContainer
          enabled={Boolean(savingsGoal.enabled)}
          pointerEvents={savingsGoal.enabled ? 'auto' : 'none'}>
          <Row alignItems="center" gap={space[3]} ml={-space[1]}>
            <MoneyBagIncrementIcon />
            <FnaInput
              label={t('fna:savingsGoals.savings.whatYouWantToSave')}
              placeholder={t('fna:savingsGoals.savings.customGoal')}
              right={<Icon.Create size={space[5]} />}
              value={savingsGoal.purpose}
              style={{ flex: 1 }}
              onChange={updatePurpose}
            />
          </Row>
          <Row mt={space[8]}>
            <Row flex={1}>
              <LargeLabel>1.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.expectedSavingsNeeded')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={savingsGoal.targetAmount}
              onChange={updateTargetAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1}>
              <LargeLabel>2.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.yearsNeeded')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:savingsGoals.savings.years')}
              style={{ flex: 1 }}
              value={savingsGoal.yearsToAchieve}
              onChange={updateYearsToAchieve}
              maxLength={2}
              precision={0}
            />
          </Row>
          <Row mt={space[5]}>
            <Row flex={1}>
              <LargeLabel>3.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:savingsGoals.savings.currentSavings')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              style={{ flex: 1 }}
              value={savingsGoal.coverageAmount}
              onChange={updateCoverageAmount}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        </FormContainer>
      </Box>
      <Divider />
      <Box mb={space[10]} mt={space[4]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Divider = styled(View)(({ theme }) => {
  return {
    marginTop: theme.space[6],
    marginBottom: theme.space[2],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));
