import DeviceBasedRendering from 'components/DeviceBasedRendering';
import React, { useMemo } from 'react';
import GoalSummaryPhone from './GoalSummary.phone';
import GoalSummaryTablet from './GoalSummary.tablet';
import { Summary } from 'features/fna/types/goal';
import { country } from 'utils/context';

interface Props {
  title: string;
  summary: Summary;
  isCompleted?: boolean;
  isGroupCompleted?: boolean;
  onNext?: () => void;
  onDone?: () => void;
  children?: React.ReactNode;
}

export default function GoalSummary({
  title,
  summary,
  isCompleted,
  isGroupCompleted,
  onNext,
  onDone,
  children,
}: Props) {
  const disabledNext = useMemo(() => {
    switch (country) {
      case 'ph':
        return false;
      case 'my':
      case 'ib':
        return false;
      default:
        return false;
    }
  }, []);

  const disabledDone = useMemo(() => {
    switch (country) {
      case 'ph':
        return !isCompleted;
      case 'my':
      case 'ib':
        return !isCompleted;
      default:
        return false;
    }
  }, [isCompleted]);

  return (
    <DeviceBasedRendering
      tablet={
        <GoalSummaryTablet
          title={title}
          summary={summary}
          isCompleted={isCompleted}
          isGroupCompleted={isGroupCompleted}
          onNext={onNext}
          onDone={onDone}
          children={children}
          disabledNext={disabledNext}
          disabledDone={disabledDone}
        />
      }
      phone={
        <GoalSummaryPhone
          title={title}
          summary={summary}
          isCompleted={isCompleted}
          isGroupCompleted={isGroupCompleted}
          onNext={onNext}
          onDone={onDone}
          children={children}
          disabledNext={disabledNext}
          disabledDone={disabledDone}
        />
      }
    />
  );
}
