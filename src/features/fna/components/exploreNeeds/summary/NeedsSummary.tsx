import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import React, { useMemo } from 'react';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AGE_RETIREMENT } from 'features/fna/constants/goalCalculation';
import { goalsByNeed } from 'features/fna/constants/lifeJourney';
import { countryModuleFnaConfig } from 'utils/config/module';
import { country } from 'utils/context';
import HumanBrainIcon from '../../icons/HumanBrainIcon';
import FamilyHeartIcon from '../../icons/FamilyHeartIcon';
import FinancialGrowthIcon from '../../icons/FinancialGrowthIcon';
import MoneyBagIncrementIcon from '../../icons/MoneyBagIncrementIcon';
import MoneyBagIcon from '../../icons/MoneyBagIcon';
import MedicineIcon from '../../icons/MedicineIcon';
import MoneyUnderUmbrellaIcon from '../../icons/MoneyUnderUmbrellaIcon';
import NuturingFinanceIcon from '../../icons/NuturingFinanceIcon';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import NeedsSummaryTablet from './NeedsSummary.tablet';
import NeedsSummaryPhone from './NeedsSummary.phone';

export interface SummaryItem {
  id: string;
  title: string;
  icon: JSX.Element;
  value: number | null;
  target: number | null;
  coverage: number | null;
}

interface GoalGroupSummary {
  totalTarget: number;
  totalCoverage: number;
  totalGap: number;
  items: SummaryItem[];
}

export interface InternalNeedsSummary {
  savings: GoalGroupSummary;
  protection: GoalGroupSummary;
}

export default function NeedsSummary() {
  const { t } = useTranslation(['fna']);
  const { sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const fnaState = useFnaStore();

  const { savings, protection } = useMemo(() => {
    const concerns = Array.from(
      new Set([
        ...countryModuleFnaConfig.concerns.items,
        ...countryModuleFnaConfig.concerns.additionalItems,
      ]),
    );
    const savingsItems: SummaryItem[] = [];
    const protectionItems: SummaryItem[] = [];
    concerns.forEach(concern => {
      switch (concern) {
        case 'EDUCATION':
          savingsItems.push({
            id: 'EDUCATION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.EDUCATION')
              : t('fna:lifeStage.concern.EDUCATION.mobile'),
            icon: <HumanBrainIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.educationGoal.gapAmount,
            target: fnaState.educationGoal.targetAmount,
            coverage: fnaState.educationGoal.coverageAmount,
          });
          break;
        case 'RETIREMENT':
          savingsItems.push({
            id: 'RETIREMENT',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.RETIREMENT')
              : t('fna:lifeStage.concern.RETIREMENT.mobile'),
            icon: <FamilyHeartIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.retirementGoal.gapAmount,
            target: fnaState.retirementGoal.targetAmount,
            coverage: fnaState.retirementGoal.coverageAmount,
          });
          break;
        case 'INVESTMENT':
          savingsItems.push({
            id: 'INVESTMENT',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.INVESTMENT')
              : t('fna:lifeStage.concern.INVESTMENT.mobile'),
            icon: <FinancialGrowthIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.investmentGoal.gapAmount,
            target: fnaState.investmentGoal.targetAmount,
            coverage: fnaState.investmentGoal.coverageAmount,
          });
          break;
        case 'SAVINGS':
          savingsItems.push({
            id: 'SAVINGS',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.SAVINGS')
              : t('fna:lifeStage.concern.SAVINGS.mobile'),
            icon: <MoneyBagIncrementIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.savingsGoal.gapAmount,
            target: fnaState.savingsGoal.targetAmount,
            coverage: fnaState.savingsGoal.coverageAmount,
          });
          break;
        case 'INCOME_PROTECTION':
          protectionItems.push({
            id: 'INCOME_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.INCOME_PROTECTION')
              : t('fna:lifeStage.concern.INCOME_PROTECTION.mobile'),
            icon: <MoneyBagIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.incomeProtectionGoal.gapAmount,
            target: fnaState.incomeProtectionGoal.targetAmount,
            coverage: fnaState.incomeProtectionGoal.coverageAmount,
          });
          break;
        case 'HEALTH_PROTECTION':
          protectionItems.push({
            id: 'HEALTH_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.HEALTH_PROTECTION')
              : t('fna:lifeStage.concern.HEALTH_PROTECTION.mobile'),
            icon: <MedicineIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.healthProtectionGoal.gapAmount,
            target: fnaState.healthProtectionGoal.targetAmount,
            coverage: fnaState.healthProtectionGoal.coverageAmount,
          });
          break;
        case 'LOAN_PROTECTION':
          protectionItems.push({
            id: 'LOAN_PROTECTION',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.LOAN_PROTECTION')
              : t('fna:lifeStage.concern.LOAN_PROTECTION.mobile'),
            icon: <MoneyUnderUmbrellaIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.loanCoverageGoal.targetAmount,
            target: fnaState.loanCoverageGoal.targetAmount,
            coverage: fnaState.loanCoverageGoal.coverageAmount,
          });
          break;
        case 'LEGACY_PLANNING':
          protectionItems.push({
            id: 'LEGACY_PLANNING',
            title: isTabletMode
              ? t('fna:lifeStage.concern.recommendation.LEGACY_PLANNING')
              : t('fna:lifeStage.concern.LEGACY_PLANNING.mobile'),
            icon: <NuturingFinanceIcon width={sizes[6]} height={sizes[6]} />,
            value: fnaState.legacyPlanningGoal.gapAmount,
            target: fnaState.legacyPlanningGoal.targetAmount,
            coverage: fnaState.legacyPlanningGoal.coverageAmount,
          });
          break;
      }
    });

    const savings = {
      totalTarget: 0,
      totalCoverage: 0,
      totalGap: 0,
      items: savingsItems.filter(item =>
        country === 'ph' && item.id === 'RETIREMENT'
          ? fnaState.lifeJourney.dob &&
            calculateAge(fnaState.lifeJourney.dob) < MAX_AGE_RETIREMENT
          : true,
      ),
    };
    goalsByNeed['SAVINGS'].forEach(goalType => {
      const goal = fnaState[goalType];
      if (goalType) {
        savings.totalTarget += goal.targetAmount || 0;
        savings.totalCoverage += goal.coverageAmount || 0;
        savings.totalGap += goal.gapAmount || 0;
      }
    });

    const protection = {
      totalTarget: 0,
      totalCoverage: 0,
      totalGap: 0,
      items: protectionItems,
    };
    goalsByNeed['PROTECTION'].forEach(goalType => {
      const goal = fnaState[goalType];
      if (goalType) {
        protection.totalTarget += goal.targetAmount || 0;
        protection.totalCoverage += goal.coverageAmount || 0;
        protection.totalGap += goal.gapAmount || 0;
      }
    });

    return {
      savings,
      protection,
    };
  }, [fnaState, isTabletMode, sizes, t]);

  return (
    <DeviceBasedRendering
      tablet={<NeedsSummaryTablet savings={savings} protection={protection} />}
      phone={<NeedsSummaryPhone savings={savings} protection={protection} />}
    />
  );
}
