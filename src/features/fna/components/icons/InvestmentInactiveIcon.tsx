import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const InvestmentInactiveIcon = (props: SvgPictogramProps) => (
  <Svg
    width={props.width || props.size || 40}
    height={props.height || props.size || 40}
    viewBox="0 0 40 40"
    fill="none">
    <Path
      d="M16.932 31.243C17.2463 31.243 17.4749 31.5001 17.4749 31.7858C17.4749 32.3573 17.9606 32.843 18.532 32.843C19.1035 32.843 19.5892 32.3573 19.5892 31.7858V6.58583C19.5892 6.27155 19.8463 6.04297 20.132 6.04297C20.4178 6.04297 20.6749 6.30012 20.6749 6.58583V31.7858C20.6749 32.9858 19.7035 33.9573 18.5035 33.9573C17.3035 33.9573 16.332 32.9858 16.332 31.7858C16.3606 31.4715 16.6178 31.243 16.932 31.243Z"
      fill="#DBDFE1"
    />
    <Path
      d="M20.0742 8.41431C23.7314 8.41431 26.6742 16.7286 26.6742 20.3857C26.6742 20.3857 28.3314 18.6714 31.3028 18.6714C32.2742 18.6714 32.9314 18.8429 33.3599 19.0143C33.6171 19.1286 33.9028 18.8429 33.7885 18.5857C32.8171 15.9286 29.1028 8.41431 20.0742 8.41431Z"
      fill="#DBDFE1"
    />
    <Path
      d="M20.074 8.41431C11.1597 8.41431 7.4454 15.7286 6.35968 18.5C6.2454 18.8143 6.53111 19.1286 6.8454 18.9857C7.27397 18.8143 7.9311 18.6714 8.81682 18.6714C11.8168 18.6714 13.4454 20.3857 13.4454 20.3857C13.474 16.7572 16.4454 8.41431 20.074 8.41431Z"
      fill="#DBDFE1"
    />
    <Path
      d="M20.0746 8.41431C16.4175 8.41431 13.4746 16.7286 13.4746 20.3857C13.4746 20.3857 15.3603 19.1 20.0746 19.1C24.7889 19.1 26.6746 20.3857 26.6746 20.3857C26.6746 16.7572 23.7318 8.41431 20.0746 8.41431Z"
      fill="#EDEFF0"
    />
    <Path
      d="M10.7304 31.3286C13.2393 31.3286 15.2732 29.2947 15.2732 26.7858C15.2732 24.2768 13.2393 22.2429 10.7304 22.2429C8.22141 22.2429 6.1875 24.2768 6.1875 26.7858C6.1875 29.2947 8.22141 31.3286 10.7304 31.3286Z"
      fill="#DBDFE1"
    />
    <Path
      d="M10.5299 29.4429V28.8144C9.70128 28.7001 9.30128 28.1001 9.24414 27.5858L9.98701 27.4144C10.0156 27.7858 10.2727 28.1572 10.8442 28.1572C11.2442 28.1572 11.4727 27.9572 11.4727 27.7001C11.4727 27.5001 11.3299 27.3287 11.0156 27.2715L10.4727 27.1572C9.81557 27.0144 9.41558 26.6144 9.41558 26.0144C9.41558 25.3858 9.90129 24.9001 10.5584 24.8144V24.1572H11.187V24.8144C11.8727 24.9287 12.187 25.4144 12.2727 25.8144L11.5299 26.0144C11.5013 25.8144 11.3299 25.4429 10.8442 25.4429C10.4727 25.4429 10.2441 25.6715 10.2441 25.9001C10.2441 26.1001 10.387 26.2429 10.6727 26.3001L11.2156 26.4144C11.987 26.5858 12.3299 27.0429 12.3299 27.5858C12.3299 28.1287 11.9299 28.6429 11.2156 28.7572V29.4144H10.5299V29.4429Z"
      fill="white"
    />
    <Path
      d="M28.616 28.1572C28.5303 28.9001 27.9303 29.4715 27.1874 29.5572C26.0446 29.7001 25.1303 28.7001 25.3874 27.5572C25.5303 26.9572 26.016 26.4715 26.616 26.3286C27.7588 26.1001 28.7588 27.0429 28.616 28.1572Z"
      fill="#EDEFF0"
    />
    <Path
      d="M29.6438 22.6429C29.7581 23.1857 29.2724 23.6714 28.7295 23.5571C28.4152 23.5 28.1867 23.2429 28.1295 22.9571C28.0438 22.4143 28.5009 21.9571 29.0438 22.0429C29.3295 22.1 29.5867 22.3286 29.6438 22.6429Z"
      fill="#EDEFF0"
    />
  </Svg>
);
export default InvestmentInactiveIcon;
