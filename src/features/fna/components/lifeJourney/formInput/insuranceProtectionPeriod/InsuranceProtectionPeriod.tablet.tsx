import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import FnaInput from '../../../common/FnaInput';
import { InternalInsuranceProtectionPeriodProps } from './InsuranceProtectionPeriod';
import Autocomplete from 'components/Autocomplete';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const InsuranceProtectionPeriodTablet = memo(
  ({
    value,
    selectedItem,
    data,
    onDone,
    readonly,
  }: InternalInsuranceProtectionPeriodProps) => {
    const { t } = useTranslation(['fna']);
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:lifeStage.detail.question.insuranceProtectionPeriod')}
            value={selectedItem?.label}
          />
        ) : (
          <Autocomplete
            label={t('fna:lifeStage.detail.question.insuranceProtectionPeriod')}
            renderInput={props => (
              <FnaInput
                {...props}
                value={value}
                placeholder={t(
                  'fna:lifeStage.detail.question.insuranceProtectionPeriod.placeholder',
                )}
                highlight={shouldHighlight && !value}
              />
            )}
            style={{ flex: 1 }}
            data={data}
            keyExtractor={item => item.label}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            value={selectedItem?.value}
            onChange={value => {
              const selectedItem = data.find(i => i.value === value);
              if (selectedItem) {
                onDone?.(selectedItem);
              }
            }}
          />
        )}
      </>
    );
  },
);

export default InsuranceProtectionPeriodTablet;
