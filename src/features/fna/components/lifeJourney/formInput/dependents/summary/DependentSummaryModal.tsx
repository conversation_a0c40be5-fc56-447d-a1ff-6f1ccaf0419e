import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { Dependent } from 'types/case';
import DependentSummaryModalTablet from './DependentSummaryModal.tablet';

export interface DependentSummaryProps {
  visible: boolean;
  onDismiss: () => void;
  data: Dependent[];
}

export default function DependentSummaryModal(props: DependentSummaryProps) {
  return (
    <DeviceBasedRendering
      phone={null}
      tablet={<DependentSummaryModalTablet {...props} />}
    />
  );
}
