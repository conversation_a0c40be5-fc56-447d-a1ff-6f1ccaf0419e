import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import ProductCurrencyTablet from './ProductCurrency.tablet';
import { ProductCurrencyEnum } from 'types/products';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
export interface InternalProductCurrencyProps {
  productCurrency: ProductCurrencyEnum | null;
  setProductCurrency?: (type: ProductCurrencyEnum) => void;
  resetProductCurrency?: () => void;
  shouldShowRequiredBeforeSavingError?: boolean;
}

const ProductCurrency = memo(({ readonly }: ReadonlyProps) => {
  const {
    productCurrency,
    updateProductCurrency,
    shouldShowRequiredBeforeSavingError,
    setRequiredBeforeSavingError,
  } = useFnaStore(
    state => ({
      productCurrency: state.lifeJourney.productCurrency,
      updateProductCurrency: state.updateProductCurrency,
      shouldShowRequiredBeforeSavingError:
        state.shouldShowRequiredBeforeSavingError,
      setRequiredBeforeSavingError: state.setRequiredBeforeSavingError,
    }),
    shallow,
  );

  const setProductCurrency = useCallback(
    (type: ProductCurrencyEnum) => {
      setRequiredBeforeSavingError(false);
      updateProductCurrency(type);
    },
    [updateProductCurrency, setRequiredBeforeSavingError],
  );

  const resetProductCurrency = useCallback(() => {
    setRequiredBeforeSavingError(false);
    updateProductCurrency(null);
  }, [updateProductCurrency, setRequiredBeforeSavingError]);

  return (
    <ProductCurrencyTablet
      productCurrency={productCurrency}
      setProductCurrency={setProductCurrency}
      resetProductCurrency={resetProductCurrency}
      shouldShowRequiredBeforeSavingError={shouldShowRequiredBeforeSavingError}
      readonly={readonly}
    />
  );
});

export default ProductCurrency;
