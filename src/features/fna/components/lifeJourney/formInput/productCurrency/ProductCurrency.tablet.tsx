import { memo } from 'react';
import { InternalProductCurrencyProps } from './ProductCurrency';
import { Column, Picker, Row, SmallLabel } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { ProductCurrencyEnum } from 'types/products';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';
import ReadonlyInput from '../readonly/ReadonlyInput';

const ProductCurrencyTablet = memo(
  ({
    productCurrency,
    setProductCurrency,
    readonly,
  }: InternalProductCurrencyProps & ReadonlyProps) => {
    const { t } = useTranslation(['fna']);
    const { space, colors } = useTheme();
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:productPreference')}
            value={
              productCurrency === ProductCurrencyEnum.RUPIAH
                ? t('fna:productCurrencyRupiah')
                : t('fna:productCurrencyForeignCurrency')
            }
          />
        ) : (
          <Column flex={1} gap={space[2]}>
            <SmallLabel fontWeight="medium" color={colors.placeholder}>
              {t('fna:productCurrency')}
            </SmallLabel>
            <Row flex={1} gap={space[3]}>
              <Picker
                type="chip"
                size="large"
                highlight={shouldHighlight && !productCurrency}
                items={[
                  {
                    value: ProductCurrencyEnum.RUPIAH,
                    label: t('fna:productCurrencyRupiah'),
                  },
                  {
                    value: ProductCurrencyEnum.FOREIGN_CURRENCY,
                    label: t('fna:productCurrencyForeignCurrency'),
                  },
                ]}
                value={productCurrency as ProductCurrencyEnum}
                onChange={type => {
                  if (type) {
                    setProductCurrency?.(type as ProductCurrencyEnum);
                  }
                }}
                textPosition="right"
              />
            </Row>
          </Column>
        )}
      </>
    );
  },
);

export default ProductCurrencyTablet;
