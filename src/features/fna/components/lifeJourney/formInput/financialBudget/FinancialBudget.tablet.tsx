import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import FnaInput from '../../../common/FnaInput';
import { InternalFinancialBudgetProps } from './FinancialBudget';
import Autocomplete from 'components/Autocomplete';
import ReadonlyInput from '../readonly/ReadonlyInput';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';

const FinancialBudgetTablet = memo(
  ({
    value,
    selectedItem,
    data,
    onDone,
    readonly,
  }: InternalFinancialBudgetProps) => {
    const { t } = useTranslation(['fna']);
    const shouldHighlight = useFnaStore(state => state.shouldHighlight);

    return (
      <>
        {readonly ? (
          <ReadonlyInput
            label={t('fna:lifeStage.detail.question.financialBudget.part1')}
            value={selectedItem?.label}
          />
        ) : (
          <Autocomplete
            label={t('fna:lifeStage.detail.question.financialBudget.part1')}
            hint={t('fna:lifeStage.detail.question.financialBudget.part2')}
            renderInput={props => (
              <FnaInput
                {...props}
                value={value}
                placeholder={t('fna:select')}
                highlight={shouldHighlight && !value}
              />
            )}
            style={{ flex: 1 }}
            data={data}
            keyExtractor={item => item.label}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            value={selectedItem?.value}
            onChange={value => {
              const selectedItem = data.find(i => i.value === value);
              if (selectedItem) {
                onDone?.(selectedItem);
              }
            }}
          />
        )}
      </>
    );
  },
);

export default FinancialBudgetTablet;
