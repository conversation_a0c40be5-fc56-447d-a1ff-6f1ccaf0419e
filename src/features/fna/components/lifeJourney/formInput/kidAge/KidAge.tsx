import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { memo, useCallback } from 'react';
import { shallow } from 'zustand/shallow';
import KidAgePhone from './KidAge.phone';
import KidAgeTablet from './KidAge.tablet';
import { NumberSequence } from 'types';

export interface InternalKidAgeProps {
  label?: string;
  kidIndex: NumberSequence<1, 5>;
  value: string;
  onChange: (value: string) => void;
}

interface Props {
  label?: string;
  kidIndex: NumberSequence<1, 5>;
}

const KidAge = memo(({ kidIndex, label }: Props) => {
  const { lifeJourney, updateKidsAge } = useFnaStore(
    state => ({
      lifeJourney: state.lifeJourney,
      updateKidsAge: state.updateKidsAge,
    }),
    shallow,
  );

  const onChange = useCallback(
    (value: string) => {
      if (value.length === 0) {
        updateKidsAge(0, kidIndex);
        return;
      }
      const ageNumber = parseInt(value, 10);
      updateKidsAge(ageNumber, kidIndex);
    },
    [kidIndex, updateKidsAge],
  );

  return (
    <DeviceBasedRendering
      phone={
        <KidAgePhone
          kidIndex={kidIndex}
          value={String(lifeJourney.kidsAge[kidIndex])}
          onChange={onChange}
        />
      }
      tablet={
        <KidAgeTablet
          label={label}
          kidIndex={kidIndex}
          value={String(lifeJourney.kidsAge[kidIndex])}
          onChange={onChange}
        />
      }
    />
  );
});

export default KidAge;
