import { useTheme } from '@emotion/react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { Box, DropdownPanel, Icon, LargeLabel, Row } from 'cube-ui-components';
import { checkEducationCompletion } from 'features/fna/utils/helper/checkGoalCompletion';
import {
  initialEducationKidGoal,
  useFnaStore,
  AdviceType as AdviceTypeKey,
} from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { cloneDeep } from 'utils/helper/objectUtil';
import { shallow } from 'zustand/shallow';
import NumberOfKidsPhone from './NumberOfKids.phone';
import NumberOfKidsTablet from './NumberOfKids.tablet';
import { ReadonlyProps } from '../../tablet/LifeJourneySummary.tablet';

export interface InternalNumberOfKids extends ReadonlyProps {
  data: number[];
  onDone: (numberOfKids: number) => void;
  orderNumber?: number;
}

const numberOfKidsData = Array.from({ length: 5 }, (_, index) => index + 1);

const NumberOfKids = memo(
  ({ readonly, orderNumber }: { orderNumber?: number } & ReadonlyProps) => {
    const {
      updateNumberOfKids,
      storeEducationGoal,
      updateEducationGoal,
      adviceType,
      concerns,
    } = useFnaStore(
      state => ({
        updateNumberOfKids: state.updateNumberOfKids,
        storeEducationGoal: state.educationGoal,
        updateEducationGoal: state.updateEducationGoal,
        adviceType: state.adviceType,
        concerns: state.lifeJourney.concerns,
      }),
      shallow,
    );

    const updateNumberOfKidsForEducation = useCallback(
      (numberOfKids: number) => {
        const goals = storeEducationGoal.goals;
        const newGoals = [];
        for (let i = 0; i < numberOfKids; i++) {
          if (typeof goals[i] !== 'undefined') {
            newGoals.push(goals[i]);
          } else {
            const initial = cloneDeep(initialEducationKidGoal);
            newGoals.push(initial);
          }
        }
        updateEducationGoal({
          numberOfKids: numberOfKids,
          goals: newGoals,
        });
      },
      [storeEducationGoal.goals, updateEducationGoal],
    );

    const onDone = useCallback(
      (item: number) => {
        updateNumberOfKids(item);
        if (
          !checkEducationCompletion(storeEducationGoal, adviceType, concerns)
        ) {
          updateNumberOfKidsForEducation(item);
        }
      },
      [
        adviceType,
        concerns,
        storeEducationGoal,
        updateNumberOfKids,
        updateNumberOfKidsForEducation,
      ],
    );

    return (
      <DeviceBasedRendering
        phone={
          <NumberOfKidsPhone
            data={numberOfKidsData}
            onDone={onDone}
            readonly={readonly}
            orderNumber={orderNumber}
          />
        }
        tablet={
          <NumberOfKidsTablet
            data={numberOfKidsData}
            onDone={onDone}
            readonly={readonly}
          />
        }
      />
    );
  },
);

export default NumberOfKids;
