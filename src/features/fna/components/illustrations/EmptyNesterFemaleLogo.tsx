import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { <PERSON>, G, Defs, ClipPath } from 'react-native-svg';

const EmptyNesterFemaleLogo = () => (
  <Svg width={116} height={80} fill="none">
    <Path fill="#FEF9F4" d="M127-5H-4v93h131V-5Z" />
    <Path
      fill="#F8E3D2"
      d="M109.398 64.73S98.808 24.908 87.78 24.908c-11.029 0-14.937 22.139-22.034 22.139-7.097 0-7.76-6.46-15.277-6.46-7.518 0-22.072 23.703-28.735 25.037l87.669-.894h-.005Z"
    />
    <Path
      fill="#F89F60"
      d="M49.134 65.118h-68.63L2.529 40.014l5.183-5.907 2.651-3.022 1.45-1.65a4.002 4.002 0 0 1 6.016 0l1.578 1.801 2.013 2.29 5.789 6.597 21.93 24.995h-.005Z"
    />
    <Path
      fill="#fff"
      d="M11.127 34.11c0 1.15-.307 2.233-.845 3.16a6.316 6.316 0 0 1-7.755 2.743l5.184-5.907 2.65-3.022a6.32 6.32 0 0 1 .766 3.022v.005ZM27.202 40.121a5.76 5.76 0 0 1-2.57.605c-2.5 0-4.645-1.593-5.562-3.864a6.553 6.553 0 0 1-.472-2.464c0-1.154.293-2.237.808-3.164l2.013 2.29 5.788 6.597h-.005Z"
    />
    <Path
      fill="#fff"
      d="M21.415 33.524c-.557 1.32-1.365 2.464-2.344 3.334-1.299 1.169-2.906 1.854-4.64 1.854-1.526 0-2.948-.53-4.153-1.447-1.045-.794-1.928-1.882-2.571-3.164l2.65-3.022 1.452-1.65a4.002 4.002 0 0 1 6.015 0l1.578 1.801 2.013 2.29v.004Z"
    />
    <Path
      fill="#F89F60"
      d="M137.721 65.32H69.965c5.325-6.63 12.153-13.749 25.408-11.535 14.157 2.365 16.817-21.969 27.26-21.969 11.232 0 15.083 33.504 15.083 33.504h.005Z"
    />
    <Path fill="#F3BB90" d="M213.456 64.842h-329.331V88h329.331V64.842Z" />
    <Path
      fill="#F89F60"
      d="M23.467 75.451H2.425a.92.92 0 1 0 0 1.84h21.042a.92.92 0 1 0 0-1.84ZM59.428 75.451H38.386a.92.92 0 1 0 0 1.84h21.042a.92.92 0 1 0 0-1.84ZM95.389 75.451H74.347a.92.92 0 1 0 0 1.84H95.39a.92.92 0 1 0 0-1.84ZM134.159 75.285h-21.042a.921.921 0 1 0 0 1.84h21.042a.92.92 0 1 0 0-1.84Z"
    />
    <Path fill="#fff" d="M66.546 68.814h-1.694v40.601l1.443-.149.251-40.452Z" />
    <G clipPath="url(#a)">
      <Path
        fill="#849D98"
        d="M59 30.777c-1.41-.038-2.86.267-4.195-.191-1.145-.382-2.06-1.375-2.403-2.522-.344-1.146-.115-2.445.61-3.438.534-.726 1.335-1.261 1.869-1.987 1.182-1.605.915-3.783 1.45-5.693.8-2.942 3.737-5.196 6.789-5.196 1.182 0 2.326.306 3.47.688 1.526.535 3.09 1.375 3.739 2.865.686 1.605.076 3.515.763 5.12.572 1.375 1.983 2.254 2.67 3.553.877 1.681.457 3.744-.458 5.387-.534.917-1.22 1.758-2.098 2.33-1.755 1.109-4.043.918-6.065.421-2.021-.496-4.043-1.26-6.14-1.337Z"
      />
      <Path
        fill="#FED241"
        d="M62.739 33.375s6.789 2.063 9.23 4.394c0 0 .916 14.671.801 19.867 0 0-2.174 7.985-4.615 12.838l-5.53-2.56.8-2.178-5.988-.573 2.06 2.292-4.387 3.706-4.958-5.425-3.662-1.643c-1.144-.497-1.755-1.796-1.411-2.98l.686-2.407s-.915-18.263 3.128-22.733c0-.038 7.476-2.942 13.846-2.598Z"
      />
      <Path
        fill="#FBE69D"
        d="M68.422 64.78s.076 12.112-.077 17.843c-.152 4.432-.42 15.13-.534 19.715-.038 1.223-.953 2.254-2.212 2.369-3.585.382-11.138.955-18.88-.115-1.145-.153-2.06-1.146-2.098-2.292-.42-7.489-1.755-33.546-.572-36.296 1.373-3.21 2.25-4.662 2.25-4.662l22.123 3.439Z"
      />
      <Path
        fill="#FBE2D0"
        d="m58.58 67.302 2.594 3.21 1.22 2.33 1.908-5.196 3.013 1.567-2.86 5.73.343 5.885-.534-.612-.19.956-.611-.765-.534.612-.61-1.567-.61 2.178s-.306 1.299-.65 1.299c-.342 0-.419-2.827-.419-2.827s-.61 2.445-.991 2.483c-.306.038-.267-1.108-.267-1.108s-.534-.535.953-4.355c0 0-1.983-3.21-1.83-4.929l-2.938-3.553 3.014-1.338ZM59.801 25.39s-1.792 7.336-2.021 7.909c-.23.573-1.602.993-1.602.993s4.157 4.47 9.688.917c0 0-1.755-1.07-1.945-1.49-.191-.42.42-5.464.42-5.464L59.8 25.39Z"
      />
      <Path
        fill="#FBE2D0"
        d="M69.108 20.08c1.03 6.532-2.098 9.55-3.547 9.742-1.908.267-5.379-.994-6.332-3.86 0 0-2.365.116-2.632-1.183-.458-2.37 1.488-1.223 1.488-1.223s-3.7-8.1 2.708-10.24c4.234-1.375 7.361.497 8.315 6.763Z"
      />
      <Path
        fill="#849D98"
        d="M58.123 23.556s.42.267.458.267c.038 0 3.47-5.157 4.615-7.756l6.293 4.394s-.343-4.814-2.136-5.96c-1.792-1.184-2.784-2.75-6.217-1.49-3.433 1.223-4.806 4.24-4.234 7.106.61 2.828 1.22 3.439 1.22 3.439Z"
      />
      <Path
        fill="#fff"
        d="M59.115 25.619a.643.643 0 0 0-.648-.65.643.643 0 0 0-.649.65c0 .344.267.65.649.65a.669.669 0 0 0 .648-.65Z"
      />
      <Path
        fill="#F58535"
        d="M57.437 65.163s-.229-12.494-.229-14.824c0-1.987-1.526-13.029-1.945-16.2-3.586.764-6.332 1.834-6.332 1.834-4.043 4.508-3.128 22.733-3.128 22.733l-1.03 3.63a1.018 1.018 0 0 0 .573 1.222l4.844 2.178 4.31 4.7c.381.382.992.458 1.411.076l2.86-2.407c.42-.382.497-1.032.115-1.452l-1.45-1.49ZM66.4 34.712l-2.975 31.024-.496 1.337c-.19.497.038 1.032.534 1.261l3.815 1.758c.495.229 1.106 0 1.334-.497 2.25-4.89 4.196-11.92 4.196-11.92.114-5.197-.8-19.868-.8-19.868-1.26-1.184-3.586-2.293-5.608-3.095Z"
      />
      <Path
        fill="#183028"
        d="M61.861 22.524c.039.268.23.497.42.459.19-.038.343-.268.305-.535-.038-.267-.229-.497-.42-.459-.19.039-.343.268-.305.535ZM66.935 22.295c.038.268.228.497.42.459.19-.039.343-.268.304-.535-.038-.268-.229-.497-.42-.459-.19 0-.343.23-.304.535Z"
      />
      <Path
        fill="#E77824"
        d="M65.218 25.16c-.038 0-.115 0-.153-.076-.038-.076 0-.153.038-.19.305-.192.458-.383.458-.612 0-.306-.305-.611-.496-.688-.42-.19-.458-1.49-.458-2.063 0-.076.077-.153.153-.153s.152.077.152.153c0 .917.153 1.758.306 1.834.267.115.648.497.648.917 0 .23-.076.611-.572.879h-.076Z"
      />
      <Path
        fill="#E77825"
        d="M64.875 26.613c.19 0 .343 0 .496-.039.534-.114.877-.382.915-.382.038-.038.076-.114 0-.153-.038-.038-.153-.038-.19 0-.039.039-1.26.917-2.556-.229-.038-.038-.153-.038-.191 0a.117.117 0 0 0 0 .153c.534.497 1.106.65 1.526.65Z"
      />
      <Path
        fill="#E77825"
        d="M64.684 27.338c-.916-.076-1.297-.916-1.373-1.375 0-.076.038-.115.114-.115.076 0 .*************** 0 .038.228 1.146 1.22 1.146.038 0 .458.077.84-.153.228-.152.38-.42.419-.764 0-.076.076-.114.152-.114.077 0 .***************-.038.42-.23.765-.534.956-.458.267-.992.19-.992.19-.038-.038-.076-.038-.114-.038ZM65.027 27.835c.267 0 .42-.076.42-.076.075-.038.075-.115.037-.153-.038-.038-.152-.077-.19-.038 0 0-.23.114-.649-.039-.076-.038-.152 0-.19.039-.039.076 0 .**************.077.381.115.496.115Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M32-4h54v140H32z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default EmptyNesterFemaleLogo;
