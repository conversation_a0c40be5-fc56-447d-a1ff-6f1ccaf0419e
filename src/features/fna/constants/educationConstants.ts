import { subYears } from 'date-fns';
import { TF<PERSON><PERSON><PERSON><PERSON> } from 'i18next';

export const CHILD_AGE = [
  '0',
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
];

export const ANNUAL_INTEREST_RATE = 0.1;

export const UNIVERSITY_OF_CHOICE: {
  label: TFunc<PERSON>ey<['fna']>;
  shortLabel: TFuncKey<['fna']>;
  value: number;
}[] = [
  {
    label: 'fna:education.stateUniversity',
    shortLabel: 'fna:education.stateUniversity.short',
    value: 0,
  },
  {
    label: 'fna:education.privateUniversity',
    shortLabel: 'fna:education.privateUniversity.short',
    value: 1,
  },
  {
    label: 'fna:education.premierUniversity',
    shortLabel: 'fna:education.premierUniversity.short',
    value: 2,
  },
];

export const COST_OF_UNIVERSITY = [50000, 160000, 300000];

export const YEARS_FOR_COLLEGE = [2, 3, 4, 5];

export const EDUKASYON_URL = 'https://www.edukasyon.ph/';

export const MAX_EDUCATION_DATE_OF_BIRTH = new Date();
export const MIN_EDUCATION_DATE_OF_BIRTH = subYears(new Date(), 17);

export const KIDS_ORDINAL_LIST = [
  'fna:savingsGoals.education.first',
  'fna:savingsGoals.education.second',
  'fna:savingsGoals.education.third',
  'fna:savingsGoals.education.fourth',
  'fna:savingsGoals.education.fifth',
  'fna:savingsGoals.education.sixth',
  'fna:savingsGoals.education.seventh',
  'fna:savingsGoals.education.eighth',
  'fna:savingsGoals.education.ninth',
  'fna:savingsGoals.education.tenth',
];

export const PH_MAX_KIDS = 5;
export const PH_MAX_PARTNER = 1;
export const MY_MAX_DEPENDENTS = 10;