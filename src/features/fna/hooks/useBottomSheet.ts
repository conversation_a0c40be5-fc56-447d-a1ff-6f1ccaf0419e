import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProps,
} from '@gorhom/bottom-sheet';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { BackHandler } from 'react-native';

export const useBottomSheet = (): Omit<
  BottomSheetModalProps,
  'snapPoints' | 'children'
> & {
  ref: React.RefCallback<BottomSheetModal>;
  bottomSheetRef: React.RefObject<BottomSheetModal>;
} => {
  const { space, colors, borderRadius } = useTheme();
  const bottomSheetRef = useRef<BottomSheetModal | null>(null);

  const keyboardShown = useKeyboardShown();
  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return React.createElement(BottomSheetBackdrop, {
      ...props,
      appearsOnIndex: 0,
      disappearsOnIndex: -1,
    });
  }, []);
  const sheetStyle = useMemo(
    () => ({ padding: space[4], paddingTop: 0 }),
    [space],
  );
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  useEffect(() => {
    const backAction = () => {
      bottomSheetRef?.current?.dismiss();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);

  return {
    ref: ref => {
      ref?.present();
      bottomSheetRef.current = ref;
    },
    bottomSheetRef,
    handleIndicatorStyle,
    handleStyle,
    backgroundStyle,
    backdropComponent: renderBackdrop,
    keyboardBehavior: 'extend',
    keyboardBlurBehavior: 'restore',
    enableHandlePanningGesture: !keyboardShown,
    enableContentPanningGesture: !keyboardShown,
    enablePanDownToClose: true,
    style: sheetStyle,
  };
};
