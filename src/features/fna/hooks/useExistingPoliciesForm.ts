import { useForm } from 'react-hook-form';
import { RefObject, useCallback, useEffect } from 'react';
import * as Crypto from 'expo-crypto';

import { useYupResolver } from 'utils/validation/useYupResolver';
import { existingPoliciesSchema } from 'features/fna/utils/validation/existingPoliciesValidation';
import {
  defaultPolicies,
  getDefaultPolicy,
  InternalExistingPoliciesProps,
} from '../components/lifeJourney/formInput/existingPolicies/ExistingPolicies';
import { ExistingPolicy } from 'features/fna/utils/store/fnaStore';
import { ScrollView } from 'react-native';

const useExistingPoliciesForm = ({
  existingPolicies,
  visible,
  onDone,
  scrollRef,
}: InternalExistingPoliciesProps & {
  scrollRef: RefObject<ScrollView>;
}) => {
  const resolver = useYupResolver(existingPoliciesSchema);

  const {
    control,
    formState: { isValid },
    handleSubmit,
    reset,
    trigger,
    setValue,
    watch,
  } = useForm({
    mode: 'onBlur',
    resolver,
    defaultValues: defaultPolicies,
  });

  const policies = watch('policies');

  useEffect(() => {
    if (visible && existingPolicies?.length) {
      reset({
        policies: existingPolicies.map(p => ({
          ...p,
          premiumBenefits: p.premiumBenefits.map(benefit => ({ ...benefit })),
          id: Crypto.randomUUID(),
        })),
      });
    }
  }, [existingPolicies, reset, visible]);

  const onSubmit = useCallback(() => {
    handleSubmit(({ policies }) => {
      onDone(policies.filter(Boolean));
    })();
  }, [handleSubmit, onDone]);

  const onDelete = useCallback(
    (id?: string) => {
      setValue(
        'policies',
        policies.filter(currentPolicy => currentPolicy.id !== id),
      );
    },
    [policies, setValue],
  );

  const onAdd = useCallback(() => {
    setValue(
      'policies',
      policies.concat({
        ...(getDefaultPolicy() as unknown as ExistingPolicy),
        id: Crypto.randomUUID(),
      }),
    );
    setTimeout(
      () =>
        scrollRef.current?.scrollToEnd({
          animated: true,
        }),
      500,
    );
  }, [policies, setValue, scrollRef]);

  return {
    control,
    formState: { isValid },
    handleSubmit,
    reset,
    setValue,
    watch,
    trigger,
    onSubmit,
    onDelete,
    onAdd
  };
};

export default useExistingPoliciesForm;
