import { Al<PERSON>, ViewStyle } from 'react-native';
import React from 'react';
// import Button from '@Framework/common/components/Button';
import * as LocalAuthentication from 'expo-local-authentication';
// import Colors from '@Framework/common/constants/Colors';
// import { FaceIDSVG, FingerPrintSVG } from '../Icon';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Row, Typography } from 'cube-ui-components';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import useBoundStore from 'hooks/useBoundStore';

type Props = {
  onSuccess: (token: string) => void;
  enableManualInput: () => void;
  refreshToken: string | null;
  visible: boolean;
  isLoading?: boolean;
  customStyle?: ViewStyle;
};

export default function BiometricLoginButton({
  onSuccess,
  enableManualInput,
  refreshToken,
  visible,
  isLoading,
  customStyle = {},
}: Props) {
  const { colors, space } = useTheme();

  const { t } = useTranslation('common');
  //   const biometricInfo = useAppSelector(selectBiometricInfo);
  // const supported = [LocalAuthentication.AuthenticationType.FINGERPRINT];
  const biometricInfo = useBoundStore(state => state.auth.bioMetricInfo);
  const { supported } = biometricInfo;
  const supportMultiple = supported.length > 1;
  const type = supported[0];
  const label = supportMultiple
    ? t('login.biometric.biometric')
    : type === LocalAuthentication.AuthenticationType.FINGERPRINT
    ? t('login.biometric.touchID')
    : type === LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION
    ? t('login.biometric.faceID')
    : type === LocalAuthentication.AuthenticationType.IRIS
    ? t('login.biometric.iris')
    : t('login.biometric.biometric');

  const bioMetricLogin = async () => {
    console.log(
      '🚀 ~ file: BiometricButton.tsx:74 ~ bioMetricLogin ~ bioMetricLogin to be confirmed',
    );

    const authRes = await LocalAuthentication.authenticateAsync({
      disableDeviceFallback: true,
      requireConfirmation: true,
      // fallbackLabel: t('login.biometric.prompt.fallbackLabel'),
      cancelLabel: t('login.biometric.prompt.cancel'),
    });
    console.log(
      '🚀 ~ file: BiometricButton.tsx:57 ~ bioMetricLogin ~ authRes:',
      authRes,
    );

    if (authRes.success) {
      if (refreshToken) {
        onSuccess(refreshToken);
      } else {
        Alert.alert(t('login.biometric.error.failed'));
      }
    } else {
      if (authRes.error == 'user_cancel') {
        return;
      } else if (authRes.error == 'user_fallback') {
        enableManualInput();
        //   } else if (authRes.error == 'lockout') {
        //     Alert.alert(t('login.biometric.error.failed.lockout'));
        //     enableManualInput();
      } else {
        // Alert.alert(authRes.error);
      }
    }
  };

  if (!visible || !refreshToken) {
    return null;
  }

  return (
    <ButtonForBiometrics onPress={bioMetricLogin} style={customStyle}>
      <Row style={{ gap: space[2] }}>
        {!supportMultiple &&
        supported[0] === LocalAuthentication.AuthenticationType.FINGERPRINT ? (
          <Icon.TouchId />
        ) : (
          <Icon.FaceId />
        )}
        <Typography.LargeLabel fontWeight="bold" color={colors.primary}>
          {t('login.biometric.button', { bioMeticType: label })}
        </Typography.LargeLabel>
      </Row>
    </ButtonForBiometrics>
  );
}

const ButtonForBiometrics = styled(TouchableOpacity)(({ theme }) => ({
  height: theme.space[10],
  justifyContent: 'center',
  alignItems: 'center',
  alignSelf: 'center',
  paddingHorizontal: theme.space[6],
  paddingVertical: theme.space[2],
}));
