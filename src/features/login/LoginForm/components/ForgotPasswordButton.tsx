import { memo, useMemo } from 'react';
import styled from '@emotion/native';

import { Linking } from 'react-native';
import React from 'react';
import { Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { country, eIrisUrl, salesConnectUrl } from 'utils/context';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import { useTranslation } from 'react-i18next';

export const ForgotPasswordButton = memo(function ForgotPasswordButton() {
  const { colors } = useTheme();
  const { t } = useTranslation();

  const { url } = useMemo(() => {
    switch (country) {
      case 'id':
        return {
          url: salesConnectUrl,
        };
      default:
        return {
          url: eIrisUrl,
        };
    }
  }, []);
  return (
    <Button
      onPress={() => {
        Linking.canOpenURL(url)
          .then(supported => supported && Linking.openURL(url))
          .catch(err => console.error('An error occurred', err));
      }}
      hitSlop={HIT_SLOP_SPACE(1)}>
      <Typography.Label
        fontWeight="bold"
        color={colors.palette.fwdAlternativeOrange[100]}>
        {t('login.here')}
      </Typography.Label>
    </Button>
  );
});
export default ForgotPasswordButton;

const Button = styled.TouchableOpacity(() => ({
  display: 'flex',
}));
