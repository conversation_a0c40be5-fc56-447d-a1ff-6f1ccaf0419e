import { useTheme } from '@emotion/react';
import { RankingPercentage, TeamRanking } from 'components/Chart';
import { Icon, Row } from 'cube-ui-components';
import {
  RankingTabsConfigKeys,
  RankingTabsLabelKeys,
  TabConfigItem,
  rankingTabsConfig,
} from 'features/performance/config';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAllPerformance } from 'hooks/useGetPerformance';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, TouchableOpacity, useWindowDimensions } from 'react-native';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function RankChartsInSnapCarousel({
  floatingButton,
  curTab,
  setCurTab,
  isScrollTriggeredByButtons,
  setIsScrollTriggeredByButtons,
  tabsInputConfig = rankingTabsConfig,
}: {
  floatingButton?: React.ReactNode;
  curTab: RankingTabsConfigKeys;
  setCurTab: React.Dispatch<React.SetStateAction<RankingTabsConfigKeys>>;
  isScrollTriggeredByButtons: boolean;
  setIsScrollTriggeredByButtons: React.Dispatch<React.SetStateAction<boolean>>;
  tabsInputConfig?: Array<
    TabConfigItem<RankingTabsConfigKeys, RankingTabsLabelKeys>
  >;
}) {
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const flatListRef =
    useRef<FlatList<(typeof rankingTabsConfig)[number]>>(null);
  const { height, width } = useWindowDimensions();
  const { colors, sizes } = useTheme();
  const { t } = useTranslation('performance');

  const chartHeight = 330;
  // const arrowHeight = sizes[8];
  const arrowHeight = sizes[6];
  const arrowWidth = sizes[6];
  const arrowPadding = sizes[1];

  const tabsLength = tabsInputConfig?.length;
  const activeArrow = colors.secondary;
  const inactiveArrow = colors.palette.fwdGrey[100];

  const isFirstTab = curTab === tabsInputConfig[0].key;
  const isLastTab = curTab === tabsInputConfig[tabsLength - 1].key;

  const currentTabConfigIndex =
    tabsInputConfig.findIndex(tab => tab.key === curTab) ?? 0;

  useEffect(() => {
    console.log(
      '🚀 ~ file: RankChartsInSnapCarousel.tsx:61 ~ useEffect ~ currentTabConfigIndex:',
      currentTabConfigIndex,
    );

    flatListRef.current?.scrollToIndex({
      animated: true,
      index: currentTabConfigIndex,
    });
  }, [curTab]);

  const { data } = useGetAllPerformance();

  return (
    <Row>
      {floatingButton}
      <TouchableOpacity
        onPress={() => {
          const prevTabConfigIndex = currentTabConfigIndex - 1;
          if (prevTabConfigIndex < 0) return;
          setIsScrollTriggeredByButtons(true);
          setCurTab(tabsInputConfig[prevTabConfigIndex].key);
        }}
        style={{
          position: 'absolute',
          left: arrowPadding,
          top: chartHeight / 2 - arrowHeight,
          zIndex: 10,
        }}>
        <Icon.ChevronLeft
          fill={isFirstTab ? inactiveArrow : activeArrow}
          height={arrowHeight}
          width={arrowWidth}
        />
      </TouchableOpacity>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => item.key + index}
        decelerationRate={'fast'}
        ref={flatListRef}
        data={tabsInputConfig}
        bounces={false}
        snapToInterval={width}
        getItemLayout={(data, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        renderItem={({ item, index }) => {
          if (item.chart === 'TeamRanking') {
            return <DirectTeamChart />;
          }
          if (item.chart === 'RankingPercentage') {
            const labelMap: Record<RankingTabsConfigKeys, string> = {
              directTeam: '',
              YTD: 'YTD',
            };
            const asOfDate =
              data?.ranking?.companyAsOfDate == null
                ? '--'
                : dateFormatUtil(data?.ranking?.companyAsOfDate);

            const rankingDataMap: Record<
              // Exclude<, 'directTeam'>,
              RankingTabsConfigKeys,
              { myRanking: number; totalAgent: number }
            > = {
              YTD: {
                myRanking: data?.ranking?.myAPE?.[0]?.apeRanking ?? 0,
                totalAgent: data?.ranking?.totalAgentCount ?? 0,
              },
              // ! Handle directTeam
              directTeam: {
                myRanking: 0,
                totalAgent: 10,
              },
            };

            return (
              <RankingPercentage
                title={t('performance.ranking.chart.wholeRegion.label')}
                style={{ width: width, height: isNarrowScreen ? 280 : 300 }}
                rankingType={labelMap[curTab] ?? ''}
                asOfDate={asOfDate}
                myRanking={
                  rankingDataMap?.[
                    curTab as Exclude<RankingTabsConfigKeys, 'directTeam'>
                  ]?.myRanking ?? 0
                }
                totalRanking={
                  rankingDataMap?.[
                    curTab as Exclude<RankingTabsConfigKeys, 'directTeam'>
                  ]?.totalAgent ?? 0
                }
              />
            );
          }
          return <></>;
        }}
        onScroll={event => {
          if (isScrollTriggeredByButtons) {
            return;
          }
          const { contentOffset } = event.nativeEvent;
          const index = Math.round(contentOffset.x / width);
          setCurTab(tabsInputConfig[index].key);
        }}
        onScrollToIndexFailed={error => {
          console.log(
            '🚀 ~ file: RankChartsInSnapCarousel.tsx:171 ~ error:',
            error,
          );
          console.log('error', error);
        }}
        onScrollEndDrag={() => {
          if (isScrollTriggeredByButtons) {
            setIsScrollTriggeredByButtons(false);
            return;
          }
        }}
      />
      <TouchableOpacity
        disabled={isLastTab}
        onPress={() => {
          const nextTabConfigIndex = currentTabConfigIndex + 1;
          if (nextTabConfigIndex > tabsLength - 1) return;
          setIsScrollTriggeredByButtons(true);
          setCurTab(tabsInputConfig[nextTabConfigIndex].key);
        }}
        style={{
          position: 'absolute',
          right: arrowPadding,
          top: chartHeight / 2 - arrowHeight,
          zIndex: 10,
        }}>
        <Icon.ChevronRight
          fill={isLastTab ? inactiveArrow : activeArrow}
          height={arrowHeight}
          width={arrowWidth}
        />
      </TouchableOpacity>
    </Row>
  );
}

const DirectTeamChart = () => {
  const { width } = useWindowDimensions();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation('performance');

  const { data } = useGetAllPerformance();

  const teamSize = data?.ranking?.teamSize ?? 0;
  const agentCode = useBoundStore(state => state.auth.agentCode) ?? '';

  const myRanking = data?.ranking?.myAPE?.[0]?.apeRanking ?? 0;
  const myApe = data?.ranking?.myAPE?.[0]?.ape ?? 0;

  const teamAsOfDate =
    data?.ranking?.teamAsOfDate == null
      ? '--'
      : dateFormatUtil(data?.ranking?.teamAsOfDate);

  const topAgentCode = data?.ranking?.no1APE?.[0]?.agentCode ?? '';
  const topAgentApes = data?.ranking?.no1APE?.[0]?.ape ?? 0;

  return (
    <TeamRanking
      style={{
        width: width,
        height: isNarrowScreen ? 280 : 300,
        zIndex: 0,
      }}
      title={t('performance.ranking.chart.directTeam.label')}
      AsOfDate={teamAsOfDate}
      teamData={[]}
      rankingType={'APE'}
      myData={{
        AgentCode: agentCode,
        MM: 0,
        YYYY: 0,
        // SC: undefined,
        // SCRanking: undefined,
        APE: myApe,
        APERanking: myRanking,
        // FYP: 3,
        // FYPRanking: 3,
      }}
      topData={{
        AgentCode: topAgentCode,
        MM: 0,
        YYYY: 0,
        // SC: undefined,
        // SCRanking: undefined,
        APE: topAgentApes,
        // APERanking: 0,
        // FYP: 10000,
        // FYPRanking: 1,
      }}
      teamSize={teamSize}
    />
  );
};
