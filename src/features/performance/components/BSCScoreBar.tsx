import { useTheme } from '@emotion/react';
import { Box, Typography } from 'cube-ui-components';
import {
  bscHappy,
  bscNormal,
  bscSad,
} from 'features/teamManagement/assets/images';
import Svg, { Line } from 'react-native-svg';
import { Image, TouchableOpacity, View } from 'react-native';

export const BSCScoreBar = ({
  width,
  bscScore,
  barPaddingTop,
}: {
  width: number;
  bscScore: number | null;
  barPaddingTop?: number;
}) => {
  //* bscPerformancePercent is range from 0 to 1 here

  const { space, colors, borderRadius, sizes } = useTheme();
  // const width = 270;

  const totalRange = 136;
  const redRange = 75;
  const yellowRange = 25;
  const greenRange = 36;

  const redWidth = (redRange / totalRange) * (width - 20);
  const yellowWidth = (yellowRange / totalRange) * (width - 20);
  const greenWidth = (greenRange / totalRange) * (width - 20);

  const x1 = redWidth + 10;
  const x2 = redWidth + yellowWidth + 10;
  const x3 = redWidth + yellowWidth + greenWidth + 10;

  let performer = '';
  let xHappy: number | null = null;
  let textColor = colors.palette.alertRed;

  if (bscScore === null) {
    performer = 'No score';
    xHappy = null; // No xHappy position
    textColor = colors.placeholder;
  } else if (bscScore <= 75) {
    performer = 'Under performer';
    xHappy = (bscScore / 75) * redWidth;

    textColor = colors.palette.alertRed;
  } else if (bscScore <= 100) {
    performer = 'Normal';
    xHappy = redWidth + ((bscScore - 75) / 25) * yellowWidth;

    textColor = colors.palette.black;
  } else {
    performer = 'Outperformer';
    xHappy = redWidth + yellowWidth + ((bscScore - 100) / 36) * greenWidth;

    textColor = colors.palette.black;
  }

  //* any percentage pass in for x position of the happy icon
  // const xHappy = bscPerformancePercent * (width - 20);
  // let performer = '';
  // if (bscPerformancePercent <= 1 / 3) performer = 'Under performer';
  // else if (bscPerformancePercent <= (1 / 3) * 2) performer = 'Performer';
  // else performer = 'Outperformer';

  const getImageBasedOnPerformance = (performer: string) => {
    switch (performer) {
      case 'Under performer':
        return bscSad;
      case 'Normal':
        return bscNormal;
      case 'Outperformer':
        return bscHappy;
      default:
        return bscNormal; // Default image
    }
  };

  return (
    <Box paddingTop={barPaddingTop}>
      {/* indicating which area the laugh icon stay */}
      <Box>
        <Typography.LargeBody fontWeight="medium" color={textColor}>
          {performer ?? '--'}
        </Typography.LargeBody>
      </Box>
      <Box w={width}>
        <Svg width={width} height={40}>
          <Line
            x1="10"
            y1="20"
            x2={x1}
            y2="20"
            stroke={colors.palette.alertRed}
            strokeWidth="12"
            strokeLinecap="round"
          />
          <Line
            x1={x2}
            y1="20"
            x2={x3}
            y2="20"
            stroke={colors.palette.fwdLightGreen[100]}
            strokeWidth="12"
            strokeLinecap="round"
          />
          <Line
            x1={x1}
            y1="20"
            x2={x2}
            y2="20"
            stroke={colors.palette.fwdYellow[100]}
            strokeWidth="12"
          />

          {/* </ForeignObject> */}
        </Svg>

        {/* ios cannot show the foreignObject with image */}
        {/* <ForeignObject x={xHappy} y="10" width="24" height="24"> */}
        {xHappy !== null && (
          <Image
            source={getImageBasedOnPerformance(performer)}
            style={{
              width: space[8],
              height: space[8],
              position: 'absolute',
              top: space[1],
              left: xHappy - space[1],
            }}
          />
        )}
      </Box>
    </Box>
  );
};
