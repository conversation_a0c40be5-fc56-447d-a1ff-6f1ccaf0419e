import { PerfTabsConfigKeys } from 'features/performance/config';
import { ProcPerformanceResponse } from 'hooks/useGetPerformance';
import { ParsedPerformanceData } from 'features/performance/tablet/parsedPerformanceFunction';

type LabelData = {
  value: number | null;
  isAchieved: boolean;
};
/**
 * Data handler
 */

const bothNonZero = (value: number | undefined, target: number | undefined) => {
  return value != null && target != null && value != 0 && target != 0;
};

const performanceLabelDataHandler = (
  curTab: PerfTabsConfigKeys,
  data: ProcPerformanceResponse | ParsedPerformanceData | undefined,
): {
  caseCount: LabelData;
  salesAPE: LabelData;
} => {
  const defaultData = null;
  if (curTab === 'MTD') {
    return {
      caseCount: {
        value: data?.mtd?.caseCompletion ?? defaultData,
        isAchieved:
          bothNonZero(data?.mtd?.caseCompletion, data?.mtd?.caseTarget) &&
          (data?.mtd?.caseCompletion ?? 0) >= (data?.mtd?.caseTarget ?? 1),
      },
      salesAPE: {
        value: data?.mtd?.apeCompletion ?? defaultData,
        isAchieved:
          bothNonZero(data?.mtd?.apeCompletion, data?.mtd?.apeTarget) &&
          (data?.mtd?.apeCompletion ?? 0) >= (data?.mtd?.apeTarget ?? 1),
      },
    };
  }
  if (curTab === 'YTD') {
    return {
      caseCount: {
        value: data?.ytd?.caseCompletion ?? defaultData,
        isAchieved:
          bothNonZero(data?.ytd?.caseCompletion, data?.ytd?.caseTarget) &&
          (data?.ytd?.caseCompletion ?? 0) >= (data?.ytd?.caseTarget ?? 1),
      },
      salesAPE: {
        value: data?.ytd?.apeCompletion ?? defaultData,
        isAchieved:
          bothNonZero(data?.ytd?.apeCompletion, data?.ytd?.apeTarget) &&
          (data?.ytd?.apeCompletion ?? 0) >= (data?.ytd?.apeTarget ?? 1),
      },
    };
  }

  __DEV__ &&
    console.error(
      '🚀 ~ file: PerformanceChartSection.tsx:42 ~ performanceLabelDataHandler ~ ERROR_curTab:',
      curTab,
    );

  return {
    caseCount: {
      value: defaultData,
      isAchieved: false,
    },
    salesAPE: {
      value: defaultData,
      isAchieved: false,
    },
  };
};

export default performanceLabelDataHandler;
