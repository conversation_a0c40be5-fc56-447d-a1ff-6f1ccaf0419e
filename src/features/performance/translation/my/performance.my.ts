export default {
  // Tooltip
  'performance.tooltip.cheer': ' Woohoo! ',
  'performance.tooltip.ifClause':
    'If you can close the {{count}} saved proposals, you can get:',
  'performance.button.viewProposal': 'View proposals',
  RM: 'RM',
  'performance.yourPerformance': 'Your Performance',
  'performance.potentialCustomType': 'Potential {{type}}',
  'performance.myCustomType': 'My {{type}}',
  'performance.bestCustomType': 'No.1  {{type}}',
  'performance.toBeTopCustomType': 'To be the Top ({{type}})',

  'performance.youAreTheNo1': 'You are the No. 1',
  'performance.no1ACE': 'No.1 ACE',
  'performance.toBeTheTopACE': 'To be the Top (ACE)',
  'performance.myACE': 'My ACE',
  'performance.rankByCaseCount': 'Rank by  case count',
  'performance.yourCaseCount': 'Your case count',

  // Metric
  'performance.metric.APE': 'APE',
  'performance.metric.ACE': 'ACE',
  'performance.metric.FYP': 'FYP',
  'performance.metric.AFYP': 'AFYP',
  'performance.metric.FYC': 'FYC',
  'performance.metric.Case': 'Case',
  'performance.metric.issued': '{{metric}} Issued',
  'performance.metric.completed': '{{metric}} Completed',

  'performance.metric.submitted': ' {{metric}} Submitted',
  'performance.metric.totalIssued': 'Total {{metric}} Issued',
  'performance.metric.totalSubmitted': 'Total {{metric}} Submitted',
  'performance.metric.totalCompleted': 'Total {{metric}} Completed',

  'performance.potentialAPE': 'Potential APE',
  'performance.mySalesAPE': 'My sales APE',
  'performance.bestSalesAPE': 'No.1 sales APE',
  'performance.toBeTop': 'To be the Top (FYP)',
  'performance.tab.MTD': 'MTD',
  'performance.tab.YTD': 'YTD',
  'performance.caseCount': 'Case Count',
  'performance.salesAPE': 'Sales APE',
  'performance.label.seeDetails': 'See details',
  'performance.daysLeft.single': '{{dayCount}} day left',
  'performance.daysLeft.plural': '{{dayCount}} days left',

  // Details
  'performance.details.month.others': 'This month other performance',
  'performance.details.totalMetric.submitted': 'Total {{metric}} submitted',
  'performance.details.totalMetric.completed': 'Total {{metric}} completed',
  'performance.details.year.others.metric': 'This year {{metric}} count',
  'performance.details.metric.submitted': '{{metric}} submitted',
  'performance.details.metric.completed': '{{metric}} completed',
  'performance.details.total.submitted': 'Total Submitted',
  'performance.details.total.completed': 'Total Completed',

  'performance.details.tabLabel.salesApe': 'Sales APE',
  'performance.details.tabLabel.caseOthers': 'Case & others',

  // Ranking
  'performance.ranking.directTeam': 'Direct team',
  'performance.ranking.MTD': 'PH (MTD)',
  'performance.ranking.YTD': 'PH (YTD)',
  'performance.ranking.chart.directTeam.label': 'Ranking among Direct Team',
  'performance.ranking.chart.wholeRegion.label': 'Ranking among MY FWDT',
  yourRanking: 'Your ranking',

  // Recognition
  'performance.recognition': 'Recognition',
  'performance.recognition.emptyCase': `You haven't got any recognition yet.`,
  'performance.recognition.details.nextTier': 'Next tier',
  'performance.recognition.overAllAchievement': 'Overall Achievement',
  'performance.recognition.requirementsWithMetric': '{{metric}} Requirements',
  'performance.recognition.currentTier': 'Current Tier',
  'performance.recognition.complete': 'Completed',
  'performance.recognition.inprogress': 'In progress',
  'performance.recognition.mdrtComplete': 'completed',
  'performance.recognition.target': 'FYP target',
  'performance.recognition.completion': 'FYP completion',
  'performance.recognition.shortfall': 'FYP shortfall',
  'performance.recognition.Incomplete': 'Incomplete',
  'performance.recognition.congratulation': ' Congratulation',
  'top.congratulation': 'Congratulations!',
  'performance.recognition.yourNextTierWillBe': ', your next tier will be:',
  'performance.recognition.overAllAchievementWithYear':
    'Overall Achievements in {{year}}',
  'performance.recognition.achievedTiersWithYeah':
    'Achieved tiers for {{year}}',
  recognition: 'Recognition',
  firstYearPremium: 'First year premium',
  firstYearCommissions: 'First year commissions',
  'performance.recognition.achieved': 'Achieved',
  'performance.recognition.NoAchievement': 'None',
  'performance.recognition.FWDElite': 'FWD Elite',
  'performance.recognition.MDRT': 'MDRT',
  'performance.recognition.COT': 'COT',
  'performance.recognition.TOT': 'TOT',
  'current.rank.by': 'Current rank by FYP',
  'over.active.agents': ' /{{totalActiveAgents}} active agents',
  'recognition.title': 'MDRT tracking',

  //Target Setting
  'target.title.monthly': 'Monthly target',
  'target.title.yearly': 'Yearly target',
  'performance.target': 'Target ',
  'performance.targetCases': 'Target Cases',
  'performance.editTarget': 'Edit Target',
  'performance.editTarget.reset': 'Reset',
  'performance.editTarget.save': 'Save',
  'performance.editTarget.screenTitle': 'Edit targets',
  'performance.editTarget.success.message':
    'Targets have been saved successfully',
  'performance.editTarget.exitModal.title': 'Exit edit target',
  'performance.editTarget.exitModal.content':
    'Do you want to save before exiting the edit targets?',
  'performance.editTarget.exitModal.save': 'Save',
  'performance.editTarget.exitModal.notSave': "Don't save",

  // Others
  'performance.done': 'Done',
  overallPerformance: 'Overall performance',
  monthly: '(Monthly)',
  yearly: '(Yearly)',
  viewPerformanceDetails: 'View performance details',
  currentMonthCase: 'Current month case count',
  totalCaseSubmitted: 'Total Case Submitted',
  totalCaseIssued: 'Total Case Issued',
  currentMonthACE: 'Current month Annual Contribution Equivalent ',
  currentMonthAPE: 'Current month APE ',
  currentMonthFYP: 'Current month FYP ',
  currentMonthAFYP: 'Current month AFYP ',
  currentMonthFYC: 'Current month FYC ',
  totalACESub: 'Total ACE Submitted',
  target: 'Target',
  shortfall: 'Shortfall',
  totalACEIssued: 'Total ACE Issued',
  thisYearACE: 'This year Annual Contribution Equivalent',
  numberOfCaseYear: 'Number of Case in This Year',
  caseSubmitted: 'Case Submitted',
  caseIssued: 'Case Issued',
  caseCompleted: 'Case Completed',
  aceSubmitted: 'ACE Submitted',
  aceIssued: 'ACE Issued',
  persistency: '1st Year Persistency',

  //BSC
  balancedScorecard: 'Balanced Scorecard (BSC) - YTD',
};
