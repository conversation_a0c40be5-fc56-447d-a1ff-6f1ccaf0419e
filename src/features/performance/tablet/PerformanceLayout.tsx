import React, { useEffect, useState } from 'react';
import { Dimensions, ScrollView, TouchableOpacity } from 'react-native';
import { useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import CampaignsTablet from 'features/performance/tablet/CampaignsTablet';
import OverallPerformance from 'features/performance/tablet/OverallPerformance';
import PerformanceDetailsContent from 'features/performance/tablet/PerformanceDetailsContent';
import PerformanceTabHeader from 'features/performance/tablet/PerformanceTabHeader';
import PerformanceBSC from './PerformanceBSC';
import RankingTablet from '../components/RankingTablet';
import RankingTabletV2 from '../components/RankingTabletV2';
import RecognitionPreview from '../components/RecognitionPreview';
import {
  useGetAllPerformance,
  useGetTeamIndividualMDRT,
  useGetTeamIndividualPerformance,
} from 'hooks/useGetPerformance';
import { useGetPerformanceTarget } from 'hooks/usePerformanceTarget';
import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { MDRTTiers, TeamIndividualMDRTResponse } from 'types/performance';
import useParsedPerformanceData from '../hooks/useParsedPerformanceData';
import { usePerviewTeamBasedMdrt } from 'features/recognition/hooks/useMDRTTierCalculations';
import { useCheckAgentIsMDRT } from 'hooks/useCheckAgentIsMDRT';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';

export default function PerformanceLayout() {
  const { colors, space } = useTheme();
  const { top, bottom } = useSafeAreaInsets();
  const itemsGap = space[4];
  const { t: pt } = useTranslation('performance');
  const isIbOrMy = country === 'ib' || country === 'my';

  const { isLoading: isAllPerformanceLoading, data: allPerfData } =
    useGetAllPerformance();

  const { isLoading: isPerformanceTargetLoading } = useGetPerformanceTarget();
  const {
    isLoading: isIndividualPerformanceLoading,
    data: individualPerformanceData,
  } = useGetTeamIndividualPerformance();
  const { isLoading: isTeamMDRTLoading, data: teamMdrtDataObj } =
    useGetTeamIndividualMDRT({
      params: { isGetTeam: false },
      isEnabled: isIbOrMy,
    });

  const teamMdrtData = teamMdrtDataObj?.mdrtRanking?.find(item => {
    const currentYear = new Date().getFullYear();
    return item.year === String(currentYear);
  });

  const { data: isMDRT } = useCheckAgentIsMDRT();
  const isMdrtAvailble = isMDRT != null;

  const isLoading = isIbOrMy
    ? isIndividualPerformanceLoading && isPerformanceTargetLoading
    : isAllPerformanceLoading;

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const isFirstTimeTip = useBoundStore(
    state => state.performance.tipYourPerformance,
  );
  const updateTipYourPerformance = useBoundStore(
    state => state.performanceActions.updateTipYourPerformance,
  );

  const { isLoading: isAgentProfileLoading, data: agentProfile } =
    useGetAgentProfile();

  const agentFirstName = agentProfile?.person?.firstName ?? '';

  useEffect(() => {
    if (isLoading == true) {
      setAppLoading();
    } else setAppIdle();

    return () => {
      if (isFirstTimeTip) {
        updateTipYourPerformance();
      }
    };
  }, [
    isLoading,
    isFirstTimeTip,
    setAppIdle,
    setAppLoading,
    updateTipYourPerformance,
  ]);
  const { data } = useParsedPerformanceData();
  const agentCode = useBoundStore.getState().auth.agentCode;
  return (
    <Column pt={top} backgroundColor={colors.background}>
      <ScrollView
        bounces={false}
        nestedScrollEnabled={true}
        contentContainerStyle={{
          backgroundColor: colors.palette.fwdGrey[50],
          paddingHorizontal: space[8],
          paddingBottom: space[6] + bottom,
          gap: itemsGap,
        }}>
        <PerformanceTabHeader itemsGap={itemsGap} />
        {countryModuleSellerConfig.performance?.isPerformanceBSCShown && (
          <PerformanceBSC data={individualPerformanceData} />
        )}
        <OverallPerformance
          data={data}
          agentCode={agentCode}
          agentName={agentFirstName}
        />
        <CollapsibleCard title={pt('viewPerformanceDetails')}>
          <PerformanceDetailsContent data={data} />
        </CollapsibleCard>

        {countryModuleSellerConfig.performance?.isRankingShown &&
          (country == 'ph' ? <RankingTablet /> : <RankingTabletV2 />)}

        {countryModuleSellerConfig.performance?.isRecognitionShown && (
          <>
            <RecognitionPreview
              isLoading={isIbOrMy ? isTeamMDRTLoading : isAllPerformanceLoading}
              data={
                isIbOrMy
                  ? usePerviewTeamBasedMdrt(teamMdrtData)
                  : {
                      currentTier:
                        allPerfData?.mdrt?.nextTier === 'MDRT'
                          ? 'FWDElite'
                          : allPerfData?.mdrt?.nextTier === 'COT'
                          ? 'MDRT'
                          : allPerfData?.mdrt?.nextTier === 'TOT'
                          ? 'COT'
                          : 'TOT',
                      nextTier: allPerfData?.mdrt?.nextTier as MDRTTiers,
                      percent:
                        allPerfData?.mdrt?.nextTier === 'MDRT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToMDRTPercent ?? 0)
                          : allPerfData?.mdrt?.nextTier === 'COT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToCOTPercent ?? 0)
                          : allPerfData?.mdrt?.nextTier === 'TOT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToTOTPercent ?? 0)
                          : 0,
                    }
              }
              agentName={agentFirstName}
            />
            {false && <CampaignsTablet />}
          </>
        )}
      </ScrollView>
    </Column>
  );
}
export const CollapsibleCard = ({
  title,
  children,
}: // data,
{
  title: string;
  children?: React.ReactNode;
}) => {
  const { colors, space, borderRadius } = useTheme();
  const [isCollapse, setIsCollapse] = useState(false);

  return (
    <Box
      padding={space[6]}
      backgroundColor={colors.background}
      borderRadius={borderRadius.large}>
      <Row justifyContent={'space-between'} alignItems={'center'}>
        <Row justifyContent="center" alignItems="center" gap={8}>
          <Typography.H6 fontWeight="bold">{title}</Typography.H6>
        </Row>
        <TouchableOpacity
          onPress={() => {
            setIsCollapse(!isCollapse);
          }}>
          {isCollapse ? (
            <Icon.ChevronDown width={24} height={24} />
          ) : (
            <Icon.ChevronUp width={24} height={24} />
          )}
        </TouchableOpacity>
      </Row>
      {!isCollapse && children}
    </Box>
  );
};
