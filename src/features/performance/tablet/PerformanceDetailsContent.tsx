import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';

import TabButton from 'features/lead/tablet/components/TabButton';
import PerformanceMTDdetails from 'features/performance/tablet/PerformanceMTDdetails';
import PerformanceYTDdetails from 'features/performance/tablet/PerformanceYTDdetails';
import { ProcPerformanceResponse } from 'hooks/useGetPerformance';
import { ParsedPerformanceData } from './parsedPerformanceFunction';
import { ParsedTeamPerformanceData } from 'features/teamManagement/Utils/teamIndividualProfileUtils';

export default function PerformanceDetailsContent({
  data,
}: {
  data:
    | ProcPerformanceResponse
    | ParsedPerformanceData
    | ParsedTeamPerformanceData;
}) {
  const [currentTab, setCurrentTab] = useState('MTD');
  const { colors, space, borderRadius } = useTheme();

  return (
    <Box alignItems="center" gap={space[5]}>
      <Row w={416} gap={space[4]} marginY={space[5]}>
        <TabButton
          isActive={currentTab === 'MTD'}
          label={'MTD details'}
          onPress={() => {
            setCurrentTab('MTD');
          }}
        />
        <TabButton
          isActive={currentTab === 'YTD'}
          label={'YTD details'}
          onPress={() => {
            setCurrentTab('YTD');
          }}
        />
      </Row>
      <Box w={'100%'}>
        {currentTab === 'MTD' && <PerformanceMTDdetails data={data} />}
        {currentTab === 'YTD' && <PerformanceYTDdetails data={data} />}
      </Box>
    </Box>
  );
}
