import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Column,
  H4,
  H7,
  H8,
  Row,
  SmallLabel,
  Typography,
} from 'cube-ui-components';

import {
  useGetAllPerformance,
  useGetPerformanceKpi,
  useGetTeamIndividualPerformance,
} from 'hooks/useGetPerformance';
import { Dimensions, View } from 'react-native';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function PersistencyAndEfficiencyCardSmallScreen() {
  const { colors, space } = useTheme();

  // const { data: allPerf } = useGetAllPerformance();
  const { data: teamIndividualData } = useGetTeamIndividualPerformance();

  const { data } = useGetPerformanceKpi();

  const collectionEfficiency =
    teamIndividualData?.individualPerformance?.persistency
      ?.collectionEfficiency ?? '--';

  const firstYearpersistency = data?.firstYearPersistency ?? '--';
  const secondYearPersistency = data?.secondYearPersistency ?? '--';

  const dateForEfficiency = data?.collectionEfficiencyPostingDate;
  const dateForFirstYear = data?.firstYearPersistencyPostingDate;
  const dateForSecondYear = data?.secondYearPersistencyPostingDate;

  return (
    <Row gap={space[3]}>
      <CardContainer style={{ flex: 1 }}>
        <Box gap={space[1]}>
          <H7 fontWeight="bold" style={{ textAlign: 'center' }}>
            {'Collection efficiency (CE)'}
          </H7>
          <SmallLabel
            color={colors.palette.fwdGreyDarker}
            style={{ textAlign: 'center' }}>
            {'As of ' +
              `${dateForEfficiency ? dateFormatUtil(dateForEfficiency) : '--'}`}
          </SmallLabel>
        </Box>
        <Box flex={1} alignItems="center" justifyContent="center">
          <H4 fontWeight="bold" color={colors.primary}>
            {collectionEfficiency ? `${collectionEfficiency}%` : '--'}
          </H4>
        </Box>
      </CardContainer>

      <CardContainer
        style={{
          flex: 3,
          minHeight: 180,
          backgroundColor:
            firstYearpersistency != null
              ? colors.primaryVariant3
              : colors.palette.fwdGrey[20],
        }}>
        <Box gap={space[1]}>
          <H7 fontWeight="bold" style={{ textAlign: 'center' }}>
            {'Persistency (PR)'}
          </H7>
          <SmallLabel
            color={colors.palette.fwdGreyDarker}
            style={{ textAlign: 'center' }}>
            {
              'As of ' +
                `${dateForFirstYear ? dateFormatUtil(dateForFirstYear) : '--'}`
              // (dateForFirstYear ?? dateForSecondYear ?? '--')
            }
          </SmallLabel>
        </Box>
        <Row flex={1} justifyContent="center" alignItems="center">
          <Box flex={1} alignItems="center">
            <Box minH={space[10]}>
              {firstYearpersistency == null ? (
                <Typography.LargeBody
                  fontWeight="bold"
                  color={colors.placeholder}>
                  {'--'}
                </Typography.LargeBody>
              ) : (
                <H4 fontWeight="bold" color={colors.primary}>
                  {`${firstYearpersistency}%`}
                </H4>
              )}
            </Box>
            <Box pt={space[1]}>
              <H8 color={colors.placeholder} style={{ textAlign: 'center' }}>
                {'1st year\n(12-months)'}
              </H8>
            </Box>
          </Box>
          <LineSeparator />
          <Box flex={1} alignItems="center">
            <Box minH={space[10]}>
              {secondYearPersistency == null ? (
                <Typography.LargeBody
                  fontWeight="bold"
                  color={colors.placeholder}>
                  {'--'}
                </Typography.LargeBody>
              ) : (
                <H4 fontWeight="bold" color={colors.primary}>
                  {`${secondYearPersistency}%`}
                </H4>
              )}
            </Box>
            <Box pt={space[1]}>
              <H8 color={colors.placeholder} style={{ textAlign: 'center' }}>
                {'2nd year\n(24-months)'}
              </H8>
            </Box>
          </Box>
        </Row>
      </CardContainer>
    </Row>
  );
}

const CardContainer = styled(View)(({ theme }) => ({
  backgroundColor: theme.colors.primaryVariant3,
  padding: theme.space[4],
  borderRadius: theme.borderRadius.medium,
  alignItems: 'center',
  height: 180,
}));

const LineSeparator = styled.View(({ theme }) => ({
  marginTop: theme.space[5],
  marginBottom: theme.space[2],
  height: '100%',
  width: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));
