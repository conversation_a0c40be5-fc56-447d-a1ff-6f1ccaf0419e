import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

type PerformanceManInTipProps = {
  size?: number;
  height?: number;
  width?: number;
};

export default function PerformanceManInTip(props: PerformanceManInTipProps) {
  return (
    <Svg
      width={props?.size ?? props.width ?? 115}
      height={props?.size ?? props.height ?? 109}
      viewBox="0 0 115 109"
      fill="none"
      //   xmlns="http://www.w3.org/2000/svg"
      //   {...props}
    >
      <Path
        d="M91.732 19.763c-.034.493-.357.867-.732.833-.374-.034-.63-.46-.578-.952.05-.493.357-.867.732-.833.374.034.612.459.578.952zM82.66 19.44c-.035.493-.358.867-.733.833-.374-.034-.63-.46-.579-.952.052-.493.358-.868.733-.834.374.035.63.46.578.953z"
        fill="#183028"
      />
      <Path
        d="M86.472 28.044c.325.013.65-.01.97-.068a4.546 4.546 0 001.771-.834.272.272 0 00.034-.39.257.257 0 00-.284-.088.256.256 0 00-.09.053c-.103.085-2.52 2.075-4.818-.595a.27.27 0 00-.374-.034.272.272 0 00-.017.391c.953 1.123 1.957 1.514 2.808 1.565zM86.302 29.455c.294.031.591-.028.851-.17a.275.275 0 00.085-.374.258.258 0 00-.374-.085c-.017.017-.477.255-1.209-.102a.26.26 0 00-.357.12.278.278 0 00.119.373c.278.132.578.213.885.238zM85.996 24.558a.254.254 0 00.255-.119.248.248 0 00-.085-.34c-.528-.323-.783-.697-.766-1.122.034-.544.545-1.071.902-1.207.732-.306.902-2.67.92-3.673a.247.247 0 00-.494-.017c-.051 1.598-.34 3.111-.613 3.213-.46.188-1.14.834-1.209 1.633-.034.425.12 1.054 1.005 1.598a.13.13 0 00.085.034z"
        fill="#F89F60"
      />
      <Path
        d="M32.29 62.494l-2.571-1.156-2.094 2.635 3.337 3.061 1.327-4.54z"
        fill="#FBE2D0"
      />
      <Path
        d="M28.885 63.752l.868-1.105a2.74 2.74 0 00-.46-3.826l-6.145-4.812a2.747 2.747 0 00-3.83.459 4.146 4.146 0 00.698 5.798l5.056 3.945a2.701 2.701 0 003.813-.459z"
        fill="#FBE2D0"
      />
      <Path
        d="M28.732 60.555c-.698.255-1.413-.153-1.6-.918l-1.158-5.067c-.17-.765.238-1.666.953-1.853.562-.154 1.413.153 1.6.918l1.158 5.067a1.648 1.648 0 01-.953 1.853z"
        fill="#FBE2D0"
      />
      <Path
        d="M100.38 61.066s-6.52 24.281-24.223 26.526C60.462 89.564 35.762 72.169 30.4 68.242a1.53 1.53 0 01-.46-1.94l2.043-4.097a1.54 1.54 0 011.805-.8l29.483 8.248c2.27.638 4.69.486 6.862-.431a10.38 10.38 0 005.088-4.62l9.243-17.02 15.916 13.484z"
        fill="#B7E0D5"
      />
      <Path
        d="M89.111 30.73l.834 10.609 10.435.444-4.205-16.445-7.064 5.392z"
        fill="#F8E3D2"
      />
      <Path
        d="M84.651 32.669c-1.328-.034-2.553-.238-3.183-.748-3.813-5.544.528-20.49.528-20.49l6.094-2.568 7.745 4.83.443 10.015c.119 2.67-1.175 5.237-3.456 6.903a16.001 16.001 0 01-8.17 2.058z"
        fill="#FBE2D0"
      />
      <Path
        d="M94.626 19.354c-.425-1.683-.204-5.56-1.923-5.288-1.243.204-5.532.374-6.98.374-3.438 0-9.566-1.343-9.566-6.564 0-2.618 2.536-4.863 4.136-5.526 1.6-.663 3.354-.629 4.97-.068 3.78 1.326 8.802 4.234 10.81 3.843 3.37-.68 6.724 1.292 6.673 4.15-.051 3.06-1.651 7.65-3.149 10.559a16.027 16.027 0 01-3.439 4.489l-1.532-5.969z"
        fill="#183028"
      />
      <Path
        d="M98.1 19.745c-.767-.68-2.197-.544-3.2.29-1.005.833-1.193 2.057-.427 2.737.767.68 2.196.544 3.2-.289 1.005-.833 1.192-2.074.426-2.738z"
        fill="#FBE2D0"
      />
      <Path
        d="M94.746 35.015C118.612 30.067 114.555 109 114.555 109H76.702s-.545-13.74-1.924-31.168c-1.174-14.777 7.133-31.458 19.968-42.817z"
        fill="#74C7AF"
      />
      <Path
        d="M47.27 72.68c.09-2.433.64-4.827 1.617-7.057l-14.93-4.166a1.773 1.773 0 00-2.06.918l-1.889 3.758a1.769 1.769 0 00.528 2.21c2.656 1.939 9.414 6.717 17.448 11.053a24.462 24.462 0 01-.715-6.717z"
        fill="#B7E0D5"
      />
      <Path
        d="M89.111 40.659V35.44l8.715-3.623 3.183 4.014-11.898 4.828z"
        fill="#F8E3D2"
      />
      <Path
        d="M34.604 48.041l-2.468-1.056-1.924 2.57 3.251 2.822 1.14-4.336z"
        fill="#FBE2D0"
      />
      <Path
        d="M31.387 49.316l.8-1.071a2.616 2.616 0 00-.528-3.639L25.7 40.168a2.623 2.623 0 00-3.643.527 3.947 3.947 0 00.8 5.51l4.903 3.638a2.602 2.602 0 003.626-.527z"
        fill="#FBE2D0"
      />
      <Path
        d="M31.182 46.273c-.663.255-1.344-.12-1.532-.85l-1.208-4.796c-.187-.731.187-1.598.868-1.785.528-.153 1.345.119 1.532.85l1.209 4.795a1.568 1.568 0 01-.869 1.786z"
        fill="#FBE2D0"
      />
      <Path
        d="M100.959 55.862a1.453 1.453 0 01.204 1.616c-2.077 4.217-11.83 22.07-27.56 21.646-15.882-.425-37.739-21.51-42.335-26.118a1.45 1.45 0 01-.153-1.888l2.723-3.826a1.487 1.487 0 011.822-.493l27.934 12.634a10.408 10.408 0 0012.58-3.18l10.639-13.977a1.478 1.478 0 012.298-.068l11.848 13.654z"
        fill="#B7E0D5"
      />
      <Path
        d="M91.732 19.763c-.034.493-.357.867-.732.833-.374-.034-.63-.46-.578-.952.05-.493.357-.867.732-.833.374.034.612.459.578.952zM82.66 19.44c-.035.493-.358.867-.733.833-.374-.034-.63-.46-.579-.952.052-.493.358-.868.733-.834.374.035.63.46.578.953z"
        fill="#183028"
      />
      <Path
        d="M86.472 28.044c.325.013.65-.01.97-.068a4.546 4.546 0 001.771-.834.272.272 0 00.034-.39.257.257 0 00-.284-.088.256.256 0 00-.09.053c-.103.085-2.52 2.075-4.818-.595a.27.27 0 00-.374-.034.272.272 0 00-.017.391c.953 1.123 1.957 1.514 2.808 1.565zM86.302 29.455c.294.031.591-.028.851-.17a.275.275 0 00.085-.374.258.258 0 00-.374-.085c-.017.017-.477.255-1.209-.102a.26.26 0 00-.357.12.278.278 0 00.119.373c.278.132.578.213.885.238zM85.996 24.558a.254.254 0 00.255-.119.248.248 0 00-.085-.34c-.528-.323-.783-.697-.766-1.122.034-.544.545-1.071.902-1.207.732-.306.902-2.67.92-3.673a.247.247 0 00-.494-.017c-.051 1.598-.34 3.111-.613 3.213-.46.188-1.14.834-1.209 1.633-.034.425.12 1.054 1.005 1.598a.13.13 0 00.085.034z"
        fill="#E77825"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.284 45.626h6.605c.221 0 .425.085.425.17s-.17.17-.408.17h-6.673c-.221 0-.408-.085-.408-.17a.582.582 0 01.46-.17z"
        fill="#333"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.596 46.357v1.514H20.867v-1.514a6.83 6.83 0 00-3.166-5.866 16.412 16.412 0 01-6.401-8.1 16.393 16.393 0 015.633-18.766 16.44 16.44 0 0119.613 0 16.41 16.41 0 015.928 8.451 16.392 16.392 0 01-.294 10.315 16.412 16.412 0 01-6.401 8.1 6.817 6.817 0 00-3.183 5.866z"
        fill="#FED141"
      />
      <Path
        d="M25.839 39.295v-2.823c-.925-.116-1.718-.363-2.412-.743a6.365 6.365 0 01-1.718-1.304 6.076 6.076 0 01-1.073-1.634 5.42 5.42 0 01-.463-1.734l3.287-.792c.033.43.132.825.298 ************.396.726.693 1.023.298.297.678.545 1.107.71.446.182.975.264 1.586.264.859 0 1.536-.181 2.015-.56.48-.364.71-.843.71-1.42 0-.463-.165-.859-.495-1.173-.33-.33-.826-.56-1.503-.693l-2.428-.528c-1.421-.314-2.56-.908-3.387-1.767-.826-.858-1.239-1.93-1.239-3.202 0-.71.133-1.354.38-1.964a4.937 4.937 0 011.058-1.585 5.944 5.944 0 011.585-1.156 6.68 6.68 0 011.966-.627v-2.823h2.692v2.906c.76.148 1.42.363 1.966.693a5.58 5.58 0 011.404 1.09c.38.412.677.841.892 1.303.215.463.38.925.463 1.354l-3.255.924a3.244 3.244 0 00-.23-.759 2.956 2.956 0 00-.53-.809 2.98 2.98 0 00-.891-.627c-.364-.165-.81-.248-1.355-.248-.842 0-1.503.215-1.949.628-.446.412-.677.891-.677 1.436 0 .413.148.76.446 1.09.297.313.76.544 1.37.693l2.412.56c1.669.364 2.908 1.008 3.684 1.932a4.782 4.782 0 011.156 3.17c0 .61-.099 1.205-.314 1.766a4.946 4.946 0 01-.958 1.552c-.43.462-.941.858-1.552 1.189-.611.33-1.305.544-2.098.676v2.856h-2.643v-.05z"
        fill="#183028"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.271 46.697h12.903a.586.586 0 01.58.578v1.174a.585.585 0 01-.58.578H20.271a.574.574 0 01-.579-.578v-1.174a.585.585 0 01.58-.578z"
        fill="#183028"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.723 57.138h-1.668a2.336 2.336 0 01-1.958-1.037l-1.651-2.466H32l-1.652 2.466a2.251 2.251 0 01-1.957 1.037h-1.668z"
        fill="#F89F60"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.701 53.737h10.06a.837.837 0 00.835-.833v-3.027a.834.834 0 00-.835-.833h-10.06a.836.836 0 00-.834.833v3.027a.835.835 0 00.834.833z"
        fill="#F89F60"
      />
      <Path d="M32.596 51.39H20.867v1.174h11.729V51.39z" fill="#F89F60" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.74 3.184a.451.451 0 01.442.442v2.788a.442.442 0 01-.885 0V3.626a.45.45 0 01.443-.442zM10.007 43.466a.429.429 0 010-.612l1.974-1.972a.433.433 0 11.613.612l-1.975 1.972a.432.432 0 01-.612 0zM3.094 26.768a.45.45 0 01.443-.442H6.33a.443.443 0 110 .884H3.538a.452.452 0 01-.443-.442zm6.928-16.698a.432.432 0 01.613 0l1.975 1.973a.434.434 0 01-.613.612l-1.974-1.973a.43.43 0 010-.612zM43.422 43.5a.434.434 0 01-.613 0l-1.974-1.972a.434.434 0 11.612-.612l1.975 1.972a.432.432 0 010 .612zm6.928-16.698a.45.45 0 01-.442.442h-2.792a.443.443 0 110-.884h2.792a.452.452 0 01.442.442zm-6.894-16.698a.43.43 0 010 .613l-1.975 1.972a.433.433 0 11-.612-.612l1.957-1.973a.453.453 0 01.63 0zm9.958 5.663a.432.432 0 01-.238.56l-7.422 3.079a.432.432 0 01-.323-.8l7.422-3.077a.433.433 0 01.561.238zM37.804.14a.432.432 0 01.239.561l-3.081 7.414a.43.43 0 01-.8-.323l3.08-7.414a.432.432 0 01.563-.238zM.031 37.804a.432.432 0 01.239-.561l7.421-3.078a.432.432 0 01.324.8L.61 38.041a.46.46 0 01-.579-.238zm.017-22.071a.432.432 0 01.562-.238l7.422 3.077a.43.43 0 11-.324.8L.287 16.294a.432.432 0 01-.239-.561zM15.692.123a.433.433 0 01.562.238l3.08 7.414a.431.431 0 11-.8.323L15.455.684a.445.445 0 01.238-.561z"
        fill="#FBE69D"
      />
      <Path
        d="M28.595 63.685l.868-1.105a2.74 2.74 0 00-.46-3.826l-6.145-4.813a2.748 2.748 0 00-3.83.46 4.146 4.146 0 00.698 5.798l5.056 3.945a2.725 2.725 0 003.813-.46z"
        fill="#FBE2D0"
      />
      <Path
        d="M28.442 60.47c-.698.256-1.413-.152-1.6-.917l-1.157-5.067c-.17-.766.238-1.667.953-1.854.561-.153 1.413.153 1.6.918l1.157 5.068a1.609 1.609 0 01-.953 1.853z"
        fill="#FBE2D0"
      />
    </Svg>
  );
}
