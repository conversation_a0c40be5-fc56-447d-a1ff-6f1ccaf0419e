import React from 'react';
import styled from '@emotion/native';
import { StyleProp, View, ViewStyle } from 'react-native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { XView } from 'cube-ui-components';

export interface PaginationProps {
	activeIndex: number;
	count: number;
	onPressDot?: (index: number) => void;
	style?: StyleProp<ViewStyle>;
}

export default function Pagination({
	activeIndex = 0,
	count,
	onPressDot,
	style,
}: PaginationProps) {
	return (
		<XView style={style}>
			{Boolean(count) &&
				Array(count)
					.fill('dot')
					.map((v, index) => (
						<IndicatorDot
							key={String(v + index)}
							active={activeIndex === index}
							onPress={onPressDot && (() => onPressDot(index))}
						/>
					))}
		</XView>
	);
}

interface IndicatorDotProps {
	active?: boolean;
	onPress?: () => void;
}

function IndicatorDot({ active, onPress }: IndicatorDotProps) {
	return (
		<View pointerEvents={onPress ? 'auto' : 'none'}>
			<DotContainerOutsider active={active} onPress={onPress}>
				<DotContainer active={active} onPress={onPress}>
					<Dot active={active} />
				</DotContainer>
			</DotContainerOutsider>
		</View>
	);
}

const Dot = styled.View<IndicatorDotProps>(({ theme, active }) => ({
	width: 14,
	height: 14,
	borderWidth: 2,
	borderRadius: theme.borderRadius.full,
	backgroundColor: active ? colors.fwdOrange[50] : colors.fwdGrey[100],
	borderColor: active
		? colors.fwdOrange[100]
		: colors.fwdGrey[100],
}));

const DotContainer = styled.TouchableOpacity<IndicatorDotProps>(
	({ theme, active }) => ({
		height: 16,
		width: 16,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: active
			? theme.colors.palette.fwdOrange[20]
			: 'transparent',
		borderRadius: theme.borderRadius.full,
		marginHorizontal: 2,
	}),
);

const DotContainerOutsider = styled.TouchableOpacity<IndicatorDotProps>(
	({ theme, active }) => ({
		height: 24,
		width: 24,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: active
			? theme.colors.palette.fwdOrange[20]
			: 'transparent',
		borderRadius: theme.borderRadius.full,
		marginRight: sizes[2]
	}),
);
