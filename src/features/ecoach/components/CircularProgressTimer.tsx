import React, { useEffect, useRef, useState } from 'react';
import Svg, { Circle, G } from 'react-native-svg';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import styled from '@emotion/native';
import { H7 } from 'cube-ui-components';

// 8 mins
export const DEFAULT_CALL_TIME = 8 * 60 + 5;

// 2 mins
export const TWO_MIN_CALL_TIME = 2 * 60 + 5;

// 5 mins
export const FIVE_MIN_CALL_TIME = 5 * 60 + 5;

interface CircularProgressTimerProps {
  size?: number;
  strokeWidth?: number;
  setLeavingModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  setTimeOutStatus: React.Dispatch<React.SetStateAction<boolean>>;
  timeRemainingNotification?: number;
  totalTime: number;
  reTryTime: number; // Add key prop for reset countdown
}

const CircularProgressTimer: React.FC<CircularProgressTimerProps> = ({
  size = 64,
  strokeWidth = 7,
  setLeavingModalVisible,
  setTimeOutStatus,
  timeRemainingNotification = 60,
  totalTime,
  reTryTime,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const [timeLeft, setTimeLeft] = useState<number>(totalTime);
  const [progress, setProgress] = useState<number>(circumference);
  const timerRef = useRef<any>();

  useEffect(() => {
    setTimeLeft(totalTime);
  }, [reTryTime, totalTime]);

  useEffect(() => {
    if (totalTime) {
      setTimeLeft(totalTime);
    }
  }, [totalTime]);

  useEffect(() => {
    timerRef.current = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime <= 1) {
          setTimeOutStatus(true);
          clearInterval(timerRef.current);
          return 0;
        }
        if (prevTime - 1 === timeRemainingNotification) {
          setLeavingModalVisible(true);
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timerRef.current);
  }, [totalTime, timeRemainingNotification, reTryTime]);

  useEffect(() => {
    setProgress((timeLeft / totalTime) * circumference);
  }, [timeLeft, circumference, totalTime]);

  let validTimeLeft = timeLeft;
  if (timeRemainingNotification === 15 && validTimeLeft > 2 * 60) {
    validTimeLeft = 2 * 60;
  }
  if (timeRemainingNotification === 30 && validTimeLeft > 5 * 60) {
    validTimeLeft = 5 * 60;
  }
  if (timeRemainingNotification === 60 && validTimeLeft > 15 * 60) {
    validTimeLeft = 15 * 60;
  }
  const minutes = Math.floor(validTimeLeft / 60);
  const seconds = validTimeLeft % 60;

  const isLessThanAMinute = timeLeft < timeRemainingNotification;
  const strokeColor = isLessThanAMinute ? '#DC3D43' : colors.fwdOrange[100];
  const textColor = isLessThanAMinute ? '#DC3D43' : colors.fwdDarkGreen[100];

  return (
    <Container>
      <Svg width={size} height={size}>
        <G
          rotation="-90"
          origin={`${size / 2}, ${size / 2}`}
          fill={colors.white}>
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
          />
          <Circle
            stroke={strokeColor}
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={progress}
            strokeLinecap="round"
          />
        </G>
      </Svg>
      <TimerText style={{ color: textColor }} fontWeight={'bold'}>
        {`${minutes.toString().padStart(2, '0')}:${seconds
          .toString()
          .padStart(2, '0')}`}
      </TimerText>
    </Container>
  );
};

const Container = styled.View(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const TimerText = styled(H7)(() => ({
  position: 'absolute',
}));

export default CircularProgressTimer;
