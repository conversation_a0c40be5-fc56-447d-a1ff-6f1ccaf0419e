import React, { useEffect, useState } from 'react';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { Modal, Platform } from 'react-native';
import styled from '@emotion/native';
import { homePageBg } from 'features/ecoach/assets';
import { BlurView } from 'expo-blur';

interface CountdownCircleProps {
  initialCount?: number;
  onComplete: () => void;
  visible: boolean;
}

const CountdownCircleModal: React.FC<CountdownCircleProps> = ({
  initialCount = 3,
  onComplete,
  visible,
}) => {
  const [count, setCount] = useState(initialCount);

  useEffect(() => {
    setCount(initialCount);
  }, [initialCount]);

  useEffect(() => {
    if (count > 0) {
      const timer = setTimeout(() => setCount(count => count - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      onComplete();
    }
  }, [count, onComplete]);

  const content = (
    <Container>
      <ImgBg source={homePageBg}>
        <Circle>
          <CountText>{count}</CountText>
        </Circle>
      </ImgBg>
    </Container>
  );

  if (Platform.OS === 'ios') {
    return <ABSView>{content}</ABSView>;
  }
  return <Modal visible={visible}>{content}</Modal>;
};

const ImgBg = styled.ImageBackground(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  width: '100%',
  height: '100%',
}));

const ABSView = styled(BlurView)(() => ({
  display: 'flex',
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 9999,
}));

const Container = styled(BlurView)(() => ({
  flex: 1,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(24, 48, 40, 0.7)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Circle = styled.View(() => ({
  width: 288,
  height: 288,
  borderRadius: 144,
  justifyContent: 'center',
  alignItems: 'center',
  display: 'flex',
  borderWidth: 10,
  borderColor: colors.fwdOrange[50],
}));

const CountText = styled.Text(() => ({
  fontSize: 150,
  color: colors.fwdOrange[50],
  fontWeight: 'bold',
}));

export default CountdownCircleModal;
