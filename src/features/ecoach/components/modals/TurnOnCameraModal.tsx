import React, { useState } from 'react';
import { Image, Modal } from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { Button, H8, Icon } from 'cube-ui-components';
import styled from '@emotion/native';
import { ricoAvatar } from 'features/ecoach/assets';
import { Camera } from 'expo-camera';

const Container = styled.View(() => ({
  flex: 1,
  justifyContent: 'center',
  backgroundColor: 'rgba(24, 48, 40, 0.7)',
}));

const CenterView = styled.View(() => ({
  backgroundColor: 'white',
  justifyContent: 'center',
  borderRadius: sizes[3],

  padding: sizes[4],
  margin: sizes[4],
}));

const ContentView = styled.View(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  paddingVertical: sizes[2],
  paddingHorizontal: sizes[5],
  gap: sizes[2],
}));

const ImgView = styled.View(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'center',
  gap: sizes[2],
}));

const TurnOnCameraModal = () => {
  const [modalVisible, setModalVisible] = useState(true);
  const { t } = useTranslation('ecoach');
  const [permission, requestPermission] = Camera.useCameraPermissions();
  if (permission && permission.granted) return null;

  const pressRequestPermission = () => {
    requestPermission();
    setModalVisible(false);
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}>
      <Container>
        <CenterView>
          <ImgView>
            <Image source={ricoAvatar} />
            <Icon.Camera fill={colors.fwdDarkGreen[100]} size={sizes[10]} />
          </ImgView>
          <ContentView>
            <H8 fontWeight="bold" color={colors.black}>
              {t('turnOnCamera')}
            </H8>
            <H8
              fontWeight="normal"
              color={colors.black}
              style={{ textAlign: 'center' }}>
              {t('toTalkWithRico')}
            </H8>
          </ContentView>

          <Button
            variant={'primary'}
            onPress={pressRequestPermission}
            text={t('yesIAmOkayUsingCamera')}
          />
        </CenterView>
      </Container>
    </Modal>
  );
};

export default TurnOnCameraModal;
