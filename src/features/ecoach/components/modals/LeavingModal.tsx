import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, Modal } from 'react-native';
import { useTranslation } from 'react-i18next';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { H8, PictogramIcon } from 'cube-ui-components';
import styled from '@emotion/native';

const { height: windowHeight } = Dimensions.get('window');

type LeavingModalProps = {
  avatarName: string;
  autoDismissed?: boolean;
  leavingMessage?: string;
  onClose: () => void;
};

const Container = styled.View(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  width: '100%',
  height: 80,
  top: windowHeight - 300,
  position: 'absolute'
}));

const CenterView = styled.View(() => ({
  flexDirection: 'row',
  backgroundColor: 'white',
  justifyContent: 'flex-start',
  borderRadius: sizes[3],
  paddingVertical: sizes[3],
  paddingHorizontal: sizes[5],
  marginHorizontal: sizes[4],
  maxWidth: 440,
}));

const ContentView = styled.View(() => ({
  marginLeft: sizes[4],
}));

const LeavingModal = ({ avatarName, onClose, leavingMessage, autoDismissed = true }: LeavingModalProps) => {
  const [modalVisible, setModalVisible] = useState(true);
  const { t } = useTranslation('ecoach');
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Effect for auto-closing the modal
  useEffect(() => {
    if (modalVisible && autoDismissed) {
      // Set a timer to close the modal after 3 seconds
      timerRef.current = setTimeout(() => {
        setModalVisible(false);
        onClose?.();
      }, 10000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [modalVisible, autoDismissed]);

  return (
    <Container>
      <CenterView>
        <PictogramIcon.TimeTimer size={64} />
        <ContentView>
          <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
            {avatarName}
            {t('leaving')}
          </H8>
          <H8 fontWeight="medium" color={colors.fwdDarkGreen[100]}>
            {leavingMessage || t('minuteLeft')}
          </H8>
          <H8 fontWeight="medium" color={colors.fwdDarkGreen[100]}>
            {t('wrapUp')}
          </H8>
        </ContentView>
      </CenterView>
    </Container>
  );
};

export default LeavingModal;
