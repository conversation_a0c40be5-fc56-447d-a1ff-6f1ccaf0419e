import Svg, { Mask, Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export const Star = (props: SvgIconProps) => {
  const { fill, width, height, size } = props;
  return (
    <Svg
      width={width || size || 24}
      height={height || size || 24}
      viewBox={`0 0 ${width || size || 24} ${width || size || 24}`}
      fill="none"
      {...props}>
      <Mask id="path-1-inside-1_4050_334136" fill="white">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M16.9225 21.116C16.7655 21.116 16.6075 21.079 16.4625 21.004L11.6045 18.489L6.74646 21.004C6.40946 21.178 6.00196 21.147 5.69546 20.922C5.38896 20.6975 5.23646 20.3185 5.30146 19.9445L6.22596 14.638L2.30796 10.877C2.03196 10.612 1.93096 10.213 2.04796 9.8495C2.16546 9.485 2.47996 9.22 2.85846 9.166L8.28596 8.389L10.7105 3.5515C10.8805 3.2135 11.226 3 11.6045 3C11.983 3 12.329 3.2135 12.4985 3.5515L14.923 8.389L20.35 9.166C20.7285 9.22 21.043 9.485 21.1605 9.849C21.2775 10.213 21.177 10.612 20.901 10.8765L16.9835 14.638L17.908 19.9445C17.973 20.3185 17.8205 20.6975 17.514 20.922C17.339 21.0505 17.1315 21.116 16.9225 21.116Z"
        />
      </Mask>
      <Path
        d="M16.4625 21.004L16.0027 21.8921L16.003 21.8922L16.4625 21.004ZM11.6045 18.489L12.0642 17.601L11.6045 17.3629L11.1447 17.601L11.6045 18.489ZM6.74646 21.004L7.20524 21.8926L7.20621 21.8921L6.74646 21.004ZM5.69546 20.922L6.28722 20.1159L6.28637 20.1153L5.69546 20.922ZM5.30146 19.9445L4.3163 19.7729L4.31623 19.7733L5.30146 19.9445ZM6.22596 14.638L7.21112 14.8096L7.30249 14.2852L6.91847 13.9166L6.22596 14.638ZM2.30796 10.877L1.61537 11.5983L1.61546 11.5984L2.30796 10.877ZM2.04796 9.8495L1.09619 9.54269L1.09606 9.54311L2.04796 9.8495ZM2.85846 9.166L2.9997 10.156L3.00018 10.1559L2.85846 9.166ZM8.28596 8.389L8.42768 9.37891L8.94555 9.30477L9.17996 8.83706L8.28596 8.389ZM10.7105 3.5515L9.81709 3.10217L9.81646 3.10344L10.7105 3.5515ZM12.4985 3.5515L13.3925 3.10344L13.3924 3.10323L12.4985 3.5515ZM14.923 8.389L14.029 8.83706L14.2634 9.30476L14.7812 9.37891L14.923 8.389ZM20.35 9.166L20.2082 10.1559L20.2087 10.156L20.35 9.166ZM21.1605 9.849L22.1125 9.54299L22.1121 9.54181L21.1605 9.849ZM20.901 10.8765L20.2091 10.1545L20.2084 10.1552L20.901 10.8765ZM16.9835 14.638L16.2909 13.9167L15.907 14.2853L15.9983 14.8096L16.9835 14.638ZM17.908 19.9445L18.8932 19.7733L18.8931 19.7729L17.908 19.9445ZM17.514 20.922L16.9231 20.1153L16.9221 20.116L17.514 20.922ZM16.9225 20.116L16.9219 20.1158L16.003 21.8922C16.2926 22.042 16.6085 22.116 16.9225 22.116V20.116ZM16.9222 20.116L12.0642 17.601L11.1447 19.3771L16.0027 21.8921L16.9222 20.116ZM11.1447 17.601L6.28672 20.116L7.20621 21.8921L12.0642 19.3771L11.1447 17.601ZM6.28768 20.1154L6.28722 20.1159L5.1037 21.7281C5.71757 22.1788 6.53219 22.2401 7.20524 21.8926L6.28768 20.1154ZM6.28637 20.1153L6.28669 20.1157L4.31623 19.7733C4.18623 20.5213 4.49109 21.2794 5.10455 21.7287L6.28637 20.1153ZM6.28662 20.1161L7.21112 14.8096L5.2408 14.4664L4.3163 19.7729L6.28662 20.1161ZM6.91847 13.9166L3.00047 10.1556L1.61546 11.5984L5.53346 15.3594L6.91847 13.9166ZM3.00055 10.1557L2.99987 10.1559L1.09606 9.54311C0.861564 10.2716 1.06435 11.0693 1.61537 11.5983L3.00055 10.1557ZM2.99973 10.1563L2.9997 10.156L2.71722 8.17603C1.95991 8.28407 1.33095 8.81444 1.09619 9.54269L2.99973 10.1563ZM3.00018 10.1559L8.42768 9.37891L8.14425 7.39909L2.71675 8.17609L3.00018 10.1559ZM9.17996 8.83706L11.6045 3.99956L9.81646 3.10344L7.39196 7.94094L9.17996 8.83706ZM11.6038 4.00083C11.6042 4.00013 11.6043 4.00011 11.6045 3.99997C11.6047 3.99986 11.6043 4 11.6045 4V2C10.8472 2 10.1565 2.42735 9.81709 3.10217L11.6038 4.00083ZM11.6045 4L11.6046 3.99977L13.3924 3.10323C13.0531 2.42674 12.361 2 11.6045 2V4ZM11.6045 3.99956L14.029 8.83706L15.817 7.94094L13.3925 3.10344L11.6045 3.99956ZM14.7812 9.37891L20.2082 10.1559L20.4917 8.1761L15.0647 7.39909L14.7812 9.37891ZM20.2087 10.156L20.2088 10.1562L22.1121 9.54181C21.8772 8.81416 21.2483 8.28404 20.4912 8.17603L20.2087 10.156ZM20.2084 10.155L20.2091 10.1545L21.5929 11.5985C22.1458 11.0686 22.3463 10.2703 22.1125 9.54299L20.2084 10.155ZM20.2084 10.1552L16.2909 13.9167L17.6761 15.3593L21.5936 11.5978L20.2084 10.1552ZM15.9983 14.8096L16.9228 20.1161L18.8931 19.7729L17.9686 14.4664L15.9983 14.8096ZM16.9227 20.1157L16.9231 20.1153L18.1049 21.7287C18.7183 21.2794 19.0232 20.5213 18.8932 19.7733L16.9227 20.1157ZM16.9221 20.116L16.9225 20.116V22.116C17.3406 22.116 17.7562 21.9847 18.1058 21.728L16.9221 20.116Z"
        fill={fill || '#FF9B0A'}
        mask="url(#path-1-inside-1_4050_334136)"
      />
    </Svg>
  );
};
