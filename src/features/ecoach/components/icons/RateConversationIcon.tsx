import React from 'react';
import { SvgIconProps } from 'cube-ui-components';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';
import { useTheme } from '@emotion/react';

function RateConversationIcon(props: SvgIconProps): JSX.Element {
  const theme = useTheme();
  return (
    <Svg
      width={props.width || props.size || 24}
      height={props.height || props.size || 24}
      viewBox="0 0 24 24"
      fill="none">
      <G clip-path="url(#clip0_4156_6025)">
        <Path
          d="M22.0503 2H13.3403C12.9203 2 12.5703 2.35 12.5703 2.77V10.19C12.5703 10.69 12.8603 11.13 13.3203 11.31C13.4603 11.38 13.6203 11.41 13.8003 11.41C14.1303 11.41 14.4303 11.29 14.6603 11.05L15.8103 9.9H22.0503C22.4703 9.9 22.8103 9.56 22.8103 9.14V2.77C22.8103 2.35 22.4703 2 22.0503 2ZM20.8203 7.92H15.3003C15.0903 7.92 14.8903 8 14.7703 8.14L14.5703 8.34V3.98H20.8203V7.92Z"
          fill={props.fill || theme.colors.primary}
        />
        <Path
          d="M16.93 13.1909C16.46 12.6209 15.76 12.3009 15.03 12.3009H11.81V9.64094C11.81 7.97094 10.14 7.21094 8.6 7.21094C8.06 7.21094 7.62 7.65094 7.62 8.19094C7.62 9.63094 7.35 10.9409 7.17 11.1209L5.22 13.0209H1.98C1.44 13.0209 1 13.4609 1 14.0009V21.2609C1 21.8009 1.44 22.2409 1.98 22.2409H14.09C15.26 22.2409 16.28 21.4109 16.5 20.2509L17.45 15.1609C17.58 14.4609 17.39 13.7409 16.93 13.1909ZM2.96 20.2909V14.9709H4.64V20.2909H2.96ZM14.59 19.8909C14.54 20.1309 14.32 20.2809 14.08 20.2909H6.59V14.4109L8.53 12.5209C9.18 11.8909 9.44 10.5009 9.53 9.34094C9.7 9.41094 9.86 9.51094 9.86 9.64094V13.2809C9.86 13.8109 10.3 14.2509 10.83 14.2509H15.03C15.19 14.2509 15.33 14.3209 15.44 14.4409C15.52 14.5409 15.56 14.6809 15.53 14.8109L14.59 19.8909Z"
          fill={props.fill || theme.colors.primary}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_4156_6025">
          <Rect
            width="21.81"
            height="20.24"
            fill="white"
            transform="translate(1 2)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RateConversationIcon;
