import React from 'react';
import { ImageBackground, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { entryPointBg, tabletEntryPointBg } from 'features/ecoach/assets';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const Card = styled(View)(({ theme }) => ({
  width: '100%',
  backgroundColor: theme.colors.surface,
  borderRadius: theme.borderRadius.large,
  height: 222,
  overflow: 'hidden',
  position: 'relative',
}));

const BGImg = styled(ImageBackground)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));

const EcoachEntryPointCard = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <Card>
      <TouchableOpacity onPress={() => navigation.navigate('EcoachHome')}>
        <BGImg source={isTabletMode ? tabletEntryPointBg : entryPointBg} />
      </TouchableOpacity>
    </Card>
  );
};

export default EcoachEntryPointCard;
