import React, { useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { H3, H8, Icon, Label, SmallLabel } from 'cube-ui-components';
import { format, parseISO } from 'date-fns';
import { ConversationData } from 'features/ecoach/api/conversationApi';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from 'types';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { extractScore } from 'features/ecoach/ultils/extractScore';
import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';

const Card = styled.View<{ isTabletMode: boolean }>(({ isTabletMode }) => ({
  width: '100%',
  backgroundColor: colors.white,
  borderRadius: sizes[3],
  borderWidth: 1,
  borderColor: colors.fwdOrange[50],
  height: isTabletMode ? 'auto' : 109,
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[3],
}));

const HorizontalView = styled.View(() => ({
  width: '100%',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const ProductText = styled(SmallLabel)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    maxWidth: isTabletMode ? 'auto' : 100,
  }),
);

const ContentView = styled.View(() => ({
  width: '45%',
  flexDirection: 'row',
  justifyContent: 'space-between',
}));

const HighlightedScoreText = styled(H3)(() => ({
  textAlign: 'center',
}));

type SessionCardProps = {
  session: ConversationData;
  sessionNumber: number | undefined;
};

const SessionCard = ({ session, sessionNumber }: SessionCardProps) => {
  const { conversation_id, datetime, difficulty, product_selection, report } =
    session;
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();
  const score = extractScore(report?.overall_score);
  const productConfig = useBoundStore(state => state.ecoach.productConfig);

  const productName = useMemo(() => {
    const productForCurrentCountry = productConfig?.[country];

    return (
      productForCurrentCountry?.find(e => e.product_code === product_selection)
        ?.product_name ||
      product_selection
        ?.split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    );
  }, [product_selection, productConfig, country]);

  return (
    <Card isTabletMode={isTabletMode}>
      <TouchableOpacity
        onPress={() =>
          navigation.push(isTabletMode ? 'SummaryTablet' : 'Summary', {
            conversationId: conversation_id,
            session: session,
          })
        }>
        <HorizontalView>
          <View>
            <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
              {t('ssID')} {sessionNumber || ''}
            </H8>
            <Label fontWeight="normal" color={colors.fwdDarkGreen[50]}>
              {format(parseISO(datetime), 'dd MMMM')}
            </Label>
          </View>
          <Icon.ChevronRight fill={colors.fwdOrange[50]} size={18} />
        </HorizontalView>

        <HorizontalView>
          <Label
            fontWeight={'bold'}
            color={colors.fwdDarkGreen[50]}
            style={{ textAlign: 'center' }}>
            <HighlightedScoreText
              fontWeight={'bold'}
              color={score < 60 ? colors.alertRed : colors.fwdDarkGreen[100]}>
              {score}{' '}
            </HighlightedScoreText>
            / 100
          </Label>
          <ContentView>
            <View>
              <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                {t('selectPolicy')}
              </SmallLabel>
              {productName && (
                <ProductText
                  isTabletMode={isTabletMode}
                  fontWeight="bold"
                  color={colors.fwdDarkGreen[100]}>
                  {productName}
                </ProductText>
              )}
            </View>
            <View>
              <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                {t('selectDifficulty')}
              </SmallLabel>
              <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                {difficulty === '1' ? t('beginner') : t('expert')}
              </SmallLabel>
            </View>
          </ContentView>
        </HorizontalView>
      </TouchableOpacity>
    </Card>
  );
};

export default React.memo(SessionCard);
