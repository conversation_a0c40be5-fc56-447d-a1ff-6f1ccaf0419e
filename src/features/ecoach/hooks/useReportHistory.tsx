import { InfiniteData, useInfiniteQuery } from '@tanstack/react-query';
import {
  ConversationData,
  ConversationHistoryResponse,
  ConversationType,
  getConversationHistoryByPaging,
} from 'features/ecoach/api/conversationApi';

export const SESSION_HISTORY_CONVERSATION_HISTORY_V_2_CACHE_KEY =
  'SessionHistoryConversationHistoryV2';

export const isReportReady = (report: ConversationData) => true; // report.report_is_ready === 'true';

export const selectPage = (data: InfiniteData<ConversationHistoryResponse>) => {
  const countPrevious = (
    pages: ConversationHistoryResponse[],
    pageIndex: number,
  ) => {
    return pages.reduce(
      (prev, p, index) =>
        index < pageIndex
          ? prev + p.items.filter(e => isReportReady(e)).length
          : prev,
      0,
    );
  };

  return {
    ...data,
    pages: (data.pages || []).map((page, pageIndex) => ({
      ...page,
      items: page.items
        .filter(e => isReportReady(e))
        .map((e, index) => ({
          ...e,
          session_number: Math.max(
            0,
            data.pages[0].total_count -
              countPrevious(data.pages, pageIndex) -
              index,
          ),
        })),
    })),
  };
};

export function useReportHistory(
  enabled: boolean,
  conversationType = ConversationType.CUSTOMER,
) {
  return useInfiniteQuery({
    queryKey: [
      `${SESSION_HISTORY_CONVERSATION_HISTORY_V_2_CACHE_KEY}-${conversationType}`,
    ],
    queryFn: ({ pageParam = '' }) =>
      getConversationHistoryByPaging(100, conversationType, pageParam),
    getNextPageParam: lastPage => {
      return lastPage.has_more
        ? encodeURIComponent(lastPage.last_evaluated_key)
        : undefined;
    },
    select: selectPage,
    cacheTime: 0,
    staleTime: 0,
    enabled,
  });
}
