import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import InCallManager from 'react-native-incall-manager';
import WebRTC from '../../components/WebRTC';
import {
  NavigationProp,
  RouteProp,
  useIsFocused,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import VideoStreaming from '../../components/VideoStreaming';
import { customAlphabet } from 'nanoid/non-secure';
import {
  getWebSocketParam,
  measureDownloadSpeed,
} from 'features/ecoach/ultils/videoCallHelper';
import {
  emptyStatus,
  end,
  heart,
  heartAni,
  heartBroken,
  mic,
  mutedMic,
  stageEightStatus,
  stageFiveStatus,
  stageFourStatus,
  stageOneStatus,
  stageSevenStatus,
  stageSixStatus,
  stageTwoStatus,
} from 'features/ecoach/assets';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { EcoachParamList, RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { country, ecoachPublicConversationWebsocketUrl } from 'utils/context';
import CircularProgressTimer, {
  DEFAULT_CALL_TIME,
  FIVE_MIN_CALL_TIME,
} from 'features/ecoach/components/CircularProgressTimer';
import CountdownCircleModal from 'features/ecoach/components/modals/CountdownCircleModal';
import LottieView from 'lottie-react-native';
import MissionCompleteModal from 'features/ecoach/components/modals/MissionCompleteModal';
import MissionFailModal from 'features/ecoach/components/modals/MissionFailModal';
import AvatarNotReady from '../../components/AvatarNotReady';
import styled from '@emotion/native';
import useBoundStore from 'hooks/useBoundStore';
import { DifficultType, MissionCompleteScore } from '../SelectDifficulty';
import { H6, H7 } from 'cube-ui-components';
import LeavingModal from 'features/ecoach/components/modals/LeavingModal';
import LeavingSoSoonModal from 'features/ecoach/components/modals/LeavingSoSoonModal';
import WelcomeTooltipModal from 'features/ecoach/components/modals/WelcomeTooltipModal';
import LowBandwidthModal from 'features/ecoach/components/modals/LowBandwidthModal';
import NoInternetModal from 'features/ecoach/components/modals/NoInternetModal';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { checkAvatarAvailability } from 'features/ecoach/api/conversationApi';
import useConnectionStatus from 'features/ecoach/hooks/useConnectionStatus';
import { loginWithRefreshToken } from 'features/login/commonLogic';
import { ProductFlowType } from 'features/ecoach/store/ecouchSlice';
import { Audio, InterruptionModeAndroid, InterruptionModeIOS } from 'expo-av';
import AppointmentCallPage from 'features/ecoach/screens/call/AppointmentCallPage';
import AppointmentStreaming from 'features/ecoach/components/AppointmentStreaming';
import { StatusBar } from 'expo-status-bar';

InCallManager.stopProximitySensor();
InCallManager.setSpeakerphoneOn(true);
InCallManager.start({ media: 'audio' });

const { width } = Dimensions.get('window');

const HeaderView = styled(View)(() => ({
  flexDirection: 'row',
  paddingLeft: sizes[4],
  paddingTop: sizes[4],
  justifyContent: 'flex-start',
  width: '100%',
}));

const StatusBarImage = styled(Image)(() => ({
  width: 180,
  height: 21,
}));

const HeartImage = styled(Image)(() => ({
  width: 20,
  height: 17,
  marginLeft: 10,
  marginTop: sizes[1],
}));

const LottieViewContainer = styled(View)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1000,
  pointerEvents: 'none',
}));

const LottieViewElement = styled(LottieView)(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 1000,
  pointerEvents: 'none',
}));

const BrokenHeartView = styled(View)(() => ({
  position: 'absolute',
  zIndex: 999,
  top: 150,
  width: 100,
  height: 100,
  right: (width - 60) / 2,
}));

const ContentView = styled(View)(() => ({
  flex: 1,
  paddingTop: sizes[10],
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const BottomView = styled(View)(() => ({
  justifyContent: 'space-around',
  alignItems: 'center',
  width: '100%',
}));

const GroupButtonView = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  marginBottom: sizes[13],
  gap: sizes[4],
}));

const ButtonImage = styled(Image)(() => ({
  width: sizes[16],
  height: sizes[16],
}));

const NameStyle = styled(H6)(() => ({
  color: colors.white,
  marginBottom: sizes[7],
}));

const ThinkingStyle = styled(H7)(() => ({
  color: colors.white,
  width: '100%',
  textAlign: 'center',
  fontStyle: 'italic',
}));

const VideoCallWithAvatarPage = () => {
  const { t } = useTranslation('ecoach');
  const { data: agentProfile } = useGetAgentProfile();
  const quickfireCompletedHearts = useBoundStore(
    state => state.ecoach.quickfireCompletedHearts,
  );
  const [websocket, setWebSocket] = useState<WebSocket | null>(null);
  const [muted, setMuted] = useState(false);
  const [avatarId, setAvatarId] = useState<string | null>(null);
  const [avatarIsReady, setAvatarIsReady] = useState(false);
  const [avatarServerAvailabile, setAvatarServerAvailabile] = useState(true);
  const [thinking, setThinkingState] = useState(false);
  const [heartScore, setHeartScore] = useState(0);
  const [endCallFlag, setEndCallFlag] = useState(false);
  const [previousHeartScore, setPreviousHeartScore] = useState(0);
  const [avatarName, setAvatarName] = useState('Rico');
  const [leavingModalVisible, setLeavingModalVisible] = useState(false);
  const [welcomeTooltipVisible, setWelcomeTooltipVisible] = useState(false);
  const [conversationId, setConversationId] = useState('');
  const [showCountDownCircle, setShowCountDownCircle] = useState(true);
  const [showFailModal, setShowFailModal] = useState(false);
  const [showTimeOutModal, setShowTimeOutModal] = useState(false);
  const heartFloatingRef = useRef<LottieView>(null);
  const heartbrokenRef = useRef<LottieView>(null);
  const [showHeartBroken, setShowHeartBroken] = useState(false);
  const [reTryTime, setReTryTime] = useState(0);
  const [isLowBandwidth, setIsLowBandwidth] = useState(false);
  const [showLowBandwidthModal, setShowLowBandwidthModal] = useState(false);
  // Get the internet speed limit from the global state
  const videoToAudioSpeed = useBoundStore(
    state => state.ecoach.videoToAudioSpeed,
  );
  // Get connection status
  const { isConnected } = useConnectionStatus();
  // console.log('VideoCallWithAvatarPage isConnected', isConnected);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const isFocused = useIsFocused();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { params } = useRoute<RouteProp<EcoachParamList, 'VideoCallPage'>>();
  const { accessToken, refreshToken } = useBoundStore(
    state => state.auth.authInfo,
  );
  const login = useBoundStore(state => state.authActions.login);
  const [totalTime, setTotalTime] = useState(
    params.productFlowType === ProductFlowType.QUICKFIRE
      ? FIVE_MIN_CALL_TIME
      : DEFAULT_CALL_TIME,
  );

  const setupWebSocket = async () => {
    const avatarServerAvailabile = await checkAvatarAvailability();
    if (!avatarServerAvailabile) {
      setAvatarServerAvailabile(false);

      setTimeout(() => {
        navigation.navigate('EcoachHome');
      }, 3000);

      return;
    }

    const conversationId = customAlphabet(
      'abcdefghijklmnopqrstuvwxyz0123456789',
      21,
    )();

    setConversationId(conversationId);

    const openWebSocket = async (token: string | null) => {
      // console.log('VideoCallPage openWebSocket token:', token);

      const url = `${ecoachPublicConversationWebsocketUrl.replace(
        'conversation',
        country === 'my' ? 'conversation_v2' : 'conversation',
      )}${conversationId}?access_token=${token}&country=${country}`;
      // console.log(`wss url`, url);
      const ws = new WebSocket(url);

      const param = await getWebSocketParam({
        ...params,
        agentName: agentProfile?.person?.firstName || 'Meggie',
        country,
      });

      // console.log('openWebSocket params', params);
      // console.log('openWebSocket param', param);
      ws.onopen = async () => {
        setWebSocket(ws);
        setWelcomeTooltipVisible(true);
        ws.send(JSON.stringify({ type: 'setup', param }));
      };

      ws.onclose = async event => {
        websocket && setWebSocket(null);
        // console.log(`WebSocket connection closed: `, JSON.stringify(event));
        // Handle 403 error (Forbidden) for token expiration
        if (event.code === 1008 || event.reason.includes('403')) {
          const loginWithRefreshRes = await loginWithRefreshToken(
            refreshToken || '',
          );
          if (loginWithRefreshRes.status !== 200) {
            // console.error('Failed to refresh token');
            return;
          }
          const { access_token, refresh_token, id_token } =
            loginWithRefreshRes.data;
          login(access_token, refresh_token, id_token);
          // Reopen WebSocket with the new access token
          openWebSocket(access_token);
        }
      };
    };
    openWebSocket(accessToken);
  };

  const resetTotalTime = () => {
    setTotalTime(
      params.productFlowType === ProductFlowType.QUICKFIRE
        ? FIVE_MIN_CALL_TIME
        : DEFAULT_CALL_TIME,
    );
  };

  const cleanUp = () => {
    setConversationId('');
    setEndCallFlag(false);
    setShowFailModal(false);
    setShowTimeOutModal(false);
    setAvatarIsReady(false);
    setHeartScore(0);
    setIsLowBandwidth(false);
    setShowLowBandwidthModal(false);
    websocket?.close();
    setWebSocket(null);
    setWelcomeTooltipVisible(false);
    resetTotalTime();
  };

  const reTake = () => {
    cleanUp();
    setShowCountDownCircle(true);
    setReTryTime(reTryTime + 1);
    setupWebSocket();
  };

  const goToHome = () => {
    cleanUp();
    navigation.navigate('EcoachHome');
  };

  const endCallAndGoReport = () => {
    cleanUp();
    navigation.navigate(isTabletMode ? 'SummaryTablet' : 'Summary', {
      ...params,
      conversationId,
      reTake,
    });
  };

  const tryFullSessionExperience = () => {
    if (isLowScore) {
      reTake();
    } else {
      cleanUp();
      navigation.navigate('SelectPolicy', {
        ...params,
        productFlowType: ProductFlowType.FULL_EXPERIENCE,
      });
    }
  };

  const isQuickfireProduct = useMemo(
    () => params.productFlowType === ProductFlowType.QUICKFIRE,
    [params.productFlowType],
  );

  const isLowScore = useMemo(() => {
    return heartScore < quickfireCompletedHearts;
  }, [heartScore, quickfireCompletedHearts]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      !conversationId && setupWebSocket();
    });
    return () => {
      cleanUp();
      unsubscribe();
    };
  }, [navigation, params]);

  useEffect(() => {
    if (!isFocused) {
      cleanUp();
    }
  }, [isFocused]);

  useEffect(() => {
    if (!conversationId) return;

    if (heartScore - previousHeartScore >= 5) {
      heartFloatingRef.current?.play(0);
    }
    if (previousHeartScore - heartScore >= 5) {
      heartbrokenRef.current?.play(0);
      setShowHeartBroken(true);
      setTimeout(() => {
        setShowHeartBroken(false);
      }, 1500);
    }
    setPreviousHeartScore(heartScore);
  }, [conversationId, heartScore, previousHeartScore]);

  useEffect(() => {
    InCallManager.setForceSpeakerphoneOn(true);
    const setup = async () => {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        interruptionModeIOS: InterruptionModeIOS.DuckOthers,
        shouldDuckAndroid: true,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
        playThroughEarpieceAndroid: false,
      });
    };
    if (Platform.OS === 'ios') {
      setup();
    }
  }, []);

  useEffect(() => {
    // Set up a timeout to run checkInternetSpeed after the initial delay
    const timeoutId = setTimeout(async () => {
      const speed = await measureDownloadSpeed();
      if (!speed) return;
      if (!isLowBandwidth && speed < videoToAudioSpeed) {
        setIsLowBandwidth(true);
        setShowLowBandwidthModal(true);
      }
    }, 15000);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [isLowBandwidth, videoToAudioSpeed]);

  const getStatusBarImage = () => {
    if (params.difficultType === DifficultType.Beginner) {
      if (heartScore >= 80) {
        return stageEightStatus;
      } else if (heartScore >= 70) {
        return stageSevenStatus;
      } else if (heartScore >= 60) {
        return stageSixStatus;
      } else if (heartScore >= 50) {
        return stageFiveStatus;
      } else if (heartScore >= 40) {
        return stageFourStatus;
      } else if (heartScore >= 30) {
        return stageTwoStatus;
      } else if (heartScore >= 20) {
        return stageOneStatus;
      } else {
        return emptyStatus;
      }
    } else {
      if (heartScore >= 140) {
        return stageEightStatus;
      } else if (heartScore >= 122) {
        return stageSevenStatus;
      } else if (heartScore >= 105) {
        return stageSixStatus;
      } else if (heartScore >= 87) {
        return stageFiveStatus;
      } else if (heartScore >= 52) {
        return stageFourStatus;
      } else if (heartScore >= 35) {
        return stageTwoStatus;
      } else if (heartScore >= 17) {
        return stageOneStatus;
      } else {
        return emptyStatus;
      }
    }
  };

  if (!avatarServerAvailabile) {
    return <AvatarNotReady />;
  }

  const iBFullExp =
    country === 'ib' &&
    params.productFlowType === ProductFlowType.FULL_EXPERIENCE;

  return (
    <View style={{ flex: 1 }}>
      <StatusBar hidden />
      {showCountDownCircle && (
        <CountdownCircleModal
          initialCount={5}
          onComplete={() => setShowCountDownCircle(false)}
          visible={showCountDownCircle}
        />
      )}
      {!iBFullExp && (
        <LottieViewContainer pointerEvents={'none'}>
          <LottieViewElement
            ref={heartFloatingRef}
            source={heartAni}
            autoPlay={false}
            loop={false}
            resizeMode="cover"
            speed={3}
          />
        </LottieViewContainer>
      )}
      {showHeartBroken && !iBFullExp && (
        <BrokenHeartView pointerEvents={'none'}>
          <LottieViewElement
            ref={heartbrokenRef}
            source={heartBroken}
            autoPlay={true}
            loop={false}
            resizeMode="cover"
            speed={3}
          />
        </BrokenHeartView>
      )}

      {!isQuickfireProduct && showTimeOutModal && (
        <MissionFailModal reTake={reTake} onExitRole={endCallAndGoReport} />
      )}

      {isQuickfireProduct && leavingModalVisible && (
        <LeavingModal
          avatarName={avatarName}
          leavingMessage={t('secondsLeft')}
          onClose={() => setLeavingModalVisible(false)}
        />
      )}
      {isQuickfireProduct && welcomeTooltipVisible && !showCountDownCircle && (
        <WelcomeTooltipModal
          avatarName={avatarName}
          onClose={() => setWelcomeTooltipVisible(false)}
        />
      )}

      {!isQuickfireProduct &&
        params.difficultType === DifficultType.Beginner && (
          <View>
            {endCallFlag && heartScore >= MissionCompleteScore.Beginner && (
              <MissionCompleteModal endCall={endCallAndGoReport} />
            )}
            {showFailModal && heartScore >= MissionCompleteScore.Beginner && (
              <MissionCompleteModal endCall={endCallAndGoReport} />
            )}
            {showFailModal && heartScore < MissionCompleteScore.Beginner && (
              <MissionFailModal
                reTake={reTake}
                onExitRole={endCallAndGoReport}
              />
            )}
          </View>
        )}
      {!isQuickfireProduct && params.difficultType === DifficultType.Expert && (
        <View>
          {endCallFlag && heartScore >= MissionCompleteScore.Expert && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore >= MissionCompleteScore.Expert && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore < MissionCompleteScore.Expert && (
            <MissionFailModal reTake={reTake} onExitRole={endCallAndGoReport} />
          )}
        </View>
      )}

      {isQuickfireProduct && (
        <View>
          {(endCallFlag || showTimeOutModal) && (
            <MissionCompleteModal
              isQuickfireProduct={true}
              showButton={true}
              primaryButtonText={
                isLowScore ? t('letsTryAgain') : t('letsTryTheSalesRoleplay')
              }
              secondaryButtonText={t('exitQuickfire')}
              onOkButton={tryFullSessionExperience}
              onExitRole={goToHome}
              score={heartScore}
              missCompleteScore={quickfireCompletedHearts}
            />
          )}
        </View>
      )}
      {isQuickfireProduct && showFailModal && (
        <LeavingSoSoonModal
          avatarName={avatarName}
          reTake={reTake}
          onExitRole={goToHome}
        />
      )}

      {websocket && (
        <WebRTC
          ws={websocket}
          muted={muted}
          productFlowType={params.productFlowType}
          productSelectionCode={params.productSelectionCode}
          isTimeOut={showTimeOutModal}
          setAvatarIsReady={setAvatarIsReady}
          setThinkingState={setThinkingState}
          setHeartScore={setHeartScore}
          setEndCallFlag={setEndCallFlag}
          setAvatarName={setAvatarName}
          setAvatarId={setAvatarId}
        />
      )}
      {avatarIsReady && avatarId && !isLowBandwidth && (
        <VideoStreaming
          isFocused
          isTimeOut={showTimeOutModal}
          avatarId={avatarId || ''}
        />
      )}

      {isLowBandwidth && <AppointmentStreaming />}

      {showLowBandwidthModal && (
        <LowBandwidthModal
          visible={showLowBandwidthModal}
          onClose={() => setShowLowBandwidthModal(false)}
        />
      )}

      <NoInternetModal visible={!isConnected} onExit={goToHome} />

      {/*Temporarily removed -> Showing in the next release*/}
      {/*<ProfileModal />*/}

      <ContentView>
        <HeaderView>
          {!iBFullExp && <StatusBarImage source={getStatusBarImage()} />}
          {!iBFullExp && <HeartImage source={heart} />}
        </HeaderView>
        <BottomView>
          {thinking && (
            <View>
              <ActivityIndicator size="large" color="#0894B3" />
              <ThinkingStyle>
                {avatarName} {t('isThinking')}
              </ThinkingStyle>
            </View>
          )}
          {avatarName && (
            <NameStyle fontWeight={'bold'}>{avatarName}</NameStyle>
          )}
          <GroupButtonView>
            <CircularProgressTimer
              setLeavingModalVisible={setLeavingModalVisible}
              setTimeOutStatus={setShowTimeOutModal}
              timeRemainingNotification={isQuickfireProduct ? 30 : 60}
              totalTime={totalTime}
              reTryTime={reTryTime}
            />
            <TouchableOpacity onPress={() => setMuted(mute => !mute)}>
              <ButtonImage source={muted ? mutedMic : mic} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setShowFailModal(true)}>
              <ButtonImage source={end} />
            </TouchableOpacity>
          </GroupButtonView>
        </BottomView>
      </ContentView>
    </View>
  );
};

const VideoCallPage = () => {
  const { params } = useRoute<RouteProp<EcoachParamList, 'VideoCallPage'>>();
  if (params.productFlowType === ProductFlowType.APPOINTMENT) {
    return <AppointmentCallPage />;
  } else {
    return <VideoCallWithAvatarPage />;
  }
};

export default VideoCallPage;
