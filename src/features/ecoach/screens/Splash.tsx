import React from 'react';
import { Image, TouchableOpacity, View } from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { arrowRight, closeIcon, fwdLogo } from 'features/ecoach/assets';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { EcoachParamList, RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { H6, H7, H8 } from 'cube-ui-components';

const PageContainer = styled(View)(() => ({
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
}));

const ImageContainer = styled(View)(() => ({
  paddingBottom: '40%',
  alignItems: 'center',
}));

const CloseButton = styled(TouchableOpacity)(() => ({
  position: 'absolute',
  top: 47,
  left: 20,
  zIndex: 1,
}));

const CloseIcon = styled(Image)(() => ({
  width: 30,
  height: 30,
}));

const FwdLogo = styled(Image)(() => ({
  width: 90,
  height: 29,
}));

const AppDescriptionText = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingTop: sizes[5],
  width: 264,
  textAlign: 'center',
}));

const BottomContainer = styled(TouchableOpacity)(() => ({
  paddingBottom: sizes[10],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: '10%',
}));

const LetsGoText = styled(H7)(() => ({
  color: colors.fwdOrange[100],
  textAlign: 'center',
  paddingEnd: sizes[2],
}));

const ArrowImage = styled(Image)(() => ({
  width: 45,
  height: 45,
}));

const SplashPage = () => {
  const { t } = useTranslation('ecoach');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { params } = useRoute<RouteProp<EcoachParamList, 'Splash'>>();

  const goUserProfilePage = () => {
    navigation.navigate('UserProfile', params);
  };

  const handleClose = () => {
    navigation.navigate('EcoachHome');
  };

  return (
    <PageContainer>
      <CloseButton onPress={handleClose}>
        <CloseIcon source={closeIcon} />
      </CloseButton>
      <ImageContainer>
        <FwdLogo source={fwdLogo} />
        <H6 color={colors.fwdOrange[100]}>{t('saleCoPilot')}</H6>
        <AppDescriptionText>{t('practiceText')}</AppDescriptionText>
      </ImageContainer>
      <BottomContainer onPress={goUserProfilePage}>
        <LetsGoText fontWeight={'normal'}>{t('letGo')}</LetsGoText>
        <ArrowImage source={arrowRight} />
      </BottomContainer>
    </PageContainer>
  );
};

export default SplashPage;
