import React, { useCallback, useMemo } from 'react';
import {
  ImageBackground,
  Pressable,
  TouchableOpacity,
  View,
} from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import {
  Button,
  H3,
  H4,
  H7,
  Icon,
  LargeLabel,
  Typography,
} from 'cube-ui-components';
import OperatingHoursIcon from 'features/ecoach/components/icons/OperatingHoursIcon';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { EcoachParamList, RootStackParamList } from 'types';
import { country } from 'utils/context';
import { cafeBGMobile, cafeBGTablet } from 'features/ecoach/assets';
import { Spacer } from 'features/lead/ib/tablet/LeadTableTitleRow';
import { StatusBar } from 'expo-status-bar';

const HomeBGImg = styled(ImageBackground)(() => ({
  width: '100%',
  height: '100%',
  flex: 1,
  backgroundColor: colors.black,
}));

const DividerHorizontal = ({ isTabletMode }: { isTabletMode: boolean }) => (
  <HorizontalDividerView>
    {!isTabletMode && <HorizontalDivider />}
  </HorizontalDividerView>
);

const YourGoalContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    alignItems: 'flex-start',
    width: '100%',
    marginBottom: isTabletMode ? 0 : sizes[6],
  }),
);

const YourGoal = styled(Pressable)(
  ({ theme: { space, borderRadius, colors } }) => ({
    fontWeight: 'normal',
    width: 'auto',
    backgroundColor: colors.palette.white,
    paddingVertical: space[1],
    paddingHorizontal: space[3],
    borderRadius: borderRadius['xx-large'],
  }),
);

const ConvinceAvatarView = styled(View)<{
  isTabletMode: boolean;
}>(({ isTabletMode }) => ({
  flexDirection: 'column',
  gap: isTabletMode ? 0 : sizes[6],
  width: isTabletMode ? 400 : '100%',
}));

const Container = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    paddingHorizontal: isTabletMode ? 0 : sizes[4],
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    backgroundColor:
      'linear-gradient(90deg, rgba(1, 1, 1, 0.75) 33.19%, rgba(1, 1, 1, 0.75) 93.58%)',
    gap: isTabletMode ? sizes[8] : 0,
  }),
);

const ProductSelectView = styled(View)(() => ({
  display: 'flex',
  flexDirection: 'row',
}));

const TimeView = styled(View)(() => ({
  marginLeft: sizes[1],
  display: 'flex',
  flexDirection: 'row',
  gap: sizes[1],
  justifyContent: 'center',
  alignItems: 'center',
}));

const OneToFour = styled(View)(() => ({
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  paddingRight: sizes[2],
}));

const NumberRow = styled(View)(() => ({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: sizes[4],
}));

const Circle = styled(View)(() => ({
  width: sizes[9],
  height: sizes[9],
  backgroundColor: colors.fwdAlternativeOrange[50],
  borderRadius: 50,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
}));
const Title = styled(H7)(() => ({
  paddingRight: sizes[2],
}));

const HorizontalDividerView = styled(View)(() => ({
  width: '100%',
  height: 30,
  display: 'flex',
  justifyContent: 'flex-start',
  paddingLeft: 17,
}));

const HorizontalDivider = styled(View)(() => ({
  width: 1,
  height: 30,
  backgroundColor: 'rgba(255, 255, 255, 0.5)',
}));

const YourGoalView = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    width: isTabletMode ? 400 : '100%',
    padding: isTabletMode ? sizes[8] : 0,
    gap: isTabletMode ? sizes[6] : 0,
    borderRadius: isTabletMode ? sizes[4] : 0,
    borderWidth: isTabletMode ? 1 : 0,
    borderColor: isTabletMode ? '#d3d3d3' : 'transparent',
    flexDirection: isTabletMode ? 'column' : 'row',
  }),
);

const GoBackBtn = styled(TouchableOpacity)({
  paddingHorizontal: sizes[4],
  position: 'absolute',
  top: sizes[10],
  left: sizes[1],
  zIndex: 1,
});
const StartBtnView = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    width: isTabletMode ? 400 : '100%',
    alignItems: 'flex-start',
    marginTop: !isTabletMode ? sizes[6] : 0,
  }),
);

const StartBtn = styled(Button)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    width: isTabletMode ? 180 : '100%',
  }),
);

const AppointmentGuideLines = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { params } = useRoute<RouteProp<EcoachParamList, 'GuideLinesPage'>>();
  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();

  const productConfig = useBoundStore(
    state => state.ecoach.appointmentProductConfig,
  );

  const personaName = useMemo(() => {
    const productForCurrentCountry = productConfig?.[country];
    const product = productForCurrentCountry?.find(
      pro => pro.product_code === params.productSelectionCode,
    );
    return product?.personas.persona_name || '';
  }, [productConfig, params.productSelectionCode]);

  const goVideoCallPage = useCallback(() => {
    navigation.push('VideoCallPage', { ...params });
  }, [navigation, params]);

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <HomeBGImg
      source={isTabletMode ? cafeBGTablet : cafeBGMobile}
      resizeMode={isTabletMode ? 'cover' : 'cover'}>
      <StatusBar hidden />
      <GoBackBtn onPress={goBack}>
        <Icon.ArrowLeft size={sizes[6]} fill={colors.white} />
      </GoBackBtn>
      <Container isTabletMode={isTabletMode}>
        {!isTabletMode && (
          <YourGoalContainer isTabletMode={isTabletMode}>
            <YourGoal>
              <Typography.SmallBody
                fontWeight={'bold'}
                color={colors.fwdAlternativeOrange[100]}>
                {t('hereYourGoal')}
              </Typography.SmallBody>
            </YourGoal>
          </YourGoalContainer>
        )}
        <ConvinceAvatarView isTabletMode={isTabletMode}>
          {isTabletMode ? (
            <H3 fontWeight="bold" color={colors.white}>
              {t('appointmentCallWith', { personaName: personaName })}
            </H3>
          ) : (
            <H4
              fontWeight="bold"
              color={colors.white}
              style={{ maxWidth: 343 }}>
              {t('appointmentCallWith', { personaName: personaName })}
            </H4>
          )}
          <ProductSelectView>
            <LargeLabel fontWeight={'medium'} color={colors.fwdOrange[100]}>
              {t('appointmentSetting')}
            </LargeLabel>
            <TimeView>
              <LargeLabel fontWeight="normal" color={colors.white}>
                |
              </LargeLabel>
              <OperatingHoursIcon size={16} fill={colors.white} />
              <LargeLabel fontWeight="normal" color={colors.white}>
                {t('fiveMinutes')}
              </LargeLabel>
            </TimeView>
          </ProductSelectView>
        </ConvinceAvatarView>
        {!isTabletMode && <Spacer height={sizes[6]}></Spacer>}
        <YourGoalView isTabletMode={isTabletMode}>
          {isTabletMode && (
            <YourGoalContainer isTabletMode={isTabletMode}>
              <YourGoal>
                <Typography.SmallBody
                  fontWeight={'bold'}
                  color={colors.fwdAlternativeOrange[100]}>
                  {t('hereYourGoal')}
                </Typography.SmallBody>
              </YourGoal>
            </YourGoalContainer>
          )}
          <OneToFour>
            {[
              [t('appointmentIntroduce'), t('appointmentBuildRapport')],
              [t('appointmentInterest'), t('appointmentAskQuestion')],
              [t('appointmentSetTimeDate'), t('appointmentInsertATrial')],
              [t('appointmentConfirmMeetUp'), t('appointmentGiveAssurance')],
            ].map((row, index) => (
              <React.Fragment key={index}>
                <NumberRow>
                  <Circle>
                    <H7 fontWeight="bold" color={colors.white}>
                      {index + 1}
                    </H7>
                  </Circle>
                  <View>
                    <H7 fontWeight="bold" color={colors.white}>
                      {row[0]}
                    </H7>
                    <Title fontWeight="normal" color={colors.white}>
                      {row[1]}
                    </Title>
                  </View>
                </NumberRow>
                {index < 3 && <DividerHorizontal isTabletMode={isTabletMode} />}
              </React.Fragment>
            ))}
          </OneToFour>
        </YourGoalView>
        <StartBtnView isTabletMode={isTabletMode}>
          <StartBtn
            isTabletMode={isTabletMode}
            variant={'primary'}
            onPress={goVideoCallPage}
            text={t('startTheCall')}
          />
        </StartBtnView>
      </Container>
    </HomeBGImg>
  );
};
export default AppointmentGuideLines;
