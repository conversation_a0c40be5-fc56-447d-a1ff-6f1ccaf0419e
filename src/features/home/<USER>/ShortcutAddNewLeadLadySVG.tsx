import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function ShortcutAddNewLeadLadySVG(props: SvgIconProps) {
  return (
    <Svg width={65} height={48} viewBox="0 0 65 48" fill="none" {...props}>
      <Path
        d="M25.074 27.223c3.187 0 5.77-2.771 5.77-6.19 0-3.418-2.583-6.19-5.77-6.19-3.186 0-5.77 2.772-5.77 6.19 0 3.418 2.584 6.19 5.77 6.19z"
        fill="#193027"
      />
      <Path
        d="M33.932.099s-3.088-.93-6.267 2.905c-2.002 2.408-5.063 20.78-2.302 21.552 2.747.772 4.422-.42 5.194.64.785 1.048 1.884.537 3.336-.26 1.452-.8 2.12 1.753 4.749 1.4 2.617-.354 1.805-2.016 3.912-1.702C45.184 25.014 47.8-.595 33.932.1z"
        fill="#193027"
      />
      <Path
        d="M27.978 44.443s-3.454 6.385-4.5 9.97C22.444 58 20.102 73.166 18.912 74.592c-1.19 1.44-18.97-1.583-18.97-1.583l-2.133 2.944s20.462 9.579 24.674 8.82c5.678-1.02 11.958-21.8 11.958-21.8s2.892 19.51 2.303 23.802c-.589 4.305-.17 23.933-.17 23.933l3.69.183s4.199-15.938 5.978-35.304c1.78-19.366-6.79-31.156-6.79-31.156H27.978v.013z"
        fill="#FBE2D0"
      />
      <Path
        d="M26.828 43.596s-4.121 6.7-4.723 10.979c-.602 4.266-3.44 19.34-3.44 19.34l2.943 1.165 2.342-4.436.34 5.064s14.561 3.284 23.353-1.806c0 0-1.91-22.821-6.633-30.777l-14.182.471z"
        fill="#FBCE43"
      />
      <Path
        d="M36.377 24.558s1.335-.785.746-3.585l-.392-1.348-4.985.301s.288 1.544.916 2.264c.628.72 2.878 2.015 2.878 2.015l.837.353zM59.434 24.097l-.668-.523a1.648 1.648 0 01-.287-2.316l2.89-3.73a1.647 1.647 0 012.317-.288c1.086.838 1.282 2.421.444 3.507l-2.38 3.062a1.671 1.671 0 01-2.316.288z"
        fill="#FBE2D0"
      />
      <Path
        d="M59.04 22.188c.445.052.811-.3.798-.772l-.065-3.14c0-.471-.38-.943-.838-.956-.353 0-.811.301-.798.772l.065 3.14c.014.485.393.917.838.956z"
        fill="#FBE2D0"
      />
      <Path
        d="M24.265 28.313s-2.434 12.43-15.74 17.482l-.47-2.473s8.634-6.805 11.107-16.095l5.103 1.086z"
        fill="#FAE1CE"
      />
      <Path
        d="M9.428 44.79l-.314-.785a1.658 1.658 0 00-2.159-.916l-4.396 1.78a1.658 1.658 0 00-.915 2.159 2.51 2.51 0 003.27 1.387l3.611-1.453a1.683 1.683 0 00.903-2.172z"
        fill="#FBE2D0"
      />
      <Path
        d="M7.703 43.897c-.079.445-.51.694-.968.563l-3.023-.93c-.458-.143-.81-.627-.693-1.072.092-.34.51-.694.968-.563l3.022.93c.458.143.772.627.694 1.072z"
        fill="#FBE2D0"
      />
      <Path
        d="M46.427 26.815s2.316 3.101 4.24 3.101c1.922 0 8.477-7.838 8.477-7.838l1.256 1.767s-5.194 12.34-10.205 12.43c-5.01.092-8.765-5.495-8.674-5.783.092-.288 3.271-3.677 3.271-3.677h1.635z"
        fill="#FAE1CE"
      />
      <Path
        d="M40.947 42.443v1.884s-3.558 3.468-14.038 0l-.026-1.884 9.197-.576 4.867.576z"
        fill="#FBCE43"
      />
      <Path
        d="M37.373 19.782s3.716-.144 5.6 1.165c1.033.707 3.009 3.939 3.885 5.43.262.432.157.982-.222 1.309l-2.76 2.342s.81 3.167-2.369 4.763l-.575 7.655s-5.98 3.468-14.065 0c0 0-.772-12.758-1.635-11.593a91.797 91.797 0 00-1.413 2.04 1.03 1.03 0 01-1.622.119l-4.004-4.37a1.035 1.035 0 01-.04-1.362c1.453-1.792 5.326-6.438 6.909-7.157 3.153-1.44 7.195-.68 7.195-.68l5.116.34z"
        fill="#E87722"
      />
      <Path
        d="M41.3 11.222s.602-.641.942-.288c.864.89-.353 1.715-1.047 1.82l.105-1.532z"
        fill="#FBE2D0"
      />
      <Path
        d="M36.456 1.654c-3.048-.25-5.848 1.766-6.463 4.658a5.612 5.612 0 00.092 2.761c.51 2.146.942 3.873 1.61 5.182.68 1.334.837 2.852.392 4.266l-.445 1.426c3.231 2.198 5.037.733 5.037.733l.536-3.376c3.86.117 4.41-6.7 4.723-8.702 0-.079.026-.144.04-.223.51-3.323-1.976-6.438-5.522-6.725z"
        fill="#FBE2D0"
      />
      <Path
        d="M35.095 7.503c-.85.615-1.622 1.335-2.38 2.042-.812.759-1.65 1.557-1.976 2.604-.864-.864-1.322-2.015-1.675-3.167-.17-.536-.314-1.073-.314-1.636.013-.785.327-1.53.641-2.263.406-.943.837-1.91 1.636-2.565.536-.432 1.19-.694 1.857-.877 1.557-.445 3.245-.523 4.802-.078 1.557.445 2.97 1.452 3.715 2.84.968 1.792.772 3.938.55 5.94-.079-.864-.537-1.636-.981-2.382-.288-.484-.262-.785-.576-1.269-.523-.824-.589-.89-1.23-1.963-.575 1.191-2.996 2.002-4.069 2.774z"
        fill="#193027"
      />
      <Path
        d="M31.668 10.97a.91.91 0 00-.406-.485c-.392-.223-1.099-.51-1.675-.092-.85.602.34 2.722 1.95 1.99 0 0 .484-.34.13-1.414z"
        fill="#FBE2D0"
      />
      <Path
        d="M34.589 9.417c-.04.288.104.536.314.55.209.026.405-.184.444-.472.04-.287-.104-.536-.314-.55-.209-.012-.405.197-.444.472zM39.757 10.27c-.04.288.104.537.314.55.209.026.405-.184.444-.471.04-.288-.104-.537-.314-.55-.209-.026-.405.183-.444.471z"
        fill="#183028"
      />
      <Path
        d="M37.295 12.79s-.105-.04-.13-.092a.142.142 0 01.09-.183c.341-.118.524-.301.564-.55.039-.314-.184-.667-.367-.785-.38-.262-.196-1.61-.105-2.185a.148.148 0 01.17-.118c.08.013.131.092.118.17-.157.916-.157 1.806 0 1.897.236.157.55.602.498 1.06-.027.249-.184.59-.746.786h-.092z"
        fill="#E77824"
      />
      <Path
        d="M36.956 14.364c.183.026.366.026.51 0 .55-.053.929-.301.955-.301.066-.04.066-.118 0-.17a.178.178 0 00-.21-.027s-1.347.838-2.564-.458a.177.177 0 00-.21-.026c-.065.04-.065.118-.012.17.523.537 1.06.746 1.517.812h.014z"
        fill="#E77825"
      />
      <Path
        d="M36.707 15.073c-.916-.17-1.23-1.047-1.27-1.518 0-.065.053-.117.131-.117.079 0 .144.052.157.117 0 .053.118 1.165 1.139 1.296.026 0 .484.117.85-.079.262-.13.432-.392.51-.759 0-.065.092-.104.158-.091.078.013.13.078.117.144-.078.431-.3.745-.628.902-.47.236-1.02.105-1.046.092-.053.026-.079.013-.118.013zM36.996 15.622c.275.04.445-.04.458-.04.066-.025.092-.104.052-.17-.039-.065-.13-.09-.196-.052-.013 0-.248.105-.641-.091-.066-.04-.157-.013-.196.04-.04.052 0 .13.065.17.157.078.327.13.458.143z"
        fill="#E77825"
      />
    </Svg>
  );
}
