import React, { Fragment } from 'react';
import styled from '@emotion/native';
import FwdNewsCard from 'features/home/<USER>/Overview/FwdNewsCard/FwdNewsCard.tablet';
import PerformanceCard from 'features/home/<USER>/Overview/PerformanceCard/PerformanceCard';
import BusinessOppCard from 'features/home/<USER>/Overview/BusinessOppCard';
import Animated, { LinearTransition } from 'react-native-reanimated';
import EcoachEntryPointCard from 'features/ecoach/components/cards/EcoachEntryPointCard';
import useGetFilteredDashboardCardOrder from 'features/home/<USER>/useGetFilteredDashboardCardOrder';

export default function DashboardSection() {
  const { cardOrderForDisplay } = useGetFilteredDashboardCardOrder();
  return (
    <DashboardSectionsContainer>
      {cardOrderForDisplay.map(card => {
        switch (card.key) {
          /*Comment out TrainerGuruCard on tablet, will open after has design*/
          case 'TrainerGuruCard':
            return (
              <Animated.View key={card.key} layout={LinearTransition}>
                <EcoachEntryPointCard />
              </Animated.View>
            );
          case 'FWDNewsCard':
            return (
              <Animated.View key={card.key} layout={LinearTransition}>
                <FwdNewsCard />
              </Animated.View>
            );

          case 'PerformanceCard':
            return (
              <Animated.View key={card.key} layout={LinearTransition}>
                <PerformanceCard />
              </Animated.View>
            );

          case 'BusinessOpportunityCard':
            return (
              <Animated.View key={card.key} layout={LinearTransition}>
                <BusinessOppCard />
              </Animated.View>
            );
          default:
            return (
              <Animated.View key={card.key} layout={LinearTransition}>
                <Fragment />
              </Animated.View>
            );
        }
      })}
    </DashboardSectionsContainer>
  );
}

const DashboardSectionsContainer = styled.View(({ theme }) => ({
  gap: theme.space[4],
  paddingTop: theme.space[4],
}));
