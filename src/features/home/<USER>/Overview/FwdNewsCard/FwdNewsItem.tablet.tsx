import React from 'react';
import { Icon, XView, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import { AllContentStackNewsTagTypes, ContentStackNewsItem } from 'types/news';
import { NewsTranslationKeys } from 'features/fwdNews/translation/types';
import { Image } from 'expo-image';
import { country } from 'utils/context';
import FwdNewBookmarkButton from 'features/fwdNews/components/FwdNewBookmarkButton';
import styled from '@emotion/native';
import NewsImagePlaceholder from 'features/fwdNews/assets/images/news-image-placeholder.png';

export const CARD_WIDTH = country === 'my' ? 208 : 218;
export const CARD_WIDTH_WITH_MARGIN = CARD_WIDTH + 16;

const GIF_MIME_TYPE = 'image/gif';

export const fwdNewsTagMap: Record<
  AllContentStackNewsTagTypes,
  NewsTranslationKeys
> = {
  promotion: 'tags.promotion',
  general_information: 'tags.information',
  company_activity: 'tags.company',
  regulation: 'tags.regulation',
  campaign: 'tags.campaign',
  contest: 'tags.contest',
  notice: 'tags.notice',
  news: 'tags.news',
  info: 'tags.info',
};

export default function FwdNewsItem({
  newsItem,
}: {
  newsItem: ContentStackNewsItem;
}) {
  const { colors, sizes } = useTheme();
  const { t } = useTranslation('news');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const isGif = newsItem?.thumbnail?.content_type === GIF_MIME_TYPE;

  return (
    <ItemCard
      onPress={() =>
        navigation.navigate('FWDNewsDetails', {
          id: newsItem?.uid,
          title: newsItem?.display_title,
          type_of_news_post: newsItem?.type_of_news_post,
        })
      }>
      <NewsImageContainer>
        <Image
          source={
            isGif ? NewsImagePlaceholder : { uri: newsItem?.thumbnail?.url }
          }
          style={{ width: 'auto', height: 99 }}
          contentFit="cover"
        />
      </NewsImageContainer>

      {country === 'my' && (
        <XView style={{ alignItems: 'center' }}>
          <XView style={{ flex: 1, alignItems: 'center' }}>
            <Icon.Promotion width={18} height={18} fill={colors.primary} />
            <CategoryText>
              {fwdNewsTagMap[newsItem?.type_of_news_post]
                ? t(fwdNewsTagMap[newsItem?.type_of_news_post])
                : '--'}
            </CategoryText>
          </XView>

          <FwdNewBookmarkButton
            newsId={newsItem?.uid}
            iconSize={18}
            hitSlop={{
              left: sizes[1],
              right: sizes[1],
              bottom: sizes[1],
              top: sizes[1],
            }}
          />
        </XView>
      )}

      <NewsTitle
        fontWeight="bold"
        numberOfLines={country === 'my' ? 2 : 3}
        ellipsizeMode="tail">
        {newsItem?.display_title}
      </NewsTitle>

      {country != 'my' && (
        <XView style={{ alignItems: 'center' }}>
          <Icon.Promotion width={18} height={18} fill={colors.primary} />
          <CategoryText>
            {fwdNewsTagMap[newsItem?.type_of_news_post]
              ? t(fwdNewsTagMap[newsItem?.type_of_news_post])
              : '--'}
          </CategoryText>
        </XView>
      )}
    </ItemCard>
  );
}

const ItemCard = styled.TouchableOpacity(({ theme }) => ({
  width: CARD_WIDTH,
  backgroundColor: theme.colors.background,
  padding: theme.space[4],
  marginRight: theme.space[4],
  borderRadius: theme.borderRadius.large,
}));

const NewsImageContainer = styled.View(({ theme }) => ({
  width: '100%',
  height: 99,
  borderRadius:
    country === 'my' ? theme.borderRadius.small : theme.borderRadius.medium,
  marginBottom: theme.space[3],
  overflow: 'hidden',
}));

const NewsTitle = styled(Typography.LargeLabel)(({ theme }) => ({
  marginBottom: country === 'my' ? 0 : theme.space[2],
  marginTop: country === 'my' ? theme.space[2] : 0,
}));

const CategoryText = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginLeft: theme.space[1],
}));
