// * Performance tabs in home screen && related charts' data keys

import { t } from 'i18next';
import { BuildCountry } from 'types';
import { PerformanceMetrics } from 'types/moduleConfig';
import { FEMappedIDNHomePerfFields, HomePerformance } from 'types/performance';
import { countryModuleSellerConfig } from 'utils/config/module';

export type HomePerformanceCardType = {
  type: 'chart' | 'table' | 'percent' | 'chartWithPercent';
  title: string;
  subTitle?: string;
  value?: number;
  target?: number;
  submit?: number;
  issue?: number;
  submitLabel?: string;
  issueLabel?: string;
  dollarSign?: string;
};

export type HomePerformanceConfig = {
  type: 'chart' | 'table' | 'percent' | 'chartWithPercent';
  title: PerformanceMetrics | '--';
  subTitle?: string;
  valueKey?: keyof HomePerformance | keyof FEMappedIDNHomePerfFields;
  targetKey?: keyof HomePerformance | keyof FEMappedIDNHomePerfFields;
  submitKey?: keyof HomePerformance | keyof FEMappedIDNHomePerfFields;
  issueKey?: keyof HomePerformance | keyof FEMappedIDNHomePerfFields;
  submitLabel?: string;
  issueLabel?: string;
  dollarSign: string;
};

// ********* Tablet config
//* home performance
const PH_TABLET_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'mtdAPECompletion',
        targetKey: 'mtdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'mtdCASESubmissionCM',
        issueKey: 'mtdCASECompletionCM',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
      // {
      //   type: 'percent',
      //   title: 'Persistency',
      //   value: 'persistency',
      // },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'ytdAPECompletion',
        targetKey: 'ytdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'ytdCASESubmissionCY',
        issueKey: 'ytdCASECompletionCY',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
      // {
      //   type: 'percent',
      //   title: 'Persistency',
      //   value: 'persistency',
      // },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

//* home performance
const MY_TABLET_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdACECompletionCM',
        targetKey: 'mtdTargetACE',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdCASECompletionCM',
        targetKey: 'mtdTargetCASE',
        dollarSign: '',
      },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdACECompletionCY',
        targetKey: 'ytdTargetACE',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdCASECompletionCY',
        targetKey: 'ytdTargetCASE',
        dollarSign: '',
      },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

const IB_TABLET_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdAPECompletion',
        targetKey: 'mtdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdCASECompletionCM',
        targetKey: 'mtdTargetCASE',
        dollarSign: '',
      },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdAPECompletion',
        targetKey: 'ytdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdCASECompletionCY',
        targetKey: 'ytdTargetCASE',
        dollarSign: '',
      },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

const ID_TABLET_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chartWithPercent',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'MDRT',
        valueKey: 'mdrtData',
        targetKey: 'mdrtData',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdAFYPCompletionCM',
        targetKey: 'mtdAFYPTarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'mtdCASECompletionCM',
        targetKey: 'mtdTargetCASE',
        dollarSign: '',
      },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chartWithPercent',
        title: '--',
        subTitle: 'MDRT',
        valueKey: 'mdrtData',
        targetKey: 'mdrtData',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdAFYPCompletionCY',
        targetKey: 'ytdAFYPTarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        subTitle: 'Issued',
        valueKey: 'ytdCASECompletionCY',
        targetKey: 'ytdTargetCASE',
        dollarSign: '',
      },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

export const HomePerformanceTabletModule = {
  ph: PH_TABLET_CONFIG,
  my: MY_TABLET_CONFIG,
  ib: IB_TABLET_CONFIG,
  id: ID_TABLET_CONFIG,
};

// ********* Phone config
const PH_PHONE_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'mtdAPECompletion',
        targetKey: 'mtdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'mtdCASESubmissionCM',
        issueKey: 'mtdCASECompletionCM',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'ytdAPECompletion',
        targetKey: 'ytdTargetACE',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'ytdCASESubmissionCY',
        issueKey: 'ytdCASECompletionCY',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

const ID_PHONE_CONFIG = [
  {
    tab: 'MTD',
    cards: [
      {
        type: 'chartWithPercent',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'MDRT',
        valueKey: 'mdrtData',
        targetKey: 'mdrtData',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'mtdAPECompletion',
        targetKey: 'mtdAPETarget',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'mtdCASESubmissionCM',
        issueKey: 'mtdCASECompletionCM',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
    ],
  },
  {
    tab: 'YTD',
    cards: [
      {
        type: 'chartWithPercent',
        title: '--',
        subTitle: 'MDRT',
        valueKey: 'mdrtData',
        targetKey: 'mdrtData',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'chart',
        title: countryModuleSellerConfig?.performance?.metricOne ?? '--',
        subTitle: 'Completed',
        valueKey: 'ytdAPECompletion',
        targetKey: 'ytdTargetACE',
        dollarSign: t('common:currencySymbol'),
      },
      {
        type: 'table',
        title: countryModuleSellerConfig?.performance?.metricTwo ?? '--',
        submitKey: 'ytdCASESubmissionCY',
        issueKey: 'ytdCASECompletionCY',
        submitLabel: 'Submitted',
        issueLabel: 'Issued',
        dollarSign: '',
      },
    ],
  },
] satisfies Array<{
  tab: 'MTD' | 'YTD';
  cards: Array<HomePerformanceConfig>;
}>;

export const HomePerformancePhoneModule = {
  ph: PH_PHONE_CONFIG,
  my: PH_PHONE_CONFIG,
  ib: PH_PHONE_CONFIG,
  id: ID_PHONE_CONFIG,
} satisfies Record<
  BuildCountry,
  Array<{
    tab: 'MTD' | 'YTD';
    cards: Array<HomePerformanceConfig>;
  }>
>;
