import { useTheme } from '@emotion/react';
import { useQueryClient } from '@tanstack/react-query';
import { checkAnimation } from 'features/home/<USER>/image';
import { useCompleteTaskById } from 'hooks/useCompleteTask';
import * as React from 'react';
import { TouchableOpacity, Image } from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';

type TaskCheckCircleProps = {
  taskId: string;
  completed: boolean;
  toggleIsCompleted?: (value: string) => void;
};

function TaskCheckCircle({
  taskId,
  completed,
  toggleIsCompleted,
}: TaskCheckCircleProps) {
  const { colors, sizes } = useTheme();

  const [checked, setChecked] = React.useState(completed);

  const { mutate } = useCompleteTaskById();

  return (
    <TouchableOpacity
      onPress={async () => {
        setChecked(true);

        setTimeout(() => {
          toggleIsCompleted && toggleIsCompleted(String(taskId));
        }, 2000);

        mutate(taskId, {
          onSuccess: res => {
            if (!res.isCompleted) {
              setChecked(false);
            }
          },
        });
      }}>
      {checked ? (
        <Image
          source={checkAnimation}
          style={{
            height: sizes[6],
            width: sizes[6],
          }}
        />
      ) : (
        <Svg width={sizes[6]} height={sizes[6]} viewBox="0 0 24 24" fill="none">
          <Circle
            cx={12}
            cy={12}
            r={11}
            fill={colors.background}
            stroke={colors.primary}
            strokeWidth={2}
          />
          <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.98 16l-3.703-3.79.224-.21a.994.994 0 011.34 0l2.14 2.327 6.477-6.067a.994.994 0 011.34 0l.223.21L9.981 16z"
            fill={colors.primaryVariant2}
            stroke={colors.primaryVariant2}
          />
        </Svg>
      )}
    </TouchableOpacity>
  );
}

export default TaskCheckCircle;
