import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Column, Typography, Icon } from 'cube-ui-components';
import { BirthdayTask } from 'types';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

import { renderLabelByLanguage } from 'utils/helper/translation';
import BirthdayCakeSVG from 'features/home/<USER>/BirthdayCakeSVG';
import TaskCheckCircle from '../../TaskCheckCircle';
import { MYTaskContainer } from '../../TaskStyledComponent';
import TaskNotificationButton from '../../TaskNotificationButton';
import YellowBasedBirthdayCakeSVG from 'features/home/<USER>/YellowBasedBirthdayCakeSVG';

export default function BirthdayTaskItem({
  onPress,
  taskId,
  completed,
  leadRole,
  nameSuffix,
  displayName,
  onContact,
}: {
  onPress: () => void;
  taskId: number;
  completed: boolean;
  leadRole: 'home:policyOwner' | 'home:insured' | 'home:teammate';
  onContact?: () => void;
} & BirthdayTask) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['home', 'lead']);
  return (
    <TouchableOpacity onPress={onPress}>
      <MYTaskContainer>
        <Column marginLeft={space[3]} my={space[3]}>
          <TaskCheckCircle taskId={String(taskId)} completed={completed} />
        </Column>
        <Column justifyContent="center" mr={space[1]}>
          <YellowBasedBirthdayCakeSVG />
        </Column>

        <Column flex={1} my={space[3]} paddingY={space[3]} gap={space[1]}>
          <Typography.LargeLabel color={colors.secondary} fontWeight="bold">
            {t('home:task.birthday.cardDescription')}
          </Typography.LargeLabel>
          <Typography.LargeLabel
            fontWeight="bold"
            color={colors.primary}
            numberOfLines={2}>
            {displayName?.en || nameSuffix}
          </Typography.LargeLabel>
          <Typography.Label color={colors.secondaryVariant}>
            {/* {'Policy owner'} */}
            {t(leadRole)}
          </Typography.Label>
        </Column>
        <Column marginLeft={space[3]} marginY={space[3]}>
          <TaskNotificationButton
            taskId={String(taskId)}
            notificationTitle={t('home:task.birthday.action.title')}
            notificationBody={
              t('home:task.birthday.actionDescription') +
              renderLabelByLanguage(displayName)
            }
          />
        </Column>
        <Column justifyContent="center" ml={space[1]} mr={space[2]}>
          <Icon.ChevronRight fill={colors.secondaryVariant} size={sizes[5]} />
        </Column>
      </MYTaskContainer>
    </TouchableOpacity>
  );
}
