export default {
  // Welcome section
  'home.welcomeSection.target': 'task completed',

  // Shortcut section
  'home.shortcutSection.title': 'Shortcuts',
  'home.shortcutSection.addNewLead.normal': 'Add a\nnew lead',
  'home.shortcutSection.addNewLead.tablet': 'Add a new lead',
  'home.shortcutSection.fna.normal': 'Know your\ncustomer (FNA)',
  'home.shortcutSection.fna.tablet': 'Know your customer (FNA)',
  'home.shortcutSection.fna.normalV2': 'Customer needs analysis (FNA)',
  'home.shortcutSection.fna.tabletV2': 'Customer needs analysis (FNA)',
  'home.shortcutSection.createQuickQuote.normal': 'Create\nquick quote',
  'home.shortcutSection.createQuickQuote.tablet': 'Create quick quote',

  // Dashboard section
  'home.dashboard.title': 'Dashboard',

  // Tooltip
  'home.tooltip.title': 'Did you know',
  'home.tooltip.content': 'You can customise your dashboard content',
  'home.tooltip.button': 'Try now',

  // FWD news card
  'home.fwdNews.title': 'FWD news',

  // Performance card
  'home.performance.title': 'Performance',
  'home.performance.title.performanceTracking': 'Performance tracking',
  'home.performance.case': 'Case',
  'home.performance.case.submitted': 'Submitted',
  'home.performance.case.issued': 'Issued',
  'home.performance.persistency': 'Persistency',
  'home.performance.issued': 'Issued',
  'home.performance.completed': 'Completed',
  // Business opp card
  'home.businessOpp.title': 'Business opportunity in your pipeline',
  'home.businessOpp.reminderText': 'Displaying data from last 60 days',
  'home.businessOpp.inProgress': 'Proposals in progress',
  'home.businessOpp.inSI': 'Proposals in sale illustration',
  'home.businessOpp.inApplication': 'Proposals in application',
  'home.businessOpp.submitted': 'Submitted cases (pending)',
  'home.businessOpp.submitted.urgent': 'Urgent',

  // Reorder panel
  'home.reorder.title': 'Change order',
  'home.reorder.dashboard.title': 'Change order',
  'home.reorder.dashboard.news': 'FWD News',
  'home.reorder.dashboard.performance': 'Performance',
  'home.reorder.dashboard.businessOpportunity': 'Business opportunity',
  'home.reorder.dashboard.trainerGuru': 'Trainer Guru',

  'home.reorder.task.birthday': 'Birthday',
  'home.reorder.task.leads': 'Contact leads',
  'home.reorder.task.paymentReminder': 'Payment reminders',
  'home.reorder.task.policyIssue': 'Policy issues',

  // Other
  'home.viewMore': 'View more',
  'home.viewAll': 'View all',

  // Tasks
  policyOwner: 'Policy owner',
  insured: 'Insured',
  teammate: 'Teammate',
  currency: 'PHP',
  'task.completion.fail.msg': 'failed to complete the task',
  'task.payment.title': 'Payment reminders',
  'task.payment.card.title': 'Payment reminder',
  'task.payment.card.msg.overdue': '{{daysDiff}} days overdue',
  'task.payment.card.msg.due': 'Due in {{daysDiff}} days',
  'task.payment.emptyCase':
    'There are no policies nearly due or that are overdue at the moment, but you can keep an eye on other policy statuses.',
  'task.payment.contactMsg':
    'Dear {{displayName}}, keep your insurance protection going! Your premium for FWD policy {{policyNo}} is overdue on {{paidToDate}}. Premium due: {{basicPremium}} (including insurance levy). Total outstanding amount is {{overdueAmt}} . I will contact and follow up with you shortly.',
  'task.payment.card.nextContribution': 'Next contribution',
  'task.payment.notification.title': 'Payment reminder',
  'task.payment.notification.body': 'Remind {{displayName}}',
  'task.policy.notification.title': 'Policy reminder',
  'task.policy.notification.body': 'Remind to follow up Lead {{displayName}}',

  'task.policy.title': 'Policy issues',
  'task.policy.newBusinessPending': 'New Business Pending',
  'task.policy.emptyCase':
    'There are no policies nearly due or that are overdue at the moment, but you can keep an eye on other policy statuses.',
  'task.reminder.title': 'Set reminder',
  'task.reminder.functionDescription':
    'The system will remind you to do the task in every ',
  'task.reminder.label.30mins': 'Remind me every 30 minutes',
  'task.reminder.label.1hr': 'Remind me every 1 hour',
  'task.reminder.label.2hrs': 'Remind me every 2 hours',
  'task.reminder.lead': 'Remind to follow up Lead ',
  'task.birthday.cardDescription': ' Say happy birthday to',
  'task.birthday.actionDescription': 'Send birthday card to ',
  'task.birthday.action.title': 'Birthday reminder',
  // Notification
  'home.notification.clear': 'Clear',
  'home.termsAndConditions': 'Terms and conditions',
  'home.agree': 'Agree',
};
