import { useMemo } from 'react';
import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { useEcoachEnable } from 'features/aiBot/utils/useEcoachEnable';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useCheckIsAnyValidNews } from 'hooks/useGetNews';

export default function useGetFilteredDashboardCardOrder() {
  const isEcoachEnabled = useEcoachEnable();
  const { isTabletMode } = useLayoutAdoptionCheck();

  // Hide New business pending on FIB mobile on Day 1 release

  //  Do not directly use the store value
  const NOT_PROCEEDED_cardsOrder = useBoundStore(
    store => store.home.overView.cardsOrder,
  );
  const setNewCardsOrder = useBoundStore(
    store => store.homeActions.overView.setNewCardsOrder,
  );

  const hasPermission = useHasPermission();
  const canViewPerformance = hasPermission('performance');
  const canViewProposal = hasPermission('savedProposal');
  const canViewPolicy = hasPermission('policy');

  // CUBEFIB 2291 CUBEMY-3570 Hide the whole news section if there are no valid news, for ib and my
  const { data: isAnyValidNews } = useCheckIsAnyValidNews();

  const cardOrderForDisplay = useMemo(() => {
    return NOT_PROCEEDED_cardsOrder?.filter(item => {
      if (item.key == 'FWDNewsCard') {
        return isAnyValidNews;
      }
      if (
        countryModuleSellerConfig.dashboard &&
        Boolean(countryModuleSellerConfig.dashboard[item.key]?.isShown) == false
      ) {
        return false;
      }

      if (item.key == 'TrainerGuruCard') {
        return isEcoachEnabled;
      }
      if (item.key == 'PerformanceCard') {
        return canViewPerformance;
      }

      if (item.key == 'BusinessOpportunityCard') {
        return canViewProposal || canViewPolicy;
      }

      return true;
    });
  }, [
    NOT_PROCEEDED_cardsOrder,
    canViewPerformance,
    canViewPolicy,
    canViewProposal,
    isEcoachEnabled,
    isAnyValidNews,
  ]);

  return {
    cardOrderForDisplay,
    setNewCardsOrder,
  };
}
