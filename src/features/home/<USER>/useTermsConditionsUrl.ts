import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useCallback, useMemo } from 'react';
import { CHANNELS } from 'types/channel';
import {
  agencyTermsAndConditionsUrl,
  bancaTermsAndConditionsUrl,
} from 'utils/context';

export const useTermsConditionsUrl = (): [
  string,
  () => Promise<{ url: string; fileName: string }>,
] => {
  const { data: agentInfo } = useGetAgentProfile();

  const pdfUrl = useMemo(() => {
    let url = '';
    if (agentInfo?.channel === CHANNELS.AGENCY) {
      url = agencyTermsAndConditionsUrl;
    } else if (agentInfo?.channel === CHANNELS.BANCA) {
      url = bancaTermsAndConditionsUrl;
    }
    return url;
  }, [agentInfo?.channel]);
  const pdfGenerator = useCallback(async () => {
    return {
      url: pdfUrl,
      fileName: '',
    };
  }, [pdfUrl]);
  return [pdfUrl, pdfGenerator];
};
