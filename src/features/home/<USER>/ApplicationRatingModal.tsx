import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import ResponsiveView from 'components/ResponsiveView';
import { Icon } from 'cube-ui-components';
import useBoundStore from 'hooks/useBoundStore';
import { Modal, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import WebView from 'react-native-webview';
import { build, country, surveyUrl } from 'utils/context';

export type messageProps = {
  message: '';
  leadName?: string;
};

interface ModalProps {
  visible: boolean;
  setIsVisible: (value: React.SetStateAction<boolean>) => void;
  isAgentIdAsParam?: boolean;
}

export default function ApplicationRatingModal({
  visible,
  setIsVisible,
  isAgentIdAsParam = true,
}: ModalProps) {
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { sizes, colors } = useTheme();
  const { top } = useSafeAreaInsets();

  const injectedJS = `document.documentElement.style.cssText =
  document.documentElement.style.cssText +
  ';background-color: transparent !important; overflow: scroll;';

  document.body.style.cssText =
  document.body.style.cssText +
  ';background-color: transparent !important; overflow: scroll; ';
   true;`;

  const getSurveyWebUri = () => {
    if (isAgentIdAsParam) {
      if (surveyUrl.includes('?')) {
        return surveyUrl + `&agentId=${agentId}`;
      } else {
        return surveyUrl + `/?agentId=${agentId}`;
      }
    } else {
      return surveyUrl;
    }
  };

  // const surveyWebUri = isAgentIdAsParam
  //   ? surveyUrl + `&agentId=${agentId}`
  //   : surveyUrl;

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={() => setIsVisible(false)}>
      <Container>
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: '10%',
            right: sizes[4],
            zIndex: 200,
          }}
          onPress={() => {
            setIsVisible(false);
          }}>
          <Icon.CloseCircleFill size={sizes[8]} fill={colors.palette.white} />
        </TouchableOpacity>
        <WebViewWrapper>
          <WebView
            automaticallyAdjustContentInsets={false}
            injectedJavaScript={injectedJS}
            source={{
              uri: getSurveyWebUri(),
            }}
            style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}
          />
        </WebViewWrapper>
      </Container>
    </Modal>
  );
}

const Container = styled(ResponsiveView)(({ theme }) => {
  return {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingBottom: 0,
  };
});

const ButtonBackGround = styled(View)(({ theme }) => {
  return {
    backgroundColor: theme.colors.secondary,
    width: '50%',
    height: '50%',
    position: 'absolute',
    alignSelf: 'center',
    top: '20%',
  };
});

const WebViewWrapper = styled(View)(({ theme }) => {
  return {
    width: '100%',
    height: '80%',
  };
});
