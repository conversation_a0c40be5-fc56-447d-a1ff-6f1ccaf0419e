import { StyleSheet, View } from 'react-native';
import React from 'react';
import Svg, { Circle, Polygon, Text as SvgText } from 'react-native-svg';
import Skeleton from 'components/Skeleton';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';

export default function ProgressGaugeSkeleton({
  radius = 27.5,
  padding = 0,
  title,
  titleTop,
}: {
  radius?: number;
  padding?: number;
  title: React.ReactNode;
  titleTop?: number;
}) {
  const strokeWidth = radius * 0.15;
  const innerRadius = radius - strokeWidth / 2;
  const circumference = innerRadius * 2 * Math.PI;
  const arc = circumference * (270 / 360);
  const dashArray = `${arc} ${circumference}`;
  const transform = `rotate(135, ${radius + padding}, ${radius + padding})`;

  //  Bar skeleton UI
  function SkeletonPerformanceChart() {
    const { colors } = useTheme();

    return (
      <View
        style={[
          {
            position: 'relative',
            alignItems: 'center',
            justifyContent: 'center',
          },
        ]}>
        <Skeleton
          width={radius * 2}
          height={radius * 2}
          radius={99999}
          containerStyle={{ position: 'absolute' }}
        />
        <Svg
          style={styles.svg2}
          height={radius * 2 + padding * 2}
          width={radius * 2 + padding * 2}>
          <Circle
            // class="gauge_base"
            cx={radius + padding}
            cy={radius + padding}
            fill={colors.background}
            r={innerRadius - 2}
            stroke="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            transform={transform}
          />

          <Svg id="triangle" viewBox="0 -5 100 100">
            <Polygon
              x={0}
              y={35}
              points="50 25, 145 100, -45 100"
              fill={'white'}
            />
          </Svg>
        </Svg>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SkeletonPerformanceChart />

      <View
        style={{
          zIndex: 99,
          position: 'absolute',
          top: titleTop ? 35 : 20,
        }}>
        <View style={styles.h1}>{title}</View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  svg: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    zIndex: 100,
  },
  titles: {
    zIndex: 99,
    position: 'absolute',
    top: 20,
  },
  h1: {
    alignSelf: 'center',
    height: 21,
  },
  svg2: {
    position: 'relative',
    backgroundColor: 'transparent',
  },
});
