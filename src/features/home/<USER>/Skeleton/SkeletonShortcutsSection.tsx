import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { Typography, XView } from 'cube-ui-components';
import { SHORTCUTS_ICON_SIZE } from 'features/home/<USER>/Overview/ShortcutsSection/constants';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function SkeletonShortcutsSection() {
  const SKELETON_SHORTCUTS_CONFIG = [
    { type: 'addNewLead' },
    { type: 'fna' },
    { type: 'createQuickQuote' },
  ] as const;
  const { t } = useTranslation('home');

  return (
    <>
      <Title children={t('home.shortcutSection.title')} />

      <ShortcutsContainer>
        {SKELETON_SHORTCUTS_CONFIG.map(({ type }) => (
          <SkeletonShortcutsItem key={'SKELETON_SHORTCUTS_' + type} />
        ))}
      </ShortcutsContainer>
    </>
  );
}

function SkeletonShortcutsItem() {
  const { space, sizes, borderRadius } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const SVG_SIZE = isWideScreen
    ? SHORTCUTS_ICON_SIZE.wideScreen
    : SHORTCUTS_ICON_SIZE.default;

  return (
    <ItemContainer>
      <Skeleton
        width={sizes[18]}
        height={sizes[18]}
        radius={borderRadius.large}
      />
      <Skeleton
        width={64}
        height={16}
        radius={2}
        containerStyle={{ marginVertical: space[1] }}
      />
      <Skeleton width={80} height={16} radius={2} />
    </ItemContainer>
  );
}

const Title = styled(Typography.LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginBottom: theme.space[3],
}));

const ShortcutsContainer = styled(XView)(({ theme }) => ({
  width: '100%',
  marginBottom: theme.space[4],
}));

const ItemContainer = styled.View(() => ({
  flex: 1,
  alignItems: 'center',
  gap: 4,
}));
