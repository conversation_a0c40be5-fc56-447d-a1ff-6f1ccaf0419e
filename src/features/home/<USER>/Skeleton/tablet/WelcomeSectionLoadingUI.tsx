import React from 'react';
import Skeleton from 'components/Skeleton';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Box, Row } from 'cube-ui-components';

const shortcuts = ['shortcut1', 'shortcut2', 'shortcut3'];

export default function WelcomeSectionLoadingUI() {
  const { borderRadius, sizes } = useTheme();

  return (
    <WhiteCard>
      <Row justifyContent="space-between">
        <Skeleton width={sizes[60]} height={sizes[10]} radius={sizes[2]} />
        <Skeleton
          width={sizes[10]}
          height={sizes[10]}
          radius={borderRadius.full}
        />
      </Row>
      <Box height={sizes[3]} />
      <Skeleton width={sizes[50]} height={sizes[6]} radius={sizes[2]} />
      <Box height={sizes[4]} />
      <Row justifyContent="space-between">
        {shortcuts.map((shortcut, index) => (
          <Box key={shortcut} alignItems="center">
            <Skeleton
              width={sizes[18]}
              height={sizes[18]}
              radius={borderRadius['x-large']}
            />
            <Box height={sizes[2]} />
            <Skeleton
              width={sizes[30]}
              height={sizes[5]}
              radius={borderRadius.full}
            />
          </Box>
        ))}
      </Row>
    </WhiteCard>
  );
}

const WhiteCard = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius['large'],
  paddingVertical: theme.space[4],
  paddingHorizontal: theme.space[6],
}));
