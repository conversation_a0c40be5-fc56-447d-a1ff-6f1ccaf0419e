import React, { useEffect, useState } from 'react';
import { Button, Icon, Typography, XView } from 'cube-ui-components';
import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import DraggableFlatList, {
  RenderItemParams,
} from 'react-native-draggable-flatlist';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import {
  DashboardCardsKeys,
  ReorderItem,
  TodayTasksSectionsKeys,
} from 'types/home';
import DragHandleSVG from 'features/home/<USER>/DragHandleSVG';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { View } from 'react-native';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import SlidingSideModal from 'components/SlidingSideModal';
import { countryModuleSellerConfig } from 'utils/config/module';
import { useEcoachEnable } from 'features/aiBot/utils/useEcoachEnable';

// type ItemsOrder = Array<
//   ReorderItem<DashboardCardsKeys | TodayTasksSectionsKeys>
// >;

export default function ReorderPanelTablet({
  type,
  visible,
  onClose,
  itemsOrder,
  onSaveReorder,
}:
  | {
      type: 'todayTasks';
      visible: boolean;
      onClose: () => void;
      onSaveReorder: (
        newOrder: Array<ReorderItem<TodayTasksSectionsKeys>>,
      ) => void;
      itemsOrder: Array<ReorderItem<TodayTasksSectionsKeys>>;
    }
  | {
      type: 'dashboard';
      visible: boolean;
      onClose: () => void;
      onSaveReorder: (newOrder: Array<ReorderItem<DashboardCardsKeys>>) => void;
      itemsOrder: Array<ReorderItem<DashboardCardsKeys>>;
    }) {
  const { t } = useTranslation('home');
  const { bottom } = useSafeAreaInsets();
  const { colors, space, borderRadius, sizes } = useTheme();
  const [disableSaveBtn, setDisableSaveBtn] = useState(true);

  const [tempOrder, setTempOrder] = useState<
    | Array<ReorderItem<TodayTasksSectionsKeys>>
    | Array<ReorderItem<DashboardCardsKeys>>
  >([]);

  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const handleSave = (
    newOrder:
      | Array<ReorderItem<DashboardCardsKeys>>
      | Array<ReorderItem<TodayTasksSectionsKeys>>,
  ) => {
    const tempArr = [...newOrder];

    itemsOrder.forEach(item => {
      const index = tempArr.findIndex(tempItem => tempItem.key === item.key);
      if (index === -1) {
        tempArr.push(item);
      }
    });

    onSaveReorder(tempArr);
    setDisableSaveBtn(true);
  };

  const handleClose = () => {
    onClose();
  };

  useEffect(() => {
    setTempOrder(itemsOrder);
  }, [itemsOrder]);

  return (
    <SlidingSideModal
      title={t('home.reorder.title')}
      visible={visible}
      onClose={onClose}>
      <View
        style={{
          paddingTop: space[6],
          paddingHorizontal: space[5],
          height:
            space[6] +
            space[19] * tempOrder.length -
            space[4] +
            space[isWideScreen ? 5 : 4] +
            bottom +
            space[12] +
            space[11],
        }}>
        <GestureHandlerRootView>
          <DraggableFlatList
            containerStyle={{ flex: 1 }}
            data={tempOrder}
            onDragEnd={({ data }) => {
              setTempOrder(data);
              setDisableSaveBtn(false);
            }}
            keyExtractor={item => item.key}
            renderItem={props => (
              <View
                style={{
                  marginBottom: space[4],
                }}>
                <CategoryItem {...props} />
              </View>
            )}
            scrollEnabled={false}
          />
        </GestureHandlerRootView>
        <View
          style={{
            paddingBottom: space[isWideScreen ? 5 : 4] + bottom,
          }}>
          <Button
            text="Save"
            disabled={disableSaveBtn}
            onPress={() => (handleSave(tempOrder), handleClose())}
          />
        </View>
      </View>
    </SlidingSideModal>
  );
}

const CategoryItem = ({
  item,
  drag,
  isActive,
}: RenderItemParams<ItemsOrder[number]>) => {
  const { colors, sizes } = useTheme();
  const { t } = useTranslation('home');

  return (
    <ItemContainer style={{}} onPressIn={drag}>
      <DragHandleSVG />
      <CategoryContainer isActive={isActive}>
        {/* <Icon.InvestorInformation
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        /> */}
        {keyMappedIcon({
          key: item.key,
          colors,
          sizes,
        })}
        <CategoryText>{t(item.label as any)}</CategoryText>
      </CategoryContainer>
    </ItemContainer>
  );
};

const keyMappedIcon = ({
  key,
  colors,
  sizes,
}: {
  key: TodayTasksSectionsKeys | DashboardCardsKeys;
  colors: Theme['colors'];
  sizes: Theme['sizes'];
}) => {
  switch (key) {
    /*Comment out TrainerGuruCard on tablet, will open after has design*/
    case 'TrainerGuruCard':
      return (
        <Icon.Progress
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        />
      );
    case 'FWDNewsCard':
      return (
        <Icon.Book fill={colors.primary} height={sizes[5]} width={sizes[5]} />
      );
    case 'PerformanceCard':
      return (
        <Icon.InvestorInformation
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        />
      );
    case 'BusinessOpportunityCard':
      return (
        <Icon.Progress
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        />
      );
    case 'AgencyConfidentialReportSection':
      return (
        <Icon.Report fill={colors.primary} height={sizes[5]} width={sizes[5]} />
      );
    case 'BirthdaySection':
      return (
        <Icon.Birthday
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        />
      );
    case 'ContactLeadsSection':
      return (
        <Icon.Call fill={colors.primary} height={sizes[5]} width={sizes[5]} />
      );
    case 'PaymentRemindersSection':
      return (
        <Icon.Coin2 fill={colors.primary} height={sizes[5]} width={sizes[5]} />
      );
    case 'PolicyIssuesSection':
      return (
        <Icon.Warning
          fill={colors.primary}
          height={sizes[5]}
          width={sizes[5]}
        />
      );
  }
};
const ItemContainer = styled.Pressable(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: theme.space[4],
}));

const CategoryContainer = styled(XView)<{
  isActive: boolean;
}>(({ isActive, theme }) => ({
  flex: 1,
  backgroundColor: isActive
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  alignItems: 'center',
  padding: theme.space[4],
  borderWidth: 1,
  borderColor: isActive
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.medium,
  gap: theme.space[2],
}));

const CategoryText = styled(Typography.LargeBody)(() => ({
  display: 'flex',
}));
