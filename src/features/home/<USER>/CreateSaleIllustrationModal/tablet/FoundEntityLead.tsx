import { useTheme } from '@emotion/react';
import { Box, Button, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Lead } from 'types';

type FoundEntityLeadProps = {
  foundEntityLead: Partial<Lead>;
  onClose?: () => void;
  onConfirm?: () => void;
};

export default function FoundEntityLead({
  foundEntityLead,
  onClose,
  onConfirm,
}: FoundEntityLeadProps) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['home', 'createSaleIllustration']);
  return (
    <Box
      backgroundColor={colors.background}
      width={380}
      padding={space[12]}
      borderRadius={borderRadius.large}>
      <Box>
        <Typography.H5 fontWeight="bold">
          {t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundCompany.title',
          )}
        </Typography.H5>
        <Box p={space[2]} />
        <Typography.Body>
          {t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundLeads.subtitle',
          )}
        </Typography.Body>
        <Typography.Body>
          {t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundCompany.companyName',
            {
              companyName: foundEntityLead?.firstName ?? '--',
            },
          )}
        </Typography.Body>

        <Typography.Body>
          {t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundCompany.registrationNumberNew',
            {
              registrationNumber: foundEntityLead?.natureOfBusiness ?? '--',
            },
          )}
        </Typography.Body>
      </Box>

      <Row gap={space[4]} marginTop={space[12]}>
        <Button
          style={{ flex: 1 }}
          size="medium"
          text={t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundLeads.buttonNoText',
          )}
          variant="secondary"
          onPress={onClose}
        />
        <Button
          style={{ flex: 1 }}
          size="medium"
          text={t(
            'home:home.shortcutSection.createSaleIllustrationModal.foundLeads.buttonYesText',
          )}
          variant="primary"
          onPress={onConfirm}
        />
      </Row>
    </Box>
  );
}
