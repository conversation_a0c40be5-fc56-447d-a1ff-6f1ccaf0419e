import { SvgIconProps } from 'cube-ui-components';
import { CaseStatus } from 'types/case';
import { PartyType } from 'types/party';
import { TFuncKey } from 'i18next';

export type FilterTagConfig = {
  label: TFun<PERSON><PERSON><PERSON><['savedProposals']>;
  type: FilterStatus;
};

export type EntityFilterTagsConfig = {
  label: TFuncKey<['savedProposals']>;
  type: PartyType;
  icon?: (props: SvgIconProps) => JSX.Element;
};
export type FilterStatus =
  | CaseStatus.FNA
  | CaseStatus.CFF
  | CaseStatus.QUICK_SI
  | CaseStatus.FULL_SI
  | CaseStatus.IN_APP;

export type SortDirectionKeys = 'newest' | 'oldest';
