import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import Table from 'components/Table';
import {
  Box,
  Chip,
  Column,
  Icon,
  LoadingIndicator,
  Row,
  Typography,
} from 'cube-ui-components';
import { sub } from 'date-fns';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import ProposalsTable from 'features/savedProposals/components/tablet/ProposalsTable'; // TempProposaLDataType,
import SavedProposalsTableTitleRow from 'features/savedProposals/components/tablet/SavedProposalsTableTitleRow';
import {
  entityFilterTagsConfig,
  filterTagsConfig,
  getStatusFilterParams,
  styleConfig,
  tableConfig,
  updateDateFilterConfig,
} from 'features/savedProposals/config';
import { SortDirectionKeys } from 'features/savedProposals/types';
import useBoundStore from 'hooks/useBoundStore';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import {
  useGetSavedProposalCountMap,
  useGetSavedProposalsFilters,
} from 'hooks/useGetSavedProposalsFilters';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView } from 'react-native';
import { RootStackParamList, ShownMainTabParamList } from 'types';
import { CaseStatus } from 'types/case';
import { PartyType } from 'types/party';
import { SavedProposal } from 'types/proposal';
import { moduleConfigs } from 'utils/config/module';
import { country } from 'utils/context';
import SavedProposalsSearchSection from './SavedProposalsSearchSection';
import GATracking from 'utils/helper/gaTracking';

const currentCountryDefaultStatusFilterParams = getStatusFilterParams();

export default function SavedProposals() {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['savedProposals']);
  const backgroundColor = colors.palette.fwdGrey[50];
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const { params } = useRoute<RouteProp<ShownMainTabParamList, 'Proposals'>>();
  const { current: today } = useRef(new Date());

  const caseActions = useBoundStore(state => state.caseActions);
  const quotationActions = useBoundStore(state => state.quotationActions);

  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const [interval, setInterval] = useState<string>(
    updateDateFilterConfig.filter[0].interval,
  );
  const [isSearching, setIsSearching] = useState(false);
  const [statusFilterParams, setsStatusFilterParams] = useState<CaseStatus[]>(
    [],
  );

  const [clientTypeFilterParams, setClientTypeFilterParams] = useState<
    PartyType[]
  >([]);

  const [showInFilterParams, setShowInFilterParams] = useState<string[]>([
    updateDateFilterConfig.filter[0].label,
  ]);

  useFocusEffect(
    React.useCallback(() => {
      setIsSearching(false);
    }, []),
  );

  const startDateOfProposals = updateDateFilterConfig.showFilter
    ? sub(today, { days: parseInt(interval) }).toISOString()
    : undefined;

  const { isLoading, isRefetching, refetch, data, hasNextPage, fetchNextPage } =
    useGetSavedProposals({
      offset: 0,
      limit: tableConfig.savedProposals.queryLimit,
      status:
        statusFilterParams.length > 0
          ? statusFilterParams
          : currentCountryDefaultStatusFilterParams,
      start: startDateOfProposals,
      clientTypes: clientTypeFilterParams,
      sort_by: order === 'oldest' ? '+updatedAt' : '-updatedAt',
      q: undefined,
    });

  const {
    data: proposalFilterCounts,
    isLoading: isProposalFilterCountLoading,
  } = useGetSavedProposalsFilters({
    start: startDateOfProposals,
    clientTypes: tableConfig.savedProposals.excludeEntity
      ? ['INDIVIDUAL']
      : undefined,
  });
  const totalProposalCount = useGetSavedProposalCountMap(proposalFilterCounts);

  useEffect(() => {
    if (params?.preFilter === 'inSI') {
      setsStatusFilterParams([CaseStatus['FULL_SI']]);
    }
    if (params?.preFilter === 'inApplication') {
      setsStatusFilterParams([CaseStatus['IN_APP']]);
    }
  }, [params]);

  const savedProposalsList = useMemo(() => {
    if (!data) {
      return [];
    }

    if (tableConfig.savedProposals.excludeEntity) {
      return data?.pages
        .map(page => page.data)
        .flat()
        .filter(item => item.parties?.[0]?.clientType !== 'ENTITY');
    }

    return data?.pages.map(page => page.data).flat();
  }, [data]);

  const onProposalItemPress = useCallback(
    (data: SavedProposal) => {
      caseActions.setActiveCase(data.caseId);
      switch (data.latestStatus) {
        case CaseStatus.QUICK_SI:
        case CaseStatus.FULL_SI:
          data.quotationId && quotationActions.setQuotationId(data.quotationId);
          navigate('SalesIllustrationForm', { from: 'saved_proposals' }); // TODO: need to check if is start RPQ
          break;
        case CaseStatus.IN_APP:
          GATracking.logCustomEvent('application', {
            action_type: 'eapp_open_form',
            form_source: 'saved_proposals ',
          });
          navigate('EApp');
          break;
        case CaseStatus.CFF:
          navigate('CustomerFactFind');
          break;
        case CaseStatus.FNA:
          navigate('Fna');
          break;
        default:
          console.warn(`Need to handle ${data.latestStatus} case status`);
      }
    },
    [navigate, quotationActions, caseActions],
  );

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const onProposalItemPressWithLoading = useCallback(
    (data: SavedProposal) => {
      setAppLoading();
      setTimeout(setAppIdle, 500);

      return onProposalItemPress(data);
    },
    [onProposalItemPress, setAppIdle, setAppLoading],
  );

  const currentCount =
    statusFilterParams.length == 0
      ? totalProposalCount.total
      : statusFilterParams.reduce((acc, cur) => {
          const key = cur as keyof typeof totalProposalCount;
          if (key in totalProposalCount) {
            acc += totalProposalCount?.[key] ?? 0;
          }
          return acc;
        }, 0);

  if (isSearching) {
    return (
      <SavedProposalsSearchSection
        interval={interval}
        isSearching={isSearching}
        setIsSearching={setIsSearching}
      />
    );
  }

  return (
    <AnimatedViewWrapper style={{ flex: 1 }}>
      <Column
        backgroundColor={backgroundColor}
        paddingLeft={space[8]}
        paddingRight={space[6]}
        paddingTop={space[5]}
        flex={1}>
        <TitleRow setIsSearching={setIsSearching} />
        <FilterRow
          setInterval={setInterval}
          statusFilterParams={statusFilterParams}
          setsStatusFilterParams={setsStatusFilterParams}
          clientTypeFilterParams={clientTypeFilterParams}
          setClientTypeFilterParams={setClientTypeFilterParams}
          showInFilterParams={showInFilterParams}
          setShowInFilterParams={setShowInFilterParams}
        />
        <Box
          marginTop={styleConfig.savedProposalMarginTop}
          marginBottom={styleConfig.savedProposalMarginBottom}>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {statusFilterParams.length
              ? t('savedProposals:filtered')
              : t('savedProposals:totalSavedProposal')}
            {` (${currentCount})`}
          </Typography.Body>
        </Box>
        <Column flex={1} borderRadius={borderRadius.large}>
          <ProposalsTable
            mode="paginated"
            isRefreshing={isRefetching}
            onRefresh={refetch}
            proposalListData={savedProposalsList}
            ListEmptyComponent={() =>
              isLoading ? (
                <Box
                  backgroundColor={colors.background}
                  minHeight={120}
                  justifyContent="center"
                  alignItems="center"
                  borderBottomRadius={borderRadius.large}>
                  <Box h={space[5]} width={space[5]}>
                    <LoadingIndicator size={space[5]} />
                  </Box>
                </Box>
              ) : (
                <Table.ListEmptyRecord height={84} width={100} />
              )
            }
            HeaderComponent={() => (
              <SavedProposalsTableTitleRow
                sortOrder={order}
                setSortOrder={setOrder}
                backgroundColor={backgroundColor}
                currencyAbbreviation={
                  tableConfig.savedProposals.currencyAbbreviation
                }
              />
            )}
            onPressItem={
              country === 'id'
                ? onProposalItemPressWithLoading
                : onProposalItemPress
            }
            onEndReached={() => {
              if (hasNextPage) {
                fetchNextPage();
              }
            }}
            isLastPage={!hasNextPage}
          />
        </Column>
      </Column>
    </AnimatedViewWrapper>
  );
}

function TitleRow({
  setIsSearching,
}: {
  setIsSearching: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation('savedProposals');

  return (
    <Row
      justifyContent="space-between"
      alignItems="center"
      marginBottom={styleConfig.titleMarginBottom}>
      <Typography.H6 fontWeight="bold">Saved proposals</Typography.H6>
      {/* <TouchableOpacity onPress={() => setIsSearching(!isSearching)}>
        <Row
          borderRadius={borderRadius.full}
          height={space[11]}
          width={288}
          alignItems="center"
          backgroundColor={colors.palette.white}
          borderWidth={1}
          borderColor={colors.primary}> */}
      <SearchButton onPress={() => setIsSearching(pre => !pre)}>
        <Box marginLeft={space[4]} marginRight={space[2]}>
          <Icon.Search />
        </Box>
        <Typography.H7 fontWeight="medium" color={colors.primary}>
          {t('search.searchBar.placeholder')}
        </Typography.H7>
      </SearchButton>
      {/* </Row>
      </TouchableOpacity> */}
    </Row>
  );
}

const SearchButton = styled.TouchableOpacity(
  ({ theme: { colors, space, borderRadius, getElevation } }) => ({
    flexDirection: 'row',
    borderColor: colors.palette.fwdOrange[50],
    borderWidth: 2,
    borderRadius: borderRadius['x-large'],
    height: space[11],
    width: 288,
    alignItems: 'center',
    backgroundColor: colors.palette.white,
  }),
);

function FilterRow({
  setInterval,
  showInFilterParams,
  setShowInFilterParams,
  clientTypeFilterParams,
  setClientTypeFilterParams,
  statusFilterParams,
  setsStatusFilterParams,
}: {
  setInterval: React.Dispatch<React.SetStateAction<string>>;
  showInFilterParams: string[];
  setShowInFilterParams: React.Dispatch<React.SetStateAction<string[]>>;
  clientTypeFilterParams: PartyType[];
  setClientTypeFilterParams: React.Dispatch<React.SetStateAction<PartyType[]>>;
  statusFilterParams: CaseStatus[];
  setsStatusFilterParams: React.Dispatch<React.SetStateAction<CaseStatus[]>>;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['savedProposals']);

  return (
    <Row width={'100%'} gap={space[2]} alignItems="center">
      {updateDateFilterConfig.showFilter && (
        <Row alignItems="center" gap={space[2]} mr={space[2]}>
          <Typography.H8 color={colors.palette.fwdGreyDarkest}>
            {t('savedProposals:showIn')}
          </Typography.H8>
          {updateDateFilterConfig.filter?.map(item => (
            <Chip
              key={item.label}
              label={item.label}
              focus={showInFilterParams.includes(item.label)}
              onPress={() => {
                if (!showInFilterParams.includes(item.label)) {
                  setShowInFilterParams([item.label]);
                  setInterval(item.interval);
                }
              }}
            />
          ))}
        </Row>
      )}

      <Typography.H8 color={colors.palette.fwdGreyDarkest}>
        {t('savedProposals:filterBy')}
      </Typography.H8>
      <ScrollView
        horizontal
        scrollEnabled={styleConfig.statusFilterScrollable}
        contentContainerStyle={{
          gap: space[1],
          width: '100%',
          height: space[9],
          alignItems: 'center',
        }}>
        {filterTagsConfig.map(({ type, label }) => {
          if (moduleConfigs[country]?.proposalConfig?.stages[type]) {
            const currentTagTypeisCFForFNA = [
              CaseStatus.CFF,
              CaseStatus.FNA,
            ].includes(type);
            const onlyOrganisationIsOn =
              clientTypeFilterParams.length === 1 &&
              clientTypeFilterParams.includes(PartyType.ENTITY);
            return (
              <Chip
                key={type}
                label={t(label)}
                disabled={currentTagTypeisCFForFNA && onlyOrganisationIsOn}
                focus={statusFilterParams.includes(type)}
                onPress={() => {
                  //** remove the current onPress type if already in the status list
                  if (statusFilterParams.includes(type)) {
                    const statusParamAfterRemovedCurrentType =
                      statusFilterParams.filter(item => item !== type);

                    if (
                      statusParamAfterRemovedCurrentType.length === 1 &&
                      [CaseStatus.CFF, CaseStatus.FNA].some(element =>
                        statusParamAfterRemovedCurrentType.includes(element),
                      )
                    ) {
                      setClientTypeFilterParams(
                        clientTypeFilterParams.filter(
                          item => item !== PartyType.ENTITY,
                        ),
                      );
                    }
                    setsStatusFilterParams(statusParamAfterRemovedCurrentType);

                    return;
                  } else {
                    //** add the current onPress type into the status list
                    const statusParamAfterAddingCurrentType = [
                      ...statusFilterParams,
                      type,
                    ];
                    if (
                      statusParamAfterAddingCurrentType.length === 1 &&
                      [CaseStatus.CFF, CaseStatus.FNA].some(element =>
                        statusParamAfterAddingCurrentType.includes(element),
                      )
                    ) {
                      setClientTypeFilterParams(
                        clientTypeFilterParams.filter(
                          item => item !== PartyType.ENTITY,
                        ),
                      );
                    }
                    setsStatusFilterParams([...statusFilterParams, type]);
                  }
                }}
              />
            );
          }
        })}

        {entityFilterTagsConfig.map(({ type, label, icon }) => {
          if (moduleConfigs[country]?.proposalConfig?.clientType[type]) {
            const currentTagTypeIsOrganisation = type === PartyType.ENTITY;

            const onlyCFFIsOn =
              statusFilterParams.length === 1 &&
              [CaseStatus.CFF, CaseStatus.FNA].some(element =>
                statusFilterParams.includes(element),
              );
            return (
              <Chip
                key={type}
                label={t(label)}
                disabled={currentTagTypeIsOrganisation && onlyCFFIsOn}
                focus={clientTypeFilterParams.includes(type)}
                icon={icon}
                onPress={() => {
                  if (clientTypeFilterParams.includes(type)) {
                    const clientTypeParamAfterRemovedCurrentType =
                      clientTypeFilterParams.filter(item => item !== type);
                    if (
                      clientTypeParamAfterRemovedCurrentType.length === 1 &&
                      clientTypeParamAfterRemovedCurrentType.includes(
                        PartyType.ENTITY,
                      )
                    ) {
                      setsStatusFilterParams(
                        statusFilterParams.filter(
                          item =>
                            item !== CaseStatus.CFF && item !== CaseStatus.FNA,
                        ),
                      );
                    }

                    setClientTypeFilterParams(
                      clientTypeParamAfterRemovedCurrentType,
                    );
                    return;
                  } else {
                    const clientTypeParamAfterAddingCurrentType = [
                      ...clientTypeFilterParams,
                      type,
                    ];
                    if (
                      clientTypeParamAfterAddingCurrentType.length === 1 &&
                      clientTypeParamAfterAddingCurrentType.includes(
                        PartyType.ENTITY,
                      )
                    ) {
                      setsStatusFilterParams(
                        statusFilterParams.filter(
                          item =>
                            item !== CaseStatus.CFF && item !== CaseStatus.FNA,
                        ),
                      );
                    }

                    setClientTypeFilterParams([
                      ...clientTypeFilterParams,
                      type,
                    ]);
                  }
                }}
              />
            );
          }
        })}
      </ScrollView>
    </Row>
  );
}
