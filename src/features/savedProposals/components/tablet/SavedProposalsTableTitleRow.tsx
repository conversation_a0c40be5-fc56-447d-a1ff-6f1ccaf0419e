import React, { Fragment } from 'react';

import { Theme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import Table from 'components/Table';

import { SortDirectionKeys } from '../../types';
import { tableConfig } from '../../config';

export default function SavedProposalsTableTitleRow({
  sortOrder,
  setSortOrder,
  backgroundColor,
  currencyAbbreviation,
}: {
  sortOrder: string;
  setSortOrder: (sortOrder: SortDirectionKeys) => void;
  backgroundColor?: string;
  currencyAbbreviation?: string;
}) {
  const { t } = useTranslation('savedProposals');

  return (
    <Table.ListHeaderStyleWrapper
      wrapperStyle={{ height: 74 }}
      backgroundColor={backgroundColor}>
      {tableConfig.savedProposals.listHeader.map(
        ({ name, width, type }, idx) => {
          switch (type) {
            case 'insured':
              return (
                <Fragment key={name}>
                  <Table.ListHeaderTitle
                    type="width"
                    name={t('insured')}
                    width={width}
                    wrapperStyle={{ paddingLeft: 20 }}
                  />
                  <Table.ColumnSeparator height={18} />
                </Fragment>
              );
            case 'sumAssured':
            case 'premium': {
              return (
                <Fragment key={name}>
                  <Table.ListHeaderTitle
                    type="width"
                    name={
                      currencyAbbreviation
                        ? t(name) + ` (${currencyAbbreviation})`
                        : t(name)
                    }
                    width={width}
                  />
                  <Table.ColumnSeparator height={18} />
                </Fragment>
              );
            }

            default:
              return (
                <Fragment key={name}>
                  <Table.ListHeaderTitle
                    type="width"
                    name={t(name)}
                    width={width}
                  />
                  <Table.ColumnSeparator height={18} />
                </Fragment>
              );
          }
        },
      )}
      <Table.ListHeaderDate
        type="width"
        dateTitle={t(tableConfig.savedProposals.listHeaderDate.dateTitle)}
        width={tableConfig.savedProposals.listHeaderDate.width}
        onPress={() =>
          setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest')
        }
        // * if true, showing ArrowDownSVG
        isSortDateDesc={sortOrder === 'newest'}
      />
    </Table.ListHeaderStyleWrapper>
  );
}
