import { View, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { Button, Typography, Icon, Checkbox } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { FilterDot } from 'components/FilterDot';

import styled from '@emotion/native';
import SlidingSideModal from 'components/SlidingSideModal';

export type FilterOption = {
  type: string;
  label: string;
  isOn: boolean;
};

export default function SavedProposalsFilterModalButton({
  firstFilterSet,
  setHasFiltered,
  hasFiltered,
  firstFilterLabel,
  onPressFirstFilterApply,
  ...restProps
}:
  | {
      firstFilterSet: Array<FilterOption>;
      setHasFiltered: (value: boolean) => void;
      hasFiltered: boolean;
      firstFilterLabel: string;
      onPressFirstFilterApply: (filter: FilterOption[]) => void;
    }
  | {
      setHasFiltered: (value: boolean) => void;
      hasFiltered: boolean;
      firstFilterSet: Array<FilterOption>;
      firstFilterLabel: string;
      onPressFirstFilterApply: (filter: FilterOption[]) => void;
      secondFilterSet: Array<FilterOption>;
      secondFilterLabel: string;
      onPressSecondFilterApply: (filter: FilterOption[]) => void;
    }) {
  const isSecondFilterSetShown = 'secondFilterSet' in restProps;

  const [isOpen, setIsOpen] = useState(false);
  const { colors, space, typography } = useTheme();
  const [localFirstFilter, setLocalFirstFilter] = useState<FilterOption[]>([
    ...firstFilterSet,
  ]);

  const [localSecondFilter, setLocalSecondFilter] = useState<FilterOption[]>(
    isSecondFilterSetShown ? [...restProps.secondFilterSet] : [],
  );
  // const isSourceFilterChanged = localSourceFilter.some(
  //   item =>
  //     item.isOn !== sourceFilter.find(item => item.type === item.type)?.isOn,
  // );
  // console.log(
  //   '🚀 ~ file: LeadScreen.tablet.tsx:730 ~ isSourceFilterChanged:',
  //   isSourceFilterChanged,
  // );
  // const isStatusFilterChanged = localStatusFilter.some(
  //   item =>
  //     item.isOn !== statusFilter.find(item => item.type === item.type)?.isOn,
  // );
  // console.log(
  //   '🚀 ~ file: LeadScreen.tablet.tsx:732 ~ isStatusFilterChanged:',
  //   isStatusFilterChanged,
  // );

  const isBothFiltersNotApplied = isSecondFilterSetShown
    ? localFirstFilter.every(item => item.isOn === false) &&
      localSecondFilter.every(item => item.isOn === false)
    : localFirstFilter.every(item => item.isOn === false);

  const onPressApplyHandler = () => {
    if (isBothFiltersNotApplied) {
      setHasFiltered(false);
    } else {
      setHasFiltered(true);
    }
    onPressFirstFilterApply(localFirstFilter);

    'onPressSecondFilterApply' in restProps
      ? restProps.onPressSecondFilterApply(localSecondFilter)
      : null;

    if ('onPressSecondFilterApply' in restProps) {
      const isAllFilterOn =
        localFirstFilter.every(item => item.isOn === true) &&
        restProps.secondFilterSet.every(item => item.isOn === true);
      if (isAllFilterOn) {
        onPressFirstFilterApply(
          localFirstFilter.map(item => ({ ...item, isOn: false })),
        );
        restProps.onPressSecondFilterApply(
          localSecondFilter.map(item => ({ ...item, isOn: false })),
        );
      }
      setHasFiltered(false);
      onPressClearAll();
    }

    if (localFirstFilter.every(item => item.isOn === true)) {
      onPressFirstFilterApply(
        localFirstFilter.map(item => ({ ...item, isOn: false })),
      );
      setHasFiltered(false);
      onPressClearAll();
    }
    setIsOpen(false);
  };
  const onPressClearAll = () => {
    setLocalFirstFilter(
      localFirstFilter.map(item => ({
        ...item,
        isOn: false,
      })),
    );
    setLocalSecondFilter(
      localSecondFilter.map(item => ({
        ...item,
        isOn: false,
      })),
    );
  };

  return (
    <>
      <TouchableOpacity
        onPress={() => setIsOpen(true)}
        style={{
          justifyContent: 'center',
          alignSelf: 'center',
        }}>
        {hasFiltered && <FilterDot style={{ right: 0, top: 0 }} />}
        <Icon.Filter fill={colors.onBackground} />
      </TouchableOpacity>

      <SlidingSideModal
        title={'Filter'}
        visible={isOpen}
        onClose={() => setIsOpen(false)}>
        <View
          style={{
            padding: space[4],
            gap: space[5],
          }}>
          <View
            style={{
              gap: space[4],
            }}>
            {/* Filter Options */}
            <Typography.H8>{firstFilterLabel}</Typography.H8>
            <View
              style={{
                gap: space[3],
              }}>
              {localFirstFilter.map((item, index) => (
                <Checkbox
                  key={item.type}
                  value={item.isOn}
                  labelStyle={{
                    fontSize: typography.body.size,
                    lineHeight: typography.body.lineHeight,
                  }}
                  onChange={() => {
                    const newSourceFilter = [...localFirstFilter];
                    newSourceFilter[index].isOn = !newSourceFilter[index].isOn;
                    setLocalFirstFilter(newSourceFilter);
                  }}
                  label={item.label}
                />
              ))}
            </View>
            {isSecondFilterSetShown && (
              <>
                <LineSeparator />
                <Typography.H8>{}</Typography.H8>
                <View
                  style={{
                    gap: space[3],
                  }}>
                  {localSecondFilter?.map((item, index) => (
                    <Checkbox
                      key={item.type}
                      value={item.isOn}
                      labelStyle={{
                        fontSize: typography.body.size,
                        lineHeight: typography.body.lineHeight,
                      }}
                      onChange={() => {
                        const newStatusFilter = [...localSecondFilter];
                        newStatusFilter[index].isOn =
                          !newStatusFilter[index].isOn;
                        setLocalSecondFilter(newStatusFilter);
                      }}
                      label={item.label}
                    />
                  ))}
                </View>
              </>
            )}
          </View>
          {isSecondFilterSetShown && <LineSeparator />}
          <Button
            text="Apply"
            variant="primary"
            onPress={onPressApplyHandler}
          />
          <Button
            text="Clear all"
            variant="secondary"
            onPress={onPressClearAll}
          />
        </View>
      </SlidingSideModal>
    </>
  );
}
const LineSeparator = styled.View(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
  width: '100%',
}));
