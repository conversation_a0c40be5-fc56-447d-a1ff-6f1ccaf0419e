import {
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import React, { Fragment, useRef, useState } from 'react';
import { Row, Typography, Icon, Box, Label } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import Table from 'components/Table';
import { Proposal, SavedProposal } from 'types/proposal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { PartyRole, PartyType } from 'types/party';
import { FlashList } from '@shopify/flash-list';
import { isAfter, parse } from 'date-fns';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import { tableConfig } from 'features/savedProposals/config';
import {
  PaymentTranslationKeys,
  paymentKeyMapping,
  paymentKeyToPremiumKey,
} from 'features/savedProposals/tablet/config';
import { country } from 'utils/context';
import RenameQuotationPanelTablet from 'features/proposal/components/RenameQuotationPanel/RenameQuotationPanel.tablet';
import { useUpdateQuotationName } from 'hooks/useGetQuotation';
import { remoteSellingStatus } from 'features/savedProposals/utli';

type TableHeaderKeys =
  | 'insured'
  | 'proposalName'
  | 'product'
  | 'productStage'
  | 'sumAssured'
  | 'premium';

export default function ProposalsTable({
  HeaderComponent,
  proposalListData,
  ListEmptyComponent,
  onPressItem,
  onRefresh,
  isRefreshing,
  ...props
}:
  | {
      mode: 'all';
      HeaderComponent: React.ComponentType<any>;
      ListEmptyComponent?: React.ComponentType<any>;
      proposalListData: Array<SavedProposal>;
      onPressItem?: (data: SavedProposal) => void;
      onRefresh?: () => void;
      isRefreshing?: boolean;
    }
  | {
      mode: 'paginated';
      HeaderComponent: React.ComponentType<any>;
      ListEmptyComponent?: React.ComponentType<any>;
      proposalListData: Array<SavedProposal>;
      onPressItem?: (data: SavedProposal) => void;
      onRefresh?: () => void;
      isRefreshing: boolean;
      onEndReached: () => void;
      isLastPage: boolean;
    }) {
  const { bottom } = useSafeAreaInsets();
  const { space } = useTheme();
  const [showSaveQuotationPanel, setShowSaveQuotationPanel] = useState(false);
  const quotationIdRef = useRef<string | undefined>();
  const caseIdRef = useRef<string | undefined>();
  const [quotationName, setQuotationName] = useState<string | undefined>();
  const { mutateAsync: updateQuotationName } = useUpdateQuotationName();

  return (
    <>
      <HeaderComponent />
      <FlashList
        keyExtractor={item =>
          item?.caseId + item?.quotationId + item?.updatedAt
        }
        estimatedItemSize={100}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing ?? false}
            onRefresh={onRefresh}
          />
        }
        contentContainerStyle={{ paddingBottom: bottom + space[4] }}
        showsHorizontalScrollIndicator={false}
        data={proposalListData}
        ListEmptyComponent={
          ListEmptyComponent ? <ListEmptyComponent /> : undefined
        }
        // ListFooterComponent={
        //   proposalListData.length > 0 ? (
        //     <SimpleLoadingFooter
        //       isLastPage={Boolean(
        //         props.mode == 'paginated' ? props?.isLastPage : false,
        //       )}
        //       isLoading={isRefreshing ?? false}
        //     />
        //   ) : (
        //     <React.Fragment />
        //   )
        // }
        ItemSeparatorComponent={() => <Table.RowSeparator />}
        renderItem={({ item, index }) => (
          <TableContentRow
            data={item}
            index={index}
            onPress={onPressItem}
            dataCount={proposalListData.length ?? 0}
            onEditName={props => {
              setQuotationName(props?.quotation?.quotationName);
              quotationIdRef.current = props?.quotationId;
              caseIdRef.current = props?.caseId;
              setShowSaveQuotationPanel(true);
            }}
          />
        )}
        onEndReached={
          props.mode === 'paginated' ? props.onEndReached : undefined
        }
        onEndReachedThreshold={props.mode === 'paginated' ? 0.1 : undefined}
      />
      <RenameQuotationPanelTablet
        visible={showSaveQuotationPanel}
        onClose={() => setShowSaveQuotationPanel(false)}
        isFirstVersion={false}
        quotationName={quotationName ?? ''}
        disableTextField={false}
        onSave={async (quotationName: string) => {
          await updateQuotationName({
            requestData: {
              quotationName: quotationName,
            },
            caseId: caseIdRef.current ?? '',
            quotationId: quotationIdRef.current ?? '',
          });
          onRefresh?.();
        }}
        // isFirstVersion={!isResumeQuotation}
        // defaultQuotationName={defaultQuotationName}
        // error={saveProposalNameError}
      />
    </>
  );
}

function SimpleLoadingFooter({
  isLoading,
  isLastPage,
}: {
  isLastPage: boolean;
  isLoading: boolean;
}) {
  if (isLastPage) {
    return (
      <Box style={{ alignItems: 'center', marginTop: 24 }}>
        <Label>END</Label>
      </Box>
    );
  }
  return isLoading ? <ActivityIndicator style={{ marginTop: 24 }} /> : <></>;
}

// // * ~~~~~~~~~~~~~~~~~~~~~~~~~~ Each row of table content
function TableContentRow({
  data,
  index,
  dataCount,
  onPress,
  onEditName,
}: {
  data: SavedProposal;
  index: number;
  dataCount: number;
  onPress?: (data: SavedProposal) => void;
  onEditName?: (data: SavedProposal) => void;
}) {
  const { t } = useTranslation('savedProposals');

  const { sizes, space, colors, borderRadius } = useTheme();

  const clientType = data.parties?.find(({ roles }) =>
    roles.includes(PartyRole.PROPOSER),
  )?.clientType;

  const renderExpired = () => {
    if (country === 'my') {
      return (
        <ExpiredBG>
          <Typography.SmallBody color={colors.error}>
            {t('savedProposals:expired' as any)}
          </Typography.SmallBody>
        </ExpiredBG>
      );
    }
    return (
      <ExpiredContainer>
        <Box style={{ marginRight: space[1] }}>
          <Icon.OperatingHours fill={colors.error} size={18} />
        </Box>
        <Typography.H8 color={colors.error}>
          {t('savedProposals:expired' as any)}
        </Typography.H8>
      </ExpiredContainer>
    );
  };

  const dataHandler = (
    type: TableHeaderKeys,
    data: SavedProposal, // TODO update with the actual data
  ) => {
    if (typeof data === 'undefined' || data === null) return <Text>--</Text>;
    const isEditNameOn = false;

    const isExpired = () => {
      if (!data.quotation?.expiryDate) {
        return false;
      }

      const ed = parse(data.quotation?.expiryDate, 'yyyy-MM-dd', new Date());
      if (!ed) {
        return false;
      }

      return isAfter(Date.now(), ed);
    };

    switch (type) {
      case 'insured': {
        const insured = (data?.parties || []).find(
          (party: SavedProposal['parties'][number]) =>
            party.roles.includes(PartyRole.INSURED),
        );
        const displayName =
          !insured?.person.name.firstName && !insured?.person.name.lastName
            ? data.fna?.name
            : (insured?.person.name.firstName ?? '') +
              ' ' +
              (insured?.person.name.lastName ?? '');

        return (
          <Typography.H8 numberOfLines={3} style={{ marginLeft: 8 }}>
            {displayName}
          </Typography.H8>
        );
      }

      case 'proposalName':
        return (
          <Row alignItems="center" gap={space[3]} pl={space[1]}>
            <Row gap={space[1]} flex={1}>
              {tableConfig?.savedProposals?.proposalName?.showIcon && (
                <>
                  {clientType === PartyType.INDIVIDUAL && (
                    <ClientTypeIconContainer>
                      <Icon.Account
                        width={14}
                        height={14}
                        fill={colors.secondary}
                      />
                    </ClientTypeIconContainer>
                  )}
                  {clientType === PartyType.ENTITY && (
                    <ClientTypeIconContainer>
                      <Icon.Office
                        width={14}
                        height={14}
                        fill={colors.secondary}
                      />
                    </ClientTypeIconContainer>
                  )}
                </>
              )}
              <Typography.H8 numberOfLines={3} style={[{ flex: 1 }]}>
                {/* {(data?.proposalName as string) ?? '--'} */}
                {data.quotation?.quotationName ?? '--'}
              </Typography.H8>
            </Row>
            {(country === 'ib' || country === 'id') && (
              <TouchableOpacity
                onPress={() => {
                  onEditName?.(data);
                }}>
                <Icon.Edit
                  size={sizes[4]}
                  fill={colors.palette.fwdDarkGreen[100]}
                />
              </TouchableOpacity>
            )}
          </Row>
        );
      case 'product':
        return (
          <Typography.H8>
            {data?.quotation?.plans?.[0].productName
              ? getProductName(data?.quotation?.plans?.[0].productName)
              : '--'}
          </Typography.H8>
        );
      case 'productStage': {
        const remoteSellingStatusName = remoteSellingStatus(
          data?.remoteSelling,
        );
        return (
          <Box>
            <Typography.H8>
              {t(`filter.${data.latestStatus}` as any)}
            </Typography.H8>
            {isExpired() && renderExpired()}

            {/* display remote selling status if present */}
            {data?.isRemoteSelling && !!remoteSellingStatusName && (
              <Typography.H8
                style={{ marginTop: isExpired() ? space[2] : 0 }}
                color={colors.palette.fwdGreyDarker}>
                {t(remoteSellingStatusName as any)}
              </Typography.H8>
            )}
          </Box>
        );
      }
      case 'sumAssured':
        return (
          <Typography.H8>
            {tableConfig.savedProposals.showCurrencyInTable &&
              data.quotation?.plans?.[0].currency &&
              data.quotation.plans[0].currency + '\n'}
            {data.quotation?.plans?.[0].sumAssured
              ? numberToThousandsFormat(data.quotation?.plans?.[0].sumAssured)
              : '--'}
          </Typography.H8>
        );
      case 'premium': {
        const paymentEnum =
          data.quotation?.basicInfo?.paymentMode ??
          data?.quotation?.plans?.[0].paymentMode;
        const activePremium = paymentEnum
          ? data?.quotation?.summary?.[paymentKeyToPremiumKey[paymentEnum]] ??
            null
          : null;

        if (data?.quotation?.summary?.annualPrem == null) {
          return <Typography.H8> -- </Typography.H8>;
        } else {
          return (
            <Typography.H8>
              {tableConfig.savedProposals.showCurrencyInTable &&
                data.quotation?.plans?.[0].currency &&
                data.quotation.plans[0].currency + ' '}
              {numberToThousandsFormat(activePremium, undefined, '--')}
              {`\n/ ` +
                (paymentEnum ? t(paymentKeyMapping[paymentEnum]) : '--')}
            </Typography.H8>
          );
        }
      }
      default:
        return <></>;
    }
  };

  return (
    <Table.ListItemStyleWrapper
      wrapperStyle={[{ minHeight: 75, paddingVertical: space[3] }]}
      index={index}
      dataCount={dataCount}
      onPress={() => onPress?.(data as SavedProposal)}>
      {tableConfig.savedProposals.listHeader.map((item, idx) => {
        return (
          <Fragment key={index + 'contentROw' + idx}>
            <Box justifyContent="center" px={space[3]} w={item.width}>
              {dataHandler(item.type, data)}
            </Box>
            <Table.ColumnSeparator
              color={colors.palette.fwdGrey[100]}
              height={18}
            />
          </Fragment>
        );
      })}
      {data?.quotation?.updatedAt == null && data?.updatedAt == null ? (
        <Typography.H8
          style={{
            justifyContent: 'center',
            alignSelf: 'center',
            padding: 16,
            width: tableConfig.savedProposals.listHeaderDate.width,
          }}>
          --
        </Typography.H8>
      ) : (
        //TODO: felix update date format here with different country
        <Table.ListItemDate
          date={data?.updatedAt ?? '--'}
          width={tableConfig.savedProposals.listHeaderDate.width}
          showIcon={data.latestStatus != 'APP_SUBMITTED'}
        />
      )}
    </Table.ListItemStyleWrapper>
  );
}

export const paymentModeMapping: Record<
  Exclude<Proposal['paymentMode'], null>,
  PaymentTranslationKeys
> = {
  EVERY_MONTH: 'paymentMode.EVERY_MONTH',
  EVERY_QUARTER: 'paymentMode.EVERY_QUARTER',
  EVERY_HALF_YEAR: 'paymentMode.EVERY_HALF_YEAR',
  EVERY_YEAR: 'paymentMode.EVERY_YEAR',
  ONE_TIME: 'paymentMode.ONE_TIME',
};

const ExpiredBG = styled.View(({ theme }) => ({
  alignSelf: 'flex-start',
  paddingHorizontal: theme.space[1],
  paddingVertical: 2,
  marginTop: theme.space[1],
  borderRadius: 2,
  backgroundColor: theme.colors.palette.alertRedLight,
}));

const ExpiredContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  marginTop: theme.space[1],
}));

const ClientTypeIconContainer = styled.View(({ theme }) => ({
  marginRight: theme.space[1],
  paddingTop: 2,
  alignSelf: 'flex-start',
}));
