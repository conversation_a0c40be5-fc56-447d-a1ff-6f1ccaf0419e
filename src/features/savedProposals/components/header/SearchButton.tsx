import { memo, useState } from 'react';
import { IconContainer } from './IconContainer';
import { SearchIcon } from './SearchIcon';
import { SearchProposalModal } from '../SearchProposalModal';
import { SavedProposalsQueryParams } from 'api/caseApi';

export const SearchButton = memo(function SearchButton(
  props: SavedProposalsQueryParams,
) {
  const [searchModalVisible, setSearchModalVisible] = useState(false);

  return (
    <>
      <IconContainer onPress={() => setSearchModalVisible(true)}>
        <SearchIcon />
      </IconContainer>

      <SearchProposalModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(false)}
        {...props}
      />
    </>
  );
});
