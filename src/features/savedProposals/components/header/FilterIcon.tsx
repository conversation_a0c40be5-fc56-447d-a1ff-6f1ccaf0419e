import { memo } from 'react';
import { ColorValue } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface Props {
  fill?: ColorValue;
  active?: boolean;
}

export const FilterIcon = memo(function FilterIcon({
  fill = '#183028',
  active,
}: Props) {
  return (
    <Svg fill="none" width={24} height={24}>
      <Path
        fill={!active ? fill : 'none'}
        fillRule="evenodd"
        d="M16 13.5a1.001 1.001 0 0 1 0-2 1.001 1.001 0 0 1 0 2Zm4.5-2h-1.674A3.003 3.003 0 0 0 16 9.5a3.003 3.003 0 0 0-2.825 2H3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h9.676a3.002 3.002 0 0 0 2.824 2 3.002 3.002 0 0 0 2.825-2H20.5a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5ZM8 19.5a1.001 1.001 0 0 1 0-2 1.001 1.001 0 0 1 0 2Zm0-4a3.003 3.003 0 0 0-2.824 2H3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1.676a3.002 3.002 0 0 0 2.825 2 3.002 3.002 0 0 0 2.824-2H19.5A1.5 1.5 0 0 0 21 18v-.5H10.825a3.003 3.003 0 0 0-2.824-2Zm0-8a1.001 1.001 0 0 1 0-2 1.001 1.001 0 0 1 0 2Zm2.825-2a3.003 3.003 0 0 0-2.824-2 3.003 3.003 0 0 0-2.825 2H3.5A.5.5 0 0 0 3 6v1a.5.5 0 0 0 .5.5h1.676a3.002 3.002 0 0 0 2.825 2 3.002 3.002 0 0 0 2.824-2H20.5A.5.5 0 0 0 21 7V5.5H10.825Z"
        clipRule="evenodd"
      />
      <Path
        fill={active ? fill : 'none'}
        fillRule="evenodd"
        d="M9 6.5a1 1 0 1 0-1.998.001A1 1 0 0 0 9 6.5Zm-3.824 1H3.5A.5.5 0 0 1 3 7V6a.5.5 0 0 1 .5-.5h1.676A3.002 3.002 0 0 1 8 3.5c1.303 0 2.412.837 2.825 2h2.533c.16.747.496 1.428.963 2h-3.496A3.002 3.002 0 0 1 8 9.5a3.003 3.003 0 0 1-2.824-2ZM16 13.5a1 1 0 1 1 .002-2.001 1 1 0 0 1-.002 2Zm4.5-2h-1.675A3.003 3.003 0 0 0 16 9.5a3.003 3.003 0 0 0-2.825 2H3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h9.675a3.003 3.003 0 0 0 2.825 2 3.002 3.002 0 0 0 2.825-2H20.5a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5ZM8 19.5a1 1 0 1 1 .002-2.001 1 1 0 0 1-.003 2.001Zm0-4a3.003 3.003 0 0 0-2.824 2H3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1.676A3.003 3.003 0 0 0 8 21.5a3.002 3.002 0 0 0 2.825-2H19.5A1.5 1.5 0 0 0 21 18v-.5H10.825A3.003 3.003 0 0 0 8 15.5Z"
        clipRule="evenodd"
      />
      <Path
        fill={active ? '#E87722' : 'none'}
        fillRule="evenodd"
        d="M18.25 1A3.254 3.254 0 0 0 15 4.25a3.266 3.266 0 0 0 2.002 3 3.233 3.233 0 0 0 2.496 0 3.268 3.268 0 0 0 1.752-1.752 3.254 3.254 0 0 0-3-4.498Z"
        clipRule="evenodd"
      />
    </Svg>
  );
});
