import styled from '@emotion/native';
import { LargeBody } from 'cube-ui-components';
import { memo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import Cell, { CellProps } from './Cell';

const Text = styled(LargeBody)(({ theme }) => ({
  color: theme.colors.secondary,
}));

interface Props extends CellProps {
  data?: string;
  style?: StyleProp<ViewStyle>;
}

export const TextCell = memo(function TextCell({ data, ...rest }: Props) {
  return (
    <Cell {...rest}>
      <Text numberOfLines={2}>{data ? data : '--'}</Text>
    </Cell>
  );
});
export default TextCell;
