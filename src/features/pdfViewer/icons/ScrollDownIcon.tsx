import Svg, { <PERSON><PERSON><PERSON><PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const ScrollDownIcon = (props: SvgProps) => {
  return (
    <Svg width={40} height={40} viewBox="0 0 40 40" fill="none" {...props}>
      <G clipPath="url(#clip0_25360_209016)">
        <Path
          d="M5.349 5.949a1.172 1.172 0 00-1.658.016L.96 8.75a1.172 1.172 0 001.673 1.64l.724-.737V24.49a1.172 1.172 0 002.344 0V9.576l.793.779a1.172 1.172 0 101.642-1.673L5.349 5.949zm16.558 27.986a9.87 9.87 0 007.54.985 9.87 9.87 0 006.037-4.626l2.525-4.373c.911-1.58 1.367-3.275 1.368-4.97 0-1.695-.456-3.39-1.368-4.97l-5.743-9.945a2.605 2.605 0 00-3.552-.955c-1.164.671-2.005 1.73-2.368 2.98a4.843 4.843 0 00.474 3.806l.342.593-11.03-6.368a3.584 3.584 0 00-4.89 1.31 3.54 3.54 0 00-.454 2.199 3.554 3.554 0 00-2.391 1.714 3.555 3.555 0 00-.356 2.716c.248.925.84 1.697 1.666 2.174l5.31 3.066c-.32.267-.592.59-.806.962a3.549 3.549 0 00-.354 2.712c.086.322.213.625.378.904-.423.28-.79.66-1.06 1.128a3.395 3.395 0 001.241 4.632l7.49 4.326zm1.172-2.03l-7.49-4.325a1.049 1.049 0 011.05-1.817l2.918 1.685a1.172 1.172 0 101.172-2.03l-4.038-2.33a1.225 1.225 0 01-.57-.75 1.222 1.222 0 01.12-.934c.164-.285.43-.488.749-.574a1.226 1.226 0 01.924.114l4.048 2.337a1.172 1.172 0 001.172-2.03L10.88 14.174a1.226 1.226 0 01-.573-.75 1.228 1.228 0 01.87-1.51c.32-.085.653-.042.935.121l12.259 7.076a1.172 1.172 0 001.172-2.03L13.724 10.26a1.226 1.226 0 01-.573-.751 1.22 1.22 0 01.12-.934 1.237 1.237 0 011.689-.453l14.818 8.555a1.172 1.172 0 001.6-1.601l-2.529-4.38a2.569 2.569 0 01-.352-1.288c0-.233.033-.465.1-.693.193-.667.65-1.236 1.287-1.603a.261.261 0 01.352.094l5.743 9.947c1.394 2.414 1.394 5.182 0 7.596l-2.524 4.374a7.542 7.542 0 01-4.614 3.534 7.541 7.541 0 01-5.762-.752z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_25360_209016">
          <Path fill="#fff" transform="matrix(0 1 1 0 0 0)" d="M0 0H40V40H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
