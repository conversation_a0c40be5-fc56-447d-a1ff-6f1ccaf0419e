import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import FloatingHintButton from 'components/FloatingHintButton';
import { Box, H6, Icon, LargeLabel } from 'cube-ui-components';
import EmailSharing from 'features/pdfViewer/components/EmailSharing/EmailSharing';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import Pdf from 'react-native-pdf';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import useSavePdf from '../hooks/useSavePdf';
import useSharePdf from '../hooks/useSharePdf';

const HIT_SLOP = { top: 12, bottom: 12, left: 12, right: 12 };
const DEFAULT_PRODUCT_HIGHLIGHT_NAME = 'Product Highlight Sheet';
const DEFAULT_SI_PDF = 'Sales Illustration';
const DEFAULT_BROCHURE_PDF = 'Product brochure';

type Sharable = {
  sharable?: boolean;
} & (
  | {
      shareType: 'email';
      mailConfig?: {
        mailTo?: string;
        mailCc: string;
        subject: string;
        content: string;
        html: string;
      };
    }
  | {
      shareType: 'social-media';
      mailConfig?: undefined;
    }
);

interface NonSharable {
  sharable?: undefined | false;
  shareType?: undefined;
  mailConfig?: undefined;
}

export type PdfViewerOptions = {
  title: string;
  url?: string;
  base64?: string;
  fileName?: string;
  downloadable?: boolean;
  attachmentName?: string;
  productHighlightUrl?: string;
  brochureUrl?: string;
  iconLeft?: 'cross' | 'arrow';
  password?: string;
  isRequiredToReadAllPdf?: boolean;
} & (Sharable | NonSharable);

/**
 * @deprecated This function is deprecated and will be removed in future releases.
 */
export default function PdfViewerOld({
  title,
  url,
  base64,
  fileName,
  downloadable,
  sharable,
  shareType,
  mailConfig,
  attachmentName,
  productHighlightUrl,
  brochureUrl,
  iconLeft = 'cross',
  password,
  isRequiredToReadAllPdf,
}: PdfViewerOptions) {
  const { t } = useTranslation(['common', 'proposal']);
  const { sizes, colors } = useTheme();
  const { canGoBack, goBack, navigate } =
    useNavigation<NavigationProp<RootStackParamList>>();

  const { savePdf } = useSavePdf();
  const { sharePdf } = useSharePdf();
  const [pdfTitle, setPdfTitle] = useState('');
  const [emailSharingVisible, setEmailSharingVisible] = useState(false);
  const [isSendPdfEnabled, setSendPdfEnabled] = useState(false);

  const isSendPdfDisabled = !isSendPdfEnabled && isRequiredToReadAllPdf;

  const attachments = useMemo(() => {
    const data = [
      {
        attachmentBase64: base64,
        attachmentUrl: url,
        attachmentName: attachmentName || DEFAULT_SI_PDF,
        required: true,
        selected: true,
      },
    ];

    if (productHighlightUrl) {
      data.push({
        attachmentBase64: undefined,
        attachmentUrl: productHighlightUrl,
        attachmentName: DEFAULT_PRODUCT_HIGHLIGHT_NAME,
        required: true,
        selected: true,
      });
    }

    if (brochureUrl) {
      data.push({
        attachmentBase64: undefined,
        attachmentUrl: encodeURI(brochureUrl),
        attachmentName: DEFAULT_BROCHURE_PDF,
        required: false,
        selected: true,
      });
    }

    return data;
  }, [base64, url, attachmentName, productHighlightUrl, brochureUrl]);

  const onShare = async () => {
    if (shareType === 'email') {
      setEmailSharingVisible(true);
    }
    if (shareType === 'social-media') {
      sharePdf({ url, base64, defaultName: fileName || pdfTitle });
    }
  };

  // const base64ViewPdf = base64
  //   ? {
  //       base64: Platform.select({
  //         android: 'data:application/pdf;base64,' + base64,
  //         default: base64,
  //       }),
  //     }
  //   : {};
  const base64ViewPdf = base64 && 'data:application/pdf;base64,' + base64;

  const { isTabletMode } = useLayoutAdoptionCheck();

  const handleOnScroll = (page: number, numberOfPages: number) => {
    setSendPdfEnabled(page === numberOfPages);
  };

  return (
    <Container>
      <Header>
        {isTabletMode ? (
          <H6Title fontWeight="bold" numberOfLines={1}>
            {title}
          </H6Title>
        ) : (
          <Title fontWeight="bold" numberOfLines={1}>
            {title}
          </Title>
        )}
        <IconButton
          hitSlop={HIT_SLOP}
          onPress={() => {
            if (canGoBack()) {
              goBack();
            } else {
              navigate('Main');
            }
          }}>
          {iconLeft == 'arrow' ? (
            <Icon.ArrowLeft size={sizes[6]} fill={colors.secondary} />
          ) : (
            <Icon.Close size={sizes[6]} fill={colors.secondary} />
          )}
        </IconButton>
        <Box flex={1} />
        {downloadable && (
          <IconButton
            hitSlop={HIT_SLOP}
            onPress={() => {
              savePdf({ url, base64, defaultName: fileName || pdfTitle });
            }}>
            <Icon.Download size={sizes[6]} fill={colors.secondary} />
            {isTabletMode && (
              <LargeLabel fontWeight="bold">{t('common:download')}</LargeLabel>
            )}
          </IconButton>
        )}
        {sharable && (
          <IconButton
            hitSlop={HIT_SLOP}
            onPress={onShare}
            disabled={isSendPdfDisabled}>
            <Icon.Share size={sizes[6]} fill={colors.secondary} />
            {isTabletMode && (
              <LargeLabel fontWeight="bold">{t('common:share')}</LargeLabel>
            )}
          </IconButton>
        )}
      </Header>
      <Pdf
        trustAllCerts={false}
        source={{
          uri: url || base64ViewPdf,
        }}
        onPageChanged={handleOnScroll}
        onLoadComplete={() => {
          setPdfTitle(
            url?.split('/').pop()?.split('.').slice(0, -1).join('.') || '',
          );
        }}
        onError={e => {
          console.log(e);
        }}
        style={{ flex: 1 }}
        scale={isTabletMode ? 1.5 : 1}
        password={password}></Pdf>
      {/* <PdfReader
        source={{
          uri: url,
          ...base64ViewPdf,
        }}
        style={{ flex: 1 }}
        onLoadEnd={e => {
          setPdfTitle(e.nativeEvent.title);
        }}
        onError={e => {
          console.log(e, url);
        }}
      /> */}

      {isRequiredToReadAllPdf && (
        <FloatingHintButton
          visible={isSendPdfDisabled}
          containerStyle={{
            bottom: 104,
          }}
          text={t('proposal:previewDocument.scrollBottom')}
        />
      )}

      {shareType === 'email' && mailConfig && (
        <EmailSharing
          visible={emailSharingVisible}
          title={'PDF'}
          onDismiss={() => setEmailSharingVisible(false)}
          agentEmail={mailConfig.mailCc}
          customerEmail={mailConfig.mailTo}
          subject={mailConfig.subject}
          content={mailConfig.content}
          html={mailConfig.html}
          attachments={attachments}
        />
      )}
    </Container>
  );
}

const Container = styled(SafeAreaView)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  flex: 1,
}));

const Header = styled(View)(({ theme: { space, colors } }) => ({
  flexDirection: 'row',
  paddingRight: space[1],
  backgroundColor: colors.background,
  borderBottomColor: colors.surface,
  borderBottomWidth: 1,
}));

const IconButton = styled(TouchableOpacity)(({ theme: { sizes, space } }) => ({
  flexDirection: 'row',
  height: sizes[11],
  justifyContent: 'center',
  alignItems: 'center',
  gap: space[1],
  padding: 10,
}));

const Title = styled(LargeLabel)(({ theme }) => ({
  position: 'absolute',
  left: 0,
  right: 0,
  textAlign: 'center',
  alignSelf: 'center',
  paddingHorizontal: theme.space[22],
}));

const H6Title = styled(H6)(({ theme }) => ({
  position: 'absolute',
  left: 0,
  right: 0,
  alignSelf: 'center',
  paddingHorizontal: theme.space[15],
}));
