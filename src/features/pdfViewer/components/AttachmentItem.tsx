import { useTheme } from '@emotion/react';
import {
  Checkbox,
  Column,
  Icon,
  LargeBody,
  Row,
  Typography,
} from 'cube-ui-components';
import { TouchableWithoutFeedback } from 'react-native';
import { countryModuleSiConfig } from 'utils/config/module';

type AttachmentItemProps = {
  name: string;
  selected?: boolean;
  required?: boolean;
  onToggleSelect?: () => void;
};

const AttachmentItem = ({
  name,
  selected,
  required,
  onToggleSelect,
}: AttachmentItemProps) => {
  const { space, colors } = useTheme();

  const isAttachmentChangeable =
    countryModuleSiConfig.pdf.isAttachmentChangeable;

  if (!isAttachmentChangeable) {
    return (
      <Row marginTop={space[4]}>
        <Icon.Document fill={colors.palette.black} />
        <LargeBody style={{ marginLeft: space[1] }}>{name}</LargeBody>
      </Row>
    );
  }

  return (
    <TouchableWithoutFeedback onPress={required ? undefined : onToggleSelect}>
      <Row marginTop={space[2]} alignItems="center">
        <Checkbox
          disabled={required}
          checked={selected}
          onChange={onToggleSelect}
        />

        <Column flex={1} marginLeft={space[2]}>
          <Typography.LargeBody color={colors.palette.fwdDarkGreen[100]}>
            {name}
          </Typography.LargeBody>
        </Column>
      </Row>
    </TouchableWithoutFeedback>
  );
};

export default AttachmentItem;
