const {
  ConfigPlugin,
  withProjectBuildGradle,
  // eslint-disable-next-line @typescript-eslint/no-var-requires
} = require('@expo/config-plugins');

function replace(contents, match, replace) {
  if (!(match.test ? RegExp(match).test(contents) : contents.includes(match)))
    throw new Error('Invalid text replace in config');

  return contents.replace(match, replace);
}

const withCustomAndroidBuildGradle = config => {
  return withProjectBuildGradle(config, async config => {
    config.modResults.contents = replace(
      config.modResults.contents,
      "maven { url 'https://www.jitpack.io' }",
      `jcenter()
        maven { url 'https://www.jitpack.io' }
      `,
    );
    console.log('Build gradle contents:\n', config.modResults.contents);

    return config;
  });
};

module.exports = withCustomAndroidBuildGradle;
